import { useCallback } from 'react';
import { useAsyncDispatch } from './useAsyncDispatch';

/**
 * 批量异步 dispatch Hook
 * 支持并发和串行两种模式
 */
export const useAsyncDispatchBatch = (options = {}) => {
  const dispatchAsync = useAsyncDispatch(options);

  // 并发执行多个 dispatch
  const dispatchAllParallel = useCallback(
    async actions => {
      const promises = actions.map(action => dispatchAsync(action));
      return Promise.all(promises);
    },
    [dispatchAsync],
  );

  // 串行执行多个 dispatch
  const dispatchAllSerial = useCallback(
    async actions => {
      const results = [];
      for (const action of actions) {
        const result = await dispatchAsync(action);
        results.push(result);
      }
      return results;
    },
    [dispatchAsync],
  );

  return {
    dispatchAsync,
    dispatchAllParallel,
    dispatchAllSerial,
  };
};
