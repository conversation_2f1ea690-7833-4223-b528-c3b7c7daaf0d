import { useState, useCallback } from 'react';
import { useAsyncDispatch } from './useAsyncDispatch';

/**
 * 带 loading 状态的异步 dispatch Hook
 */
export const useAsyncDispatchWithLoading = (options = {}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const dispatchAsync = useAsyncDispatch(options);

  const dispatchWithLoading = useCallback(
    async action => {
      setLoading(true);
      setError(null);

      try {
        const result = await dispatchAsync(action);
        return result;
      } catch (err) {
        setError(err);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [dispatchAsync],
  );

  const dispatchAllWithLoading = useCallback(
    async actions => {
      setLoading(true);
      setError(null);

      try {
        const promises = actions.map(action => dispatchAsync(action));
        const results = await Promise.all(promises);
        return results;
      } catch (err) {
        setError(err);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [dispatchAsync],
  );

  return {
    loading,
    error,
    dispatchWithLoading,
    dispatchAllWithLoading,
  };
};
