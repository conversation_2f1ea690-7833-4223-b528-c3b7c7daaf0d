import { useCallback } from 'react';
import { useDispatch } from 'umi';

/**
 * 自定义 Hook: 将 DVA dispatch 转换为 Promise
 * @param {Object} options - 配置选项
 * @param {number} options.timeout - 请求超时时间(ms)，默认 30000
 * @param {Function} options.onError - 全局错误处理函数
 * @param {Function} options.onSuccess - 全局成功处理函数
 */
export const useAsyncDispatch = (options = {}) => {
  const dispatch = useDispatch();
  const { timeout = 30000, onError = null, onSuccess = null } = options;

  const dispatchAsync = useCallback(
    action => {
      return new Promise((resolve, reject) => {
        let timer = null;
        let isCompleted = false;

        // 设置超时处理
        if (timeout) {
          timer = setTimeout(() => {
            if (!isCompleted) {
              isCompleted = true;
              const error = new Error(`请求超时: ${action.type}`);
              onError?.(error);
              reject(error);
            }
          }, timeout);
        }

        dispatch({
          ...action,
          callback: response => {
            if (isCompleted) return;
            isCompleted = true;

            // 清除超时定时器
            if (timer) {
              clearTimeout(timer);
            }

            if (response?.code === 200) {
              onSuccess?.(response);
              resolve(response);
            } else {
              const error = new Error(
                response?.message || response?.msg || '请求失败',
              );
              error.response = response;
              onError?.(error);
              reject(error);
            }
          },
        });
      });
    },
    [dispatch, timeout, onError, onSuccess],
  );

  return dispatchAsync;
};
