import React from 'react';
import { useDispatch, useSelector } from 'umi';
import { Tooltip } from 'antd';
//获得redux维护的权限
const HOCAuth = props => {
  // const auth = useSelector(state => {
  //   return state.layouts.auth;
  // });
  // //存储在sessionStorage防止页面刷新，存储并区分账号权限
  let authNew = JSON.parse(sessionStorage.getItem('permissions'));
  // console.log('----------authNew-------------', authNew);
  // if (!auth || Object.keys(auth).length === 0) {
  //   authNew = JSON.parse(sessionStorage.getItem('permissions'));
  // }
  const { authKey, children } = props;
  // console.log(auth, authKey, 'typeof children');
  if (!authNew || !authKey) return null;

  let authAccess = false;
  //判断key值为数组的情况，比如悬浮框操作
  if (typeof authKey === 'object' && authKey.length > 0) {
    let flag = true;
    authKey.forEach(item => {
      if (authNew[item]) {
        flag = false;
      }
    });
    if (flag) {
      return null;
    } else {
      return children(authAccess);
    }
  }
  //定义disable值
  if (authNew[authKey] === 2) {
    authAccess = true;
  }
  // console.log(authKey, auth, children, 'auth=====');
  // 自定义渲染
  if (typeof children === 'function') {
    return authNew[authKey] === 1 || authNew[authKey] === 2
      ? children(authAccess)
      : null;
  }
  return authNew[authKey] === 1 || authNew[authKey] === 2 ? children : null; // 过滤掉 auth与authKey
};

export default HOCAuth;
