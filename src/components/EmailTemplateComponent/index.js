import React, {
  useState,
  useRef,
  useEffect,
  useImperativeHandle,
  forwardRef,
} from 'react';
import HOCAuth from '@/components/HOCAuth/index';
import {
  Button,
  Dropdown,
  Form,
  Input,
  Modal,
  notification,
  Popconfirm,
  Popover,
  Select,
  Space,
  Spin,
  Tooltip,
  Upload,
  Checkbox,
} from 'antd';
import styles from './index.less';
import { useDispatch, getIntl } from 'umi';
import EditorJS from '@editorjs/editorjs';
import Header from '@editorjs/header';
import List from '@editorjs/list';
import CodeTool from '@editorjs/code';
import ImageTool from '@editorjs/image';
import Checklist from '@editorjs/checklist';
import Quote from '@editorjs/quote';
import Underline from '@editorjs/underline';
import Delimiter from '@editorjs/delimiter';
import Marker from '@editorjs/marker';
import ColorPlugin from 'editorjs-text-color-plugin';
import {
  AbbreviationIcon,
  AbbreviationNormalIcon,
  AiBtnIcon,
  AttachmentIcon,
  BccCopyIcon,
  ElaborateIcon,
  ElaborateNormalIcon,
  EmailSendIcon,
  EmailTemplateIcon,
  FormalIcon,
  FormalNormalIcon,
  GentleIcon,
  GentleNormalIcon,
  GrammarCorrectionIcon,
  GrammarCorrectionNormalIcon,
  MakeCopyIcon,
  MoreIcon,
  NeutralityIcon,
  NeutralityNormalIcon,
  NewAiIcon,
  OptimizeIcon,
  OptimizeNormalIcon,
  SendDraftIcon,
} from '../../pages/workTableUpgrade/chatLayout/icon';
import { FormattedMessage } from '../../.umi/plugin-locale/localeExports';
import AddEmailTemplateIcon from '../../assets/add-email-template.png';
import { SearchOutlined } from '@ant-design/icons';
import EmailTemplateLikeNoIcon from '../../assets/email-template-like-not.png';
import EmailTemplateEditorIcon from '../../assets/email-template-editor.png';
import EmailTemplateDeleteIcon from '../../assets/email-template-delete.png';
import EmailTemplateLikeIcon from '../../assets/email-template-like.png';
import EmailAddAttachmentIcon from '../../assets/new-email-add-attachment.png';

const { TextArea } = Input;

const EmailTemplateComponent = forwardRef((props, ref) => {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const editorRef = useRef(null); // 创建一个 ref 来保存 EditorJS 实例
  const editorRef1 = useRef(null); // 创建一个 ref 来保存 EditorJS 实例
  const popoverRef = useRef(null);
  const aiOperationRef = useRef(null);
  // 收件人下拉框禁用状态
  const [open, setOpen] = useState(false); // 初始状态为false，隐藏下拉框
  // 是否显示抄送
  const [showMakeCopy, setShowMakeCopy] = useState(false);
  // 是否显示密送
  const [showBccCopy, setShowBccCopy] = useState(false);
  // 草稿弹窗
  const [draftOpen, setDraftOpen] = useState(false);
  const [draftValue, setDraftValue] = useState('');
  // 邮件模板
  const [emailTemplateOpen, setEmailTemplateOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  // 回复邮件附件
  const [workRecordFileList, setWorkRecordFileList] = useState([]);
  const [fileList, setFileList] = useState([]);
  // 收件人
  const [desAddress, setDesAddress] = useState([]);
  const [loading, setLoading] = useState(false);
  // 邮件模板list
  const [emailTemplatesList, setEmailTemplatesList] = useState([]);
  const [saveLoading, setSaveLoading] = useState(false);
  const [applyLoading, setApplyLoading] = useState(false);
  const [editorStatus, setEditorStatus] = useState(false);
  const [editorEmailTemplateValue, setEditorEmailTemplateValue] = useState({});
  const [editorTemplateData, setEditorTemplateData] = useState([]);

  const [editorLoading, setEditorLoading] = useState(false);

  const [selectedText, setSelectedText] = useState('');
  const [selectedTextInit, setSelectedTextInit] = useState('');
  const [updatedJsonData, setUpdatedJsonData] = useState([]);
  const [translationCodeList, setTranslationCodeList] = useState([]);
  const [lang, setLang] = useState('');
  const textAreaRef = useRef(null);
  const [isTextSelected, setIsTextSelected] = useState(false);
  let [search, setSearch] = useState('');
  let [sendEmail, setSendEmail] = useState(false);
  const [channelOptions, setChannelOptions] = useState([]); //通知渠道

  useEffect(() => {
    // 翻译的语言列表
    const googleLanguage = JSON.parse(localStorage.getItem('languageLocal'));
    setTranslationCodeList(googleLanguage);
    const language = localStorage.getItem('lang');
    if (language === 'zh-CN') {
      setLang('中文 (Chinese)');
    } else if (language === 'en-US') {
      setLang('English');
    }

    setFileList([]);
    setShowMakeCopy(false);
    setShowBccCopy(false);
    setUpdatedJsonData([]);
    emailTemplates();
    getChannels();
    if (editorRef.current === null) {
      // 确保 EditorJS 只初始化一次
      const editor = new EditorJS({
        holder: 'editorjs', // 容器 ID (必须)
        tools: {
          header: {
            class: Header,
            inlineToolbar: true,
          },
          list: {
            class: List,
            inlineToolbar: true,
          },
          // link: {
          //   class: LinkTool,
          //   inlineToolbar: true,
          // },
          code: {
            class: CodeTool,
            inlineToolbar: true,
          },
          image: {
            class: ImageTool,
            inlineToolbar: true,
            config: {
              uploader: {
                async uploadByFile(file) {
                  let { size } = file;
                  let fileSize = size / 1024 / 1024;
                  if (fileSize > 2) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'message.upload.size',
                        },
                        {
                          fileSize: 2,
                        },
                      ),
                    });
                    return;
                  }
                  const formData = new FormData();
                  formData.append('file', file);
                  // 上传图片
                  return dispatch({
                    type: 'workOrderCenter/queryUploadPictureNew',
                    payload: formData,
                  });
                },
              },
            },
          },
          checklist: {
            class: Checklist,
            inlineToolbar: true,
          },
          quote: Quote,
          underline: Underline,
          delimiter: Delimiter,
          Marker: {
            class: Marker,
            inlineToolbar: true,
          },
          color: {
            class: ColorPlugin,
            config: {
              colorCollections: [
                '#3463FC',
                '#00B900',
                '#AD30E5',
                '#F22417',
                '#D7CE1E',
                '#86BF00',
                '#FFF',
              ],
              defaultColor: '#000',
              type: 'text',
              customPicker: true, // add a button to allow selecting any colour
            },
          },
        },
        // placeholder: 'Start typing here...', // 编辑器占位符
        autofocus: true, // 自动聚焦
        i18n: {
          messages: {
            toolNames: {
              Text: getIntl().formatMessage({
                id: 'new.worktable.email.editor.text',
                defaultValue: '文本',
              }),
              Heading: getIntl().formatMessage({
                id: 'new.worktable.email.editor.header',
                defaultValue: '标题',
              }),
              List: getIntl().formatMessage({
                id: 'new.worktable.email.editor.list',
                defaultValue: '列表',
              }),
              Quote: getIntl().formatMessage({
                id: 'new.worktable.email.editor.quote',
                defaultValue: '引语',
              }),
              Code: getIntl().formatMessage({
                id: 'new.worktable.email.editor.code',
                defaultValue: '代码',
              }),
              Delimiter: getIntl().formatMessage({
                id: 'new.worktable.email.editor.delimiter',
                defaultValue: '分隔符',
              }),
              Link: getIntl().formatMessage({
                id: 'new.worktable.email.editor.link',
                defaultValue: '链接',
              }),
              Image: getIntl().formatMessage({
                id: 'new.worktable.email.editor.image',
                defaultValue: '图片',
              }),
              Checklist: getIntl().formatMessage({
                id: 'new.worktable.email.editor.check.list',
                defaultValue: '多选框',
              }),
              Underline: getIntl().formatMessage({
                id: 'new.worktable.email.editor.underline',
                defaultValue: '下划线',
              }),
              TextColor: getIntl().formatMessage({
                id: 'new.worktable.email.editor.color',
                defaultValue: '颜色',
              }),
              Marker: getIntl().formatMessage({
                id: 'new.worktable.email.editor.marker',
                defaultValue: '标记',
              }),
            },
          },
        },
        data: updatedJsonData,
      });
      editorRef.current = editor; // 将 EditorJS 实例保存到 ref 中

      return () => {
        // 组件卸载时销毁 EditorJS 实例
        if (editorRef.current) {
          editorRef.current.destroy();
          editorRef.current = null;
        }
      };
    } else {
      editorRef.current.destroy();
      editorRef.current = null;
    }
  }, []);
  useEffect(() => {
    if (isModalOpen) {
      if (editorRef1.current === null || editorRef1.current === undefined) {
        // 确保 EditorJS 只初始化一次
        const editor1 = new EditorJS({
          holder: 'editorjs1', // 容器 ID (必须)
          tools: {
            header: {
              class: Header,
              inlineToolbar: true,
            },
            list: {
              class: List,
              inlineToolbar: true,
            },
            // link: {
            //   class: LinkTool,
            //   inlineToolbar: true,
            // },
            code: {
              class: CodeTool,
              inlineToolbar: true,
            },
            image: {
              class: ImageTool,
              inlineToolbar: true,
              config: {
                uploader: {
                  async uploadByFile(file) {
                    let { size } = file;
                    let fileSize = size / 1024 / 1024;
                    if (fileSize > 2) {
                      notification.error({
                        message: getIntl().formatMessage(
                          {
                            id: 'message.upload.size',
                          },
                          {
                            fileSize: 2,
                          },
                        ),
                      });
                      return;
                    }
                    const formData = new FormData();
                    formData.append('file', file);
                    // 上传图片
                    return dispatch({
                      type: 'workOrderCenter/queryUploadPictureNew',
                      payload: formData,
                    });
                  },
                },
              },
            },
            checklist: {
              class: Checklist,
              inlineToolbar: true,
            },
            quote: Quote,
            underline: Underline,
            delimiter: Delimiter,
            Marker: {
              class: Marker,
              inlineToolbar: true,
            },
            color: {
              class: ColorPlugin,
              config: {
                colorCollections: [
                  '#3463FC',
                  '#00B900',
                  '#AD30E5',
                  '#F22417',
                  '#D7CE1E',
                  '#86BF00',
                  '#FFF',
                ],
                defaultColor: '#000',
                type: 'text',
                customPicker: true, // add a button to allow selecting any colour
              },
            },
          },
          // placeholder: 'Start typing here...', // 编辑器占位符
          autofocus: true, // 自动聚焦
          i18n: {
            messages: {
              toolNames: {
                Text: getIntl().formatMessage({
                  id: 'new.worktable.email.editor.text',
                  defaultValue: '文本',
                }),
                Heading: getIntl().formatMessage({
                  id: 'new.worktable.email.editor.header',
                  defaultValue: '标题',
                }),
                List: getIntl().formatMessage({
                  id: 'new.worktable.email.editor.list',
                  defaultValue: '列表',
                }),
                Quote: getIntl().formatMessage({
                  id: 'new.worktable.email.editor.quote',
                  defaultValue: '引语',
                }),
                Code: getIntl().formatMessage({
                  id: 'new.worktable.email.editor.code',
                  defaultValue: '代码',
                }),
                Delimiter: getIntl().formatMessage({
                  id: 'new.worktable.email.editor.delimiter',
                  defaultValue: '分隔符',
                }),
                Link: getIntl().formatMessage({
                  id: 'new.worktable.email.editor.link',
                  defaultValue: '链接',
                }),
                Image: getIntl().formatMessage({
                  id: 'new.worktable.email.editor.image',
                  defaultValue: '图片',
                }),
                Checklist: getIntl().formatMessage({
                  id: 'new.worktable.email.editor.check.list',
                  defaultValue: '多选框',
                }),
                Underline: getIntl().formatMessage({
                  id: 'new.worktable.email.editor.underline',
                  defaultValue: '下划线',
                }),
                TextColor: getIntl().formatMessage({
                  id: 'new.worktable.email.editor.color',
                  defaultValue: '颜色',
                }),
                Marker: getIntl().formatMessage({
                  id: 'new.worktable.email.editor.marker',
                  defaultValue: '标记',
                }),
              },
            },
          },
          data: editorTemplateData,
        });
        editorRef1.current = editor1;

        return () => {
          // 组件卸载时销毁 EditorJS 实例
          if (editorRef1.current) {
            editorRef1.current.destroy();
            editorRef1.current = null;
          }
        };
      } else {
        // editorRef1.current.configuration.data=editorTemplateData;
        editorRef1.current.render(editorTemplateData);
      }
    }
  }, [isModalOpen, editorTemplateData]);
  useEffect(() => {
    if (props.emailAddress) {
      setDesAddress(props.emailAddress);
    } else {
      setSendEmail(false);
      setDesAddress([]);
      props.formReplayRef.current?.setFieldsValue({ customerEmail: false });
    }
    if (props.emailAddress && sendEmail) {
      props.formReplayRef.current?.setFieldsValue({
        recipient: props.emailAddress,
      });
    }
  }, [props.emailAddress]);

  // 加载邮件通知渠道
  const getChannels = () => {
    dispatch({
      type: 'channel/getChannel',
      payload: { pageSize: 1000, pageNum: 1, channelTypeId: 1 },
      callback: res => {
        if (res.code === 200) {
          setChannelOptions(res.data.rows);
        }
      },
    });
  };

  const convertToJsonHtml = data => {
    let html = '';
    data.blocks.forEach(block => {
      switch (block.type) {
        case 'header':
          html += `<h${block.data.level}>${block.data.text}</h${block.data.level}>`;
          break;
        case 'paragraph':
          html += `<p>${block.data.text}</p>`;
          break;
        case 'list':
          html += `<ul>${block.data.items
            .map(item => `<li>${item}</li>`)
            .join('')}</ul>`;
          break;
        case 'image':
          html += `<img src="${block.data.file.url}" alt="${block.data
            .caption || ''}" />`;
          break;
        case 'code':
          html += `${block.data.code}`;
          break;
        case 'quote':
          html += `<blockquote>${block.data.text} <footer>${block.data.caption}</footer></blockquote>`;
          break;
        case 'delimiter':
          html += '<hr>';
          break;
        case 'checklist':
          html += '<ul style="list-style: none">';
          block.data.items.forEach(item => {
            const checked = item.checked ? 'checked' : '';
            html += `<li><input style="float: left;margin-top: 2px;margin-right: 3px;" type="checkbox" ${checked} disabled>${item.text}</li>`;
          });
          html += '</ul>';
          break;
        default:
          console.warn('Unsupported block type:', block.type);
      }
    });
    return html;
  };

  // 上传组件的相关属性配置
  const uploadProperties = {
    name: 'file',
    multiple: true,
    showUploadList: true,
    maxCount: 5,
    // defaultFileList: [...this.props.fileList],
    // 上传前的校验
    beforeUpload: file => {
      for (let i = 0; i < fileList.length; i++) {
        if (file.name == fileList[i].name) {
          notification.warning({
            message: getIntl().formatMessage({
              id: 'upload.repeat.waring.tips',
              defaultValue: '不允许上传重复的附件！',
            }),
          });
          return Upload.LIST_IGNORE;
        }
      }
      if (fileList.length === 5) {
        notification.warning({
          message: getIntl().formatMessage({
            id: 'upload.max.num.waring.tips',
            defaultValue: '最多上传5个附件！',
          }),
        });
        return Upload.LIST_IGNORE;
      }
      let { size } = file;
      let fileSize = size / 1024 / 1024;
      if (fileSize > 10) {
        notification.error({
          message: getIntl().formatMessage(
            {
              id: 'message.upload.size',
            },
            {
              fileSize: 10,
            },
          ),
        });
        return Upload.LIST_IGNORE;
      }
      return true;
    },
    // 自定义的上传接口
    customRequest: ({ file, onSuccess }) => {
      const fmData = new FormData();
      fmData.append('file', file);
      dispatch({
        type: 'workOrderCenter/queryUpload',
        payload: fmData,
        callback: response => {
          if (200 === response.code) {
            workRecordFileList.push(response.data);
            setWorkRecordFileList(workRecordFileList);
            onSuccess(response.data, file);
          }
        },
      });
    },
    // 文件上传中、上传成功、上传失败 都会回调这个函数
    onChange: ({ file }) => {
      let { status, response } = file;
      if (status === 'done') {
        // 表示上传成功了
        // 添加url属性 对应的Upload组件展示的附件列表就可以直接下载了
        file.url = response.preSignedUrl;
        // 添加附件的类型 后台数据库需要
        response.fileType = 1;

        // 不要直接使用fileList  位于Redux中的数据不能直接修改
        let fList = JSON.parse(JSON.stringify(fileList));
        fList.push(file);
        setFileList(fList);
      }
    },
    // 删除接口
    onRemove: file => {
      if (!file) return;
      // 只有上传成功了 才能删除
      if (file.status !== 'done') {
        notification.error({
          message: getIntl().formatMessage({
            id: 'delete.attachment.error',
          }),
        });
        return;
      }

      let {
        uid,
        response: { fileUrl, bucketName, preSignedUrl },
      } = file;
      return new Promise((resolve, reject) => {
        dispatch({
          type: 'workOrderCenter/queryDeleteFile',
          payload: {
            fileUrl,
            bucketName,
            url: preSignedUrl,
          },
          callback: response => {
            if (response && response.code === 200) {
              resolve();
            } else {
              reject();
            }
          },
        });
      })
        .then(data => {
          // 删除附件成功后 更新页面附件列表
          let remainList = [];
          for (let i = 0; i < fileList.length; i++) {
            if (fileList[i].uid != uid) {
              remainList.push(fileList[i]);
            }
          }
          setFileList(remainList);
          notification.success({
            message: getIntl().formatMessage({
              id: 'delete.attachment.success',
            }),
          });
        })
        .catch(error => {
          notification.error({
            message: getIntl().formatMessage({
              id: 'delete.attachment.error',
            }),
          });
        });
    },
  };

  // 收件人输入框
  const handleChange = value => {
    setDesAddress(value);
  };
  const handleOpenChange = newOpen => {
    setOpen(false);
  };
  // 回车事件判断
  const handleKeyDown = e => {
    if (e.key === 'Enter' && !e.shiftKey) {
      const regexPattern = /^\\s*\\w+([-+.']\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*\\s*$/;
      if (!regexPattern.test(desAddress)) {
        e.stopPropagation();
        e.preventDefault();
        notification.warning({
          message: getIntl().formatMessage({ id: 'login.user.email.pattern' }),
        });
      }
    }
  };
  // 展示抄送
  const handleShowMakeCopy = () => {
    setShowMakeCopy(true);
  };
  // 展示密送
  const handleShowBccCopy = () => {
    setShowBccCopy(true);
  };

  // 展示邮件模板
  const handleEmailTemplateOpenChange = newOpen => {
    setEmailTemplateOpen(newOpen);
    setSearch('');
  };
  // 显示创建邮件模板弹窗
  const handleShowEmailTemplate = () => {
    setIsModalOpen(true);
  };
  // 显示修改邮件模板弹窗
  const handleShowEditorEmailTemplate = values => {
    setEditorTemplateData(JSON.parse(values.otherContent));
    setEditorEmailTemplateValue(values);
    setEditorStatus(true);
    setIsModalOpen(true);
  };
  useEffect(() => {
    if (editorStatus && isModalOpen) {
      let newList = {};
      newList.templateName = editorEmailTemplateValue.templateName;
      form.setFieldsValue(newList);
    }
  }, [editorStatus, editorEmailTemplateValue]);
  const handleCancel = () => {
    setIsModalOpen(false);
    setEmailTemplateOpen(true);
    setEditorTemplateData([]);
    setEditorEmailTemplateValue({});
    setEditorStatus(false);
    form.resetFields();
    // setEditorTemplateData({});
    if (editorRef1.current) {
      editorRef1.current.destroy();
      editorRef1.current = null;
    }
  };
  // 查询所有邮件模板
  const emailTemplates = () => {
    dispatch({
      type: 'worktable/emailTemplates',
      payload: { templateName: search },
      callback: response => {
        if (response.code == 200) {
          setEmailTemplatesList(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询邮件模板详情
  const emailTemplatesDetail = value => {
    let params = {
      templateId: '',
    };
    dispatch({
      type: 'worktable/emailTemplatesDetail',
      payload: params,
      callback: response => {
        if (response.code == 200) {
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  const searchChange = e => {
    setSearch(e.target.value);
  };
  const searchPressEnter = event => {
    event.stopPropagation();
    event.preventDefault();
    emailTemplates();
  };

  // 是否删除邮件模板
  const confirmDefaultDelete = values => {
    let params = {
      templateId: values.templateId,
    };
    dispatch({
      type: 'worktable/emailTemplatesDelete',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          setEmailTemplateOpen(true);
          if (search) {
            setSearch('');
          } else {
            emailTemplates();
          }
          notification.success({
            message: response.msg,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  const cancelDefaultDelete = e => {
    // console.log(e);
  };
  // 创建邮件模板
  const onFinishSave = async values => {
    const action = values.action; // 获取 action 字段的值
    const outputData = await editorRef1.current.save();
    const html = convertToJsonHtml(outputData);
    let params = {};
    if (editorStatus) {
      params = {
        templateId: editorEmailTemplateValue.templateId,
        templateName: values.templateName,
        otherContent: JSON.stringify(outputData),
        content: html,
      };
      dispatch({
        type: 'worktable/emailTemplatesUpdate',
        payload: params,
        callback: response => {
          setSaveLoading(false);
          setApplyLoading(false);
          if (response.code == 200) {
            setIsModalOpen(false);
            setEditorStatus(false);
            setEmailTemplateOpen(true);
            setEditorEmailTemplateValue({});
            setEditorTemplateData([]);
            if (search) {
              setSearch('');
            } else {
              emailTemplates();
            }
            form.resetFields();
            if (editorRef1.current) {
              editorRef1.current.destroy();
              editorRef1.current = null;
            }
            notification.success({
              message: response.msg,
            });
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    } else {
      if (action === 'save') {
        // 保存逻辑
        setSaveLoading(true);
      } else if (action === 'apply') {
        // 保存并应用逻辑
        setApplyLoading(true);
      }

      params = {
        templateName: values.templateName,
        otherContent: JSON.stringify(outputData),
        content: html,
      };
      dispatch({
        type: 'worktable/emailTemplatesCreate',
        payload: params,
        callback: response => {
          setSaveLoading(false);
          setApplyLoading(false);
          if (response.code == 200) {
            setIsModalOpen(false);
            setEmailTemplateOpen(true);
            if (search) {
              setSearch('');
            } else {
              emailTemplates();
            }
            notification.success({
              message: response.msg,
            });
            if (action === 'apply') {
              handleSelectTemplate(params);
            }
            form.resetFields();
            if (editorRef1.current) {
              editorRef1.current.destroy();
              editorRef1.current = null;
            }
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    }
  };
  // 是否保存为默认邮件模板
  const confirmDefault = values => {
    let params = {
      templateId: values.templateId,
      // isDefault:1,
    };
    dispatch({
      type: 'worktable/updateDefault',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          if (search) {
            setSearch('');
          } else {
            emailTemplates();
          }
          notification.success({
            message: response.msg,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  const cancelDefault = e => {
    console.log(e);
  };

  // 双击选择邮件模板
  const handleSelectTemplate = async values => {
    const outputData = await editorRef.current.save();
    const updatedJsonDataNew = { ...outputData };
    let newData = JSON.parse(values.otherContent);
    const mergedBlocks = [...updatedJsonDataNew.blocks, ...newData.blocks];
    updatedJsonDataNew.blocks = mergedBlocks;
    setUpdatedJsonData(updatedJsonDataNew);
    editorRef.current?.render(updatedJsonDataNew);
    setEmailTemplateOpen(false);
    setSearch('');
  };

  // 鼠标选中文字
  useEffect(() => {
    const handleMouseUp = () => {
      const selection = window.getSelection();
      if (selection.rangeCount > 0) {
        const selectedRange = selection?.getRangeAt(0);

        // 检查选中区域是否在目标 div 内
        if (
          selection.rangeCount > 0 &&
          textAreaRef.current.contains(selectedRange.commonAncestorContainer)
        ) {
          setSelectedText(selection.toString());
          setSelectedTextInit(selection.toString());
          setIsTextSelected(true);
        }
      }
    };

    const handleSelectionChange = () => {
      setTimeout(() => {
        if (window.getSelection().toString() === '') {
          setSelectedText(''); // 选中状态消失时清空
          setIsTextSelected(false);
        }
      }, 500);
    };

    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('selectionchange', handleSelectionChange);

    return () => {
      document.removeEventListener('mouseup', handleMouseUp);
      document.removeEventListener('selectionchange', handleSelectionChange);
    };
  }, []);

  // AI优化邮件
  const handleOptimize = async () => {
    setEditorLoading(true);
    const outputData = await editorRef.current.save();
    let params = {
      format: 'string',
      content: selectedTextInit,
      workRecordId: '',
    };
    const updatedJsonDataNew = { ...outputData }; // 创建 jsonData 的副本，避免直接修改原数据
    dispatch({
      type: 'worktable/aiBeautifyEmail',
      payload: params,
      callback: response => {
        setEditorLoading(false);
        if (response.code == 200) {
          let replacementString = response.data;
          updatedJsonDataNew.blocks = updatedJsonDataNew.blocks.map(block => {
            if (block.data && block.data.text) {
              return {
                ...block,
                data: {
                  ...block.data,
                  text: block.data.text.replace(
                    selectedTextInit,
                    replacementString,
                  ),
                },
              };
            }
            return block;
          });
          setUpdatedJsonData(updatedJsonDataNew);
          editorRef.current?.render(updatedJsonDataNew);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // AI扩写邮件
  const aiExpandEmail = async () => {
    const outputData = await editorRef.current.save();
    const updatedJsonDataNew = { ...outputData };
    setEditorLoading(true);
    let params = {
      format: 'string',
      content: selectedTextInit,
      workRecordId: '',
    };
    dispatch({
      type: 'worktable/aiExpandEmail',
      payload: params,
      callback: response => {
        setEditorLoading(false);
        if (response.code == 200) {
          // setSelectedTextInit('');
          let replacementString = response.data;
          updatedJsonDataNew.blocks = updatedJsonDataNew.blocks.map(block => {
            if (block.data && block.data.text) {
              return {
                ...block,
                data: {
                  ...block.data,
                  text: block.data.text.replace(
                    selectedTextInit,
                    replacementString,
                  ),
                },
              };
            }
            return block;
          });
          setUpdatedJsonData(updatedJsonDataNew);
          editorRef.current?.render(updatedJsonDataNew);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // AI缩写邮件
  const aiSummarizeEmail = async () => {
    const outputData = await editorRef.current.save();
    const updatedJsonDataNew = { ...outputData };
    setEditorLoading(true);
    let params = {
      format: 'string',
      content: selectedTextInit,
    };
    dispatch({
      type: 'worktable/aiSummarizeEmail',
      payload: params,
      callback: response => {
        setEditorLoading(false);
        if (response.code == 200) {
          // setSelectedTextInit('');
          let replacementString = response.data;
          updatedJsonDataNew.blocks = updatedJsonDataNew.blocks.map(block => {
            if (block.data && block.data.text) {
              return {
                ...block,
                data: {
                  ...block.data,
                  text: block.data.text.replace(
                    selectedTextInit,
                    replacementString,
                  ),
                },
              };
            }
            return block;
          });
          setUpdatedJsonData(updatedJsonDataNew);
          editorRef.current?.render(updatedJsonDataNew);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // AI语法纠错
  const grammarCheck = async () => {
    const outputData = await editorRef.current.save();
    const updatedJsonDataNew = { ...outputData };
    setEditorLoading(true);
    let params = {
      format: 'string',
      content: selectedTextInit,
    };
    dispatch({
      type: 'worktable/grammarCheck',
      payload: params,
      callback: response => {
        setEditorLoading(false);
        if (response.code == 200) {
          // setSelectedTextInit('');
          let replacementString = response.data;
          updatedJsonDataNew.blocks = updatedJsonDataNew.blocks.map(block => {
            if (block.data && block.data.text) {
              return {
                ...block,
                data: {
                  ...block.data,
                  text: block.data.text.replace(
                    selectedTextInit,
                    replacementString,
                  ),
                },
              };
            }
            return block;
          });
          setUpdatedJsonData(updatedJsonDataNew);
          editorRef.current?.render(updatedJsonDataNew);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // AI温柔
  const aiGentleEmail = async () => {
    const outputData = await editorRef.current.save();
    const updatedJsonDataNew = { ...outputData };
    setEditorLoading(true);
    let params = {
      format: 'string',
      content: selectedTextInit,
    };
    dispatch({
      type: 'worktable/aiGentleEmail',
      payload: params,
      callback: response => {
        setEditorLoading(false);
        if (response.code == 200) {
          // setSelectedTextInit('');
          let replacementString = response.data;
          updatedJsonDataNew.blocks = updatedJsonDataNew.blocks.map(block => {
            if (block.data && block.data.text) {
              return {
                ...block,
                data: {
                  ...block.data,
                  text: block.data.text.replace(
                    selectedTextInit,
                    replacementString,
                  ),
                },
              };
            }
            return block;
          });
          setUpdatedJsonData(updatedJsonDataNew);
          editorRef.current?.render(updatedJsonDataNew);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // AI中立
  const aiNeutralEmail = async () => {
    const outputData = await editorRef.current.save();
    const updatedJsonDataNew = { ...outputData };
    setEditorLoading(true);
    let params = {
      format: 'string',
      content: selectedTextInit,
    };
    dispatch({
      type: 'worktable/aiNeutralEmail',
      payload: params,
      callback: response => {
        setEditorLoading(false);
        if (response.code == 200) {
          // setSelectedTextInit('');
          let replacementString = response.data;
          updatedJsonDataNew.blocks = updatedJsonDataNew.blocks.map(block => {
            if (block.data && block.data.text) {
              return {
                ...block,
                data: {
                  ...block.data,
                  text: block.data.text.replace(
                    selectedTextInit,
                    replacementString,
                  ),
                },
              };
            }
            return block;
          });
          setUpdatedJsonData(updatedJsonDataNew);
          editorRef.current?.render(updatedJsonDataNew);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // AI正式
  const formalEmail = async () => {
    const outputData = await editorRef.current.save();
    const updatedJsonDataNew = { ...outputData };
    setEditorLoading(true);
    let params = {
      format: 'string',
      content: selectedTextInit,
    };
    dispatch({
      type: 'worktable/formalEmail',
      payload: params,
      callback: response => {
        setEditorLoading(false);
        if (response.code == 200) {
          // setSelectedTextInit('');
          let replacementString = response.data;
          updatedJsonDataNew.blocks = updatedJsonDataNew.blocks.map(block => {
            if (block.data && block.data.text) {
              return {
                ...block,
                data: {
                  ...block.data,
                  text: block.data.text.replace(
                    selectedTextInit,
                    replacementString,
                  ),
                },
              };
            }
            return block;
          });
          setUpdatedJsonData(updatedJsonDataNew);
          editorRef.current?.render(updatedJsonDataNew);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 获取草稿输入的value
  const handleChangeDraftText = e => {
    setDraftValue(e.target.value);
  };
  const handleTranslationCodeChange = e => {
    setLang(e);
  };
  // 展示草稿
  const handleDraftOpenChange = async newOpen => {
    setDraftOpen(newOpen);
    setDraftValue('');
    const language = localStorage.getItem('lang');
    if (language === 'zh-CN') {
      setLang('中文 (Chinese)');
    } else if (language === 'en-US') {
      setLang('English');
    }
  };
  // 生成邮件草稿---发送并关闭草稿
  const aiGenerateEmailDraft = async () => {
    const outputData = await editorRef.current.save();
    const updatedJsonDataNew = { ...outputData };
    setDraftOpen(false);
    setEditorLoading(true);
    let params = {
      language: lang,
      format: 'string',
      content: draftValue,
      workRecordId: '',
    };
    dispatch({
      type: 'worktable/aiGenerateEmailDraft',
      payload: params,
      callback: response => {
        setEditorLoading(false);
        if (response.code == 200) {
          let newText = response.data;
          const lines = newText.split('\n');
          // 移除末尾的空字符串（如果存在）
          const filteredLines = lines.filter(line => line.trim() !== '');
          let newDataArray = [];
          filteredLines.map(item => {
            const characters =
              'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            let randomString = '';
            for (let i = 0; i < 10; i++) {
              const randomIndex = Math.floor(Math.random() * characters.length);
              randomString += characters.charAt(randomIndex);
            }
            let newData = {
              id: randomString,
              type: 'paragraph',
              data: {
                text: item,
              },
            };
            newDataArray.push(newData);
          });
          if (filteredLines.length === newDataArray.length) {
            const totalData = [...updatedJsonDataNew.blocks, ...newDataArray];
            updatedJsonDataNew.blocks = totalData;
            setUpdatedJsonData(updatedJsonDataNew);
            editorRef.current?.render(updatedJsonDataNew);
          }
          // let newData = {
          //   id: randomString,
          //   type: 'paragraph',
          //   data: {
          //     text: newText,
          //   },
          // };
          // const totalData = [...updatedJsonDataNew.blocks, newData];
          // updatedJsonDataNew.blocks = totalData;
          // setUpdatedJsonData(updatedJsonDataNew);
          // editorRef.current?.render(updatedJsonDataNew);
          setDraftValue('');
          const language = localStorage.getItem('lang');
          if (language === 'zh-CN') {
            setLang('中文 (Chinese)');
          } else if (language === 'en-US') {
            setLang('English');
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 取消草稿
  const cancelEmailDraft = () => {
    setDraftOpen(false);
    setDraftValue('');
    const language = localStorage.getItem('lang');
    if (language === 'zh-CN') {
      setLang('中文 (Chinese)');
    } else if (language === 'en-US') {
      setLang('English');
    }
  };

  const items = [
    {
      key: '1',
      label: (
        <div
          onClick={selectedText ? grammarCheck : null}
          className={selectedText ? 'detailItems' : 'detailNormalItems'}
        >
          <span>
            {selectedText ? (
              <GrammarCorrectionIcon />
            ) : (
              <GrammarCorrectionNormalIcon />
            )}
          </span>
          <p>
            <FormattedMessage
              id="new.worktable.email.grammar.correction"
              defaultMessage="语法纠错"
            />
          </p>
        </div>
      ),
    },
    {
      key: '2',
      label: (
        <div
          onClick={selectedText ? aiGentleEmail : null}
          className={selectedText ? 'detailItems' : 'detailNormalItems'}
        >
          <span>{selectedText ? <GentleIcon /> : <GentleNormalIcon />}</span>
          <p>
            <FormattedMessage
              id="new.worktable.email.gentle"
              defaultMessage="温柔"
            />
          </p>
        </div>
      ),
    },
    {
      key: '3',
      label: (
        <div
          onClick={selectedText ? aiNeutralEmail : null}
          className={selectedText ? 'detailItems' : 'detailNormalItems'}
        >
          <span>
            {selectedText ? <NeutralityIcon /> : <NeutralityNormalIcon />}
          </span>
          <p>
            <FormattedMessage
              id="new.worktable.email.neutrality"
              defaultMessage="中立"
            />
          </p>
        </div>
      ),
    },
    {
      key: '4',
      label: (
        <div
          onClick={selectedText ? formalEmail : null}
          className={selectedText ? 'detailItems' : 'detailNormalItems'}
        >
          <span>{selectedText ? <FormalIcon /> : <FormalNormalIcon />}</span>
          <p>
            <FormattedMessage
              id="new.worktable.email.formal"
              defaultMessage="正式"
            />
          </p>
        </div>
      ),
    },
  ];

  // 获取富文本组件内容
  const getData = async () => {
    const outputData = await editorRef.current.save();
    const html = convertToJsonHtml(outputData);
    return html;
  };

  // 是否给客户发送邮件
  const onChange = e => {
    setSendEmail(e.target.checked);
    if (e.target.checked) {
      props.formReplayRef.current?.setFieldsValue({
        recipient: props.emailAddress,
      });
    }
  };

  // 让父组件能访问 getData 方法
  useImperativeHandle(ref, () => ({
    clearData: () => {
      setFileList([]);
      setShowMakeCopy(false);
      setShowBccCopy(false);
      setUpdatedJsonData([]);
      if (editorRef.current) {
        editorRef.current.destroy();
        editorRef.current = null;
      }
    },
    getData,
  }));

  return (
    <div className={styles.replyEmailContent}>
      <Form.Item
        label={null}
        name="customerEmail"
        valuePropName="checked"
        rules={[
          {
            required: false,
          },
        ]}
      >
        <Checkbox
          disabled={props.emailAddress ? false : true}
          onChange={onChange}
        >
          <FormattedMessage
            id="work.order.create.send.email.customer"
            defaultMessage="给客户发送邮件"
          />
        </Checkbox>
      </Form.Item>
      {sendEmail && (
        <Form.Item
          label={getIntl().formatMessage({
            id: 'work.order.reply.email.sender',
            defaultValue: '发件人：',
          })}
          name="channelId"
          rules={[
            {
              required: true,
              message: getIntl().formatMessage({
                id: 'work.order.reply.email.sender.required',
                defaultValue: '请选择发件人',
              }),
            },
          ]}
        >
          <Select
            placeholder={getIntl().formatMessage({
              id: 'work.order.reply.email.sender.required',
              defaultValue: '请选择发件人',
            })}
            allowClear
            showArrow
            showSearch
            filterOption={(inputValue, option) =>
              option.name.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
            }
            fieldNames={{
              label: 'name',
              value: 'channelId',
              key: 'channelId',
            }}
            options={channelOptions}
          ></Select>
        </Form.Item>
      )}
      {sendEmail && (
        <Form.Item
          label={getIntl().formatMessage({
            id: 'work.order.reply.email.theme',
            defaultValue: '邮件主题：',
          })}
          name="emailTheme"
          rules={[
            {
              required: true,
              message: getIntl().formatMessage({
                id: 'new.worktable.email.theme.required',
                defaultValue: '请输入邮件主题',
              }),
            },
            {
              max: 200,
              message: getIntl().formatMessage({
                id: 'new.worktable.email.theme.required.max',
                defaultMessage: '邮件主题最多输入40个字',
              }),
            },
          ]}
        >
          <Input
            placeholder={getIntl().formatMessage({
              id: 'new.worktable.email.theme.required',
              defaultValue: '请输入邮件主题',
            })}
            maxLength={40}
          />
        </Form.Item>
      )}
      {sendEmail && (
        <Form.Item
          label={getIntl().formatMessage({
            id: 'new.worktable.work.record.recipient',
            defaultValue: '收件人：',
          })}
        >
          <Space.Compact
            block
            style={{ width: '100%', height: '32px', marginBottom: '10px' }}
          >
            <Form.Item
              name="recipient"
              rules={[
                {
                  required: true,
                  message: getIntl().formatMessage({
                    id: 'new.worktable.work.record.recipient.required',
                    defaultValue: '请输入收件人',
                  }),
                },
              ]}
              style={{
                width:
                  showMakeCopy && showBccCopy
                    ? '100%'
                    : showMakeCopy || showBccCopy
                    ? '98%'
                    : '92%',
                marginRight: showMakeCopy && showBccCopy ? '0%' : '2%',
              }}
            >
              <Select
                mode="tags"
                placeholder={getIntl().formatMessage({
                  id: 'new.worktable.work.record.recipient.required',
                  defaultValue: '请输入收件人',
                })}
                onChange={handleChange}
                open={open}
                onOpenChange={handleOpenChange}
                // onInputKeyDown={handleKeyDown}
              />
            </Form.Item>
            <span
              style={{
                display: showMakeCopy ? 'none' : 'block',
                cursor: 'pointer',
              }}
              onClick={handleShowMakeCopy}
            >
              {MakeCopyIcon()}
            </span>
            <span
              style={{
                display: showBccCopy ? 'none' : 'block',
                cursor: 'pointer',
              }}
              onClick={handleShowBccCopy}
            >
              {BccCopyIcon()}
            </span>
          </Space.Compact>
        </Form.Item>
      )}
      {sendEmail && showMakeCopy && (
        <Form.Item
          label={getIntl().formatMessage({
            id: 'new.worktable.email.make.copy',
            defaultValue: '抄送给：',
          })}
          name="makeCopy"
          rules={[
            {
              required: false,
              message: getIntl().formatMessage({
                id: 'new.worktable.email.make.copy.required',
                defaultValue: '请输入抄送人邮箱',
              }),
            },
          ]}
        >
          <Select
            mode="tags"
            placeholder={getIntl().formatMessage({
              id: 'new.worktable.email.make.copy.required',
              defaultValue: '请输入抄送人邮箱',
            })}
            open={open}
            onOpenChange={handleOpenChange}
            // onInputKeyDown={handleKeyDown}
          />
        </Form.Item>
      )}
      {sendEmail && showBccCopy && (
        <Form.Item
          label={getIntl().formatMessage({
            id: 'new.worktable.email.bcc.copy',
            defaultValue: '密送给：',
          })}
          name="bccCopy"
          rules={[
            {
              required: false,
              message: getIntl().formatMessage({
                id: 'new.worktable.email.bcc.copy.required',
                defaultValue: '请输入密送人邮箱',
              }),
            },
          ]}
        >
          <Select
            mode="tags"
            placeholder={getIntl().formatMessage({
              id: 'new.worktable.email.bcc.copy.required',
              defaultValue: '请输入密送人邮箱',
            })}
            open={open}
            onOpenChange={handleOpenChange}
            // onInputKeyDown={handleKeyDown}
          />
        </Form.Item>
      )}
      <Spin spinning={editorLoading}>
        <div className={styles.aiOperation}>
          <HOCAuth authKey={'ai_aiGenerateEmailDraft'}>
            {authAccess => (
              <Popover
                style={{ width: '30% !important' }}
                placement="bottomLeft"
                content={
                  <div>
                    <p className="popoverTitle">
                      <FormattedMessage
                        id="phone.header.select.language"
                        defaultMessage="选择语言"
                      />
                    </p>
                    <Select
                      style={{ width: '100%', marginBottom: '15px' }}
                      showSearch
                      placeholder={getIntl().formatMessage({
                        id: 'home.set.language.select',
                        defaultValue: '请选择语言',
                      })}
                      value={lang}
                      onChange={e => handleTranslationCodeChange(e)}
                      filterSort={(optionA, optionB) =>
                        (optionA?.label ?? '')
                          .toLowerCase()
                          .localeCompare((optionB?.label ?? '').toLowerCase())
                      }
                      options={translationCodeList.map(item => ({
                        label: item.label,
                        value: item.label,
                        key: item.value,
                      }))}
                    />
                    <p className="popoverTitle">
                      <FormattedMessage
                        id="work.table.email.draft.reply"
                        defaultMessage="回复草稿"
                      />
                      <span>
                        <FormattedMessage
                          id="work.table.email.draft.reply.1"
                          defaultMessage="（可选）"
                        />
                      </span>
                    </p>
                    <TextArea
                      value={draftValue}
                      onChange={handleChangeDraftText}
                      autoSize={{
                        minRows: 4,
                        maxRows: 4,
                      }}
                      placeholder={getIntl().formatMessage({
                        id: 'new.worktable.email.add.draft.placeholder',
                        defaultValue: '告诉AIGC你想写什么内容...',
                      })}
                    />
                    <div className="btnContainer">
                      <Button onClick={cancelEmailDraft}>
                        <FormattedMessage
                          id="work.order.management.btn.cancel"
                          defaultMessage="取消"
                        />
                      </Button>
                      <Button
                        className="intelligenceSummary"
                        icon={AiBtnIcon()}
                        onClick={aiGenerateEmailDraft}
                      >
                        <FormattedMessage
                          id="work.table.email.draft.reply.btn"
                          defaultMessage="生成回复"
                        />
                      </Button>
                    </div>
                  </div>
                }
                title={null}
                trigger="click"
                open={draftOpen}
                overlayClassName="draftContainerPopover1"
                onOpenChange={handleDraftOpenChange}
                getPopupContainer={trigger => trigger.parentElement}
              >
                <div className={styles.draftContainer}>
                  {NewAiIcon()}
                  <span className={styles.draftText}>
                    <FormattedMessage
                      id="emailMarketing.form.status.op.1"
                      defaultMessage="草稿"
                    />
                  </span>
                </div>
              </Popover>
            )}
          </HOCAuth>
          <HOCAuth authKey={'ai_aiBeautifyEmail'}>
            {authAccess => (
              <Tooltip
                title={getIntl().formatMessage({
                  id: 'new.worktable.email.hover.tips',
                  defaultValue: '请选中一段文字后再进行AIGC优化',
                })}
              >
                <div
                  ref={aiOperationRef}
                  onClick={selectedText ? handleOptimize : ''}
                  className={`${styles.optimizeContainer} ${authAccess &&
                    'disabled'}`}
                >
                  {selectedText ? OptimizeIcon() : OptimizeNormalIcon()}
                  <span>
                    <FormattedMessage
                      id="new.worktable.email.optimize"
                      defaultMessage="优化"
                    />
                  </span>
                </div>
              </Tooltip>
            )}
          </HOCAuth>
          <HOCAuth authKey={'ai_aiExpandEmail'}>
            {authAccess => (
              <Tooltip
                title={getIntl().formatMessage({
                  id: 'new.worktable.email.hover.tips.1',
                  defaultValue: '请选中一段文字后再进行AIGC扩写',
                })}
              >
                <div
                  className={`${styles.elaborateContainer} ${authAccess &&
                    'disabled'}`}
                  onClick={selectedText ? aiExpandEmail : ''}
                >
                  {selectedText ? ElaborateIcon() : ElaborateNormalIcon()}
                  <span>
                    <FormattedMessage
                      id="new.worktable.email.elaborate"
                      defaultMessage="扩写"
                    />
                  </span>
                </div>
              </Tooltip>
            )}
          </HOCAuth>
          <HOCAuth authKey={'ai_aiSummarizeEmail'}>
            {authAccess => (
              <Tooltip
                title={getIntl().formatMessage({
                  id: 'new.worktable.email.hover.tips.2',
                  defaultValue: '请选中一段文字后再进行AIGC缩写',
                })}
              >
                <div
                  className={`${styles.abbreviationContainer} ${authAccess &&
                    'disabled'}`}
                  onClick={selectedText ? aiSummarizeEmail : ''}
                >
                  {selectedText ? AbbreviationIcon() : AbbreviationNormalIcon()}
                  <span>
                    <FormattedMessage
                      id="new.worktable.email.abbreviation"
                      defaultMessage="缩写"
                    />
                  </span>
                </div>
              </Tooltip>
            )}
          </HOCAuth>
          <Dropdown
            menu={{
              items: [
                {
                  key: '1',
                  label: (
                    <HOCAuth authKey={'ai_grammarCheck'}>
                      {authAccess => {
                        return (
                          <div
                            onClick={
                              selectedText && !authAccess ? grammarCheck : null
                            }
                            className={
                              selectedText && !authAccess
                                ? 'detailItems'
                                : 'detailNormalItems'
                            }
                          >
                            <span>
                              {selectedText && !authAccess ? (
                                <GrammarCorrectionIcon />
                              ) : (
                                <GrammarCorrectionNormalIcon />
                              )}
                            </span>
                            <p>
                              <FormattedMessage
                                id="new.worktable.email.grammar.correction"
                                defaultMessage="语法纠错"
                              />
                            </p>
                          </div>
                        );
                      }}
                    </HOCAuth>
                  ),
                },
                {
                  key: '2',
                  label: (
                    <HOCAuth authKey={'ai_aiGentleEmail'}>
                      {authAccess => (
                        <div
                          onClick={
                            selectedText && !authAccess ? aiGentleEmail : null
                          }
                          className={
                            selectedText && !authAccess
                              ? 'detailItems'
                              : 'detailNormalItems'
                          }
                        >
                          <span>
                            {selectedText && !authAccess ? (
                              <GentleIcon />
                            ) : (
                              <GentleNormalIcon />
                            )}
                          </span>
                          <p>
                            <FormattedMessage
                              id="new.worktable.email.gentle"
                              defaultMessage="温柔"
                            />
                          </p>
                        </div>
                      )}
                    </HOCAuth>
                  ),
                },
                {
                  key: '3',
                  label: (
                    <HOCAuth authKey={'ai_aiNeutralEmail'}>
                      {authAccess => (
                        <div
                          onClick={
                            selectedText && !authAccess ? aiNeutralEmail : null
                          }
                          className={
                            selectedText && !authAccess
                              ? 'detailItems'
                              : 'detailNormalItems'
                          }
                        >
                          <span>
                            {selectedText && !authAccess ? (
                              <NeutralityIcon />
                            ) : (
                              <NeutralityNormalIcon />
                            )}
                          </span>
                          <p>
                            <FormattedMessage
                              id="new.worktable.email.neutrality"
                              defaultMessage="中立"
                            />
                          </p>
                        </div>
                      )}
                    </HOCAuth>
                  ),
                },
                {
                  key: '4',
                  label: (
                    <HOCAuth authKey={'ai_formal_email'}>
                      {authAccess => (
                        <div
                          onClick={
                            selectedText && !authAccess ? formalEmail : null
                          }
                          className={
                            selectedText && !authAccess
                              ? 'detailItems'
                              : 'detailNormalItems'
                          }
                        >
                          <span>
                            {selectedText && !authAccess ? (
                              <FormalIcon />
                            ) : (
                              <FormalNormalIcon />
                            )}
                          </span>
                          <p>
                            <FormattedMessage
                              id="new.worktable.email.formal"
                              defaultMessage="正式"
                            />
                          </p>
                        </div>
                      )}
                    </HOCAuth>
                  ),
                },
              ],
            }}
            overlayClassName="moreOperationDrodown"
          >
            <div className={styles.moreIconContainer}>{MoreIcon()}</div>
          </Dropdown>
          <div className={styles.operationLine}></div>
          <Popover
            placement="topLeft"
            title={null}
            open={emailTemplateOpen}
            getPopupContainer={trigger => trigger.parentElement}
            content={
              <div>
                <div className="emailTemplateTitle">
                  <span>
                    <FormattedMessage
                      id="new.worktable.email.email.template"
                      defaultMessage="邮件模板"
                    />
                  </span>
                  <img
                    onClick={handleShowEmailTemplate}
                    src={AddEmailTemplateIcon}
                  />
                </div>
                <div className="searchContainer">
                  <Input
                    value={search}
                    placeholder={getIntl().formatMessage({
                      id: 'document.knowledge.base.input.tips',
                      defaultValue: '请输入想要搜索的内容',
                    })}
                    onPressEnter={event => searchPressEnter(event)}
                    onChange={searchChange}
                    prefix={<SearchOutlined />}
                  />
                </div>
                <div className="emailTemplateList">
                  {emailTemplatesList?.map(item => {
                    if (item.isDefault) {
                      return (
                        <div
                          onDoubleClick={() => handleSelectTemplate(item)}
                          className="emailTemplateItem"
                        >
                          <p className="defaultItem">
                            <div className="circleText"></div>
                            {item.templateName}
                          </p>
                          <div className="defaultContainer">
                            <FormattedMessage
                              id="new.worktable.email.default.text"
                              defaultValue="默认"
                            />
                          </div>
                          <div className="emailTemplateOperation">
                            <img src={EmailTemplateLikeNoIcon} />
                            <img
                              onClick={() =>
                                handleShowEditorEmailTemplate(item)
                              }
                              src={EmailTemplateEditorIcon}
                            />
                            <Popconfirm
                              title={getIntl().formatMessage({
                                id:
                                  'new.worktable.email.default.text.delete.tips',
                                defaultValue: '是否删除当前邮件模版？',
                              })}
                              onConfirm={() => confirmDefaultDelete(item)}
                              onCancel={cancelDefaultDelete}
                              okText={getIntl().formatMessage({
                                id:
                                  'work.order.management.table.robot.work.order.yes',
                                defaultValue: '是',
                              })}
                              cancelText={getIntl().formatMessage({
                                id:
                                  'work.order.management.table.robot.work.order.no',
                                defaultValue: '否',
                              })}
                              getPopupContainer={trigger =>
                                trigger.parentElement
                              }
                              overlayClassName="defaultTipsConfirm"
                            >
                              <img src={EmailTemplateDeleteIcon} />
                            </Popconfirm>
                          </div>
                        </div>
                      );
                    } else {
                      return (
                        <div
                          onDoubleClick={() => handleSelectTemplate(item)}
                          className="emailTemplateItem"
                        >
                          <p className="normalItem">
                            <div className="circleText"></div>
                            {item.templateName}
                          </p>
                          <div className="emailTemplateOperation">
                            <Popconfirm
                              title={getIntl().formatMessage({
                                id: 'new.worktable.email.default.text.tips',
                                defaultValue:
                                  '是否将当前邮件模版设置为默认邮件模版',
                              })}
                              onConfirm={() => confirmDefault(item)}
                              onCancel={cancelDefault}
                              okText={getIntl().formatMessage({
                                id:
                                  'customerInformation.add.basicInformation.button.save',
                                defaultValue: '保存',
                              })}
                              cancelText={getIntl().formatMessage({
                                id: 'awsAccountSetting.cancel.btn',
                                defaultValue: '取消',
                              })}
                              getPopupContainer={trigger =>
                                trigger.parentElement
                              }
                              overlayClassName="defaultTipsConfirm"
                            >
                              <img src={EmailTemplateLikeIcon} />
                            </Popconfirm>
                            <img
                              onClick={() =>
                                handleShowEditorEmailTemplate(item)
                              }
                              src={EmailTemplateEditorIcon}
                            />
                            <Popconfirm
                              title={getIntl().formatMessage({
                                id:
                                  'new.worktable.email.default.text.delete.tips',
                                defaultValue: '是否删除当前邮件模版？',
                              })}
                              onConfirm={() => confirmDefaultDelete(item)}
                              onCancel={cancelDefaultDelete}
                              okText={getIntl().formatMessage({
                                id:
                                  'work.order.management.table.robot.work.order.yes',
                                defaultValue: '是',
                              })}
                              cancelText={getIntl().formatMessage({
                                id:
                                  'work.order.management.table.robot.work.order.no',
                                defaultValue: '否',
                              })}
                              getPopupContainer={trigger =>
                                trigger.parentElement
                              }
                              overlayClassName="defaultTipsConfirm"
                            >
                              <img src={EmailTemplateDeleteIcon} />
                            </Popconfirm>
                          </div>
                        </div>
                      );
                    }
                  })}
                </div>
              </div>
            }
            trigger="click"
            onOpenChange={handleEmailTemplateOpenChange}
            overlayClassName="emailTemplateContainerPopover"
          >
            <div ref={popoverRef} className={styles.emailTemplateIcon}>
              <Tooltip
                overlayClassName="emailTemplateTooltip"
                title={getIntl().formatMessage({
                  id: 'new.worktable.email.email.template',
                  defaultValue: '邮件模板',
                })}
              >
                {EmailTemplateIcon()}
              </Tooltip>
            </div>
          </Popover>
        </div>
        <Form.Item
          label={null}
          name="emailBody"
          rules={[
            {
              required: false,
              message: getIntl().formatMessage({
                id: 'new.worktable.email.email.body.required',
                defaultValue: '请输入邮件内容',
              }),
            },
          ]}
        >
          <div
            ref={textAreaRef}
            // onMouseUp={handleMouseUp}
            className={styles.editorjsNew}
            id="editorjs"
          ></div>
        </Form.Item>
      </Spin>
      <div className={styles.attachmentContainer}>
        <span className={styles.attachmentIcon}>{AttachmentIcon()}</span>
        <Form.Item
          label={getIntl().formatMessage({
            id: 'new.worktable.email.add.attachment',
            defaultValue: '添加附件：',
          })}
          name="addAttachment"
          rules={[
            {
              required: false,
            },
          ]}
        >
          <Upload
            listType="picture"
            className="uploadListInline"
            {...uploadProperties}
          >
            <img
              className={styles.emailAddAttachmentIcon}
              src={EmailAddAttachmentIcon}
            />
          </Upload>
        </Form.Item>
      </div>

      {/*创建-修改邮件模板弹窗*/}
      <Modal
        title={
          editorStatus
            ? getIntl().formatMessage({
                id: 'new.worktable.email.new.editor.email.template',
                defaultValue: '修改邮件模板',
              })
            : getIntl().formatMessage({
                id: 'new.worktable.email.new.create.email.template',
                defaultValue: '新建邮件模板',
              })
        }
        footer={null}
        onCancel={handleCancel}
        mask={false}
        open={isModalOpen}
        className="emailTemplateModal"
      >
        <Form
          form={form}
          name="basic1"
          onFinish={onFinishSave}
          autoComplete="off"
        >
          <Form.Item
            label={getIntl().formatMessage({
              id: 'work.order.reply.email.template.name',
              defaultValue: '模板名称：',
            })}
            name="templateName"
            rules={[
              {
                max: 200,
                message: getIntl().formatMessage({
                  id: 'new.worktable.email.template.required.max',
                  defaultMessage: '邮件模板最多输入200个字',
                }),
              },
              {
                required: true,
                message: getIntl().formatMessage({
                  id: 'new.worktable.email.template.name.required',
                  defaultValue: '请输入模板名称',
                }),
              },
            ]}
          >
            <Input
              placeholder={getIntl().formatMessage({
                id: 'new.worktable.email.template.name.required',
                defaultValue: '请输入模板名称',
              })}
              maxLength={40}
            />
          </Form.Item>

          <div className="emailTemplateBody">
            <Form.Item
              label={null}
              name="emailBody"
              rules={[
                {
                  required: false,
                  message: getIntl().formatMessage({
                    id: 'new.worktable.email.email.body.required',
                    defaultValue: '请输入邮件内容',
                  }),
                },
              ]}
            >
              <div className="editorjs1" id="editorjs1"></div>
            </Form.Item>
          </div>
          <Form.Item name="action">
            <Input type="hidden" /> {/* 隐藏的 action 字段 */}
          </Form.Item>
          <Form.Item
            style={{
              width: '100%',
              textAlign: 'center',
              marginBottom: '20px',
              marginTop: '-50px',
            }}
          >
            <div className={styles.defaultBtnContainer}>
              <Button className={styles.cancelBtn} onClick={handleCancel}>
                <FormattedMessage
                  id="awsAccountSetting.cancel.btn"
                  defaultMessage="取消"
                />
              </Button>
              <Button
                loading={saveLoading}
                type={'primary'}
                htmlType="submit"
                onClick={() => form.setFieldsValue({ action: 'save' })}
              >
                <FormattedMessage
                  id="awsAccountSetting.save.btn"
                  defaultMessage="保存"
                />
              </Button>
              <Button
                style={{ display: editorStatus ? 'none' : 'inline-block' }}
                loading={applyLoading}
                type={'primary'}
                onClick={() => {
                  form.setFieldsValue({ action: 'apply' });
                  form.submit();
                }}
              >
                <FormattedMessage
                  id="new.worktable.email.save.use.btn"
                  defaultMessage="保存并应用"
                />
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
});

export default EmailTemplateComponent;
