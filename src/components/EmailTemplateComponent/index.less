.replyEmailContent {
  width: 100%;
  padding: 0px 5px;
  //margin-top: 10px;

  .aiOperation {
    width: 100%;
    height: 24px;
    position: relative;
    .draftContainer {
      height: 24px;
      border-radius: 2px;
      padding: 2px 4px;
      float: left;
    }
    .draftContainer:hover {
      background: #eee;
    }
    .draftText {
      color: #333;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 21px;
      float: left;
    }

    .optimizeContainer,
    .elaborateContainer,
    .abbreviationContainer {
      float: left;
      margin-left: 10px;
      height: 20px;
      margin-top: 2px;
      cursor: pointer;
      span {
        display: none;
      }
    }
    .optimizeContainer:hover,
    .elaborateContainer:hover,
    .abbreviationContainer:hover {
      span {
        display: block;
        color: #333;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 21px;
        float: left;
        margin-left: 4px;
      }
    }
    .moreIconContainer {
      float: left;
      margin-left: 10px;
      height: 20px;
      margin-top: 1px;
      cursor: pointer;
    }
    .operationLine {
      width: 1px;
      height: 16px;
      background: #e6e6e6;
      float: left;
      margin-left: 10px;
      margin-top: 3px;
    }
    .emailTemplateIcon {
      float: left;
      margin-left: 10px;
      height: 20px;
      margin-top: 2px;
    }
  }
  .editorjsNew {
    width: 100%;
    min-height: 300px;
    //overflow: hidden;
    //overflow-y: scroll;
    margin-top: 10px;
    border-radius: 4px;
    border: 1px solid #e6e6e6;
    background: rgba(255, 255, 255, 0.5);
    padding: 2px 10px;

    :global {
      img {
        max-width: 250px;
      }
      .codex-editor__redactor {
        padding-bottom: 0px !important;
      }
      .ce-toolbar__content,
      .ce-block__content {
        max-width: 90%;
      }
    }
  }
  .attachmentContainer {
    padding-bottom: 20px;
    //border-bottom: 1px solid #e6e6e6;
    margin-bottom: 20px;

    .attachmentIcon {
      float: left;
      margin-top: 7px;
      margin-right: 4px;
    }

    .emailAddAttachmentIcon {
      width: 24px;
      height: 24px;
      float: left;
      cursor: pointer;
      margin-top: 3px;
    }
  }

  :global {
    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      display: none;
    }
    .ant-form-item {
      margin-bottom: 20px;
    }
    .ant-form-item-label > label {
      color: #333;
      font-size: 12px;
      font-style: normal;
      font-weight: 700;
      line-height: 32px;
      width: auto;
    }
    .ant-input {
      border-radius: 6px;
      //border: 1px solid #E6E6E6;
      background: rgba(255, 255, 255, 0.5);
      font-size: 12px;
      height: 32px;
      box-shadow: none;
    }
    .ant-select {
      border-radius: 6px;
      background: rgba(255, 255, 255, 0.5);
      font-size: 12px;
      height: 32px;
    }
    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      box-shadow: none;
      border-radius: 6px;
      height: 32px;
      overflow: hidden;
      overflow-y: scroll;
    }
    .ant-space-item:first-child {
      //width: 83%;
    }
    .ant-select-multiple .ant-select-selection-item {
      border-radius: 4px;
      border: 1px solid rgba(52, 99, 252, 0.6);
      background: linear-gradient(
          0deg,
          rgba(52, 99, 252, 0.1) 0%,
          rgba(52, 99, 252, 0.1) 100%
        ),
        #fff;
      color: #3463fc;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px; /* 150% */
    }
    .ant-select-multiple .ant-select-selection-item-remove {
      color: #3463fc;
    }
    .ant-select-compact-item.ant-select-compact-first-item.ant-select:not(.ant-select-compact-last-item):not(.ant-select-compact-item-rtl)
      > .ant-select-selector {
      border-top-right-radius: 6px;
      border-bottom-right-radius: 6px;
    }

    .ant-checkbox-checked .ant-checkbox-inner {
      background-color: #3463fc;
      border-color: #3463fc;
    }
  }

  .defaultBtnContainer {
    width: 100%;
    //margin-top: 15px;
    //margin-bottom: 15px;
    text-align: center;
    .cancelBtn {
      width: 92px;
      height: 30px;
      padding: 2px 10px;
      font-size: 12px;
      color: #3463fc;
      border-radius: 4px;
      background-color: #fff;
      border: 1px solid #3463fc;
      margin-right: 20px;
    }
    .sendBtn {
      color: #fff;
      font-size: 12px;
      border-radius: 4px;
      border: none;
      height: 30px;
      padding: 2px 24px;
      background: #3463fc;
      //width: 92px;
    }
  }
}
