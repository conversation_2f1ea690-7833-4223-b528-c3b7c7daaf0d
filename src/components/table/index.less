.agentContent {
  // margin: 20px;
  background-color: #fff;
  min-height: 100%;

  .agentContentTop {
    padding: 20px;
    height: 60px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;

    .agentContentTopTitle {
      height: 28px;
      padding: 1px 8px;
      border-left: 3px #3463fc solid;
      font-size: 18px;
    }
  }

  .agentContentTopBack {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    font-size: 18px;
  }

  .headContent {
    width: 100%;
    height: 40px;
    background: #fff;
    border-radius: 6px;
    padding: 0px 24px 0px;

    .ghostBtn {
      margin-right: 16px;
    }

    label {
      color: rgba(0, 0, 0, 0.88);
      float: left;
      line-height: 32px;
      margin-right: 8px;
    }

    :global {
      .ant-input {
        width: 25%;
        float: left;
        border-radius: 6px;
        margin-right: 2%;
      }

      .ant-select {
        width: 80%;
      }

      .ant-select:not(.ant-select-customize-input) .ant-select-selector {
        border-radius: 6px;
        font-size: 12px;
      }
    }
  }

  .agentManagementContent {
    width: 100%;
    min-height: 100%;
    background: #fff;
    border-radius: 6px;
    padding: 0 24px 24px;
    // margin-top: 16px;
    // overflow: hidden;
    // overflow-y: scroll;
    // scrollbar-width: 0px;
    // -ms-overflow-style: none;

    .titleContent {
      width: 100%;
      height: 50px;

      p {
        font-size: 16px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.88);
        float: left;
      }

      :global {
        .ant-btn-primary {
          float: right;
          font-size: 12px;
        }
      }
    }

    .tableContent {
      width: 100%;
      // height: 500px;
      margin-top: 12px;

      // overflow: hidden;
      //       overflow-y: scroll;
      //       scrollbar-width: 0px;
      //       -ms-overflow-style: none;
      .userType {
        color: #409eff;
      }

      .connectList {
        width: 150px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .operationArray {
        display: flex;
        flex-direction: row;

        .operationText {
          margin-right: 8px;
        }
      }

      :global {
        .ant-table-thead > tr > th {
          background-color: #f3f7fe;
        }
      }
    }

    .tableContent::-webkit-scrollbar {
      display: none;
    }
  }
}
