import React, {
  useState,
  useCallback,
  useEffect,
  useImperativeHandle,
  forwardRef,
} from 'react';
import styles from './index.less';
import {
  Input,
  Button,
  Table,
  Tooltip,
  Modal,
  notification,
  Form,
  Col,
  Row,
  Select,
} from 'antd';
import { getIntl, FormattedMessage, useDispatch } from 'umi';
import { MenuOutlined } from '@ant-design/icons';
import { DndContext } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import HOCAuth from '@/components/HOCAuth/index';
const BackIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M1.8525 9.32188L8.95375 2.54375C9.01313 2.48707 9.08309 2.44264 9.15964 2.413C9.2362 2.38336 9.31783 2.36909 9.3999 2.371C9.48197 2.37292 9.56285 2.39098 9.63794 2.42416C9.71302 2.45734 9.78084 2.50499 9.8375 2.56438L10.2688 3.01625C10.374 3.12631 10.4354 3.27098 10.4415 3.42312C10.4476 3.57525 10.3979 3.72438 10.3019 3.8425L10.2481 3.9L4.83938 9.0625H17.8125C17.9783 9.0625 18.1372 9.12835 18.2544 9.24556C18.3717 9.36277 18.4375 9.52174 18.4375 9.6875V10.3125C18.4375 10.4783 18.3717 10.6372 18.2544 10.7544C18.1372 10.8717 17.9783 10.9375 17.8125 10.9375H4.83938L10.2481 16.1C10.3583 16.2051 10.4263 16.3467 10.4394 16.4984C10.4526 16.6501 10.4099 16.8013 10.3194 16.9238L10.2694 16.9838L9.8375 17.4356C9.78084 17.495 9.71302 17.5427 9.63794 17.5758C9.56285 17.609 9.48197 17.6271 9.3999 17.629C9.31783 17.6309 9.2362 17.6166 9.15964 17.587C9.08309 17.5574 9.01313 17.5129 8.95375 17.4563L1.8525 10.6781C1.7608 10.5906 1.68781 10.4854 1.63793 10.3688C1.58806 10.2522 1.56235 10.1268 1.56235 10C1.56235 9.87323 1.58806 9.74776 1.63793 9.63121C1.68781 9.51465 1.7608 9.40942 1.8525 9.32188Z"
      fill="#3463FC"
    />
  </svg>
);
const TableComponents = forwardRef(
  (
    {
      props = {},
      onSelectChange,
      languageData,
      getList,
      getLanguage,
      AgentContenClass,
      HeadContentClass,
      AgentManagementContentClass,
    },
    ref,
  ) => {
    const dispatch = useDispatch();
    let [formShow, setFormShow] = useState(props.formShow || false);
    let [btnShow, setBtnShow] = useState(props.btnShow || false);
    let [titleShow, setTitleShow] = useState(props.titleShow || true);
    let [selectedRowKeys, setSelectedRowKeys] = useState(
      props.selectedRowKeys || [],
    );
    let [languageList, setLanguageList] = useState([]);
    let [language, setLanguage] = useState('');
    let [pageNum, setPageNum] = useState(1);
    let [pageSize, setPageSize] = useState(10);
    let [rows, setRows] = useState([]);
    let [total, setTotal] = useState(0);
    // 添加按钮  加载
    const [loadings, setLoadings] = useState([]);

    useEffect(() => {
      getLanguageList();
    }, []);

    useEffect(() => {
      queryList();
    }, [pageNum, pageSize, language]);

    /**
     * 按钮加载状态
     * @type {(function(*): void)|*}
     */
    const enterLoading = useCallback(
      index => {
        setLoadings(prevLoadings => {
          const newLoadings = [...prevLoadings];
          newLoadings[index] = true;
          return newLoadings;
        });
      },
      [loadings],
    );
    /**
     * 语言改变
     */
    const handleLenguageChange = value => {
      pageChange(1, 10);
      setLanguage(value);
      getLanguage(value);
    };
    /**
     * 按钮取消加载状态
     * @type {(function(*): void)|*}
     */
    const existLoading = useCallback(
      index => {
        setLoadings(prevLoadings => {
          const newLoadings = [...prevLoadings];
          newLoadings[index] = false;
          return newLoadings;
        });
      },
      [loadings],
    );
    /**语言下拉 */
    const getLanguageList = () => {
      dispatch({
        type: 'personalCenter/listLanguage',
        callback: response => {
          let { code, data, msg } = response;
          if (code === 200) {
            setLanguageList(data);
            let l = localStorage.getItem('lang');
            setLanguage(l);
          } else {
            notification.error({
              message: msg,
            });
          }
        },
      });
    };
    const pageChange = (num, size) => {
      setPageNum(num);
      setPageSize(size);
    };
    /**
     * 查询列表信息
     */
    const queryList = useCallback(() => {
      enterLoading(0);
      dispatch({
        type: props.interface,
        payload: {
          pageNum,
          pageSize,
          languageCode: language,
        },
        callback: response => {
          let { code, data, msg } = response;
          if (200 === code) {
            // console.log(data)
            let { rows, total } = data;
            setRows(rows);
            setTotal(total);
            console.log('1111', language, localStorage.getItem('lang'));
          }
          existLoading(0);
        },
      });
    }, [pageNum, pageSize, language]);

    /**
     * 删除某一行
     */
    useImperativeHandle(ref, () => ({
      deleteList: (interfaceName, data) => {
        dispatch({
          type: interfaceName,
          payload: data,
          callback: response => {
            let { code, data, msg } = response;
            if (200 === code) {
              queryList();
              notification.success({
                message: getIntl().formatMessage({
                  id: 'user.management.remove.success',
                }),
              });
            } else {
              notification.error({
                message: msg,
              });
            }
          },
        });
      },
      queryListRef: () => {
        console.log('oooo');
        enterLoading(0);
        dispatch({
          type: props.interface,
          payload: {
            pageNum,
            pageSize,
            languageCode: language,
          },
          callback: response => {
            let { code, data, msg } = response;
            if (200 === code) {
              // console.log(data)
              let { rows, total } = data;
              setRows(rows);
              setTotal(total);
            }
            existLoading(0);
          },
        });
      },
    }));
    /**
     * 拖拽
     */
    const Row = ({ children, ...props }) => {
      const {
        attributes,
        listeners,
        setNodeRef,
        setActivatorNodeRef,
        transform,
        transition,
        isDragging,
      } = useSortable({
        id: props['data-row-key'],
      });
      const style = {
        ...props.style,
        transform: CSS.Transform.toString(
          transform && {
            ...transform,
            scaleY: 1,
          },
        ),
        transition,
        ...(isDragging
          ? {
              position: 'relative',
              zIndex: 999,
            }
          : {}),
      };
      return (
        <tr {...props} ref={setNodeRef} style={style} {...attributes}>
          {React.Children.map(children, child => {
            if (child.key === 'sort') {
              return React.cloneElement(child, {
                children: (
                  <Tooltip
                    placement="top"
                    title={<FormattedMessage id="user.management.remove" />}
                  >
                    <MenuOutlined
                      ref={setActivatorNodeRef}
                      style={{
                        touchAction: 'none',
                        cursor: 'move',
                      }}
                      {...listeners}
                    />
                  </Tooltip>
                ),
              });
            }
            return child;
          })}
        </tr>
      );
    };
    const onDragEnd = ({ active, over }) => {
      console.log(active, over);
      if (active.id !== over?.id) {
        setRows(previous => {
          console.log(previous);
          const activeIndex = previous.findIndex(
            i => i[props.key] === active.id,
          );
          const overIndex = previous.findIndex(i => i[props.key] === over?.id);
          const data = arrayMove(previous, activeIndex, overIndex);
          const payLoadData = data.map(item => {
            return {
              customerExtDefId: item.customerExtDefId,
              customerExtDefOrder: item.customerExtDefOrder,
            };
          });
          dispatch({
            type: props.sortInterfaceName,
            payload: { data: payLoadData, params: { languageCode: language } },
            callback: response => {
              let { code, data, msg } = response;
              if (200 === code) {
                queryList();
                // notification.success({
                //   message: getIntl().formatMessage({
                //     id: 'user.management.remove.success',
                //   }),
                // });
              } else {
                notification.error({
                  message: msg,
                });
              }
            },
          });
          return arrayMove(previous, activeIndex, overIndex);
        });
      }
    };
    return (
      <div className={`${AgentContenClass}  ${styles.agentContent}`}>
        {titleShow &&
          (props.isBack ? (
            <div className={styles.agentContentTopBack}>
              <div style={{ display: 'flex' }}>
                <span
                  style={{
                    marginRight: 10,
                    lineHeight: '35px',
                    cursor: 'pointer',
                  }}
                  onClick={() => history.go(-1)}
                >
                  {BackIcon()}
                </span>
                <FormattedMessage id={languageData.title} />
              </div>
              <div>
                {props.isBackBtnShow &&
                  props.btnList.map((item, index) => (
                    <HOCAuth authKey={item.authKey}>
                      {authAccess => (
                        <Button
                          type={item.type}
                          onClick={item.onClick}
                          style={{ fontSize: 14 }}
                          key={index}
                          disabled={authAccess}
                          title={
                            authAccess
                              ? getIntl().formatMessage({
                                  id: 'auth.access.no.1',
                                  defaultValue: '您的当前版本不支持此功能',
                                })
                              : ''
                          }
                        >
                          {item.icons}
                          <FormattedMessage id={item.buttonLang} />
                        </Button>
                      )}
                    </HOCAuth>
                  ))}
              </div>
            </div>
          ) : (
            <div className={styles.agentContentTop}>
              <div className={styles.agentContentTopTitle}>
                <FormattedMessage id={languageData.title} />
              </div>
            </div>
          ))}
        {(formShow || btnShow) && (
          <div className={`${styles.headContent} ${HeadContentClass}`}>
            {formShow && (
              <span>
                <Tooltip
                  title={getIntl().formatMessage({
                    id: 'user.management.user.name',
                  })}
                >
                  <label>
                    <FormattedMessage id="user.management.user.name" />：
                  </label>
                </Tooltip>
                <Input
                  placeholder={getIntl().formatMessage({
                    id: 'customerInformation.customerName.placeholder',
                  })}
                  onChange={e => setUserName(e.target.value)}
                  style={{ width: 264, height: 32 }}
                />
              </span>
            )}
            {btnShow && language && (
              <div>
                <Form>
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'row',
                      justifyContent: 'space-between',
                    }}
                  >
                    <Col span={8}>
                      <Form.Item
                        label={getIntl().formatMessage({
                          id: 'home.language.select',
                        })}
                      >
                        <Select
                          placeholder={getIntl().formatMessage({
                            id: 'home.set.language.select',
                            defaultValue: '请选择语言',
                          })}
                          options={languageList.map(item => ({
                            label: item.languageName,
                            value: item.languageCode,
                            key: item.languageCode,
                          }))}
                          onChange={value => handleLenguageChange(value)}
                          defaultValue={language}
                        />
                      </Form.Item>
                    </Col>
                    <Col>
                      {!props.isBackBtnShow &&
                        props.btnList.map((item, index) => (
                          <HOCAuth authKey={item.authKey}>
                            {authAccess => (
                              <Button
                                type={item.type}
                                onClick={item.onClick}
                                style={{ fontSize: 14 }}
                                key={index}
                                disabled={authAccess}
                                title={
                                  authAccess
                                    ? getIntl().formatMessage({
                                        id: 'auth.access.no.1',
                                        defaultValue:
                                          '您的当前版本不支持此功能',
                                      })
                                    : ''
                                }
                              >
                                {item.icons}
                                <FormattedMessage id={item.buttonLang} />
                              </Button>
                            )}
                          </HOCAuth>
                        ))}
                    </Col>
                  </div>
                </Form>

                {/* <Button
                type="primary"
                ghost
                className={styles.ghostBtn}
                onClick={() => batchConfirmation()}
              >
                <FormattedMessage id="customerList.remove.grouping.title" />
              </Button> */}
              </div>
            )}
          </div>
        )}
        <div
          className={`${styles.agentManagementContent} ${AgentManagementContentClass}`}
        >
          <div className={styles.tableContent}>
            <DndContext
              modifiers={[restrictToVerticalAxis]}
              onDragEnd={onDragEnd}
            >
              <SortableContext
                // rowKey array
                items={rows.map(i => i.key)}
                strategy={verticalListSortingStrategy}
              >
                <Table
                  components={{
                    body: {
                      row: Row,
                    },
                  }}
                  loading={loadings[0]}
                  dataSource={rows}
                  columns={props.columns}
                  rowSelection={
                    props.rowSelection
                      ? { selectedRowKeys, onChange: onSelectChange }
                      : props.rowSelection
                  }
                  rowKey={row => row[props.key]}
                  pagination={{
                    total: total,
                    current: pageNum,
                    pageSize: pageSize,
                    showSizeChanger: true,
                    pageSizeOptions: [10, 20, 50, 100],
                    showTotal: total => (
                      <FormattedMessage
                        id="page.total.num"
                        defaultMessage={`共 ${total} 条`}
                        values={{ total }}
                      />
                    ),
                    onChange: (pageNum, pageSize) =>
                      pageChange(pageNum, pageSize),
                  }}
                />
              </SortableContext>
            </DndContext>
          </div>
        </div>
      </div>
    );
  },
);
export default TableComponents;
