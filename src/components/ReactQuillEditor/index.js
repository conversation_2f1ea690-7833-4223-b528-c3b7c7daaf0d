import React, { useState } from 'react';
import ReactQuill from 'react-quill';
import { notification } from '@/utils/utils';
import 'react-quill/dist/quill.snow.css';
import { Button } from 'antd';
import { connect } from 'umi';
import { getIntl } from '../../.umi/plugin-locale/localeExports';

/*
 * Simple editor component that takes placeholder text as a prop
 */
class Editor extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      editorHtml: '',
      theme: 'snow',
      showHtml: '',
    };
    this.handleChange = this.handleChange.bind(this);
  }

  handleChange(html) {
    this.setState({ editorHtml: html });
  }
  replay = () => {
    const { editorHtml } = this.state;
    this.setState({
      showHtml: editorHtml,
    });

    const { workRecordId, email } = this.props;

    const replyMessage = {
      workRecordId: workRecordId,
      // workRecordId:"8bc3cc95-4489-4c66-89da-b486b5969add",
      desAddress: email.emailForm,
      subject: email.emailSubject,
      content: editorHtml,
    };

    this.props.dispatch({
      type: 'workRecord/replyMessage',
      payload: replyMessage,
      callback: response => {
        console.log('replyMessage response' + response);
        if (response) {
          let { code, data, msg } = response;
          if (code === 200) {
            notification.success({
              message: getIntl().formatMessage({
                id: 'worktable.reply.success',
              }),
            });
          } else {
            notification.error({
              message: getIntl().formatMessage({
                id: 'worktable.reply.fail',
              }),
            });
          }
        }
      },
    });
  };

  render() {
    return (
      <div>
        <ReactQuill
          theme={this.state.theme}
          onChange={this.handleChange}
          value={this.state.editorHtml}
          modules={Editor.modules}
          formats={Editor.formats}
          bounds={'.app'}
          placeholder={this.props.placeholder}
          style={{ height: '200px', marginTop: '16px' }}
        />
        <div>
          <Button
            style={{ marginTop: '50px', float: 'right' }}
            type="primary"
            onClick={this.replay}
          >
            回复
          </Button>
        </div>
      </div>
    );
  }
}

/*
 * Quill modules to attach to editor
 * See https://quilljs.com/docs/modules/ for complete options
 */
Editor.modules = {
  toolbar: [
    [{ header: '1' }, { header: '2' }, { font: [] }],
    [{ size: [] }],
    ['bold', 'italic', 'underline', 'strike', 'blockquote'],
    [
      { list: 'ordered' },
      { list: 'bullet' },
      { indent: '-1' },
      { indent: '+1' },
    ],
    ['link', 'image', 'video'],
    ['clean'],
  ],
  clipboard: {
    // toggle to add extra line breaks when pasting HTML:
    matchVisual: false,
  },
};
/*
 * Quill editor formats
 * See https://quilljs.com/docs/formats/
 */
Editor.formats = [
  'header',
  'font',
  'size',
  'bold',
  'italic',
  'underline',
  'strike',
  'blockquote',
  'list',
  'bullet',
  'indent',
  'link',
  'image',
  'video',
];

const mapStateToProps = ({ worktable }) => {
  return {
    ...worktable,
  };
};

export default connect(mapStateToProps)(Editor);
