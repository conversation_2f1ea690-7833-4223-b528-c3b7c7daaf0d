.ChatBox {
  padding: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  width: 100%;
  height: 100%;

  .ChatBoxTop::-webkit-scrollbar {
    width: 0;
    /* 滚动条宽度 */
  }

  .ChatBoxTips {
    font-size: 12px;
    color: #999999;
    line-height: 22px;
  }

  .ChatBoxFooter {
    flex: 1;
    width: 100%;
    display: flex;
    flex-direction: column;

    .ChatBoxFooterHistory {
      display: flex;
      flex: 1;
      flex-direction: row;
      align-items: center;
      width: 100%;
      font-size: 12px;
      color: #999999;

      .ChatBoxFooterHistoryLine1 {
        width: 33%;
        height: 1px;
        // border: 1px solid linear-gradient(to right, #fff, #999);
        background: linear-gradient(to right, #ffffff, #999999) !important;
        margin-right: 12px;
      }

      .ChatBoxFooterHistoryfont {
        width: 45%;
        text-align: center;
      }

      .ChatBoxFooterHistoryLine2 {
        width: 33%;
        height: 1px;
        // border: 1px solid linear-gradient(to right, #999, #fff);
        background: linear-gradient(to right, #999999, #ffffff) !important;
        margin-left: 12px;
      }
    }

    .ChatBoxFooterLanguage {
      margin-bottom: 12px;
      flex: 1;
      text-align: center;

      :global {
        .ant-select {
          width: 70%;
          font-size: 12px;
        }

        .ant-select-selector {
          border-radius: 6px !important;
          max-height: 64px;
          overflow-y: scroll;
        }
      }
    }

    .ChatBoxFooterTextArea {
      flex: 3;
      position: relative;

      .ChatBoxFooterTextAreaIcon {
        position: absolute;
        right: 10px;
        bottom: 2px;
        cursor: pointer;
      }
    }
  }

  .CocoAssistant {
    margin-top: 12px;
    background-color: #f9f9f9;
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    padding: 10px;
    font-size: 12px;
    position: relative;

    .CocoAssistantHeader {
      display: flex;
      align-items: center;
      flex-direction: row;
    }

    .CocoAssistantBody {
      margin-top: 12px;
      /* 这两个在技术上是一样的, 为了兼容了浏览器两个都加上 */
      overflow-wrap: break-word;
      word-wrap: break-word;

      -ms-word-break: break-all;
      /* 这个的使用在web-kit中有些危险，他可能会阶段所有东西 */
      word-break: break-all;
      /* Instead use this non-standard one: */
      word-break: break-word;

      /* 如果浏览器支持的话增加一个连接符(Blink不支持) */
      -ms-hyphens: auto;
      -moz-hyphens: auto;
      -webkit-hyphens: auto;
      hyphens: auto;

      // p {
      //   margin-bottom: 0 !important;
      // }

      :global {
        .ant-skeleton-title {
          display: none !important;
        }

        .ant-skeleton-paragraph {
          margin-top: 12px !important;
        }

        .ant-collapse-header-text {
          font-size: 12px;
        }

        .ant-collapse-content-box {
          word-wrap: break-word;
          white-space: pre-wrap;
        }

        .ant-collapse-borderless {
          background-color: #fff;
        }
      }
    }

    .CocoAssistantFooter2 {
      display: flex;
      flex-direction: row;
      justify-content: end;

      .CocoAssistantFooterSpan {
        width: 0.5px;
        height: 16px;
        border: 0.5px solid #e6e6e6;
        margin: 0 5px;
      }
    }

    .CocoAssistantFooter {
      display: flex;
      flex-direction: row;
      justify-content: end;
      margin-top: 12px;

      .CocoAssistantFooterSpan {
        width: 0.5px;
        height: 16px;
        border: 0.5px solid #e6e6e6;
        margin: 0 5px;
      }
    }
  }

  .CocoQuestion {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding-top: 12px;
    align-items: baseline;

    .CocoQuestionHeader {
      font-size: 12px;
      font-weight: 500;
      display: flex;
      line-height: 22px;
      flex-grow: 1;

      .CocoQuestionHeaderSpan {
        /* 这两个在技术上是一样的, 为了兼容了浏览器两个都加上 */
        overflow-wrap: break-word;
        word-wrap: break-word;

        -ms-word-break: break-all;
        /* 这个的使用在web-kit中有些危险，他可能会阶段所有东西 */
        word-break: break-all;
        /* Instead use this non-standard one: */
        word-break: break-word;

        /* 如果浏览器支持的话增加一个连接符(Blink不支持) */
        -ms-hyphens: auto;
        -moz-hyphens: auto;
        -webkit-hyphens: auto;
        hyphens: auto;
      }

      .CocoQuestionHeaderTxt {
        display: flex;
        flex-direction: column;
        margin-left: 10px;
        flex-grow: 1;
      }

      .CocoQuestionHeaderTxtIcon {
        display: flex;
        flex-direction: row;
        justify-content: end;
        margin-top: 8px;
      }
    }
  }
}

.emptyBox {
  margin-top: 40%;

  :global {
    .ant-empty-description {
      font-size: 12px;
      color: #999;
    }
  }
}
