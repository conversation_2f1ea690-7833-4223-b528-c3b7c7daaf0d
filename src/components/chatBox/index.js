import React, { PureComponent } from 'react';
import { connect, FormattedMessage, getIntl, Link } from 'umi';
import styles from './index.less';
import {
  Input,
  Select,
  notification,
  message,
  Skeleton,
  Collapse,
  Image,
  Empty,
  Tag,
} from 'antd';
import { ReactComponent as AIGCHeader } from '../../assets/AIGCHeader.svg';
import { ReactComponent as BadNo } from '../../assets/badNo.svg';
import { ReactComponent as BadOff } from '../../assets/badOff.svg';
import { ReactComponent as GoodNo } from '../../assets/goodNo.svg';
import { ReactComponent as GoodOff } from '../../assets/goodOff.svg';
import { ReactComponent as Copy } from '../../assets/copy.svg';
import { ReactComponent as Send } from '../../assets/send.svg';
import { ReactComponent as AIGCAva } from '../../assets/AIGCAva.svg';
import { ReactComponent as AIGCEdit } from '../../assets/AIGCEdit.svg';
import { ReactComponent as AIGCY } from '../../assets/AIGCY.svg';
import { ReactComponent as AIGCN } from '../../assets/AIGCN.svg';
import { ReactComponent as RedBadNo } from '../../assets/redBadNo.svg';
import ViewIcon from '../../assets/knowledge-download.png';

import { CopyToClipboard } from 'react-copy-to-clipboard';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism'; // 代码高亮主题风格
const { TextArea } = Input;
const googleLanguage = JSON.parse(localStorage.getItem('languageLocal'));
const userArray = JSON.parse(sessionStorage.getItem('user'));
const { Option, OptGroup } = Select;
export class ChatBox extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      questionContent: '',
      mockData: [],
      longPayLoad: [],
      longPayLoadMax: 6, //随意聊对话记录最大长度
      language: 'en',
      languageLabel: 'English (United States)',
      kendraIndexId: [],
      searchData: [],
      newLanguageList: [
        {
          languageName: '中文 (Chinese)',
          languageIsoCode: 'zh',
        },
        {
          languageName: 'English',
          languageIsoCode: 'en',
        },
        {
          languageName: '日本語 (Japanese)',
          languageIsoCode: 'ja',
        },
        {
          languageName: '한국어 (Korean)',
          languageIsoCode: 'ko',
        },
        {
          languageName: '‫ (Arabic) العربية',
          languageIsoCode: 'ar',
        },
        {
          languageName: 'հայերեն (Armenian)',
          languageIsoCode: 'hy',
        },
        {
          languageName: 'euskara (Basque)',
          languageIsoCode: 'eu',
        },
        {
          languageName: 'বাংলা (Bengali)',
          languageIsoCode: 'bn',
        },
        {
          languageName: 'български (Bulgarian)',
          languageIsoCode: 'bg',
        },
        {
          languageName: 'català (Catalan)',
          languageIsoCode: 'ca',
        },
        {
          languageName: 'čeština (Czech)',
          languageIsoCode: 'cs',
        },
        {
          languageName: 'dansk (Danish)',
          languageIsoCode: 'da',
        },
        {
          languageName: 'Nederlands (Dutch)',
          languageIsoCode: 'nl',
        },
        {
          languageName: 'suomi (Finnish)',
          languageIsoCode: 'fi',
        },
        {
          languageName: 'français (French)',
          languageIsoCode: 'fr',
        },
        {
          languageName: 'galego (Galician)',
          languageIsoCode: 'gr',
        },
        {
          languageName: 'Deutsch (German)',
          languageIsoCode: 'de',
        },
        {
          languageName: 'Ελληνικά (Greek)',
          languageIsoCode: 'el',
        },
        {
          languageName: 'हिन्दी (Hindi)',
          languageIsoCode: 'hi',
        },
        {
          languageName: 'magyar (Hungarian)',
          languageIsoCode: 'hu',
        },
        {
          languageName: 'Indonesia (Indonesian)',
          languageIsoCode: 'id',
        },
        {
          languageName: 'Gaeilge (Irish)',
          languageIsoCode: 'ga',
        },
        {
          languageName: 'italiano (Italian)',
          languageIsoCode: 'it',
        },
        {
          languageName: 'latviešu (Latvian)',
          languageIsoCode: 'lv',
        },
        {
          languageName: 'lietuvių (Lithuanian)',
          languageIsoCode: 'lt',
        },
        {
          languageName: 'norsk (Norwegian)',
          languageIsoCode: 'no',
        },
        {
          languageName: ' ‎‫ (Persian) فارسی',
          languageIsoCode: 'fa',
        },
        {
          languageName: 'português (Portuguese)',
          languageIsoCode: 'pt',
        },
        {
          languageName: 'română (Romanian)',
          languageIsoCode: 'ro',
        },
        {
          languageName: '‎‫(Central Kurdish) کوردیی ناوەندی',
          languageIsoCode: 'ckb',
        },
        {
          languageName: 'русский (Russian)',
          languageIsoCode: 'ru',
        },
        {
          languageName: 'español (México)',
          languageIsoCode: 'es',
        },
        {
          languageName: 'svenska (Swedish)',
          languageIsoCode: 'sv',
        },
        {
          languageName: 'Türkçe (Turkish)',
          languageIsoCode: 'tr',
        },
      ],
      continueSend: true,
      tagValue: [],
      standardTagList: [],
    };
  }
  chatBoxRef = React.createRef();
  questionInput = React.createRef();
  componentDidMount() {
    this.setState({
      kendraIndexId:
        this.props.knowledgeBaseList && this.props.knowledgeBaseList.length > 0
          ? this.props.knowledgeBaseList[0].knowledgeId
          : [],
    });
  }
  /**
   * 问题编辑
   */
  questionEdit = i => {
    this.setState(prevState => {
      const updatedMockData = [...prevState.mockData];
      updatedMockData[i].editStatus = true;
      return { mockData: updatedMockData };
    });
  };
  /**
   * 取消问题编辑
   */
  questionCancel = i => {
    this.setState(prevState => {
      const updatedMockData = [...prevState.mockData];
      updatedMockData[i].editStatus = false;
      updatedMockData[i].questionHistory = updatedMockData[i].question;
      return { mockData: updatedMockData };
    });
  };
  /**
   * 确认问题编辑内容
   */
  questionConfirm = i => {
    //若为知识库需要先选择文档知识库下拉
    if (!this.state.mockData[i].questionHistory.trim()) {
      return false;
    }
    if (this.props.showDocument) {
      if (!this.state.kendraIndexId) {
        message.warning(
          getIntl().formatMessage({
            id: 'knowledge.QA.tabs.2.form.label1.placeholder',
          }),
        );
        return false;
      }
    }
    //更新是否编辑状态
    this.setState(prevState => {
      const updatedMockData = [...prevState.mockData];
      console.log(prevState.mockData);
      updatedMockData[i].editStatus = false;
      updatedMockData[i].text = ['loading'];
      return { mockData: updatedMockData };
    });

    // ai陪聊需要记录聊天内容
    if (
      this.props.promptName === '随心聊' ||
      this.props.promptName === 'Free Chat'
    ) {
      const data = {
        contentId: this.state.mockData[i].contentId,
        promptId: this.props.promptId,
        contentQuestion: '',
      };
      //记录聊天内容
      this.setState(
        prevState => {
          const updatedLongPayLoad = [...prevState.longPayLoad];
          if (updatedLongPayLoad.length >= this.state.longPayLoadMax) {
            updatedLongPayLoad.shift();
            updatedLongPayLoad.shift();
            updatedLongPayLoad.push(
              'Q:' + this.state.mockData[i].questionHistory + '\n\n',
            );
          } else {
            updatedLongPayLoad.push(
              'Q:' + this.state.mockData[i].questionHistory + '\n\n',
            );
          }
          return { longPayLoad: updatedLongPayLoad };
        },
        () => {
          // 在回调函数中读取更新后的 longPayLoad 值
          data.contentQuestion = this.state.longPayLoad.join(',');
        },
      );
      //并且接口返回前不可再次提问
      // this.setState({
      //   continueSend: false,
      // });
      this.props.dispatch({
        type: 'aigc/aiUpdateContent',
        payload: data,
        callback: response => {
          if (response.code == 200) {
            this.setState(prevState => {
              const updatedMockData = [...prevState.mockData];
              //备份问题
              const rData = response.data;
              rData.question = updatedMockData[i].questionHistory;
              //合并返回之后的数据
              const newObject = {
                editStatus: false,
                isGood: false,
                isBad: false,
                questionHistory: updatedMockData[i].questionHistory,
                ...rData,
              };
              updatedMockData.splice(i, 1, newObject);
              return { mockData: updatedMockData };
            });
            //记录回答的问题
            this.setState(prevState => {
              const updatedLongPayLoad = [...prevState.longPayLoad];

              if (updatedLongPayLoad.length >= this.state.longPayLoadMax) {
                updatedLongPayLoad.shift();
                updatedLongPayLoad.push('A:' + response.data.text + '\n\n');
              } else {
                updatedLongPayLoad.push('A:' + response.data.text + '\n\n');
              }
              return { longPayLoad: updatedLongPayLoad };
            });
          } else if ([60000, 60001, 60002].includes(response.code)) {
            this.setState(prevState => {
              const updatedMockData = [...prevState.mockData];
              //合并返回之后的数据
              const newObject = {
                editStatus: false,
                isGood: false,
                isBad: false,
                questionHistory: updatedMockData[i].questionHistory,
                text: response.msg,
              };
              updatedMockData.splice(i, 1, newObject);
              return { mockData: updatedMockData };
            });
          } else {
            this.setState(prevState => {
              const updatedMockData = [...prevState.mockData];
              //合并返回之后的数据
              const newObject = {
                editStatus: false,
                isGood: false,
                isBad: false,
                questionHistory: updatedMockData[i].questionHistory,
                text: getIntl().formatMessage({
                  id: 'AIGC.chatBox.error',
                }),
              };
              updatedMockData.splice(i, 1, newObject);
              return { mockData: updatedMockData };
            });
          }
          // //恢复可提问
          // this.setState({
          //   continueSend: true,
          // });
        },
      });
    } else if (this.props.showDocument) {
      //并且接口返回前不可再次提问
      // this.setState({
      //   continueSend: false,
      // });
      this.props.dispatch({
        type: 'ccpKnowledge/queryOverallAnswerKnowledge',
        payload: {
          questions: this.state.mockData[i].questionHistory,
          kendraIndexId: this.state.kendraIndexId,
          // kendraIndexId: '1234',
          //知识库的下拉和其他tabs不一样，需要转一下
          // language: this.state.language,
          tagIdList: this.state.tagValue,
          isCheckAI: 1,
        },
        callback: response => {
          if (response) {
            let { code, data, msg } = response;
            if (code === 200) {
              if (data.knowledgeTypeCode == 1) {
                let newData = data.answerPreviewList?.map(item => {
                  let answerList = { type: item.atype, content: item.answer };
                  return {
                    data: [answerList],
                    form:
                      getIntl().formatMessage({
                        id: 'worktable.work.knowledge.form',
                      }) +
                      ' ' +
                      getIntl().formatMessage({
                        id:
                          data.knowledgeTypeName === '文档知识库'
                            ? 'homePage.data.key.10.children.105.no111111'
                            : 'homePage.data.key.10.children.105.no',
                      }),
                  };
                });
                console.log(newData);

                this.setState(prevState => {
                  const updatedMockData = [...prevState.mockData];
                  const newObject = {
                    editStatus: false,
                    isGood: false,
                    isBad: false,
                    questionHistory: updatedMockData[i].questionHistory,
                    text: newData,
                    question: updatedMockData[i].questionHistory,
                    msgId: data.msgId,
                  };
                  updatedMockData.splice(i, 1, newObject);
                  return { mockData: updatedMockData };
                });
                // this.setState({
                //   searchData: newData,
                // });
              } else if (data.knowledgeTypeCode == 2) {
                let newData = data.answerPreviewList?.map(item => {
                  let sources = item.answerKnowledgeSourceResultList?.map(
                    it => {
                      return {
                        sourceUrl: it.filePath,
                        sourceName: it.fileName,
                      };
                    },
                  );
                  return {
                    data: [
                      { type: '1', content: item.answer, sources: sources },
                    ],
                    form:
                      getIntl().formatMessage({
                        id: 'worktable.work.knowledge.form',
                      }) +
                      ' ' +
                      getIntl().formatMessage({
                        id:
                          data.knowledgeTypeName === '文档知识库'
                            ? 'homePage.data.key.10.children.105.no111111'
                            : 'homePage.data.key.10.children.105.no',
                      }),
                  };
                });
                console.log(newData);
                this.setState(prevState => {
                  const updatedMockData = [...prevState.mockData];
                  const newObject = {
                    editStatus: false,
                    isGood: false,
                    isBad: false,
                    questionHistory: updatedMockData[i].questionHistory,
                    question: updatedMockData[i].questionHistory,
                    text: newData,
                    msgId: data.msgId,
                  };
                  updatedMockData.splice(i, 1, newObject);
                  return { mockData: updatedMockData };
                });
              }
            } else if ([60000, 60001, 60002, 201].includes(response.code)) {
              this.setState(prevState => {
                const updatedMockData = [...prevState.mockData];
                //合并返回之后的数据
                const newObject = {
                  editStatus: false,
                  isGood: false,
                  isBad: false,
                  questionHistory: updatedMockData[i]?.questionHistory,
                  question: updatedMockData[i].question,
                  msgId: data,
                  text: [
                    {
                      data: [
                        {
                          content: msg,
                        },
                      ],
                    },
                  ],
                };
                updatedMockData.splice(i, 1, newObject);
                return { mockData: updatedMockData };
              });
            } else {
              this.setState(prevState => {
                const updatedMockData = [...prevState.mockData];
                //合并返回之后的数据
                const newObject = {
                  editStatus: false,
                  isGood: false,
                  isBad: false,
                  questionHistory: updatedMockData[i]?.questionHistory,
                  question: updatedMockData[i].question,
                  text: [
                    {
                      data: [
                        {
                          content: getIntl().formatMessage({
                            id: 'AIGC.chatBox.error',
                          }),
                        },
                      ],
                    },
                  ],
                };
                updatedMockData.splice(i, 1, newObject);
                return { mockData: updatedMockData };
              });
            }
          }
          // //恢复可提问
          // this.setState({
          //   continueSend: true,
          // });
        },
      });
    } else {
      const data = {
        contentId: this.state.mockData[i].contentId,
        promptId: this.props.promptId,
        contentQuestion: this.state.mockData[i].questionHistory,
      };
      // //需要语言字段的tabs
      if (['30000', '40000', '70000', '90000'].includes(this.props.promptId)) {
        data.language = this.state.languageLabel;
      }
      //并且接口返回前不可再次提问
      // this.setState({
      //   continueSend: false,
      // });
      this.props.dispatch({
        type: 'aigc/aiUpdateContent',
        payload: data,
        callback: response => {
          if (response.code == 200) {
            this.setState(prevState => {
              const updatedMockData = [...prevState.mockData];
              const newObject = {
                ...response.data,
                editStatus: false,
                isGood: false,
                isBad: false,
                questionHistory: response.data?.question,
              };
              updatedMockData.splice(i, 1, newObject);
              return { mockData: updatedMockData };
            });
          } else if ([60000, 60001, 60002].includes(response.code)) {
            this.setState(prevState => {
              const updatedMockData = [...prevState.mockData];
              //合并返回之后的数据
              const newObject = {
                editStatus: false,
                isGood: false,
                isBad: false,
                questionHistory: updatedMockData[i].questionHistory,
                text: response.msg,
              };
              updatedMockData.splice(i, 1, newObject);
              return { mockData: updatedMockData };
            });
          } else {
            this.setState(prevState => {
              const updatedMockData = [...prevState.mockData];
              //合并返回之后的数据
              const newObject = {
                editStatus: false,
                isGood: false,
                isBad: false,
                questionHistory: response.data?.question,
                text: getIntl().formatMessage({
                  id: 'AIGC.chatBox.error',
                }),
              };
              updatedMockData.splice(i, 1, newObject);
              return { mockData: updatedMockData };
            });
          }
          //恢复可提问
          // this.setState({
          //   continueSend: true,
          // });
        },
      });
    }
  };

  /**
   * 差评
   */
  handleBad = (i, index) => {
    if (
      this.state.mockData[i].isGood == true ||
      this.state.mockData[i].isBad == true
    ) {
      message.warning(
        getIntl().formatMessage({
          id: 'AIGC.chatBox.message',
        }),
      );
      return;
    }
    //非知识库点踩点赞
    if (index === 'noDocument') {
      const data = {
        contentId: this.state.mockData[i].contentId,
        data: {
          mood: 2,
        },
      };
      this.props.dispatch({
        type: 'aigc/updateMood',
        payload: data,
        callback: response => {
          if (response.code == 200) {
            this.setState(prevState => {
              const updatedMockData = [...prevState.mockData];
              updatedMockData[i].isBad = !updatedMockData[i].isBad;
              return { mockData: updatedMockData };
            });
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    } else {
      //知识库点赞点踩
      const data = {
        msgId: this.state.mockData[i].msgId,
        supportStatus: 2,
        companyId: this.props.user.companyId
          ? this.props.user.companyId
          : userArray.companyId,
      };
      this.props.dispatch({
        type: 'ccpKnowledge/likeDislikeOperate',
        payload: data,
        callback: response => {
          if (response.code == 200) {
            this.setState(prevState => {
              const updatedMockData = [...prevState.mockData];
              updatedMockData[i].isBad = !updatedMockData[i].isBad;
              return { mockData: updatedMockData };
            });
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    }
  };

  /**
   * 好评
   */
  handleGood = (i, index) => {
    console.log(i, this.state.mockData[i]);
    if (
      this.state.mockData[i].isGood == true ||
      this.state.mockData[i].isBad == true
    ) {
      message.warning(
        getIntl().formatMessage({
          id: 'AIGC.chatBox.message',
        }),
      );
      return;
    }
    //非知识库点踩点赞
    if (index === 'noDocument') {
      const data = {
        contentId: this.state.mockData[i].contentId,
        data: {
          mood: 1,
        },
      };
      this.props.dispatch({
        type: 'aigc/updateMood',
        payload: data,
        callback: response => {
          if (response.code == 200) {
            this.setState(prevState => {
              const updatedMockData = [...prevState.mockData];
              updatedMockData[i].isGood = !updatedMockData[i].isGood;
              return { mockData: updatedMockData };
            });
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    } else {
      //知识库点赞点踩
      const data = {
        msgId: this.state.mockData[i].msgId,
        supportStatus: 1,
        companyId: this.props.user.companyId
          ? this.props.user.companyId
          : userArray.companyId,
      };
      this.props.dispatch({
        type: 'ccpKnowledge/likeDislikeOperate',
        payload: data,
        callback: response => {
          if (response.code == 200) {
            this.setState(
              prevState => {
                const updatedMockData = [...prevState.mockData];
                updatedMockData[i].isGood = !updatedMockData[i].isGood;
                return { mockData: updatedMockData };
              },
              () => {
                console.log(this.state.mockData[i]);
              },
            );
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    }
  };

  /**
   * 复制
   */
  handleCopy = i => {
    const tempInput = document.createElement('input');
    tempInput.setAttribute('value', this.state.mockData[i].text);
    document.body.appendChild(tempInput);
    tempInput.select();
    document.execCommand('copy');
    document.body.removeChild(tempInput);
  };
  /**
   * 发送问题
   */
  sendQuestion = () => {
    //判空格
    if (!this.state.questionContent.trim() || !this.state.continueSend) {
      return false;
    }
    //若为知识库需要先选择文档知识库下拉
    if (this.props.showDocument) {
      if (!this.state.kendraIndexId) {
        message.warning(
          getIntl().formatMessage({
            id: 'knowledge.QA.tabs.2.form.label1.placeholder',
          }),
        );
        return false;
      }
    }
    //提前赋值，问题展示在聊天窗口
    this.setState(prevState => {
      const updatedMockData = [...prevState.mockData];
      const newObject = {
        editStatus: false,
        isGood: false,
        isBad: false,
        questionHistory: this.state.questionContent,
        text: ['loading'],
      };
      updatedMockData.push(newObject);
      return { mockData: updatedMockData };
    });
    // 随心聊需要记录聊天内容
    if (
      this.props.promptName === '随心聊' ||
      this.props.promptName === 'Free Chat'
    ) {
      const data = {
        promptId: this.props.promptId,
        contentQuestion: '',
      };

      //记录聊天内容
      this.setState(
        prevState => {
          const updatedLongPayLoad = [...prevState.longPayLoad];
          if (updatedLongPayLoad.length >= this.state.longPayLoadMax) {
            updatedLongPayLoad.shift();
            updatedLongPayLoad.shift();
            updatedLongPayLoad.push('Q:' + this.state.questionContent + '\n\n');
          } else {
            updatedLongPayLoad.push('Q:' + this.state.questionContent + '\n\n');
          }
          return { longPayLoad: updatedLongPayLoad };
        },
        () => {
          // 在回调函数中读取更新后的 longPayLoad 值
          data.contentQuestion = this.state.longPayLoad.join(',');
        },
      );
      //清空输入框内容,并且接口返回前不可再次提问
      this.setState({
        questionContent: '',
        continueSend: false,
      });

      // 调取接口
      this.props.dispatch({
        type: 'aigc/aiGetAnswer',
        payload: data,
        callback: response => {
          if (response.code == 200) {
            this.setState(prevState => {
              const updatedMockData = [...prevState.mockData];
              //备份问题
              const rData = response.data;
              rData.question =
                updatedMockData[updatedMockData.length - 1].questionHistory;
              //合并返回之后的数据
              const newObject = {
                ...updatedMockData[updatedMockData.length - 1],
                ...rData,
              };
              updatedMockData[updatedMockData.length - 1] = newObject;
              return { mockData: updatedMockData };
            });
            //记录回答的问题
            this.setState(prevState => {
              const updatedLongPayLoad = [...prevState.longPayLoad];

              if (updatedLongPayLoad.length >= this.state.longPayLoadMax) {
                updatedLongPayLoad.shift();
                updatedLongPayLoad.push('A:' + response.data.text + '\n\n');
              } else {
                updatedLongPayLoad.push('A:' + response.data.text + '\n\n');
              }
              return { longPayLoad: updatedLongPayLoad };
            });
          } else if ([60000, 60001, 60002].includes(response.code)) {
            this.setState(prevState => {
              const updatedMockData = [...prevState.mockData];
              //合并返回之后的数据
              const newObject = {
                ...updatedMockData[updatedMockData.length - 1],
                text: response.msg,
              };
              updatedMockData[updatedMockData.length - 1] = newObject;
              return { mockData: updatedMockData };
            });
          } else {
            this.setState(prevState => {
              const updatedMockData = [...prevState.mockData];
              //合并返回之后的数据
              const newObject = {
                ...updatedMockData[updatedMockData.length - 1],
                text: getIntl().formatMessage({
                  id: 'AIGC.chatBox.error',
                }),
              };
              updatedMockData[updatedMockData.length - 1] = newObject;
              return { mockData: updatedMockData };
            });
          }
          //恢复可提问
          this.setState({
            continueSend: true,
          });
        },
      });
    } else if (this.props.showDocument) {
      //清空输入框的值,并且接口返回前不可再次提问
      this.setState({
        questionContent: '',
        continueSend: false,
      });
      this.props.dispatch({
        type: 'ccpKnowledge/queryOverallAnswerKnowledge',
        payload: {
          questions: this.state.questionContent,
          kendraIndexId: this.state.kendraIndexId,
          // kendraIndexId: '1234',
          //知识库的下拉和其他tabs不一样，需要转一下
          // language: this.state.language,
          tagIdList: this.state.tagValue,
          isCheckAI: 1,
        },
        callback: response => {
          if (response) {
            let { code, data, msg } = response;
            if (code === 200) {
              if (data.knowledgeTypeCode == 1) {
                let newData = [{ data: [], form: '' }];
                data.answerPreviewList?.forEach(item => {
                  let answerList = { type: item.atype, content: item.answer };
                  // item.answers?.map(i => {
                  // return ;
                  // });
                  newData[0].form =
                    getIntl().formatMessage({
                      id: 'worktable.work.knowledge.form',
                    }) +
                    ' ' +
                    getIntl().formatMessage({
                      id:
                        data.knowledgeTypeName === '文档知识库'
                          ? 'homePage.data.key.10.children.105.no111111'
                          : 'homePage.data.key.10.children.105.no',
                    });
                  newData[0].data.push(answerList);
                });
                console.log(newData);
                this.setState(prevState => {
                  const updatedMockData = [...prevState.mockData];
                  //合并返回之后的数据
                  const newObject = {
                    ...updatedMockData[updatedMockData.length - 1],
                    text: newData,
                    question: this.state.searchData,
                    msgId: data.msgId,
                  };
                  updatedMockData[updatedMockData.length - 1] = newObject;
                  console.log(updatedMockData);
                  return { mockData: updatedMockData };
                });
                // this.setState({
                //   searchData: newData,
                // });
              } else if (data.knowledgeTypeCode == 2) {
                let newData = data.answerPreviewList?.map(item => {
                  let sources = item.fileList?.map(it => {
                    return {
                      sourceUrl: it.filePath,
                      sourceName: it.fileName,
                      type: it.type,
                    };
                  });
                  return {
                    data: [
                      { type: '1', content: item.answer, sources: sources },
                    ],
                    form:
                      getIntl().formatMessage({
                        id: 'worktable.work.knowledge.form',
                      }) +
                      ' ' +
                      getIntl().formatMessage({
                        id:
                          data.knowledgeTypeName === '文档知识库'
                            ? 'homePage.data.key.10.children.105.no111111'
                            : 'homePage.data.key.10.children.105.no',
                      }),
                  };
                });
                console.log(newData);
                this.setState(prevState => {
                  const updatedMockData = [...prevState.mockData];
                  //合并返回之后的数据
                  const newObject = {
                    ...updatedMockData[updatedMockData.length - 1],
                    text: newData,
                    question: this.state.searchData,
                    msgId: data.msgId,
                  };
                  updatedMockData[updatedMockData.length - 1] = newObject;
                  console.log(updatedMockData);
                  return { mockData: updatedMockData };
                });
              }
            } else if ([60000, 60001, 60002, 201].includes(response.code)) {
              this.setState(prevState => {
                const updatedMockData = [...prevState.mockData];
                //合并返回之后的数据
                const newObject = {
                  ...updatedMockData[updatedMockData.length - 1],
                  question: this.state.searchData,
                  msgId: data,
                  text: [
                    {
                      data: [
                        {
                          content: msg,
                        },
                      ],
                    },
                  ],
                };
                updatedMockData[updatedMockData.length - 1] = newObject;
                return { mockData: updatedMockData };
              });
            } else {
              this.setState(prevState => {
                const updatedMockData = [...prevState.mockData];
                //合并返回之后的数据
                const newObject = {
                  ...updatedMockData[updatedMockData.length - 1],
                  question: this.state.searchData,
                  text: [
                    {
                      data: [
                        {
                          content: getIntl().formatMessage({
                            id: 'AIGC.chatBox.error',
                          }),
                        },
                      ],
                    },
                  ],
                };
                updatedMockData[updatedMockData.length - 1] = newObject;
                return { mockData: updatedMockData };
              });
            }
          }
          //恢复可提问
          this.setState({
            continueSend: true,
          });
        },
      });
    } else {
      const data = {
        promptId: this.props.promptId,
        contentQuestion: this.state.questionContent,
      };
      // //需要语言字段的tabs
      if (['30000', '40000', '70000', '90000'].includes(this.props.promptId)) {
        data.language = this.state.languageLabel;
      }
      //清空输入框的值,并且接口返回前不可再次提问
      this.setState({
        questionContent: '',
        continueSend: false,
      });

      this.props.dispatch({
        type: 'aigc/aiGetAnswer',
        payload: data,
        callback: response => {
          if (response.code == 200) {
            this.setState(prevState => {
              const updatedMockData = [...prevState.mockData];
              //合并返回之后的数据
              const newObject = {
                ...updatedMockData[updatedMockData.length - 1],
                ...response.data,
              };
              updatedMockData[updatedMockData.length - 1] = newObject;
              return { mockData: updatedMockData };
            });
          } else if ([60000, 60001, 60002].includes(response.code)) {
            this.setState(prevState => {
              const updatedMockData = [...prevState.mockData];
              //合并返回之后的数据
              const newObject = {
                ...updatedMockData[updatedMockData.length - 1],
                text: response.msg,
              };
              updatedMockData[updatedMockData.length - 1] = newObject;
              return { mockData: updatedMockData };
            });
          } else {
            this.setState(prevState => {
              const updatedMockData = [...prevState.mockData];
              //合并返回之后的数据
              const newObject = {
                ...updatedMockData[updatedMockData.length - 1],
                text: getIntl().formatMessage({
                  id: 'AIGC.chatBox.error',
                }),
              };
              updatedMockData[updatedMockData.length - 1] = newObject;
              return { mockData: updatedMockData };
            });
          }
          //恢复可提问
          this.setState({
            continueSend: true,
          });
        },
      });
    }
    //滚动条

    // 获取包含滚动内容的元素
    if (this.chatBoxRef.current) {
      const { scrollHeight, clientHeight, scrollTop } = this.chatBoxRef.current;
      setTimeout(() => {
        this.chatBoxRef.current.scrollTop = scrollHeight;
      }, 0);

      // 将滚动条置底
      console.log(scrollHeight, scrollTop);
    }
  };
  onChange = e => {
    this.setState({
      questionContent: e.target.value,
      searchData: e.target.value,
    });
  };
  /**
   * 知识库改变
   */
  kendraIndexIdChange = e => {
    console.log(e);
    this.setState({
      kendraIndexId: e,
    });
    this.questionInput.current.focus();
  };
  /**
   * 语言选择
   */
  handleSelect = e => {
    this.setState({
      language: e,
    });
    this.questionInput.current.focus();
  };
  handleSelectLabel = e => {
    this.setState({
      languageLabel: e,
    });
    this.questionInput.current.focus();
  };
  /**
   * 修改已经问出去的问题
   */
  onChangeQusetion = (e, index) => {
    this.setState(prevState => {
      const updatedMockData = [...prevState.mockData];
      updatedMockData[index].questionHistory = e.target.value;
      return { mockData: updatedMockData };
    });
  };
  /**
   * 回车事件
   */
  handleKeyDown = event => {
    console.log(event);
    if (event.keyCode === 13 && !event.shiftKey) {
      // 检测到回车键且没有按下 Shift 键
      event.preventDefault(); // 阻止默认的换行行为
      this.sendQuestion();
    }
  };
  tagRender = props => {
    const { label, value, closable, onClose } = props;
    let resultObj = this.findTagById(value);
    const onPreventMouseDown = event => {
      event.preventDefault();
      event.stopPropagation();
    };
    if (resultObj) {
      return (
        <Tag
          className={resultObj.tagColorCode || 'colorType1'}
          onMouseDown={onPreventMouseDown}
          closable={closable}
          onClose={onClose}
          style={{
            marginRight: 3,
            fontSize: 12,
            width: 'max-content',
          }}
        >
          <span className="tagText">{resultObj.categoryContent} / </span>
          {resultObj.tagContent}{' '}
        </Tag>
      );
    }
  };
  findTagById = tagId => {
    for (let category of this.props.standardTagList) {
      for (let tag of category.tagList) {
        if (tag.tagId == tagId) {
          return tag;
        }
      }
    }
    return null; // 如果没有找到匹配的标签，返回 null
  };

  // 切换知识标签
  handleTagChange = value => {
    this.setState({
      tagValue: value,
    });
    this.questionInput.current.focus();
  };
  render() {
    const {
      showLanguage,
      languageListA,
      languageList,
      showDocument,
      knowledgeBaseList,
      standardTagList,
      disabled,
    } = this.props;
    const { editStatus, mockData, questionContent } = this.state;
    {
      return disabled ? (
        <div className={styles.emptyBox}>
          <Empty
            description={getIntl().formatMessage({
              id: 'auth.access.no.1',
            })}
          ></Empty>
        </div>
      ) : (
        <div className={styles.ChatBox}>
          <div
            ref={this.chatBoxRef}
            className={styles.ChatBoxTop}
            style={{ flex: 3, overflowY: 'scroll' }}
          >
            <p className={styles.ChatBoxTips}>
              <FormattedMessage id="AIGC.chatBox.tips" defaultMessage="" />
            </p>
            <CocoAssistant
              showIcon={false}
              showDocument={this.props.showDocument}
            />
            {/* 非知识库模块展示 */}
            {!showDocument &&
              mockData?.map((item, index) => {
                return (
                  <div key={index}>
                    <CocoQuestion
                      questionEdit={() => this.questionEdit(index)}
                      editStatus={item.editStatus}
                      questionConfirm={() => this.questionConfirm(index)}
                      questionContent={item.questionHistory}
                      onChangeQusetion={e => this.onChangeQusetion(e, index)}
                      questionCancel={() => this.questionCancel(index)}
                    />
                    <CocoAssistant
                      showIcon={true}
                      askContent={item.text}
                      handleGood={() => this.handleGood(index, 'noDocument')}
                      handleBad={() => this.handleBad(index, 'noDocument')}
                      handleCopy={() => this.handleCopy(index)}
                      isGood={item.isGood}
                      isBad={item.isBad}
                      //以下是用于知识库参数
                      showDocument={this.props.showDocument}
                    />
                  </div>
                );
              })}
            {/* 知识库模块展示 */}
            {showDocument && mockData && mockData.length > 0
              ? mockData?.map((item, index) => {
                  return (
                    <div key={index}>
                      <CocoQuestion
                        questionEdit={() => this.questionEdit(index)}
                        editStatus={item.editStatus}
                        questionConfirm={() => this.questionConfirm(index)}
                        questionContent={item.questionHistory}
                        onChangeQusetion={e => this.onChangeQusetion(e, index)}
                        questionCancel={() => this.questionCancel(index)}
                      />
                      {item.text &&
                      item.text.length > 0 &&
                      item.text[0].data &&
                      item.text[0].data.length > 0 ? (
                        item.text[0].data?.map((itemC, indexC) => {
                          return (
                            <CocoAssistant
                              key={indexC}
                              showIcon={true}
                              askContent={itemC}
                              handleGood={() =>
                                this.handleGood(index, 'document')
                              }
                              handleBad={() =>
                                this.handleBad(index, 'document')
                              }
                              handleCopy={() => this.handleCopy(indexC)}
                              isGood={item.isGood}
                              isBad={item.isBad}
                              //以下是用于知识库参数
                              showDocument={this.props.showDocument}
                              form={item.text[0]?.form}
                            />
                          );
                        })
                      ) : (
                        <CocoAssistant
                          showIcon={false}
                          showDocument={this.props.showDocument}
                          askContent={'loading'}
                        />
                      )}
                    </div>
                  );
                })
              : ''}
          </div>
          {/* 底部 */}
          <div className={styles.ChatBoxFooter}>
            <div className={styles.ChatBoxFooterHistory}>
              <span className={styles.ChatBoxFooterHistoryLine1}></span>
              <span className={styles.ChatBoxFooterHistoryfont}>
                <FormattedMessage
                  id="AIGC.chatBox.history"
                  defaultMessage="以上是历史信息"
                />
              </span>
              <span className={styles.ChatBoxFooterHistoryLine2}></span>
            </div>
            {/* 有知识库的tabs */}
            {showDocument && (
              <div>
                <div className={styles.ChatBoxFooterLanguage}>
                  <span
                    style={{
                      fontSize: 12,
                      color: '#666666',
                      display: 'inline-block',
                      width: '28%',
                    }}
                  >
                    <FormattedMessage
                      id="knowledge.QA.tabs.2.form.label1"
                      defaultMessage="文档"
                    />
                    ：
                  </span>
                  <Select
                    placeholder={getIntl().formatMessage({
                      id: 'knowledge.QA.tabs.2.form.label1.placeholder',
                    })}
                    options={knowledgeBaseList
                      ?.map(item => {
                        if (item.status == 2) {
                          return {
                            label: item.knowledgeName,
                            value: item.knowledgeId,
                            key: item.knowledgeId,
                          };
                        }
                      })
                      ?.filter(item => item !== undefined)}
                    value={this.state.kendraIndexId}
                    // defaultValue={
                    //   knowledgeBaseList && knowledgeBaseList.length > 0
                    //     ? knowledgeBaseList[0].kendraId
                    //     : ''
                    // }
                    onSelect={this.kendraIndexIdChange}
                  />
                </div>
                <div className={styles.ChatBoxFooterLanguage}>
                  <span
                    style={{
                      fontSize: 12,
                      color: '#666666',
                      display: 'inline-block',
                      width: '28%',
                    }}
                  >
                    <FormattedMessage
                      id="knowledge.QA.select.knowledge.tag"
                      defaultMessage="知识标签："
                    />
                  </span>
                  <Select
                    tagRender={this.tagRender}
                    optionLabelProp="label"
                    optionFilterProp="children"
                    showArrow={false}
                    showSearch
                    mode="multiple"
                    filterOption={(inputValue, option) =>
                      option.label
                        .toLowerCase()
                        .indexOf(inputValue.toLowerCase()) >= 0
                    }
                    allowClear
                    placeholder={getIntl().formatMessage({
                      id: 'knowledge.QA.select.knowledge.tag.tips',
                      defaultMessage: '请选择知识标签',
                    })}
                    onChange={value => this.handleTagChange(value)}
                  >
                    {standardTagList?.map(group => (
                      <OptGroup
                        key={group.categoryId}
                        label={
                          group.categoryContent !== 'private_tag_category_code'
                            ? group.categoryContent
                            : getIntl().formatMessage({
                                id: 'tag.management.tab.private',
                                defaultValue: '私有标签',
                              })
                        }
                      >
                        {group.tagList.map(option => (
                          <Option
                            key={option.tagId}
                            value={option.tagId}
                            label={option.tagContent}
                          >
                            <div
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                              }}
                            >
                              <div
                                style={{
                                  width: '12px',
                                  height: '12px',
                                  backgroundColor: option.tagColor,
                                  marginRight: '4px',
                                }}
                              ></div>
                              <span>{option.tagContent}</span>
                            </div>
                          </Option>
                        ))}
                      </OptGroup>
                    ))}
                  </Select>
                </div>
                {/* <div className={styles.ChatBoxFooterLanguage}>
              <span
                style={{
                  fontSize: 12,
                  color: '#666666',
                  display: 'inline-block',
                  width: '28%',
                }}
              >
                <FormattedMessage
                  id="AIGC.chatBox.language"
                  defaultMessage="目标语言"
                />
                ：
              </span>
              <Select
                // defaultValue="中文"
                value={this.state.language}
                options={this.state.newLanguageList.map(item => ({
                  label: item.languageName,
                  value: item.languageIsoCode,
                  key: item.languageIsoCode,
                }))}
                onSelect={this.handleSelect}
                showSearch
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) >= 0
                }
              />
            </div> */}
              </div>
            )}
            {/* 无知识库的tabs */}
            {!showDocument && showLanguage && (
              <div className={styles.ChatBoxFooterLanguage}>
                <span
                  style={{
                    fontSize: 12,
                    color: '#666666',
                    display: 'inline-block',
                    width: '28%',
                  }}
                >
                  <FormattedMessage
                    id="AIGC.chatBox.language"
                    defaultMessage="目标语言"
                  />
                  ：
                </span>
                <Select
                  value={this.state.languageLabel}
                  // options={languageListA}
                  options={googleLanguage.map(item => ({
                    label: item.label,
                    value: item.label,
                    key: item.value,
                  }))}
                  onSelect={this.handleSelectLabel}
                  showSearch
                  filterOption={(inputValue, option) =>
                    option.label
                      .toLowerCase()
                      .indexOf(inputValue.toLowerCase()) >= 0
                  }
                />
              </div>
            )}
            <div className={styles.ChatBoxFooterTextArea}>
              <TextArea
                style={{
                  height: '100%',
                  resize: 'none',
                  backgroundColor: '#f9f9f9',
                  borderRadius: 6,
                  border: 'none',
                  fontSize: 12,
                }}
                ref={this.questionInput}
                onPressEnter={e => this.handleKeyDown(e)}
                value={questionContent}
                onChange={e => this.onChange(e)}
                placeholder={getIntl().formatMessage({
                  id: 'AIGC.chatBox.body',
                })}
              />
              <div
                className={styles.ChatBoxFooterTextAreaIcon}
                onClick={this.sendQuestion}
                style={{
                  opacity: questionContent ? 1 : 0.5,
                  pointerEvents: questionContent ? 'all' : 'none',
                }}
              >
                <Send />
              </div>
            </div>
          </div>
        </div>
      );
    }
  }
}

//客服组件
const CocoAssistant = props => {
  const ImgRenderer = props => {
    console.log(props);
    let src = props.src;
    return <Image src={src} />;
  };
  // 跳转页面预览文档
  const handleView = source => {
    localStorage.setItem('documentPreviewData', JSON.stringify(source));
  };
  // 下载文档
  const handleDownload = async (url, fileName) => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      const blobUrl = window.URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = blobUrl;
      a.download = fileName || 'downloaded-file.pdf'; // 自定义文件名
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);

      window.URL.revokeObjectURL(blobUrl);
    } catch (error) {
      notification.success({
        message: error,
      });
    }
  };
  return (
    <div className={styles.CocoAssistant}>
      <div className={styles.CocoAssistantHeader}>
        <AIGCHeader />
        <span style={{ marginLeft: 10 }}>
          <FormattedMessage
            id="AIGC.chatBox.header"
            defaultMessage="CoCo助手"
          />
        </span>
      </div>

      {!props.showDocument ? (
        <div className={styles.CocoAssistantBody}>
          {/* 正常的展示 */}
          {props.askContent ? (
            props.askContent.length > 0 && props.askContent[0] != 'loading' ? (
              <ReactMarkdown
                children={props.askContent}
                components={{
                  code({ node, inline, className, children, ...props }) {
                    const match = /language-(\w+)/.exec(className || '');
                    return !inline && match ? (
                      <SyntaxHighlighter
                        showLineNumbers={true}
                        style={vscDarkPlus}
                        language={match[1]}
                        PreTag="div"
                        {...props}
                      >
                        {String(children).replace(/\n$/, '')}
                      </SyntaxHighlighter>
                    ) : (
                      <code className={className} {...props}>
                        {children}
                      </code>
                    );
                  },
                  img: ImgRenderer,
                }}
              />
            ) : (
              <Skeleton active />
            )
          ) : (
            <FormattedMessage id="AIGC.chatBox.body" defaultMessage="" />
          )}
        </div>
      ) : (
        <div className={styles.CocoAssistantBody}>
          {/* 知识库的展示 */}
          {props.askContent ? (
            props.askContent == 'loading' ? (
              <Skeleton active />
            ) : (
              <div>
                <div className={styles.matchBoxData}>
                  {props.askContent.type == '1' ? (
                    <div className={styles.matchBoxDataText}>
                      <div>
                        <ReactMarkdown
                          components={{
                            code({
                              node,
                              inline,
                              className,
                              children,
                              ...props
                            }) {
                              const match = /language-(\w+)/.exec(
                                className || '',
                              );
                              return !inline && match ? (
                                <SyntaxHighlighter
                                  showLineNumbers={true}
                                  style={vscDarkPlus}
                                  language={match[1]}
                                  PreTag="div"
                                  {...props}
                                >
                                  {String(children).replace(/\n$/, '')}
                                </SyntaxHighlighter>
                              ) : (
                                <code className={className} {...props}>
                                  {children}
                                </code>
                              );
                            },
                            img: ImgRenderer,
                          }}
                        >
                          {props.askContent.content}
                        </ReactMarkdown>
                        {props.askContent.sources &&
                        props.askContent.sources.length > 0 ? (
                          <Collapse
                            bordered={false}
                            expandIconPosition={'end'}
                            defaultActiveKey="1"
                          >
                            <Collapse.Panel
                              header={getIntl().formatMessage({
                                id: 'worktable.work.knowledge.Sources',
                                defaultValue: 'Sources',
                              })}
                              key="1"
                            >
                              <div>
                                {props.askContent.sources?.map(
                                  (source, sourceIndex) => {
                                    if (+source.type === 2) {
                                      return (
                                        <div
                                          key={sourceIndex}
                                          style={{ height: '25px' }}
                                        >
                                          <p
                                            style={{
                                              fontSize: 12,
                                              width: '90%',
                                              overflow: 'hidden',
                                              whiteSpace: 'nowrap',
                                              textOverflow: 'ellipsis',
                                              float: 'left',
                                            }}
                                          >
                                            <a
                                              href={source.sourceUrl}
                                              target="_blank"
                                              style={{
                                                fontSize: 12,
                                                width: '100%',
                                              }}
                                            >
                                              {source.sourceName}
                                            </a>
                                          </p>
                                        </div>
                                      );
                                    } else {
                                      return (
                                        <div
                                          key={sourceIndex}
                                          style={{ height: '25px' }}
                                        >
                                          <Link
                                            to={'/documentPreview'}
                                            target="_blank"
                                          >
                                            <p
                                              style={{
                                                fontSize: 12,
                                                width: '90%',
                                                overflow: 'hidden',
                                                whiteSpace: 'nowrap',
                                                textOverflow: 'ellipsis',
                                                float: 'left',
                                              }}
                                              onClick={() => handleView(source)}
                                            >
                                              {source.sourceName}
                                            </p>
                                          </Link>
                                          <img
                                            style={{
                                              width: '16px',
                                              cursor: 'pointer',
                                              float: 'right',
                                            }}
                                            src={ViewIcon}
                                            onClick={() =>
                                              handleDownload(
                                                source.sourceUrl,
                                                source.sourceName,
                                              )
                                            }
                                          />
                                        </div>
                                      );
                                    }
                                  },
                                )}
                              </div>
                            </Collapse.Panel>
                          </Collapse>
                        ) : (
                          ''
                        )}
                      </div>
                    </div>
                  ) : props.askContent.type == '2' ? (
                    <div className={styles.matchBoxDataPhoto}>
                      <div className={styles.matchBoxDataImg}>
                        <img
                          src={props.askContent.content}
                          style={{ width: '100%', height: 'auto' }}
                        />
                      </div>
                    </div>
                  ) : props.askContent.type == '3' ? (
                    <div className={styles.matchBoxDataPhoto}>
                      <div className={styles.matchBoxDataImg}>
                        <video
                          controls
                          style={{ width: '100%', height: 'auto' }}
                        >
                          <source
                            src={props.askContent.content}
                            type="video/mp4"
                          />
                        </video>
                      </div>
                    </div>
                  ) : (
                    <div className={styles.matchBoxDataText}>
                      <div>{props.askContent.content}</div>
                    </div>
                  )}
                </div>

                <div
                  className={styles.matchBoxForm}
                  style={{ position: 'absolute', bottom: 14 }}
                >
                  {props.form}
                </div>
              </div>
            )
          ) : (
            <FormattedMessage id="AIGC.chatBox.body" defaultMessage="" />
          )}
        </div>
      )}
      {props.showIcon && (
        <div className={styles.CocoAssistantFooter}>
          {/* {!props.showDocument ? ( */}
          <div className={styles.CocoAssistantFooter2}>
            <div style={{ cursor: 'pointer' }} onClick={props.handleGood}>
              {props.isGood ? <GoodNo /> : <GoodOff />}
            </div>
            <span className={styles.CocoAssistantFooterSpan}></span>
            <div style={{ cursor: 'pointer' }} onClick={props.handleBad}>
              {props.isBad ? <RedBadNo /> : <BadOff />}
            </div>
            <span className={styles.CocoAssistantFooterSpan}></span>
          </div>
          {/* ) : (
            ''
          )} */}
          <div style={{ cursor: 'pointer' }}>
            <CopyToClipboard
              text={
                props.showDocument ? props.askContent.content : props.askContent
              }
              onCopy={(_, result) => {
                if (result) {
                  message.success(
                    getIntl().formatMessage({
                      id: 'work.order.detail.copy.success',
                      defaultValue: '复制成功',
                    }),
                  );
                } else {
                  message.error(
                    getIntl().formatMessage({
                      id: 'work.order.detail.copy.error',
                      defaultValue: '复制失败，请稍后再试',
                    }),
                  );
                }
              }}
            >
              <div className={styles.copyContent}>
                <Copy />
              </div>
            </CopyToClipboard>
          </div>
        </div>
      )}
    </div>
  );
};
//问题组件
const CocoQuestion = props => {
  return (
    <div className={styles.CocoQuestion}>
      <div className={styles.CocoQuestionHeader}>
        <span>
          <AIGCAva />
        </span>

        {props.editStatus ? (
          <div className={styles.CocoQuestionHeaderTxt}>
            <TextArea
              style={{
                height: 'auto',
                borderRadius: 2,
                border: '1px solid #3463FC',
                fontSize: 12,
                color: '#666666',
              }}
              value={props.questionContent}
              onChange={e => props.onChangeQusetion(e)}
            />
            <div className={styles.CocoQuestionHeaderTxtIcon}>
              <span
                onClick={props.questionCancel}
                style={{ cursor: 'pointer' }}
              >
                <AIGCN />
              </span>
              <span
                onClick={props.questionConfirm}
                style={{ cursor: 'pointer', marginLeft: 8 }}
              >
                <AIGCY />
              </span>
            </div>
          </div>
        ) : (
          <span
            style={{ marginLeft: 10 }}
            className={styles.CocoQuestionHeaderSpan}
          >
            {props.questionContent}
          </span>
        )}
      </div>
      {!props.editStatus && (
        <div
          style={{ cursor: 'pointer', height: 16 }}
          onClick={props.questionEdit}
        >
          <AIGCEdit />
        </div>
      )}
    </div>
  );
};
const CodeBlockRenderer = ({ language, value }) => {
  return (
    <SyntaxHighlighter language={language} style={theme}>
      {value}
    </SyntaxHighlighter>
  );
};
const mapStateToProps = ({ aigc, knowledgeQA, layouts }) => ({
  ...aigc,
  ...knowledgeQA,
  user: layouts.user,
});
export default connect(mapStateToProps)(ChatBox);
