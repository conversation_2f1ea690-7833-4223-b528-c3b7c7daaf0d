import React, { Component } from 'react';
import { connect } from 'umi';
import { loadChatWidget } from '../../utils/chatWidget';

class ChatWidget extends Component {
  constructor(props) {
    super(props);
    let containerId = Math.floor(Math.random() * 10) + '-goclouds-chat';
    this.state = {
      containerId: containerId,
    };
  }

  componentDidMount() {
    const { instanceId, companyId, lang } = this.props;
    this.loadChatWidgetWithLanguage(instanceId, companyId, lang);
  }

  componentDidUpdate(prevProps) {
    const { instanceId, companyId, lang } = this.props;
    if (prevProps.lang !== lang) {
      this.loadChatWidgetWithLanguage(instanceId, companyId, lang);
    }
  }

  loadChatWidgetWithLanguage(instanceId, companyId, languageCode) {
    loadChatWidget(this.state.containerId, instanceId, companyId, languageCode);
  }

  render() {
    return <div id={this.state.containerId}></div>;
  }
}

const mapStateToProps = ({ global }) => {
  return {
    lang: global.lang,
  };
};
export default connect(mapStateToProps)(ChatWidget);
