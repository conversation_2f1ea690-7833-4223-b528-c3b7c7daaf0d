import React, {
  useState,
  useEffect,
  useCallback,
  useImperativeHandle,
  forwardRef,
} from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  Button,
  Row,
  Col,
  Radio,
  notification,
} from 'antd';
import {
  connect,
  getIntl,
  FormattedMessage,
  useDispatch,
  useSelector,
} from 'umi';
import styles from './index.less';
import { PlusOutlined, MinusOutlined } from '@ant-design/icons';

const CustomModal = forwardRef((props, ref) => {
  const dispatch = useDispatch();
  //获得redux维护的字典
  const propertyTypeList = useSelector(
    state => state.customerExt.propertyTypeList,
  );
  const [loading, setLoading] = useState(false);
  let [isEdit, setIsEdit] = useState(false);
  let [selectList, setSelectList] = useState([]);
  let [propertyType, setPropertyType] = useState('');
  let [typeList, setTypeList] = useState([]);
  let [checkValue, setCheckValue] = useState(1);
  let [initialValues, setInitialValues] = useState({
    isRequired: 1,
  });
  let [propertyTypeLi, setPropertyTypeLi] = useState([]);
  const [form] = Form.useForm();
  useEffect(() => {
    getListOption();
  }, []);
  // useEffect(() => {
  //   if (props.updateId != '') {
  //     updateModelData(props.updateInterface, props.updateId);
  //   }
  // }, [props.updateId]);
  /**
   * 关闭弹窗
   */
  const handleCancel = () => {
    form.resetFields(); // 清空表单字段的值
    setSelectList([]);
    setIsEdit(false);
    props.changeSetOpen(false);
  };

  /**
   * 获取属性表单下拉
   */
  const getListOption = () => {
    dispatch({
      type: 'customerExt/getListOption',
      callback: response => {
        let { code, msg, data } = response;
        if (code === 200) {
          setPropertyTypeLi(data);
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };
  /**
   * 表单提交
   */
  const handleSubmit = values => {
    console.log(values, selectList, propertyTypeList, props, '12dsdk');
    const data = {
      ...values,
      [props.optionList]: selectList,
      optionsType: 1,
      interfaceUrl: null,
      languageCode: props.language,
    };
    setLoading(true);
    if (isEdit) {
      dispatch({
        type: props.updateInterface,
        payload: {
          ...data,
          [props.formName.id]: props.updateId,
        },
        callback: response => {
          let { code, msg } = response;
          // 取消加载
          setLoading(false);
          if (code === 200) {
            notification.success({
              message: getIntl().formatMessage({
                id: 'user.management.operation.success',
              }),
            });
            //刷新列表
            props.refreshList();
            // 关闭弹窗
            handleCancel();
          } else {
            console.log(msg);

            notification.error({
              message: msg,
            });
          }
        },
      });
    } else {
      dispatch({
        type: props.interface,
        payload: data,
        callback: response => {
          let { code, msg } = response;
          setLoading(false);
          if (code === 200) {
            notification.success({
              message: getIntl().formatMessage({
                id: 'user.management.create.success',
              }),
            });
            //刷新列表
            props.refreshList();
            // 关闭弹窗
            handleCancel();
          } else {
            console.log(msg);
            notification.error({
              message: msg,
            });
          }
          // 取消加载
        },
      });
    }
  };
  /**
   * 回显
   */
  // 在子组件中暴露方法给父组件调用
  useImperativeHandle(ref, () => ({
    updateModelData: async (interfaceName, values) => {
      setLoading(true);
      await dispatch({
        type: interfaceName,
        payload: values,
        callback: response => {
          let { code, msg, data } = response;
          if (code === 200) {
            setPropertyType(data.propertyTypeId);
            setSelectList(
              data.customerExtOptionVoList || data.workOrderExtOptionDefList,
            );
            form.setFieldsValue({
              ...data,
            });
            //走修改接口标识
            setIsEdit(true);
          } else {
            notification.error({
              message: msg,
            });
          }
          // 取消加载
          setLoading(false);
        },
      });
    },
  }));
  /**
   * 是否必填改变
   */
  const onChangeCheck = useCallback(
    e => {
      setCheckValue(e.target.value);
    },
    [checkValue],
  );
  /**
   * 加选项卡
   */
  const addSelectList = useCallback(() => {
    const newObject = {}; // 新的对象
    // 将新对象与现有数组合并
    const newSelectList = [...selectList, newObject];
    setSelectList(newSelectList);
    console.log(selectList);
  }, [selectList]);
  /**
   * 删除选项卡内容项
   */
  const removeSelectListItem = useCallback(
    indexToRemove => {
      const newSelectList = selectList.filter(
        (item, index) => index !== indexToRemove,
      );
      console.log(newSelectList);
      setSelectList(newSelectList);
    },
    [selectList],
  );

  // 更新输入框内容时的回调函数
  const handleInputChange = useCallback(
    (index, key, value) => {
      const updatedList = [...selectList];
      updatedList[index][key] = value;
      setSelectList(updatedList);
    },
    [selectList],
  );
  /**
   * 获取属性类型的值
   */
  const onChangeType = useCallback(
    value => {
      setPropertyType(value);
    },
    [propertyType],
  );
  return (
    <div>
      <Modal
        open={props.open}
        title={props.title}
        onCancel={handleCancel}
        footer={null}
        width={750}
        className={styles.customModal}
      >
        <Form
          form={form}
          onFinish={handleSubmit}
          initialValues={initialValues}
          style={{ marginLeft: 40 }}
        >
          {/* 属性名称，属性代码 */}
          <Row gutter={22}>
            <Col span={11}>
              <Form.Item
                name={props.formName.name}
                label={getIntl().formatMessage({
                  id: 'customer.ext.info.name',
                })}
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <Input
                  // disabled={isEdit}
                  placeholder={getIntl().formatMessage({
                    id: 'channel.please.input',
                  })}
                />
              </Form.Item>
            </Col>
            <Col span={11}>
              <Form.Item
                name={props.formName.code}
                label={getIntl().formatMessage({
                  id: 'customer.ext.info.code',
                })}
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <Input
                  disabled={isEdit}
                  placeholder={getIntl().formatMessage({
                    id: 'channel.please.input',
                  })}
                />
              </Form.Item>
            </Col>
          </Row>
          {/* 属性类型 */}
          <Row gutter={22}>
            <Col span={22}>
              <Form.Item
                name="propertyTypeId"
                label={getIntl().formatMessage({
                  id: 'customer.ext.info.type',
                })}
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                {propertyTypeLi.length > 0 && (
                  <Select
                    allowClear={true}
                    options={propertyTypeLi?.map(item => ({
                      label: item.propertyTypeName,
                      value: item.propertyTypeId,
                      key: item.propertyTypeId,
                    }))}
                    onChange={onChangeType}
                    placeholder={getIntl().formatMessage({
                      id: 'customer.worker.info.select.placeholder',
                    })}
                  />
                )}
              </Form.Item>
            </Col>
          </Row>
          {/* 是否必填 */}
          <Row gutter={22}>
            <Col span={22}>
              <Form.Item
                name="isRequired"
                label={getIntl().formatMessage({
                  id: 'customer.ext.info.require',
                })}
              >
                <Radio.Group onChange={onChangeCheck}>
                  <Radio value={1}>
                    <FormattedMessage id="user.management.operation.btn.yes" />
                  </Radio>
                  <Radio value={0}>
                    <FormattedMessage id="user.management.operation.btn.no" />
                  </Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>
          {/* 提示信息 */}
          <Row gutter={22}>
            <Col span={22}>
              <Form.Item
                name="prompt"
                label={getIntl().formatMessage({
                  id: 'customer.ext.info.tipsInfo',
                })}
              >
                <Input
                  placeholder={getIntl().formatMessage({
                    id: 'channel.please.input',
                  })}
                />
              </Form.Item>
            </Col>
          </Row>
          {/* 选项卡 */}
          {['1003', '1004', '1005', '1006'].includes(propertyType) && (
            <Row>
              <Col
                style={{ padding: 10, backgroundColor: '#F9F9F9' }}
                span={22}
              >
                <div
                  style={{
                    lineHeight: '40px',
                    marginBottom: 10,
                    display: 'flex',
                    alignItems: 'baseline',
                  }}
                >
                  <span style={{ fontSize: 14, margin: '0px 10px' }}>
                    <FormattedMessage id="customer.ext.info.selectList" />
                  </span>
                  <Button
                    type="primary"
                    shape="circle"
                    icon={<PlusOutlined style={{ fontSize: '10px' }} />}
                    style={{
                      borderRadius: '50%',
                      minWidth: 20,
                      height: 20,
                      width: 20,
                      verticalAlign: 'middle',
                    }}
                    onClick={addSelectList}
                  />
                </div>
                {selectList.map((item, index) => {
                  return (
                    <Row gutter={24} key={index}>
                      <Col span={11}>
                        <Form.Item
                          label={getIntl().formatMessage({
                            id: 'customer.ext.info.label',
                          })}
                        >
                          <Input
                            value={item.optionName}
                            onChange={e =>
                              handleInputChange(
                                index,
                                'optionName',
                                e.target.value,
                              )
                            }
                            placeholder={getIntl().formatMessage({
                              id: 'channel.please.input',
                            })}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={11}>
                        <Form.Item
                          label={getIntl().formatMessage({
                            id: 'customer.ext.info.value',
                          })}
                        >
                          <Input
                            value={item.optionValue}
                            onChange={e =>
                              handleInputChange(
                                index,
                                'optionValue',
                                e.target.value,
                              )
                            }
                            placeholder={getIntl().formatMessage({
                              id: 'channel.please.input',
                            })}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={2} style={{ textAlign: 'center' }}>
                        <Button
                          type="primary"
                          danger
                          icon={<MinusOutlined style={{ fontSize: '10px' }} />}
                          style={{
                            borderRadius: '50%',
                            backgroundColor: '#ff4d4f',
                            borderColor: '#ff4d4f',
                            marginTop: 5,
                          }}
                          onClick={() => removeSelectListItem(index)}
                        ></Button>
                      </Col>
                    </Row>
                  );
                })}
              </Col>
            </Row>
          )}

          <Form.Item
            wrapperCol={{
              offset: 8,
              span: 16,
            }}
            style={{ marginTop: 20 }}
          >
            <Button
              key="cancel"
              onClick={handleCancel}
              style={{ marginRight: 20 }}
            >
              <FormattedMessage id="customer.ext.info.cancel" />
            </Button>

            <Button loading={loading} htmlType="submit" type="primary">
              <FormattedMessage id="customer.ext.info.save" />
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
});

export default CustomModal;
