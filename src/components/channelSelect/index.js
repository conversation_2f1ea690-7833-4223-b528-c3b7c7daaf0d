import React, { useEffect } from 'react';
import { Select, Tag } from 'antd';
import { getIntl } from 'umi';

// 导入渠道图标
import EmailIcon from '@/assets/email.svg';
import AllchannelIcon from '@/assets/allchannel.svg';
import FacebookIcon from '@/assets/facebook.svg';
import WhatsAppIcon from '@/assets/whats-app.svg';
import TwitterIcon from '@/assets/twitter.svg';
import LineIcon from '@/assets/line.svg';
import PhoneIcon from '@/assets/phone.svg';
import ChatIcon from '@/assets/chat-icon.jpg';
import AppChatOutlinedIcon from '@/assets/AppChatOutlined.svg';
import WebVideoOutlinedIcon from '@/assets/WebVideoOutlined.svg';
import AppVideoOutlinedIcon from '@/assets/AppVideoOutlined.svg';
import AwsChannelIcon from '@/assets/aws-channel-icon.svg';
import NewInstagramIcon from '@/assets/ins.svg';
import NewLineIcon from '@/assets/new-line-icon.svg';
import NewTwitterIcon from '@/assets/new-twitter-icon.svg';
import NewTelegramIcon from '@/assets/new-telegram-icon.svg';
import NewWeComIcon from '@/assets/new-wecom-icon.svg';
import NewWeChatMiniProgramIcon from '@/assets/new-wechat-mini-program-icon.svg';
import NewWechatOfficialAccountIcon from '@/assets/new-wechat-official-account-icon.svg';
import NewShopifyIcon from '@/assets/new-shopify-icon.svg';
import NewGooglePlayIcon from '@/assets/google-play-icon.svg';
import NewWebOnlineVoiceIcon from '@/assets/web-online-voice-icon.svg';
import NewAppOnlineVoiceIcon from '@/assets/app-online-voice-icon.svg';
import TikTokIcon from '@/assets/new-tiktok-icon.svg';
import DiscordIcon from '@/assets/new-discord-icon.svg';
const { Option } = Select;

// 渠道图标映射函数
export const renderChannelIcon = (code, name) => {
  if (code == '0') {
    return (
      <>
        <img src={AllchannelIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else if (code == '1') {
    return (
      <>
        <img src={EmailIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else if (code == '3') {
    return (
      <>
        <img src={FacebookIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else if (code == '4') {
    return (
      <>
        <img src={WhatsAppIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else if (code == '5') {
    return (
      <>
        <img src={TwitterIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else if (code == '6') {
    return (
      <>
        <img src={LineIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else if (code == '7') {
    return (
      <>
        <img src={PhoneIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else if (code == '8') {
    return (
      <>
        <img src={ChatIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else if (code == '9') {
    return (
      <>
        <img src={AppChatOutlinedIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else if (code == '10') {
    return (
      <>
        <img src={WebVideoOutlinedIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else if (code == '11') {
    return (
      <>
        <img src={AppVideoOutlinedIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else if (code == '12') {
    return (
      <>
        <img src={AwsChannelIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else if (code == '13') {
    return (
      <>
        <img src={NewInstagramIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else if (code == '14') {
    return (
      <>
        <img src={NewLineIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else if (code == '15') {
    return (
      <>
        <img src={NewWeComIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else if (code == '16') {
    return (
      <>
        <img src={NewWechatOfficialAccountIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else if (code == '17') {
    return (
      <>
        <img src={NewWebOnlineVoiceIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else if (code == '18') {
    return (
      <>
        <img src={NewAppOnlineVoiceIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else if (code == '19') {
    return (
      <>
        <img src={NewTwitterIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else if (code == '20') {
    return (
      <>
        <img src={NewTelegramIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else if (code == '21') {
    return (
      <>
        <img src={NewWeChatMiniProgramIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else if (code == '22') {
    return (
      <>
        <img src={NewShopifyIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else if (code == '23') {
    return (
      <>
        <img src={NewGooglePlayIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else if (code == '24') {
    return (
      <>
        <img src={TikTokIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else if (code == '25') {
    return (
      <>
        <img src={DiscordIcon} width={16} height={16} />
        <span style={{ marginLeft: 5 }}>{name}</span>
      </>
    );
  } else {
    return <>{name}</>;
  }
};

// 渠道类型选择组件
export const ChannelTypeSelect = ({
  value,
  onChange,
  channelTypeList = [],
  placeholder,
  disabled = false,
  allowClear = true,
  defaultStyle,
  popupClassName,
  mode,
  valueKey = 'code',
  labelKey = 'name',
}) => {
  return (
    <Select
      mode={mode}
      style={defaultStyle}
      value={value}
      popupClassName={popupClassName}
      allowClear={allowClear}
      disabled={disabled}
      onChange={onChange}
      placeholder={
        placeholder ||
        getIntl().formatMessage({
          id: 'email.channel.configuration.channel.type.placeholder',
          defaultValue: '请选择渠道类型',
        })
      }
    >
      {channelTypeList &&
        channelTypeList.map(items => {
          return (
            <Option value={items[valueKey]} key={items[valueKey]}>
              {renderChannelIcon(items[valueKey], items[labelKey])}
            </Option>
          );
        })}
    </Select>
  );
};

export default ChannelTypeSelect;
