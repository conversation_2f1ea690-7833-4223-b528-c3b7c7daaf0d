import { useState, useEffect } from 'react';
import { FormattedMessage, setLocale, connect } from 'umi';
import styles from './index.less';
import { Dropdown, Popover } from 'antd';
import { Link, useLocation, useHistory } from 'umi';
import NewLangIcon from '@/assets/new-lang-icon.png';
import logo from '@/assets/logo.png';
import localStorage from 'localStorage';
import NewRegionIcon from '../../assets/new-region-icon.png';
import CookieConsent from '@/components/CookieConsent';
const Header = ({ dispatch }) => {
  const location = useLocation();
  const history = useHistory();
  const [langText, setLangText] = useState('中文');
  const [defaultSelectedKeys, setDefaultSelectedKeys] = useState('zh');
  const [activeKey, setActiveKey] = useState('home');
  const [activeKeyLang, setActiveKeyLang] = useState('zh');
  // 定义下拉菜单项
  const productDropdownItems = [
    {
      key: 'productChannel',
      title: <FormattedMessage id="home.page.new.product.channel.title" />,
      desc: <FormattedMessage id="home.page.new.product.channel.desc" />,
      path: '/productChannel',
    },
    {
      key: 'productAiCallCenter',
      title: <FormattedMessage id="home.page.new.product.callcenter.title" />,
      desc: <FormattedMessage id="home.page.new.product.callcenter.desc" />,
      path: '/productAiCallCenter',
    },
    {
      key: 'productAiAgent',
      title: <FormattedMessage id="home.page.new.product.agent.title" />,
      desc: <FormattedMessage id="home.page.new.product.agent.desc" />,
      path: '/productAiAgent',
    },
    {
      key: 'productAIGC',
      title: <FormattedMessage id="home.page.new.product.aigc.title" />,
      desc: <FormattedMessage id="home.page.new.product.aigc.desc" />,
      path: '/productAIGCCustomerService',
    },
    {
      key: 'productAIGCAssistant',
      title: <FormattedMessage id="home.page.new.product.assist.title" />,
      desc: <FormattedMessage id="home.page.new.product.assist.desc" />,
      path: '/productAIGCAssistant',
    },
    {
      key: 'productSmartWorkOrder',
      title: <FormattedMessage id="home.page.new.product.ticket.title" />,
      desc: <FormattedMessage id="home.page.new.product.ticket.desc" />,
      path: '/productSmartWorkOrder',
    },
    {
      key: 'productAiVoiceRobot',
      title: <FormattedMessage id="home.page.new.product.voice.title" />,
      desc: <FormattedMessage id="home.page.new.product.voice.desc" />,
      path: '/productAiVoiceRobot',
    },
    {
      key: 'productAIGCMarketing',
      title: <FormattedMessage id="home.page.new.product.marketing.title" />,
      desc: <FormattedMessage id="home.page.new.product.marketing.desc" />,
      path: '/productAIGCMarketing',
    },
    {
      key: 'productVideoCustomerService',
      title: <FormattedMessage id="home.page.new.product.video.title" />,
      desc: <FormattedMessage id="home.page.new.product.video.desc" />,
      path: '/productVideoCustomerService',
    },
    {
      key: 'productDataReport',
      title: <FormattedMessage id="home.page.new.product.report.title" />,
      desc: <FormattedMessage id="home.page.new.product.report.desc" />,
      path: '/productDataReport',
    },
  ];

  const solutionDropdownItems = [
    {
      key: 'solution1',
      label: <FormattedMessage id="header.solution.finance" />,
      path: '/solutionFinance',
    },
    {
      key: 'solution2',
      label: <FormattedMessage id="header.solution.retail" />,
      path: '/solutionRetail',
    },
    {
      key: 'solution3',
      label: <FormattedMessage id="header.solution.manufacturing" />,
      path: '/solutionManufacturing',
    },
    {
      key: 'solution4',
      label: <FormattedMessage id="header.solution.consumer.electronics" />,
      path: '/solutionConsumerElectronics',
    },
    {
      key: 'solution5',
      label: <FormattedMessage id="header.solution.new.energy" />,
      path: '/solutionNewEnergy',
    },
  ];

  const resourceDropdownItems = [
    // {
    //   key: 'resource1',
    //   label: <FormattedMessage id="header.resource.help.center" />,
    //   path: '/resourcesHelpCenter',
    // },
    {
      key: 'resource2',
      label: <FormattedMessage id="header.resource.blog" />,
      path: '/resourcesBlog',
    },
    {
      key: 'companyNews',
      label: <FormattedMessage id="header.company.news" />,
      path: '/companyNews',
    },
  ];
  // 回显中英文
  useEffect(() => {
    const lang = localStorage.getItem('lang');
    const langMap = {
      'zh-CN': { text: '中文', key: 'zh' },
      'en-US': { text: 'English', key: 'en' },
      'de-DE': { text: 'Deutsch', key: 'de' },
      ja: { text: '日本語', key: 'ja' },
      'id-ID': { text: 'Indonesian', key: 'id' },
    };

    if (lang && langMap[lang]) {
      setLangText(langMap[lang].text);
      setDefaultSelectedKeys(langMap[lang].key);
      setActiveKeyLang(langMap[lang].key);
    }
  }, []);
  useEffect(() => {
    // 根据路径设置当前激活的菜单项
    const pathname = location.pathname;
    // 定义路径和对应的key的映射关系
    const pathKeyMapping = {
      '/home': 'home',
      '/product': 'product',
      '/solution': 'solution',
      '/partner': 'partner',
      '/resource': 'resource',
      '/companyNews': 'companyNews',
      '/aiAgentLibrary': 'aiAgentLibrary',
    };
    console.log('pathname', pathname);
    // 遍历映射关系找到匹配的路径
    for (const [path, key] of Object.entries(pathKeyMapping)) {
      if (path === pathname || (path !== '/home' && pathname.includes(path))) {
        setActiveKey(key);
        break;
      }
    }
  }, [location]);

  const items = [
    {
      key: 'zh',
      label: '中文',
    },
    {
      key: 'en',
      label: 'English',
    },
    {
      key: 'de',
      label: 'Deutsch',
    },
    {
      key: 'ja',
      label: '日本語',
    },
    {
      key: 'id',
      label: 'Indonesian',
    },
  ];
  const menuItems = [
    {
      key: 'product',
      label: <FormattedMessage id="header.product" />,
      path: '/product',
      isDropdown: true,
      items: productDropdownItems,
    },
    {
      key: 'solution',
      label: <FormattedMessage id="header.solution" />,
      isDropdown: true,
      items: solutionDropdownItems,
    },
    {
      key: 'partner',
      label: <FormattedMessage id="header.partner" />,
      path: '/partner',
    },
    {
      key: 'aiAgentLibrary',
      // label: <FormattedMessage id="header.aiAgentLibrary" />,
      label: 'AI Agent Library',
      path: '/aiAgentLibrary',
    },
    {
      key: 'resource',
      label: <FormattedMessage id="header.resource" />,
      isDropdown: true,
      items: resourceDropdownItems,
    },
  ];
  const handleMenuClick = ({ key }) => {
    // 切换语言后刷新页面
    const langConfig = {
      zh: {
        text: '中文',
        locale: 'zh-CN',
      },
      en: {
        text: 'English',
        locale: 'en-US',
      },
      de: {
        text: 'Deutsch',
        locale: 'de-DE',
      },
      ja: {
        text: '日本語',
        locale: 'ja',
      },
      id: {
        text: 'Indonesian',
        locale: 'id-ID',
      },
    };
    if (langConfig[key]) {
      const { text, locale } = langConfig[key];
      setLangText(text);
      setLocale(locale, false);
      // localStorage.setItem('lang', locale);
      dispatch({
        type: 'global/changeLang',
        payload: locale,
      });
    }
    setActiveKeyLang(key);
    // window.location.reload();
  };
  const toJump = path => {
    history.push(path);
  };
  const renderMenuItem = item => {
    if (item.key === 'product') {
      return (
        <Dropdown
          key={item.key}
          arrow={false}
          // placement="bottom"
          dropdownRender={() => {
            return (
              <div className={styles.productDropdown}>
                {item.items.map(item => (
                  <Link
                    className={styles.productDropdownItem}
                    to={item.path}
                    key={item.key}
                  >
                    <div key={item.key}>
                      <div className={styles.productDropdownItemTitle}>
                        {item.title}
                      </div>
                      <div className={styles.productDropdownItemDesc}>
                        {item.desc}
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            );
          }}
        >
          <div
            key={item.key}
            className={`${activeKey === item.key ? styles.active : ''} ${
              styles.menuItem
            }`}
          >
            {item.label}
          </div>
        </Dropdown>
      );
    }
    if (item.isDropdown) {
      return (
        <Dropdown
          key={item.key}
          arrow
          placement="bottom"
          dropdownRender={() => {
            return (
              <div className={styles.headerDropdown}>
                {item.items.map(item => (
                  <div
                    onClick={() => toJump(item.path)}
                    className={styles.headerDropdownItem}
                    key={item.key}
                  >
                    {item.label}
                    <div className={styles.arrowWrapper}>
                      <div className={styles.arrow}></div>
                    </div>
                  </div>
                ))}
              </div>
            );
          }}
        >
          <div
            key={item.key}
            className={`${activeKey === item.key ? styles.active : ''} ${
              styles.menuItem
            }`}
          >
            {item.label}
          </div>
        </Dropdown>
      );
    }
    // 非下拉菜单
    return (
      <div
        key={item.key}
        className={`${activeKey === item.key ? styles.active : ''} ${
          styles.menuItem
        }`}
        onClick={() => toJump(item.path)}
      >
        {item.label}
      </div>
    );
  };

  const [regionItems, setRegionItems] = useState([
    {
      key: 'china',
      label: '中国',
      url: 'https://www.connectnow.cn',
      id: 'home.page.region.china',
    },
    {
      key: 'asia',
      label: '亚太',
      url: 'https://www.connectnowai.com',
      id: 'home.page.region.asia',
    },
    {
      key: 'europe',
      label: '欧洲',
      url: 'https://eu.connectnowai.com',
      id: 'home.page.region.europe',
    },
    {
      key: 'usa',
      label: '美国',
      url: 'https://us.connectnowai.com',
      id: 'home.page.region.usa',
    },
  ]);
  const { UMI_ENV, regionValue } = process.env;
  const [open, setOpen] = useState(false);
  const handleOpenChange = newOpen => {
    setOpen(newOpen);
  };

  return (
    <>
      <CookieConsent />
      <div className={styles.header}>
        <div className={styles.logo}>
          <Link to="/home">
            <img src={logo} alt="logo" />
          </Link>
        </div>
        <div className={styles.menu}>
          {menuItems.map(item => renderMenuItem(item))}
        </div>
        <div className={styles.right}>
          {/* <div className={styles.contactUs}>
          <FormattedMessage id="header.contact.us" defaultMessage="联系我们" />
        </div>
        <div className={styles.demo}>
          <FormattedMessage id="header.demo" defaultMessage="演示" />
        </div> */}
          {/* <div className={styles.login}>
          <Link to="/login">
            <FormattedMessage id="header.login" defaultMessage="登录" />
          </Link>
        </div> */}
          <Dropdown
            arrow
            placement="bottom"
            dropdownRender={() => {
              return (
                <div className={styles.langDropdownNew}>
                  {regionItems.map(item => (
                    <a
                      href={item.url}
                      key={item.key}
                      className={styles.langDropdownItem}
                    >
                      <FormattedMessage
                        id={item.id}
                        defaultMessage={item.label}
                      />
                    </a>
                  ))}
                </div>
              );
            }}
          >
            <div className={styles.lang}>
              <img src={NewRegionIcon} />
              <span>
                <FormattedMessage
                  id="awsAccountSetting.region"
                  defaultMessage="区域"
                />
              </span>
            </div>
          </Dropdown>
          <Dropdown
            arrow
            placement="bottom"
            dropdownRender={() => {
              return (
                <div className={styles.langDropdownNew}>
                  {items.map(item => (
                    <div
                      key={item.key}
                      onClick={() => handleMenuClick(item)}
                      className={`${styles.langDropdownItem} ${
                        activeKeyLang === item.key ? styles.langActive : ''
                      }`}
                    >
                      {item.label}
                    </div>
                  ))}
                </div>
              );
            }}
          >
            <div className={styles.lang}>
              <img src={NewLangIcon} />
              <span>{langText}</span>
            </div>
          </Dropdown>
          <div className={styles.loginButton}>
            <Link to="/site">
              <FormattedMessage id="header.register" />
            </Link>
            <div className={styles.arrowWrapper}>
              <div className={styles.arrow}></div>
            </div>
          </div>
          <div className={styles.registerButton}>
            <Link to="/login">
              <FormattedMessage id="header.login" />
            </Link>
            <div className={styles.arrowWrapper}>
              <div className={styles.arrow}></div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default connect(({ global }) => ({
  lang: global.lang,
}))(Header);
