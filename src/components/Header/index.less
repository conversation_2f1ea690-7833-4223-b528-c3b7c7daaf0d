.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 4rem;
  background-color: #fff;
  box-shadow: 0px 2px 30px 0px rgba(0, 0, 0, 0.15);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10002;
  padding: 0 6.25rem 0 6.25rem;

  .logo {
    min-width: 7.8125rem;
    min-height: 3.125rem;

    a {
      width: 7.8125rem;
      height: 3.125rem;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 75%;
        height: 75%;
      }
    }
  }

  .menu {
    height: 100%;
    flex: 1;
    margin-left: 20px;
    display: flex;
    align-items: center;
    padding-right: 6.25rem;

    .menuItem.active {
      color: #3463fc;
      position: relative;
      background-color: rgba(52, 99, 252, 0.05);
      font-size: 1.2rem;
      font-weight: bold;
      font-family: 'MicrosoftYaHei';

      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #3463fc;
      }
    }

    .menuItem {
      white-space: nowrap;
      position: relative;
      padding: 0 2rem;
      font-size: 1.25rem;
      color: #333333;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 4rem;

      &:hover {
        color: #3463fc;
        font-weight: bold;
        font-family: 'MicrosoftYaHei';

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 2px;
          background-color: #3463fc;
        }
      }
    }
  }

  .right {
    display: flex;
    align-items: center;
    gap: 30px;

    .contactUs,
    .demo,
    .login {
      font-size: 1.2rem;
      cursor: pointer;
      margin-left: 1.25rem;
      white-space: nowrap;

      a {
        color: #333333;
      }
    }

    .registerButton {
      a {
        color: var(--secondary-color);
        font-family: 'MicrosoftYaHei Regular';
      }

      --primary-color: #645bff;
      --secondary-color: #fff;
      --hover-color: #111;
      --arrow-width: 10px;
      --arrow-stroke: 2px;
      box-sizing: border-box;
      border: 1px solid var(--primary-color);
      border-radius: 5px;
      color: var(--secondary-color);
      background-color: var(--primary-color);
      padding: 8px 15px;
      display: flex;
      transition: 0.2s background;
      align-items: center;
      gap: 0.6em;
      font-weight: bold;

      .arrowWrapper {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .arrow {
        margin-top: 1px;
        width: var(--arrow-width);
        background: transparent;
        height: var(--arrow-stroke);
        position: relative;
        transition: 0.2s;
      }

      .arrow::before {
        content: '';
        box-sizing: border-box;
        position: absolute;
        border: solid var(--secondary-color);
        border-width: 0 var(--arrow-stroke) var(--arrow-stroke) 0;
        display: inline-block;
        top: -3px;
        right: 3px;
        transition: 0.2s;
        padding: 3px;
        transform: rotate(-45deg);
      }

      &:hover {
        background-color: var(--hover-color);
      }

      &:hover .arrow {
        background: var(--secondary-color);
      }

      &:hover .arrow:before {
        right: 0;
      }
    }

    .loginButton {
      --primary-color: #645bff;
      --secondary-color: #645bff;
      --hover-bg-color: #111;
      --hover-text-color: #fff;
      --arrow-width: 10px;
      --arrow-stroke: 2px;

      a {
        color: var(--secondary-color);
        font-family: 'MicrosoftYaHei Regular';
      }

      &:hover {
        a {
          color: var(--hover-text-color);
        }

        background-color: var(--hover-bg-color);
      }

      box-sizing: border-box;
      border: 1px solid var(--primary-color);
      border-radius: 5px;
      color: var(--secondary-color);
      padding: 8px 15px;
      background: transparent;
      display: flex;
      transition: 0.2s background;
      align-items: center;
      gap: 0.6em;
      font-weight: bold;

      .arrowWrapper {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .arrow {
        margin-top: 1px;
        width: var(--arrow-width);
        background: transparent;
        height: var(--arrow-stroke);
        position: relative;
        transition: 0.2s;
      }

      .arrow::before {
        content: '';
        box-sizing: border-box;
        position: absolute;
        border: solid var(--secondary-color);
        border-width: 0 var(--arrow-stroke) var(--arrow-stroke) 0;
        display: inline-block;
        top: -3px;
        right: 3px;
        transition: 0.2s;
        padding: 3px;
        transform: rotate(-45deg);
      }

      &:hover {
        background-color: var(--hover-bg-color);
        color: var(--hover-text-color);
      }

      &:hover .arrow {
        background: var(--hover-text-color);
      }

      &:hover .arrow:before {
        right: 0;
        border-color: var(--hover-text-color);
      }
    }

    .lang {
      display: flex;
      align-items: center;
      gap: 10px;
      cursor: pointer;
      &:hover {
        span {
          color: #3463fc;
          font-weight: bold;
        }
      }
      img {
        width: 18px;
        height: 18px;
      }

      span {
        font-size: 1.2rem;
        color: #333;
        font-family: 'MicrosoftYaHei Regular';
      }
    }
  }
}

.langDropdownNew {
  min-width: 110px;
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(2px);
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 4px 0;

  .langActive {
    color: #fff !important;
    background-color: #3463fc;
  }

  .langDropdownItem {
    position: relative;
    z-index: 999;
    width: 100%;
    font-size: 1.2rem;
    color: #333;
    text-align: center;
    cursor: pointer;
    line-height: 22px;
    padding: 5px 12px;
    transition: 0.3s;

    &:hover {
      color: #fff;
      background-color: #3463fc;
    }
  }
}

.productDropdown {
  width: 100vw;
  padding: 20px 30px;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
  border-radius: 10px;

  .productDropdownItem {
    width: calc(100% / 4 - 20px);
    min-height: 108px;
    padding: 20px;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    cursor: pointer;
    border: 1px solid #7260ff;

    &:hover {
      box-shadow: 0px 2px 5px 0px #00000026;
      background-color: #f5f7ff;
    }

    .productDropdownItemTitle {
      font-weight: 700;
      font-size: 1.25rem;
      color: #333333;
      font-family: 'MicrosoftYaHei Regular';
    }

    .productDropdownItemDesc {
      font-weight: 400;
      font-size: 1rem;
      color: #333333;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      line-clamp: 2;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      font-family: 'MicrosoftYaHei Regular';
    }
  }
}

.headerDropdown {
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
  border-radius: 10px;
  padding: 24px 100px 24px 24px;
  gap: 24px;

  .headerDropdownItem {
    font-weight: 700;
    font-size: 1.25rem;
    color: #333333;
    cursor: pointer;
    font-family: 'MicrosoftYaHei Regular';
    display: flex;
    gap: 0.625rem;
    align-items: center;
    &:hover {
      color: #3463fc;
      .arrow {
        background-color: #333;
        &::before {
          right: 0;
        }
      }
    }
  }
}

.arrowWrapper {
  display: flex;
  justify-content: center;
  align-items: center;

  .arrow {
    margin-top: 1px;
    width: 10px;
    height: 2px;
    position: relative;
    transition: 0.2s;

    &::before {
      content: '';
      box-sizing: border-box;
      position: absolute;
      border: solid #333;
      border-width: 0 2px 2px 0;
      display: inline-block;
      top: -3px;
      right: 3px;
      transition: 0.2s;
      padding: 3px;
      transform: rotate(-45deg);
    }
  }
}
