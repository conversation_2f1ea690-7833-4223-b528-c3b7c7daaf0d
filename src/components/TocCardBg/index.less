.tocCardBg {
  // height: 800px;
  display: flex;
  gap: 5rem;
  align-items: center;
  padding: 0 8.75rem;

  .tocCardBgLeft {
    width: 50%;
    min-height: 438px;
    // height: 438px;
    position: relative;
    .tocCardBgLeftImg {
      width: 100%;
    }
  }
  .tocCardBgLeftSolution {
    position: relative;
    .tocCardBgLeftImg {
      transform: scale(0.8);
    }
  }

  .tocCardBgRight {
    width: 50%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .tocCardBgRightTitle {
      font-weight: 700;
      font-size: 2rem;
      line-height: 2.5rem;
      letter-spacing: 0%;
      color: #333333;
      font-family: 'MicrosoftYaHei';
    }

    .tocCardBgRightContent {
      font-weight: 400;
      font-size: 1.25rem;
      line-height: 2.5rem;
      margin-top: 1.25rem;
      color: #333333;
      font-family: 'MicrosoftYaHei Regular';
    }

    .tocCardBgRightBtn {
      height: 45px;
      display: flex;
      align-items: center;
      position: relative;
      margin-top: 1.25rem;
      padding: 0.75rem 1.125rem;
      transition: all 0.2s ease;
      border: none;
      background: none;
      cursor: pointer;
      max-width: max-content;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        display: block;
        border-radius: 50px;
        background: #ff91e7;
        width: 45px;
        height: 45px;
        transition: all 0.3s ease;
      }

      span {
        position: relative;
        font-family: 'Ubuntu', sans-serif;
        font-size: 1.125rem;
        font-weight: 700;
        letter-spacing: 0.05em;
        color: #333;
      }

      svg {
        position: relative;
        top: 0;
        margin-left: 0.625rem;
        fill: none;
        stroke-linecap: round;
        stroke-linejoin: round;
        stroke: #333;
        stroke-width: 2;
        transform: translateX(-5px);
        transition: all 0.3s ease;
      }
      &:hover:before {
        width: 100%;
        background: #ff91e7;
      }
      &:hover svg {
        transform: translateX(0);
      }
      &:active {
        transform: scale(0.95);
      }
    }
  }
}

.tocCardNoBg {
  flex-direction: row-reverse;
}
