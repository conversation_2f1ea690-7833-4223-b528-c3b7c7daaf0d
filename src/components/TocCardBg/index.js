import styles from './index.less';
import { FormattedMessage, Link, useLocation } from 'umi';
import { useEffect, useState } from 'react';
const TocCardBg = props => {
  const location = useLocation();
  const pathname = location.pathname;
  const [isSolution, setIsSolution] = useState(false);
  useEffect(() => {
    setIsSolution(pathname.includes('solution'));
  }, [pathname]);
  const bgColorDom = () => {
    return (
      <div
        className={styles.tocCardBg}
        style={{ backgroundColor: props.bgColor }}
      >
        <div
          className={`${styles.tocCardBgLeft} ${
            isSolution ? styles.tocCardBgLeftSolution : ''
          }`}
        >
          {props.children ? (
            props.children
          ) : (
            <img
              className={styles.tocCardBgLeftImg}
              src={props.img}
              alt="img"
            />
          )}
        </div>
        <div className={styles.tocCardBgRight}>
          <div className={styles.tocCardBgRightTitle}>
            {props.titleId ? (
              <FormattedMessage
                id={props.titleId}
                defaultMessage={props.title}
              />
            ) : (
              <span>{props.title}</span>
            )}
          </div>
          <div className={styles.tocCardBgRightContent}>
            {props.descId ? (
              <FormattedMessage id={props.descId} defaultMessage={props.desc} />
            ) : (
              <span>{props.desc}</span>
            )}
          </div>
          <Link to="/login" className={styles.tocCardBgRightBtn}>
            <span>
              <FormattedMessage id="solution-finance-banner-button" />
            </span>
            <svg width="15px" height="10px" viewBox="0 0 13 10">
              <path d="M1,5 L11,5"></path>
              <polyline points="8 1 12 5 8 9"></polyline>
            </svg>
          </Link>
        </div>
      </div>
    );
  };
  const dom = () => {
    return (
      <div className={`${styles.tocCardBg} ${styles.tocCardNoBg}`}>
        <div
          className={`${styles.tocCardBgLeft} ${
            isSolution ? styles.tocCardBgLeftSolution : ''
          }`}
        >
          {props.children ? (
            props.children
          ) : (
            <img
              className={styles.tocCardBgLeftImg}
              src={props.img}
              alt="img"
            />
          )}
        </div>
        <div className={styles.tocCardBgRight}>
          <div className={styles.tocCardBgRightTitle}>
            {props.titleId ? (
              <FormattedMessage
                id={props.titleId}
                defaultMessage={props.title}
              />
            ) : (
              <span>{props.title}</span>
            )}
          </div>
          <div className={styles.tocCardBgRightContent}>
            {props.descId ? (
              <FormattedMessage id={props.descId} defaultMessage={props.desc} />
            ) : (
              <span>{props.desc}</span>
            )}
          </div>
          <Link to="/login" className={styles.tocCardBgRightBtn}>
            <span>
              <FormattedMessage id="solution-finance-banner-button" />
            </span>
            <svg width="15px" height="10px" viewBox="0 0 13 10">
              <path d="M1,5 L11,5"></path>
              <polyline points="8 1 12 5 8 9"></polyline>
            </svg>
          </Link>
        </div>
      </div>
    );
  };
  return props.bgColor ? bgColorDom() : dom();
};

export default TocCardBg;
