import React, { useEffect } from 'react';
import { Select, Tag } from 'antd';
import { getIntl } from 'umi';

// 导入渠道图标
import EmailIcon from '@/assets/email.svg';
import AllchannelIcon from '@/assets/allchannel.svg';
import FacebookIcon from '@/assets/facebook.svg';
import WhatsAppIcon from '@/assets/whats-app.svg';
import TwitterIcon from '@/assets/twitter.svg';
import LineIcon from '@/assets/line.svg';
import PhoneIcon from '@/assets/phone.svg';
import ChatIcon from '@/assets/chat-icon.jpg';
import AppChatOutlinedIcon from '@/assets/AppChatOutlined.svg';
import WebVideoOutlinedIcon from '@/assets/WebVideoOutlined.svg';
import AppVideoOutlinedIcon from '@/assets/AppVideoOutlined.svg';
import AwsChannelIcon from '@/assets/aws-channel-icon.svg';
import NewInstagramIcon from '@/assets/ins.svg';
import NewLineIcon from '@/assets/new-line-icon.svg';
import NewTwitterIcon from '@/assets/new-twitter-icon.svg';
import NewTelegramIcon from '@/assets/new-telegram-icon.svg';
import NewWeComIcon from '@/assets/new-wecom-icon.svg';
import NewWeChatMiniProgramIcon from '@/assets/new-wechat-mini-program-icon.svg';
import NewWechatOfficialAccountIcon from '@/assets/new-wechat-official-account-icon.svg';
import NewShopifyIcon from '@/assets/new-shopify-icon.svg';
import NewGooglePlayIcon from '@/assets/google-play-icon.svg';
import NewWebOnlineVoiceIcon from '@/assets/web-online-voice-icon.svg';
import NewAppOnlineVoiceIcon from '@/assets/app-online-voice-icon.svg';
import TikTokIcon from '@/assets/new-tiktok-icon.svg';
import DiscordIcon from '@/assets/new-discord-icon.svg';

const { Option, OptGroup } = Select;

const ChannelMultipleSelect = ({
  value = [],
  onChange,
  channelOptions = [],
  placeholder,
  disabled = false,
}) => {
  // 选中后tag标签颜色
  const tagRender = props => {
    const { label, value, closable, onClose } = props;
    let { sonItem, parentItem } = findChannelById(value);
    const onPreventMouseDown = event => {
      event.preventDefault();
      event.stopPropagation();
    };
    if (sonItem) {
      return (
        <Tag
          onMouseDown={onPreventMouseDown}
          closable={closable}
          onClose={onClose}
          style={{
            marginRight: 3,
          }}
        >
          {parentItem.name}
          <span>/{sonItem.name}</span>
        </Tag>
      );
    }
  };

  const findChannelById = channelId => {
    for (let option of channelOptions) {
      for (let op of option.channelVOList) {
        if (op.channelId == channelId) {
          return { sonItem: op, parentItem: option };
        }
      }
    }
    return {}; // 如果没有找到匹配的标签，返回空对象
  };

  // 生成渠道图标
  const renderChannelIcon = (code, name) => {
    if (code == '0') {
      return (
        <>
          <img src={AllchannelIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else if (code == '1') {
      return (
        <>
          <img src={EmailIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else if (code == '3') {
      return (
        <>
          <img src={FacebookIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else if (code == '4') {
      return (
        <>
          <img src={WhatsAppIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else if (code == '5') {
      return (
        <>
          <img src={TwitterIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else if (code == '6') {
      return (
        <>
          <img src={LineIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else if (code == '7') {
      return (
        <>
          <img src={PhoneIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else if (code == '8') {
      return (
        <>
          <img src={ChatIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else if (code == '9') {
      return (
        <>
          <img src={AppChatOutlinedIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else if (code == '10') {
      return (
        <>
          <img src={WebVideoOutlinedIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else if (code == '11') {
      return (
        <>
          <img src={AppVideoOutlinedIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else if (code == '12') {
      return (
        <>
          <img src={AwsChannelIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else if (code == '13') {
      return (
        <>
          <img src={NewInstagramIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else if (code == '14') {
      return (
        <>
          <img src={NewLineIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else if (code == '15') {
      return (
        <>
          <img src={NewWeComIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else if (code == '16') {
      return (
        <>
          <img src={NewWechatOfficialAccountIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else if (code == '17') {
      return (
        <>
          <img src={NewWebOnlineVoiceIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else if (code == '18') {
      return (
        <>
          <img src={NewAppOnlineVoiceIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else if (code == '19') {
      return (
        <>
          <img src={NewTwitterIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else if (code == '20') {
      return (
        <>
          <img src={NewTelegramIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else if (code == '21') {
      return (
        <>
          <img src={NewWeChatMiniProgramIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else if (code == '22') {
      return (
        <>
          <img src={NewShopifyIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else if (code == '23') {
      return (
        <>
          <img src={NewGooglePlayIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else if (code == '24') {
      return (
        <>
          <img src={TikTokIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else if (code == '25') {
      return (
        <>
          <img src={DiscordIcon} width={16} height={16} />
          <span style={{ marginLeft: 5 }}>{name}</span>
        </>
      );
    } else {
      return <>{name}</>;
    }
  };
  useEffect(() => {
    console.log(
      channelOptions,
      JSON.stringify(value),
      JSON.stringify(value.flat(Infinity)),
      'channelOptions',
    );
  }, [channelOptions]);
  return (
    <Select
      mode="multiple"
      optionLabelProp="label"
      optionFilterProp="children"
      showSearch
      allowClear
      disabled={disabled}
      placeholder={
        placeholder ||
        getIntl().formatMessage({
          id: 'feedbackPerformance.card.5.select.placeholder',
          defaultValue: '请选择渠道',
        })
      }
      filterOption={(inputValue, option) =>
        String(option.label)
          .toLowerCase()
          .indexOf(inputValue.toLowerCase()) >= 0
      }
      tagRender={tagRender}
      onChange={onChange}
      value={value}
    >
      {channelOptions?.map(group => (
        <OptGroup
          key={group.code}
          label={renderChannelIcon(group.code, group.name)}
        >
          {group?.channelVOList?.map(option => (
            <Option
              key={option.channelId}
              value={option.channelId}
              label={option.name}
            >
              <span>{option.name}</span>
            </Option>
          ))}
        </OptGroup>
      ))}
    </Select>
  );
};

export default ChannelMultipleSelect;
