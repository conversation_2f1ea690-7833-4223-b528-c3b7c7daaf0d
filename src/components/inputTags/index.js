import React, {
  useState,
  useEffect,
  forwardRef,
  useRef,
  useImperativeHandle,
} from 'react';
import { getIntl } from 'umi';
import styles from './index.less';
import { Input, notification, Tooltip, Tag } from 'antd';
const InputTags = forwardRef(({ termsTagsEditorValue, placeholder }, ref) => {
  let [menuToAgentTxt, setMenuToAgentTxt] = useState('');
  const [termsTags, setTermsTags] = useState([]);
  const tagsRef = useRef(null);

  useEffect(() => {
    //回显
    if (termsTagsEditorValue) {
      console.log(termsTags, termsTagsEditorValue);
      if (termsTags?.length < termsTagsEditorValue?.length) {
        console.log(termsTagsEditorValue);
        setTermsTags(termsTagsEditorValue);
      }
    }
  }, [termsTagsEditorValue]);
  //向上暴露已添加标签和清空函数
  useImperativeHandle(ref, () => ({
    termsTags: termsTags,
    resetFields: resetFields,
  }));
  /**
   * 清空数据
   */
  const resetFields = () => {
    setTermsTags([]);
    setMenuToAgentTxt('');
  };
  /**
   * 回显
   */
  // 在子组件中暴露方法给父组件调用
  // useImperativeHandle(ref, () => ({
  //   handleNext: handleNext,
  //   // handlePrev: handlePrev,
  // }));
  /**
   * 输入框change事件
   */
  const handleInputChange = e => {
    setMenuToAgentTxt(e.target.value);
  };
  /**
   * 回车事件
   */
  const handleEnterPress = e => {
    // 中文输入状态下按下回车键判断
    if (e.key === 'Enter') {
      if (!e.nativeEvent.isComposing) {
        // 直接按下回车键
        handleInputConfirm(e);
      }
    }
  };
  const handleInputConfirm = e => {
    if (termsTags && termsTags.length < 20) {
      if (menuToAgentTxt?.trim() && termsTags?.indexOf(menuToAgentTxt) === -1) {
        setTermsTags([...termsTags, menuToAgentTxt]);
      }
    } else {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'definition.synonyms.add.synonym.rules.number.tips.20',
          defaultValue: '最多添加20个标签！',
        }),
      });
    }
    setMenuToAgentTxt('');
    e.preventDefault();
  };
  /**
   * content4删除标签
   */
  const handleClose = removedTag => {
    const newTags = termsTags.filter(tag => tag !== removedTag);
    setTermsTags(newTags);
  };

  return (
    <div className={styles.inputTags} ref={tagsRef}>
      <Input
        type="text"
        value={menuToAgentTxt}
        maxLength={80}
        onChange={e => handleInputChange(e)}
        onKeyDown={e => handleEnterPress(e)}
        placeholder={getIntl().formatMessage({
          id: placeholder
            ? placeholder
            : 'definition.synonyms.input.placeholder',
        })}
      />
      {termsTags?.map((tag, index) => {
        const isLongTag = tag.length > 20;
        const tagElem = (
          <Tag
            className="edit-tag"
            key={tag}
            // closable={index !== 0}
            closable
            onClose={() => handleClose(tag)}
          >
            <span>{isLongTag ? `${tag.slice(0, 20)}...` : tag}</span>
          </Tag>
        );
        return isLongTag ? (
          <Tooltip title={tag} key={tag}>
            {tagElem}
          </Tooltip>
        ) : (
          tagElem
        );
      })}
    </div>
  );
});

export default InputTags;
