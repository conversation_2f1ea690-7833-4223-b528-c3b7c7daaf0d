.footerContainer {
  display: flex;
  flex-direction: column;
  .footerContent {
    background: rgba(236, 241, 255, 1);
    padding: 1.25rem 0;
    .center {
      max-width: 1200px;
      height: 36.4375rem;
      margin: 0 auto;
      padding: 3.125rem 0 1.875rem 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .content {
        display: flex;
        justify-content: space-between;
        .item {
          display: flex;
          flex-direction: column;
          gap: 1rem;
          a {
            color: #666;
            font-size: 1.165rem;
            font-family: 'MicrosoftYaHei Regular';
            display: flex;
            align-items: center;
            gap: 0.625rem;
            &:hover {
              color: #644cf6;
              .arrow {
                background-color: #644cf6;
                &::before {
                  right: 0;
                  opacity: 1;
                }
              }
            }
          }
          .title {
            color: rgba(51, 51, 51, 1);
            font-size: 1.5rem;
            font-weight: 700;
            font-family: 'MicrosoftYaHei';
          }
        }
      }
      .externalLink {
        display: flex;
        justify-content: space-between;
        margin-top: 2.5rem;
        .logo {
          width: 100px;
          cursor: pointer;
          img {
            width: 100%;
          }
        }
        .iconList {
          display: flex;
          gap: 30px;
          img {
            height: 25px;
            cursor: pointer;
          }
        }
      }
    }
  }
  .footerRecord {
    height: 3.75rem;
    background-color: rgba(52, 99, 252, 1);
    color: #fff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 1rem;
    div {
      font-family: 'MicrosoftYaHei Regular';
    }
    .icp {
      span:nth-child(2) {
        margin-left: 0.9375rem;
      }
    }
  }
}

.arrowWrapper {
  display: flex;
  justify-content: center;
  align-items: center;

  .arrow {
    margin-top: 1px;
    width: 10px;
    height: 2px;
    position: relative;
    transition: 0.2s;

    &::before {
      content: '';
      box-sizing: border-box;
      position: absolute;
      border: solid #644cf6;
      border-width: 0 2px 2px 0;
      display: inline-block;
      top: -3px;
      right: 3px;
      transition: 0.2s;
      padding: 3px;
      transform: rotate(-45deg);
      opacity: 0;
    }
  }
}
