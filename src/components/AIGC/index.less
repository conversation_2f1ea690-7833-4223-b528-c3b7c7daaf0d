//Modal
// .drawerBox {
//   :global {
//     .ant-modal-content {
//       background: linear-gradient(to left bottom, #7109ff, #187cfc);
//       border-radius: 4px;
//       height: 100%;
//     }

//     .react-draggable {
//       height: 100%;
//     }

//     .ant-modal-close-x {
//       width: 40px !important;
//       text-align: left;
//     }

//     .ant-modal-header {
//       background: linear-gradient(to left bottom, #7700fe, #4f4bf4);

//       .ant-modal-title {
//         font-size: 18px !important;
//         color: #fff !important;
//       }
//     }

//     .ant-modal-body {
//       height: 93%;
//       padding-top: 0;
//     }

//     .ant-tabs-nav-wrap {
//       width: 97px;
//     }

//     .ant-tabs-card {
//       height: 100%;
//     }

//     .ant-tabs-tab {
//       background: transparent !important;
//       border: none !important;
//       color: #fff !important;
//       height: 58px;
//       display: flex;
//       justify-content: center;
//       font-size: 13px;
//     }

//     .ant-tabs-tabpane-active {
//       padding-left: 0 !important;
//       height: 100%;
//     }

//     .ant-tabs-content-holder {
//       background-color: #fff;
//       border-radius: 4px;
//     }

//     .ant-tabs-content {
//       height: 100%;
//     }

//     .ant-tabs-tab-active {
//       // background: #fff !important;
//       // border-radius: 150px 0 0 150px !important;
//       background-image: url('../../assets/whiteBorder.png') !important;
//       background-repeat: no-repeat !important;
//       background-size: cover !important;
//       height: 58px !important;
//       width: 97px !important;
//       font-size: 13px;
//     }
//   }

//   .drawerIcon {
//     position: absolute;
//     top: 0;
//     left: 0;
//     transform: translateX(-40%) translateY(-40%);
//   }

//   .drawerExtra {
//     display: inline-block;
//     vertical-align: middle;
//     cursor: pointer;
//   }
// }
//drawer
// .drawerBox {
//   :global {
//     .ant-drawer-content {
//       background: linear-gradient(to left bottom, #7700fe, #187ffc);
//       border-radius: 4px;
//       position: relative;
//       overflow: visible;
//     }

//     .ant-drawer-content-wrapper {
//       top: auto;
//       bottom: 20px;
//     }

//     .ant-drawer-header {
//       border-bottom: none;
//       padding: 16px 10px 16px 0;
//     }

//     .ant-drawer-title {
//       font-size: 18px;
//       color: #fff;
//     }

//     .ant-drawer-body {
//       padding-top: 0;
//       padding-bottom: 10px !important;
//       padding-left: 10px !important;
//       height: 100%;
//     }

//     .ant-drawer-extra {
//       margin-right: 10px;
//     }

//     .ant-tabs-nav-wrap {
//       width: 102px;
//     }

//     .ant-tabs-card {
//       height: 100%;
//     }

//     .ant-tabs-tab {
//       background: transparent !important;
//       border: none !important;
//       color: #fff !important;
//       height: 58px;
//       display: flex;
//       justify-content: center;
//       font-size: 13px;
//     }

//     .ant-tabs-tabpane-active {
//       padding-left: 0 !important;
//       height: 100%;
//     }

//     .ant-tabs-content-holder {
//       background-color: #fff;
//       border-radius: 4px;
//     }

//     .ant-tabs-content {
//       height: 100%;
//     }

//     .ant-tabs-tab-active {
//       // background: #fff !important;
//       // border-radius: 150px 0 0 150px !important;
//       background-image: url('../../assets/whiteBorder.jpg') !important;
//       background-repeat: no-repeat !important;
//       background-size: cover !important;
//       height: 58px !important;
//       width: 102px !important;
//       font-size: 13px;
//     }
//   }

//   .drawerIcon {
//     position: absolute;
//     top: 0;
//     left: 0;
//     transform: translateX(-40%) translateY(-40%);
//   }

//   .drawerExtra {
//     display: inline-block;
//     vertical-align: middle;
//     cursor: pointer;
//   }
// }

.transformBox {
  transform: translateY(60px) !important;
}

.drawerBox {
  background: linear-gradient(to left bottom, #7700fe, #187ffc);
  border-radius: 4px;
  position: absolute;
  overflow: visible;
  top: 50px;
  right: 60px;

  .drawerHeader {
    border-bottom: none;
    padding: 16px 10px 8px 25px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: move;

    span {
      font-size: 18px;
      color: #fff;
    }

    .drawerExtra {
      margin-right: 10px;
    }
  }

  :global {
    .ant-tabs-nav-wrap {
      width: 102px;
    }

    .ant-tabs-card {
      margin: 0 20px;
    }

    .ant-tabs-tab {
      background: transparent !important;
      border: none !important;
      color: #fff !important;
      height: 58px;
      display: flex;
      justify-content: center;
      font-size: 12px;
    }

    .ant-tabs-tabpane-active {
      padding-left: 0 !important;
      height: 100%;
    }

    .ant-tabs-content-holder {
      background-color: #fff;
      border-radius: 4px;
    }

    .ant-tabs-content {
      height: 100%;
    }

    .ant-tabs-tab-active {
      // background: #fff !important;
      // border-radius: 150px 0 0 150px !important;
      background-image: url('../../assets/whiteBorder.jpg') !important;
      background-repeat: no-repeat !important;
      background-size: cover !important;
      height: 58px !important;
      width: 102px !important;
      font-size: 12px;
    }
  }

  .drawerIcon {
    position: absolute;
    top: 0;
    left: 0;
    transform: translateX(-40%) translateY(-40%);

    img {
      -webkit-user-drag: none;
      /* Chrome 和 Safari */
      user-drag: none;
      -webkit-user-select: none;
      /* Chrome 和 Safari */
      user-select: none;
      cursor: move;
    }
  }

  .drawerExtra {
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
  }
}
