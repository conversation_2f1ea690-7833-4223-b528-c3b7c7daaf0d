import React, { Component } from 'react';
import { connect, FormattedMessage, getIntl } from 'umi';
import styles from './index.less';
import { Button, Drawer, Tabs, Form, Input, Modal, notification } from 'antd';
import { ReactComponent as AIGCIcon } from '../../assets/AIGC.svg';
// import { ReactComponent as AIGCClose } from '../../assets/AIGCClose.svg';
// import { ReactComponent as AIGCBig } from '../../assets/AIGCBig.svg';
// import { ReactComponent as AIGCSmall } from '../../assets/AIGCSmall.svg';
import AIGCGif from '../../assets/ai.gif';
import AIGCBig from '../../assets/AIGCBig.jpg';
import AIGCSmall from '../../assets/AIGCSmall.jpg';
import AIGCClose from '../../assets/AIGCClose.jpg';
import ChatBox from '../chatBox';
import useAuthAccess from '@/utils/authAccess';

import Draggable from 'react-draggable';
import { CloseOutlined } from '@ant-design/icons';
const UploadIcon = () => (
  <svg
    style={{ float: 'left', marginRight: '3px', marginTop: '2px' }}
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M2.4001 14.104C2.27838 14.0886 2.16647 14.0293 2.08535 13.9372C2.00423 13.8452 1.95947 13.7267 1.95947 13.604C1.95947 13.4813 2.00423 13.3628 2.08535 13.2708C2.16647 13.1787 2.27838 13.1194 2.4001 13.104H13.6001C13.7218 13.1194 13.8337 13.1787 13.9149 13.2708C13.996 13.3628 14.0407 13.4813 14.0407 13.604C14.0407 13.7267 13.996 13.8452 13.9149 13.9372C13.8337 14.0293 13.7218 14.0886 13.6001 14.104H2.4001ZM8.5041 3.70399V11.432C8.48867 11.5537 8.42938 11.6656 8.33732 11.7467C8.24527 11.8279 8.12679 11.8726 8.0041 11.8726C7.88141 11.8726 7.76293 11.8279 7.67088 11.7467C7.57883 11.6656 7.51953 11.5537 7.5041 11.432V3.70399L3.6881 7.51999C3.64188 7.56622 3.587 7.60289 3.5266 7.6279C3.46621 7.65292 3.40147 7.6658 3.3361 7.6658C3.27073 7.6658 3.206 7.65292 3.1456 7.6279C3.0852 7.60289 3.03033 7.56622 2.9841 7.51999C2.93788 7.47377 2.90121 7.41889 2.87619 7.35849C2.85117 7.2981 2.8383 7.23337 2.8383 7.16799C2.8383 7.10262 2.85117 7.03789 2.87619 6.97749C2.90121 6.9171 2.93788 6.86222 2.9841 6.81599L7.5921 2.19999C7.69947 2.09637 7.84288 2.03845 7.9921 2.03845C8.14133 2.03845 8.28473 2.09637 8.3921 2.19999L13.0001 6.80799C13.0935 6.90135 13.1459 7.02797 13.1459 7.15999C13.1459 7.29202 13.0935 7.41864 13.0001 7.51199C12.9067 7.60535 12.7801 7.6578 12.6481 7.6578C12.5161 7.6578 12.3895 7.60535 12.2961 7.51199L8.5041 3.70399Z"
      fill="white"
    />
  </svg>
);

export class AIGCComponents extends Component {
  constructor(props) {
    super(props);
    this.state = {
      tabsCurrent: 1,
      tabs: [],
      closeIcon: false,
      disabled: true,
      bounds: {
        left: 0,
        top: 0,
        bottom: 0,
        right: 0,
      },
      visibleAi: false,
      dynamicStyles: false,
      knowledgeBaseList: [],
      languageList: [],
      standardTagList: [],
    };
  }
  draggleRef = React.createRef();
  modalRef = React.createRef();
  draggableRef = React.createRef();
  // //组件是否需要更新
  // shouldComponentUpdate(nextProps, nextState) {
  //   // 根据您的逻辑判断是否需要更新组件
  //   // 返回true表示需要更新，返回false表示不需要更新
  // }
  componentDidMount() {
    this.getTabs();
    this.queryDocumentKnowledge();
    this.queryAllStandardTag();
    this.getLanguageList();
  }
  componentDidUpdate(prevProps) {
    if (prevProps.authAccess !== this.props.authAccess) {
      this.getTabs();
      this.queryDocumentKnowledge();
      this.queryAllStandardTag();
      this.getLanguageList();
    }
    // 检测父组件传递的props是否发生变化
    if (prevProps.visible !== this.props.visible) {
      this.queryDocumentKnowledge();
      this.queryAllStandardTag();

      // 使用父组件传递的props来更新子组件的state
      this.setState({ visibleAi: this.props.visible });
    }
  }
  // 查询客户标签
  queryAllStandardTag = () => {
    this.props.dispatch({
      type: 'knowledgeQA/queryKnowStandardTag',
      callback: response => {
        if (response.code === 200) {
          this.setState({
            standardTagList: response.data,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  /**
   * 查询文档知识库下拉
   */
  queryDocumentKnowledge = () => {
    this.props.dispatch({
      type: 'documentKnowledgeBase/queryDocumentKnowledge',
      callback: response => {
        if (response.code == 200) {
          this.setState({
            knowledgeBaseList: response.data,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  /**语言下拉 */
  getLanguageList = () => {
    this.props.dispatch({
      type: 'personalCenter/listLanguage',
      callback: response => {
        let { code, data, msg } = response;
        if (code === 200) {
          this.setState({
            languageList: data,
          });
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };
  // 查询tabs
  getTabs = () => {
    this.props.dispatch({
      type: 'aigc/aiTabsList',
      callback: response => {
        if (response.code == 200) {
          setTimeout(() => {
            let tabs = response.data;
            // if (
            //   useAuthAccess().results({
            //     auth: this.props.authAccess,
            //     authKey: 'aigc_intelligent_knowledge_base',
            //   })
            // ) {
            tabs?.forEach(item => {
              switch (item.promptId) {
                case '10000':
                  item.disabled = !useAuthAccess().results({
                    auth: this.props.authAccess,
                    authKey: 'aigc_email_content_formalization',
                  });
                  break;
                case '20000':
                  item.disabled = !useAuthAccess().results({
                    auth: this.props.authAccess,
                    authKey: 'aigc_email_content_generation',
                  });
                  break;
                case '30000':
                  item.disabled = !useAuthAccess().results({
                    auth: this.props.authAccess,
                    authKey: 'aigc_grammar_correction',
                  });
                  break;
                case '40000':
                  item.disabled = !useAuthAccess().results({
                    auth: this.props.authAccess,
                    authKey: 'aigc_translation',
                  });
                  break;
                case '50000':
                  item.disabled = !useAuthAccess().results({
                    auth: this.props.authAccess,
                    authKey: 'freestyle_chat',
                  });
                  break;
              }
            });
            tabs.push({
              companyId: '1111222',
              promptId: '12345',
              promptName: getIntl().formatMessage({
                id: 'worktable.work.knowledge.icon2',
              }),
              disabled: !useAuthAccess().results({
                auth: this.props.authAccess,
                authKey: 'aigc_intelligent_knowledge_base',
              }),
            });
            // }
            this.setState({
              tabs: tabs,
            });
          }, 1000);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  /**
   * tabs标签切换
   */
  tabOnChange = e => {
    console.log(e);
    if (e === '12345') {
      // if (
      //   useAuthAccess().verification({
      //     auth: this.props.authAccess,
      //     authKey: 'aigc_intelligent_knowledge_base',
      //   })
      // ) {
      this.queryDocumentKnowledge();
      this.queryAllStandardTag();
      // this.setState({
      //   tabsCurrent: e,
      // });
      // }
    }

    this.setState({
      tabsCurrent: e,
    });
  };
  /**
   * 关闭抽屉
   */
  onCloseAIGC = () => {
    this.props.dispatch({
      type: 'aigc/setOpen',
      payload: false,
    });
    this.props.onParentStateChange(false);
  };
  /**
   * 收缩
   */
  handleZoom = () => {
    let btm =
      window.innerHeight -
      this.draggableRef.current?.getBoundingClientRect().bottom;
    console.log(btm);
    if (this.props.zoom == true && btm < 300) {
      const x =
        this.draggableRef.current?.getBoundingClientRect().left -
        this.draggableRef.current?.offsetLeft;
      this.draggableRef.current.style.transform = `translate(${x}px,60px)`;
    }

    this.props.dispatch({
      type: 'aigc/setZoom',
      payload: !this.props.zoom,
    });
  };
  /**
   * 拖动
   */
  onStart = (event, uiData) => {
    const { clientWidth, clientHeight } = window?.document?.documentElement;
    const targetRect = this.draggleRef?.current?.getBoundingClientRect();
    this.setState({
      bounds: {
        left: -targetRect?.left + uiData?.x,
        right: clientWidth - (targetRect?.right - uiData?.x),
        top: -targetRect?.top + uiData?.y,
        bottom: clientHeight - (targetRect?.bottom - uiData?.y),
      },
    });
  };

  render() {
    const { open, zoom, height } = this.props;
    const {
      tabsCurrent,
      tabs,
      closeIcon,
      disabled,
      bounds,
      visibleAi,
      dynamicStyles,
      knowledgeBaseList,
    } = this.state;

    const tabsList = tabs?.map(item => {
      return {
        label: item.promptName,
        key: item.promptId,
        disabled: item.disabled,
        children: (
          <div style={{ height: '100%' }}>
            <ChatBox
              showLanguage={
                ['30000', '40000', '70000', '90000', '12345'].includes(
                  item.promptId,
                )
                  ? true
                  : false
              }
              promptId={item.promptId}
              promptName={item.promptName}
              disabled={item.disabled}
              // 下面是知识库使用字段
              // 是否展示知识库
              showDocument={['12345'].includes(item.promptId) ? true : false}
              // 知识库语言下拉
              klanguageList={this.state.languageList}
              // 知识库下拉
              knowledgeBaseList={knowledgeBaseList}
              // 知识库标签
              standardTagList={this.state.standardTagList}
            />
          </div>
        ),
      };
    });
    // const divs = document.getElementsByClassName('ant-modal-root');
    // console.log(divs);
    // if (divs.length > 0) {
    //   divs[0].setAttribute('style', 'display:none');
    // }

    return (
      <Draggable
        size={200}
        defaultPosition={{ x: 25, y: 25 }}
        bounds={{ bottom: zoom ? 350 : 60, right: 60, top: 0 }}
        handle=".move"
      >
        <div
          className={`${styles.drawerBox}  ${
            dynamicStyles ? styles.transformBox : ''
          }`}
          style={{
            width: zoom ? 398 : 478,
            zIndex: 1003,
            display: open ? 'block' : 'none',
            height: zoom ? '50%' : '86%',
            transition: 'height 0.5s ease',
          }}
          ref={this.draggableRef}
        >
          <div className={`${styles.drawerIcon} move`}>
            <img
              src={AIGCGif}
              style={{
                width: 50,
                height: 50,
              }}
            />
          </div>
          <div className={`${styles.drawerHeader} move`}>
            <span>
              <FormattedMessage id="AIGC.title" defaultMessage="CoCo在线" />
            </span>
            <div>
              <div
                onClick={this.handleZoom}
                className={styles.drawerExtra}
                style={{ marginRight: 5 }}
              >
                {zoom ? (
                  <img src={AIGCSmall} style={{ width: 20, height: 20 }} />
                ) : (
                  <img src={AIGCBig} style={{ width: 20, height: 20 }} />
                )}
              </div>
              <div onClick={this.onCloseAIGC} className={styles.drawerExtra}>
                <img src={AIGCClose} style={{ width: 20, height: 20 }} />
              </div>
            </div>
          </div>

          <Tabs
            tabPosition="left"
            type="card"
            items={tabsList}
            onChange={this.tabOnChange}
            style={{ height: zoom ? '84%' : '90%' }}
          />
        </div>
      </Draggable>
      //抽屉写的不可拖动可操作底部内容
      // <div>
      //   <Drawer
      //     title={}
      //     placement="right"
      //     onClose={this.onCloseAIGC}
      //     open={true}
      //     contentWrapperStyle={{
      //       height: zoom ? '50%' : '89%',
      //       transition: 'height 0.5s ease',
      //     }}
      //     width={zoom ? 398 : 478}
      //     mask={false}
      //     style={{
      //       zIndex: 998,
      //       display: open ? 'block' : 'none',
      //     }}
      //     push={{ distance: 460 }}
      //     closeIcon={false}
      //     // tabsCurrent != 1 && (
      //     extra={
      //       <div>
      //         <div
      //           onClick={this.handleZoom}
      //           className={styles.drawerExtra}
      //           style={{ marginRight: 5 }}
      //         >
      //           {zoom ? (
      //             <img src={AIGCSmall} style={{ width: 20, height: 20 }} />
      //           ) : (
      //             <img src={AIGCBig} style={{ width: 20, height: 20 }} />
      //           )}
      //         </div>
      //         <div onClick={this.onCloseAIGC} className={styles.drawerExtra}>
      //           <img src={AIGCClose} style={{ width: 20, height: 20 }} />
      //         </div>
      //       </div>
      //     }
      //     className={styles.drawerBox}
      //   >
      //     <div className={styles.drawerIcon}>
      //       <img
      //         src={AIGCGif}
      //         style={{
      //           width: 50,
      //           height: 50,
      //         }}
      //       />
      //     </div>

      //     <Tabs
      //       tabPosition="left"
      //       type="card"
      //       items={tabsList}
      //       onChange={this.tabOnChange}
      //     />
      //

      //   </Drawer>
      // </div>
      //Modal写的可拖动不可操作底部内容
      // <div>
      //   <Modal
      //     title={
      //       <div
      //         style={{
      //           width: '100%',
      //           cursor: 'move',
      //         }}
      //         onMouseOver={() => {
      //           if (disabled) {
      //             this.setState({
      //               disabled: false,
      //             });
      //           }
      //         }}
      //         onMouseOut={() => {
      //           this.setState({
      //             disabled: true,
      //           });
      //         }}
      //       >
      //         <FormattedMessage id="AIGC.title" defaultMessage="CoCo在线" />
      //         <div
      //           onClick={this.onCloseAIGC}
      //           className={styles.drawerExtra}
      //           style={{ float: 'right' }}
      //         >
      //           <img src={AIGCClose} style={{ width: 20, height: 20 }} />
      //         </div>
      //         <div
      //           onClick={this.handleZoom}
      //           className={styles.drawerExtra}
      //           style={{ marginRight: 6, float: 'right' }}
      //         >
      //           {zoom ? (
      //             <img src={AIGCBig} style={{ width: 20, height: 20 }} />
      //           ) : (
      //             <img src={AIGCSmall} style={{ width: 20, height: 20 }} />
      //           )}
      //         </div>
      //       </div>
      //     }
      //     closeIcon={<div style={{ display: 'none' }}></div>}
      //     onCancel={this.onCloseAIGC}
      //     visible={visibleAi}
      //     width={zoom ? 478 : 418}
      //     mask={false}
      //     zIndex={1000}
      //     maskClosable={false}
      //     style={{
      //       zIndex: 999,
      //       height: zoom ? '85%' : '50%',
      //       transition: 'height 0.5s ease',
      //       left: 500,
      //     }}
      //     footer={null}
      //     className={styles.drawerBox}
      //     // getContainer={() => document.getElementById('modal-root')}
      //     modalRender={modal => (
      //       <Draggable
      //         disabled={this.state.disabled}
      //         bounds={this.state.bounds}
      //         onStart={(event, uiData) => this.onStart(event, uiData)}
      //       >
      //         <div ref={this.draggleRef}>{modal}</div>
      //       </Draggable>
      //     )}
      //   >
      //     <div className={styles.drawerIcon}>
      //       <img
      //         src={AIGCGif}
      //         style={{
      //           width: 50,
      //           height: 50,
      //         }}
      //       />
      //     </div>
      //     <Tabs
      //       tabPosition="left"
      //       type="card"
      //       items={tabsList}
      //       onChange={this.tabOnChange}
      //     />
      //     {/* {tabsCurrent == 1 && (

      //       <Drawer
      //         title={
      //           <FormattedMessage
      //             id="AIGC.Drawer.two.title"
      //             defaultMessage="回复工单"
      //           />
      //         }
      //         width={450}
      //         contentWrapperStyle={{
      //           margin: '80px 0 15px 0',
      //         }}
      //         closable={false}
      //         mask={false}
      //         extra={
      //           <div>
      //             <div
      //               onClick={this.onCloseAIGC}
      //               className={styles.drawerExtra}
      //             >
      //               <CloseOutlined />
      //             </div>
      //           </div>
      //         }
      //         className="workerDrawer"
      //         onClose={this.onCloseAIGC}
      //         open={open}
      //       >
      //         <Form
      //           name="Form"
      //           ref={this.formRef}
      //           labelCol={{
      //             span: 5,
      //           }}
      //           wrapperCol={{
      //             span: 14,
      //           }}
      //           labelAlign="right"
      //           labelWrap
      //           onFinish={this.onFinish}
      //           style={{ maxWidth: 600, marginTop: 20 }}
      //         >
      //           <Form.Item
      //             label={getIntl().formatMessage({
      //               id: 'AIGC.Drawer.two.input.label',
      //             })}
      //             name="language"
      //             rules={[
      //               {
      //                 required: true,
      //               },
      //             ]}
      //           >
      //             <Input
      //               name="email"
      //               placeholder={getIntl().formatMessage({
      //                 id: 'channel.please.input',
      //               })}
      //             />
      //           </Form.Item>

      //           <Form.Item
      //             label={getIntl().formatMessage({
      //               id: 'AIGC.Drawer.two.button.label',
      //             })}
      //             name="timezoneId"
      //           >
      //             <Button icon={<UploadIcon />} type="primary">
      //               <FormattedMessage
      //                 id="create.work.order.btn.upload"
      //                 defaultValue="上传"
      //               />
      //             </Button>
      //           </Form.Item>

      //           <Form.Item
      //             label={getIntl().formatMessage({
      //               id: 'AIGC.Drawer.two.input.editor',
      //             })}
      //             name="language"
      //           >
      //             <Input
      //               name="email"
      //               placeholder={getIntl().formatMessage({
      //                 id: 'channel.please.input',
      //               })}
      //             />
      //           </Form.Item>
      //           <Form.Item
      //             wrapperCol={{
      //               offset: 8,
      //               span: 16,
      //             }}
      //             style={{ marginTop: 60 }}
      //           >
      //             <Button style={{ marginRight: 20 }}>
      //               <FormattedMessage
      //                 id="AIGC.Drawer.two.form.cancel"
      //                 defaultValue="取消"
      //               />
      //             </Button>
      //             <Button type="primary" htmlType="submit">
      //               <FormattedMessage
      //                 id="AIGC.Drawer.two.form.reply"
      //                 defaultValue="回复"
      //               />
      //             </Button>
      //           </Form.Item>
      //         </Form>
      //       </Drawer>
      //     )} */}
      //   </Modal>
      // </div>
    );
  }
}

const mapStateToProps = ({ aigc, layouts }) => ({
  open: aigc.open,
  zoom: aigc.zoom,
  height: aigc.height,
  authAccess: layouts.auth,
});
export default connect(mapStateToProps)(AIGCComponents);
