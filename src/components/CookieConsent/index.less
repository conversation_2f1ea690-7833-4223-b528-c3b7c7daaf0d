.cookieConsent {
  max-width: 60%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  border-radius: 3.125rem 3.125rem 0 0;
  border: 0.1875rem solid #fff;
  background: rgba(255, 255, 255, 0.7);
  box-shadow: 0 0.25rem 0.625rem 0 rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(0.9375rem);
  padding: 3.5rem 6.25rem;

  .cookieConsentContent {
    color: #333;
    font-family: 'Microsoft YaHei';
    font-size: 1.5rem;
    font-style: normal;
    font-weight: 400;
    line-height: 200%;
    margin-bottom: 5rem;
  }

  .cookieConsentButton {
    display: flex;
    width: 100%;
    align-items: center;
    gap: 1.375rem;
    justify-content: flex-end;

    .accept {
      padding: 1rem 1.5rem;
      color: #fff;
      font-family: 'Microsoft YaHei';
      font-size: 1.25rem;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 1.25rem;
      background: #644cf6;
      cursor: pointer;
    }

    .necessary {
      color: #333;
      font-family: 'Microsoft YaHei';
      font-size: 1.25rem;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      cursor: pointer;
    }
  }
}
