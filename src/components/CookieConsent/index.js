import React, { useState, useEffect } from 'react';
import { FormattedMessage, Link, getLocale } from 'umi';
import styles from './index.less';

const CookieConsent = () => {
  const [visible, setVisible] = useState(false);
  useEffect(() => {
    // 检查本地存储中是否已经同意
    const hasConsented = localStorage.getItem('cookieConsent');
    if (!hasConsented) {
      setVisible(true);
    }
  }, []);

  const handleOk = () => {
    // 保存用户同意到本地存储
    localStorage.setItem('cookieConsent', 'true');
    setVisible(false);
  };

  const getCookiePolicyLink = () => {
    const locale = getLocale();
    if (locale === 'zh-CN') {
      return (
        <a
          href={`${window.location.origin}/#/cookiePolicy`}
          target="_blank"
          rel="noopener noreferrer"
        >
          《Cookie 政策》
        </a>
      );
    } else {
      return (
        <a
          href={`${window.location.origin}/#/cookiePolicy`}
          target="_blank"
          rel="noopener noreferrer"
        >
          《Cookie Policy》
        </a>
      );
    }
  };

  return (
    <>
      {visible && (
        <div className={styles.cookieConsent}>
          <div className={styles.cookieConsentContent}>
            <FormattedMessage
              id="cookie.consent.content"
              values={{
                link: getCookiePolicyLink(),
              }}
            />
          </div>
          <div className={styles.cookieConsentButton}>
            {/* 接受cookie 只接受必要cookie */}
            <div className={styles.accept} onClick={handleOk}>
              <FormattedMessage
                id="cookie.consent.accept"
                defaultValue="接受Cookie"
              />
            </div>
            {/* 拒绝cookie 拒绝所有cookie */}
            <div className={styles.necessary} onClick={handleOk}>
              <FormattedMessage
                id="cookie.consent.reject"
                defaultValue="接受必要Cookie"
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default CookieConsent;
