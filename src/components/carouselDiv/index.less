.carousel {
  position: absolute;
}

// 从左往右
.carousel_box1 {
  left: 13vw;
  border-radius: 4px;
  transition: transform 0.8s ease, left 0.8s ease;
  transform: scale(1);
  z-index: 99;

  .progressCardContent {
    color: #fff;
    background: linear-gradient(
      to right,
      rgba(52, 99, 252, 1),
      rgba(173, 48, 229, 1)
    );
    width: 24.5vw !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);

    .progressCardContentLeftBtm {
      font-size: 12px !important;
      color: #fff !important;
    }

    .progressCardContentLeftTop {
      color: #fff !important;
      font-size: 18px !important;
      font-weight: bold;
    }

    :global {
      .ant-progress-text {
        color: #fff;
      }
    }
  }
}

.carousel_box1_collapsed {
  left: 15vw !important;

  .progressCardContent {
    width: 25.5vw !important;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.5);
  }

  to {
    opacity: 1;
    transform: scale(0.9) translateX(-20px);
  }
}

.carousel_box2 {
  z-index: 9;
  left: 1vw;
  transform: scale(0.9) rotateY(8deg);
  transition: 0.8s;

  // animation: fadeIn 0.8s forwards;
  .progressCardContent {
    box-shadow: 0 1.81px 9.06px rgba(0, 0, 0, 0.08);
  }

  :global {
    .ant-progress-text {
      font-size: 12px;
    }
  }
}

.carousel_box2_collapsed {
  .progressCardContent {
    width: 25vw !important;
  }
}

.carousel_box3 {
  z-index: 9;
  right: 0px;
  position: absolute;
  left: 27.6vw;
  transition: 0.8s;
  transform: scale(0.9) rotateY(-8deg);

  .progressCardContent {
    box-shadow: 0 1.81px 9.06px rgba(0, 0, 0, 0.08);
  }

  :global {
    .ant-progress-text {
      font-size: 12px;
    }
  }
}

.carousel_box3_collapsed {
  left: 31vw !important;
}

.carousel_box4 {
  left: 15vw;
  transform: scale(0.2);
  z-index: 1;
}

.carousel_box4_collapsed {
  left: 16 !important;
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: scale(0.9);
  }

  to {
    opacity: 0;
    transform: scale(0.5);
  }
}

.carousel_box5 {
  z-index: 9;
  right: 0px;
  position: absolute;
  left: 29.6vw;
  animation: fadeOut 0.8s forwards;

  :global {
    .ant-progress-text {
      font-size: 12px;
    }
  }
}

// 从右往左
.carousel_box11 {
  right: 13.5vw;
  border-radius: 4px;
  transition: right 0.8s ease;
  z-index: 99;

  .progressCardContent {
    color: #fff;
    background: linear-gradient(
      to right,
      rgba(52, 99, 252, 1),
      rgba(173, 48, 229, 1)
    );
    width: 24.5vw !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);

    .progressCardContentLeftBtm {
      font-size: 12px !important;
      color: #fff !important;
    }

    .progressCardContentLeftTop {
      color: #fff !important;
      font-size: 18px !important;
      font-weight: bold;
    }

    :global {
      .ant-progress-text {
        color: #fff;
      }
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.5);
  }

  to {
    opacity: 1;
    transform: scale(0.9) translateX(-20px);
  }
}

.carousel_box12 {
  z-index: 9;
  right: 29.6vw;
  position: absolute;
  left: 0;
  transition: 0.8s;
  transform: scale(0.9);

  .progressCardContent {
    box-shadow: 0 1.81px 9.06px rgba(0, 0, 0, 0.08);
  }

  :global {
    .ant-progress-text {
      font-size: 12px;
    }
  }
}

.carousel_box13 {
  z-index: 9;
  right: -26px;
  // animation: fadeIn 0.8s forwards;
  transform: scale(0.9);
  transition: 0.8s;

  .progressCardContent {
    box-shadow: 0 1.81px 9.06px rgba(0, 0, 0, 0.08);
  }

  :global {
    .ant-progress-text {
      font-size: 12px;
    }
  }
}

.carousel_box14 {
  right: 15vw;
  transform: scale(0.2);
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: scale(0.9);
  }

  to {
    opacity: 0;
    transform: scale(0.5);
  }
}

.carousel_box15 {
  z-index: 9;
  left: 0;
  position: absolute;
  right: 29.6vw;
  animation: fadeOut 0.8s forwards;

  :global {
    .ant-progress-text {
      font-size: 12px;
    }
  }
}

.progressCardContent {
  padding: 20px;
  background-color: #ffffff;
  border-radius: 4px;
  // box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.08);
  width: 24vw;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;

  .progressCardContentLeft {
    float: left;
    display: flex;
    flex-direction: column;

    .progressCardContentLeftTop {
      font-size: 16px;
      font-weight: bold;
    }

    .progressCardContentLeftBtm {
      font-size: 10px;
      color: #999999;
    }
  }

  .progressCardContentRight {
    float: right;
  }

  .progress2 {
    :global {
      .ant-progress-bg {
        background-color: #37c837;
      }
    }
  }

  .progress3 {
    :global {
      .ant-progress-bg {
        background-color: #fcb830;
      }
    }
  }

  .progress4 {
    :global {
      .ant-progress-bg {
        background-color: #8500bb;
      }
    }
  }

  :global {
    .ant-progress {
      line-height: 2;
    }

    .ant-progress-outer {
      width: 77%;
    }

    .ant-progress-inner {
      width: 90%;
    }

    .ant-progress-bg {
      height: 10px !important;
    }
  }
}
