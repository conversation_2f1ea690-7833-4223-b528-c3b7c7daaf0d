.ticketDetailComponent {
  width: 760px;
  .ticketTitleContainer {
    width: 100%;
    height: 150px;
    .titleText {
      color: #333;
      font-size: 18px;
      font-style: normal;
      font-weight: 700;
      margin-bottom: 10px;
    }
    .detailItem {
      width: 35%;
      float: left;
      margin-bottom: 10px;
      p {
        width: 35%;
        height: 26px;
        line-height: 26px;
        text-align: right;
        float: left;
        margin-bottom: 0px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-right: 5px;
      }
      .detailText {
        height: 26px;
        line-height: 26px;
        text-align: left;
        float: left;
        margin-bottom: 0px;
        width: 60%;
      }
      .ticketTypeText {
        width: 55%;
        float: left;
        margin-bottom: 0px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        text-align: left;
        height: 26px;
        line-height: 26px;
      }

      .extIntsListDetail {
        width: 60%;
        text-align: left;
        float: left;
        margin-bottom: 0px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        height: 26px;
        line-height: 26px;

        :global {
          .ant-tag {
            font-size: 12px;
          }

          .ant-tag:first-child {
            margin-left: 0px;
          }
        }
      }

      .starImg {
        width: 16px;
        float: left;
        margin-right: 4px;
        margin-top: 5px;
      }

      .channelTypeImg {
        min-width: 60px;
        float: left;
        padding: 0px 8px;
        border-radius: 4px;
        background: rgba(52, 99, 252, 0.1);

        img {
          width: 14px;
          margin-right: 3px;
          float: left;
          margin-top: 8px;
        }

        span {
          color: #3463fc;
        }
      }

      .agentImg {
        img {
          width: 14px;
          margin-right: 3px;
          float: left;
          margin-top: 8px;
          border-radius: 90px;
        }
      }

      .priority1 {
        width: 35px;
        height: 26px;
        line-height: 26px;
        float: left;
        color: #13c825;
        border-radius: 4px;
        text-align: center;
        border: 1px solid rgba(19, 200, 37, 0.5);
        background: rgba(19, 200, 37, 0.15);
      }

      .priority3 {
        width: 35px;
        height: 26px;
        line-height: 26px;
        float: left;
        color: #3463fc;
        border-radius: 4px;
        text-align: center;
        border: 1px solid rgba(52, 99, 252, 0.5);
        background: rgba(52, 99, 252, 0.05);
      }

      .priority5 {
        width: 35px;
        height: 26px;
        line-height: 26px;
        float: left;
        color: #f22417;
        border-radius: 4px;
        text-align: center;
        border: 1px solid rgba(242, 36, 23, 0.5);
        background: rgba(242, 36, 23, 0.05);
      }

      :global {
        .ant-rate-star-first .anticon,
        .ant-rate-star-second .anticon {
          margin-top: 5px;
          vertical-align: revert;
        }
      }
    }
  }
  .customerInfoContainer {
    width: 100%;
    min-height: 110px;
    margin-top: 10px;
    .titleText {
      color: #333;
      font-size: 18px;
      font-style: normal;
      font-weight: 700;
      text-align: left;
      margin-bottom: 10px;
    }
    .detailItem {
      width: 35%;
      float: left;
      margin-bottom: 10px;
      p {
        width: 35%;
        height: 26px;
        line-height: 26px;
        text-align: right;
        float: left;
        margin-bottom: 0px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-right: 5px;
      }
      .customerDetailText {
        float: left;

        img {
          width: 14px;
          margin-right: 3px;
        }
        span {
          color: #3463fc;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px;
        }
      }
    }
    .detailTagItem {
      width: 100%;
      float: left;
      margin-bottom: 10px;
      p {
        width: 12.2%;
        height: 26px;
        line-height: 26px;
        text-align: right;
        float: left;
        margin-bottom: 0px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin-right: 5px;
      }
      .customerDetailTag {
        width: 85%;
        //height: 30px;
        //overflow: hidden;
        //overflow-y: scroll;
        float: left;
        .tagText {
          color: #333;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 18px;
        }

        :global {
          .ant-tag:first-child {
            margin-left: 0px;
          }
          .ant-tag {
            font-size: 12px;
            margin-bottom: 3px;
          }
        }
      }
    }
  }
  .contentSummaryContainer {
    width: 100%;
    height: 170px;
    margin-top: 10px;
    .leftContent {
      width: 69%;
      float: left;
      .titleText {
        color: #333;
        font-size: 18px;
        font-style: normal;
        font-weight: 700;
        text-align: left;
        margin-bottom: 10px;
      }
      .detailContentSummary {
        width: 96%;
        height: 100px;
        border-radius: 4px;
        background: #f9f9f9;
        padding: 10px;
        overflow: hidden;
        overflow-y: scroll;
        /* 隐藏滚动条 */
        scrollbar-width: none;
        /* firefox */
        -ms-overflow-style: none;
      }
      .detailContentSummary::-webkit-scrollbar {
        display: none;
        /* Chrome Safari */
      }
    }
    .rightContent {
      width: 30%;
      height: 150px;
      float: left;
      border-left: 1px solid #e6e6e6;
      padding: 0px 10px 0px 25px;

      .moodContent {
        width: 100%;
        height: 30px;
        margin-bottom: 10px;

        .moodIcon {
          float: left;
          width: 20px;
          margin-right: 12px;
          cursor: pointer;
        }
      }

      .toDoTitle {
        width: 100%;
        height: 25px;
      }
      .labelText {
        float: left;
        color: #333;
        font-size: 14px;
        margin-right: 12px;
      }

      .toDoContent {
        width: 100%;
        height: 70px;
        margin-top: 12px;
        overflow: hidden;
        overflow-y: scroll;
        /* 隐藏滚动条 */
        scrollbar-width: none;
        /* firefox */
        -ms-overflow-style: none;

        .alreadyDone {
          font-size: 12px;
          color: #999;
          text-decoration-line: line-through;
        }
        .toDo {
          font-size: 12px;
          color: #333;
        }
      }

      .toDoContent::-webkit-scrollbar {
        display: none;
        /* Chrome Safari */
      }
    }
  }
}
