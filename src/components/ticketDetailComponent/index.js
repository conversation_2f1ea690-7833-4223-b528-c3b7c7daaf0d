import React, { useState, useRef, useEffect } from 'react';
import { Tag, Spin, notification } from 'antd';
import styles from './index.less';
import { useDispatch, getIntl, FormattedMessage } from 'umi';
import DefaultUserName from '../../assets/user-name.png';
import EmailIcon from '../../assets/email-channel.png';
import PhoneIcon from '../../assets/phone-voice.png';
import MoodGoodActiveIcon from '../../assets/mood-good-active.png';
import MoodGoodIcon from '../../assets/mood-good.png';
import MoodNormalActiveIcon from '../../assets/mood-normal-active.png';
import MoodNormalIcon from '../../assets/mood-normal.png';
import MoodBadActiveIcon from '../../assets/mood-bad-active.png';
import MoodBadIcon from '../../assets/mood-bad.png';

const TicketDetailComponent = props => {
  const dispatch = useDispatch();
  // const [customerMood, seCustomerMood] = useState(1);
  const [loading, setLoading] = useState(false);
  const [detailData, setDetailData] = useState([]);
  const [allTagsData, setAllTagsData] = useState([]);

  useEffect(() => {
    if (props.data.workRecordId) {
      setLoading(true);
      setDetailData([]);
      setAllTagsData([]);
      ticketGetDetails(props.data.workRecordId);
    }
  }, [props.data]);

  // 查询工单详情
  const ticketGetDetails = workRecordId => {
    dispatch({
      type: 'workOrderCenter/ticketGetDetails',
      payload: workRecordId,
      callback: response => {
        setLoading(false);
        if (response.code === 200) {
          setDetailData(response.data);

          const customerTagData = response.data?.customerTag;
          const allTags = customerTagData.flatMap(item => item.tagList);
          setAllTagsData(allTags);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  return (
    <Spin spinning={loading}>
      <div className={styles.ticketDetailComponent}>
        <div className={styles.ticketTitleContainer}>
          <p className={styles.titleText}>
            {detailData?.workRecordDetailVo?.workRecordTheme
              ? detailData?.workRecordDetailVo?.workRecordTheme
              : ''}
            （ID：
            {detailData?.workRecordDetailVo?.wordRecordCode
              ? detailData?.workRecordDetailVo?.wordRecordCode
              : '--'}
            ）
          </p>
          <div className={styles.detailItem}>
            <p
              title={getIntl().formatMessage({
                id: 'work.order.detail.info',
                defaultValue: '处理客服：',
              })}
            >
              <FormattedMessage
                id="work.order.detail.info"
                defaultMessage="处理客服："
              />
            </p>
            <p className={styles.detailText}>
              {detailData?.workRecordDetailVo?.agentName
                ? detailData?.workRecordDetailVo?.agentName
                : '--'}
            </p>
          </div>
          <div className={styles.detailItem}>
            <p
              title={getIntl().formatMessage({
                id: 'work.order.detail.info.1',
                defaultValue: '优先级：',
              })}
            >
              <FormattedMessage
                id="work.order.detail.info.1"
                defaultMessage="优先级："
              />
            </p>
            <span>
              <span className={styles.priority3}>
                {detailData?.workRecordDetailVo?.priorityLevelName
                  ? detailData?.workRecordDetailVo?.priorityLevelName
                  : '--'}
              </span>
            </span>
          </div>
          <div className={styles.detailItem}>
            <p
              title={getIntl().formatMessage({
                id: 'work.order.detail.info.2',
                defaultValue: '来源渠道：',
              })}
            >
              <FormattedMessage
                id="work.order.detail.info.2"
                defaultMessage="来源渠道："
              />
            </p>
            <p className={styles.detailText}>
              {detailData?.workRecordDetailVo?.channelConfigName
                ? detailData?.workRecordDetailVo?.channelConfigName
                : '--'}
            </p>
          </div>
          <div className={styles.detailItem}>
            <p
              title={getIntl().formatMessage({
                id: 'work.order.detail.info.3',
                defaultValue: '工单分类：',
              })}
            >
              <FormattedMessage
                id="work.order.detail.info.3"
                defaultMessage="工单分类："
              />
            </p>
            <p className={styles.ticketTypeText}>
              {detailData?.workRecordDetailVo?.workRecordTypeName
                ? detailData?.workRecordDetailVo?.workRecordTypeName
                : '--'}
            </p>
          </div>
          <div className={styles.detailItem}>
            <p
              title={getIntl().formatMessage({
                id: 'work.order.detail.info.note',
                defaultValue: '最新备注：',
              })}
            >
              <FormattedMessage
                id="work.order.detail.info.note"
                defaultMessage="最新备注："
              />
            </p>
            <p className={styles.detailText}>
              {detailData?.workOrderRemarksVO?.operationLogReason
                ? detailData?.workOrderRemarksVO?.operationLogReason
                : '--'}
            </p>
          </div>
        </div>
        <div className={styles.customerInfoContainer}>
          <p className={styles.titleText}>
            <FormattedMessage
              id="customerInformation.contactCustomer.info.title"
              defaultValue="客户信息"
            />
          </p>
          <div className={styles.detailItem}>
            <p
              title={getIntl().formatMessage({
                id: 'contact.customers.contact.information.contact.email',
                defaultValue: '联系邮箱：',
              })}
            >
              <FormattedMessage
                id="contact.customers.contact.information.contact.email"
                defaultMessage="联系邮箱："
              />
            </p>
            <div className={styles.customerDetailText}>
              {detailData?.crmCustomerVO?.emailAddress ? (
                <>
                  <img src={EmailIcon} />
                  <span>{detailData?.crmCustomerVO?.emailAddress}</span>
                </>
              ) : (
                '--'
              )}
            </div>
          </div>
          <div className={styles.detailItem}>
            <p
              title={getIntl().formatMessage({
                id: 'contact.customers.contact.information.contact.number',
                defaultValue: '联系电话：',
              })}
            >
              <FormattedMessage
                id="contact.customers.contact.information.contact.number"
                defaultMessage="联系电话："
              />
            </p>
            <div className={styles.customerDetailText}>
              {detailData?.crmCustomerVO?.telephone ? (
                <>
                  <img src={PhoneIcon} />
                  <span>{detailData?.crmCustomerVO?.telephone}</span>
                </>
              ) : (
                '--'
              )}
            </div>
          </div>
          <div className={styles.detailTagItem}>
            <p
              title={getIntl().formatMessage({
                id: 'new.customerDataGroupManagement.left.btn.3.select',
                defaultValue: '客户标签：',
              })}
            >
              <FormattedMessage
                id="new.customerDataGroupManagement.left.btn.3.select"
                defaultMessage="客户标签："
              />
            </p>
            <div className={styles.customerDetailTag}>
              {allTagsData.length > 0
                ? allTagsData?.map(itemTag => {
                    return (
                      <Tag className={itemTag.tagColorCode || 'colorType1'}>
                        <span className={styles.tagText}>
                          {itemTag.categoryId ===
                          'private_tag_category_code' ? (
                            <FormattedMessage
                              id="tag.management.tab.private"
                              defaultMessage="私有标签"
                            ></FormattedMessage>
                          ) : itemTag.categoryContent ? (
                            itemTag.categoryContent
                          ) : (
                            '--'
                          )}{' '}
                          /{' '}
                        </span>
                        {itemTag.tagContent ? itemTag.tagContent : '--'}
                      </Tag>
                    );
                  })
                : '--'}
            </div>
          </div>
        </div>
        {detailData?.workRecordDetailVo?.summarizeStatus ? (
          <div className={styles.contentSummaryContainer}>
            <div className={styles.leftContent}>
              <p className={styles.titleText}>
                <FormattedMessage
                  id="work.order.detail.info.content.summary"
                  defaultValue="AIGC内容总结"
                />
              </p>
              <div className={styles.detailContentSummary}>
                {detailData?.workRecordDetailVo?.contentSummary
                  ? detailData?.workRecordDetailVo?.contentSummary
                  : '--'}
              </div>
            </div>
            <div className={styles.rightContent}>
              <div className={styles.moodContent}>
                <span className={styles.labelText}>
                  <FormattedMessage
                    id="work.order.detail.customer.mood"
                    defaultMessage="客户心情"
                  />
                </span>
                <img
                  className={styles.moodIcon}
                  src={
                    detailData?.workRecordDetailVo?.customerMood === 1
                      ? MoodGoodActiveIcon
                      : MoodGoodIcon
                  }
                />
                <img
                  className={styles.moodIcon}
                  src={
                    detailData?.workRecordDetailVo?.customerMood === 2
                      ? MoodNormalActiveIcon
                      : MoodNormalIcon
                  }
                />
                <img
                  className={styles.moodIcon}
                  src={
                    detailData?.workRecordDetailVo?.customerMood === 3
                      ? MoodBadActiveIcon
                      : MoodBadIcon
                  }
                />
              </div>
              <div className={styles.toDoTitle}>
                <span className={styles.labelText}>
                  <FormattedMessage
                    id="work.order.detail.to.do"
                    defaultMessage="代办事项"
                  />
                </span>
              </div>
              <div className={styles.toDoContent}>
                {detailData?.workRecordDetailVo?.waitExecuteList?.map(item => {
                  if (item.waitExecuteStatus) {
                    return (
                      <p className={styles.alreadyDone}>
                        {item.waitExecuteEvent}
                      </p>
                    );
                  } else {
                    return (
                      <p className={styles.toDo}>{item.waitExecuteEvent}</p>
                    );
                  }
                })}
              </div>
            </div>
          </div>
        ) : null}
      </div>
    </Spin>
  );
};

export default TicketDetailComponent;
