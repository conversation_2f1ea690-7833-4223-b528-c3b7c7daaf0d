import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';

const variants = {
  fadeRight: {
    hidden: custom => ({
      x: 100,
      opacity: 0,
      scale: custom?.scale || 1,
      translateX: custom?.translateX || '0%',
    }),
    visible: custom => ({
      x: 0,
      opacity: 1,
      scale: custom?.scale || 1,
      translateX: custom?.translateX || '0%',
      transition: {
        duration: custom?.duration || 0.6,
        delay: custom?.delay || 0,
      },
    }),
  },
  fadeLeft: {
    hidden: custom => ({
      x: -100,
      opacity: 0,
      scale: custom?.scale || 1,
      translateX: custom?.translateX || '0%',
    }),
    visible: custom => ({
      x: 0,
      opacity: 1,
      scale: custom?.scale || 1,
      translateX: custom?.translateX || '0%',
      transition: {
        duration: custom?.duration || 0.6,
        delay: custom?.delay || 0,
      },
    }),
  },
  fadeUp: {
    hidden: custom => ({
      y: 100,
      opacity: 0,
      scale: custom?.scale || 1,
    }),
    visible: custom => ({
      y: 0,
      opacity: 1,
      scale: custom?.scale || 1,
      transition: {
        duration: custom?.duration || 0.6,
        delay: custom?.delay || 0,
      },
    }),
  },
  fadeDown: {
    hidden: custom => ({
      y: -100,
      opacity: 0,
      scale: custom?.scale || 1,
    }),
    visible: custom => ({
      y: 0,
      opacity: 1,
      scale: custom?.scale || 1,
      transition: {
        duration: custom?.duration || 0.6,
        delay: custom?.delay || 0,
      },
    }),
  },
};

const MotionFade = ({
  as = motion.div,
  type = 'fadeRight',
  threshold = 0.3,
  triggerOnce = false,
  scale,
  translateX,
  delay = 0, // 新增延迟参数，默认为0
  duration = 1, // 新增持续时间参数，默认为0.6
  ...props
}) => {
  const [ref, inView] = useInView({
    threshold,
    triggerOnce,
  });

  const Component = as;
  const customStyles = {
    scale,
    translateX,
    delay, // 将延迟参数传递给variants
    duration, // 将持续时间参数传递给variants
  };

  return (
    <Component
      ref={ref}
      variants={variants[type]}
      initial="hidden"
      animate={inView ? 'visible' : 'hidden'}
      custom={customStyles}
      {...props}
    />
  );
};

export default MotionFade;
