import { history, getIntl } from 'umi';
import AOS from 'aos';
import { setLanguageByDomain } from '@/utils/utils';
import { setLocale } from 'umi';
// 添加路由监听，页面切换时滚动到顶部
history.listen(() => {
  window.scrollTo(0, 0);
});

// rem 适配函数
function setRem() {
  const isMobile = /Android|iPhone|iPad|iPod|Windows Phone|BlackBerry/i.test(
    navigator.userAgent,
  );
  const baseWidth = isMobile ? 375 : 1920; // 设计稿基准宽度
  const minWidth = isMobile ? 344 : 1366; // 最小宽度
  const maxWidth = isMobile ? 430 : 2560; // 最大宽度
  const baseSize = isMobile ? 14 : 16; // 基准字体大小

  let clientWidth = document.documentElement.clientWidth;
  // 限制最小最大宽度
  clientWidth = Math.min(maxWidth, Math.max(minWidth, clientWidth));

  const scale = clientWidth / baseWidth;
  document.documentElement.style.fontSize = baseSize * scale + 'px';
}

// 初始化
setRem();
// 窗口大小改变时重新计算
window.addEventListener('resize', setRem);

/**
 * 做权限管理
 *    如果没有登录系统，那么不能访问登录后的链接
 *    如果已经登录，那么访问/或者/login直接跳转到首页
 *    如果在其他标签页退出系统，那么本标签页在刷新页面或者有新情求时跳转到登录页
 * @param oldRender
 */
export function render(oldRender) {
  // 根据域名自动设置语言
  const domainLang = setLanguageByDomain();
  setLocale(domainLang, false);

  // 先检查浏览器是否支持
  if (!window.Notification) {
    console.log('浏览器不支持通知');
  } else {
    // 检查用户曾经是否同意接受通知
    if (Notification.permission === 'default') {
      // 用户还未选择，可以询问用户是否同意发送通知
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          console.log('用户同意授权');
        } else if (permission === 'default') {
          console.warn('用户关闭授权 未刷新页面之前 可以再次请求授权');
        } else {
          // denied
          console.log('用户拒绝授权 不能显示通知');
        }
      });
    } else {
      console.log('用户已授权');
    }
  }
  // 通过token来判断是否登录了系统。 这里还可以通过其他有效的方式判断是否登录
  let token = localStorage.getItem('Authorization');
  // console.log(token, history.location.pathname);
  if (token) {
    //在index.html头部中添加base标签，强制登陆后页面内链接都跳新页面
    var newElement = document.createElement('base');
    newElement.target = '_blank';
    document.head.appendChild(newElement);
    //获取登录角色
    const roleId = sessionStorage.getItem('roleId');
    //有token跳到首页
    if (history.location.pathname === '/login') {
      history.replace('/');
    }

    //暂时控制营销不跳到首页
    if (
      history.location.pathname === '/' &&
      ['1002', '1004'].includes(roleId)
    ) {
      history.replace('/campaignCalendar');
    }
    //控制座席登录后直接跳到工作台
    if (
      history.location.pathname === '/' &&
      ['1003', '1005'].includes(roleId)
    ) {
      sessionStorage.setItem(
        'currentMenu',
        `${getIntl().formatMessage({
          id: 'layout.worktable',
        })},`,
      );
      sessionStorage.setItem('currentUrl', '/worktable');
      sessionStorage.setItem('currentSelectKeys', '800000');
      sessionStorage.setItem('currentSelectID', '800000');
      sessionStorage.setItem('showWorkTable', true);
    }
    oldRender();
  } else {
    if (
      ![
        '/login',
        '/register',
        '/loginTips',
        '/contactUs',
        '/workshop1',
        '/workshop2',
        '/workshop3',
        '/workshop4',
        '/workshop5',
        '/workshop6',
        '/workshop7',
        '/workshop8',
        '/workshop9',
        '/workshop10',
        '/workshop11',
        '/workshop12',
        '/workshop13',
        '/workshop14',
        '/workshop15',
        '/workshop16',
        '/workshop17',
        '/workshop18',
        '/workshop19',
        '/workshop20',
        '/workshopNewChat',
        '/terms-service',
        '/cloudContactCenter',
        '/test',
        '/documentPreview',
        '/site',
        '/userTerms',
        '/privacyPolicy',
        '/cookiePolicy',
        '/partner',
        '/aiAgentLibrary',
        '/resourcesBlog',
        '/resourcesBlogDetail',
        '/lineChannelDocument',
        '/weChatOfficialDocument',
        '/whatsAppChannelDocument',
      ].includes(history.location.pathname)
    ) {
      // 判断是否为手机端
      const isMobile = /Android|iPhone|iPad|iPod|Windows Phone|BlackBerry/i.test(
        navigator.userAgent,
      );
      if (isMobile) {
        history.replace('/homePage');
      } else {
        history.replace('/home');
      }
    }
    oldRender();
  }
}
export function onRouteChange() {
  AOS.init({ duration: 1000, once: false });
  AOS.refresh();
}
