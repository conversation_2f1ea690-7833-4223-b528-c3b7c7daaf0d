.layoutContent {
  width: 100%;
  height: 100%;

  .layoutContentAlert {
    position: absolute;
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;

    :global {
      .ant-alert {
        border-radius: 6px !important;
        font-size: 12px !important;
      }

      .ant-alert-warning {
        background-color: #fdf6ec;
        border: 1px solid #faecd8;
        box-shadow: 0px 2px 5px 0px #00000026;
        padding: 15px 20px;

        .ant-alert-message {
          color: #fcbe44;
        }
      }

      .ant-alert-error {
        background-color: #fef0f0;
        border: 1px solid #fde2e2;
        box-shadow: 0px 2px 5px 0px #00000026;
        padding: 15px 20px;

        .ant-alert-message {
          color: #f22417;
        }
      }
    }
  }

  .logo {
    height: 40px;
    float: left;
    margin-top: 8px;
    margin-left: 10px;
    margin-right: 36px;
  }

  // 座席状态内容样式
  .headerAgent {
    display: flex;
    align-items: center;
    height: 65px;

    span {
      margin-left: 4px;
      font-size: 12px;
      font-weight: 400;
      color: #ffffff;
      text-decoration: underline;
      margin-top: 5px;
    }

    :global {
      .ant-select .ant-select-selector {
        padding: 0 !important;
        // height: 20px !important;
      }

      .ant-select-arrow {
        display: none !important;
      }

      .ant-select-selection-item {
        line-height: 15px !important;
      }

      .ant-select {
        margin-top: 0 !important;
        margin-left: 5px;
        margin-right: 0 !important;
      }
    }
  }

  .bannerTop {
    float: left;
    width: 100px;
    height: 40px;
    /* line-height: 60px; */
    margin: 8px 20px;
  }

  .bannerButton {
    float: left;
    cursor: pointer;
    //margin: 10px 0;
    color: #ffffff !important;
    font-size: 20px !important;
    border: none !important;
  }

  .bannerButton:hover {
    color: #ffffff !important;
    border: none !important;
  }

  .bannerButton:visited {
    color: #ffffff !important;
    border: none !important;
  }

  .bannerButton:active {
    color: #ffffff !important;
    border: none !important;
  }

  .bannerWorker {
    float: right;
    margin-top: 13px;
    margin-right: 24px;
    height: 32px;
    cursor: pointer;
  }

  .workTableBtn {
    float: right;
    margin-top: 13px;
    margin-right: 24px;
    height: 32px;
    cursor: pointer;
    border-radius: 4px;
    color: #8501bb;
    padding: 0 16px;
    border: 0.5px solid var(--, #f3f7fe);
    background: #fff;
    box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.1);

    .settingOutlined1 {
      width: 20px;
      float: left;
      margin-top: 7px;
      margin-right: 5px;
      cursor: pointer;
    }

    span {
      line-height: 32px;
      float: left;
    }
  }

  .workTableBtn1 {
    float: right;
    margin-top: 13px;
    margin-right: 24px;
    height: 32px;
    cursor: pointer;
    border-radius: 4px;
    color: #f22417;
    padding: 0 16px;
    border: 0.5px solid #f22417;
    background: linear-gradient(
        0deg,
        rgba(242, 36, 23, 0.1) 0%,
        rgba(242, 36, 23, 0.1) 100%
      ),
      #fff;
    box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.1);

    .settingOutlined1 {
      width: 20px;
      float: left;
      margin-top: 7px;
      margin-right: 5px;
      cursor: pointer;
    }

    span {
      line-height: 32px;
      float: left;
    }
  }

  .workTableBtn2 {
    float: right;
    margin-top: 13px;
    margin-right: 24px;
    height: 32px;
    cursor: pointer;
    border-radius: 4px;
    color: #e6a23c;
    padding: 0 16px;
    border: 0.5px solid #e6a23c;
    //background: linear-gradient(
    //  0deg,
    //  rgba(242, 36, 23, 0.1) 0%,
    //  rgba(242, 36, 23, 0.1) 100%
    //),
    //#fff;
    background: #fef1d6;
    box-shadow: 0px 0px 1px 0px rgba(0, 0, 0, 0.1);

    .settingOutlined1 {
      width: 20px;
      float: left;
      margin-top: 7px;
      margin-right: 5px;
      cursor: pointer;
    }

    span {
      line-height: 32px;
      float: left;
    }
  }

  .bannerSet {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #ffffff;
  }

  .headerContent {
    height: 60px;
    float: right;
    // width: 60%;

    .logout {
      float: right;
      height: 60px;
      cursor: pointer;
      margin-right: 50px;

      img {
        width: 16px;
        float: left;
        margin-top: 21px;
        margin-right: 3px;
      }

      span {
        line-height: 60px;
        font-size: 12px;
        float: left;
        font-family: 'Poppins', sans-serif;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.85);
      }
    }

    .line {
      width: 1px;
      height: 22px;
      background: rgba(255, 255, 255, 0.25);
      float: right;
      margin-top: 13px;
      margin-right: 16px;
    }

    .userName {
      float: right;
      height: 60px;
      cursor: pointer;
      margin-right: 16px;

      img {
        width: 24px;
        height: 24px;
        float: left;
        margin-top: 18px;
        margin-right: 8px;
        border-radius: 50%;
      }

      p {
        width: 60px;
        line-height: 60px;
        font-size: 12px;
        float: left;
        font-family: 'Poppins', sans-serif;
        font-weight: 400;
        color: rgba(255, 255, 255, 0.85);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .settingOutlined {
      width: 24px;
      float: right;
      margin-top: 18px;
      margin-right: 24px;
      cursor: pointer;
    }

    .languageIcon {
      width: 24px;
      float: right;
      margin-top: 18px;
      margin-right: 24px;
      cursor: pointer;
    }

    .attachmentIcon {
      margin-left: 20px;
      color: rgba(0, 0, 0, 0.45);
      margin-right: 10px;
    }

    :global {
      .ant-select:not(.ant-select-customize-input) .ant-select-selector {
        //background: #001d34 !important;
        //border-radius: 4px !important;
        //border: 1px solid rgba(0, 0, 0, 0.15) !important;
      }

      .ant-select {
        //color: rgba(255, 255, 255, 0.65);
        font-size: 12px;
        font-family: 'Poppins', sans-serif;
        font-weight: 400;
        width: 280px;
        float: right;
        margin-top: 14px;
        margin-right: 24px;
      }

      .ant-select-arrow {
        //color: rgba(255, 255, 255, 0.65);
      }
    }
  }

  .downLineContent {
    min-width: 25%;
    border-radius: 4px;
    border: 1px solid #fde2e2;
    background: #fef0f0;
    position: fixed;
    right: 10px;
    top: 70px;
    min-height: 50px;
    z-index: 5;
    padding: 15px;

    img {
      width: 16px;
      float: left;
      margin-top: 3px;
      margin-right: 3px;
    }

    span {
      float: left;
      color: #f22417;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
      width: 400px;
    }

    .closeIcon {
      width: 18px;
      float: right;
      margin-top: 2px;
      cursor: pointer;
      margin-left: 20px;
    }
  }

  .offLineContent {
    min-width: 25%;
    border-radius: 4px;
    border: 1px solid #faecd8;
    background: #fdf6ec;
    position: fixed;
    right: 10px;
    top: 70px;
    min-height: 50px;
    z-index: 5;
    padding: 15px;

    img {
      width: 16px;
      float: left;
      margin-top: 3px;
      margin-right: 3px;
    }

    span {
      float: left;
      color: #e6a23c;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 157.143% */
      width: 400px;
    }

    .closeIcon {
      width: 18px;
      float: right;
      margin-top: 2px;
      cursor: pointer;
      margin-left: 20px;
    }
  }

  .menuContent {
    width: 100%;
    float: left;
    margin-top: 10px;

    .selectStyle {
      background: #3463fc !important;
      border-radius: 10px !important;
      color: #fff !important;
      width: 60px !important;
      height: 60px !important;
      box-shadow: 0 2px 10px 0 #9ea2e8;

      svg path {
        fill: #fff;
      }
    }

    :global {
      li {
        // span {
        //   opacity: 0.6;
        // }

        span:hover {
          opacity: 1;
        }
      }

      // 未选中状态下svg图标置灰
      .ant-menu-item:not(.ant-menu-inline-collapsed),
      .ant-menu-submenu:not(.ant-menu-inline-collapsed) {
        svg {
          opacity: 0.6;
        }
      }

      // 选中状态下svg图标不置灰
      .ant-menu-item-selected:not(.ant-menu-inline-collapsed),
      .ant-menu-submenu-selected:not(.ant-menu-inline-collapsed) {
        svg {
          opacity: 1 !important;
        }
      }

      .ant-menu.ant-menu-inline-collapsed > .ant-menu-item {
        padding: 0;
      }

      .ant-menu {
        font-size: 14px;
        font-family: 'Poppins', sans-serif;
        font-weight: 400;
      }

      .ant-menu-item-icon + span {
        transition: none;
      }

      .ant-tabs-tab-active {
        li {
          span {
            opacity: 1 !important;
          }
        }
      }

      //收缩模式下样式
      .ant-menu-inline-collapsed {
        width: 100px !important;
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;

        .ant-menu-item,
        .ant-menu-submenu-title {
          transition: none !important;
        }

        .ant-menu-submenu,
        .ant-menu-submenu-inline {
          transition: none !important;
        }

        li {
          opacity: 1 !important;
          margin-bottom: 10px;
        }

        .ant-menu-item {
          height: 60px !important;
          width: 60px !important;

          svg {
            opacity: 1 !important;
          }

          .ant-menu-title-content {
            opacity: 0 !important;
          }
        }

        .ant-menu-submenu {
          width: 60px !important;
          height: 60px !important;
          display: flex;
          justify-content: center;

          .ant-menu-submenu-title {
            height: 60px !important;
            width: 60px !important;
            margin: 0;
          }
        }

        // .ant-menu-submenu-active {
        //   background: #3463fc !important;
        //   border-radius: 10px !important;
        //   color: #fff !important;
        //   width: 60px !important;
        //   height: 60px !important;
        //   box-shadow: 0 2px 10px 0 #9EA2E8;

        //   svg path {
        //     fill: #fff;
        //   }
        // }

        .ant-menu-submenu-selected {
          svg {
            opacity: 1 !important;
          }

          .ant-menu-submenu-title {
            background: #3463fc !important;
            border-radius: 10px !important;
            color: #fff !important;
            width: 60px !important;
            height: 60px !important;
            box-shadow: 0 2px 10px 0 #9ea2e8;

            svg path {
              fill: #fff;
            }
          }
        }

        .ant-menu-submenu {
          svg {
            opacity: 1 !important;
          }
        }

        .ant-menu-item-selected {
          // background: rgba(52, 99, 252, 0.15);
          background: #3463fc !important;
          border-radius: 10px !important;
          color: #fff !important;
          width: 60px !important;
          height: 60px !important;
          box-shadow: 0 2px 10px 0 #9ea2e8;

          svg {
            opacity: 1 !important;
          }

          svg path {
            fill: #fff;
          }
        }

        .ant-menu-item-icon {
          // width: 60px;
          // height: 30px;
          margin-top: 5px !important;
          // margin-left: -5px !important;
        }

        // .ant-menu-item-active {
        //   background: #3463fc !important;
        //   border-radius: 10px !important;
        //   color: #fff !important;
        //   width: 60px !important;
        //   height: 60px !important;
        //   box-shadow: 0 2px 10px 0 #9EA2E8;
        //   svg path {
        //     fill: #fff;
        //   }
        // }
        .ant-menu-item:focus {
          color: #fff !important;
        }

        .ant-menu-item:active {
          color: #fff !important;
        }

        .ant-menu-item:hover {
          background: #3463fc;
          color: #fff;
          width: 60px !important;
          height: 60px !important;
          box-shadow: 0 2px 10px 0 #9ea2e8;
          border-radius: 10px !important;

          svg path {
            fill: #fff;
          }
        }

        .ant-menu-submenu-title:hover {
          background: #3463fc;
          border-radius: 10px !important;
          color: #fff;
          width: 60px !important;
          height: 60px !important;
          box-shadow: 0 2px 10px 0 #9ea2e8;

          svg path {
            fill: #fff;
          }
        }
      }

      //展开下的样式

      .ant-menu-inline.ant-menu-root .ant-menu-item,
      .ant-menu-inline.ant-menu-root .ant-menu-submenu-title {
        transition: border-radius 0.5s, width 0.5s,
          height 0.5s cubic-bezier(0.215, 0.61, 0.355, 1);
      }

      // .ant-menu-vertical .ant-menu-item:not(:last-child),
      // .ant-menu-vertical-left .ant-menu-item:not(:last-child),
      // .ant-menu-vertical-right .ant-menu-item:not(:last-child),
      // .ant-menu-inline .ant-menu-item:not(:last-child) {
      //   margin-bottom: 0px;
      // }
      .ant-menu-vertical .ant-menu-item,
      .ant-menu-vertical .ant-menu-submenu-title {
        overflow: visible !important;
      }

      a:hover {
        color: #000;
      }

      .ant-menu-item::after {
        content: '';
        border: 0;
      }

      .ant-menu-item-active {
        background: rgba(52, 99, 252, 0.05);
        color: #000000;
      }

      .ant-menu-item:hover {
        color: #000000;
        background: rgba(52, 99, 252, 0.05);
      }

      .ant-menu-submenu-title:hover {
        color: #000000;
        background: rgba(52, 99, 252, 0.05);
      }

      .ant-menu-submenu-active {
        color: #000000;
      }

      .ant-menu-submenu-selected {
        opacity: 1 !important;
        color: #000000;

        .ant-menu-submenu-title {
          background: rgba(52, 99, 252, 0.15);
          width: 200px;
          opacity: 1;
          border-radius: 0 100px 100px 0;
          // background-image: url(../assets/Rectangle.png);
          // background-size: 160px 100%;
          // background-repeat: no-repeat;
          // opacity: 1;

          // border-radius: 0 100px 100px 0;
          color: #000000;

          // width: 160px;
          span {
            opacity: 1 !important;
          }
        }
      }

      .ant-menu-sub {
        background-color: #ffffff;

        .ant-tabs-left > .ant-tabs-nav .ant-tabs-tab,
        .ant-tabs-right > .ant-tabs-nav .ant-tabs-tab,
        .ant-tabs-left > div > .ant-tabs-nav .ant-tabs-tab,
        .ant-tabs-right > div > .ant-tabs-nav .ant-tabs-tab {
          padding: 0 0 0 0;
          text-align: center;
        }

        .ant-tabs-left > .ant-tabs-nav .ant-tabs-tab + .ant-tabs-tab,
        .ant-tabs-right > .ant-tabs-nav .ant-tabs-tab + .ant-tabs-tab,
        .ant-tabs-left > div > .ant-tabs-nav .ant-tabs-tab + .ant-tabs-tab,
        .ant-tabs-right > div > .ant-tabs-nav .ant-tabs-tab + .ant-tabs-tab {
          margin: 0;
        }

        .ant-tabs-tab-btn {
          width: 100%;
          text-align: left;
        }

        .ant-tabs-nav {
          width: 88%;

          a:hover {
            color: #3463fc !important;
          }
        }

        .ant-tabs-right {
          background: #ffffff;

          .ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
            background: #ffffff !important;
          }
        }

        // .ant-tabs-content-holder{
        //   display: none;
        // }
        .ant-tabs-content-holder {
          /* flex: auto; */
          width: 10%;
          min-width: 0;
          min-height: 0;
        }

        .ant-tabs-right > .ant-tabs-content-holder,
        .ant-tabs-right > div > .ant-tabs-content-holder {
          order: 0;
          margin-right: -1px;
          border-right: 2px solid rgba(52, 99, 252, 0.15);
        }

        .ant-menu-item-active {
          background-color: #ffffff;
        }

        .ant-menu-item-selected a,
        .ant-menu-item-selected a:hover {
          background: #ffffff !important;
          color: #000000;
        }

        li {
          padding-left: 12px !important;
          margin: 0;
        }
      }

      .ant-menu-submenu-arrow::before,
      .ant-menu-submenu-arrow::after {
        background-color: #000000 !important;
      }

      .ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
        background-color: #ffffff;
      }

      // .ant-menu-dark.ant-menu-dark:not(.ant-menu-horizontal)
      .ant-menu:not(.ant-menu-horizontal)
        .ant-menu-item-selected:not(.ant-menu-item-only-child) {
        // background: linear-gradient(90deg, #2370ff 0%, #5eb2ff 100%);
        background: rgba(52, 99, 252, 0.15);
        width: 200px;
        border-radius: 0 100px 100px 0;

        span {
          opacity: 1;
        }

        a {
          color: #000000;
        }
      }
    }
  }

  .helpIconContent {
    float: right;
    margin-right: 24px;

    img {
      width: 24px;
      height: 24px;
      float: right;
      margin-top: 18px;
      cursor: pointer;
    }
  }

  header {
    :global {
      .ant-select {
        font-size: 12px;
        height: 30px;
      }

      .ant-select-selector {
        height: 30px !important;
      }
    }
  }

  :global {
    .ant-layout {
      /* 隐藏滚动条 */
      scrollbar-width: none;
      /* firefox */
      -ms-overflow-style: none;
      background: linear-gradient(72deg, #f6f9ff 22.53%, #fdf6ff 100%), #f5f5f5;
    }

    ::-webkit-scrollbar {
      width: 0;
    }

    .ant-layout-sider {
      background-color: #ffffff;
      //background-image: url(../assets/Group.png);
      background-size: cover;
      z-index: 999;
      background-position: center;
      box-shadow: 2px 4px 30px 0px rgba(0, 0, 0, 0.03);
    }

    .ant-layout-sider-collapsed {
      max-width: 100px !important;
      min-width: 100px !important;
      flex: 0 0 100px !important;
      justify-content: center;
    }

    .ant-layout-header {
      background: #8501bb;
      //padding-left: 15px;
      height: 60px;
      min-height: 60px;
      padding: 0px 0px 0px 15px;
      display: inline;
    }

    .ant-breadcrumb li:last-child a {
      color: #fff !important;
    }

    .ant-breadcrumb li {
      color: #fff !important;
    }

    .ant-breadcrumb {
      float: left;
      font-size: 12px;
      font-family: 'Poppins', sans-serif;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.45);
    }

    .ant-breadcrumb-separator {
      color: rgba(255, 255, 255, 0.45);
    }

    .ant-breadcrumb > span:last-child {
      font-size: 12px;
      font-family: 'Poppins', sans-serif;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.65);
    }

    .ant-layout-content {
      //background: #f5f5f5;
      background: linear-gradient(72deg, #f6f9ff 22.53%, #fdf6ff 100%), #f5f5f5;
      // height: calc(100vh - 60px);
      clear: both;
      //padding-top: 24px;
    }

    .ant-table {
      font-size: 12px;
    }

    .ant-input {
      font-size: 12px;
      height: 30px;
    }

    .ant-checkbox-wrapper {
      font-size: 12px;
    }

    .ant-picker-input > input {
      font-size: 12px;
      height: 21px;
    }

    .ant-input-group .ant-input {
      font-size: 12px;
      height: 29px !important;
    }

    .ant-input-search-button {
      font-size: 12px;
      height: 29px;
    }

    .ant-btn {
      font-size: 12px;
    }

    .ant-form-item-explain-error {
      font-size: 12px;
    }
  }

  .homepageBeginnerGuide {
    width: 100%;
    height: 100%;
    position: fixed;
    z-index: 99999;
    background: rgba(0, 0, 0, 0.5);
    //display: none;

    .userBeginnerGuide {
      display: none;

      .userBannerImg {
        width: 40px;
        position: absolute;
        right: 180px;
        top: 8px;
      }

      .settingVerticalBeginnerGuideIcon {
        width: 100px;
        position: absolute;
        right: 195px;
        top: 60px;
      }

      .userDetailContent {
        width: 25%;
        min-height: 130px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        right: 115px;
        top: 180px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .settingBeginnerGuide {
      display: none;

      .settingBannerImg {
        width: 40px;
        position: absolute;
        right: 245px;
        top: 8px;
      }

      .settingVerticalBeginnerGuideIcon {
        width: 100px;
        position: absolute;
        right: 255px;
        top: 60px;
      }

      .settingDetailContent {
        width: 25%;
        min-height: 130px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        right: 370px;
        top: 115px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .platformUserManagementBeginnerGuide {
      display: none;

      .platformUserManagementIcon {
        width: 208px;
        height: 40px;
        position: absolute;
        background-image: url('../assets/Platform-User-Management.png');
        background-size: 100%;
        top: 220px;

        p {
          font-size: 14px;
          line-height: 40px;
          margin-left: 45px;
          color: #666;
        }
      }

      .transverseVerticalBeginnerGuideIcon {
        width: 150px;
        position: absolute;
        left: 210px;
        top: 235px;
      }

      .platformUserManagementContent {
        width: 25%;
        min-height: 120px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        left: 370px;
        top: 190px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .workOrderCenterBeginnerGuide {
      display: none;

      .workOrderCenterIcon {
        width: 208px;
        height: 40px;
        position: absolute;
        background-image: url('../assets/work-order-center.png');
        background-size: 100%;
        top: 130px;

        p {
          font-size: 14px;
          line-height: 40px;
          margin-left: 45px;
          color: #666;
        }
      }

      .transverseVerticalBeginnerGuideIcon {
        width: 150px;
        position: absolute;
        left: 210px;
        top: 145px;
      }

      .workOrderCenterContent {
        width: 25%;
        min-height: 120px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        left: 370px;
        top: 110px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .customerCenterBeginnerGuide {
      display: none;

      .customerCenterIcon {
        width: 208px;
        height: 40px;
        background-image: url('../assets/work-order-center.png');
        background-size: 100%;
        position: absolute;
        top: 170px;

        p {
          font-size: 14px;
          line-height: 40px;
          margin-left: 45px;
          color: #666;
          margin-bottom: 0px;
        }
      }

      .transverseVerticalBeginnerGuideIcon {
        width: 150px;
        position: absolute;
        left: 210px;
        top: 185px;
      }

      .customerCenterContent {
        width: 25%;
        min-height: 120px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        left: 370px;
        top: 150px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .knowledgeBeginnerGuide {
      display: none;

      .knowledgeCenterIcon {
        width: 208px;
        height: 40px;
        background-image: url('../assets/knowledge-icon.png');
        background-size: 100%;
        position: absolute;
        top: 260px;

        p {
          font-size: 14px;
          line-height: 40px;
          margin-left: 45px;
          color: #666;
          margin-bottom: 0px;
        }
      }

      .transverseVerticalBeginnerGuideIcon {
        width: 150px;
        position: absolute;
        left: 210px;
        top: 270px;
      }

      .knowledgeContent {
        width: 25%;
        min-height: 120px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        left: 370px;
        top: 240px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .collapsedPlatformUserManagementBeginnerGuide {
      //display: none;
      .collapsedPlatformUserManagementIcon {
        width: 80px;
        height: 80px;
        position: absolute;
        background-image: url('../assets/collapsed-user-management.png');
        background-size: 100%;
        top: 275px;
        left: 10px;

        p {
          font-size: 12px;
          color: #fff;
          text-align: center;
          margin-bottom: 0px;
          margin-top: 46px;
        }
      }

      .transverseVerticalBeginnerGuideIcon {
        width: 150px;
        position: absolute;
        left: 90px;
        top: 295px;
      }

      .collapsedPlatformUserManagementContent {
        width: 25%;
        min-height: 120px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        left: 260px;
        top: 270px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .collapsedWorkOrderCenterBeginnerGuide {
      //display: none;
      .workOrderCenterIcon {
        width: 80px;
        height: 80px;
        position: absolute;
        background-image: url('../assets/collapsed-work-order-center.png');
        background-size: 100%;
        top: 135px;
        left: 10px;

        p {
          font-size: 12px;
          color: #fff;
          text-align: center;
          margin-bottom: 0px;
          margin-top: 46px;
        }
      }

      .transverseVerticalBeginnerGuideIcon {
        width: 150px;
        position: absolute;
        left: 90px;
        top: 165px;
      }

      .workOrderCenterContent {
        width: 25%;
        min-height: 120px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        left: 260px;
        top: 130px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .collapsedCustomerCenterBeginnerGuide {
      //display: none;
      .customerCenterIcon {
        width: 80px;
        height: 80px;
        background-image: url('../assets/collapsed-custom-info.png');
        background-size: 100%;
        position: absolute;
        top: 205px;
        left: 10px;

        p {
          font-size: 12px;
          color: #fff;
          text-align: center;
          margin-bottom: 0px;
          margin-top: 46px;
        }
      }

      .transverseVerticalBeginnerGuideIcon {
        width: 150px;
        position: absolute;
        left: 90px;
        top: 235px;
      }

      .customerCenterContent {
        width: 25%;
        min-height: 120px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        left: 260px;
        top: 200px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .collapsedKnowledgeBeginnerGuide {
      //display: none;
      .knowledgeCenterIcon {
        width: 80px;
        height: 80px;
        background-image: url('../assets/collapsed-knowledge.png');
        background-size: 100%;
        position: absolute;
        top: 350px;
        left: 10px;

        p {
          font-size: 12px;
          color: #fff;
          margin-bottom: 0px;
          text-align: center;
          margin-bottom: 0px;
          margin-top: 46px;
        }
      }

      .transverseVerticalBeginnerGuideIcon {
        width: 150px;
        position: absolute;
        left: 90px;
        top: 370px;
      }

      .knowledgeContent {
        width: 25%;
        min-height: 120px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        left: 260px;
        top: 340px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    :global {
      .ant-checkbox-wrapper {
        color: #fff;
        margin-top: 3px;
      }

      .ant-checkbox-checked .ant-checkbox-inner {
        background-color: #fff !important;
        border-color: #fff !important;
      }

      .ant-checkbox-checked::after {
        border-color: #fff !important;
      }

      .ant-checkbox-checked .ant-checkbox-inner::after {
        border: 2px solid #3463fc;
        border-top: 0;
        border-left: 0;
      }
    }
  }

  .helpBeginnerGuideMask {
    width: 100%;
    height: 100%;
    position: fixed;
    z-index: 99999;
    background: rgba(0, 0, 0, 0.5);
    display: none;
  }

  .helpBeginnerGuide {
    z-index: 999999;

    //display: none;
    .helpBannerImg {
      width: 24px;
      position: absolute;
      //right: 165px;
      top: 18px;
    }

    .settingVerticalBeginnerGuideIcon {
      width: 120px;
      position: absolute;
      right: 180px;
      top: 50px;
    }

    .helpDetailContent {
      width: 25%;
      min-height: 100px;
      padding: 10px;
      border-radius: 4px;
      background: #3463fc;
      position: absolute;
      right: 100px;
      top: 170px;

      .beginnerGuideContent {
        color: #fff;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
      }

      .beginnerGuideFooter {
        width: 100%;
        height: 32px;

        .nextBtn {
          float: right;
        }

        .jumpBeginnerGuide {
          float: right;
          color: rgba(255, 255, 255, 0.8);
          font-size: 12px;
          font-style: normal;
          line-height: 32px;
          margin-right: 12px;
          cursor: pointer;
        }
      }
    }
  }

  .customerServiceSupervisorBeginnerGuide {
    width: 100%;
    height: 100%;
    position: fixed;
    z-index: 99999;
    background: rgba(0, 0, 0, 0.5);
    //display: none;

    .helpBeginnerGuide {
      //display: none;
      .helpBannerImg {
        width: 24px;
        position: absolute;
        //right: 165px;
        top: 18px;
      }

      .settingVerticalBeginnerGuideIcon {
        width: 100px;
        position: absolute;
        right: 180px;
        top: 50px;
      }

      .helpDetailContent {
        width: 25%;
        min-height: 100px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        right: 100px;
        top: 170px;

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .userBeginnerGuide {
      display: none;

      .userBannerImg {
        width: 40px;
        position: absolute;
        right: 180px;
        top: 8px;
      }

      .settingVerticalBeginnerGuideIcon {
        width: 100px;
        position: absolute;
        right: 195px;
        top: 60px;
      }

      .userDetailContent {
        width: 25%;
        min-height: 130px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        right: 115px;
        top: 180px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .workOrderCenterBeginnerGuide {
      display: none;

      .workOrderCenterIcon {
        width: 208px;
        height: 40px;
        position: absolute;
        background-image: url('../assets/work-order-center.png');
        background-size: 100%;
        top: 130px;

        p {
          font-size: 14px;
          line-height: 40px;
          margin-left: 45px;
          color: #666;
        }
      }

      .transverseVerticalBeginnerGuideIcon {
        width: 150px;
        position: absolute;
        left: 210px;
        top: 145px;
      }

      .workOrderCenterContent {
        width: 25%;
        min-height: 120px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        left: 370px;
        top: 110px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .customerCenterBeginnerGuide {
      display: none;

      .customerCenterIcon {
        width: 208px;
        height: 40px;
        background-image: url('../assets/work-order-center.png');
        background-size: 100%;
        position: absolute;
        top: 170px;

        p {
          font-size: 14px;
          line-height: 40px;
          margin-left: 45px;
          color: #666;
          margin-bottom: 0px;
        }
      }

      .transverseVerticalBeginnerGuideIcon {
        width: 150px;
        position: absolute;
        left: 210px;
        top: 185px;
      }

      .customerCenterContent {
        width: 25%;
        min-height: 120px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        left: 370px;
        top: 150px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .customerServiceManagingBeginnerGuide {
      display: none;

      .customerServiceManagingIcon {
        width: 208px;
        height: 40px;
        position: absolute;
        background-image: url('../assets/customer-service-management.png');
        background-size: 100%;
        top: 220px;

        p {
          font-size: 14px;
          line-height: 40px;
          margin-left: 45px;
          color: #666;
        }
      }

      .transverseVerticalBeginnerGuideIcon {
        width: 150px;
        position: absolute;
        left: 210px;
        top: 235px;
      }

      .customerServiceManagingContent {
        width: 25%;
        min-height: 120px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        left: 370px;
        top: 190px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .knowledgeBeginnerGuide {
      display: none;

      .knowledgeCenterIcon {
        width: 208px;
        height: 40px;
        background-image: url('../assets/knowledge-icon.png');
        background-size: 100%;
        position: absolute;
        top: 260px;

        p {
          font-size: 14px;
          line-height: 40px;
          margin-left: 45px;
          color: #666;
          margin-bottom: 0px;
        }
      }

      .transverseVerticalBeginnerGuideIcon {
        width: 150px;
        position: absolute;
        left: 210px;
        top: 270px;
      }

      .knowledgeContent {
        width: 25%;
        min-height: 120px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        left: 370px;
        top: 240px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .collapsedCustomerServiceManagingBeginnerGuide {
      //display: none;
      .collapsedCustomerServiceManagingIcon {
        width: 80px;
        height: 80px;
        position: absolute;
        background-image: url('../assets/collapsed-customer-service.png');
        background-size: 100%;
        top: 275px;
        left: 10px;

        p {
          font-size: 12px;
          color: #fff;
          text-align: center;
          margin-bottom: 0px;
          margin-top: 46px;
        }
      }

      .transverseVerticalBeginnerGuideIcon {
        width: 150px;
        position: absolute;
        left: 90px;
        top: 295px;
      }

      .collapsedCustomerServiceManagingContent {
        width: 25%;
        min-height: 120px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        left: 260px;
        top: 270px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .collapsedWorkOrderCenterBeginnerGuide {
      //display: none;
      .workOrderCenterIcon {
        width: 80px;
        height: 80px;
        position: absolute;
        background-image: url('../assets/collapsed-work-order-center.png');
        background-size: 100%;
        top: 135px;
        left: 10px;

        p {
          font-size: 12px;
          color: #fff;
          text-align: center;
          margin-bottom: 0px;
          margin-top: 46px;
        }
      }

      .transverseVerticalBeginnerGuideIcon {
        width: 150px;
        position: absolute;
        left: 90px;
        top: 165px;
      }

      .workOrderCenterContent {
        width: 25%;
        min-height: 120px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        left: 260px;
        top: 130px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .collapsedCustomerCenterBeginnerGuide {
      //display: none;
      .customerCenterIcon {
        width: 80px;
        height: 80px;
        background-image: url('../assets/collapsed-custom-info.png');
        background-size: 100%;
        position: absolute;
        top: 205px;
        left: 10px;

        p {
          font-size: 12px;
          color: #fff;
          text-align: center;
          margin-bottom: 0px;
          margin-top: 46px;
        }
      }

      .transverseVerticalBeginnerGuideIcon {
        width: 150px;
        position: absolute;
        left: 90px;
        top: 235px;
      }

      .customerCenterContent {
        width: 25%;
        min-height: 120px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        left: 260px;
        top: 200px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .collapsedKnowledgeBeginnerGuide {
      //display: none;
      .knowledgeCenterIcon {
        width: 80px;
        height: 80px;
        background-image: url('../assets/collapsed-knowledge.png');
        background-size: 100%;
        position: absolute;
        top: 350px;
        left: 10px;

        p {
          font-size: 12px;
          color: #fff;
          margin-bottom: 0px;
          text-align: center;
          margin-bottom: 0px;
          margin-top: 46px;
        }
      }

      .transverseVerticalBeginnerGuideIcon {
        width: 150px;
        position: absolute;
        left: 90px;
        top: 370px;
      }

      .knowledgeContent {
        width: 25%;
        min-height: 120px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        left: 260px;
        top: 340px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    :global {
      .ant-checkbox-wrapper {
        color: #fff;
        margin-top: 3px;
      }

      .ant-checkbox-checked .ant-checkbox-inner {
        background-color: #fff !important;
        border-color: #fff !important;
      }

      .ant-checkbox-checked::after {
        border-color: #fff !important;
      }

      .ant-checkbox-checked .ant-checkbox-inner::after {
        border: 2px solid #3463fc;
        border-top: 0;
        border-left: 0;
      }
    }
  }

  .customerServiceBeginnerGuide {
    width: 100%;
    height: 100%;
    position: fixed;
    z-index: 99999;
    background: rgba(0, 0, 0, 0.5);
    //display: none !important;

    .helpBeginnerGuide {
      //display: none;
      .helpBannerImg {
        width: 24px;
        position: absolute;
        //right: 165px;
        top: 18px;
      }

      .settingVerticalBeginnerGuideIcon {
        width: 100px;
        position: absolute;
        right: 180px;
        top: 50px;
      }

      .helpDetailContent {
        width: 25%;
        min-height: 100px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        right: 100px;
        top: 170px;

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .userBeginnerGuide {
      //display: none;
      .userBannerImg {
        width: 40px;
        position: absolute;
        right: 165px;
        top: 8px;
      }

      .settingVerticalBeginnerGuideIcon {
        width: 100px;
        position: absolute;
        right: 180px;
        top: 60px;
      }

      .userDetailContent {
        width: 25%;
        min-height: 130px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        right: 100px;
        top: 180px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .workOrderCenterBeginnerGuide {
      //display: none;
      .workOrderCenterIcon {
        width: 208px;
        height: 40px;
        position: absolute;
        background-image: url('../assets/work-order-center.png');
        background-size: 100%;
        top: 130px;

        p {
          font-size: 14px;
          line-height: 40px;
          margin-left: 45px;
          color: #666;
        }
      }

      .transverseVerticalBeginnerGuideIcon {
        width: 150px;
        position: absolute;
        left: 210px;
        top: 145px;
      }

      .workOrderCenterContent {
        width: 25%;
        min-height: 120px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        left: 370px;
        top: 110px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .customerCenterBeginnerGuide {
      //display: none;
      .customerCenterIcon {
        width: 208px;
        height: 40px;
        background-image: url('../assets/customer-service-management.png');
        background-size: 100%;
        position: absolute;
        top: 170px;

        p {
          font-size: 14px;
          line-height: 40px;
          margin-left: 45px;
          color: #666;
          margin-bottom: 0px;
        }
      }

      .transverseVerticalBeginnerGuideIcon {
        width: 150px;
        position: absolute;
        left: 210px;
        top: 185px;
      }

      .customerCenterContent {
        width: 25%;
        min-height: 120px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        left: 370px;
        top: 150px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .knowledgeBeginnerGuide {
      //display: none;
      .knowledgeCenterIcon {
        width: 208px;
        height: 40px;
        background-image: url('../assets/knowledge-icon.png');
        background-size: 100%;
        position: absolute;
        top: 220px;

        p {
          font-size: 14px;
          line-height: 40px;
          margin-left: 45px;
          color: #666;
          margin-bottom: 0px;
        }
      }

      .transverseVerticalBeginnerGuideIcon {
        width: 150px;
        position: absolute;
        left: 210px;
        top: 235px;
      }

      .knowledgeContent {
        width: 25%;
        min-height: 120px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        left: 370px;
        top: 190px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .workspaceBeginnerGuide {
      //display: none;
      .workspaceIcon {
        width: 130px;
        height: 32px;
        position: absolute;
        background-image: url('../assets/workspace-icon.png');
        background-size: 100%;
        top: 12px;
        right: 265px;

        p {
          font-size: 12px;
          line-height: 34px;
          margin-left: 35px;
          margin-bottom: 0px;
          color: #8501bb;
          font-style: normal;
          font-weight: 400;
        }
      }

      .settingVerticalBeginnerGuideIcon {
        width: 100px;
        position: absolute;
        right: 360px;
        top: 60px;
      }

      .workspaceContent {
        width: 25%;
        min-height: 130px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        right: 270px;
        top: 180px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .collapsedWorkOrderCenterBeginnerGuide {
      //display: none;
      .workOrderCenterIcon {
        width: 80px;
        height: 80px;
        position: absolute;
        background-image: url('../assets/collapsed-work-order-center.png');
        background-size: 100%;
        top: 135px;
        left: 10px;

        p {
          font-size: 12px;
          color: #fff;
          text-align: center;
          margin-bottom: 0px;
          margin-top: 46px;
        }
      }

      .transverseVerticalBeginnerGuideIcon {
        width: 150px;
        position: absolute;
        left: 90px;
        top: 165px;
      }

      .workOrderCenterContent {
        width: 25%;
        min-height: 120px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        left: 260px;
        top: 130px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .collapsedCustomerCenterBeginnerGuide {
      //display: none;
      .customerCenterIcon {
        width: 80px;
        height: 80px;
        background-image: url('../assets/collapsed-custom-info.png');
        background-size: 100%;
        position: absolute;
        top: 205px;
        left: 10px;

        p {
          font-size: 12px;
          color: #fff;
          text-align: center;
          margin-bottom: 0px;
          margin-top: 46px;
        }
      }

      .transverseVerticalBeginnerGuideIcon {
        width: 150px;
        position: absolute;
        left: 90px;
        top: 235px;
      }

      .customerCenterContent {
        width: 25%;
        min-height: 120px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        left: 260px;
        top: 200px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    .collapsedKnowledgeBeginnerGuide {
      //display: none;
      .knowledgeCenterIcon {
        width: 80px;
        height: 80px;
        background-image: url('../assets/collapsed-knowledge.png');
        background-size: 100%;
        position: absolute;
        top: 275px;
        left: 10px;

        p {
          font-size: 12px;
          color: #fff;
          margin-bottom: 0px;
          text-align: center;
          margin-bottom: 0px;
          margin-top: 46px;
        }
      }

      .transverseVerticalBeginnerGuideIcon {
        width: 150px;
        position: absolute;
        left: 90px;
        top: 300px;
      }

      .knowledgeContent {
        width: 25%;
        min-height: 120px;
        padding: 10px;
        border-radius: 4px;
        background: #3463fc;
        position: absolute;
        left: 260px;
        top: 280px;

        .beginnerGuideTitle {
          color: #fff;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideContent {
          color: #fff;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }

        .beginnerGuideFooter {
          width: 100%;
          height: 32px;

          .nextBtn {
            float: right;
          }

          .jumpBeginnerGuide {
            float: right;
            color: rgba(255, 255, 255, 0.8);
            font-size: 12px;
            font-style: normal;
            line-height: 32px;
            margin-right: 12px;
            cursor: pointer;
          }
        }
      }
    }

    :global {
      .ant-checkbox-wrapper {
        color: #fff;
        margin-top: 3px;
      }

      .ant-checkbox-checked .ant-checkbox-inner {
        background-color: #fff !important;
        border-color: #fff !important;
      }

      .ant-checkbox-checked::after {
        border-color: #fff !important;
      }

      .ant-checkbox-checked .ant-checkbox-inner::after {
        border: 2px solid #3463fc;
        border-top: 0;
        border-left: 0;
      }
    }
  }
}

:global {
  .ant-menu-submenu-placement-rightTop {
    // padding-top:8px;
    // padding-bottom:8px;
    // background: rgba(52, 99, 252, 0.15);
  }

  .ant-menu-sub {
    // background: rgba(52, 99, 252, 0.15);
    .ant-tabs-left > .ant-tabs-nav .ant-tabs-tab,
    .ant-tabs-right > .ant-tabs-nav .ant-tabs-tab,
    .ant-tabs-left > div > .ant-tabs-nav .ant-tabs-tab,
    .ant-tabs-right > div > .ant-tabs-nav .ant-tabs-tab {
      padding: 0 0 0 0;
      text-align: center;
    }

    .ant-tabs-left > .ant-tabs-nav .ant-tabs-tab + .ant-tabs-tab,
    .ant-tabs-right > .ant-tabs-nav .ant-tabs-tab + .ant-tabs-tab,
    .ant-tabs-left > div > .ant-tabs-nav .ant-tabs-tab + .ant-tabs-tab,
    .ant-tabs-right > div > .ant-tabs-nav .ant-tabs-tab + .ant-tabs-tab {
      margin: 0;
    }

    .ant-tabs-tab-btn {
      width: 100%;
      text-align: left;
    }

    .ant-tabs-nav {
      width: 95%;
    }

    .ant-tabs-right {
      // background: rgba(52, 99, 252, 0.15);
      background-color: #f9faff;
    }

    // .ant-tabs-content-holder{
    //   display: none;
    // }
    .ant-tabs-content-holder {
      /* flex: auto; */
      // width: 10%;
      min-width: 20px !important;
      min-height: 0;
    }

    .ant-tabs-right > .ant-tabs-content-holder,
    .ant-tabs-right > div > .ant-tabs-content-holder {
      order: 0;
      margin-right: -1px;
      border-right: 2px solid rgba(52, 99, 252, 0.15);
    }

    // .ant-menu-item-active {
    //   background: rgba(52, 99, 252, 0.15);
    // }
    .ant-menu-item-selected a,
    .ant-menu-item-selected a:hover {
      color: #000000;
    }

    li {
      padding-left: 12px !important;
      margin: 0;
    }
  }

  .ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
    background: none;
  }

  .ant-layout-sider-children {
    overflow-y: scroll !important;
  }
}

.draggableBox {
  img {
    -webkit-user-drag: none;
    /* Chrome 和 Safari */
    -webkit-user-select: none;
    /* Chrome 和 Safari */
    user-select: none;
    cursor: grab;
  }
}
