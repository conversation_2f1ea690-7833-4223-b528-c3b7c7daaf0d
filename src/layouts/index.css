.settingOutlined {
  width: 20px;
  float: left;
  margin-top: 5px;
  margin-right: 5px;
  cursor: pointer;
  /* 定义容器内的图片的CSS属性 */

  animation: scale 0.5s infinite;
}
/* 定义动画循环和播放顺序 */

@keyframes scale {

  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }

  100% {

    transform: scale(1);
  }
}



.settingVerticalBeginnerGuideIcon {
  width: 100px;
  position: absolute;
  right: 195px;
  top: 60px;

  animation-name: example;
  animation-duration: 1s;
  animation-delay: 1s;
}

@keyframes example {
  from {width: 100px;}
  to {width:0px;height:0px;right:242px;}
}


.helpDetailContent {
  width: 25%;
  min-height: 50px;
  padding: 10px;
  border-radius: 4px;
  background: #3463fc;
  position: absolute;
  right: 100px;
  top: 170px;

  animation-name: example1;
  animation-duration: 1s;
  animation-delay: 1s;
}

@keyframes example1 {
  /*from {width: 25%;top: 170px;min-height: 100px;}*/
  /*to {width:0%;top:60px;min-height:0px;right:242px;}*/
  from {top: 170px;opacity:1;}
  to {top:60px;right:80px;opacity:0;}
}

.beginnerGuideContent {
  color: #fff;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  margin-bottom: 0px;
  /*line-height: 50px;*/
}

.beginnerGuideFooter {
  width: 100%;
  height: 32px;
}

.nextBtn {
  float: right;
}

.jumpBeginnerGuide {
  float: right;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  font-style: normal;
  line-height: 32px;
  margin-right: 12px;
  cursor: pointer;
}

.settingVerticalBeginnerGuideIcon {
  width: 120px;
  position: absolute;
  right: 180px;
  top: 50px;
}
