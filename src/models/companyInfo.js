import { updateCompanyConfig, getCompanyInfo } from '@/service/companyInfo';

export default {
  namespace: 'companyInfo',
  state: {
    companyInfo: {},
  },
  effects: {
    *updateCompanyConfig({ payload, callback }, { call, put }) {
      let response = yield call(updateCompanyConfig, payload);
      if (response) {
        callback(response);
      }
    },
    *getCompanyInfo({ payload, callback }, { call, put }) {
      let response = yield call(getCompanyInfo, payload);
      if (response) {
        callback(response);
      }
    },
  },
};
