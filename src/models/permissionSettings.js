import {
  companyPermission,
  saveOrUpdateCompanyPermission,
} from '../service/permissionSettings';
export default {
  namespace: 'permissionSettings',
  state: {},
  effects: {
    *queryCompanyPermission({ callback }, { call, put }) {
      let response = yield call(companyPermission);
      if (response) {
        callback(response);
      }
    },
    *saveOrUpdateCompanyPermission({ payload, callback }, { call, put }) {
      let response = yield call(saveOrUpdateCompanyPermission, payload);
      if (response) {
        callback(response);
      }
    },
  },
  reducers: {},
};
