import {
  getArticleList,
  getHotArticleList,
  getArticleDetail,
} from '@/service/blog';

export default {
  namespace: 'blog',
  state: {
    articleList: [],
  },
  effects: {
    *getArticleList({ payload, callback }, { call, put }) {
      let response = yield call(getArticleList, payload);
      if (response) {
        callback(response);
      }
    },
    *getHotArticleList({ payload, callback }, { call, put }) {
      let response = yield call(getHotArticleList, payload);
      if (response) {
        callback(response);
      }
    },
    *getArticleDetail({ payload, callback }, { call, put }) {
      let response = yield call(getArticleDetail, payload);
      if (response) {
        callback(response);
      }
    },
  },
};
