import {
  queryTiktokMarketRegion,
  queryTiktokShopInfo,
  getTikTokOrderList,
  checkShopInfoExist,
} from '@/service/tiktokRegionConfiguration';
export default {
  namespace: 'tiktokRegionConfiguration',
  state: {},
  effects: {
    *queryTiktokMarketRegion({ payload, callback }, { call, put }) {
      let response = yield call(queryTiktokMarketRegion, payload);
      if (response) {
        callback(response);
      }
    },
    *queryTiktokShopInfo({ payload, callback }, { call, put }) {
      let response = yield call(queryTiktokShopInfo, payload);
      if (response) {
        callback(response);
      }
    },

    *getTikTokOrderList({ payload, callback }, { call, put }) {
      let response = yield call(getTikTokOrderList, payload);
      if (response) {
        callback(response);
      }
    },
    *checkShopInfoExist({ payload, callback }, { call, put }) {
      let response = yield call(checkShopInfoExist, payload);
      if (response) {
        callback(response);
      }
    },
  },
  reducers: {},
};
