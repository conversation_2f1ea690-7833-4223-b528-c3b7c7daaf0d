import {
  menuList,
  getUser,
  logout,
  queryAllConnectList,
  getDeptList,
  saveSelectedConnect,
  getSelectedConnect,
  setMenuStatus,
  getMenuStatus,
  getSingWeChat,
  updateWelcomeRemind,
  queryRemindFlag,
  queryWorkOrderIdByContactId,
} from '@/service/layouts';
import React from 'react';
import UserAvatar from '../../assets/user-avatar.png';
import { channelType } from '@/service/channel';

export default {
  namespace: 'layouts',
  state: {
    menuList: [],
    user: {},
    userAvatar: UserAvatar,
    menuIdList: [],
    connectList: [],
    selectedConnect: {},
    deptList: [],
    channelTypeList: [],
    allChannelInitList: [],
    showWorkTable: false,
    phoneNumber: '',
    logoutFlag: false,
    roleId: '',
    auth: {},
    authInit: {},
    agentStatus: '', // 座席状态
    agentStatusName: '', //坐席状态文字
    agentStatusList: [], //坐席状态列表
    websocketStatus: false, //socket状态
    reconnect: false,
    privateUserId: '',
    allowOnlineCountFlag: true,
    currentSettingTab: '1',
  },
  effects: {
    // 获取账号权限规则
    *getUserRules({ payload, callback }, { call, put }) {
      let response = yield call(getUser, payload);
      if (response) {
        callback(response);
        let { code, data, msg } = response;
        let permissions = {};
        data?.permissions?.forEach(item => {
          permissions[item.perms] = item.displayStatus;
        });
        //存储在sessionStorage防止页面刷新，存储并区分账号权限
        sessionStorage.setItem('permissions', JSON.stringify(permissions));
        if (200 === code) {
          yield put({
            type: 'updateUser',
            user: data,
          });
          yield put({
            type: 'saveAuth',
            auth: permissions,
          });
          yield put({
            type: 'saveAuthInit',
            authInit: permissions,
          });
        }
      }
    },

    *setAgentStatus({ payload }, { call, put }) {
      localStorage.setItem('agentStatus', payload);
      yield put({
        type: 'saveState',
        payload: {
          agentStatus: payload,
        },
      });
    },
    *setAgentStatusName({ payload }, { call, put }) {
      localStorage.setItem('agentStatusName', payload);
      yield put({
        type: 'saveState',
        payload: {
          agentStatusName: payload,
        },
      });
    },
    *setAgentStatusList({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          agentStatusList: payload,
        },
      });
    },
    *setReconnect({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          reconnect: payload,
        },
      });
    },
    *setWebsocketStatusNewAw({ payload }, { call, put }) {
      localStorage.setItem('websocketStatus', payload);
      yield put({
        type: 'saveState',
        payload: {
          websocketStatus: payload,
        },
      });
    },
    //达到上线人数不允许上线
    *setAllowOnlineCount({ payload }, { call, put }) {
      localStorage.setItem('allowOnlineCountFlag', payload);
      yield put({
        type: 'saveState',
        payload: {
          allowOnlineCountFlag: payload,
        },
      });
    },
    *setUserAvatar({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          userAvatar: payload,
        },
      });
    },
    *setCurrentSettingTab({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          currentSettingTab: payload,
        },
      });
    },
    /**
     * 左侧树目录
     * @param payload
     * @param call
     * @param put
     * @returns {Generator<*, void, *>}
     */
    *menuList({ payload }, { call, put }) {
      let response = yield call(menuList, payload);
      if (response) {
        let { code, data, msg } = response;
        if (200 === code) {
          yield put({
            type: 'updateMenuList',
            menuList: data,
            menuIdList: data.map(item => {
              return item.menuId;
            }),
          });
        }
      }
    },
    /**
     * 当前登录人的详细信息
     * @param payload
     * @param call
     * @param put
     * @returns {Generator<*, void, *>}
     */
    *getUser({ payload }, { call, put }) {
      let response = yield call(getUser, payload);
      if (response) {
        let { code, data, msg } = response;
        // 保存创建工单使用的userId
        yield put({
          type: 'saveState',
          privateUserId: data.userId,
        });
        localStorage.setItem('privateUserId', data.userId);
        let permissions = {};
        data?.permissions?.forEach(item => {
          permissions[item.perms] = item.displayStatus;
        });
        //存储在sessionStorage防止页面刷新，存储并区分账号权限
        // sessionStorage.setItem('permissions', JSON.stringify(permissions));
        if (200 === code) {
          yield put({
            type: 'updateUser',
            user: data,
          });
          // yield put({
          //   type: 'saveAuth',
          //   auth: permissions,
          // });
          // yield put({
          //   type: 'saveAuthInit',
          //   authInit: permissions,
          // });
        }
      }
    },
    *getUser1({ payload, callback }, { call, put }) {
      let response = yield call(getUser, payload);
      if (response) {
        let { code, data, msg } = response;
        if (200 === code) {
          callback(response);
        }
      }
    },
    *getOrSetUser({ payload, callback }, { call, put }) {
      let response = yield call(getUser, payload);
      if (response) {
        let { code, data, msg } = response;
        if (200 === code) {
          yield put({
            type: 'updateRoleId',
            roleId: data?.roleList?.[0]?.roleId,
          });
          callback(response);
        }
      }
    },
    *setMenuStatus({ payload, callback }, { call, put }) {
      let response = yield call(setMenuStatus, payload);
      if (response) {
        let { code, data, msg } = response;
        if (200 === code) {
          callback(response);
        }
      }
    },
    *getMenuStatus({ payload, callback }, { call, put }) {
      let response = yield call(getMenuStatus, payload);
      if (response) {
        let { code, data, msg } = response;
        if (200 === code) {
          callback(response);
        }
      }
    },
    /**
     * 退出
     * @param payload
     * @param call
     * @param put
     * @returns {Generator<*, void, *>}
     */
    *logout({ payload, callback }, { call, put }) {
      let response = yield call(logout);
      if (response) {
        if (200 === response.code) {
          callback();
          yield put({
            type: 'updateLogout',
            payload: true,
          });
        }
      }
    },
    /**
     * 获取当前登录人所在公司的connect list
     * @param payload
     * @param call
     * @param put
     * @returns {Generator<*, void, *>}
     */
    *getAllConnectList({ payload }, { call, put }) {
      let response = yield call(queryAllConnectList, payload);
      if (response) {
        let { code, data, msg } = response;
        if (200 === code) {
          let connectList;
          if (data) {
            connectList = data.map(item => ({
              value: JSON.stringify(item), // 这里的对象必须序列化
              label: item.connectAlias,
            }));
          } else {
            connectList = [];
          }

          yield put({
            type: 'updateConnectList',
            payload: connectList,
          });
        }
      }
    },
    *saveSelectedConnect({ payload }, { call, put }) {
      let response = yield call(saveSelectedConnect, payload);
    },
    *getSelectedConnect({ payload }, { call, put }) {
      let response = yield call(getSelectedConnect, payload);
      if (response) {
        let { code, data } = response;
        if (200 === code) {
          if (!data) {
            data = {};
          }
          yield put({
            type: 'updateConnect',
            payload: data,
          });
        }
      }
    },
    *deptList({ payload }, { call, put }) {
      let response = yield call(getDeptList, payload);
      if (response) {
        let { code, data, msg } = response;
        if (200 === code) {
          yield put({
            type: 'updateDeptList',
            payload: data,
          });
        }
      }
    },
    *getChannelType({ payload, callback }, { call, put, select }) {
      let response = yield call(channelType, payload);
      if (response) {
        let { code, data, msg } = response;
        if (200 === code) {
          yield put({
            type: 'updateChannelTypeList',
            payload: data,
          });
          yield put({
            type: 'updateAllChannelInitList',
            payload: data.map(item => ({
              label: item.name,
              value: item.code,
            })),
          });
        }
      }
    },
    *workerTablePush({ payload, callback }, { call, put, select }) {
      yield put({
        type: 'saveState',
        payload: {
          showWorkTable: payload,
        },
      });
    },
    *getSingWeChat({ payload, callback }, { call, put, select }) {
      let response = yield call(getSingWeChat, payload);
      if (response) {
        callback(response);
      }
    },

    *setPhoneNumber({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          phoneNumber: payload,
        },
      });
    },
    *updateWelcomeRemind({ payload, callback }, { call, put }) {
      let response = yield call(updateWelcomeRemind, payload);
      if (response) {
        callback(response);
      }
    },
    *queryRemindFlag({ callback }, { call, put }) {
      let response = yield call(queryRemindFlag);
      if (response) {
        callback(response);
      }
    },
    *queryWorkOrderIdByContactId({ payload, callback }, { call, put }) {
      let response = yield call(queryWorkOrderIdByContactId, payload);
      if (response) {
        callback(response);
      }
    },
    // 更新知识库权限到全局
    *saveAuthKnowledge({ payload }, { call, put }) {
      yield put({
        type: 'saveAuth',
        auth: payload,
      });
    },
  },
  reducers: {
    saveState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    saveAuth(state, { auth }) {
      return {
        ...state,
        auth,
      };
    },
    saveAuthInit(state, { authInit }) {
      return {
        ...state,
        authInit,
      };
    },
    updateMenuList(state, { menuList, menuIdList }) {
      return { ...state, menuList, menuIdList };
    },
    updateUser(state, { user }) {
      sessionStorage.setItem('user', JSON.stringify(user));
      return { ...state, user };
    },
    updateRoleId(state, { roleId }) {
      return { ...state, roleId };
    },
    updateLogout(state, { payload: logoutFlag }) {
      return { ...state, logoutFlag };
    },
    updateConnectList(state, { payload: connectList }) {
      return { ...state, connectList };
    },
    // updateMenuIdList(state, { payload: menuIdList }) {
    //   return { ...state, menuIdList };
    // },
    updateConnect(state, { payload: selectedConnect }) {
      return { ...state, selectedConnect };
    },
    updateDeptList(state, { payload: deptList }) {
      return { ...state, deptList };
    },
    updateChannelTypeList(state, { payload: channelTypeList }) {
      return { ...state, channelTypeList };
    },
    updateAllChannelInitList(state, { payload: allChannelInitList }) {
      return { ...state, allChannelInitList };
    },
  },
};
