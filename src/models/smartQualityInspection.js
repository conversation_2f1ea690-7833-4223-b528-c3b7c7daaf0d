import {
  getQualityInspectionRuleList,
  getQualityInspectionRuleDetail,
  deleteQualityInspectionRule,
  saveQualityInspectionRule,
  enableOrDisableQualityInspectionRule,
  saveAssessmentVersion,
  saveAndDeployAssessmentVersion,
  getAssessmentVersionList,
  getCategoryTreeStructureByAssessmentId,
  addCategoryTreeStructure,
  updateCategoryTreeStructure,
  deleteCategoryTreeStructure,
  getRuleTableList,
  saveRule,
  getRuleDetail,
  deleteRule,
  getAssessmentRecordPage,
  exportAssessmentForm,
  getAllRuleList,
} from '@/service/smartQualityInspection';

export default {
  namespace: 'smartQualityInspection',
  // 简化state，只保留isEditMode
  state: {
    // 添加评估表基本信息
    evaluationFormData: {},
    // 是否是编辑模式
    isEditMode: false,
    // 分类列表
    categoryList: [],
    // 评估表信息
    assessmentInfo: {},
  },
  effects: {
    *getQualityInspectionRuleList({ payload, callback }, { call, put }) {
      const response = yield call(getQualityInspectionRuleList, payload);
      if (response) {
        callback(response);
      }
    },
    *getQualityInspectionRuleDetail(
      { payload, callback, error },
      { call, put },
    ) {
      const response = yield call(getQualityInspectionRuleDetail, payload);
      if (response) {
        callback(response);
      }
    },
    *deleteQualityInspectionRule({ payload, callback, error }, { call, put }) {
      const response = yield call(deleteQualityInspectionRule, payload);
      if (response) {
        callback(response);
      }
    },
    *saveQualityInspectionRule({ payload, callback, error }, { call, put }) {
      const response = yield call(saveQualityInspectionRule, payload);
      if (response) {
        callback(response);
      }
    },
    *enableOrDisableQualityInspectionRule(
      { payload, callback, error },
      { call, put },
    ) {
      const response = yield call(
        enableOrDisableQualityInspectionRule,
        payload,
      );
      if (response) {
        callback(response);
      }
    },
    *saveAssessmentVersion({ payload, callback, error }, { call, put }) {
      const response = yield call(saveAssessmentVersion, payload);
      if (response) {
        callback(response);
      }
    },
    *saveAndDeployAssessmentVersion(
      { payload, callback, error },
      { call, put },
    ) {
      const response = yield call(saveAndDeployAssessmentVersion, payload);
      if (response) {
        callback(response);
      }
    },
    *getRuleTableList({ payload, callback, error }, { call, put }) {
      const response = yield call(getRuleTableList, payload);
      if (response) {
        callback(response);
      }
    },
    *addCategoryTreeStructure({ payload, callback, error }, { call, put }) {
      const response = yield call(addCategoryTreeStructure, payload);
      if (response) {
        callback(response);
      }
    },
    *getCategoryTreeStructureByAssessmentId(
      { payload, callback, error },
      { call, put },
    ) {
      const response = yield call(
        getCategoryTreeStructureByAssessmentId,
        payload,
      );
      if (response) {
        callback(response);
      }
    },
    *updateCategoryTreeStructure({ payload, callback, error }, { call, put }) {
      const response = yield call(updateCategoryTreeStructure, payload);
      if (response) {
        callback(response);
      }
    },
    *deleteCategoryTreeStructure({ payload, callback, error }, { call, put }) {
      const response = yield call(deleteCategoryTreeStructure, payload);
      if (response) {
        callback(response);
      }
    },
    *saveRule({ payload, callback, error }, { call, put }) {
      const response = yield call(saveRule, payload);
      if (response) {
        callback(response);
      }
    },
    *getAssessmentVersionList({ payload, callback, error }, { call, put }) {
      const response = yield call(getAssessmentVersionList, payload);
      if (response) {
        callback(response);
      }
    },
    *getRuleDetail({ payload, callback, error }, { call, put }) {
      const response = yield call(getRuleDetail, payload);
      if (response) {
        callback(response);
      }
    },
    *deleteRule({ payload, callback, error }, { call, put }) {
      const response = yield call(deleteRule, payload);
      if (response) {
        callback(response);
      }
    },
    *getAssessmentRecordPage({ payload, callback, error }, { call, put }) {
      const response = yield call(getAssessmentRecordPage, payload);
      if (response) {
        callback(response);
      }
    },
    *exportAssessmentForm({ payload, callback, error }, { call, put }) {
      const response = yield call(exportAssessmentForm, payload);
      if (response) {
        callback(response);
      }
    },
    *getAllRuleList({ payload, callback, error }, { call, put }) {
      const response = yield call(getAllRuleList, payload);
      if (response) {
        callback(response);
      }
    },
  },
  // 简化reducers
  reducers: {
    // 设置编辑模式
    setEditMode(state, { payload }) {
      return {
        ...state,
        isEditMode: payload,
      };
    },
    // 设置分类列表
    setCategoryList(state, { payload }) {
      return {
        ...state,
        categoryList: payload,
      };
    },
    // 设置评估表基本信息
    setEvaluationFormData(state, { payload }) {
      return {
        ...state,
        evaluationFormData: payload,
      };
    },
    // 编辑状态设置评估表id
    setAssessmentInfo(state, { payload }) {
      return {
        ...state,
        assessmentInfo: payload,
      };
    },
    resetAll(state) {
      return {
        ...state,
        evaluationFormData: {},
        assessmentInfo: {},
        categoryList: [],
        isEditMode: false,
      };
    },
  },
};
