import {
  queryAllPhotoSettingList,
  addPhotoSetting,
  updatePhotoSetting,
  removePhotoSetting,
  updatePhotoOrder,
  queryCustomerPhotoColor,
  queryPhotoSettingDetail,
} from '@/service/avatorService';
export default {
  namespace: 'avatorService',
  state: {},
  effects: {
    *queryAllPhotoSettingList({ payload, callback }, { call, put }) {
      let response = yield call(queryAllPhotoSettingList, payload);
      if (response) {
        callback(response);
      }
    },
    *addPhotoSetting({ payload, callback }, { call, put }) {
      let response = yield call(addPhotoSetting, payload);
      if (response) {
        callback(response);
      }
    },
    *updatePhotoSetting({ payload, callback }, { call, put }) {
      let response = yield call(updatePhotoSetting, payload);
      if (response) {
        callback(response);
      }
    },
    *removePhotoSetting({ payload, callback }, { call, put }) {
      let response = yield call(removePhotoSetting, payload);
      if (response) {
        callback(response);
      }
    },
    *updatePhotoOrder({ payload, callback }, { call, put }) {
      let response = yield call(updatePhotoOrder, payload);
      if (response) {
        callback(response);
      }
    },
    *queryCustomerPhotoColor({ payload, callback }, { call, put }) {
      let response = yield call(queryCustomerPhotoColor, payload);
      if (response) {
        callback(response);
      }
    },
    *queryPhotoSettingDetail({ payload, callback }, { call, put }) {
      let response = yield call(queryPhotoSettingDetail, payload);
      if (response) {
        callback(response);
      }
    },
  },
};
