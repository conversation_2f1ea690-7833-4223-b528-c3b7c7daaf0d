import {
  queryWorkOrderList,
  queryWorkRecordFilter,
  queryFilterCondition,
  queryWorkOrderExtList,
  createFilter,
  modifyFilter,
  queryCustomerInformation,
  querySeatsUser,
  queryWorkRecordLevel,
  queryWorkRecordType,
  queryHistoryCustomerWorkOrder,
  queryChannelNameListNew,
  queryChannelNameList,
  queryCreateWork,
  queryUpdateWork,
  queryWorkOrderDetail,
  queryAssociationWorkOrder,
  queryDefineExtList,
  queryConcernedWorkOrder,
  queryWorkRecordByUserId,
  queryContactDetailByUserId,
  addUserWorkRecordExt,
  addUserContactDetailExt,
  solveWorkStatus,
  terminationWorkStatus,
  assignWorkOrder,
  transferWorkOrder,
  urgingWorkOrder,
  updateWorkAssociation,
  associationWorkOrderTable,
  workUpgrade,
  workRemark,
  WorkOrderOperationRecords,
  claimWorkOrder,
  batchSolveWork,
  exportTicket,
  replyMessage,
  queryWorkRecordInfo,
  upload,
  deleteFile,
  uploadPicture,
  download,
  voiceAnalysis,
  workSummarize,
  addWaitExecute,
  updateWaitExecute,
  querySummary,
  queryAnswer,
  queryWorkOrderNumber,
  queryCustomTicketRecord,
  queryByTicketRemarks,
  queryCustomerLabels,
  queryByTicketRemarksNew,
  queryCallDeptList,
  updateWorkStatus,
  querySeatsUserList,
  queryRuleList,
  getAiMatchList,
  allQuickReply,
  workbenchTicketDetails,
  ticketGetDetails,
  availableAssessmentListByTicket,
  assessmentRecordByTicket,
  generateAssessmentRecords,
  assessmentCompleted,
  getAssessmentRuleDetail,
} from '@/service/workOrderCenter';
import { notification } from '@/utils/utils';
import { getIntl } from '@@/plugin-locale/localeExports';
import { uploadFile } from '@/service/batchMsgPush';
import { syncCallRecord } from '@/service/worktable';
export default {
  namespace: 'workOrderCenter',
  state: {
    test: '9999',
    customerId: '',
    historyCustomerWorkOrderList: {},
    total: 0,
    ticketContentList: [],
    ticketDetailList: [],
    loadingBtn: false,
    workOrderList: [],
    loadingTable: false,
    loadingReply: false,
    newWorkOrderDetail: [],
    fileList: [],
    pictureList: [],
    content: '',
    contentFileList: [],
    operationDescriptionList: [],
    newQueryType: '',
    channelTypeId: '',
    openAiEditor: false,
    firstOpenAiEditor: true,
    // phoneNumber: '',
    extractedTexts: '',
    initTranscriptList: [],
    initTranscriptText: '',
    initTranslateType: '',
    roleId: '',
    // 全部
    workerOrderAll: '',
    workerOrderProcessing: '',
    workerOrderAllocated: '',
    workerOrderResolved: '',
    workerOrderTransferred: '',
    workerOrderTerminated: '',
    workOrderDetail: {},
    replyWorkOrderDetail: {},
    createSuccess: false,
    replySuccess: false,
  },
  effects: {
    *setCheckedTipsStatus({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          checkedTipsStatus: payload,
        },
      });
    },
    // *setPhoneNumber({ payload }, { call, put }) {
    //   yield put({
    //     type: 'saveState',
    //     payload: {
    //       phoneNumber: payload,
    //     },
    //   });
    // },
    *setOpenAiEditor({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          openAiEditor: payload,
        },
      });
    },
    *setFirstOpenAiEditor({ payload }, { call, put }) {
      console.log(payload);
      yield put({
        type: 'saveState',
        payload: {
          firstOpenAiEditor: payload,
        },
      });
    },
    *setQueryType({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          newQueryType: payload,
        },
      });
    },
    *setExtractedTexts({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          extractedTexts: '',
        },
      });
    },
    *setContentList({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          ticketContentList: [],
          ticketDetailList: [],
        },
      });
    },
    *setContentList1({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          ticketContentList: [],
        },
      });
    },
    *setInitTranscriptList({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          initTranscriptList: [],
        },
      });
    },
    *setReplyWorkOrderDetail({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          replyWorkOrderDetail: payload,
        },
      });
    },
    *setCreateSuccess({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          createSuccess: false,
        },
      });
    },
    *setReplySuccess({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          replySuccess: false,
        },
      });
    },
    // 查看工单详情
    *queryWorkOrderDetailList({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          customerId: payload.workOrderDetail.customerId,
          roleId: payload.roleId,
          workOrderDetail: payload.workOrderDetail,
        },
      });
    },
    // 切换创建工单按钮loading状态
    *changeLoadingBtnStatus({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          loadingBtn: payload,
        },
      });
    },
    *changeLoadingTableStatus({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          loadingTable: payload,
        },
      });
    },
    *queryWorkOrderList({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          loadingTable: true,
          workOrderList: [],
          total: 0,
        },
      });
      const response = yield call(queryWorkOrderList, payload);
      if (response) {
        if (response.code == 200) {
          let workOrderList = response.data.records;
          for (let i = 0; i < workOrderList.length; i++) {
            workOrderList[i]['key'] = workOrderList[i].workRecordId;
          }
          yield put({
            type: 'saveState',
            payload: {
              workOrderList: workOrderList,
              total: response.data.total,
              loadingTable: false,
            },
          });
        } else {
          notification.error({
            message: response.msg,
          });
          yield put({
            type: 'saveState',
            payload: {
              loadingTable: false,
            },
          });
        }
      }
    },
    *saveWorkOrderList({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          workOrderList: [],
        },
      });
    },
    *queryWorkRecordFilter({ callback }, { call, put }) {
      const response = yield call(queryWorkRecordFilter);
      if (response) {
        callback(response);
      }
    },
    *queryFilterCondition({ payload, callback }, { call, put }) {
      const response = yield call(queryFilterCondition, payload);
      if (response) {
        callback(response);
      }
    },
    *queryWorkOrderExtList({ payload, callback }, { call, put }) {
      const response = yield call(queryWorkOrderExtList, payload);
      if (response) {
        callback(response);
      }
    },
    *createFilter({ payload, callback }, { call, put }) {
      const response = yield call(createFilter, payload);
      if (response) {
        callback(response);
      }
    },
    *modifyFilter({ payload, callback }, { call, put }) {
      const response = yield call(modifyFilter, payload);
      if (response) {
        callback(response);
      }
    },
    *queryCustomerInformation({ callback }, { call, put }) {
      const response = yield call(queryCustomerInformation);
      if (response) {
        callback(response);
      }
    },
    *querySeatsUser({ callback }, { call, put }) {
      const response = yield call(querySeatsUser);
      if (response) {
        callback(response);
      }
    },
    *queryWorkRecordLevel({ callback }, { call, put }) {
      const response = yield call(queryWorkRecordLevel);
      if (response) {
        callback(response);
      }
    },
    *queryWorkRecordType({ callback }, { call, put }) {
      const response = yield call(queryWorkRecordType);
      if (response) {
        callback(response);
      }
    },
    *queryHistoryCustomerWorkOrder({ payload, callback }, { call, put }) {
      const response = yield call(queryHistoryCustomerWorkOrder, payload);
      if (response) {
        callback(response);
      }
    },
    *queryChannelNameList({ callback }, { call, put }) {
      const response = yield call(queryChannelNameList);
      if (response) {
        callback(response);
      }
    },
    *queryChannelNameListNew({ callback }, { call, put }) {
      const response = yield call(queryChannelNameListNew);
      if (response) {
        callback(response);
      }
    },

    *queryCreateWork({ payload, callback }, { select, call, put }) {
      const response = yield call(queryCreateWork, payload);
      if (response) {
        if (response.code == 200) {
          notification.success({
            message: response.msg,
          });
          yield put({
            type: 'saveState',
            payload: {
              loadingBtn: false,
              loadingTable: true,
              createSuccess: true,
            },
          });
          const state = yield select(state => state);
          let newQueryType = state.workOrderCenter.newQueryType;
          yield put({
            type: 'queryWorkOrderList',
            payload: {
              selectItem: {
                queryType: newQueryType,
                status: '',
                workOrderExtVos: [],
              },
              pageNum: 1,
              pageSize: 10,
            },
          });
          yield put({
            type: 'queryWorkOrderNumber',
            payload: {
              queryType: newQueryType,
              status: '',
              workOrderExtVos: [],
            },
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
        callback(response);
      }
    },
    *queryUpdateWork({ payload, callback }, { call, put }) {
      const response = yield call(queryUpdateWork, payload);
      if (response) {
        callback(response);
      }
    },
    *queryWorkOrderDetail({ payload, callback }, { call, put }) {
      const response = yield call(queryWorkOrderDetail, payload);
      console.log(response);
      if (response) {
        callback(response);
        yield put({
          type: 'saveState',
          payload: {
            newWorkOrderDetail: response.data,
          },
        });
      }
    },
    *workbenchTicketDetails({ payload, callback }, { call, put }) {
      const response = yield call(workbenchTicketDetails, payload);
      console.log(response);
      if (response) {
        callback(response);
        // yield put({
        //   type: 'saveState',
        //   payload: {
        //     newWorkOrderDetail: response.data,
        //   },
        // });
      }
    },
    *queryAssociationWorkOrder({ payload, callback }, { call, put }) {
      const response = yield call(queryAssociationWorkOrder, payload);
      if (response) {
        callback(response);
      }
    },
    *queryDefineExtList({ callback }, { call, put }) {
      const response = yield call(queryDefineExtList);
      if (response) {
        callback(response);
      }
    },
    *queryConcernedWorkOrder({ payload, callback }, { call, put }) {
      const response = yield call(queryConcernedWorkOrder, payload);
      if (response) {
        callback(response);
      }
    },
    *queryWorkRecordByUserId({ payload, callback }, { call, put }) {
      const response = yield call(queryWorkRecordByUserId, payload);
      if (response) {
        callback(response);
      }
    },
    // 查询联络明细表头
    *queryContactDetailByUserId({ callback }, { call, put }) {
      const response = yield call(queryContactDetailByUserId);
      if (response) {
        callback(response);
      }
    },

    *addUserWorkRecordExt({ payload, callback }, { call, put }) {
      const response = yield call(addUserWorkRecordExt, payload);
      if (response) {
        callback(response);
      }
    },
    // 联络明细 动态列添加
    *addUserContactDetailExt({ payload, callback }, { call, put }) {
      const response = yield call(addUserContactDetailExt, payload);
      if (response) {
        callback(response);
      }
    },
    *querySolveWorkStatus({ payload, callback }, { call, put }) {
      const response = yield call(solveWorkStatus, payload);
      if (response) {
        callback(response);
      }
    },
    *queryTerminationWorkStatus({ payload, callback }, { call, put }) {
      const response = yield call(terminationWorkStatus, payload);
      if (response) {
        callback(response);
      }
    },
    *queryAssignWorkOrder({ payload, callback }, { call, put }) {
      const response = yield call(assignWorkOrder, payload);
      if (response) {
        callback(response);
      }
    },
    *queryTransferWorkOrder({ payload, callback }, { call, put }) {
      const response = yield call(transferWorkOrder, payload);
      if (response) {
        callback(response);
      }
    },
    *queryUrgingWorkOrder({ payload, callback }, { call, put }) {
      const response = yield call(urgingWorkOrder, payload);
      if (response) {
        callback(response);
      }
    },
    *queryUpdateWorkAssociation({ payload, callback }, { call, put }) {
      const response = yield call(updateWorkAssociation, payload);
      if (response) {
        callback(response);
      }
    },
    *queryAssociationWorkOrderTable({ payload, callback }, { call, put }) {
      const response = yield call(associationWorkOrderTable, payload);
      if (response) {
        callback(response);
      }
    },
    *queryWorkUpgrade({ payload, callback }, { call, put }) {
      const response = yield call(workUpgrade, payload);
      if (response) {
        callback(response);
      }
    },
    *queryWorkRemark({ payload, callback }, { call, put }) {
      const response = yield call(workRemark, payload);
      if (response) {
        callback(response);
      }
    },
    *queryWorkOrderOperationRecords({ payload }, { call, put }) {
      const response = yield call(WorkOrderOperationRecords, payload);
      if (response) {
        if (response.code == 200) {
          let operationDescriptionList = [];
          let data = response.data;
          for (let i = 0; i < data.length; i++) {
            let item = {
              title: data[i].createTime,
              description: data[i].operationLogDescribe,
            };
            operationDescriptionList.push(item);
          }
          yield put({
            type: 'saveState',
            payload: {
              operationDescriptionList: operationDescriptionList,
            },
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      }
    },
    *queryClaimWorkOrder({ payload, callback }, { call, put }) {
      const response = yield call(claimWorkOrder, payload);
      if (response) {
        callback(response);
      }
    },
    *queryBatchSolveWork({ payload, callback }, { call, put }) {
      const response = yield call(batchSolveWork, payload);
      if (response) {
        callback(response);
      }
    },
    *queryExportTicket({ payload, callback }, { call, put }) {
      const response = yield call(exportTicket, payload);
      if (response) {
        const url = window.URL.createObjectURL(
          new Blob([response], {
            type:
              'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          }),
        );
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = url;
        let timestamp = new Date().getTime();
        link.setAttribute('download', '工单列表' + '_' + timestamp + '.xlsx');
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        callback(response);
      }
    },
    *queryReplyMessage({ payload, callback }, { select, call, put }) {
      const response = yield call(replyMessage, payload);
      const state = yield select(state => state);
      let newWorkOrderDetail = state.workOrderCenter.newWorkOrderDetail;

      let params = {
        workRecordId: newWorkOrderDetail.workRecordId,
        channelTypeId: newWorkOrderDetail.channelTypeId,
      };
      yield put({
        type: 'saveState',
        payload: {
          loadingReply: true,
          replySuccess: true,
        },
      });
      // yield put({
      //   type: 'queryWorkRecordInfo',
      //   payload: params,
      // });
      if (response) {
        callback(response);
      }
    },
    *queryWorkRecordInfo({ payload }, { call, put }) {
      const response = yield call(queryWorkRecordInfo, payload);
      if (response) {
        if (response.code == 200) {
          let detailInfoList = response.data.dataList;
          if (
            payload.channelTypeId == 7 ||
            payload.channelTypeId == 10 ||
            payload.channelTypeId == 11 ||
            payload.channelTypeId == 17 ||
            payload.channelTypeId == 18
          ) {
            let contactId = detailInfoList[0].contact_id;
            yield put({
              type: 'queryVoiceAnalysis1',
              payload: contactId,
            });
            yield put({
              type: 'saveState',
              payload: {
                ticketContentList: detailInfoList,
                ticketDetailList: detailInfoList,
                channelTypeId: payload.channelTypeId,
                loadingReply: false,
              },
            });
          } else if (payload.channelTypeId == 1) {
            let extractedTexts = '';
            for (let i = 0; i < detailInfoList.length; i++) {
              let content = detailInfoList[i].content;
              let replyType = detailInfoList[i].reply_type;
              const parser = new DOMParser();
              const doc = parser.parseFromString(content, 'text/html');
              let textNodes = doc.body.innerText.trim().split('\n');
              // let newTextNodes=''
              if (replyType == 1 || replyType == 3) {
                // 客服
                textNodes = 'agent:' + textNodes + '\n';
              } else if (replyType == 2) {
                // 客户
                textNodes = 'custom:' + textNodes + '\n';
              }
              extractedTexts += textNodes;
              // detailInfoList[i].translate_content = null;
            }
            yield put({
              type: 'saveState',
              payload: {
                ticketContentList: detailInfoList,
                ticketDetailList: detailInfoList,
                channelTypeId: payload.channelTypeId,
                loadingReply: false,
                extractedTexts: extractedTexts,
              },
            });
          } else {
            let extractedTexts = '';
            for (let i = 0; i < detailInfoList.length; i++) {
              let content = detailInfoList[i].content;
              let replyType = detailInfoList[i].reply_type;
              const parser = new DOMParser();
              const doc = parser.parseFromString(content, 'text/html');
              let textNodes =
                +detailInfoList[i]?.reply_type !== 7 &&
                +detailInfoList[i]?.reply_type !== 6 &&
                +detailInfoList[i]?.reply_type !== 5 &&
                +detailInfoList[i]?.content_type === 1 &&
                +detailInfoList[i]?.reply_type !== 4
                  ? doc.body.innerText.trim().split('\n')
                  : '';
              // let newTextNodes=''
              if (replyType == 1 || replyType == 3) {
                // 客服
                textNodes = 'agent:' + textNodes + '\n';
              } else if (replyType == 2) {
                // 客户
                textNodes = 'custom:' + textNodes + '\n';
              }
              extractedTexts += textNodes;
              // detailInfoList[i].translate_content = null;
            }
            yield put({
              type: 'saveState',
              payload: {
                ticketContentList: detailInfoList,
                ticketDetailList: detailInfoList,
                channelTypeId: payload.channelTypeId,
                loadingReply: false,
                extractedTexts: extractedTexts,
              },
            });
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      }
    },
    *newQueryWorkOrderDetail({ payload, callback }, { call, put }) {
      const response = yield call(queryWorkRecordInfo, payload);
      if (response) {
        callback(response);
      }
    },
    *queryUpload({ payload, callback }, { call, put }) {
      const response = yield call(upload, payload);
      if (response) {
        callback(response);
      }
    },
    *queryUploadPicture({ payload, callback }, { call, put }) {
      const response = yield call(uploadPicture, payload);
      if (response) {
        callback(response);
      }
    },
    *queryUploadPictureNew({ payload }, { call, put }) {
      const response = yield call(uploadPicture, payload);
      if (response.code === 200) {
        return { success: 1, file: { url: response.data.url } };
      } else {
        return { success: 0, error: response.msg || 'Upload failed' };
      }
    },
    *queryDeleteFile({ payload, callback }, { call, put }) {
      const response = yield call(deleteFile, payload);
      if (response) {
        callback(response);
      }
    },
    *updatePictureList({ payload, callback }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          pictureList: payload,
        },
      });
    },
    *updateContent({ payload, callback }, { call, put }) {
      yield put({
        type: 'updateState',
        payload: {
          content: payload,
        },
      });
      let response = 200;
      callback(response);
    },
    *updateContentFileList({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          contentFileList: payload,
        },
      });
    },
    *queryDownload({ payload, callback }, { call, put }) {
      const response = yield call(download, payload);
      if (response) {
        callback(response);
      }
    },
    *queryVoiceAnalysis({ payload, callback }, { call, put }) {
      const response = yield call(voiceAnalysis, payload);
      if (response) {
        callback(response);
      }
    },
    *queryVoiceAnalysis1({ payload }, { call, put }) {
      const response = yield call(voiceAnalysis, payload);
      if (response) {
        if (response) {
          if (response.code == 200) {
            if (response.data) {
              let extractedTexts = '';
              let Transcript = response.data.translateVOS;
              for (let i = 0; i < Transcript.length; i++) {
                let newContent =
                  Transcript[i].type + ':' + Transcript[i].content;
                extractedTexts += newContent + '\n';
              }
              yield put({
                type: 'saveState',
                payload: {
                  extractedTexts: extractedTexts,
                  initTranscriptList: Transcript,
                  initTranscriptText: response.data.text,
                  initTranslateType: response.data.translateType,
                },
              });
            }
          }
        }
      }
    },
    *queryWorkSummarize({ payload, callback }, { call, put }) {
      const response = yield call(workSummarize, payload);
      if (response) {
        callback(response);
      }
    },
    *queryAddWaitExecute({ payload, callback }, { call, put }) {
      const response = yield call(addWaitExecute, payload);
      if (response) {
        callback(response);
      }
    },
    *queryUpdateWaitExecute({ payload, callback }, { call, put }) {
      const response = yield call(updateWaitExecute, payload);
      if (response) {
        callback(response);
      }
    },
    *querySummary({ payload, callback }, { call, put }) {
      const response = yield call(querySummary, payload);
      if (response) {
        callback(response);
      }
    },
    *queryAnswer({ payload, callback }, { select, call, put }) {
      const state = yield select(state => state);
      let ticketContentList = state.workOrderCenter.ticketContentList;
      let newTicketContentList = JSON.stringify(ticketContentList);
      let newTicketContentList1 = JSON.parse(newTicketContentList);
      // let ticketContentList1 = Object.assign({}, state.workOrderCenter.ticketContentList);
      // let ticketContentList =Array.from(ticketContentList1);
      // console.log(ticketContentList);
      // let length = Object.keys(ticketContentList).length;
      yield put({
        type: 'saveState',
        payload: {
          loadingReply: true,
        },
      });
      let params = {
        promptId: '40000',
        contentQuestion: payload.contentQuestion,
        language: payload.language,
      };
      let wordType = payload.wordType;
      let workRecordContentId = payload.workRecordContentId;
      const response = yield call(queryAnswer, params);
      if (response) {
        callback(response);
        if (wordType == 3) {
          let answerChatText = response.data.text;
          for (let i = 0; i < newTicketContentList1.length; i++) {
            if (
              newTicketContentList1[i].workRecordContentId ==
              workRecordContentId
            ) {
              newTicketContentList1[i].translationContent = answerChatText;
            }
          }
          console.log(newTicketContentList1);
          yield put({
            type: 'saveState',
            payload: {
              ticketContentList: newTicketContentList1,
            },
          });
          yield put({
            type: 'saveState',
            payload: {
              loadingReply: false,
            },
          });
        } else if (wordType == 2) {
          let answerEmailText = response.data.text;
          for (let i = 0; i < newTicketContentList1.length; i++) {
            if (
              newTicketContentList1[i].workRecordContentId ==
              workRecordContentId
            ) {
              newTicketContentList1[i].content = answerEmailText;
            }
          }
          yield put({
            type: 'saveState',
            payload: {
              ticketContentList: newTicketContentList1,
            },
          });
          yield put({
            type: 'saveState',
            payload: {
              loadingReply: false,
            },
          });
        } else {
          yield put({
            type: 'saveState',
            payload: {
              loadingReply: false,
            },
          });
        }
      }
    },
    *queryWorkOrderNumber({ payload }, { call, put }) {
      const response = yield call(queryWorkOrderNumber, payload);
      if (response) {
        if (response.code == 200) {
          let data = response.data;
          for (let i = 0; i < data.length; i++) {
            if (data[i].statusName == '全部') {
              let workerOrderAll = data[i].statusNumber;
              yield put({
                type: 'saveState',
                payload: {
                  workerOrderAll: workerOrderAll,
                },
              });
            } else if (data[i].statusName == '处理中') {
              let workerOrderProcessing = data[i].statusNumber;
              yield put({
                type: 'saveState',
                payload: {
                  workerOrderProcessing: workerOrderProcessing,
                },
              });
            } else if (data[i].statusName == '待分配') {
              let workerOrderAllocated = data[i].statusNumber;
              yield put({
                type: 'saveState',
                payload: {
                  workerOrderAllocated: workerOrderAllocated,
                },
              });
            } else if (data[i].statusName == '已解决') {
              let workerOrderResolved = data[i].statusNumber;
              yield put({
                type: 'saveState',
                payload: {
                  workerOrderResolved: workerOrderResolved,
                },
              });
            } else if (data[i].statusName == '已转单') {
              let workerOrderTransferred = data[i].statusNumber;
              yield put({
                type: 'saveState',
                payload: {
                  workerOrderTransferred: workerOrderTransferred,
                },
              });
            } else if (data[i].statusName == '已终止') {
              let workerOrderTerminated = data[i].statusNumber;
              yield put({
                type: 'saveState',
                payload: {
                  workerOrderTerminated: workerOrderTerminated,
                },
              });
            }
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
        // callback(response);
      }
    },
    *queryCustomTicketRecord({ payload, callback }, { call, put }) {
      const response = yield call(queryCustomTicketRecord, payload);
      if (response) {
        callback(response);
      }
    },
    *queryByTicketRemarks({ payload, callback }, { call, put }) {
      const response = yield call(queryByTicketRemarks, payload);
      if (response) {
        callback(response);
      }
    },
    *queryByTicketRemarksNew({ payload, callback }, { call, put }) {
      const response = yield call(queryByTicketRemarksNew, payload);
      if (response) {
        callback(response);
      }
    },

    *setTicketContentList({ payload }, { call, put }) {
      yield put({
        type: 'saveState',
        payload: {
          ticketContentList: payload,
        },
      });
    },
    *queryCustomerLabels({ payload, callback }, { call, put }) {
      const response = yield call(queryCustomerLabels, payload);
      if (response) {
        callback(response);
      }
    },
    *newSyncCallRecord({ payload }, { call, put }) {
      const response = yield call(syncCallRecord, payload);
      if (response) {
        if (response.code == 200) {
          notification.success({
            message: response.msg,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      }
    },
    *queryCallDeptList({ callback }, { call, put }) {
      const response = yield call(queryCallDeptList);
      if (response) {
        callback(response);
      }
    },
    *updateWorkStatus({ callback, payload }, { call, put }) {
      const response = yield call(updateWorkStatus, payload);
      if (response) {
        callback(response);
      }
    },
    *querySeatsUserList({ callback }, { call, put }) {
      const response = yield call(querySeatsUserList);
      if (response) {
        callback(response);
      }
    },
    // 查询路由规则
    *queryRuleList({ callback }, { call, put }) {
      const response = yield call(queryRuleList);
      if (response) {
        callback(response);
      }
    },
    // 查询智能匹配规则列表
    *getAiMatchList({ callback, payload }, { call, put }) {
      const response = yield call(getAiMatchList, payload);
      if (response) {
        callback(response);
      }
    },
    // 查询输入框快捷回复
    *allQuickReply({ callback, payload }, { call, put }) {
      const response = yield call(allQuickReply, payload);
      if (response) {
        callback(response);
      }
    },
    // 关联工单、历史工单回显工单详情
    *ticketGetDetails({ callback, payload }, { call, put }) {
      const response = yield call(ticketGetDetails, payload);
      if (response) {
        callback(response);
      }
    },
    *availableAssessmentListByTicket({ callback, payload }, { call, put }) {
      const response = yield call(availableAssessmentListByTicket, payload);
      if (response) {
        callback(response);
      }
    },
    *assessmentRecordByTicket({ callback, payload }, { call, put }) {
      const response = yield call(assessmentRecordByTicket, payload);
      if (response) {
        callback(response);
      }
    },
    *generateAssessmentRecords({ callback, payload }, { call, put }) {
      const response = yield call(generateAssessmentRecords, payload);
      if (response) {
        callback(response);
      }
    },
    *assessmentCompleted({ callback, payload }, { call, put }) {
      const response = yield call(assessmentCompleted, payload);
      if (response) {
        callback(response);
      }
    },
    *getAssessmentRuleDetail({ callback, payload }, { call, put }) {
      const response = yield call(getAssessmentRuleDetail, payload);
      if (response) {
        callback(response);
      }
    },
  },
  reducers: {
    saveState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    updateFileList(state, { payload: fileList }) {
      return { ...state, fileList };
    },
  },
};
