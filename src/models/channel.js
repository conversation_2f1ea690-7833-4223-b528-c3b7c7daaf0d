import {
  channelList,
  channelType,
  typeCode,
  updateStatus,
  deleteChannel,
  flowList,
  createChannel,
  updateChannel,
  channelDetails,
  getChannelByEmailTo,
  templateList,
  queryChannelById,
  queryRegionDefList,
  channelDetails1,
  queryWebLanguage,
  queryChannelTypeContact,
  queryPhoneList,
  retrievePhone,
  queryPartConfig,
  getAllApps,
  lineWebhookUrl,
  wxOfficeAConfiguration,
  getChannelGroup,
} from '@/service/channel';
// import {getChannelByEmailTo} from "../service/channel";

export default {
  namespace: 'channel',
  state: {
    returnValue: {},
    channelType: 1,
    channelList: [],
  },
  effects: {
    *getChannel({ payload, callback }, { call, put }) {
      let response = yield call(channelList, payload);
      if (response) {
        callback(response);
      }
    },
    *getChannelGroup({ callback }, { call, put }) {
      let response = yield call(getChannelGroup);
      if (response) {
        callback(response);
      }
    },
    *getChannelType({ payload, callback }, { call, put }) {
      let response = yield call(channelType, payload);
      if (response) {
        callback(response);
        let { code, data, msg } = response;
        // console.log(data)
        let channelList = data.map(({ code, name }) => ({
          value: code,
          label: name,
        }));
        if (200 === code) {
          yield put({
            type: 'saveState',
            payload: channelList,
          });
        }
      }
    },
    *typeCode({ payload, callback }, { call, put }) {
      let response = yield call(typeCode, payload);
      if (response) {
        callback(response);
      }
    },
    *updateStatus({ payload, callback }, { call, put }) {
      let response = yield call(updateStatus, payload);
      if (response) {
        callback(response);
      }
    },
    *deleteChannel({ payload, callback }, { call, put }) {
      let response = yield call(deleteChannel, payload);
      if (response) {
        callback(response);
      }
    },
    *flowList({ payload, callback }, { call, put }) {
      let response = yield call(flowList, payload);
      if (response) {
        callback(response);
      }
    },
    *createChannel({ payload, callback }, { call, put }) {
      let response = yield call(createChannel, payload);
      if (response) {
        callback(response);
      }
    },
    *updateChannel({ payload, callback }, { call, put }) {
      let response = yield call(updateChannel, payload);
      if (response) {
        callback(response);
      }
    },
    *channelDetails({ payload, callback }, { call, put }) {
      let response = yield call(channelDetails, payload);
      if (response) {
        callback(response);
      }
    },
    *queryChannelById({ payload, callback }, { call, put }) {
      let response = yield call(queryChannelById, payload);
      if (response) {
        callback(response);
      }
    },
    *getChannelByEmailTo({ payload, callback }, { call, put }) {
      // console.log('payload', payload);
      let response = yield call(getChannelByEmailTo, payload);
      // console.log('response', response);
      if (response) {
        callback(response);
      }
    },
    *templateList({ payload, callback }, { call, put }) {
      let response = yield call(templateList, payload);
      if (response) {
        callback(response);
      }
    },
    *queryRegionDefList({ payload, callback }, { call, put }) {
      let response = yield call(queryRegionDefList, payload);
      if (response) {
        callback(response);
      }
    },
    *channelDetails1({ payload, callback }, { call, put }) {
      let response = yield call(channelDetails1, payload);
      if (response) {
        callback(response);
      }
    },
    *queryWebLanguage({ payload, callback }, { call, put }) {
      let response = yield call(queryWebLanguage, payload);
      if (response) {
        callback(response);
      }
    },
    *queryChannelTypeContact({ payload, callback }, { call, put }) {
      let response = yield call(queryChannelTypeContact, payload);
      if (response) {
        callback(response);
      }
    },
    *queryPhoneList({ payload, callback }, { call, put }) {
      let response = yield call(queryPhoneList, payload);
      if (response) {
        callback(response);
      }
    },
    *retrievePhone({ payload, callback }, { call, put }) {
      let response = yield call(retrievePhone, payload);
      if (response) {
        callback(response);
      }
    },
    // 新版查询渠道配置信息
    *queryPartConfig({ payload, callback }, { call, put }) {
      let response = yield call(queryPartConfig, payload);
      if (response) {
        callback(response);
      }
    },
    // 查询GooglePlay应用
    *getAllApps({ callback }, { call, put }) {
      let response = yield call(getAllApps);
      if (response) {
        callback(response);
      }
    },
    // 获取line webhook url
    *lineWebhookUrl({ payload, callback }, { call, put }) {
      let response = yield call(lineWebhookUrl, payload);
      if (response) {
        callback(response);
      }
    },
    // 获取微信公众号配置
    *wxOfficeAConfiguration({ callback }, { call, put }) {
      let response = yield call(wxOfficeAConfiguration);
      if (response) {
        callback(response);
      }
    },
  },

  reducers: {
    // saveState(state, { payload }) {
    //   return {
    //     ...state,
    //     ...payload,
    //   };
    // },
    // updateChannelList(state, { payload: channelList }) {
    //   return { ...state, channelList };
    // },
  },
};
