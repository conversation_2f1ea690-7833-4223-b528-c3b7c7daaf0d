export const DocumentIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="31"
    height="30"
    viewBox="0 0 31 30"
    fill="none"
  >
    <path
      d="M19.5625 1.25C19.7111 1.2503 19.8576 1.28563 19.99 1.35313L20.0313 1.37562C20.0742 1.40083 20.1154 1.42902 20.1544 1.46L20.17 1.47312L20.2131 1.5125L26.4881 7.7875L26.5075 7.80813L26.5125 7.81375L26.475 7.77437L26.5269 7.83063L26.5475 7.85563C26.567 7.88015 26.5854 7.9056 26.6025 7.93188L26.6244 7.96875L26.6469 8.00938C26.7131 8.13812 26.75 8.28313 26.75 8.4375V27.5C26.75 27.8315 26.6183 28.1495 26.3839 28.3839C26.1495 28.6183 25.8315 28.75 25.5 28.75H5.5C5.16848 28.75 4.85054 28.6183 4.61612 28.3839C4.3817 28.1495 4.25 27.8315 4.25 27.5V2.5C4.25 2.16848 4.3817 1.85054 4.61612 1.61612C4.85054 1.3817 5.16848 1.25 5.5 1.25H19.5625ZM17.375 3.125H6.125V26.875H24.875V10.625H18.625C18.2935 10.625 17.9755 10.4933 17.7411 10.2589C17.5067 10.0245 17.375 9.70652 17.375 9.375V3.125ZM16.4375 19.375C16.6861 19.375 16.9246 19.4738 17.1004 19.6496C17.2762 19.8254 17.375 20.0639 17.375 20.3125C17.375 20.5611 17.2762 20.7996 17.1004 20.9754C16.9246 21.1512 16.6861 21.25 16.4375 21.25H11.4375C11.1889 21.25 10.9504 21.1512 10.7746 20.9754C10.5988 20.7996 10.5 20.5611 10.5 20.3125C10.5 20.0639 10.5988 19.8254 10.7746 19.6496C10.9504 19.4738 11.1889 19.375 11.4375 19.375H16.4375ZM19.6669 13.75C20.1269 13.75 20.5 14.17 20.5 14.6875C20.5 15.205 20.1269 15.625 19.6669 15.625H11.3331C10.8731 15.625 10.5 15.205 10.5 14.6875C10.5 14.17 10.8731 13.75 11.3331 13.75H19.6669ZM19.25 3.20063V8.75H24.7994L19.25 3.20063Z"
      fill="#3463FC"
    />
  </svg>
);
export const DownloadIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M18.3696 10.1123C18.0936 10.1123 17.8696 10.3363 17.8696 10.6123V13.1643C17.8696 14.6483 16.6626 15.8553 15.1786 15.8553H5.01961C3.53561 15.8553 2.32861 14.6483 2.32861 13.1643V10.6123C2.32861 10.3363 2.10461 10.1123 1.82861 10.1123C1.55261 10.1123 1.32861 10.3363 1.32861 10.6123V13.1643C1.32861 15.1993 2.98461 16.8553 5.01961 16.8553H15.1796C17.2146 16.8553 18.8706 15.1993 18.8706 13.1643V10.6123C18.8703 10.4796 18.8174 10.3524 18.7235 10.2587C18.6296 10.165 18.5023 10.1123 18.3696 10.1123Z"
      fill="#3463FC"
    />
    <path
      d="M6.42308 8.78229C6.68108 8.78229 6.70208 8.78229 8.69908 10.7153C9.01608 11.0213 9.33008 11.3283 9.60908 11.6003V3.66229C9.60908 3.38629 9.83308 3.16229 10.1091 3.16229C10.3851 3.16229 10.6091 3.38629 10.6091 3.66229V11.7433L13.4431 8.92729C13.5411 8.83029 13.6681 8.78229 13.7951 8.78229C13.9241 8.78229 14.0521 8.83129 14.1501 8.93029C14.1964 8.97681 14.2332 9.03201 14.2581 9.09274C14.2831 9.15347 14.2959 9.21854 14.2957 9.28421C14.2955 9.34988 14.2824 9.41487 14.2571 9.47545C14.2317 9.53604 14.1947 9.59104 14.1481 9.63729L10.5411 13.2213C10.4477 13.3141 10.3216 13.3663 10.19 13.3667C10.0584 13.3671 9.93194 13.3155 9.83808 13.2233C8.27208 11.6833 6.48808 9.96029 6.18608 9.72229C6.03008 9.63729 5.92408 9.47229 5.92408 9.28229C5.92381 9.21664 5.93653 9.15159 5.9615 9.09087C5.98647 9.03015 6.0232 8.97497 6.06958 8.9285C6.11596 8.88203 6.17106 8.84519 6.23173 8.8201C6.2924 8.79501 6.35743 8.78216 6.42308 8.78229Z"
      fill="#3463FC"
    />
  </svg>
);
export const HotspotIssuesIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="13"
    viewBox="0 0 12 13"
    fill="none"
  >
    <g clip-path="url(#clip0_3537_19966)">
      <path
        d="M7.50892 11.3524C7.59138 11.3081 7.66787 11.2699 7.73718 11.2185C8.35982 10.8349 8.7542 10.2911 8.94064 9.67803C9.26212 8.62396 8.96335 7.46711 8.39448 6.55884C7.86984 5.72706 7.6105 4.75187 7.60333 3.77906C7.15876 4.31924 7.12649 5.06259 6.99264 5.72587C6.85281 6.43455 6.38314 6.81579 5.85611 6.92693C5.62068 6.96876 5.37927 6.96159 5.151 6.90542C4.92274 6.84806 4.70046 6.73094 4.51641 6.57199C4.09096 6.20032 3.84955 5.58723 4.09096 4.79967C4.40288 3.80296 4.24393 2.90067 3.55795 2.12147C3.68463 3.65597 3.16357 4.97654 2.33776 6.25768C1.98521 6.80384 1.74739 7.41333 1.63266 8.02044C1.41037 9.19283 1.6458 10.523 2.71302 11.2089C2.77636 11.2567 2.85284 11.295 2.92216 11.3332C2.82655 10.7644 2.91857 10.1608 3.15759 9.63023C3.36434 9.17251 3.68463 8.77215 4.09096 8.4925C4.40288 8.28934 4.75782 8.14951 5.14503 8.12441C5.39361 8.10171 5.60872 8.28934 5.62785 8.53433C5.63741 8.69328 5.56451 8.83908 5.44978 8.92751C5.38644 8.98129 5.33863 9.03866 5.30398 9.09602C5.22749 9.23943 5.19283 9.39719 5.20239 9.55255C5.24661 10.0772 5.61589 10.2003 6.06047 10.2445C6.74645 10.3162 7.44558 10.5003 7.50892 11.3524ZM8.21402 11.975C7.89613 12.1782 7.53999 12.3407 7.14083 12.4638C7.07032 12.4889 6.98786 12.4925 6.91257 12.4769C6.67714 12.4255 6.51819 12.1913 6.56599 11.9499C6.70582 11.2735 6.47397 11.1934 5.96845 11.1337C5.09484 11.044 4.38973 10.5875 4.31325 9.62784C4.31325 9.58003 4.30727 9.53223 4.30727 9.47845C4.17342 9.63142 4.05989 9.8083 3.97025 10.0019C3.72168 10.5516 3.6906 11.2567 4.03359 11.7778C4.10291 11.8794 4.13876 12.0001 4.11606 12.1339C4.07184 12.3754 3.8364 12.5343 3.595 12.4937C3.06079 12.3957 2.61024 12.208 2.22542 11.963C0.841506 11.0691 0.463857 9.38643 0.749484 7.85791C0.88931 7.13966 1.16896 6.41902 1.58844 5.77367C2.57917 4.22483 2.90662 2.86482 2.47759 1.04708C2.4489 0.922795 2.47759 0.786554 2.55407 0.681386C2.69987 0.481806 2.97952 0.443563 3.17671 0.583389C4.7136 1.70199 5.52746 3.16957 4.94186 5.06259C4.82474 5.45697 4.92274 5.73901 5.10081 5.89437C5.17969 5.96369 5.27529 6.01269 5.38046 6.03778C5.47607 6.07244 5.58124 6.07244 5.67923 6.04735C5.87642 6.01269 6.06047 5.85733 6.11783 5.55258C6.39748 4.14835 6.50504 3.20422 7.90927 2.41307C7.99771 2.35571 8.11244 2.33659 8.22717 2.35332C8.46499 2.39515 8.62752 2.63536 8.58331 2.87796C8.37297 3.97984 8.55104 5.12952 9.15217 6.08917C9.86086 7.22331 10.1979 8.62755 9.79632 9.93617C9.54535 10.7512 9.02071 11.4671 8.21402 11.975Z"
        fill="#AD30E5"
      />
    </g>
    <defs>
      <clipPath id="clip0_3537_19966">
        <rect
          width="12"
          height="12"
          fill="white"
          transform="translate(0 0.5)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const RightArrowIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <rect
      width="16"
      height="16"
      rx="8"
      transform="matrix(0 1 1 0 0 0)"
      fill="#3463FC"
    />
    <path
      d="M6.90593 10.1913L9.271 7.99693L6.95611 5.79698C6.76794 5.5703 6.77401 5.20784 6.96933 4.98752C7.16465 4.76721 7.47539 4.77213 7.66338 4.99891L10.3188 7.60909C10.5069 7.83597 10.5009 8.19833 10.3056 8.41864L7.58675 11.0121C7.39143 11.2323 7.08079 11.2275 6.89262 11.0008C6.70453 10.774 6.71052 10.4115 6.90593 10.1913Z"
      fill="white"
    />
  </svg>
);
export const LeftArrowIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <rect
      x="16"
      width="16"
      height="16"
      rx="8"
      transform="rotate(90 16 0)"
      fill="#999999"
    />
    <path
      d="M9.09407 10.1913L6.729 7.99693L9.04389 5.79698C9.23206 5.5703 9.22599 5.20784 9.03067 4.98752C8.83535 4.76721 8.52461 4.77213 8.33662 4.99891L5.68118 7.60909C5.4931 7.83597 5.49909 8.19833 5.69441 8.41864L8.41325 11.0121C8.60857 11.2323 8.91921 11.2275 9.10738 11.0008C9.29547 10.774 9.28948 10.4115 9.09407 10.1913Z"
      fill="white"
    />
  </svg>
);
