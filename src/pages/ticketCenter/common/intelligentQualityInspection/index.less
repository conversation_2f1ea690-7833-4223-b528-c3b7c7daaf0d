.intelligentQualityInspectionContainer {
  margin: 20px;
  min-height: 89vh;
  .leftContainer {
    width: 57%;
    float: left;
    height: 89vh;
    overflow: hidden;
    overflow-y: scroll;
    /* 隐藏滚动条 */
    scrollbar-width: none;
    /* firefox */
    -ms-overflow-style: none;
    /* IE 10+ */
    font-family: 'Poppins', sans-serif !important;

    .workOrderDetailInfo {
      width: 100%;
      padding: 20px;
      //height: 183px;
      border-radius: 4px;
      background: #fff;
      box-shadow: 0px 2px 30px 0px rgba(0, 0, 0, 0.02);
      overflow: hidden;
      overflow-y: scroll;

      .titleDetail {
        width: 100%;
        height: 30px;

        .line {
          width: 4px;
          height: 28px;
          float: left;
          background: #3463fc;
        }

        .workOrderTitle {
          max-width: 200px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: #333;
          font-size: 18px;
          font-style: normal;
          font-weight: 400;
          margin-left: 8px;
          float: left;
          line-height: 30px;
          margin-bottom: 0px;
        }

        span {
          color: #333;
          font-size: 18px;
          font-style: normal;
          font-weight: 400;
          //margin-left: 8px;
          float: left;
          line-height: 30px;
        }

        .copyWordRecordCode {
          cursor: pointer;
          color: #333;
          font-size: 18px;
          font-style: normal;
          font-weight: 400;
        }

        .copyWordRecordCode:hover {
          cursor: pointer;
          color: #3463fc;
          font-size: 18px;
          font-style: normal;
          font-weight: 400;
        }

        .attentionIcon {
          width: 28px;
          height: 28px;
          float: left;
          margin-left: 12px;
          cursor: pointer;
        }
      }

      .workOrderDetailText {
        width: 100%;
        float: left;
        margin-top: 12px;

        .workOrderDetailItem {
          width: 33%;
          float: left;
          height: 30px;
          line-height: 30px;
          margin-bottom: 8px;

          p {
            width: 35%;
            text-align: right;
            float: left;
            margin-bottom: 0px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            margin-right: 5px;
          }
          .ticketTypeText {
            width: 55%;
            float: left;
            margin-bottom: 0px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            text-align: left;
          }

          .extIntsListDetail {
            width: 60%;
            text-align: left;
            float: left;
            margin-bottom: 0px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;

            :global {
              .ant-tag {
                font-size: 12px;
              }

              .ant-tag:first-child {
                margin-left: 0px;
              }
            }
          }

          .starImg {
            width: 16px;
            float: left;
            margin-right: 4px;
            margin-top: 5px;
          }

          .channelTypeImg {
            min-width: 60px;
            float: left;
            padding: 0px 8px;
            border-radius: 4px;
            background: rgba(52, 99, 252, 0.1);

            img {
              width: 14px;
              margin-right: 3px;
              float: left;
              margin-top: 8px;
            }

            span {
              color: #3463fc;
            }
          }

          .agentImg {
            img {
              width: 14px;
              margin-right: 3px;
              float: left;
              margin-top: 8px;
              border-radius: 90px;
            }
          }

          .priority1 {
            width: 35px;
            height: 26px;
            line-height: 26px;
            float: left;
            color: #13c825;
            border-radius: 4px;
            text-align: center;
            border: 1px solid rgba(19, 200, 37, 0.5);
            background: rgba(19, 200, 37, 0.15);
          }

          .priority3 {
            width: 35px;
            height: 26px;
            line-height: 26px;
            float: left;
            color: #3463fc;
            border-radius: 4px;
            text-align: center;
            border: 1px solid rgba(52, 99, 252, 0.5);
            background: rgba(52, 99, 252, 0.05);
          }

          .priority5 {
            width: 35px;
            height: 26px;
            line-height: 26px;
            float: left;
            color: #f22417;
            border-radius: 4px;
            text-align: center;
            border: 1px solid rgba(242, 36, 23, 0.5);
            background: rgba(242, 36, 23, 0.05);
          }

          :global {
            .ant-rate-star-first .anticon,
            .ant-rate-star-second .anticon {
              margin-top: 5px;
              vertical-align: revert;
            }
          }
        }
      }

      .intelligenceSummary {
        color: #fff;
        font-size: 12px;
        border-radius: 4px;
        border: none;
        margin-left: 16px;
        height: 29px;
        padding: 4px 10px;
        float: left;
        background: linear-gradient(56deg, #187cfc 20.15%, #7700fe 79.85%),
          linear-gradient(0deg, #fcb830 0%, #fcb830 100%), #3463fc;

        span {
          color: #fff;
          font-size: 12px;
          line-height: 20px;
        }
      }
    }
    .workOrderDetailBottom {
      width: 100%;
      margin-top: 20px;

      .contentSummaryContent {
        padding: 20px;
        border-radius: 4px;
        width: 100%;
        height: 260px;
        margin-bottom: 16px;
        background: #fff;
        box-shadow: 0px 2px 30px 0px rgba(0, 0, 0, 0.02);

        .leftDetail {
          width: 65%;
          float: left;

          .titleDetail {
            width: 100%;
            height: 30px;

            .line {
              width: 4px;
              height: 28px;
              float: left;
              background: #3463fc;
            }

            .workOrderTitle {
              max-width: 200px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              color: #333;
              font-size: 18px;
              font-style: normal;
              font-weight: 400;
              margin-left: 8px;
              float: left;
              line-height: 30px;
              margin-bottom: 0px;
            }

            span {
              color: #333;
              font-size: 18px;
              font-style: normal;
              font-weight: 400;
              //margin-left: 8px;
              float: left;
              line-height: 30px;
            }
            .recapBtn {
              font-size: 12px;
              color: #fff;
              float: right;
              border-radius: 4px;
              border: none;
              background: linear-gradient(56deg, #187cfc 20.15%, #7700fe 79.85%),
                linear-gradient(0deg, #3463fc 0%, #3463fc 100%),
                linear-gradient(97deg, #8500bb 0%, #3463fc 100%);
            }
          }

          .summarizeContentText {
            width: 100%;
            padding: 10px;
            border-radius: 4px;
            background: #f9f9f9;
            color: #333;
            font-size: 12px;
            line-height: 22px;
            height: 162px;
            margin-top: 16px;
            overflow: hidden;
            overflow-y: scroll;
          }

          :global {
            textarea.ant-input {
              margin-top: 16px;
              border-radius: 4px;
            }

            .ant-input[disabled] {
              border: none;
            }

            .ant-input[disabled] {
              color: #333;
            }
          }
        }

        .rightDetail {
          width: 33%;
          float: left;
          height: 100%;
          border-left: 1px solid #e6e6e6;
          padding-left: 20px;
          margin-left: 2%;

          .labelText {
            float: left;
            color: #333;
            font-size: 14px;
            margin-right: 12px;
          }

          .moodContent {
            width: 100%;
            height: 30px;
            margin-bottom: 20px;

            .moodIcon {
              float: left;
              width: 20px;
              margin-right: 12px;
              //cursor: pointer;
            }
          }

          .toDoTitle {
            width: 100%;
            height: 32px;

            .addBtn {
              float: right;
              border-radius: 4px;
              border: 1px solid #3463fc;
              color: #3463fc;
              font-size: 12px;

              :global {
                .ant-btn .anticon {
                  margin-right: 5px;
                }
              }
            }
          }

          .toDoContent {
            width: 100%;
            height: 120px;
            margin-top: 12px;
            overflow: hidden;
            overflow-y: scroll;
            /* 隐藏滚动条 */
            scrollbar-width: none;
            /* firefox */
            -ms-overflow-style: none;

            /* IE 10+ */
            .toDoInput {
              width: 100%;
              border-radius: 6px;
              border: 1px solid #e6e6e6;
              margin-bottom: 12px;
              box-shadow: none;
            }

            :global {
              .ant-checkbox-wrapper {
                width: 90%;
                float: left;
              }

              .deleteTodoIcon {
                width: 20px;
                height: 20px;
                float: right;
              }

              .ant-checkbox-group {
                width: 100%;
              }

              .ant-row {
                margin-bottom: 10px;
                cursor: pointer;
              }

              .ant-checkbox + span {
                color: #333;
                font-size: 12px;
                //text-overflow: ellipsis;
                //overflow: hidden;
                //white-space: nowrap;
                width: 100%;
                //height: 18px;
              }

              .ant-checkbox-wrapper-checked {
                .ant-checkbox + span {
                  text-decoration-line: line-through;
                  color: #999;
                }
              }

              .ant-checkbox-checked .ant-checkbox-inner {
                background-color: #999;
                border-color: #999;
              }

              .ant-checkbox-checked::after {
                border: 1px solid #999 !important;
              }
            }
          }

          .toDoContent::-webkit-scrollbar {
            display: none;
            /* Chrome Safari */
          }
        }
      }

      .noDataImg {
        width: 100%;
        text-align: center;

        img {
          width: 50%;
          margin-top: 10%;
        }

        p {
          font-size: 14px;
          color: rgba(4, 15, 36, 0.35);
          margin-top: 16px;
        }
      }
    }
    .tabContent {
      padding: 20px;
      border-radius: 4px;
      background: #fff;
      box-shadow: 0px 2px 30px 0px rgba(0, 0, 0, 0.02);
      position: relative;
      margin-top: 20px;

      .workOrderRefreshIcon {
        width: 24px;
        position: absolute;
        top: 26px;
        right: 20px;
        cursor: pointer;
      }

      .tableItem {
        height: 500px;
        width: 100%;
        overflow: hidden;
        overflow-y: scroll;

        .operationContent {
          .details {
            margin-right: 8px;
          }

          .details,
          .more {
            float: left;
            color: #3463fc;
            font-size: 12px;
            margin-bottom: 0px;
            cursor: pointer;

            img {
              width: 12px;
              float: left;
              margin-right: 4px;
              margin-top: 3px;
            }
          }
        }

        .operationContent {
          .details {
            margin-right: 8px;
          }

          .details,
          .more {
            float: left;
            color: #3463fc;
            font-size: 12px;
            margin-bottom: 0px;
            cursor: pointer;

            img {
              width: 12px;
              float: left;
              margin-right: 4px;
              margin-top: 3px;
            }
          }
        }

        .channelName {
          img {
            width: 12px;
            height: 12px;
            float: left;
            margin-top: 3px;
            margin-right: 5px;
          }
        }

        .priority1 {
          width: 35px;
          height: 24px;
          line-height: 24px;
          color: #13c825;
          border-radius: 4px;
          text-align: center;
          border: 1px solid rgba(19, 200, 37, 0.5);
          background: rgba(19, 200, 37, 0.15);
        }

        .priority2 {
          width: 35px;
          height: 24px;
          line-height: 24px;
          color: #eae832;
          text-align: center;
          border-radius: 4px;
          border: 1px solid rgba(234, 232, 50, 0.5);
          background: rgba(234, 232, 50, 0.15);
        }

        .priority3 {
          width: 35px;
          height: 24px;
          line-height: 24px;
          color: #fcb830;
          border-radius: 4px;
          text-align: center;
          border: 1px solid rgba(252, 184, 48, 0.5);
          background: rgba(252, 184, 48, 0.15);
        }

        .priority4 {
          width: 35px;
          height: 24px;
          line-height: 24px;
          color: #f26817;
          border-radius: 4px;
          text-align: center;
          border: 1px solid rgba(242, 104, 23, 0.5);
          background: rgba(242, 104, 23, 0.15);
        }

        .priority5 {
          width: 35px;
          height: 24px;
          line-height: 24px;
          color: #f22417;
          border-radius: 4px;
          text-align: center;
          border: 1px solid rgba(242, 36, 23, 0.5);
          background: rgba(242, 36, 23, 0.15);
        }

        .serviceObjectivesText1 {
          color: #f22417;
        }

        .serviceObjectivesText2 {
          color: #3463fc;
        }

        .serviceObjectivesText3 {
          color: #13c825;
        }

        .serviceObjectivesText4 {
          color: #fcb830;
        }

        .state1 {
          color: #13c825;

          .circle {
            width: 6px;
            height: 6px;
            border-radius: 90px;
            background-color: #13c825;
            float: left;
            margin-right: 5px;
            margin-top: 6px;
          }
        }

        .state2 {
          color: #333333;

          .circle {
            width: 6px;
            height: 6px;
            border-radius: 90px;
            background-color: #f22417;
            float: left;
            margin-right: 5px;
            margin-top: 6px;
          }
        }

        .state3 {
          color: #333333;

          .circle {
            width: 6px;
            height: 6px;
            border-radius: 90px;
            background-color: #3463fc;
            float: left;
            margin-right: 5px;
            margin-top: 6px;
          }
        }

        .state4 {
          color: #333333;

          .circle {
            width: 6px;
            height: 6px;
            border-radius: 90px;
            background-color: #333333;
            float: left;
            margin-right: 5px;
            margin-top: 6px;
          }
        }

        .state5 {
          color: #333333;

          .circle {
            width: 6px;
            height: 6px;
            border-radius: 90px;
            background-color: #fcb830;
            float: left;
            margin-right: 5px;
            margin-top: 6px;
          }
        }
      }

      :global {
        .ant-tabs-tab {
          padding: 8px 4px;
          color: #999;
        }

        .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
          color: #3463fc;
          font-weight: 400;
        }

        .ant-tabs-top > .ant-tabs-nav .ant-tabs-ink-bar,
        .ant-tabs-bottom > .ant-tabs-nav .ant-tabs-ink-bar,
        .ant-tabs-top > div > .ant-tabs-nav .ant-tabs-ink-bar,
        .ant-tabs-bottom > div > .ant-tabs-nav .ant-tabs-ink-bar {
          height: 1px;
        }

        .ant-tabs-ink-bar {
          background-color: #3463fc;
        }

        .ant-tabs-content-holder {
          width: 100%;
          height: 500px;
          overflow: hidden;
          overflow-y: scroll;
          /* 隐藏滚动条 */
          scrollbar-width: none;
          /* firefox */
          -ms-overflow-style: none;
          /* IE 10+ */
        }

        .ant-tabs-content-holder::-webkit-scrollbar {
          display: none;
          /* Chrome Safari */
        }

        .ant-table-cell {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          word-break: keep-all;
        }
      }
    }

    .notesContent {
      width: 100%;
      float: left;

      .noteTitle {
        width: 100%;
        padding: 0px 20px;
        background: #f3f7fe;
        height: 40px;
        line-height: 40px;

        img {
          width: 16px;
          float: left;
          margin-right: 8px;
          margin-top: 12px;
        }

        span {
          font-size: 12px;
          color: #333;
        }

        .timeText {
          float: right;
          font-size: 12px;
          color: rgba(4, 15, 36, 0.35);
        }
      }

      .noteDetail {
        width: 100%;
        float: left;
        padding: 12px 20px;
        font-size: 12px;
        color: #333;
      }
    }

    .noDataImg {
      width: 100%;
      text-align: center;

      img {
        width: 50%;
        margin-top: 10%;
      }

      p {
        font-size: 14px;
        color: rgba(4, 15, 36, 0.35);
        margin-top: 16px;
      }
    }
  }
  .leftContainer::-webkit-scrollbar {
    display: none;
    /* Chrome Safari */
  }
  .rightContainer {
    width: 42%;
    height: 89vh;
    float: left;
    margin-left: 1%;
    padding: 20px;
    background: #fff;
    box-shadow: 0px 2px 30px 0px rgba(0, 0, 0, 0.02);
    .rightTitle {
      width: 100%;
      height: 30px;
      margin-bottom: 20px;
      .line {
        width: 4px;
        height: 28px;
        float: left;
        background: #3463fc;
      }

      .workOrderTitle {
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #333;
        font-size: 18px;
        font-style: normal;
        font-weight: 400;
        margin-left: 8px;
        float: left;
        line-height: 30px;
        margin-bottom: 0px;
      }
    }
    .rightBottomContainer {
      width: 100%;
      height: 75vh;
      overflow: hidden;
      overflow-y: scroll;
      /* 隐藏滚动条 */
      scrollbar-width: none;
      /* firefox */
      -ms-overflow-style: none;
      /* IE 10+ */
      font-family: 'Poppins', sans-serif !important;

      .formLine {
        width: 100%;
        height: 1px;
        background: linear-gradient(
          to right,
          rgba(52, 99, 252, 0.3),
          rgba(52, 99, 252, 1),
          rgba(52, 99, 252, 0.3)
        );
      }
      .ruleContainer {
        width: 100%;
        margin-top: 20px;
        .levelItemContainer {
          width: 100%;
        }
        .levelTitle {
          width: 100%;
          height: 50px;
          line-height: 40px;
          padding: 0px 0px 14px 0px;
          //cursor: pointer;
          border-bottom: 1px solid rgba(230, 230, 230, 0.3);
          .progressIcon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 1px solid #e6e6e6;
            float: left;
            //margin-left:-2%;
            margin-top: 12px;
            margin-right: 12px;
          }
          .progressStatusIcon {
            float: left;
            margin-top: 4px;
            margin-right: 12px;
          }
          .levelName {
            color: #333;
            font-size: 16px;
            font-style: normal;
            font-weight: 700;
            line-height: 40px;
            margin-bottom: 0px;
            width: 70%;
            float: left;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .progressNum {
            color: #333;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 40px;
            float: right;
            margin-right: 10%;
            margin-top: 5px;
          }
          .arrowIcon {
            float: right;
            margin-left: 10px;
          }
        }
        .levelTitleSecond {
          width: 100%;
          height: 40px;
          line-height: 30px;
          padding: 0px 0px 8px 0px;
          //cursor: pointer;
          border-bottom: 1px solid rgba(230, 230, 230, 0.3);
          //border-top: 1px solid rgba(230, 230, 230, 0.30);
          .progressIcon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 1px solid #e6e6e6;
            float: left;
            //margin-left:-2%;
            margin-top: 12px;
            margin-right: 12px;
          }
          .progressStatusIcon {
            float: left;
            margin-top: 8px;
            margin-right: 12px;
          }
          .levelName {
            color: #333;
            font-size: 14px;
            font-style: normal;
            font-weight: 700;
            line-height: 40px;
            margin-bottom: 0px;
            width: 70%;
            float: left;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .progressNum {
            color: #333;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 40px;
            float: right;
            margin-right: 10%;
          }
          .arrowIcon {
            float: right;
            margin-left: 10px;
          }
        }
        .levelTitleThird {
          width: 100%;
          height: 40px;
          line-height: 30px;
          padding: 0px 0px 8px 0px;
          //cursor: pointer;
          border-bottom: 1px solid rgba(230, 230, 230, 0.3);
          //border-top: 1px solid rgba(230, 230, 230, 0.30);
          .progressIcon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 1px solid #e6e6e6;
            float: left;
            //margin-left:-2%;
            margin-top: 12px;
            margin-right: 12px;
          }
          .progressStatusIcon {
            float: left;
            margin-top: 8px;
            margin-right: 12px;
          }
          .levelName {
            color: #333;
            font-size: 13px;
            font-style: normal;
            font-weight: 700;
            line-height: 40px;
            margin-bottom: 0px;
            width: 70%;
            float: left;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .progressNum {
            color: #333;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 40px;
            float: right;
            margin-right: 10%;
          }
          .arrowIcon {
            float: right;
            margin-left: 10px;
          }
        }
        .levelContent {
          width: 98%;
          margin-left: 2%;
          .ruleContent {
            width: 100%;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(230, 230, 230, 0.3);
            display: grid;
            .ruleDetailItem {
              width: 100%;
              //margin-top: 14px;
              flex: auto;
              .ruleDetailItemHeader {
                width: 100%;
                //display: flex;
                //height: 20px;
                .ruleIcon {
                  margin-right: 5px;
                  float: left;
                  img {
                    width: 16px;
                  }
                }
                .ruleTitleText {
                  width: 90%;
                  margin-bottom: 0px;
                  //overflow: hidden;
                  //white-space: nowrap;
                  //text-overflow: ellipsis;
                  color: #333;
                  font-size: 12px;
                  font-style: normal;
                  font-weight: 700;
                  line-height: 16px;
                }
                .ruleTitleContainer {
                  width: 95%;
                  display: flex;
                }

                .pointItemTitle {
                  color: #666;
                  font-size: 12px;
                  font-style: normal;
                  font-weight: 700;
                  margin-bottom: 5px;
                  margin-top: 5px;
                }
                .pointItemText {
                  color: #666;
                  font-size: 12px;
                  font-style: normal;
                  font-weight: 400;
                  margin-bottom: 5px;
                }
                .pointItemRule {
                  color: #333;
                  font-size: 12px;
                  font-style: normal;
                  font-weight: 400;
                }

                :global {
                  .ant-collapse > .ant-collapse-item > .ant-collapse-header {
                    width: 100%;
                  }
                  .ant-collapse-ghost > .ant-collapse-item {
                    padding: 2% 3%;
                  }
                  .ant-collapse-ghost
                    > .ant-collapse-item.ant-collapse-item-active {
                    border-radius: 4px;
                    background: #f9f9f9;
                    margin-bottom: 14px;
                  }
                  .ant-collapse-content > .ant-collapse-content-box {
                    padding: 0 21px;
                  }
                }
              }
              .ruleDetailItemContent {
                width: 100%;
                padding-left: 20px;
                height: 32px;
                line-height: 32px;
                //margin-top: 14px;
                .resultItem {
                  width: 100%;
                  height: 32px;
                  margin-bottom: 2px;
                  .aiIcon {
                    float: left;
                    margin-right: 4px;
                    img {
                      width: 16px;
                      float: left;
                      margin-top: 6px;
                      margin-left: 2px;
                    }
                  }
                  .resultContainer {
                    float: left;
                    color: #333;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    .aiIcon {
                      float: left;
                      margin-right: 4px;
                      img {
                        width: 16px;
                        float: left;
                        margin-top: 6px;
                        margin-left: 2px;
                      }
                    }
                    .operatorText {
                      float: left;
                      color: #3463fc;
                      font-size: 12px;
                      font-style: normal;
                      font-weight: 700;
                      margin: 0px 2px;
                    }
                    .scopeNumContainer {
                      float: left;
                      color: #333;
                      font-size: 12px;
                      font-style: normal;
                      font-weight: 700;
                      text-decoration-line: underline;
                      cursor: pointer;
                    }
                    .scopeNumAgentContainer {
                      float: left;
                      color: #333;
                      font-size: 12px;
                      font-style: normal;
                      font-weight: 700;
                    }
                    .scopeInput {
                      float: left;
                      :global {
                        .ant-input-number {
                          border-radius: 6px !important;
                          background: #fff;
                          box-shadow: none;
                          font-size: 12px;
                          width: 100%;
                        }
                        .ant-input-number-handler-wrap {
                          border-radius: 0px 6px 6px 0px;
                        }
                        .ant-input-number-handler-down {
                          border-bottom-right-radius: 6px;
                        }
                        .ant-input-number-handler-up {
                          border-top-right-radius: 6px;
                        }
                      }
                    }
                  }
                  .cancelScopeIcon {
                    width: 16px;
                    cursor: pointer;
                    margin-left: 3px;
                  }
                  .resultCompleteContainer {
                    float: left;
                    color: #999;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    text-decoration: line-through;
                  }
                }
                .evaluationRemarks {
                  width: 100%;
                  float: left;
                  .evaluationRemarksTitle {
                    font-weight: 700;
                    margin-bottom: 0px;
                    margin-top: 10px;
                  }
                  p {
                    margin-bottom: 4px;
                    color: #333;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                  }
                  :global {
                    .ant-input {
                      height: 32px !important;
                      line-height: 32px;
                      background: #fff;
                      font-size: 12px;
                      box-shadow: none;
                      width: 85%;
                      float: left;
                      border-radius: 6px 0px 0px 6px;
                      border-right: none;
                    }
                    .ant-btn.ant-btn-primary {
                      width: 15%;
                      float: left;
                      border-radius: 0px 6px 6px 0px;
                      padding: inherit;
                    }
                  }
                }
                .radioContainer {
                  width: 100%;
                  margin-bottom: 5px;
                }
                .evaluationReferenceStandards {
                  width: 100%;
                  border-radius: 4px;
                  border-left: 1px solid #999;
                  background: #f9f9f9;
                  padding: 4px 10px;
                  p {
                    color: #333;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    margin-bottom: 0px;
                  }
                  div {
                    color: #999;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 18px;
                  }
                }
              }
            }
          }
        }
      }

      .qualityInspectionResultScore {
        width: 100%;
        margin-top: 20px;
        .totalScopeTitle {
          color: #333;
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
        }
        .scopeText {
          width: 100%;
          color: #30cf40;
          text-align: center;
          font-size: 70px;
          font-style: normal;
          font-weight: 700;
          span {
            color: #30cf40;
            font-size: 20px;
            font-style: normal;
            font-weight: 700;
          }
        }
      }
      .qualityInspectionOverview {
        width: 100%;
        margin-top: 20px;
        .totalScopeTitle {
          color: #333;
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
        }
        .evaluationText {
          color: #333;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }
      }

      :global {
        .ant-form-item-label > label {
          color: #333;
          font-size: 12px;
          font-style: normal;
          font-weight: 700;
        }
        .ant-select:not(.ant-select-customize-input) .ant-select-selector {
          border-radius: 6px;
          background: #fff;
          box-shadow: none;
          font-size: 12px;
        }
        .ant-collapse-icon-position-end
          > .ant-collapse-item
          > .ant-collapse-header {
          padding: 5px 0px;
        }
        .ant-collapse
          > .ant-collapse-item
          .ant-collapse-header-collapsible-only
          .ant-collapse-header-text {
          flex: auto;
        }
        .ant-collapse-content > .ant-collapse-content-box {
          padding: 0px;
        }
      }
    }
    .rightBottomContainer::-webkit-scrollbar {
      display: none;
      /* Chrome Safari */
    }
    .rightFooter {
      width: 100%;
      height: 5vh;
      .scopeContainer {
        float: left;
        color: #333;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 4vh;
        .scopeText {
          color: #13c825;
          font-size: 16px;
          font-style: normal;
          font-weight: 700;
          line-height: 4vh;
        }
      }
      .exportContainer {
        float: right;
        line-height: 4vh;
        cursor: pointer;
        img {
          width: 16px;
        }
        span {
          color: #3463fc;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }
      }
      :global {
        .ant-btn.ant-btn-primary {
          float: right;
          margin-left: 20px;
        }
      }
    }
  }
}
