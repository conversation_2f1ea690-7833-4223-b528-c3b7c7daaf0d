import React, { useState, useRef, useEffect } from 'react';
import {
  Button,
  Checkbox,
  Col,
  Form,
  Input,
  message,
  notification,
  Popover,
  Rate,
  Row,
  Steps,
  Switch,
  Table,
  Tabs,
  Tag,
  Select,
  InputNumber,
  Radio,
  Space,
  Spin,
  Collapse,
  Tooltip,
} from 'antd';
import styles from './index.less';
import {
  useDispatch,
  getIntl,
  history,
  FormattedMessage,
  useSelector,
} from 'umi';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import WorkReplayDetailContent from '../workOrderManagement/workReplayDetail';
import ContactCustomersContent from '../workOrderManagement/contactCustomers';
import TicketDetailComponent from '../../../../components/ticketDetailComponent';

import DefaultUserName from '../../../../assets/user-name.png';
import MoodGoodActiveIcon from '../../../../assets/mood-good-active.png';
import MoodGoodIcon from '../../../../assets/mood-good.png';
import MoodNormalActiveIcon from '../../../../assets/mood-normal-active.png';
import MoodNormalIcon from '../../../../assets/mood-normal.png';
import MoodBadActiveIcon from '../../../../assets/mood-bad-active.png';
import MoodBadIcon from '../../../../assets/mood-bad.png';
import WorkOrderRefreshIcon from '../../../../assets/work-order-refresh.png';
import TableEmailIcon from '../../../../assets/table-email-icon.png';
import TablePhoneIcon from '../../../../assets/table-phone-icon.png';
import TableInfoIcon from '../../../../assets/table-info-icon.png';
import NewFaceBookIcon from '../../../../assets/new-facebook-icon.svg';
import WhatsAppIcon from '../../../../assets/whats-app.svg';
import ChatIcon from '../../../../assets/chat-icon.jpg';
import AppChatOutlinedIcon from '../../../../assets/AppChatOutlined.svg';
import WebVideoOutlinedIcon from '../../../../assets/WebVideoOutlined.svg';
import AppVideoOutlinedIcon from '../../../../assets/AppVideoOutlined.svg';
import AwsChannelIcon from '../../../../assets/aws-channel-icon.svg';
import NewInstagramIcon from '../../../../assets/ins.svg';
import LineIcon from '../../../../assets/line.svg';
import NewWeComIcon from '../../../../assets/new-wecom-icon.svg';
import NewWechatOfficialAccountIcon from '../../../../assets/new-wechat-official-account-icon.svg';
import NewWebOnlineVoiceIcon from '../../../../assets/web-online-voice-icon.svg';
import NewAppOnlineVoiceIcon from '../../../../assets/app-online-voice-icon.svg';
import TwitterIcon from '../../../../assets/twitter.svg';
import NewTelegramIcon from '../../../../assets/new-telegram-icon.svg';
import NewWeChatMiniProgramIcon from '../../../../assets/new-wechat-mini-program-icon.svg';
import NewShopifyIcon from '../../../../assets/new-shopify-icon.svg';
import NewGooglePlayIcon from '../../../../assets/google-play-icon.svg';
import TableDetailIcon from '../../../../assets/table-details.svg';
import NoDataImg from '../../../../assets/no-data-img.jpg';
import RuleLoadingIcon from '../../../../assets/rule-loading.gif';
import CancelScopeIcon from '../../../../assets/cancel-scope-icon.jpg';
import {
  AgentIcon,
  AgentRuleIcon,
  AiScopeIcon,
  CompleteIcon,
  DownArrowIcon,
  EvaluationErrorIcon,
  ExportIcon,
  UnderEvaluationIcon,
} from './icon';
import NewAiScopeIcon from '../../../../assets/ai-scope-icon.png';
const { Panel } = Collapse;

// 辅助函数：获取所有祖先分类 ID
const getAllAncestorCategoryIds = (categories, targetCategoryId, path = []) => {
  for (const category of categories) {
    const currentCategoryIdStr = category.categoryId.toString();
    const newPath = [...path, currentCategoryIdStr];
    if (currentCategoryIdStr === targetCategoryId.toString()) {
      return newPath;
    }
    if (category.children && category.children.length > 0) {
      const foundPath = getAllAncestorCategoryIds(
        category.children,
        targetCategoryId,
        newPath,
      );
      if (foundPath) {
        return foundPath;
      }
    }
  }
  return null;
};

const IntelligentQualityInspection = () => {
  const ws = useRef(null);
  const dispatch = useDispatch();
  const formRef = useRef(null);
  const heartbeatIntervalRef = useRef(null);
  const evaluationRecordResultRef = useRef({});
  const { user } = useSelector(({ layouts }) => ({
    user: layouts.user,
  }));
  const [ticketId, setTicketId] = useState('');
  const [workOrderDetail, setWorkOrderDetail] = useState({});
  const [workRecordTypeList, setWorkRecordTypeList] = useState([]);
  const [summarizeStatus, setSummarizeStatus] = useState('');
  const [customerMood, setCustomerMood] = useState(1);
  const [contentSummary, setContentSummary] = useState('');
  const [waitExecuteList, setWaitExecuteList] = useState([]);
  const [newExtIntsListDetail, setNewExtIntsListDetail] = useState([]);
  const [tabKey, setTabKey] = useState(1);
  const [roleId, setRoleId] = useState('');
  const [userId, setUserId] = useState('');
  const [stateNumber, setStateNumber] = useState(1);
  const [ticketRemarksList, setTicketRemarksList] = useState([]);
  const [loadingTable, setLoadingTable] = useState(false);
  const [loading, setLoading] = useState(false);
  const [associatedLoading, setAssociatedLoading] = useState(false);
  const [pageSize, setPageSize] = useState(10);
  const [pageNum, setPageNum] = useState(1);
  const [total, setTotal] = useState(0);
  const [
    historyCustomerWorkOrderList,
    setHistoryCustomerWorkOrderList,
  ] = useState([]);
  const [
    associationWorkOrderTableList,
    setAssociationWorkOrderTableList,
  ] = useState([]);
  const [showInput, setShowInput] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [radioValue, setRadioValue] = useState(0);
  const [evaluationList, setEvaluationList] = useState([]);
  const [ticketDetailLoading, setTicketDetailLoading] = useState(false);
  const [rightLoading, setRightLoading] = useState(false);
  const [evaluationLoading, setEvaluationLoading] = useState(false);
  const [activePanelKey, setActivePanelKey] = useState([]); // 将 null 改为 []
  const [evaluationRecordData, setEvaluationRecordData] = useState({});
  const [evaluationRecordResult, setEvaluationRecordResult] = useState({});
  // 当前工单评估状态--是否走查询评估详情接口
  const [assessmentStatus, setAssessmentStatus] = useState('');
  // AI质检已完成展示完成评估按钮
  const [showBtn, setShowBtn] = useState(false);
  const [assessmentRecordId, setAssessmentRecordId] = useState('');
  const [assessorId, setAssessorId] = useState('');
  // 评估人ID
  const [evaluatorID, setEvaluatorID] = useState('');
  const [assessmentLoading, setAssessmentLoading] = useState(false);

  useEffect(() => {
    queryWorkRecordType();
    getUser();
  }, []);
  useEffect(() => {
    if (Object.keys(history.location.query).length > 0) {
      let ticketId = history.location.query.ticketId;
      let assessmentStatus = history.location.query?.assessmentStatus;
      let assessorId = history.location.query?.assessorId;
      let assessmentRecordId = history.location.query?.assessmentRecordId;
      setTicketId(ticketId);
      if (assessmentStatus) {
        setAssessmentStatus(assessmentStatus);
      }
      if (assessorId && assessmentRecordId) {
        setAssessorId(assessorId);
        setEvaluatorID(assessmentRecordId);
      }
    }
  }, [history.location?.query]);
  useEffect(() => {
    if (ticketId) {
      queryWorkOrderDetail();
    }
  }, [ticketId]);

  // 查询质检信息
  const assessmentRecordByTicket = workRecordId => {
    let params = {
      assessorId: assessorId,
      ticketId: workRecordId,
      assessmentRecordId: evaluatorID,
    };
    dispatch({
      type: 'workOrderCenter/assessmentRecordByTicket',
      payload: params,
      callback: response => {
        if (response.code === 200) {
          let newData = response.data;
          if (newData) {
            setEvaluationRecordData(newData);
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 查询roleId
  const getUser = () => {
    dispatch({
      type: 'layouts/getUser1',
      callback: response => {
        if (response.code === 200) {
          let roleList = response.data?.roleList;
          let roleId = roleList[0].roleId;
          setRoleId(roleId);
          setUserId(response.data?.userId);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 查询工单详情
  const queryWorkOrderDetail = () => {
    setTicketDetailLoading(true);
    dispatch({
      type: 'workOrderCenter/queryWorkOrderDetail',
      payload: ticketId,
      callback: response => {
        setTicketDetailLoading(false);
        if (response.code === 200) {
          if (response.data) {
            setWorkOrderDetail(response.data);
            const workOrderDetail = { ...response.data };
            dispatch({
              type: 'workOrderCenter/queryWorkOrderDetailList',
              payload: {
                workOrderDetail: workOrderDetail,
                roleId: roleId,
              },
            });

            let waitExecuteList = workOrderDetail.waitExecuteList;

            // 扩展字段数据组装
            // 扩展字段值
            // let newExtIntsListDetail = [...workOrderDetail.extIntsList];
            let newExtIntsListDetail = workOrderDetail.extIntsList.map(
              item => ({ ...item }),
            );
            // 所有扩展字段列表
            let newDefListDetail = [...workOrderDetail.defList];
            if (newDefListDetail) {
              if (newExtIntsListDetail) {
                for (let i = 0; i < newExtIntsListDetail.length; i++) {
                  for (let n = 0; n < newDefListDetail.length; n++) {
                    if (
                      newExtIntsListDetail[i].workRecordExtCode ==
                      newDefListDetail[n].workRecordExtDefCode
                    ) {
                      newExtIntsListDetail[i].workRecordExtDefName =
                        newDefListDetail[n].workRecordExtDefName;
                      newExtIntsListDetail[i].propertyTypeId =
                        newDefListDetail[n].propertyTypeId;
                      if (newDefListDetail[n].propertyTypeId == '1003') {
                        // 1003,单选下拉框
                        let workOrderExtOptionDefList =
                          newDefListDetail[n].workOrderExtOptionDefList;
                        let newOptionId =
                          newExtIntsListDetail[i].workRecordExtValue;
                        for (
                          let m = 0;
                          m < workOrderExtOptionDefList.length;
                          m++
                        ) {
                          if (
                            newOptionId ==
                            workOrderExtOptionDefList[m].optionvalue
                          ) {
                            newExtIntsListDetail[i].workRecordExtValue =
                              workOrderExtOptionDefList[m].optionName;
                          }
                        }
                      } else if (newDefListDetail[n].propertyTypeId == '1004') {
                        // 1004,多选下拉框
                        let workOrderExtOptionDefList =
                          newDefListDetail[n].workOrderExtOptionDefList;
                        let newOptionId =
                          newExtIntsListDetail[i].workRecordExtValue;
                        const newOptionIdArray = newOptionId?.split(',');
                        const matchedOptionNames = newOptionIdArray.map(id => {
                          const foundOption = workOrderExtOptionDefList.find(
                            option => option.optionValue === id,
                          );
                          return foundOption ? foundOption.optionName : null;
                        });
                        newExtIntsListDetail[
                          i
                        ].workRecordExtValue = matchedOptionNames;
                      } else if (newDefListDetail[n].propertyTypeId == '1005') {
                        // 1005,单选框
                        let workOrderExtOptionDefList =
                          newDefListDetail[n].workOrderExtOptionDefList;
                        let newOptionId =
                          newExtIntsListDetail[i].workRecordExtValue;
                        for (
                          let m = 0;
                          m < workOrderExtOptionDefList.length;
                          m++
                        ) {
                          if (
                            newOptionId ==
                            workOrderExtOptionDefList[m].optionValue
                          ) {
                            newExtIntsListDetail[i].workRecordExtValue =
                              workOrderExtOptionDefList[m].optionName;
                          }
                        }
                      } else if (newDefListDetail[n].propertyTypeId == '1006') {
                        // 1006,多选框
                        let workOrderExtOptionDefList =
                          newDefListDetail[n].workOrderExtOptionDefList;
                        let newOptionId =
                          newExtIntsListDetail[i].workRecordExtValue;
                        const newOptionIdArray = newOptionId?.split(',');
                        const matchedOptionNames = newOptionIdArray.map(id => {
                          const foundOption = workOrderExtOptionDefList.find(
                            option => option.optionValue === id,
                          );
                          return foundOption ? foundOption.optionName : null;
                        });
                        newExtIntsListDetail[
                          i
                        ].workRecordExtValue = matchedOptionNames;
                      } else if (newDefListDetail[n].propertyTypeId == '1007') {
                        // 1007,开关
                      } else if (newDefListDetail[n].propertyTypeId == '1009') {
                        // 1009,时间范围选择
                        let newOptionId =
                          newExtIntsListDetail[i].workRecordExtValue;
                        const formattedDateString = newOptionId.replace(
                          ',',
                          ` ${getIntl().formatMessage({
                            id: 'marketing.activities.time.line',
                          })} `,
                        );
                        newExtIntsListDetail[
                          i
                        ].workRecordExtValue = formattedDateString;
                      }
                    }
                  }
                }
              }
            }

            setSummarizeStatus(workOrderDetail.summarizeStatus);
            setCustomerMood(workOrderDetail.customerMood);
            setContentSummary(workOrderDetail.contentSummary);
            setWaitExecuteList(waitExecuteList);
            setNewExtIntsListDetail(newExtIntsListDetail);

            queryWorkRecordInfo(workOrderDetail);
            availableAssessmentListByTicket(workOrderDetail);
            assessmentRecordByTicket(ticketId);
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 查询可用评估下拉列表
  const availableAssessmentListByTicket = workOrderDetail => {
    setRightLoading(true);
    let params = {
      ticketType: workOrderDetail.workRecordTypeId
        ? workOrderDetail.workRecordTypeId
        : '',
      // channelTypeId: workOrderDetail.channelTypeId,
      channelConfigId: workOrderDetail.channelConfigId,
    };
    dispatch({
      type: 'workOrderCenter/availableAssessmentListByTicket',
      payload: params,
      callback: response => {
        setRightLoading(false);
        if (response.code === 200) {
          if (response.data) {
            setEvaluationList(response.data);
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询工单聊天记录
  const queryWorkRecordInfo = workOrderDetail => {
    let params = {
      workRecordId: workOrderDetail.workRecordId,
      channelTypeId: workOrderDetail.channelTypeId,
      chatVoice: workOrderDetail.chatVoice,
    };
    dispatch({
      type: 'workOrderCenter/queryWorkRecordInfo',
      payload: params,
    });
  };
  // 查询工单类型列表
  const queryWorkRecordType = () => {
    dispatch({
      type: 'workOrderCenter/queryWorkRecordType',
      callback: response => {
        if (response.code === 200) {
          let workRecordTypeList = response.data;
          if (workRecordTypeList) {
            setWorkRecordTypeList(workRecordTypeList);
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  const getTypeNameById = id => {
    const result = workRecordTypeList?.find(
      item => item.workRecordTypeId === id,
    );
    return result ? result.workRecordTypeName : null;
  };

  // 切换tab
  const onChangeTab = key => {
    if (key === 1) {
      queryWorkOrderDetail(workOrderDetail.workRecordId, 1);
    } else if (key === 2) {
      setStateNumber(stateNumber + 1);
    } else if (key === 4) {
      setLoadingTable(true);
      queryHistoryCustomerWorkOrder(1, 10);
    } else if (key === 3) {
      setLoadingTable(true);
      queryAssociationWorkOrderTable(1, 10);
    } else if (key === 5) {
      queryByTicketRemarks(workOrderDetail.workRecordId);
    }
    setTabKey(key);
  };

  // 查询客户历史工单
  const queryHistoryCustomerWorkOrder = (pageNum, pageSize) => {
    let params = {
      customerId: workOrderDetail.customerId,
      pageNum: pageNum,
      pageSize: pageSize,
    };
    dispatch({
      type: 'workOrderCenter/queryHistoryCustomerWorkOrder',
      payload: params,
      callback: response => {
        setLoadingTable(false);
        if (response.code === 200) {
          let historyCustomerWorkOrderList = response.data.records;
          let total = response.data.total;
          setHistoryCustomerWorkOrderList(historyCustomerWorkOrderList);
          setTotal(total);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询关联工单
  const queryAssociationWorkOrderTable = (pageNum, pageSize) => {
    let params = {
      workRecordId: workOrderDetail.workRecordId,
      workRecordInfo: '',
      pageSize: pageSize,
      pageNum: pageNum,
    };
    dispatch({
      type: 'workOrderCenter/queryAssociationWorkOrderTable',
      payload: params,
      callback: response => {
        setLoadingTable(false);
        setAssociatedLoading(false);
        if (response.code === 200) {
          let associationWorkOrderTableList = response.data.records;
          for (let i = 0; i < associationWorkOrderTableList.length; i++) {
            associationWorkOrderTableList[i]['key'] =
              associationWorkOrderTableList[i].workRecordId;
          }
          setAssociationWorkOrderTableList(associationWorkOrderTableList);
          setTotal(response.data.total);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询工单备注列表接口
  const queryByTicketRemarks = workRecordId => {
    dispatch({
      type: 'workOrderCenter/queryByTicketRemarks',
      payload: workRecordId,
      callback: response => {
        setLoadingTable(false);
        if (response.code === 200) {
          let ticketRemarksList = response.data;
          setTicketRemarksList(ticketRemarksList);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  useEffect(() => {
    if (
      Object.keys(evaluationRecordData).length > 0 &&
      Object.keys(workOrderDetail).length > 0
    ) {
      if (
        (evaluationRecordData?.assessmentRecordId &&
          evaluationRecordData?.assessmentStatus === 0) ||
        evaluationRecordData?.assessmentStatus === 1
      ) {
        formRef.current?.setFieldsValue({
          assessmentId: evaluationRecordData?.assessmentId,
        });
        let params = {
          assessmentId: evaluationRecordData?.assessmentId,
          newData: evaluationRecordData,
        };
        onFinish(params, workOrderDetail, 'evaluated');
      } else if (
        evaluationRecordData?.assessmentRecordId &&
        evaluationRecordData?.assessmentStatus === 2 &&
        assessmentStatus
      ) {
        evaluationListDetail(
          evaluationRecordData?.assessmentId,
          evaluationRecordData?.assessmentRecordId,
        );
      }
    }
  }, [evaluationRecordData, workOrderDetail]);
  // 开始评估
  const onFinish = (values, workOrderDetail, status) => {
    setRightLoading(true);
    let newValue = {};
    if (status === 'first') {
      let assessmentId = values.assessmentId;
      const matchedData =
        evaluationList &&
        evaluationList.find(item => item.assessmentId === assessmentId);
      newValue = {
        assessmentId: values?.assessmentId,
        newData: matchedData,
      };
    } else {
      newValue = values;
    }

    // 建立 WebSocket 连接
    const WEBSOCKET_URL = `wss://${
      process.env.DOMAIN_NAME_OVER
    }/api/crm/call/assessment/ws?companyId=${
      workOrderDetail.companyId
    }&ticketId=${workOrderDetail.workRecordId}&userId=${
      user.userId
    }&assessmentId=${newValue.assessmentId}&assessmentVersionId=${
      newValue?.newData?.assessmentVersionId
        ? newValue?.newData?.assessmentVersionId
        : ''
    }&assessmentRecordId=${
      newValue?.newData?.assessmentRecordId
        ? newValue?.newData?.assessmentRecordId
        : ''
    }&channelTypeId=${
      workOrderDetail?.channelTypeId ? workOrderDetail?.channelTypeId : ''
    }`;
    ws.current = new WebSocket(WEBSOCKET_URL);
    // 监听连接打开
    ws.current.onopen = () => {
      console.log('-----------WebSocket 连接已建立-------------');
      heartbeatIntervalRef.current = setInterval(() => {
        sendMessage('ping', '', 'PING', '');
      }, 45000);
    };
    // 监听消息
    ws.current.onmessage = event => {
      let resultData = JSON.parse(event?.data);
      if (resultData.type === 'ASSESSMENT_DATA') {
        setRightLoading(false);
        // 用法
        const newData = addShowInputToRules(resultData.data?.categories);
        let newResult = resultData.data;
        // const totalRules = countRules(newData);
        // setTotalRulesNum(totalRules);
        const newCategories = addChildrenRulesCount(newData);
        // 用法
        const newCategories1 = addAssessmentStatus2Count(newCategories);
        newResult.categories = newCategories1;
        newResult.totalScore = newResult.totalScore ? newResult.totalScore : 0;
        evaluationRecordResultRef.current = newResult;
        setEvaluationRecordResult(newResult);
      } else if (
        resultData.type === 'AI_SCORE_UPDATED' ||
        resultData.type === 'MANUAL_SCORE_SUBMITTED'
      ) {
        let newResult = JSON.parse(
          JSON.stringify(evaluationRecordResultRef.current),
        );
        if (Object.keys(newResult).length > 0) {
          newResult.totalScore = resultData?.data?.totalScore;
          setEvaluationRecordResult(newResult);
          evaluationRecordResultRef.current = newResult;
        }
      } else if (resultData.type === 'ASSESSMENT_TASK_FAILED') {
        // 评估失败--整体
        setEvaluationRecordResult({});
        notification.error({
          message: resultData.data.msg,
        });
        setRightLoading(false);
      } else if (resultData.type === 'UPDATE_ASSESSMENT_RULE_STATUS') {
        let newResult = JSON.parse(
          JSON.stringify(evaluationRecordResultRef?.current),
        );
        if (Object.keys(newResult).length > 0) {
          const updatedData = setWebsocketList(
            newResult.categories,
            resultData.data,
          );
          const newCategories1 = addAssessmentStatus2Count(updatedData);
          newResult.categories = newCategories1;
          if (resultData?.data?.assessmentTotalScore) {
            newResult.totalScore = resultData?.data?.assessmentTotalScore;
          }
          setEvaluationRecordResult(newResult);
          evaluationRecordResultRef.current = newResult;
        }
      } else if (resultData.type === 'ASSESSMENT_COMPLETED') {
        setShowBtn(true);
        setAssessmentRecordId(resultData?.data?.assessmentRecordId);
      } else if (resultData.type === 'UPDATE_ASSESSMENT_STATUS') {
        // 接收评估记录的状态 0--待评估  1--评估中  2--已评估
        let newResult = JSON.parse(
          JSON.stringify(evaluationRecordResultRef.current),
        );
        if (Object.keys(newResult).length > 0) {
          newResult.assessmentStatus = resultData?.data?.assessmentStatus;
          setEvaluationRecordResult(newResult);
          evaluationRecordResultRef.current = newResult;
        }
      }
    };

    // 监听连接关闭
    ws.current.onclose = () => {
      console.log('-----------WebSocket 连接已关闭-------------');
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
        heartbeatIntervalRef.current = null;
      }
    };

    // 监听错误
    ws.current.onerror = err => {
      console.log('-----------WebSocket 发生错误-------------', err);
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
        heartbeatIntervalRef.current = null;
      }
    };
  };
  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (heartbeatIntervalRef.current) {
        clearInterval(heartbeatIntervalRef.current);
        heartbeatIntervalRef.current = null;
      }
      if (ws.current) {
        ws.current.close();
      }
    };
  }, []);

  useEffect(() => {
    console.log(
      '----------evaluationRecordResult------------',
      evaluationRecordResult,
    );
  }, [evaluationRecordResult]);

  // websocket更新评估记录
  const setWebsocketList = (data, resultData) => {
    if (Array.isArray(data)) {
      // 如果是数组，递归处理每一项
      return data.map(item => setWebsocketList(item, resultData));
    } else if (typeof data === 'object' && data !== null) {
      // 如果是对象，判断是否有 rules 字段
      if (Array.isArray(data.rules)) {
        data.rules = data.rules.map(rule => {
          if (
            rule.assessmentRecordRuleItemId ===
            resultData?.assessmentRecordRuleItemId
          ) {
            return {
              ...rule,
              assessmentStatus: resultData?.status,
              aiScore: resultData?.aiScore ? resultData?.aiScore : 0,
            };
          }
          return rule;
        });
      }
      // 如果有 categories 字段，递归处理
      if (Array.isArray(data.children)) {
        data.children = setWebsocketList(data.children, resultData);
      }
      return data;
    }
    // 其他类型直接返回
    return data;
  };

  // 递归统计所有 rules 数量
  const countRules = categories => {
    let total = 0;
    for (const cat of categories) {
      // 当前节点的 rules 数量
      total += cat.rules ? cat.rules.length : 0;
      // 如果有 children，递归统计
      if (cat.children && cat.children.length > 0) {
        total += countRules(cat.children);
      }
    }
    return total;
  };
  // 递归为每个 children 节点添加 childrenRulesCount 字段
  const addChildrenRulesCount = categories => {
    return categories.map(category => {
      // 递归处理子节点
      const children = category.children
        ? addChildrenRulesCount(category.children)
        : [];
      // 当前节点的 rules 数量
      const selfRulesCount = Array.isArray(category.rules)
        ? category.rules.length
        : 0;
      // 子节点的 rules 总数
      const childrenRulesCount = children.reduce(
        (sum, child) => sum + (child.childrenRulesCount || 0),
        0,
      );
      // 当前节点的总 rules 数量
      const totalRulesCount = selfRulesCount + childrenRulesCount;

      // 返回新对象，增加 childrenRulesCount 字段
      return {
        ...category,
        children,
        childrenRulesCount: totalRulesCount,
      };
    });
  };
  const addAssessmentStatus2Count = categories => {
    return categories.map(category => {
      // 统计当前节点rules中assessmentStatus为2的数量
      const selfCount = Array.isArray(category.rules)
        ? category.rules.filter(
            rule =>
              +rule.assessmentStatus === 2 || +rule.assessmentStatus === 3,
          ).length
        : 0;

      // 递归处理子节点
      const children = category.children
        ? addAssessmentStatus2Count(category.children)
        : [];

      // 子节点的assessmentStatus为2的总数
      const childrenCount = children.reduce(
        (sum, child) => sum + (child.childrenAssessmentStatus2Count || 0),
        0,
      );

      // 当前节点及其所有子节点的总数
      const totalCount = selfCount + childrenCount;

      // 返回新对象，增加 childrenAssessmentStatus2Count 字段
      return {
        ...category,
        children,
        childrenAssessmentStatus2Count: totalCount,
      };
    });
  };

  // 评估详情
  const evaluationListDetail = (assessmentId, assessmentRecordId) => {
    setRightLoading(true);
    const matchedData =
      evaluationList &&
      evaluationList.find(item => item.assessmentId === assessmentId);
    let params = {
      // companyId: workOrderDetail.companyId,
      // ticketId: workOrderDetail.workRecordId,
      // channelId: workOrderDetail.channelConfigId,
      // assessedAgentId: workOrderDetail.agentId,
      // userId: userId,
      // assessmentId: assessmentId,
      // versionId: matchedData?.assessmentVersionId,
      recordId: assessmentRecordId,
    };
    dispatch({
      type: 'workOrderCenter/generateAssessmentRecords',
      payload: params,
      callback: response => {
        setRightLoading(false);
        if (response.code === 200) {
          if (response.data) {
            // 用法
            // const newData = addShowInputToRules(response.data?.categories);
            // let newResult = response.data;
            // newResult.categories = newData;
            // setEvaluationRecordResult(newResult);
            // setEvaluationRecordResult(response.data);

            // 用法
            const newData = addShowInputToRules(response.data?.categories);
            let newResult = response.data;
            // const totalRules = countRules(newData);
            // setTotalRulesNum(totalRules);
            const newCategories = addChildrenRulesCount(newData);
            // 用法
            const newCategories1 = addAssessmentStatus2Count(newCategories);
            newResult.categories = newCategories1;

            setEvaluationRecordResult(newResult);
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  const addShowInputToRules = data => {
    if (Array.isArray(data)) {
      // 如果是数组，递归处理每一项
      return data.map(addShowInputToRules);
    } else if (typeof data === 'object' && data !== null) {
      // 如果是对象，判断是否有 rules 字段
      if (Array.isArray(data.rules)) {
        // 给 rules 里的每个对象加 showInput: false
        data.rules = data.rules.map(rule => ({
          ...rule,
          showInput: false,
        }));
      }
      // 如果有 categories 字段，递归处理
      if (Array.isArray(data.children)) {
        data.children = addShowInputToRules(data.children);
      }
      return data;
    }
    // 其他类型直接返回
    return data;
  };

  // 发送消息
  const sendMessage = (input, name, type, ruleItem) => {
    if (ws.current && ws.current.readyState === WebSocket.OPEN) {
      let messageData = {};
      if (type === 'MANUAL_SCORE_SUBMITTED') {
        let qualityCheckpoints = ruleItem.qualityCheckpoints;
        const pointData = qualityCheckpoints.find(
          item => item.pointId === input,
        );
        messageData = {
          type: type,
          data: {
            assessmentRecordId: evaluationRecordResult.assessmentRecordId,
            assessmentRecordRuleItemId: ruleItem.assessmentRecordRuleItemId,
            manualOptionId: pointData.pointId,
            manualOptionName: pointData.pointDesc,
            manualScore: pointData.pointScore,
            scoreRule: evaluationRecordResult?.scoringRule,
          },
        };
      } else if (type === 'PING') {
        messageData = {
          type: type,
          data: {},
        };
      } else if (type === 'AI_SCORE_UPDATED') {
        // 修改AI打分
        messageData = {
          type: type,
          data: {
            assessmentRecordId: evaluationRecordResult.assessmentRecordId,
            assessmentRecordRuleItemId: ruleItem.assessmentRecordRuleItemId,
            [name]: input,
            scoreRule: evaluationRecordResult?.scoringRule,
          },
        };
      } else {
        //评估备注
        messageData = {
          type: type,
          data: {
            assessmentRecordId: evaluationRecordResult.assessmentRecordId,
            assessmentRecordRuleItemId: ruleItem.assessmentRecordRuleItemId,
            [name]: input,
          },
        };
      }

      console.log('------------messageData---------------', messageData);
      ws.current.send(JSON.stringify(messageData));
    } else {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'work.order.quality.inspection.web.socket.tips',
          defaultValue: 'WebSocket 未连接',
        }),
      });
    }
  };

  // 双击显示输入框
  const handleInputScope = (ruleItem, categoryId) => {
    let ruleId = ruleItem.ruleId;
    // 深拷贝
    let newResult = JSON.parse(JSON.stringify(evaluationRecordResult));
    const updatedData = setShowInputByRuleId(newResult.categories, ruleId);
    newResult.categories = updatedData;
    setEvaluationRecordResult(newResult);
    evaluationRecordResultRef.current = newResult;
    // 获取所有祖先分类 ID 并设置它们为激活的 key
    const ancestors = getAllAncestorCategoryIds(
      newResult.categories,
      categoryId,
    );
    if (ancestors) {
      const newActiveKeys = Array.from(
        new Set([...activePanelKey, ...ancestors]),
      );
      setActivePanelKey(newActiveKeys);
    }
  };
  const setShowInputByRuleId = (data, targetRuleId) => {
    if (Array.isArray(data)) {
      // 如果是数组，递归处理每一项
      return data.map(item => setShowInputByRuleId(item, targetRuleId));
    } else if (typeof data === 'object' && data !== null) {
      // 如果是对象，判断是否有 rules 字段
      if (Array.isArray(data.rules)) {
        data.rules = data.rules.map(rule => {
          if (rule.ruleId === targetRuleId) {
            return { ...rule, showInput: true };
          }
          return rule;
        });
      }
      // 如果有 categories 字段，递归处理
      if (Array.isArray(data.children)) {
        data.children = setShowInputByRuleId(data.children, targetRuleId);
      }
      return data;
    }
    // 其他类型直接返回
    return data;
  };

  // 回车保存评估分数
  const handleKeyPress = (e, ruleItem, categoryId) => {
    if (e.key === 'Enter') {
      e.preventDefault(); // 阻止默认行为（换行），如果你希望回车执行方法而不是换行

      if (e.target.value) {
        // Modify the validation to check if the value is a positive integer
        const value = Number(e.target.value);
        if (
          Number.isInteger(value) &&
          value >= 0 &&
          value <= ruleItem?.totalScore
        ) {
          let ruleId = ruleItem.ruleId;
          // 深拷贝
          let newResult = JSON.parse(JSON.stringify(evaluationRecordResult));
          const updatedData = setHideInputByRuleId(
            newResult.categories,
            ruleId,
            e.target.value,
          );
          newResult.categories = updatedData;
          setEvaluationRecordResult(newResult);
          evaluationRecordResultRef.current = newResult;
          // 获取所有祖先分类 ID 并设置它们为激活的 key
          const ancestors = getAllAncestorCategoryIds(
            newResult.categories,
            categoryId,
          );
          if (ancestors) {
            const newActiveKeys = Array.from(
              new Set([...activePanelKey, ...ancestors]),
            );
            setActivePanelKey(newActiveKeys);
          }
          sendMessage(
            e.target.value,
            'manualScore',
            'AI_SCORE_UPDATED',
            ruleItem,
          );
        } else {
          notification.warning({
            message: getIntl().formatMessage(
              {
                id: 'work.order.quality.inspection.scope.tips.1',
                defaultValue: '请输入大于等于0且小于等于{value}的正整数',
              },
              {
                value: ruleItem?.totalScore,
              },
            ),
          });
        }
      } else {
        notification.warning({
          message: getIntl().formatMessage({
            id: 'work.order.quality.inspection.scope.tips',
            defaultValue: '输入不能为空！',
          }),
        });
      }
    }
  };
  const setHideInputByRuleId = (data, targetRuleId, value) => {
    if (Array.isArray(data)) {
      // 如果是数组，递归处理每一项
      return data.map(item => setHideInputByRuleId(item, targetRuleId, value));
    } else if (typeof data === 'object' && data !== null) {
      // 如果是对象，判断是否有 rules 字段
      if (Array.isArray(data.rules)) {
        data.rules = data.rules.map(rule => {
          if (rule.ruleId === targetRuleId) {
            return { ...rule, showInput: false, manualScore: value };
          }
          return rule;
        });
      }
      // 如果有 categories 字段，递归处理
      if (Array.isArray(data.children)) {
        data.children = setHideInputByRuleId(
          data.children,
          targetRuleId,
          value,
        );
      }
      return data;
    }
    // 其他类型直接返回
    return data;
  };
  // 失去焦点取消修改分数
  const handleOnBlur = (ruleItem, categoryId) => {
    let ruleId = ruleItem.ruleId;
    // 深拷贝
    let newResult = JSON.parse(JSON.stringify(evaluationRecordResult));
    const updatedData = setHideInputByRuleIdCancel(
      newResult.categories,
      ruleId,
    );
    newResult.categories = updatedData;
    setEvaluationRecordResult(newResult);
    evaluationRecordResultRef.current = newResult;
    // 获取所有祖先分类 ID 并设置它们为激活的 key
    const ancestors = getAllAncestorCategoryIds(
      newResult.categories,
      categoryId,
    );
    if (ancestors) {
      const newActiveKeys = Array.from(
        new Set([...activePanelKey, ...ancestors]),
      );
      setActivePanelKey(newActiveKeys);
    }
  };
  const setHideInputByRuleIdCancel = (data, targetRuleId) => {
    if (Array.isArray(data)) {
      // 如果是数组，递归处理每一项
      return data.map(item => setHideInputByRuleIdCancel(item, targetRuleId));
    } else if (typeof data === 'object' && data !== null) {
      // 如果是对象，判断是否有 rules 字段
      if (Array.isArray(data.rules)) {
        data.rules = data.rules.map(rule => {
          if (rule.ruleId === targetRuleId) {
            return { ...rule, showInput: false };
          }
          return rule;
        });
      }
      // 如果有 categories 字段，递归处理
      if (Array.isArray(data.children)) {
        data.children = setHideInputByRuleIdCancel(data.children, targetRuleId);
      }
      return data;
    }
    // 其他类型直接返回
    return data;
  };

  // 备注输入框值
  const handleInputChange = (e, ruleItem, categoryId) => {
    console.log('----------e------------', e.target.value);
    // setInputValue(e.target.value);
    showNotesInfo(e, ruleItem, categoryId);
  };
  // 回车保存评估备注
  const handleKeyPressRemarks = (e, ruleItem, categoryId) => {
    if (e.key === 'Enter') {
      e.preventDefault(); // 阻止默认行为（换行），如果你希望回车执行方法而不是换行
      if (e.target.value) {
        showNotesInfo(e.target.value, ruleItem, categoryId);
        sendMessage(
          e.target.value,
          'assessmentRemark',
          'ASSESSMENT_REMARK_ADDED',
          ruleItem,
        );
        // 获取所有祖先分类 ID 并设置它们为激活的 key
        const ancestors = getAllAncestorCategoryIds(
          evaluationRecordResult.categories,
          categoryId,
        );
        if (ancestors) {
          const newActiveKeys = Array.from(
            new Set([...activePanelKey, ...ancestors]),
          );
          setActivePanelKey(newActiveKeys);
        }
      } else {
        notification.warning({
          message: getIntl().formatMessage({
            id: 'work.order.quality.inspection.scope.tips',
            defaultValue: '输入不能为空！',
          }),
        });
      }
    }
  };
  //点击提交按钮
  const handleBlurRemarks = (remark, ruleItem, categoryId) => {
    if (remark) {
      showNotesInfo(remark, ruleItem, categoryId);
      sendMessage(
        remark,
        'assessmentRemark',
        'ASSESSMENT_REMARK_ADDED',
        ruleItem,
      );
      // 获取所有祖先分类 ID 并设置它们为激活的 key
      const ancestors = getAllAncestorCategoryIds(
        evaluationRecordResult.categories,
        categoryId,
      );
      if (ancestors) {
        const newActiveKeys = Array.from(
          new Set([...activePanelKey, ...ancestors]),
        );
        setActivePanelKey(newActiveKeys);
      }
    } else {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'work.order.quality.inspection.scope.tips',
          defaultValue: '输入不能为空！',
        }),
      });
    }
  };
  // 回显备注信息
  const showNotesInfo = (value, ruleItem, categoryId) => {
    let ruleId = ruleItem.ruleId;
    // 深拷贝
    let newResult = JSON.parse(JSON.stringify(evaluationRecordResult));
    const updatedData = setRemarksInputByRuleId(
      newResult.categories,
      ruleId,
      value,
    );
    newResult.categories = updatedData;
    setEvaluationRecordResult(newResult);
    evaluationRecordResultRef.current = newResult;
    // 获取所有祖先分类 ID 并设置它们为激活的 key
    const ancestors = getAllAncestorCategoryIds(
      newResult.categories,
      categoryId,
    );
    if (ancestors) {
      const newActiveKeys = Array.from(
        new Set([...activePanelKey, ...ancestors]),
      );
      setActivePanelKey(newActiveKeys);
    }
  };
  const setRemarksInputByRuleId = (data, targetRuleId, value) => {
    if (Array.isArray(data)) {
      // 如果是数组，递归处理每一项
      return data.map(item =>
        setRemarksInputByRuleId(item, targetRuleId, value),
      );
    } else if (typeof data === 'object' && data !== null) {
      // 如果是对象，判断是否有 rules 字段
      if (Array.isArray(data.rules)) {
        data.rules = data.rules.map(rule => {
          if (rule.ruleId === targetRuleId) {
            return { ...rule, assessmentRemark: value };
          }
          return rule;
        });
      }
      // 如果有 categories 字段，递归处理
      if (Array.isArray(data.children)) {
        data.children = setRemarksInputByRuleId(
          data.children,
          targetRuleId,
          value,
        );
      }
      return data;
    }
    // 其他类型直接返回
    return data;
  };

  // 切换radio
  const onChangeRadio = (e, ruleItem, categoryId) => {
    let ruleId = ruleItem.ruleId;
    // 深拷贝
    let newResult = JSON.parse(JSON.stringify(evaluationRecordResult));
    const updatedData = setAgentRadioByRuleId(
      newResult.categories,
      ruleId,
      e.target.value,
    );
    const newCategories1 = addAssessmentStatus2Count(updatedData);
    newResult.categories = newCategories1;
    setEvaluationRecordResult(newResult);
    evaluationRecordResultRef.current = newResult;
    // 获取所有祖先分类 ID 并设置它们为激活的 key
    const ancestors = getAllAncestorCategoryIds(
      newResult.categories,
      categoryId,
    );
    if (ancestors) {
      const newActiveKeys = Array.from(
        new Set([...activePanelKey, ...ancestors]),
      );
      setActivePanelKey(newActiveKeys);
    }
    sendMessage(
      e.target.value,
      'manualScoring',
      'MANUAL_SCORE_SUBMITTED',
      ruleItem,
    );
  };

  const setAgentRadioByRuleId = (data, targetRuleId, value) => {
    if (Array.isArray(data)) {
      // 如果是数组，递归处理每一项
      return data.map(item => setAgentRadioByRuleId(item, targetRuleId, value));
    } else if (typeof data === 'object' && data !== null) {
      // 如果是对象，判断是否有 rules 字段
      if (Array.isArray(data.rules)) {
        data.rules = data.rules.map(rule => {
          if (rule.ruleId === targetRuleId) {
            let qualityCheckpoints = rule.qualityCheckpoints;
            const pointData = qualityCheckpoints.find(
              item => item.pointId === value,
            );
            return {
              ...rule,
              manualOptionId: value,
              manualScore: pointData.pointScore,
              assessmentStatus: 2,
            };
          }
          return rule;
        });
      }
      // 如果有 categories 字段，递归处理
      if (Array.isArray(data.children)) {
        data.children = setAgentRadioByRuleId(
          data.children,
          targetRuleId,
          value,
        );
      }
      return data;
    }
    // 其他类型直接返回
    return data;
  };

  const onChange = key => {
    // onChange now receives an array of keys
    setActivePanelKey(key); // Update activePanelKey directly with the array
  };

  // 导出PDF
  const downloadPDF = (base64Data, filename = 'document.pdf') => {
    try {
      const binaryString = atob(base64Data);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      const blob = new Blob([bytes], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      // 触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      // 清理
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('PDF下载失败:', error);
    } finally {
      setLoading(false);
    }
  };
  const handleExportPDF = record => {
    setLoading(true);
    if (!loading) {
      dispatch({
        type: 'smartQualityInspection/exportAssessmentForm',
        payload: {
          assessmentId: record.assessmentRecordId,
        },
        callback: res => {
          console.log(res);
          if (res.code === 200) {
            downloadPDF(res.data, record.assessmentName + '.pdf');
          }
        },
      });
    }
  };

  // 点击完成评估
  const handleAssessmentCompleted = () => {
    const formatDateTime = dtString => {
      const date = new Date(dtString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    };
    let newResult = JSON.parse(
      JSON.stringify(evaluationRecordResultRef.current),
    );
    if (Object.keys(newResult).length > 0) {
      newResult.createTime = formatDateTime(
        evaluationRecordResultRef.current?.createTime,
      );
      setEvaluationRecordResult(newResult);
      evaluationRecordResultRef.current = newResult;
    }
    setAssessmentLoading(true);
    dispatch({
      type: 'workOrderCenter/assessmentCompleted',
      payload: {
        recordId: assessmentRecordId
          ? assessmentRecordId
          : evaluationRecordResult?.assessmentRecordId,
      },
      callback: response => {
        setAssessmentLoading(false);
        if (response.code === 200) {
          notification.success({
            message: response.msg,
          });
          setAssessmentStatus(2);
          setShowBtn(false);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 单个规则的显示组件
  const RuleItem = ({ rule, categoryId }) => {
    const [remark, setRemark] = useState(rule.assessmentRemark || '');

    useEffect(() => {
      setRemark(rule.assessmentRemark || '');
    }, [rule.assessmentRemark]);
    //assessmentType--评估类型：1AIgc评分，2人工评分
    // assessmentStatus 评估状态：0待评估，1进行中，2已评估
    if (rule.assessmentType === 1) {
      let icon;
      if (+rule.assessmentStatus === 2) {
        icon = CompleteIcon();
      } else if (+rule.assessmentStatus === 1 || +rule.assessmentStatus === 0) {
        icon = RuleLoadingIcon;
      } else if (+rule.assessmentStatus === 3) {
        icon = EvaluationErrorIcon();
      }
      let aiAssessmentRule = JSON.parse(rule.aiAssessmentRule);
      const conditionMap = {
        '>': getIntl().formatMessage({
          id: 'smart.quality.evaluation.add.rule.aigc.rule2.condition.gt',
          defaultValue: '大于',
        }),
        '=': getIntl().formatMessage({
          id: 'smart.quality.evaluation.add.rule.aigc.rule2.condition.eq',
          defaultValue: '等于',
        }),
        '<': getIntl().formatMessage({
          id: 'smart.quality.evaluation.add.rule.aigc.rule2.condition.lt',
          defaultValue: '小于',
        }),
      };
      return (
        <div className={styles.ruleContent}>
          <div className={styles.ruleDetailItem}>
            <div className={styles.ruleDetailItemHeader}>
              <Collapse
                collapsible={'header'}
                expandIconPosition={'end'}
                ghost={true}
              >
                <Panel
                  header={
                    <div className={styles.ruleTitleContainer}>
                      <span className={styles.ruleIcon}>
                        {+rule.assessmentStatus === 2 ||
                        +rule.assessmentStatus === 3 ? (
                          icon
                        ) : (
                          <img src={icon} />
                        )}
                      </span>
                      <span className={styles.ruleTitleText}>
                        {rule.ruleName}
                      </span>
                    </div>
                  }
                  key={categoryId}
                >
                  <p className={styles.pointItemTitle}>
                    <FormattedMessage id="smart.quality.evaluation.rule.check.points" />{' '}
                    :
                  </p>
                  {rule.qualityCheckpoints?.map((pointItem, pointIndex) => (
                    <p className={styles.pointItemText}>
                      {pointIndex + 1}. {pointItem?.pointDesc}
                    </p>
                  ))}
                  <p
                    className={styles.pointItemTitle}
                    style={{ marginTop: '10px' }}
                  >
                    <FormattedMessage id="smart.quality.evaluation.rule.table.score" />{' '}
                    :
                  </p>
                  <p className={styles.pointItemRule}>
                    {/*{rule?.description ? rule?.description : '--'}*/}
                    {aiAssessmentRule?.type === '1' ? (
                      <>
                        <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rule1.title" />
                        <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rule1.prefix" />
                        {evaluationRecordResult?.scoringRule === 1 ? (
                          <FormattedMessage id="smart.quality.evaluation.rule.add.score.add" />
                        ) : (
                          <FormattedMessage id="smart.quality.evaluation.rule.add.score.subtract" />
                        )}
                        <b> {aiAssessmentRule?.deduct_per_time} </b>
                        <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rule1.middle" />
                        {evaluationRecordResult?.scoringRule === 1 ? (
                          <FormattedMessage id="smart.quality.evaluation.rule.add.score.add" />
                        ) : (
                          <FormattedMessage id="smart.quality.evaluation.rule.add.score.subtract" />
                        )}
                        <b> {aiAssessmentRule?.max_deduct} </b>
                        <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rule1.suffix" />
                      </>
                    ) : (
                      <>
                        <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rule2.title" />
                        <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rule2.prefix" />
                        <b>
                          {conditionMap[aiAssessmentRule.compare]}{' '}
                          {aiAssessmentRule?.times}{' '}
                        </b>
                        <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rule2.middle" />
                        {evaluationRecordResult?.scoringRule === 1 ? (
                          <FormattedMessage id="smart.quality.evaluation.rule.add.score.add" />
                        ) : (
                          <FormattedMessage id="smart.quality.evaluation.rule.add.score.subtract" />
                        )}
                        <b> {aiAssessmentRule?.deduct} </b>
                        <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rule2.suffix" />
                      </>
                    )}
                  </p>

                  {/*evaluationRecordResult?.scoringRule === 1   加*/}
                </Panel>
              </Collapse>
            </div>
            <div className={styles.ruleDetailItemContent}>
              {(+rule.assessmentStatus === 2 || +rule.assessmentStatus === 3) &&
                !rule.manualScore && (
                  <div className={styles.resultItem}>
                    <div className={styles.resultContainer}>
                      <span className={styles.aiIcon}>
                        <img src={NewAiScopeIcon} />
                      </span>
                      <span style={{ float: 'left' }}>
                        <FormattedMessage
                          id="intelligent.quality.inspection.evaluate.complete"
                          defaultMessage="智能质检已完成"
                        />
                      </span>
                      {evaluationRecordResult?.scoringRule === 1 ? (
                        <span className={styles.operatorText}>
                          <FormattedMessage
                            id="intelligent.quality.inspection.operator.add"
                            defaultMessage="加"
                          />
                        </span>
                      ) : (
                        <span
                          className={styles.operatorText}
                          style={{ color: '#F22417' }}
                        >
                          <FormattedMessage
                            id="intelligent.quality.inspection.operator.minus"
                            defaultMessage="减"
                          />
                        </span>
                      )}
                      {assessmentStatus && (
                        <div
                          className={styles.scopeNumContainer}
                          style={{
                            color:
                              evaluationRecordResult?.scoringRule === 1
                                ? '#333'
                                : '#F22417',
                            cursor: assessmentStatus ? 'initial' : 'pointer',
                          }}
                        >
                          {' '}
                          {rule.aiScore ? rule.aiScore : 0}{' '}
                          <FormattedMessage
                            id="selfReport.fen"
                            defaultMessage="分"
                          />
                        </div>
                      )}
                      {!rule.showInput && !assessmentStatus && (
                        <Tooltip
                          title={getIntl().formatMessage({
                            id:
                              'intelligent.quality.inspection.double.click.scope.tips',
                            defaultValue: '双击可修改分数',
                          })}
                        >
                          <div
                            onDoubleClick={() =>
                              handleInputScope(rule, categoryId)
                            }
                            className={styles.scopeNumContainer}
                            style={{
                              color:
                                evaluationRecordResult?.scoringRule === 1
                                  ? '#333'
                                  : '#F22417',
                              cursor: assessmentStatus ? 'initial' : 'pointer',
                            }}
                          >
                            {' '}
                            {rule.aiScore ? rule.aiScore : 0}{' '}
                            <FormattedMessage
                              id="selfReport.fen"
                              defaultMessage="分"
                            />
                          </div>
                        </Tooltip>
                      )}
                      {rule.showInput && (
                        <>
                          <div className={styles.scopeInput}>
                            <InputNumber
                              autoFocus={true}
                              min={0}
                              // max={rule?.totalScore}
                              value={rule?.manualScore}
                              placeholder={getIntl().formatMessage({
                                id:
                                  'intelligent.quality.inspection.input.number.placeholder',
                                defaultValue: '请输入分数并回车保存',
                              })}
                              onKeyDown={e =>
                                handleKeyPress(e, rule, categoryId)
                              }
                              onBlur={() => handleOnBlur(rule, categoryId)}
                            />
                          </div>
                          {/*<img className={styles.cancelScopeIcon} src={CancelScopeIcon} />*/}
                        </>
                      )}
                    </div>
                  </div>
                )}
              {(+rule.assessmentStatus === 2 || +rule.assessmentStatus === 3) &&
                rule.manualScore && (
                  <div className={styles.resultItem}>
                    <span className={styles.aiIcon}>
                      <img src={NewAiScopeIcon} />
                    </span>
                    <div className={styles.resultCompleteContainer}>
                      <span>
                        <FormattedMessage
                          id="intelligent.quality.inspection.evaluate.complete"
                          defaultMessage="智能质检已完成"
                        />
                        {evaluationRecordResult?.scoringRule === 1 ? (
                          <span className={styles.operatorText}>
                            <FormattedMessage
                              id="intelligent.quality.inspection.operator.add"
                              defaultMessage="加"
                            />
                          </span>
                        ) : (
                          <span
                            className={styles.operatorText}
                            style={{ color: '#F22417' }}
                          >
                            <FormattedMessage
                              id="intelligent.quality.inspection.operator.minus"
                              defaultMessage="减"
                            />
                          </span>
                        )}
                        <span
                          style={{
                            color:
                              evaluationRecordResult?.scoringRule === 1
                                ? '#333'
                                : '#F22417',
                          }}
                        >
                          {' '}
                          {rule.aiScore}{' '}
                          <FormattedMessage
                            id="selfReport.fen"
                            defaultMessage="分"
                          />
                        </span>
                      </span>
                    </div>
                  </div>
                )}
              {(+rule.assessmentStatus === 2 || +rule.assessmentStatus === 3) &&
                rule.manualScore && (
                  <div className={styles.resultItem}>
                    <span className={styles.aiIcon}>{AgentIcon()}</span>
                    <div className={styles.resultContainer}>
                      <span style={{ float: 'left' }}>
                        <FormattedMessage
                          id="intelligent.quality.inspection.manual.evaluation"
                          defaultMessage="人工评估"
                        />
                      </span>
                      {evaluationRecordResult?.scoringRule === 1 ? (
                        <span className={styles.operatorText}>
                          <FormattedMessage
                            id="intelligent.quality.inspection.operator.add"
                            defaultMessage="加"
                          />
                        </span>
                      ) : (
                        <span
                          className={styles.operatorText}
                          style={{ color: '#F22417' }}
                        >
                          <FormattedMessage
                            id="intelligent.quality.inspection.operator.minus"
                            defaultMessage="减"
                          />
                        </span>
                      )}
                      {assessmentStatus && (
                        <div
                          className={styles.scopeNumContainer}
                          style={{
                            color:
                              evaluationRecordResult?.scoringRule === 1
                                ? '#333'
                                : '#F22417',
                            cursor: assessmentStatus ? 'initial' : 'pointer',
                          }}
                        >
                          {' '}
                          {rule.manualScore}{' '}
                          <FormattedMessage
                            id="selfReport.fen"
                            defaultMessage="分"
                          />
                        </div>
                      )}
                      {!rule.showInput && !assessmentStatus && (
                        <Tooltip
                          title={getIntl().formatMessage({
                            id:
                              'intelligent.quality.inspection.double.click.scope.tips',
                            defaultValue: '双击可修改分数',
                          })}
                        >
                          <div
                            onDoubleClick={() =>
                              handleInputScope(rule, categoryId)
                            }
                            className={styles.scopeNumContainer}
                            style={{
                              color:
                                evaluationRecordResult?.scoringRule === 1
                                  ? '#333'
                                  : '#F22417',
                              cursor: assessmentStatus ? 'initial' : 'pointer',
                            }}
                          >
                            {' '}
                            {rule.manualScore}{' '}
                            <FormattedMessage
                              id="selfReport.fen"
                              defaultMessage="分"
                            />
                          </div>
                        </Tooltip>
                      )}
                      {rule.showInput && (
                        <>
                          <div className={styles.scopeInput}>
                            <InputNumber
                              autoFocus={true}
                              min={0}
                              // max={rule?.totalScore}
                              vaule={rule.manualScore}
                              placeholder={getIntl().formatMessage({
                                id:
                                  'intelligent.quality.inspection.input.number.placeholder',
                                defaultValue: '请输入分数并回车保存',
                              })}
                              onKeyDown={e =>
                                handleKeyPress(e, rule, categoryId)
                              }
                              onBlur={() => handleOnBlur(rule, categoryId)}
                            />
                          </div>
                          {/*<img className={styles.cancelScopeIcon} src={CancelScopeIcon} />*/}
                        </>
                      )}
                    </div>
                  </div>
                )}
              {rule.reference && (
                <div className={styles.evaluationReferenceStandards}>
                  <p>
                    <FormattedMessage id="intelligent.quality.inspection.evaluation.reference.standards" />
                  </p>
                  <div>{rule.reference}</div>
                </div>
              )}
              <div className={styles.evaluationRemarks}>
                {(+assessmentStatus === 2 && remark) ||
                +assessmentStatus !== 2 ? (
                  <p className={styles.evaluationRemarksTitle}>
                    <FormattedMessage
                      id="intelligent.quality.inspection.evaluation.remarks"
                      defaultMessage="评估备注"
                    />
                  </p>
                ) : null}
                {assessmentStatus && remark ? (
                  <p>{remark ? remark : '--'}</p>
                ) : null}
                {!assessmentStatus && (
                  <Input.Group>
                    <Input
                      value={remark}
                      onChange={e => setRemark(e.target.value)}
                      onKeyDown={e =>
                        handleKeyPressRemarks(e, rule, categoryId)
                      }
                      placeholder={getIntl().formatMessage({
                        id:
                          'intelligent.quality.inspection.evaluation.remarks.placeholder',
                        defaultValue: '请输入评估备注，回车保存',
                      })}
                    />
                    <Button
                      type="primary"
                      onClick={() =>
                        handleBlurRemarks(remark, rule, categoryId)
                      }
                    >
                      <FormattedMessage
                        id="customerInformation.add.basicInformation.button.save"
                        defaultValue="保存"
                      />
                    </Button>
                  </Input.Group>
                )}
              </div>
            </div>
          </div>
        </div>
      );
    } else {
      return (
        <div className={styles.ruleContent}>
          <div className={styles.ruleDetailItem}>
            <div
              className={styles.ruleDetailItemHeader}
              style={{ marginTop: '14px', marginBottom: '14px' }}
            >
              <span className={styles.ruleIcon}>
                {rule.assessmentStatus === 0 ? AgentRuleIcon() : CompleteIcon()}
              </span>
              <p className={styles.ruleTitleText}>{rule.ruleName}</p>
            </div>
            <div className={styles.ruleDetailItemContent}>
              {!assessmentStatus && (
                <div className={styles.radioContainer}>
                  <Radio.Group
                    onChange={e => onChangeRadio(e, rule, categoryId)}
                    value={rule?.manualOptionId}
                  >
                    <Space direction="vertical">
                      {rule?.qualityCheckpoints?.map(pointItem => {
                        return (
                          <Radio value={pointItem.pointId}>
                            {pointItem.pointDesc}
                          </Radio>
                        );
                      })}
                    </Space>
                  </Radio.Group>
                </div>
              )}
              {rule.manualScore > 0 && (
                <div className={styles.resultItem}>
                  <div className={styles.resultContainer}>
                    <span className={styles.aiIcon}>{AgentIcon()}</span>
                    <span
                      style={{
                        float: 'left',
                      }}
                    >
                      <FormattedMessage
                        id="intelligent.quality.inspection.manual.evaluation"
                        defaultMessage="人工评估"
                      />
                    </span>
                    {evaluationRecordResult?.scoringRule === 1 ? (
                      <span className={styles.operatorText}>
                        <FormattedMessage
                          id="intelligent.quality.inspection.operator.add"
                          defaultMessage="加"
                        />
                      </span>
                    ) : (
                      <span
                        className={styles.operatorText}
                        style={{ color: '#F22417' }}
                      >
                        <FormattedMessage
                          id="intelligent.quality.inspection.operator.minus"
                          defaultMessage="减"
                        />
                      </span>
                    )}
                    <div
                      className={styles.scopeNumAgentContainer}
                      style={{
                        color:
                          evaluationRecordResult?.scoringRule === 1
                            ? '#333'
                            : '#F22417',
                      }}
                    >
                      {' '}
                      {rule.manualScore}{' '}
                      <FormattedMessage
                        id="selfReport.fen"
                        defaultMessage="分"
                      />
                    </div>
                  </div>
                </div>
              )}
              {rule.reference && (
                <div className={styles.evaluationReferenceStandards}>
                  <p>
                    <FormattedMessage id="intelligent.quality.inspection.evaluation.reference.standards" />
                  </p>
                  <div>{rule.reference}</div>
                </div>
              )}
              <div className={styles.evaluationRemarks}>
                {(+assessmentStatus === 2 && remark) ||
                +assessmentStatus !== 2 ? (
                  <p className={styles.evaluationRemarksTitle}>
                    <FormattedMessage
                      id="intelligent.quality.inspection.evaluation.remarks"
                      defaultMessage="评估备注"
                    />
                  </p>
                ) : null}
                {assessmentStatus && remark ? (
                  <p>{remark ? remark : '--'}</p>
                ) : null}
                {!assessmentStatus && (
                  <Input.Group>
                    <Input
                      // value ={rule.assessmentRemark}
                      value={remark}
                      // onChange={e => handleInputChange(e, rule, categoryId)}
                      onChange={e => setRemark(e.target.value)}
                      onKeyDown={e =>
                        handleKeyPressRemarks(e, rule, categoryId)
                      }
                      placeholder={getIntl().formatMessage({
                        id:
                          'intelligent.quality.inspection.evaluation.remarks.placeholder',
                        defaultValue: '请输入评估备注，回车保存',
                      })}
                    />
                    <Button
                      type="primary"
                      onClick={() =>
                        handleBlurRemarks(remark, rule, categoryId)
                      }
                    >
                      <FormattedMessage
                        id="customerInformation.add.basicInformation.button.save"
                        defaultValue="保存"
                      />
                    </Button>
                  </Input.Group>
                )}
              </div>
            </div>
          </div>
        </div>
      );
    }
  };

  // 递归渲染分类及其规则
  const CategoryItem = ({ categorizeItem, index, prefix }) => (
    <Collapse
      onChange={setActivePanelKey}
      activeKey={activePanelKey}
      // accordion // 移除 accordion 属性
      collapsible={'header'}
      expandIconPosition={'end'}
      ghost={true}
    >
      <Panel
        header={
          <div
            className={`${
              categorizeItem.categoryLevel == 1
                ? styles.levelTitle
                : categorizeItem.categoryLevel == 2
                ? styles.levelTitleSecond
                : styles.levelTitleThird
            }`}
          >
            {categorizeItem.childrenAssessmentStatus2Count > 0 &&
            categorizeItem.childrenAssessmentStatus2Count <
              categorizeItem.childrenRulesCount ? (
              <span className={styles.progressStatusIcon}>
                {UnderEvaluationIcon()}
              </span>
            ) : categorizeItem.childrenAssessmentStatus2Count === 0 ? (
              <div className={styles.progressIcon}></div>
            ) : (
              <span className={styles.progressStatusIcon}>
                {CompleteIcon()}
              </span>
            )}
            <p className={styles.levelName}>
              {' '}
              {prefix ? `${prefix}.${index + 1}` : `${index + 1}.`} &nbsp;
              {categorizeItem.categoryName}
            </p>
            {/*<span className={styles.arrowIcon}>{DownArrowIcon()}</span>*/}
            <span className={styles.progressNum}>
              {categorizeItem.childrenAssessmentStatus2Count}/
              {categorizeItem.childrenRulesCount}
            </span>
          </div>
        }
        key={categorizeItem.categoryId.toString()}
      >
        {/* 渲染规则 */}
        {categorizeItem.rules?.map(rule => (
          <div className={styles.levelContent} style={{ marginLeft: '0px' }}>
            <RuleItem
              categoryId={categorizeItem.categoryId}
              key={rule.ruleId}
              rule={rule}
            />
          </div>
        ))}
        {/* 递归渲染子分类 */}
        {categorizeItem.children?.length > 0 && (
          <div className={styles.levelContent}>
            {categorizeItem.children?.map((childItem, childIndex) => (
              <CategoryItem
                key={childItem.categoryId.toString()}
                categorizeItem={childItem}
                index={childIndex}
                prefix={prefix ? `${prefix}.${index + 1}` : `${index + 1}`}
              />
            ))}
          </div>
        )}
      </Panel>
    </Collapse>
  );

  return (
    <Spin spinning={ticketDetailLoading}>
      <div className={styles.intelligentQualityInspectionContainer}>
        <div className={styles.leftContainer}>
          {/*<Spin spinning={ticketDetailLoading}>*/}
          <div className={styles.workOrderDetailInfo}>
            <div className={styles.titleDetail}>
              <div className={styles.line}></div>
              <p
                className={styles.workOrderTitle}
                title={workOrderDetail.workRecordTheme}
              >
                {workOrderDetail.workRecordTheme}
              </p>
              <span
                title={getIntl().formatMessage({
                  id: 'work.order.detail.copy.work.order.code',
                  defaultValue: '点击复制工单编号',
                })}
              >
                (ID：
                <CopyToClipboard
                  text={workOrderDetail.wordRecordCode}
                  onCopy={(_, result) => {
                    if (result) {
                      message.success(
                        getIntl().formatMessage({
                          id: 'work.order.detail.copy.success',
                          defaultValue: '复制成功',
                        }),
                      );
                    } else {
                      message.error(
                        getIntl().formatMessage({
                          id: 'work.order.detail.copy.error',
                          defaultValue: '复制失败，请稍后再试',
                        }),
                      );
                    }
                  }}
                >
                  <i className={styles.copyWordRecordCode}>
                    {workOrderDetail.wordRecordCode}
                  </i>
                </CopyToClipboard>
                )
              </span>
            </div>
            <div className={styles.workOrderDetailText}>
              <div className={styles.workOrderDetailItem}>
                <p
                  title={getIntl().formatMessage({
                    id: 'work.order.detail.info',
                    defaultValue: '处理客服：',
                  })}
                >
                  <FormattedMessage
                    id="work.order.detail.info"
                    defaultMessage="处理客服："
                  />
                </p>
                <span className={styles.agentImg}>
                  <img
                    src={
                      workOrderDetail.agentUrl
                        ? workOrderDetail.agentUrl
                        : DefaultUserName
                    }
                  />
                  {workOrderDetail.agentName ? workOrderDetail.agentName : '--'}
                </span>
              </div>
              <div className={styles.workOrderDetailItem}>
                <p
                  title={getIntl().formatMessage({
                    id: 'work.order.detail.info.1',
                    defaultValue: '优先级：',
                  })}
                >
                  <FormattedMessage
                    id="work.order.detail.info.1"
                    defaultMessage="优先级："
                  />
                </p>
                <span
                  style={{
                    display:
                      workOrderDetail.priorityLevelId == '1001' ||
                      workOrderDetail.priorityLevelId == '1002'
                        ? 'block'
                        : 'none',
                  }}
                >
                  {workOrderDetail.priorityLevelName ? (
                    <span className={styles.priority1}>
                      {workOrderDetail.priorityLevelName}
                    </span>
                  ) : (
                    '--'
                  )}
                </span>
                <span
                  style={{
                    display:
                      workOrderDetail.priorityLevelId == '1003' ||
                      workOrderDetail.priorityLevelId == '1004'
                        ? 'block'
                        : 'none',
                  }}
                >
                  {workOrderDetail.priorityLevelName ? (
                    <span className={styles.priority3}>
                      {workOrderDetail.priorityLevelName}
                    </span>
                  ) : (
                    '--'
                  )}
                </span>
                <span
                  style={{
                    display:
                      workOrderDetail.priorityLevelId == '1005'
                        ? 'block'
                        : 'none',
                  }}
                >
                  {workOrderDetail.priorityLevelName ? (
                    <span className={styles.priority5}>
                      {workOrderDetail.priorityLevelName}
                    </span>
                  ) : (
                    '--'
                  )}
                </span>
              </div>
              <div className={styles.workOrderDetailItem}>
                <p
                  title={getIntl().formatMessage({
                    id: 'work.order.detail.info.2',
                    defaultValue: '来源渠道：',
                  })}
                >
                  <FormattedMessage
                    id="work.order.detail.info.2"
                    defaultMessage="来源渠道："
                  />
                </p>
                <span>
                  {workOrderDetail.channelConfigName
                    ? workOrderDetail.channelConfigName
                    : '--'}
                </span>
              </div>
              <div className={styles.workOrderDetailItem}>
                <p
                  title={getIntl().formatMessage({
                    id: 'work.order.detail.info.3',
                    defaultValue: '工单分类：',
                  })}
                >
                  <FormattedMessage
                    id="work.order.detail.info.3"
                    defaultMessage="工单分类："
                  />
                </p>
                <p className={styles.ticketTypeText}>
                  {getTypeNameById(workOrderDetail.workRecordTypeId)
                    ? getTypeNameById(workOrderDetail.workRecordTypeId)
                    : '--'}
                </p>
              </div>
              <div className={styles.workOrderDetailItem}>
                <p
                  title={getIntl().formatMessage({
                    id: 'work.order.detail.info.6',
                    defaultValue: '创建方式：',
                  })}
                >
                  <FormattedMessage
                    id="work.order.detail.info.6"
                    defaultMessage="创建方式："
                  />
                </p>
                <span
                  style={{
                    display: workOrderDetail.createType == 0 ? 'block' : 'none',
                  }}
                >
                  <FormattedMessage
                    id="work.order.detail.info.7"
                    defaultMessage="自动创建"
                  />
                </span>
                <span
                  style={{
                    display: workOrderDetail.createType == 0 ? 'none' : 'block',
                  }}
                >
                  <FormattedMessage
                    id="work.order.detail.info.8"
                    defaultMessage="手动创建"
                  />
                </span>
                <span
                  style={{
                    display:
                      workOrderDetail.createType == 0 ||
                      workOrderDetail.createType == 1
                        ? 'none'
                        : 'block',
                  }}
                >
                  --
                </span>
              </div>
              <div
                className={styles.workOrderDetailItem}
                style={{
                  display: workOrderDetail.rating == 0 ? 'none' : 'block',
                }}
              >
                <p
                  title={getIntl().formatMessage({
                    id: 'work.order.detail.info.9',
                    defaultValue: '满意度评价：',
                  })}
                >
                  <FormattedMessage
                    id="work.order.detail.info.9"
                    defaultMessage="满意度评价："
                  />
                </p>
                <Rate
                  disabled
                  allowHalf
                  value={parseFloat(workOrderDetail.rating)}
                />
              </div>
              {newExtIntsListDetail?.map(item => {
                if (
                  item.propertyTypeId == '1004' ||
                  item.propertyTypeId == '1006'
                ) {
                  return (
                    <div className={styles.workOrderDetailItem}>
                      <p title={item.workRecordExtDefName}>
                        {item.workRecordExtDefName}
                        <FormattedMessage
                          id="work.order.detail.colon"
                          defaultMessage="："
                        />
                      </p>
                      <p
                        title={item.workRecordExtValue}
                        className={styles.extIntsListDetail}
                      >
                        {item.workRecordExtValue?.map(items => {
                          return <Tag>{items}</Tag>;
                        })}
                      </p>
                    </div>
                  );
                } else if (item.propertyTypeId == '1007') {
                  return (
                    <div className={styles.workOrderDetailItem}>
                      <p title={item.workRecordExtDefName}>
                        {item.workRecordExtDefName}
                        <FormattedMessage
                          id="work.order.detail.colon"
                          defaultMessage="："
                        />
                      </p>
                      <p className={styles.extIntsListDetail}>
                        <Switch
                          disabled
                          defaultChecked={item.workRecordExtValue}
                        />
                      </p>
                    </div>
                  );
                } else {
                  return (
                    <div className={styles.workOrderDetailItem}>
                      <p title={item.workRecordExtDefName}>
                        {item.workRecordExtDefName}
                        <FormattedMessage
                          id="work.order.detail.colon"
                          defaultMessage="："
                        />
                      </p>
                      <p
                        title={item.workRecordExtValue}
                        className={styles.extIntsListDetail}
                      >
                        {item.workRecordExtValue
                          ? item.workRecordExtValue
                          : '--'}
                      </p>
                    </div>
                  );
                }
              })}
            </div>
          </div>
          {+summarizeStatus === 1 && (
            <div className={styles.workOrderDetailBottom}>
              <div className={styles.contentSummaryContent}>
                <div className={styles.leftDetail}>
                  <div className={styles.titleDetail}>
                    <div className={styles.line}></div>
                    <p className={styles.workOrderTitle}>
                      <FormattedMessage
                        id="work.order.detail.summary.content"
                        defaultMessage="总结内容"
                      />
                    </p>
                  </div>
                  <div className={styles.summarizeContentText}>
                    {contentSummary}
                  </div>
                </div>
                <div className={styles.rightDetail}>
                  <div className={styles.moodContent}>
                    <span className={styles.labelText}>
                      <FormattedMessage
                        id="work.order.detail.customer.mood"
                        defaultMessage="客户心情"
                      />
                    </span>
                    <img
                      className={styles.moodIcon}
                      src={
                        customerMood === 1 ? MoodGoodActiveIcon : MoodGoodIcon
                      }
                    />
                    <img
                      className={styles.moodIcon}
                      src={
                        customerMood === 2
                          ? MoodNormalActiveIcon
                          : MoodNormalIcon
                      }
                    />
                    <img
                      className={styles.moodIcon}
                      src={customerMood === 3 ? MoodBadActiveIcon : MoodBadIcon}
                    />
                  </div>
                  <div className={styles.toDoTitle}>
                    <span
                      className={styles.labelText}
                      style={{ lineHeight: '32px' }}
                    >
                      <FormattedMessage
                        id="work.order.detail.to.do"
                        defaultMessage="代办事项"
                      />
                    </span>
                  </div>
                  <div className={styles.toDoContent}>
                    {waitExecuteList?.map(item => {
                      return (
                        <Row title={item.waitExecuteEvent}>
                          <Col
                            span={24}
                            style={{
                              color:
                                +item.waitExecuteStatus === 1 ? '#999' : '#333',
                              textDecoration:
                                +item.waitExecuteStatus === 1
                                  ? 'line-through'
                                  : 'initial',
                            }}
                          >
                            {item.waitExecuteEvent}
                          </Col>
                        </Row>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
          )}
          <div className={styles.tabContent}>
            <Tabs
              activeKey={tabKey}
              onChange={onChangeTab}
              items={[
                {
                  label: getIntl().formatMessage({
                    id: 'work.order.detail.replay.detail',
                    defaultValue: '回复详情',
                  }),
                  key: 1,
                  children: (
                    <WorkReplayDetailContent
                      customerId={workOrderDetail.customerId}
                      roleId={roleId}
                    />
                  ),
                },
                {
                  label: getIntl().formatMessage({
                    id: 'work.order.detail.customer.information',
                    defaultValue: '客户信息',
                  }),
                  key: 2,
                  children: (
                    <ContactCustomersContent stateNumber={stateNumber} />
                  ),
                },
                {
                  label: getIntl().formatMessage({
                    id: 'work.order.detail.historical.work.order',
                    defaultValue: '历史工单',
                  }),
                  key: 4,
                  children: (
                    <HistoricalWorkOrdersContent
                      queryWorkOrderDetail={queryWorkOrderDetail}
                      historyCustomerWorkOrderList={
                        historyCustomerWorkOrderList
                      }
                      total={total}
                      loadingTable={loadingTable}
                      queryHistoryCustomerWorkOrder={
                        queryHistoryCustomerWorkOrder
                      }
                    />
                  ),
                },
                {
                  label: getIntl().formatMessage({
                    id: 'work.order.detail.associated.work.order',
                    defaultValue: '关联工单',
                  }),
                  key: 3,
                  children: (
                    <AssociatedWorkOrderContent
                      queryWorkOrderDetail={queryWorkOrderDetail}
                      associationWorkOrderList={associationWorkOrderTableList}
                      total={total}
                      loadingTable={loadingTable}
                      queryAssociationWorkOrder={queryAssociationWorkOrderTable}
                    />
                  ),
                },
                {
                  label: getIntl().formatMessage({
                    id: 'work.order.detail.notes.list',
                    defaultValue: '备注',
                  }),
                  key: 5,
                  children: (
                    <NotesContent ticketRemarksList={ticketRemarksList} />
                  ),
                },
              ]}
            />
            <img
              // onClick={handleWorkOrderRefresh}
              className={styles.workOrderRefreshIcon}
              src={WorkOrderRefreshIcon}
            />
          </div>
          {/*</Spin>*/}
        </div>
        <div className={styles.rightContainer}>
          <Spin spinning={rightLoading}>
            <div className={styles.rightTitle}>
              <div className={styles.line}></div>
              <p className={styles.workOrderTitle}>
                <FormattedMessage
                  id="intelligent.quality.inspection.title"
                  defaultMessage="智能质检"
                />
              </p>
            </div>
            <div className={styles.rightBottomContainer}>
              {!assessmentStatus && (
                <Form
                  ref={formRef}
                  onFinish={values =>
                    onFinish(values, workOrderDetail, 'first')
                  }
                  layout="vertical"
                  name="basic"
                >
                  <Row gutter={24}>
                    <Col span={24}>
                      <Form.Item
                        label={getIntl().formatMessage({
                          id:
                            'intelligent.quality.inspection.select.evaluating',
                          defaultValue: '选择评估表',
                        })}
                        name="assessmentId"
                        rules={[
                          {
                            required: true,
                            message: getIntl().formatMessage({
                              id:
                                'intelligent.quality.inspection.select.evaluating.required',
                              defaultMessage: '请选择一个评估表',
                            }),
                          },
                        ]}
                      >
                        <Select
                          options={evaluationList}
                          placeholder={getIntl().formatMessage({
                            id:
                              'intelligent.quality.inspection.select.evaluating.required',
                            defaultMessage: '请选择一个评估表',
                          })}
                          allowClear
                          showArrow
                          showSearch
                          disabled={
                            Object.keys(evaluationRecordResult).length > 0
                          }
                          filterOption={(inputValue, option) =>
                            option.label
                              .toLowerCase()
                              .indexOf(inputValue.toLowerCase()) >= 0
                          }
                          fieldNames={{
                            label: 'assessmentName',
                            value: 'assessmentId',
                            key: 'assessmentId',
                          }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  {/*{((evaluationRecordData?.assessmentStatus === 2 &&*/}
                  {/*  Object.keys(evaluationRecordResult).length === 0) ||*/}
                  {/*  (Object.keys(evaluationRecordResult).length === 0 &&*/}
                  {/*    Object.keys(evaluationRecordData).length === 0)) && (*/}
                  {/*  <Form.Item>*/}
                  {/*    <Button loading={evaluationLoading} htmlType="submit">*/}
                  {/*      <FormattedMessage*/}
                  {/*        id="self.assessment.details.start.evaluating"*/}
                  {/*        defaultMessage="开始评估"*/}
                  {/*      />*/}
                  {/*    </Button>*/}
                  {/*  </Form.Item>*/}
                  {/*)}*/}
                  <Form.Item>
                    <Button
                      disabled={
                        !(
                          (evaluationRecordData?.assessmentStatus === 2 &&
                            Object.keys(evaluationRecordResult).length === 0) ||
                          (Object.keys(evaluationRecordResult).length === 0 &&
                            Object.keys(evaluationRecordData).length === 0)
                        )
                      }
                      loading={evaluationLoading}
                      htmlType="submit"
                    >
                      <FormattedMessage
                        id="self.assessment.details.start.evaluating"
                        defaultMessage="开始评估"
                      />
                    </Button>
                  </Form.Item>
                  <div className={styles.formLine}></div>
                </Form>
              )}
              {assessmentStatus &&
                Object.keys(evaluationRecordResult).length > 0 && (
                  <div className={styles.qualityInspectionResultScore}>
                    <p className={styles.totalScopeTitle}>
                      <FormattedMessage
                        id="intelligent.quality.inspection.result.scope"
                        defaultMessage="评估最终得分"
                      />
                    </p>
                    <p
                      className={styles.scopeText}
                      style={{
                        color: (() => {
                          const scorePercentage =
                            (evaluationRecordResult?.totalScore /
                              evaluationRecordResult?.fullScore) *
                            100;
                          const scoreColorArray = JSON.parse(
                            evaluationRecordResult?.scoreColor || '[]',
                          );
                          const matchedScope = scoreColorArray.find(
                            scope =>
                              scorePercentage >= scope.startNum &&
                              scorePercentage <= scope.endNum,
                          );
                          return matchedScope?.colorCode || '#333'; // Default to black if no range matches
                        })(),
                      }}
                    >
                      {+evaluationRecordResult?.totalScore >= 0
                        ? evaluationRecordResult?.totalScore
                        : '--'}
                      <span
                        style={{
                          color: JSON.parse(
                            evaluationRecordResult?.scoreColor,
                          ).find(
                            scope =>
                              (evaluationRecordResult?.totalScore /
                                evaluationRecordResult?.fullScore) *
                                100 >=
                                scope.startNum &&
                              (evaluationRecordResult?.totalScore /
                                evaluationRecordResult?.fullScore) *
                                100 <=
                                scope.endNum,
                          ).colorCode
                            ? JSON.parse(
                                evaluationRecordResult?.scoreColor,
                              ).find(
                                scope =>
                                  (evaluationRecordResult?.totalScore /
                                    evaluationRecordResult?.fullScore) *
                                    100 >=
                                    scope.startNum &&
                                  (evaluationRecordResult?.totalScore /
                                    evaluationRecordResult?.fullScore) *
                                    100 <=
                                    scope.endNum,
                              ).colorCode
                            : '#13C825',
                        }}
                      >
                        {' '}
                        <FormattedMessage
                          id="selfReport.fen"
                          defaultMessage="分"
                        />
                      </span>
                    </p>
                    <div className={styles.formLine}></div>
                  </div>
                )}
              {assessmentStatus && (
                <div className={styles.qualityInspectionOverview}>
                  <p className={styles.totalScopeTitle}>
                    <FormattedMessage
                      id="over.view.title"
                      defaultValue="概览"
                    />
                  </p>
                  <p className={styles.evaluationText}>
                    <FormattedMessage
                      id="intelligent.quality.inspection.evaluation.name"
                      defaultMessage="评估表名称："
                    />
                    {evaluationRecordResult?.assessmentName}
                  </p>
                  <p className={styles.evaluationText}>
                    <FormattedMessage
                      id="intelligent.quality.inspection.evaluation.version"
                      defaultMessage="评估表版本："
                    />
                    {evaluationRecordResult?.versionNo}
                  </p>
                  <p className={styles.evaluationText}>
                    <FormattedMessage
                      id="intelligent.quality.inspection.evaluation.time"
                      defaultMessage="评估表时间："
                    />
                    {evaluationRecordResult?.createTime}
                  </p>
                  <p className={styles.evaluationText}>
                    <FormattedMessage
                      id="intelligent.quality.inspection.evaluation.agent"
                      defaultMessage="评估人："
                    />
                    {evaluationRecordResult?.assessorName}
                  </p>
                  <div className={styles.formLine}></div>
                </div>
              )}
              {Object.keys(evaluationRecordResult).length > 0 && (
                <div className={styles.ruleContainer}>
                  <Collapse
                    collapsible={'header'}
                    expandIconPosition={'end'}
                    ghost={true}
                    onChange={onChange}
                    // accordion // 移除 accordion 属性
                  >
                    {evaluationRecordResult.categories?.map(
                      (categorizeItem, index) => (
                        // <CategoryItem
                        //   categorizeItem={categorizeItem}
                        //   index={index}
                        // />
                        <Panel
                          header={
                            <div
                              className={`${
                                categorizeItem.categoryLevel == 1
                                  ? styles.levelTitle
                                  : categorizeItem.categoryLevel == 2
                                  ? styles.levelTitleSecond
                                  : styles.levelTitleThird
                              }`}
                            >
                              {categorizeItem.childrenAssessmentStatus2Count >
                                0 &&
                              categorizeItem.childrenAssessmentStatus2Count <
                                categorizeItem.childrenRulesCount ? (
                                <span className={styles.progressStatusIcon}>
                                  {UnderEvaluationIcon()}
                                </span>
                              ) : categorizeItem.childrenAssessmentStatus2Count ===
                                0 ? (
                                <div className={styles.progressIcon}></div>
                              ) : (
                                <span className={styles.progressStatusIcon}>
                                  {CompleteIcon()}
                                </span>
                              )}
                              <p className={styles.levelName}>
                                {' '}
                                {index + 1}. &nbsp;
                                {categorizeItem.categoryName}
                              </p>
                              {/*<span className={styles.arrowIcon}>{DownArrowIcon()}</span>*/}
                              <span className={styles.progressNum}>
                                {categorizeItem.childrenAssessmentStatus2Count
                                  ? categorizeItem.childrenAssessmentStatus2Count
                                  : 0}
                                /
                                {categorizeItem.childrenRulesCount
                                  ? categorizeItem.childrenRulesCount
                                  : 0}
                              </span>
                            </div>
                          }
                          key={categorizeItem.categoryId.toString()}
                        >
                          {/* 渲染规则 */}
                          {categorizeItem.rules?.map(rule => (
                            <div className={styles.levelContent}>
                              <RuleItem
                                categoryId={categorizeItem.categoryId} // 传递 categoryId
                                key={rule.ruleId}
                                rule={rule}
                              />
                            </div>
                          ))}
                          {/* 递归渲染子分类 */}
                          {categorizeItem.children?.length > 0 && (
                            <div className={styles.levelContent}>
                              {categorizeItem.children?.map(
                                (childItem, childIndex) => (
                                  <CategoryItem
                                    key={childItem.categoryId.toString()}
                                    categorizeItem={childItem}
                                    index={childIndex}
                                    prefix={index + 1}
                                  />
                                ),
                              )}
                            </div>
                          )}
                        </Panel>
                      ),
                    )}
                  </Collapse>
                </div>
              )}
            </div>
            <div className={styles.rightFooter}>
              {evaluationRecordResult?.totalScore >= 0 ? (
                <div className={styles.scopeContainer}>
                  <FormattedMessage
                    id="intelligent.quality.inspection.total.scope"
                    defaultMessage="总得分："
                  />
                  <span
                    className={styles.scopeText}
                    style={{
                      color: (() => {
                        const scorePercentage =
                          (evaluationRecordResult?.totalScore /
                            evaluationRecordResult?.fullScore) *
                          100;
                        const scoreColorArray = JSON.parse(
                          evaluationRecordResult?.scoreColor || '[]',
                        );
                        const matchedScope = scoreColorArray.find(
                          scope =>
                            scorePercentage >= scope.startNum &&
                            scorePercentage <= scope.endNum,
                        );
                        return matchedScope?.colorCode || '#333'; // Default to black if no range matches
                      })(),
                    }}
                  >
                    {' '}
                    {evaluationRecordResult?.totalScore
                      ? evaluationRecordResult?.totalScore
                      : 0}{' '}
                  </span>
                  <FormattedMessage id="selfReport.fen" defaultMessage="分" />
                </div>
              ) : null}
              {+assessmentStatus !== 2 &&
                Object.keys(evaluationRecordResult).length > 0 && (
                  <Button
                    loading={assessmentLoading}
                    disabled={!showBtn}
                    type="primary"
                    onClick={showBtn ? handleAssessmentCompleted : null}
                  >
                    <FormattedMessage
                      id="intelligent.quality.inspection.evaluated.btn"
                      defaultMessage="评估完成"
                    />
                  </Button>
                )}
              {+assessmentStatus === 2 && (
                <div
                  className={styles.exportContainer}
                  onClick={() => handleExportPDF(evaluationRecordResult)}
                >
                  {loading ? <img src={RuleLoadingIcon} /> : ExportIcon()}
                  <span>
                    <FormattedMessage
                      id="intelligent.quality.inspection.export.pdf"
                      defaultMessage="导出PDF"
                    />
                  </span>
                </div>
              )}
            </div>
          </Spin>
        </div>
      </div>
    </Spin>
  );
};

const AssociatedWorkOrderContent = props => {
  const [pageSize, setPageSize] = useState(10);
  const [pageNum, setPageNum] = useState(1);
  const [selectedRecord, setSelectedRecord] = useState(null); //鼠标移入时设置当前行的数据

  // 👈 鼠标移入时设置当前行的数据
  const handleMouseEnter = record => {
    setSelectedRecord(record);
  };

  const content = <TicketDetailComponent data={selectedRecord} />;

  const columns = [
    {
      title: (
        <FormattedMessage
          id="work.order.management.table.work.order.code"
          defaultMessage="工单编号"
        />
      ),
      width: 150,
      dataIndex: 'wordRecordCode',
      key: 'wordRecordCode',
      fixed: 'left',
      ellipsis: true,
      sorter: (a, b) => a.wordRecordCode - b.wordRecordCode,
      render: (text, record) => {
        return (
          <span
            style={{ color: '#3463FC', cursor: 'pointer' }}
            onMouseEnter={() => handleMouseEnter(record)}
          >
            <Popover
              overlayClassName="ticketDetailPopover"
              placement="topRight"
              content={content}
              title={null}
            >
              {text}
            </Popover>
          </span>
        );
      },
    },
    {
      title: (
        <FormattedMessage
          id="work.order.management.table.channel.name"
          defaultMessage="渠道名称"
        />
      ),
      width: 150,
      dataIndex: 'channelTypeName',
      key: 'channelTypeName',
      fixed: 'left',
      render: (text, record) => {
        if (record) {
          if (record.channelTypeId == '1') {
            return (
              <div className={styles.channelName}>
                <img src={TableEmailIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '7') {
            return (
              <div className={styles.channelName}>
                <img src={TablePhoneIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '2') {
            return (
              <div className={styles.channelName}>
                <img src={TableInfoIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '3') {
            return (
              <div className={styles.channelName}>
                <img src={NewFaceBookIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '4') {
            return (
              <div className={styles.channelName}>
                <img src={WhatsAppIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '8') {
            return (
              <div className={styles.channelName}>
                <img src={ChatIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '9') {
            return (
              <div className={styles.channelName}>
                <img src={AppChatOutlinedIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '10') {
            return (
              <div className={styles.channelName}>
                <img src={WebVideoOutlinedIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '11') {
            return (
              <div className={styles.channelName}>
                <img src={AppVideoOutlinedIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '12') {
            return (
              <div className={styles.channelName}>
                <img src={AwsChannelIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '13') {
            return (
              <div className={styles.channelName}>
                <img src={NewInstagramIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '14') {
            return (
              <div className={styles.channelName}>
                <img src={LineIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '15') {
            return (
              <div className={styles.channelName}>
                <img src={NewWeComIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '16') {
            return (
              <div className={styles.channelName}>
                <img src={NewWechatOfficialAccountIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '17') {
            return (
              <div className={styles.channelName}>
                <img src={NewWebOnlineVoiceIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '18') {
            return (
              <div className={styles.channelName}>
                <img src={NewAppOnlineVoiceIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '19') {
            return (
              <div className={styles.channelName}>
                <img src={TwitterIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '20') {
            return (
              <div className={styles.channelName}>
                <img src={NewTelegramIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '21') {
            return (
              <div className={styles.channelName}>
                <img src={NewWeChatMiniProgramIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '22') {
            return (
              <div className={styles.channelName}>
                <img src={NewShopifyIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '23') {
            return (
              <div className={styles.channelName}>
                <img src={NewGooglePlayIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else {
            return (
              <div className={styles.channelName}>
                <span>{record.channelTypeName}</span>
              </div>
            );
          }
        } else {
          return null;
        }
      },
    },
    {
      title: (
        <FormattedMessage
          id="work.order.management.table.priority"
          defaultMessage="优先级"
        />
      ),
      dataIndex: 'priorityLevelName',
      key: 'priorityLevelName',
      width: 100,
      render: (text, record) => {
        if (record.priorityLevelId == '1001') {
          return (
            <div className={styles.priority1}>{record.priorityLevelName}</div>
          );
        } else if (record.priorityLevelId == '1002') {
          return (
            <div className={styles.priority1}>{record.priorityLevelName}</div>
          );
        } else if (record.priorityLevelId == '1003') {
          return (
            <div className={styles.priority3}>{record.priorityLevelName}</div>
          );
        } else if (record.priorityLevelId == '1004') {
          return (
            <div className={styles.priority3}>{record.priorityLevelName}</div>
          );
        } else if (record.priorityLevelId == '1005') {
          return (
            <div className={styles.priority5}>{record.priorityLevelName}</div>
          );
        }
      },
    },
    {
      title: (
        <FormattedMessage
          id="work.order.management.table.status"
          defaultMessage="状态"
        />
      ),
      dataIndex: 'status',
      key: 'status',
      width: 150,
      render: (text, record) => {
        let status = record.status;
        if (status == 0) {
          return (
            <div className={styles.state5}>
              <div className={styles.circle}></div>
              <FormattedMessage
                id="work.order.management.table.status.text1"
                defaultMessage="待分配"
              />
            </div>
          );
        } else if (status == 1) {
          return (
            <div className={styles.state3}>
              <div className={styles.circle}></div>
              <FormattedMessage
                id="work.order.management.table.status.text2"
                defaultMessage="待客服处理"
              />
            </div>
          );
        } else if (status == 2) {
          return (
            <div className={styles.state3}>
              <div className={styles.circle}></div>
              <FormattedMessage
                id="work.order.management.table.status.text3"
                defaultMessage="待客户回复"
              />
            </div>
          );
        } else if (status == 3) {
          return (
            <div className={styles.state1}>
              <div className={styles.circle}></div>
              <FormattedMessage
                id="work.order.management.table.status.text4"
                defaultMessage="已解决"
              />
            </div>
          );
        } else if (status == 4) {
          return (
            <div className={styles.state2}>
              <div className={styles.circle}></div>
              <FormattedMessage
                id="work.order.management.table.status.text5"
                defaultMessage="已终止"
              />
            </div>
          );
        } else if (status == 5) {
          return (
            <div className={styles.state4}>
              <div className={styles.circle}></div>
              <FormattedMessage
                id="work.order.management.table.status.text6"
                defaultMessage="已转单"
              />
            </div>
          );
        }
      },
    },
    {
      title: (
        <FormattedMessage
          id="work.order.management.table.work.order.theme"
          defaultMessage="工单主题"
        />
      ),
      dataIndex: 'workRecordTheme',
      key: 'workRecordTheme',
      width: 200,
      ellipsis: true,
    },
    {
      title: (
        <FormattedMessage
          id="work.order.management.table.customer.name"
          defaultMessage="客户名称"
        />
      ),
      dataIndex: 'customerName',
      key: 'customerName',
      width: 150,
    },
    {
      title: (
        <FormattedMessage
          id="work.order.management.table.customer.contact.information"
          defaultMessage="客户联系方式"
        />
      ),
      dataIndex: 'customerContactInformation',
      key: 'customerContactInformation',
      width: 150,
    },
    {
      title: (
        <FormattedMessage
          id="work.order.management.table.seat.name"
          defaultMessage="客服名称"
        />
      ),
      dataIndex: 'agentName',
      key: 'agentName',
      width: 150,
      ellipsis: true,
    },
    {
      title: (
        <FormattedMessage
          id="work.order.management.table.creation.time"
          defaultMessage="创建时间"
        />
      ),
      dataIndex: 'createTime',
      key: 'createTime',
      width: 250,
      ellipsis: true,
    },
    {
      title: (
        <FormattedMessage
          id="work.order.management.table.resolution.time"
          defaultMessage="解决时间"
        />
      ),
      dataIndex: 'resolutionTime',
      key: 'resolutionTime',
      width: 150,
      ellipsis: true,
    },
    {
      title: (
        <FormattedMessage
          id="work.order.management.table.operation"
          defaultMessage="操作"
        />
      ),
      key: 'operation',
      fixed: 'right',
      width: 150,
      render: record => {
        return (
          <div
            className={styles.operationContent}
            onClick={() => props.queryWorkOrderDetail(record.workRecordId, 0)}
          >
            <p className={styles.details}>
              <img src={TableDetailIcon} />
              <FormattedMessage
                id="work.order.management.table.detail"
                defaultMessage="详情"
              />
            </p>
          </div>
        );
      },
    },
  ];

  return (
    <div className={styles.tableItem}>
      <Table
        columns={columns}
        dataSource={props.associationWorkOrderList}
        scroll={{
          x: 1500,
          y: 300,
        }}
        loading={props.loadingTable}
        onChange={pagination => {
          setPageSize(pagination.pageSize);
          setPageNum(pagination.current);
          props.queryAssociationWorkOrder(
            pagination.pageSize,
            pagination.current,
          );
        }}
        pagination={{
          total: props.total,
          pageSize: pageSize,
          current: pageNum,
          showSizeChanger: true,
          pageSizeOptions: [10, 20, 50, 100],
          showTotal: total => (
            <FormattedMessage
              id="studentManagement.altogether"
              defaultMessage={`共 ${total} 条`}
              values={{ total }}
            />
          ),
        }}
      />
    </div>
  );
};
const HistoricalWorkOrdersContent = props => {
  const [pageSize, setPageSize] = useState(10);
  const [pageNum, setPageNum] = useState(1);
  const [selectedRecord, setSelectedRecord] = useState(null); //鼠标移入时设置当前行的数据

  // 👈 鼠标移入时设置当前行的数据
  const handleMouseEnter = record => {
    setSelectedRecord(record);
  };

  const content = <TicketDetailComponent data={selectedRecord} />;

  const columns = [
    {
      title: (
        <FormattedMessage
          id="work.order.management.table.work.order.code"
          defaultMessage="工单编号"
        />
      ),
      width: 150,
      dataIndex: 'wordRecordCode',
      key: 'wordRecordCode',
      fixed: 'left',
      ellipsis: true,
      sorter: (a, b) => a.wordRecordCode - b.wordRecordCode,
      render: (text, record) => {
        return (
          <span
            style={{ color: '#3463FC', cursor: 'pointer' }}
            onMouseEnter={() => handleMouseEnter(record)}
          >
            <Popover
              overlayClassName="ticketDetailPopover"
              placement="topRight"
              content={content}
              title={null}
            >
              {text}
            </Popover>
          </span>
        );
      },
    },
    {
      title: (
        <FormattedMessage
          id="work.order.management.table.channel.name"
          defaultMessage="渠道类型"
        />
      ),
      width: 150,
      dataIndex: 'channelTypeName',
      key: 'channelTypeName',
      fixed: 'left',
      render: (text, record) => {
        if (record) {
          if (record.channelTypeId == '1') {
            return (
              <div className={styles.channelName}>
                <img src={TableEmailIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '7') {
            return (
              <div className={styles.channelName}>
                <img src={TablePhoneIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '2') {
            return (
              <div className={styles.channelName}>
                <img src={TableInfoIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '3') {
            return (
              <div className={styles.channelName}>
                <img src={NewFaceBookIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '4') {
            return (
              <div className={styles.channelName}>
                <img src={WhatsAppIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '8') {
            return (
              <div className={styles.channelName}>
                <img src={ChatIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '9') {
            return (
              <div className={styles.channelName}>
                <img src={AppChatOutlinedIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '10') {
            return (
              <div className={styles.channelName}>
                <img src={WebVideoOutlinedIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '11') {
            return (
              <div className={styles.channelName}>
                <img src={AppVideoOutlinedIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '12') {
            return (
              <div className={styles.channelName}>
                <img src={AwsChannelIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '13') {
            return (
              <div className={styles.channelName}>
                <img src={NewInstagramIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '14') {
            return (
              <div className={styles.channelName}>
                <img src={LineIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '15') {
            return (
              <div className={styles.channelName}>
                <img src={NewWeComIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '16') {
            return (
              <div className={styles.channelName}>
                <img src={NewWechatOfficialAccountIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '17') {
            return (
              <div className={styles.channelName}>
                <img src={NewWebOnlineVoiceIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '18') {
            return (
              <div className={styles.channelName}>
                <img src={NewAppOnlineVoiceIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '19') {
            return (
              <div className={styles.channelName}>
                <img src={TwitterIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '20') {
            return (
              <div className={styles.channelName}>
                <img src={NewTelegramIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '21') {
            return (
              <div className={styles.channelName}>
                <img src={NewWeChatMiniProgramIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '22') {
            return (
              <div className={styles.channelName}>
                <img src={NewShopifyIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else if (record.channelTypeId == '23') {
            return (
              <div className={styles.channelName}>
                <img src={NewGooglePlayIcon} />
                <span>{record.channelTypeName}</span>
              </div>
            );
          } else {
            return (
              <div className={styles.channelName}>
                <span>{record.channelTypeName}</span>
              </div>
            );
          }
        } else {
          return null;
        }
      },
    },
    {
      title: (
        <FormattedMessage
          id="work.order.management.table.priority"
          defaultMessage="优先级"
        />
      ),
      dataIndex: 'priorityLevelName',
      key: 'priorityLevelName',
      width: 150,
      render: (text, record) => {
        if (record.priorityLevelId == '1001') {
          return (
            <div className={styles.priority1}>{record.priorityLevelName}</div>
          );
        } else if (record.priorityLevelId == '1002') {
          return (
            <div className={styles.priority1}>{record.priorityLevelName}</div>
          );
        } else if (record.priorityLevelId == '1003') {
          return (
            <div className={styles.priority3}>{record.priorityLevelName}</div>
          );
        } else if (record.priorityLevelId == '1004') {
          return (
            <div className={styles.priority3}>{record.priorityLevelName}</div>
          );
        } else if (record.priorityLevelId == '1005') {
          return (
            <div className={styles.priority5}>{record.priorityLevelName}</div>
          );
        }
      },
    },
    // {
    //   title: (
    //     <FormattedMessage
    //       id="work.order.management.table.SLA.service.objectives"
    //       defaultMessage="SLA服务目标"
    //     />
    //   ),
    //   dataIndex: 'serviceObjectives',
    //   key: 'serviceObjectives',
    //   width: 270,
    //   ellipsis: true,
    //   render: (text, record) => {
    //     let serviceStatus = record.serviceStatus;
    //     if (serviceStatus == 1) {
    //       return (
    //         <div className={styles.serviceObjectivesText1}>
    //           {record.serviceObjectives}
    //         </div>
    //       );
    //     } else if (serviceStatus == 2) {
    //       return (
    //         <div className={styles.serviceObjectivesText3}>
    //           {record.serviceObjectives}
    //         </div>
    //       );
    //     } else if (serviceStatus == 3) {
    //       return (
    //         <div className={styles.serviceObjectivesText4}>
    //           {record.serviceObjectives}
    //         </div>
    //       );
    //     } else if (serviceStatus == 4) {
    //       return (
    //         <div className={styles.serviceObjectivesText3}>
    //           {record.serviceObjectives}
    //         </div>
    //       );
    //     } else if (serviceStatus == 5) {
    //       return (
    //         <div className={styles.serviceObjectivesText2}>
    //           {record.serviceObjectives}
    //         </div>
    //       );
    //     } else if (serviceStatus == 6) {
    //       return (
    //         <div className={styles.serviceObjectivesText2}>
    //           {record.serviceObjectives}
    //         </div>
    //       );
    //     }
    //   },
    // },
    {
      title: (
        <FormattedMessage
          id="work.order.management.table.status"
          defaultMessage="状态"
        />
      ),
      dataIndex: 'status',
      key: 'status',
      width: 150,
      render: (text, record) => {
        let status = record.status;
        if (record) {
          if (record.agentId == '1001') {
            return <span>--</span>;
          } else {
            if (status == 0) {
              return (
                <div className={styles.state5}>
                  <div className={styles.circle}></div>
                  <FormattedMessage
                    id="work.order.management.table.status.text1"
                    defaultMessage="待分配"
                  />
                </div>
              );
            } else if (status == 1) {
              return (
                <div className={styles.state3}>
                  <div className={styles.circle}></div>
                  <FormattedMessage
                    id="work.order.management.table.status.text2"
                    defaultMessage="待客服处理"
                  />
                </div>
              );
            } else if (status == 2) {
              return (
                <div className={styles.state3}>
                  <div className={styles.circle}></div>
                  <FormattedMessage
                    id="work.order.management.table.status.text3"
                    defaultMessage="待客户回复"
                  />
                </div>
              );
            } else if (status == 3) {
              return (
                <div className={styles.state1}>
                  <div className={styles.circle}></div>
                  <FormattedMessage
                    id="work.order.management.table.status.text4"
                    defaultMessage="已解决"
                  />
                </div>
              );
            } else if (status == 4) {
              return (
                <div className={styles.state2}>
                  <div className={styles.circle}></div>
                  <FormattedMessage
                    id="work.order.management.table.status.text5"
                    defaultMessage="已终止"
                  />
                </div>
              );
            } else if (status == 5) {
              return (
                <div className={styles.state4}>
                  <div className={styles.circle}></div>
                  <FormattedMessage
                    id="work.order.management.table.status.text6"
                    defaultMessage="已转单"
                  />
                </div>
              );
            }
          }
        }
      },
    },
    {
      title: (
        <FormattedMessage
          id="work.order.management.table.work.order.theme"
          defaultMessage="工单主题"
        />
      ),
      dataIndex: 'workRecordTheme',
      key: 'workRecordTheme',
      width: 200,
      ellipsis: true,
    },
    {
      title: (
        <FormattedMessage
          id="work.order.management.table.robot.work.order"
          defaultMessage="机器人工单"
        />
      ),
      dataIndex: 'agentId',
      key: 'agentId',
      width: 200,
      ellipsis: true,
      render: (text, record) => {
        if (record.agentId) {
          if (record.agentId == '1001') {
            return (
              <FormattedMessage
                id="work.order.management.table.robot.work.order.yes"
                defaultMessage="是"
              />
            );
          } else {
            return (
              <FormattedMessage
                id="work.order.management.table.robot.work.order.no"
                defaultMessage="否"
              />
            );
          }
        } else {
          return null;
        }
      },
    },
    {
      title: (
        <FormattedMessage
          id="work.order.management.table.customer.name"
          defaultMessage="客户名称"
        />
      ),
      dataIndex: 'customerName',
      key: 'customerName',
      width: 150,
    },
    {
      title: (
        <FormattedMessage
          id="work.order.management.table.customer.contact.information"
          defaultMessage="客户联系方式"
        />
      ),
      dataIndex: 'customerContactInformation',
      key: 'customerContactInformation',
      width: 150,
      ellipsis: true,
    },
    {
      title: (
        <FormattedMessage
          id="work.order.management.table.seat.name"
          defaultMessage="客服名称"
        />
      ),
      dataIndex: 'agentName',
      key: 'agentName',
      width: 150,
      ellipsis: true,
    },
    {
      title: (
        <FormattedMessage
          id="work.order.management.table.creation.time"
          defaultMessage="创建时间"
        />
      ),
      dataIndex: 'createTime',
      key: 'createTime',
      width: 150,
      ellipsis: true,
      // defaultSortOrder: 'descend',
      sorter: (a, b) => {
        let timestamp1 = Date.parse(new Date(a.createTime));
        let timestamp2 = Date.parse(new Date(b.createTime));
        return timestamp1 - timestamp2;
      },
    },
    {
      title: (
        <FormattedMessage
          id="work.order.management.table.modify.time"
          defaultMessage="修改时间"
        />
      ),
      dataIndex: 'modifyTime',
      key: 'modifyTime',
      width: 150,
      ellipsis: true,
      // defaultSortOrder: 'descend',
      sorter: (a, b) => {
        let timestamp1 = Date.parse(new Date(a.modifyTime));
        let timestamp2 = Date.parse(new Date(b.modifyTime));
        return timestamp1 - timestamp2;
      },
    },
    {
      title: (
        <FormattedMessage
          id="work.order.management.table.resolution.time"
          defaultMessage="解决时间"
        />
      ),
      dataIndex: 'resolveTime',
      key: 'resolveTime',
      width: 150,
      ellipsis: true,
      // defaultSortOrder: 'descend',
      sorter: (a, b) => {
        let timestamp1 = Date.parse(new Date(a.resolveTime));
        let timestamp2 = Date.parse(new Date(b.resolveTime));
        return timestamp1 - timestamp2;
      },
    },
    {
      title: (
        <FormattedMessage
          id="work.order.management.table.operation"
          defaultMessage="操作"
        />
      ),
      key: 'operation',
      fixed: 'right',
      width: 150,
      render: (text, record) => {
        return (
          <div
            className={styles.operationContent}
            onClick={() => props.queryWorkOrderDetail(record.workRecordId, 0)}
          >
            <p className={styles.details}>
              <img src={TableDetailIcon} />
              <FormattedMessage
                id="work.order.management.table.detail"
                defaultMessage="详情"
              />
            </p>
          </div>
        );
      },
    },
  ];

  return (
    <div className={styles.tableItem}>
      <Table
        columns={columns}
        dataSource={props.historyCustomerWorkOrderList}
        scroll={{
          x: 1500,
          y: 300,
        }}
        loading={props.loadingTable}
        onChange={pagination => {
          setPageSize(pagination.pageSize);
          setPageNum(pagination.current);
          props.queryHistoryCustomerWorkOrder(
            pagination.pageSize,
            pagination.current,
          );
        }}
        pagination={{
          total: props.total,
          pageSize: pageSize,
          current: pageNum,
          showSizeChanger: true,
          pageSizeOptions: [10, 20, 50, 100],
          showTotal: total => (
            <FormattedMessage
              id="studentManagement.altogether"
              defaultMessage={`共 ${total} 条`}
              values={{ total }}
            />
          ),
        }}
      />
    </div>
  );
};

const NotesContent = props => {
  if (props.ticketRemarksList.length > 0) {
    return (
      <div>
        {props.ticketRemarksList?.map(item => {
          return (
            <div className={styles.notesContent}>
              <div className={styles.noteTitle}>
                <img src={EmailUser} />
                <span>{item.operatorName}</span>
                <span className={styles.timeText}>{item.createTime}</span>
              </div>
              <div
                style={{ whiteSpace: 'pre-wrap' }}
                className={styles.noteDetail}
              >
                {item.operationLogReason}
              </div>
            </div>
          );
        })}
      </div>
    );
  } else {
    return (
      <div className={styles.noDataImg}>
        <img src={NoDataImg} />
        <p>
          <FormattedMessage
            id="work.order.notes.no.data"
            defaultMessage="暂无备注信息。"
          />
        </p>
      </div>
    );
  }
};

export default IntelligentQualityInspection;
