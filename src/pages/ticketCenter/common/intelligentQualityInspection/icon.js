export const ExportIcon = () => (
  <svg
    style={{ marginRight: '5px', float: 'left', marginTop: '11px' }}
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
  >
    <path
      d="M11.147 8.11485C10.9363 8.11812 10.7664 8.29129 10.7696 8.50204V9.47411C10.7696 9.89887 10.4331 10.2452 10.0181 10.2452H2.50792C2.09296 10.2452 1.75641 9.89887 1.75641 9.47411V2.53896C1.75641 2.1142 2.09296 1.76785 2.50792 1.76785H5.12188L6.09231 3.49469C6.17072 3.63192 6.31776 3.70217 6.46316 3.68584H10.0165C10.4315 3.68584 10.768 4.03218 10.768 4.45695V5.42085C10.768 5.63486 10.9379 5.80804 11.147 5.80804C11.3561 5.80804 11.5244 5.63486 11.5244 5.42085C11.5244 5.41104 11.5244 5.40124 11.5228 5.39144V4.45695C11.5228 3.60578 10.8497 2.91472 10.0198 2.91472H6.63633L5.66754 1.19441C5.58095 1.03921 5.45679 0.998366 5.34733 1.00163L5.3457 1H2.50302C1.67309 1 1 1.69106 1 2.54223V9.45777C1 10.3089 1.67309 11 2.50302 11H10.0198C10.8497 11 11.5228 10.3089 11.5228 9.45777V8.53145C11.5244 8.52165 11.5244 8.51185 11.5244 8.50204C11.5261 8.40075 11.4868 8.30273 11.415 8.23084C11.3447 8.15733 11.2483 8.11648 11.147 8.11485Z"
      fill="#3463FC"
    />
    <path
      d="M7.13923 5.31313C7.19967 5.24778 7.28299 5.21021 7.37285 5.21021C7.46107 5.21021 7.54602 5.24778 7.60647 5.31313L8.9984 6.79491L9.00657 6.80308H9.0082L9.01637 6.81125C9.14217 6.95012 9.14217 7.1625 9.01637 7.30137L7.61954 8.79132C7.56073 8.85667 7.47741 8.89261 7.38919 8.89424C7.30096 8.89424 7.21764 8.8583 7.15883 8.79295L7.1523 8.78479C7.0265 8.64592 7.0265 8.43354 7.1523 8.29467L7.99366 7.39612H4.17239C3.99432 7.39612 3.84891 7.24092 3.84891 7.05141C3.84891 6.8619 3.99432 6.70669 4.17239 6.70669H7.97896L7.13923 5.80978C7.01016 5.67091 7.01016 5.45363 7.13923 5.31313Z"
      fill="#3463FC"
    />
  </svg>
);
export const DownArrowIcon = () => (
  <svg
    style={{ float: 'left', marginTop: '12px' }}
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <path
      d="M7.68299 11.734C7.98699 11.854 8.34999 11.794 8.59699 11.555L14.753 5.6C15.083 5.281 15.083 4.765 14.753 4.446C14.423 4.127 13.89 4.127 13.56 4.446L7.99999 9.823L2.43999 4.445C2.10999 4.126 1.57599 4.126 1.24699 4.445C0.917994 4.764 0.917994 5.28 1.24699 5.599L7.40299 11.553C7.48599 11.634 7.58099 11.694 7.68299 11.734Z"
      fill="#999999"
    />
  </svg>
);
export const CompleteIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="8" cy="8" r="8" fill="#13C825" />
    <path
      d="M4 8L7.47051 11.5755L13.0695 6.03193"
      stroke="white"
      stroke-width="1.2"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);
export const AiScopeIcon = () => (
  <svg
    style={{ float: 'left', marginTop: '7px' }}
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <path
      d="M4.28629 3.98035C4.54335 3.98041 4.76921 4.10593 4.91032 4.29871C5.01042 4.35464 5.09034 4.44079 5.13883 4.5448L5.15739 4.58875L8.44059 13.9452C8.54687 14.2467 8.35716 14.5837 8.01774 14.6981C7.69558 14.8067 7.35425 14.6764 7.22965 14.4042L7.21207 14.3602L6.36051 11.9354H2.10856L1.25797 14.3602C1.15158 14.6616 0.791061 14.8131 0.452309 14.6981C0.129587 14.5893 -0.0573393 14.2796 0.0157858 13.9901L0.0294577 13.9442L3.31266 4.58972C3.34527 4.50243 3.39821 4.42393 3.46793 4.36218C3.5376 4.30049 3.62165 4.25698 3.71207 4.23523C3.78435 4.1548 3.8731 4.09072 3.97184 4.04675C4.07059 4.00279 4.17823 3.98021 4.28629 3.98035ZM10.3244 7.96472C10.6653 7.96472 10.9452 8.13531 10.9728 8.35144L10.9748 8.38562V14.2772C10.9746 14.51 10.6826 14.6981 10.3244 14.6981C9.98342 14.698 9.70376 14.5282 9.67692 14.3114L9.67497 14.2772V8.3866C9.67503 8.15384 9.96575 7.96486 10.3244 7.96472ZM2.55485 10.661H5.91325L4.23454 5.87781L2.55485 10.661ZM11.2902 0.970583C11.4306 1.00832 11.5504 1.1006 11.6232 1.22644C11.696 1.35256 11.7155 1.50276 11.6779 1.64343C11.6402 1.78422 11.5483 1.90454 11.422 1.97742C11.3209 2.03576 11.2047 2.0598 11.09 2.04773C10.8644 2.2192 10.5314 2.48236 10.3088 2.70496C10.1461 2.86763 9.96216 3.08959 9.80778 3.28504C9.81644 3.29847 9.82514 3.31215 9.83317 3.32605C9.95944 3.54476 9.996 3.80386 9.93668 4.04871C10.089 4.15752 10.2492 4.26479 10.3908 4.34656C10.7174 4.53511 11.2117 4.73956 11.4543 4.83582C11.4645 4.82923 11.4749 4.8224 11.4855 4.81629C11.575 4.76467 11.6741 4.73114 11.7765 4.71765C11.8789 4.70422 11.9835 4.71146 12.0832 4.73816C12.1828 4.76492 12.2767 4.81113 12.3586 4.8739C12.4403 4.93671 12.5091 5.0151 12.5607 5.10437C12.6124 5.19382 12.6458 5.29298 12.6593 5.39539C12.6728 5.49783 12.6665 5.60222 12.6398 5.70203C12.6131 5.80186 12.566 5.89542 12.5031 5.97742C12.4402 6.05928 12.362 6.12794 12.2726 6.17957C12.1831 6.23125 12.0841 6.26471 11.9816 6.2782C11.8791 6.29168 11.7748 6.28541 11.675 6.25867C11.5752 6.2319 11.4815 6.18579 11.3996 6.12293C11.3176 6.06001 11.2491 5.98096 11.1974 5.89148C11.1459 5.8022 11.1123 5.70363 11.0988 5.60144C11.0939 5.56402 11.0915 5.52574 11.092 5.48816C10.8914 5.32917 10.4591 4.99531 10.1271 4.80359C9.98252 4.72012 9.80486 4.63339 9.63102 4.55457C9.58272 4.59704 9.52934 4.63521 9.47282 4.66785C9.24732 4.79793 8.97917 4.83289 8.7277 4.7655C8.47642 4.69801 8.26214 4.53379 8.132 4.30847C8.00187 4.083 7.96604 3.81483 8.03336 3.56336C8.10079 3.31182 8.26585 3.09688 8.49137 2.96668C8.71685 2.83658 8.98503 2.80168 9.23649 2.86902C9.25482 2.87393 9.27326 2.87969 9.29118 2.88562C9.50105 2.72201 9.75455 2.51307 9.93571 2.33191C10.161 2.10663 10.4285 1.76948 10.5998 1.54382C10.5951 1.48257 10.6002 1.41973 10.6164 1.35925C10.6541 1.21853 10.747 1.09812 10.8732 1.02527C10.9993 0.952668 11.1497 0.932957 11.2902 0.970583ZM8.95524 3.28504C8.66251 3.28508 8.42505 3.52258 8.42497 3.81531C8.42509 4.108 8.66253 4.34553 8.95524 4.34558C9.2478 4.34537 9.48539 4.10789 9.48551 3.81531C9.48543 3.52269 9.24783 3.28525 8.95524 3.28504Z"
      fill="#333333"
    />
    <path
      d="M4.28629 3.98035C4.54335 3.98041 4.76921 4.10593 4.91032 4.29871C5.01042 4.35464 5.09034 4.44079 5.13883 4.5448L5.15739 4.58875L8.44059 13.9452C8.54687 14.2467 8.35716 14.5837 8.01774 14.6981C7.69558 14.8067 7.35425 14.6764 7.22965 14.4042L7.21207 14.3602L6.36051 11.9354H2.10856L1.25797 14.3602C1.15158 14.6616 0.791061 14.8131 0.452309 14.6981C0.129587 14.5893 -0.0573393 14.2796 0.0157858 13.9901L0.0294577 13.9442L3.31266 4.58972C3.34527 4.50243 3.39821 4.42393 3.46793 4.36218C3.5376 4.30049 3.62165 4.25698 3.71207 4.23523C3.78435 4.1548 3.8731 4.09072 3.97184 4.04675C4.07059 4.00279 4.17823 3.98021 4.28629 3.98035ZM10.3244 7.96472C10.6653 7.96472 10.9452 8.13531 10.9728 8.35144L10.9748 8.38562V14.2772C10.9746 14.51 10.6826 14.6981 10.3244 14.6981C9.98342 14.698 9.70376 14.5282 9.67692 14.3114L9.67497 14.2772V8.3866C9.67503 8.15384 9.96575 7.96486 10.3244 7.96472ZM2.55485 10.661H5.91325L4.23454 5.87781L2.55485 10.661ZM11.2902 0.970583C11.4306 1.00832 11.5504 1.1006 11.6232 1.22644C11.696 1.35256 11.7155 1.50276 11.6779 1.64343C11.6402 1.78422 11.5483 1.90454 11.422 1.97742C11.3209 2.03576 11.2047 2.0598 11.09 2.04773C10.8644 2.2192 10.5314 2.48236 10.3088 2.70496C10.1461 2.86763 9.96216 3.08959 9.80778 3.28504C9.81644 3.29847 9.82514 3.31215 9.83317 3.32605C9.95944 3.54476 9.996 3.80386 9.93668 4.04871C10.089 4.15752 10.2492 4.26479 10.3908 4.34656C10.7174 4.53511 11.2117 4.73956 11.4543 4.83582C11.4645 4.82923 11.4749 4.8224 11.4855 4.81629C11.575 4.76467 11.6741 4.73114 11.7765 4.71765C11.8789 4.70422 11.9835 4.71146 12.0832 4.73816C12.1828 4.76492 12.2767 4.81113 12.3586 4.8739C12.4403 4.93671 12.5091 5.0151 12.5607 5.10437C12.6124 5.19382 12.6458 5.29298 12.6593 5.39539C12.6728 5.49783 12.6665 5.60222 12.6398 5.70203C12.6131 5.80186 12.566 5.89542 12.5031 5.97742C12.4402 6.05928 12.362 6.12794 12.2726 6.17957C12.1831 6.23125 12.0841 6.26471 11.9816 6.2782C11.8791 6.29168 11.7748 6.28541 11.675 6.25867C11.5752 6.2319 11.4815 6.18579 11.3996 6.12293C11.3176 6.06001 11.2491 5.98096 11.1974 5.89148C11.1459 5.8022 11.1123 5.70363 11.0988 5.60144C11.0939 5.56402 11.0915 5.52574 11.092 5.48816C10.8914 5.32917 10.4591 4.99531 10.1271 4.80359C9.98252 4.72012 9.80486 4.63339 9.63102 4.55457C9.58272 4.59704 9.52934 4.63521 9.47282 4.66785C9.24732 4.79793 8.97917 4.83289 8.7277 4.7655C8.47642 4.69801 8.26214 4.53379 8.132 4.30847C8.00187 4.083 7.96604 3.81483 8.03336 3.56336C8.10079 3.31182 8.26585 3.09688 8.49137 2.96668C8.71685 2.83658 8.98503 2.80168 9.23649 2.86902C9.25482 2.87393 9.27326 2.87969 9.29118 2.88562C9.50105 2.72201 9.75455 2.51307 9.93571 2.33191C10.161 2.10663 10.4285 1.76948 10.5998 1.54382C10.5951 1.48257 10.6002 1.41973 10.6164 1.35925C10.6541 1.21853 10.747 1.09812 10.8732 1.02527C10.9993 0.952668 11.1497 0.932957 11.2902 0.970583ZM8.95524 3.28504C8.66251 3.28508 8.42505 3.52258 8.42497 3.81531C8.42509 4.108 8.66253 4.34553 8.95524 4.34558C9.2478 4.34537 9.48539 4.10789 9.48551 3.81531C9.48543 3.52269 9.24783 3.28525 8.95524 3.28504Z"
      fill="url(#paint0_linear_3564_25687)"
    />
    <path
      d="M14.0921 1.37118C14.0782 1.35691 14.0611 1.34615 14.0422 1.33981C14.0266 1.33457 14.0101 1.33246 13.9936 1.33362C13.9772 1.33478 13.9611 1.33918 13.9464 1.34656C13.9317 1.35394 13.9185 1.36416 13.9077 1.37664C13.8969 1.38912 13.8887 1.4036 13.8835 1.41927L13.6011 2.27138L12.7512 2.5704C12.7261 2.57907 12.7044 2.59549 12.6892 2.6173C12.674 2.63911 12.6661 2.66519 12.6667 2.69176C12.6673 2.71834 12.6763 2.74404 12.6924 2.76517C12.7085 2.7863 12.7309 2.80175 12.7564 2.80931L13.5995 3.06442L13.8642 3.91183C13.8721 3.93719 13.8878 3.9594 13.9091 3.97524C13.9304 3.99108 13.9562 3.99974 13.9827 3.99997C14.0093 4.00021 14.0352 3.992 14.0568 3.97653C14.0783 3.96107 14.0945 3.93914 14.1028 3.91392L14.3857 3.06285L15.2524 2.8046C15.2782 2.79726 15.301 2.78181 15.3174 2.76052C15.3338 2.73924 15.3429 2.71324 15.3434 2.68637C15.344 2.6595 15.3358 2.63316 15.3203 2.61125C15.3048 2.58934 15.2826 2.573 15.2571 2.56465L14.3868 2.26929L14.1221 1.42188C14.1163 1.40283 14.106 1.38545 14.0921 1.37118Z"
      fill="#333333"
    />
    <path
      d="M14.0921 1.37118C14.0782 1.35691 14.0611 1.34615 14.0422 1.33981C14.0266 1.33457 14.0101 1.33246 13.9936 1.33362C13.9772 1.33478 13.9611 1.33918 13.9464 1.34656C13.9317 1.35394 13.9185 1.36416 13.9077 1.37664C13.8969 1.38912 13.8887 1.4036 13.8835 1.41927L13.6011 2.27138L12.7512 2.5704C12.7261 2.57907 12.7044 2.59549 12.6892 2.6173C12.674 2.63911 12.6661 2.66519 12.6667 2.69176C12.6673 2.71834 12.6763 2.74404 12.6924 2.76517C12.7085 2.7863 12.7309 2.80175 12.7564 2.80931L13.5995 3.06442L13.8642 3.91183C13.8721 3.93719 13.8878 3.9594 13.9091 3.97524C13.9304 3.99108 13.9562 3.99974 13.9827 3.99997C14.0093 4.00021 14.0352 3.992 14.0568 3.97653C14.0783 3.96107 14.0945 3.93914 14.1028 3.91392L14.3857 3.06285L15.2524 2.8046C15.2782 2.79726 15.301 2.78181 15.3174 2.76052C15.3338 2.73924 15.3429 2.71324 15.3434 2.68637C15.344 2.6595 15.3358 2.63316 15.3203 2.61125C15.3048 2.58934 15.2826 2.573 15.2571 2.56465L14.3868 2.26929L14.1221 1.42188C14.1163 1.40283 14.106 1.38545 14.0921 1.37118Z"
      fill="url(#paint1_linear_3564_25687)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_3564_25687"
        x1="-1.6857e-06"
        y1="8.32484"
        x2="8.52394"
        y2="3.01671"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#187CFC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_3564_25687"
        x1="12.6667"
        y1="2.75962"
        x2="14.3757"
        y2="1.59697"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#187CFC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
    </defs>
  </svg>
);
export const AgentIcon = () => (
  <svg
    style={{ float: 'left', marginTop: '7px' }}
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <path
      d="M7.99999 4.19047C10.104 4.19047 11.8095 5.89599 11.8095 7.99999H10.6667C10.6666 7.47647 10.5125 6.96453 10.2235 6.528C9.93456 6.09147 9.52347 5.74966 9.04153 5.54519C8.55959 5.34071 8.02812 5.28261 7.51339 5.37814C6.99866 5.47367 6.52343 5.7186 6.14696 6.08238C5.77048 6.44617 5.50942 6.91273 5.3963 7.42388C5.28319 7.93504 5.32304 8.46818 5.51088 8.95684C5.69872 9.44551 6.02624 9.86807 6.45262 10.1718C6.87899 10.4756 7.38534 10.6472 7.90856 10.6651L7.99999 10.6667C10.416 10.6667 12.4571 12.2731 13.1124 14.4762H11.9047C11.2952 12.9154 9.77675 11.8095 7.99999 11.8095C6.22322 11.8095 4.70475 12.9154 4.09522 14.4762H2.8876C3.10803 13.7397 3.48464 13.0595 3.99177 12.4817C4.49889 11.904 5.12461 11.4424 5.82627 11.1284C5.32101 10.778 4.90824 10.3104 4.62334 9.76551C4.33843 9.22066 4.1899 8.61484 4.19046 7.99999C4.19046 5.89599 5.89599 4.19047 7.99999 4.19047ZM7.99999 1.5238C10.8137 1.5238 13.1185 3.70285 13.3192 6.46514C13.6427 6.70286 13.8828 7.0367 14.0052 7.41899C14.1277 7.80129 14.1262 8.21249 14.001 8.59391C13.8759 8.97532 13.6334 9.30744 13.3083 9.54285C12.9831 9.77826 12.5919 9.90492 12.1905 9.90476L9.71427 9.90514C9.59434 10.065 9.42713 10.1832 9.23634 10.2428C9.04554 10.3024 8.84083 10.3004 8.6512 10.2372C8.46157 10.174 8.29663 10.0527 8.17975 9.89058C8.06288 9.72842 7.99999 9.5336 7.99999 9.33371C7.99999 9.13382 8.06288 8.939 8.17975 8.77684C8.29663 8.61468 8.46157 8.49341 8.6512 8.4302C8.84083 8.36699 9.04554 8.36505 9.23634 8.42465C9.42713 8.48425 9.59434 8.60237 9.71427 8.76228L12.1905 8.7619C12.3925 8.7619 12.5863 8.68163 12.7292 8.53874C12.8721 8.39586 12.9524 8.20206 12.9524 7.99999C12.9524 7.79792 12.8721 7.60413 12.7292 7.46125C12.5863 7.31836 12.3925 7.23809 12.1905 7.23809V6.85714C12.1905 4.54285 10.3143 2.66666 7.99999 2.66666C5.6857 2.66666 3.80951 4.54285 3.80951 6.85714V7.23809C3.60744 7.23809 3.41365 7.31836 3.27076 7.46125C3.12788 7.60413 3.0476 7.79792 3.0476 7.99999C3.0476 8.20206 3.12788 8.39586 3.27076 8.53874C3.41365 8.68163 3.60744 8.7619 3.80951 8.7619V9.90476C3.40818 9.90481 3.01709 9.77811 2.69205 9.54272C2.367 9.30733 2.12461 8.97528 1.99946 8.59397C1.87431 8.21265 1.8728 7.80155 1.99515 7.41933C2.11749 7.0371 2.35744 6.70329 2.68075 6.46552C2.88151 3.70285 5.18627 1.5238 7.99999 1.5238Z"
      fill="url(#paint0_linear_3564_25716)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_3564_25716"
        x1="1.90448"
        y1="8.45159"
        x2="9.99747"
        y2="3.28892"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#187CFC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
    </defs>
  </svg>
);
export const AgentRuleIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <path
      d="M7.99998 14.6667C11.6819 14.6667 14.6666 11.6819 14.6666 8.00004C14.6666 4.31814 11.6819 1.33337 7.99998 1.33337C4.31808 1.33337 1.33331 4.31814 1.33331 8.00004C1.33331 11.6819 4.31808 14.6667 7.99998 14.6667Z"
      fill="#FCB830"
    />
    <path
      d="M8 5.33337V8.00004"
      stroke="white"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M8 10.6666H8.00667"
      stroke="white"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);
export const UnderEvaluationIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="8" cy="8" r="7.5" fill="white" stroke="#E6E6E6" />
    <path
      d="M7.99996 14.6667C9.76807 14.6667 11.4638 13.9643 12.714 12.7141C13.9642 11.4638 14.6666 9.76815 14.6666 8.00004C14.6666 6.23193 13.9642 4.53624 12.714 3.286C11.4638 2.03575 9.76807 1.33337 7.99996 1.33337L7.99996 8.00004L7.99996 14.6667Z"
      fill="#13C825"
    />
  </svg>
);
export const EvaluationErrorIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <path
      d="M2 8C2 9.5913 2.63214 11.1174 3.75736 12.2426C4.88258 13.3679 6.4087 14 8 14C9.5913 14 11.1174 13.3679 12.2426 12.2426C13.3679 11.1174 14 9.5913 14 8C14 6.4087 13.3679 4.88258 12.2426 3.75736C11.1174 2.63214 9.5913 2 8 2C6.4087 2 4.88258 2.63214 3.75736 3.75736C2.63214 4.88258 2 6.4087 2 8Z"
      fill="#F22417"
    />
    <path
      d="M8.00047 11.4376C7.80481 11.4221 7.62312 11.3302 7.49477 11.1817C7.36642 11.0332 7.30174 10.8401 7.31475 10.6443C7.30174 10.4485 7.36642 10.2554 7.49477 10.1069C7.62312 9.95841 7.80481 9.86648 8.00047 9.85101C8.19613 9.86648 8.37781 9.95841 8.50617 10.1069C8.63452 10.2554 8.69919 10.4485 8.68618 10.6443C8.69919 10.8401 8.63452 11.0332 8.50617 11.1817C8.37781 11.3302 8.19613 11.4221 8.00047 11.4376ZM7.7759 4.56201H8.22418C8.35483 4.572 8.47625 4.63316 8.56204 4.7322C8.64784 4.83123 8.69106 4.96013 8.68232 5.09087V5.12087L8.49932 8.82287C8.50128 8.94878 8.45493 9.07065 8.3698 9.16343C8.28466 9.25622 8.16722 9.31286 8.04161 9.32173H7.95847C7.83286 9.31286 7.71541 9.25622 7.63028 9.16343C7.54515 9.07065 7.4988 8.94878 7.50075 8.82287L7.31818 5.12087C7.30394 4.99019 7.34097 4.85908 7.42147 4.75516C7.50197 4.65123 7.61966 4.5826 7.74975 4.56373L7.7759 4.56201Z"
      fill="white"
    />
  </svg>
);
