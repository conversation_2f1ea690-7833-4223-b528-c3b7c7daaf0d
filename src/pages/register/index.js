import React, { Component } from 'react';
import {
  connect,
  getIntl,
  FormattedMessage,
  getLocale,
  setLocale,
  history,
} from 'umi';
import {
  Input,
  Button,
  Checkbox,
  Form,
  Modal,
  Row,
  Col,
  Dropdown,
  notification,
} from 'antd';
import styles from './index.less';
import LogoImg from '../../assets/logo.png';
import TermsServiceContent from '../../components/TermsService/index';
import LangIcon from '../../assets/langIconWhite.svg';
import LoginReturn from '../../assets/login-return.png';

const { TextArea } = Input;
const items = [
  {
    key: '1',
    label: '中文',
  },
  {
    key: '2',
    label: 'English',
  },
  {
    key: '3',
    label: 'Deutsch',
  },
  {
    key: '4',
    label: '日本語',
  },
];
class RegisterContent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      isModalOpen: false,
      email: '',
      loadings: [],
      langText: '中文',
      captchaBtnClick: false,
      captchaCount: 60,
      awsIdentification: '', // aws跳转标识
    };
  }
  componentDidMount() {
    console.log(history.location.query, '======history=====');
    let storageAws = localStorage.getItem('awsIdentification');
    const urlParams = history.location.query?.awsIdentification;
    this.setState({
      awsIdentification: urlParams ? urlParams : storageAws,
    });
    let lang = localStorage.getItem('lang');
    if (lang) {
      setLocale(lang, false);
      if (lang == 'zh-CN') {
        this.setState({
          langText: '中文',
          defaultSelectedKeys: '1',
        });
      } else if (lang == 'en-US') {
        this.setState({
          langText: 'English',
          defaultSelectedKeys: '2',
        });
      } else if (lang == 'ja') {
        this.setState({
          langText: '日本語',
          defaultSelectedKeys: '4',
        });
      } else if (lang == 'de-DE') {
        this.setState({
          langText: 'Deutsch',
          defaultSelectedKeys: '3',
        });
      } else if (lang == 'id-ID') {
        this.setState({
          langText: 'Indonesia',
          defaultSelectedKeys: '5',
        });
      }
    } else {
      let newLang = navigator.language || navigator.userLanguage; //常规浏览器语言和IE浏览器
      setLocale(newLang, false);
      if (newLang == 'zh-CN') {
        this.setState({
          langText: '中文',
          defaultSelectedKeys: '1',
        });
      } else if (newLang == 'en-US') {
        this.setState({
          langText: 'English',
          defaultSelectedKeys: '2',
        });
      } else if (newLang == 'ja') {
        this.setState({
          langText: '日本語',
          defaultSelectedKeys: '4',
        });
      } else if (newLang == 'de-DE') {
        this.setState({
          langText: 'Deutsch',
          defaultSelectedKeys: '3',
        });
      } else if (newLang == 'id-ID') {
        this.setState({
          langText: 'Indonesia',
          defaultSelectedKeys: '5',
        });
      }
    }
  }
  componentWillUnmount() {
    localStorage.removeItem('awsIdentification');
  }
  handleMenuClick = e => {
    if (e.key === '1') {
      this.setState({
        langText: '中文',
      });
      setLocale('zh-CN', false);
      localStorage.setItem('lang', 'zh-CN');
    } else if (e.key === '2') {
      this.setState({
        langText: 'English',
      });
      setLocale('en-US', false);
      localStorage.setItem('lang', 'en-US');
    } else if (e.key === '3') {
      this.setState({
        langText: '德文',
      });
      setLocale('de-DE', false);
      localStorage.setItem('lang', 'de-DE');
    } else if (e.key === '4') {
      this.setState({
        langText: '日文',
      });
      setLocale('ja', false);
      localStorage.setItem('lang', 'ja');
    } else if (e.key === '5') {
      this.setState({
        langText: 'Indonesia',
      });
      setLocale('id-ID', false);
      localStorage.setItem('lang', 'id-ID');
    }
  };
  /**
   * 注册
   * @param values
   */
  onFinish = values => {
    console.log('Success:', this.state.awsIdentification);
    // 设置 登录按钮的loading状态
    this.setState(({ loadings }) => {
      const newLoadings = [...loadings];
      newLoadings[0] = true;
      return {
        loadings: newLoadings,
      };
    });

    //加参数，把路由的参数传过来
    let newData = {
      ...values,
      // awsIdentification: history.location?.query?.awsIdentification,
      awsIdentification: this.state.awsIdentification,
    };
    this.props.dispatch({
      type: 'login/register',
      payload: newData,
      callback: response => {
        let { code, data, msg } = response;
        if (code === 200) {
          // notification.success({
          //   message: getIntl().formatMessage({
          //     id: 'register.success',
          //   }),
          // });
          Modal.success({
            content: getIntl().formatMessage({
              id: 'register.success.modal',
            }),
            onOk: () => {
              // 取消 登录按钮的loading状态
              this.setState(({ loadings }) => {
                const newLoadings = [...loadings];
                newLoadings[0] = false;
                return {
                  loadings: newLoadings,
                };
              });
              // 跳转链接
              history.replace('/login');
            },
          });
        } else {
          // 取消 登录按钮的loading状态
          this.setState(({ loadings }) => {
            const newLoadings = [...loadings];
            newLoadings[0] = false;
            return {
              loadings: newLoadings,
            };
          });

          notification.error({
            message: msg,
          });
        }
      },
    });
  };
  onFinishFailed = errorInfo => {
    console.log('Failed:', errorInfo);
  };

  showModal = () => {
    this.setState({
      isModalOpen: true,
    });
  };
  hideModal = () => {
    this.setState({
      isModalOpen: false,
    });
  };

  /**
   * 获取邮箱验证码
   */
  getCaptcha = () => {
    let { captchaBtnClick, captchaCount } = this.state;

    this.setState({
      captchaBtnClick: true,
    });

    let { email } = this.state;
    if (!email) {
      notification.error({
        message: getIntl().formatMessage({
          id: 'login.user.email.input',
        }),
      });
      this.setState({
        captchaBtnClick: false,
      });
      return;
    }
    // let emailPatter = /^(\w+([-.][A-Za-z0-9]+)*){3,18}@\w+([-.][A-Za-z0-9]+)*\.\w+([-.][A-Za-z0-9]+)*$/i;
    // if (!emailPatter.test(email)) {
    //   notification.error({
    //     message: getIntl().formatMessage({
    //       id: 'login.user.email.pattern',
    //     }),
    //   });
    //   this.setState({
    //     captchaBtnClick: false,
    //   });
    //   return;
    // }

    if (captchaCount === 60) {
      this.setState({
        captchaBtnClick: true,
      });
      this.timeReduce = setInterval(() => {
        let { captchaCount } = this.state;
        if (captchaCount === 1) {
          this.setState({
            captchaCount: 60,
            captchaBtnClick: false,
          });
          clearInterval(this.timeReduce);
        } else {
          this.setState({
            captchaCount: captchaCount - 1,
          });
        }
      }, 1000);
    }

    this.props.dispatch({
      type: 'login/captcha',
      payload: email,
      callback: response => {
        let { code, data, msg } = response;
        if (200 === code) {
          notification.success({
            message: msg,
          });
        } else {
          notification.error({
            message: msg,
          });
        }
        // clearInterval(this.timeReduce);
        // this.setState({
        //   captchaCount: 60,
        //   captchaBtnClick: false,
        // });
      },
    });
  };

  /**
   * 跳转宣传页
   */
  goHomePage = () => {
    history.push('/home');
  };

  render() {
    const {
      isModalOpen,
      loadings,
      captchaBtnClick,
      captchaCount,
      defaultSelectedKeys,
      langText,
    } = this.state;
    return (
      <div className={styles.registerContent}>
        <div className={styles.goHome} onClick={this.goHomePage}>
          <img src={LoginReturn} />
          <span>
            <FormattedMessage id="home.page.menu.home" defaultMessage="首页" />
          </span>
        </div>
        <div className={styles.languageIcon}>
          <Dropdown
            menu={{
              items,
              selectable: true,
              defaultSelectedKeys: [defaultSelectedKeys],
              onClick: this.handleMenuClick,
            }}
            overlayClassName="langDownPersonal"
          >
            <div className={styles.langContent}>
              <img src={LangIcon} />
              <span>{langText}</span>
            </div>
          </Dropdown>
        </div>
        <div className={styles.registerDetailContent}>
          <div className={styles.registerTitle}>
            <img
              className={styles.logoImg}
              src={LogoImg}
              onClick={this.goHomePage}
            />
            <div className={styles.logoLine}></div>
            <div className={styles.platformName}>
              <FormattedMessage id="connectNow.title" />
              {/*Goclouds Data Solution*/}
              {/*CRM*/}
              {/*<FormattedMessage id="program.title" />*/}
            </div>
          </div>
          <div className={styles.registerBottomContent}>
            <Form
              name="basic"
              labelCol={{
                span: 6,
              }}
              wrapperCol={{
                span: 16,
              }}
              style={{
                maxWidth: 600,
              }}
              initialValues={{
                remember: false,
              }}
              onFinish={this.onFinish}
              onFinishFailed={this.onFinishFailed}
              autoComplete="off"
            >
              {/* Form.Item内部只能有一个元素  否则对应的rules不生效 */}
              <Form.Item
                label={getIntl().formatMessage({
                  id: 'register.email',
                })}
                name="email"
                rules={[
                  {
                    required: true,
                    message: <FormattedMessage id="login.user.email.input" />,
                  },
                  {
                    min: 1,
                    max: 40,
                    type: 'email',
                    message: <FormattedMessage id="login.user.email.pattern" />,
                  },
                ]}
                className={styles.noMarginBtm}
              >
                <Input
                  name="email"
                  onChange={e =>
                    this.setState({
                      email: e.target.value,
                    })
                  }
                  placeholder={getIntl().formatMessage({
                    id: 'login.user.email.input',
                  })}
                />
              </Form.Item>
              <Form.Item className={styles.emailTips}>
                <p>
                  <FormattedMessage id="register.email.notice" />
                </p>
              </Form.Item>
              <Row
                className={styles.lengthItem}
                gutter={20}
                style={{ marginLeft: '-20px', marginBottom: 24 }}
              >
                <Col
                  span={18}
                  style={{
                    width: '100%',
                    // paddingLeft: '39px',
                    paddingRight: '0px',
                  }}
                  className={styles.captchaClass}
                >
                  <Form.Item
                    label={getIntl().formatMessage({
                      id: 'register.captcha',
                    })}
                    name="captcha"
                    rules={[
                      {
                        required: true,
                        message: (
                          <FormattedMessage id="login.user.captcha.input" />
                        ),
                      },
                    ]}
                    className={styles.noMarginBtm}
                  >
                    <Input
                      name="captcha"
                      className={styles.codeInput}
                      placeholder={getIntl().formatMessage({
                        id: 'login.user.captcha.input',
                      })}
                      // style={{marginLeft:"9px"}}
                    />
                  </Form.Item>
                </Col>
                <Col span={5} style={{ maxWidth: '100%' }}>
                  <Button
                    // loading={loadings[0]}
                    disabled={captchaBtnClick}
                    className={styles.codeBtn}
                    onClick={this.getCaptcha}
                  >
                    {captchaBtnClick
                      ? getIntl().formatMessage(
                          {
                            id: 'register.email.captcha.second',
                          },
                          {
                            captchaCount,
                          },
                        )
                      : getIntl().formatMessage({
                          id: 'register.email.captcha',
                        })}
                  </Button>
                </Col>
              </Row>

              <Form.Item
                label={getIntl().formatMessage({
                  id: 'register.user.name',
                })}
                name="userName"
                rules={[
                  {
                    required: true,
                    message: <FormattedMessage id="register.user.name.input" />,
                  },
                ]}
              >
                <Input
                  placeholder={getIntl().formatMessage({
                    id: 'register.user.name.input',
                  })}
                />
              </Form.Item>
              <Form.Item
                label={getIntl().formatMessage({
                  id: 'register.last.name',
                })}
                name="lastName"
                rules={[
                  {
                    required: true,
                    message: <FormattedMessage id="register.last.name.input" />,
                  },
                ]}
              >
                <Input
                  placeholder={getIntl().formatMessage({
                    id: 'register.last.name.input',
                  })}
                />
              </Form.Item>
              <Form.Item
                label={getIntl().formatMessage({
                  id: 'register.phone',
                })}
                name="phonenumber"
                rules={[
                  {
                    required: true,
                    message: (
                      <FormattedMessage id="register.user.phone.input" />
                    ),
                  },
                  {
                    max: 30,
                    pattern: '^(\\+\\d{1,3})?[1-9]\\d{9,14}$',
                    message: (
                      <FormattedMessage id="register.user.phone.length.input" />
                    ),
                  },
                ]}
              >
                <Input
                  placeholder={getIntl().formatMessage({
                    id: 'register.user.phone.input',
                  })}
                />
              </Form.Item>
              <Form.Item
                label={getIntl().formatMessage({
                  id: 'register.user.post',
                })}
                name="postName"
                rules={[
                  {
                    // required: true,
                    message: <FormattedMessage id="register.user.post.input" />,
                  },
                  {
                    max: 80,
                    message: (
                      <FormattedMessage id="register.user.post.length.input" />
                    ),
                  },
                ]}
              >
                <Input
                  placeholder={getIntl().formatMessage({
                    id: 'register.user.post.input',
                  })}
                />
              </Form.Item>
              <Form.Item
                label={getIntl().formatMessage({
                  id: 'register.company.name',
                })}
                name="companyName"
                rules={[
                  {
                    required: true,
                    message: (
                      <FormattedMessage id="register.company.name.input" />
                    ),
                  },
                  {
                    max: 80,
                    message: (
                      <FormattedMessage id="register.company.name.length.input" />
                    ),
                  },
                ]}
              >
                <Input
                  placeholder={getIntl().formatMessage({
                    id: 'register.company.name.input',
                  })}
                />
              </Form.Item>
              {/* <Form.Item
                label={getIntl().formatMessage({
                  id: 'register.company.code',
                })}
                name="companyCode"
                rules={[
                  {
                    max: 80,
                    message: (
                      <FormattedMessage id="register.company.code.length.input" />
                    ),
                  },
                ]}
              >
                <Input
                  placeholder={getIntl().formatMessage({
                    id: 'register.company.code.input',
                  })}
                />
              </Form.Item> */}
              <Form.Item
                label={getIntl().formatMessage({
                  id: 'register.company.address',
                })}
                name="companyAddress"
                rules={[
                  {
                    max: 2000,
                    message: (
                      <FormattedMessage id="register.company.address.length.input" />
                    ),
                  },
                ]}
              >
                <TextArea
                  placeholder={getIntl().formatMessage({
                    id: 'register.company.address.input',
                  })}
                  autoSize={{
                    minRows: 3,
                    maxRows: 3,
                  }}
                />
              </Form.Item>
              <Row
                className={styles.lengthItem}
                gutter={20}
                style={{ marginLeft: '50px' }}
              >
                <Col span={24}>
                  <Form.Item
                    name="remember"
                    valuePropName="checked"
                    wrapperCol={{
                      span: 24,
                    }}
                    style={{ marginLeft: '15%' }}
                    rules={[
                      {
                        required: true,
                        validator: (_, value) => {
                          if (!value) {
                            return Promise.reject(
                              getIntl().formatMessage({
                                id: 'register.user.service.term',
                              }),
                            );
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                    className={styles.termsServiceTop}
                  >
                    <Checkbox>
                      <FormattedMessage id="register.remember" />
                      <span
                        onClick={this.showModal}
                        className={styles.termsService}
                      >
                        <FormattedMessage id="register.service.tips" />
                      </span>
                    </Checkbox>
                  </Form.Item>
                  {/* <Form.Item className={styles.noMarginBtm}></Form.Item> */}
                </Col>
              </Row>
              <Form.Item
                wrapperCol={{
                  offset: 5,
                  span: 18,
                }}
              >
                <Button
                  className={styles.registerBtn}
                  type="primary"
                  htmlType="submit"
                  loading={loadings[0]}
                >
                  <FormattedMessage id="register.button" />
                </Button>
              </Form.Item>
            </Form>
          </div>
        </div>

        <Modal
          title="服务条款"
          open={isModalOpen}
          onCancel={this.hideModal}
          onOk={this.hideModal}
          className="TermsServiceModal"
        >
          <TermsServiceContent />
        </Modal>
      </div>
    );
  }
}

const mapStateToProps = ({}) => {
  return {};
};
export default connect(mapStateToProps)(RegisterContent);
