import React, { Component } from 'react';
import { connect, FormattedMessage, history } from 'umi';
import { Input, Button, Checkbox, Select } from 'antd';
import styles from './index.less';
import { getIntl } from '../../.umi/plugin-locale/localeExports';
import { SearchOutlined } from '@ant-design/icons';
import Radar from './radaComponents';
import Column from './columnComponents';
import { notification } from '../../utils/utils';

const ReturnIcon = () => (
  <svg
    style={{ float: 'left', marginRight: '3px', marginTop: '1px' }}
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.9433 6.16426H2.71921L6.71847 2.60947C6.91205 2.43741 6.92949 2.14102 6.75742 1.94744C6.58536 1.75391 6.28902 1.7364 6.09542 1.90849L1.19417 6.26504C1.08541 6.35092 1.0155 6.48385 1.0155 6.63319C1.0155 6.6334 1.01552 6.63361 1.01552 6.63381C1.01552 6.63401 1.0155 6.63421 1.0155 6.63441C1.0155 6.64181 1.01574 6.6492 1.01609 6.65657C1.01611 6.65682 1.01611 6.65708 1.01611 6.65733C1.02225 6.78282 1.0785 6.90101 1.1729 6.98487L6.0954 11.3603C6.18473 11.4398 6.29595 11.4788 6.40674 11.4788C6.53603 11.4788 6.66478 11.4256 6.7574 11.3214C6.92946 11.1278 6.91203 10.8314 6.71845 10.6594L2.71645 7.10209H10.9433C12.6499 7.10209 14.0382 8.49046 14.0382 10.197C14.0382 11.9036 12.6499 13.2919 10.9433 13.2919H5.53634C5.27737 13.2919 5.06741 13.5019 5.06741 13.7609C5.06741 14.0198 5.27737 14.2298 5.53634 14.2298H10.9433C13.167 14.2298 14.9761 12.4207 14.9761 10.197C14.9761 7.97334 13.167 6.16426 10.9433 6.16426Z"
      fill="#3463FC"
    />
  </svg>
);

class TestAnalysisContent extends Component {
  constructor(props) {
    super(props);
    this.state = {
      radarDataList: [],
      histogramDataList: [],
      planComparing: '',
      planCompareDataList: [],
      eventNameItem: {
        activityId: '',
        needShowMarketingType: 2,
      },
    };
  }
  componentDidMount() {
    let { eventNameItem } = this.state;
    if (this.props.history.location.state) {
      let newActivityId = this.props.history.location.state.activityId;
      let newEventId = this.props.history.location.state.eventId;
      let newBatchNum = this.props.history.location.state.batchNum;
      if (newActivityId) {
        if (newEventId) {
          if (newBatchNum) {
            this.setState(
              {
                activityId: newActivityId,
                eventId: newEventId,
                batchId: newBatchNum,
              },
              () => {
                let params = {
                  activityId: newActivityId,
                  eventId: newEventId,
                  batchNum: newBatchNum,
                };
                let eventNameItem = {
                  activityId: newActivityId,
                  needShowMarketingType: 2,
                };
                this.queryActivityList();
                this.queryEventNameList(eventNameItem);
                this.queryEventBatchList(newEventId);
                this.queryMarketingAnalysisResult(params);
              },
            );
          } else {
            this.setState({
              activityId: newActivityId,
              eventId: newEventId,
            });
            let params = {
              activityId: newActivityId,
              eventId: newEventId,
              batchNum: '',
            };
            let eventNameItem = {
              activityId: newActivityId,
              needShowMarketingType: 2,
            };
            this.queryActivityList();
            this.queryEventNameList(eventNameItem);
            this.queryEventBatchList(newEventId);
            this.queryMarketingAnalysisResult(params);
          }
        } else {
          this.setState({
            activityId: newActivityId,
          });
          let params = {
            activityId: newActivityId,
            eventId: '',
            batchNum: '',
          };
          let eventNameItem = {
            activityId: newActivityId,
            needShowMarketingType: 2,
          };
          this.queryActivityList();
          this.queryEventNameList(eventNameItem);
          this.queryEventBatchList('');
          this.queryMarketingAnalysisResult(params);
        }
      }
    } else {
      // let activityId = 'ACT123';
      // let eventId = 'EVT456';
      let params = {
        activityId: '',
        eventId: '',
        batchNum: '',
      };
      this.queryActivityList();
      this.queryEventNameList(eventNameItem);
      this.queryEventBatchList('');
      this.queryMarketingAnalysisResult(params);
    }
  }

  // 查询活动名称
  queryActivityList = () => {
    this.props.dispatch({
      type: 'statisticalResults/queryActivityList',
      payload: { excludeEndStatus: 0 },
      callback: response => {
        if (response.code == 200) {
          this.setState({
            activityList: response.data,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询营销事件
  queryEventNameList = params => {
    this.props.dispatch({
      type: 'statisticalResults/queryEventNameListAbTest',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          this.setState({
            eventNameList: response.data,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询营销事件批次
  queryEventBatchList = params => {
    this.props.dispatch({
      type: 'statisticalResults/queryEventBatchList',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          this.setState({
            eventBatchList: response.data,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询雷达图，数据图和分析结果
  queryMarketingAnalysisResult = params => {
    this.props.dispatch({
      type: 'statisticalResults/queryMarketingAnalysisResult',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          if (response.data) {
            let planComparing = response.data.planComparing;
            let planCompareDataList = response.data.planCompareDataList;
            let planCompareDataListContent = '';
            let planComparingContent = '';
            if (planComparing == 1) {
              planComparingContent = getIntl().formatMessage({
                id: 'test.analysis.result.comparison.dimension.1',
                defaultValue: '客户群体',
              });
            } else if (planComparing == 2) {
              planComparingContent = getIntl().formatMessage({
                id: 'test.analysis.result.comparison.dimension.2',
                defaultValue: '营销内容',
              });
            } else if (planComparing == 3) {
              planComparingContent = getIntl().formatMessage({
                id: 'test.analysis.result.comparison.dimension.3',
                defaultValue: '营销时间',
              });
            } else {
              planComparingContent = '--';
            }
            if (planCompareDataList.length > 0) {
              for (let i = 0; i < planCompareDataList.length; i++) {
                let item = planCompareDataList[i];
                if (item.statusCode == 1) {
                  planCompareDataListContent +=
                    getIntl().formatMessage({
                      id: 'marketing.results.table.delivery.rate.1',
                      defaultValue: '送达率：',
                    }) +
                    'PlanA' +
                    item.compareResult +
                    'PlanB' +
                    getIntl().formatMessage({
                      id: 'test.analysis.semicolon',
                      defaultValue: '；',
                    });
                } else if (item.statusCode == 2) {
                  planCompareDataListContent +=
                    getIntl().formatMessage({
                      id: 'marketing.results.table.read.rate.1',
                      defaultValue: '已读率：',
                    }) +
                    'PlanA' +
                    item.compareResult +
                    'PlanB' +
                    getIntl().formatMessage({
                      id: 'test.analysis.semicolon',
                      defaultValue: '；',
                    });
                } else if (item.statusCode == 3) {
                  planCompareDataListContent +=
                    getIntl().formatMessage({
                      id: 'marketing.results.table.click.rate.1',
                      defaultValue: '点击率：',
                    }) +
                    'PlanA' +
                    item.compareResult +
                    'PlanB' +
                    getIntl().formatMessage({
                      id: 'test.analysis.semicolon',
                      defaultValue: '；',
                    });
                } else if (item.statusCode == 4) {
                  planCompareDataListContent +=
                    getIntl().formatMessage({
                      id: 'marketing.results.table.complaint.rate.1',
                      defaultValue: '投诉率：',
                    }) +
                    'PlanA' +
                    item.compareResult +
                    'PlanB' +
                    getIntl().formatMessage({
                      id: 'test.analysis.semicolon',
                      defaultValue: '；',
                    });
                } else if (item.statusCode == 5) {
                  planCompareDataListContent +=
                    getIntl().formatMessage({
                      id: 'marketing.results.table.unsubscribe.rate.1',
                      defaultValue: '退订率：',
                    }) +
                    'PlanA' +
                    item.compareResult +
                    'PlanB' +
                    getIntl().formatMessage({
                      id: 'test.analysis.semicolon',
                      defaultValue: '；',
                    });
                } else if (item.statusCode == 6) {
                  planCompareDataListContent +=
                    getIntl().formatMessage({
                      id: 'marketing.results.table.bounce.rate.1',
                      defaultValue: '退信率：',
                    }) +
                    'PlanA' +
                    item.compareResult +
                    'PlanB' +
                    getIntl().formatMessage({
                      id: 'test.analysis.semicolon',
                      defaultValue: '；',
                    });
                } else if (item.statusCode == 7) {
                  planCompareDataListContent +=
                    getIntl().formatMessage({
                      id: 'marketing.results.table.delivery.delay.rate.1',
                      defaultValue: '延迟送达率：',
                    }) +
                    'PlanA' +
                    item.compareResult +
                    'PlanB' +
                    getIntl().formatMessage({
                      id: 'test.analysis.period',
                      defaultValue: '。',
                    });
                } else if (item.statusCode == 8) {
                  planCompareDataListContent +=
                    getIntl().formatMessage({
                      id: 'marketing.results.table.reject.rate.1',
                      defaultValue: '拒绝率：',
                    }) +
                    'PlanA' +
                    item.compareResult +
                    'PlanB' +
                    getIntl().formatMessage({
                      id: 'test.analysis.period',
                      defaultValue: '。',
                    });
                } else if (item.statusCode == 9) {
                  planCompareDataListContent +=
                    getIntl().formatMessage({
                      id: 'marketing.results.table.rendering.failure.rate.1',
                      defaultValue: '呈现失败率：',
                    }) +
                    'PlanA' +
                    item.compareResult +
                    'PlanB' +
                    getIntl().formatMessage({
                      id: 'test.analysis.period',
                      defaultValue: '。',
                    });
                }
              }
            } else {
              planCompareDataListContent = '--';
            }

            let newData = response.data.radarDataList;
            if (newData) {
              for (let i = 0; i < newData.length; i++) {
                let value = newData[i].value;
                const percentageArray = value.map(num => `${num * 100}`);
                newData[i].value = percentageArray;
              }
            }
            this.setState({
              // （雷达图用）
              radarDataList: response.data.radarDataList,
              //（柱状图用）
              histogramDataList: response.data.histogramDataList,
              // 对比维度 （1-客户群体 2-营销内容 3-营销时间）
              planComparingContent: planComparingContent,
              // 整体结果
              planCompareDataListContent: planCompareDataListContent,
            });
          } else {
            this.setState({
              // （雷达图用）
              radarDataList: [],
              //（柱状图用）
              histogramDataList: [],
              // 对比维度 （1-客户群体 2-营销内容 3-营销时间）
              planComparingContent: '--',
              // 整体结果
              planCompareDataListContent: '--',
            });
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 切换活动名称
  handleChangeActivityName = value => {
    if (value !== undefined) {
      this.setState({
        activityId: value,
        eventId: '',
        batchId: '',
      });
      let eventNameItem = {
        activityId: value,
        needShowMarketingType: 2,
      };
      this.queryEventNameList(eventNameItem);
      this.queryEventBatchList('');
    } else {
      let { eventNameItem } = this.state;
      this.setState({
        activityId: '',
        eventId: '',
        batchId: '',
      });
      this.queryEventNameList(eventNameItem);
      this.queryEventBatchList('');
    }
  };
  // 切换营销事件
  handleChangeEventName = value => {
    if (value !== undefined) {
      this.setState({
        eventId: value,
        batchId: '',
      });
      this.queryEventBatchList(value);
    } else {
      this.setState({
        eventId: '',
        batchId: '',
      });
      this.queryEventBatchList('');
    }
  };
  // 切换营销事件批次
  handleChangeEventBatch = value => {
    if (value !== undefined) {
      this.setState({
        batchId: value,
      });
    } else {
      this.setState({
        batchId: '',
      });
    }
  };

  // 点击按钮筛选
  handleSearch = () => {
    let { activityId, eventId, batchId } = this.state;
    let params = {
      activityId: activityId,
      eventId: eventId,
      batchNum: batchId,
    };
    this.queryMarketingAnalysisResult(params);
  };
  // 返回列表
  handleReturnPrePage = () => {
    history.push('/emailMarketingEvent');
  };

  render() {
    let {
      activityList,
      eventNameList,
      eventBatchList,
      batchId,
      eventId,
      radarDataList,
      histogramDataList,
      planComparingContent,
      planCompareDataListContent,
      activityId,
    } = this.state;
    return (
      <div className={styles.testAnalysisContent}>
        <Button onClick={this.handleReturnPrePage} icon={<ReturnIcon />}>
          <FormattedMessage
            id="work.order.return.list"
            defaultMessage="返回列表"
          />
        </Button>
        <div className={styles.selectTopContent}>
          <div className={styles.selectContent}>
            <label>
              <FormattedMessage
                id="marketing.activities.activity.name"
                defaultMessage="活动名称："
              />
            </label>
            <Select
              value={activityId}
              showSearch
              allowClear={true}
              options={activityList}
              fieldNames={{
                label: 'activityName',
                value: 'activityId',
                key: 'activityId',
              }}
              filterOption={(inputValue, option) =>
                option.activityName
                  .toLowerCase()
                  .indexOf(inputValue.toLowerCase()) >= 0
              }
              onChange={this.handleChangeActivityName}
            />
            <label>
              <FormattedMessage
                id="marketing.results.marketing.event.name"
                defaultMessage="营销事件名称："
              />
            </label>
            <Select
              // value={activityId}
              allowClear={true}
              value={eventId}
              options={eventNameList}
              showSearch
              fieldNames={{
                label: 'eventName',
                value: 'eventId',
                key: 'eventId',
              }}
              filterOption={(inputValue, option) =>
                option.eventName
                  .toLowerCase()
                  .indexOf(inputValue.toLowerCase()) >= 0
              }
              onChange={this.handleChangeEventName}
            />
            {/*<label>*/}
            {/*  <FormattedMessage*/}
            {/*    id="marketing.results.marketing.event.batches"*/}
            {/*    defaultMessage="营销事件批次："*/}
            {/*  />*/}
            {/*</label>*/}
            {/*<Select*/}
            {/*  allowClear={true}*/}
            {/*  value={batchId}*/}
            {/*  options={eventBatchList}*/}
            {/*  showSearch*/}
            {/*  fieldNames={{*/}
            {/*    label: 'batchNum',*/}
            {/*    value: 'batchId',*/}
            {/*    key: 'batchId',*/}
            {/*  }}*/}
            {/*  filterOption={(inputValue, option) =>*/}
            {/*    option.batchNum*/}
            {/*      .toLowerCase()*/}
            {/*      .indexOf(inputValue.toLowerCase()) >= 0*/}
            {/*  }*/}
            {/*  placeholder={getIntl().formatMessage({*/}
            {/*    id: 'marketing.results.marketing.event.batches.placeholder',*/}
            {/*    defaultValue: '请输入营销事件批次',*/}
            {/*  })}*/}
            {/*  onChange={this.handleChangeEventBatch}*/}
            {/*/>*/}
          </div>
          <Button
            onClick={this.handleSearch}
            type="primary"
            icon={<SearchOutlined />}
          >
            <FormattedMessage
              id="marketing.activities.search.btn"
              defaultMessage="筛选"
            />
          </Button>
        </div>
        <div className={styles.analysisContent}>
          <p className="blueBorder">
            <FormattedMessage
              id="test.analysis.result.title"
              defaultMessage="分析结果"
            />
          </p>
          <p className={styles.analysisItem}>
            <span className={styles.analysisLabel}>
              <FormattedMessage
                id="test.analysis.result.comparison.dimension"
                defaultMessage="对比维度："
              />
            </span>
            <span>{planComparingContent}</span>
          </p>
          <p className={styles.analysisItem}>
            <span className={styles.analysisLabel}>
              <FormattedMessage
                id="test.analysis.result.overall.result"
                defaultMessage="整体结果："
              />
            </span>
            <span>{planCompareDataListContent}</span>
          </p>
        </div>
        <div className={styles.echartsContent}>
          <div className={styles.echartsLeftContent}>
            <p className="blueBorder">
              <FormattedMessage
                id="test.analysis.result.title"
                defaultMessage="A/B测试雷达图"
              />
            </p>
            <Radar radarDataList={radarDataList} />
          </div>
          <div className={styles.echartsRightContent}>
            <p className="blueBorder">
              <FormattedMessage
                id="test.analysis.result.title"
                defaultMessage="A/B测试数据图"
              />
            </p>
            <Column histogramDataList={histogramDataList} />
          </div>
        </div>
      </div>
    );
  }
}

const mapStateToProps = ({ statisticalResults }) => {
  return {
    ...statisticalResults,
  };
};
export default connect(mapStateToProps)(TestAnalysisContent);
