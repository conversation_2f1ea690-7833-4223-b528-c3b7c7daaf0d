import React, { Component } from 'react';

import { connect, FormattedMessage, getIntl } from 'umi';
import {
  Button,
  Col,
  DatePicker,
  Form,
  Input,
  message,
  Row,
  Select,
  Table,
  Tooltip,
  Popconfirm,
  Modal,
  Upload,
  Tag,
  Spin,
  Popover,
  Switch,
  Radio,
  Checkbox,
} from 'antd';
const { RangePicker } = DatePicker;

import styles from './index.less';
import { PlusOutlined, SearchOutlined } from '@ant-design/icons';
import RightArrowImg from '../../../../../assets/right-arrow.png';
import { notification } from '../../../../../utils/utils';
// import WangEditor from './WangEditor/WangEditor';
import moment from 'moment';
// import XLSX from 'xlsx';  //不能用这个方式引入，应该用如下方式，参考：https://blog.csdn.net/The_Lucky_one/article/details/127202499
import * as XLSX from 'xlsx';
import FileIcon from '../../../../../assets/file-text.svg';
import UploadIcon from '../../../../../assets/upload.svg';
import Facebook from '../../../../../assets/customerInformationf.svg';
import Twitter from '../../../../../assets/customerInformationt.svg';
import Line from '../../../../../assets/customerInformationl.svg';
import WhatsApp from '../../../../../assets/customerInformationw.svg';
import Instagram from '../../../../../assets/custom-Instagram.svg';
import Wechat from '../../../../../assets/custom-weChat.svg';
import AwsChannelIcon from '@/assets/aws-email.svg';

const { Search } = Input;
const { Option } = Select;
const { TextArea } = Input;
const workRecordColumns = [
  {
    title: (
      <FormattedMessage
        id="work.order.management.table.work.order.id"
        defaultMessage="工单ID"
      />
    ),
    width: 150,
    dataIndex: 'workRecordId',
    key: 'workRecordId',
    ellipsis: true,
  },
  {
    title: (
      <FormattedMessage
        id="work.order.management.table.work.order.code"
        defaultMessage="工单编号"
      />
    ),
    width: 150,
    dataIndex: 'wordRecordCode',
    key: 'wordRecordCode',
    ellipsis: true,
  },
  {
    title: (
      <FormattedMessage
        id="work.order.management.table.work.order.theme"
        defaultMessage="工单主题"
      />
    ),
    dataIndex: 'workRecordTheme',
    key: 'workRecordTheme',
    width: 200,
    ellipsis: true,
  },
  {
    title: (
      <FormattedMessage
        id="work.order.management.table.seat.name"
        defaultMessage="座席名称"
      />
    ),
    dataIndex: 'agentName',
    key: 'agentName',
    width: 150,
    ellipsis: true,
  },
  {
    title: (
      <FormattedMessage
        id="work.order.management.table.work.order.type"
        defaultMessage="工单类型"
      />
    ),
    dataIndex: 'workRecordTypeName',
    key: 'workRecordTypeName',
    width: 150,
    ellipsis: true,
  },

  {
    title: (
      <FormattedMessage
        id="work.order.management.table.priority"
        defaultMessage="优先级"
      />
    ),
    dataIndex: 'priorityLevelName',
    key: 'priorityLevelName',
    width: 100,
    render: (text, record) => {
      if (record.priorityLevelId == '1001') {
        return (
          <div className={styles.priority1}>{record.priorityLevelName}</div>
        );
      } else if (record.priorityLevelId == '1002') {
        return (
          <div className={styles.priority2}>{record.priorityLevelName}</div>
        );
      } else if (record.priorityLevelId == '1003') {
        return (
          <div className={styles.priority3}>{record.priorityLevelName}</div>
        );
      } else if (record.priorityLevelId == '1004') {
        return (
          <div className={styles.priority4}>{record.priorityLevelName}</div>
        );
      } else if (record.priorityLevelId == '1005') {
        return (
          <div className={styles.priority5}>{record.priorityLevelName}</div>
        );
      }
    },
  },
  {
    title: (
      <FormattedMessage
        id="work.order.management.table.status"
        defaultMessage="状态"
      />
    ),
    dataIndex: 'status',
    key: 'status',
    width: 150,
    render: (text, record) => {
      let status = record.status;
      if (status == 0) {
        return (
          <div className={styles.state5}>
            <div className={styles.circle}></div>
            <FormattedMessage
              id="work.order.management.table.status.text1"
              defaultMessage="待分配"
            />
          </div>
        );
      } else if (status == 1) {
        return (
          <div className={styles.state3}>
            <div className={styles.circle}></div>
            <FormattedMessage
              id="work.order.management.table.status.text2"
              defaultMessage="待座席处理"
            />
          </div>
        );
      } else if (status == 2) {
        return (
          <div className={styles.state3}>
            <div className={styles.circle}></div>
            <FormattedMessage
              id="work.order.management.table.status.text3"
              defaultMessage="待客户回复"
            />
          </div>
        );
      } else if (status == 3) {
        return (
          <div className={styles.state1}>
            <div className={styles.circle}></div>
            <FormattedMessage
              id="work.order.management.table.status.text4"
              defaultMessage="已解决"
            />
          </div>
        );
      } else if (status == 4) {
        return (
          <div className={styles.state2}>
            <div className={styles.circle}></div>
            <FormattedMessage
              id="work.order.management.table.status.text5"
              defaultMessage="已终止"
            />
          </div>
        );
      } else if (status == 5) {
        return (
          <div className={styles.state4}>
            <div className={styles.circle}></div>
            <FormattedMessage
              id="work.order.management.table.status.text6"
              defaultMessage="已转单"
            />
          </div>
        );
      }
    },
  },
  {
    title: (
      <FormattedMessage
        id="work.order.management.table.creation.time"
        defaultMessage="创建时间"
      />
    ),
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150,
    ellipsis: true,
  },
];
const formItemLayout = {
  labelCol: {
    xs: {
      span: 24,
    },
    sm: {
      span: 4,
    },
  },
  wrapperCol: {
    xs: {
      span: 24,
    },
    sm: {
      span: 16,
    },
  },
};

const tailFormItemLayout = {
  labelCol: {
    xs: {
      span: 24,
    },
    sm: {
      span: 10,
    },
  },
  wrapperCol: {
    xs: {
      span: 0,
      offset: 0,
    },
    sm: {
      span: 14,
      offset: 0,
    },
  },
};
const getSvgComponent = key => {
  switch (key) {
    case 'Facebook':
      return Facebook;
    case 'Twitter':
      return Twitter;
    case 'Line':
      return Line;
    case 'WhatsApp':
      return WhatsApp;
    case 'Instagram':
      return Instagram;
    case 'Wechat':
      return Wechat;
    case 'Amazon':
      return AwsChannelIcon;
    default:
      return null;
  }
};
const ExportIcon = () => (
  <svg
    style={{ float: 'left', marginRight: '3px', marginTop: '1px' }}
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.6871 10.0422C12.4855 10.0454 12.323 10.211 12.3262 10.4125V11.3422C12.3262 11.7485 12.0043 12.0797 11.6074 12.0797H4.42461C4.02773 12.0797 3.70586 11.7485 3.70586 11.3422V4.70942C3.70586 4.30317 4.02773 3.97192 4.42461 3.97192H6.92461L7.85273 5.62349C7.92773 5.75474 8.06836 5.82192 8.20742 5.8063H11.6059C12.0027 5.8063 12.3246 6.13755 12.3246 6.5438V7.46567C12.3246 7.67036 12.4871 7.83599 12.6871 7.83599C12.8871 7.83599 13.048 7.67036 13.048 7.46567C13.048 7.4563 13.048 7.44692 13.0465 7.43755V6.5438C13.0465 5.72974 12.4027 5.0688 11.609 5.0688H8.37305L7.44648 3.42349C7.36367 3.27505 7.24492 3.23599 7.14023 3.23911L7.13867 3.23755H4.41992C3.62617 3.23755 2.98242 3.89849 2.98242 4.71255V11.3266C2.98242 12.1407 3.62617 12.8016 4.41992 12.8016H11.609C12.4027 12.8016 13.0465 12.1407 13.0465 11.3266V10.4407C13.048 10.4313 13.048 10.4219 13.048 10.4125C13.0496 10.3157 13.0121 10.2219 12.9434 10.1532C12.8762 10.0829 12.784 10.0438 12.6871 10.0422Z"
      fill="#3463FC"
    />
    <path
      d="M8.85371 7.3626C8.91152 7.3001 8.99121 7.26416 9.07715 7.26416C9.16152 7.26416 9.24277 7.3001 9.30059 7.3626L10.6318 8.77979L10.6396 8.7876H10.6412L10.649 8.79541C10.7693 8.92822 10.7693 9.13135 10.649 9.26416L9.31309 10.6892C9.25684 10.7517 9.17715 10.786 9.09277 10.7876C9.0084 10.7876 8.92871 10.7532 8.87246 10.6907L8.86621 10.6829C8.7459 10.5501 8.7459 10.347 8.86621 10.2142L9.6709 9.35479H6.01621C5.8459 9.35479 5.70684 9.20635 5.70684 9.0251C5.70684 8.84385 5.8459 8.69541 6.01621 8.69541H9.65684L8.85371 7.8376C8.73027 7.70479 8.73027 7.49697 8.85371 7.3626Z"
      fill="#3463FC"
    />
  </svg>
);
const ImportIcon = () => (
  <svg
    style={{ float: 'left', marginRight: '3px', marginTop: '1px' }}
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.6871 10.0422C12.4855 10.0454 12.323 10.211 12.3262 10.4125V11.3422C12.3262 11.7485 12.0043 12.0797 11.6074 12.0797H4.42461C4.02773 12.0797 3.70586 11.7485 3.70586 11.3422V4.70942C3.70586 4.30317 4.02773 3.97192 4.42461 3.97192H6.92461L7.85273 5.62349C7.92773 5.75474 8.06836 5.82192 8.20742 5.8063H11.6059C12.0027 5.8063 12.3246 6.13755 12.3246 6.5438V7.46567C12.3246 7.67036 12.4871 7.83599 12.6871 7.83599C12.8871 7.83599 13.048 7.67036 13.048 7.46567C13.048 7.4563 13.048 7.44692 13.0465 7.43755V6.5438C13.0465 5.72974 12.4027 5.0688 11.609 5.0688H8.37305L7.44648 3.42349C7.36367 3.27505 7.24492 3.23599 7.14023 3.23911L7.13867 3.23755H4.41992C3.62617 3.23755 2.98242 3.89849 2.98242 4.71255V11.3266C2.98242 12.1407 3.62617 12.8016 4.41992 12.8016H11.609C12.4027 12.8016 13.0465 12.1407 13.0465 11.3266V10.4407C13.048 10.4313 13.048 10.4219 13.048 10.4125C13.0496 10.3157 13.0121 10.2219 12.9434 10.1532C12.8762 10.0829 12.784 10.0438 12.6871 10.0422Z"
      fill="#3463FC"
    />
    <path
      d="M7.59258 7.3626C7.53477 7.3001 7.45508 7.26416 7.36914 7.26416C7.28477 7.26416 7.20352 7.3001 7.1457 7.3626L5.81445 8.77979L5.80664 8.7876H5.80508L5.79727 8.79541C5.67695 8.92822 5.67695 9.13135 5.79727 9.26416L7.1332 10.6892C7.18945 10.7517 7.26914 10.786 7.35352 10.7876C7.43789 10.7876 7.51758 10.7532 7.57383 10.6907L7.58008 10.6829C7.70039 10.5501 7.70039 10.347 7.58008 10.2142L6.77539 9.35479H10.4301C10.6004 9.35479 10.7395 9.20635 10.7395 9.0251C10.7395 8.84385 10.6004 8.69541 10.4301 8.69541H6.78945L7.59258 7.8376C7.71602 7.70479 7.71602 7.49697 7.59258 7.3626Z"
      fill="#3463FC"
    />
  </svg>
);
const DownloadIcon = () => (
  <svg
    style={{ float: 'left', marginRight: '3px', marginTop: '1px' }}
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.8875 10.4451C14.7263 10.4489 14.5727 10.5146 14.4586 10.6287C14.3446 10.7427 14.2788 10.8963 14.275 11.0576C14.275 12.4051 13.375 13.5101 12.23 13.5101H3.77C2.625 13.5001 1.725 12.4051 1.725 11.0576C1.72119 10.8963 1.65544 10.7427 1.54138 10.6287C1.42733 10.5146 1.27375 10.4489 1.1125 10.4451C0.951253 10.4489 0.797666 10.5146 0.683615 10.6287C0.569565 10.7427 0.503811 10.8963 0.5 11.0576C0.5 13.1001 1.9725 14.7351 3.77 14.7351H12.23C14.0275 14.7351 15.5 13.1001 15.5 11.0576C15.4962 10.8963 15.4304 10.7427 15.3164 10.6287C15.2023 10.5146 15.0487 10.4489 14.8875 10.4451Z"
      fill="#3463FC"
    />
    <path
      d="M7.56969 10.73C7.69619 10.8349 7.85535 10.8923 8.01969 10.8923C8.18402 10.8923 8.34319 10.8349 8.46969 10.73L10.7997 8.39999C10.9021 8.28411 10.9564 8.13355 10.9517 7.97899C10.9469 7.82443 10.8834 7.6775 10.774 7.56816C10.6647 7.45882 10.5177 7.39528 10.3632 7.39051C10.2086 7.38575 10.0581 7.4401 9.94219 7.54249L8.67469 8.80999V1.85999C8.66472 1.70398 8.59573 1.55762 8.48172 1.45067C8.36772 1.34371 8.21726 1.28418 8.06094 1.28418C7.90461 1.28418 7.75415 1.34371 7.64015 1.45067C7.52614 1.55762 7.45715 1.70398 7.44719 1.85999V8.80999L6.17968 7.54249C6.06381 7.4401 5.91325 7.38575 5.75868 7.39051C5.60412 7.39528 5.4572 7.45882 5.34785 7.56816C5.23851 7.6775 5.17498 7.82443 5.17021 7.97899C5.16544 8.13355 5.21979 8.28411 5.32219 8.39999L7.56969 10.73Z"
      fill="#3463FC"
    />
  </svg>
);
const EditorGroupIcon = () => (
  <svg
    style={{ float: 'left', marginRight: '3px', marginTop: '1px' }}
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_112_8737)">
      <path
        d="M11.4947 13H3.49969C3.36716 13 3.24006 12.9473 3.14636 12.8536C3.05265 12.7599 3 12.6328 3 12.5003V4.5053C3 4.43968 3.01292 4.37471 3.03804 4.31408C3.06315 4.25346 3.09995 4.19837 3.14636 4.15197C3.19276 4.10557 3.24784 4.06876 3.30847 4.04365C3.36909 4.01854 3.43407 4.00562 3.49969 4.00562H7.99687V5.00499H3.99937V12.0006H10.995V8.00311H11.9944V12.5003C11.9944 12.5659 11.9814 12.6309 11.9563 12.6915C11.9312 12.7521 11.8944 12.8072 11.848 12.8536C11.8016 12.9 11.7465 12.9368 11.6859 12.962C11.6253 12.9871 11.5603 13 11.4947 13Z"
        fill="#3463FC"
      />
      <path
        d="M7.64337 8.35629C7.5497 8.26259 7.49707 8.13551 7.49707 8.00301C7.49707 7.87051 7.5497 7.74344 7.64337 7.64973L12.1406 3.15255C12.1867 3.10482 12.2418 3.06676 12.3028 3.04057C12.3637 3.01438 12.4293 3.0006 12.4956 3.00002C12.562 2.99944 12.6278 3.01209 12.6892 3.03721C12.7506 3.06233 12.8064 3.09944 12.8533 3.14636C12.9002 3.19327 12.9373 3.24906 12.9625 3.31047C12.9876 3.37188 13.0002 3.43768 12.9996 3.50403C12.9991 3.57038 12.9853 3.63595 12.9591 3.69691C12.9329 3.75787 12.8948 3.81301 12.8471 3.85911L8.34993 8.35629C8.25622 8.44997 8.12915 8.50259 7.99665 8.50259C7.86415 8.50259 7.73708 8.44997 7.64337 8.35629Z"
        fill="#3463FC"
      />
    </g>
    <defs>
      <clipPath id="clip0_112_8737">
        <rect width="10" height="10" fill="white" transform="translate(3 3)" />
      </clipPath>
    </defs>
  </svg>
);
const ColumnOptionsIcon = () => (
  <svg
    style={{ float: 'left', marginRight: '3px', marginTop: '1px' }}
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M6.64666 13L9.03481 11.876V8.93017L12.7796 5.13228C12.9208 4.98998 13 4.79466 13 4.59014V3.75877C13 3.33934 12.6709 3 12.2648 3H3.73524C3.32913 3 3 3.33934 3 3.75877V4.60915C3 4.80215 3.07085 4.98767 3.19859 5.12825L6.64666 8.93017V13Z"
      stroke="#3463FC"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);
//客户资料批量导入用
let dataList = [];
const options = [];

//给客户资料批量导入做判断用
const telephonePrefixNumList = [
  {
    city: '中国大陆',
    code: '+86',
  },
  {
    city: '中国香港',
    code: '+852',
  },
  {
    city: '中国澳门',
    code: '+853',
  },
  {
    city: '中国台湾',
    code: '+886',
  },
  {
    city: '新加坡',
    code: '+65',
  },
  {
    city: '阿富汗',
    code: '+93',
  },
  {
    city: '阿尔巴尼亚',
    code: '+355',
  },
  {
    city: '阿尔格拉',
    code: '+213',
  },
  {
    city: '安道尔',
    code: '+376',
  },
  {
    city: '安哥拉',
    code: '+244',
  },
  {
    city: '安圭拉',
    code: '+1264',
  },
  {
    city: '阿森松岛',
    code: '+247',
  },
  {
    city: '安提瓜和巴布达',
    code: '+1268',
  },
  {
    city: '阿根廷',
    code: '+54',
  },
  {
    city: '亚美尼亚',
    code: '+374',
  },
  {
    city: '阿鲁巴',
    code: '+297',
  },
  {
    city: '澳大利亚',
    code: '+61',
  },
  {
    city: '奥地利',
    code: '+43',
  },
  {
    city: '阿塞拜疆',
    code: '+994',
  },
  {
    city: '巴哈马',
    code: '+1242',
  },
  {
    city: '巴林',
    code: '+973',
  },
  {
    city: '孟加拉国',
    code: '+880',
  },
  {
    city: '巴巴多斯',
    code: '+1246',
  },
  {
    city: '白俄罗斯',
    code: '+375',
  },
  {
    city: '比利时',
    code: '+32',
  },
  {
    city: '伯利兹',
    code: '+501',
  },
  {
    city: '贝宁',
    code: '+229',
  },
  {
    city: '百慕大',
    code: '+1441',
  },
  {
    city: '不丹',
    code: '+975',
  },
  {
    city: '玻利维亚',
    code: '+591',
  },
  {
    city: '波斯尼亚和黑塞哥维那',
    code: '+387',
  },
  {
    city: '博茨瓦纳',
    code: '+267',
  },
  {
    city: '巴西',
    code: '+55',
  },
  {
    city: '文莱',
    code: '+673',
  },
  {
    city: '保加利亚',
    code: '+359',
  },
  {
    city: '布基纳法索',
    code: '+226',
  },
  {
    city: '布隆迪',
    code: '+257',
  },
  {
    city: '柬埔寨',
    code: '+855',
  },
  {
    city: '喀麦隆',
    code: '+237',
  },
  {
    city: '加拿大',
    code: '+1',
  },
  {
    city: '佛得角',
    code: '+238',
  },
  {
    city: '开曼群岛',
    code: '+1345',
  },
  {
    city: '中非共和国',
    code: '+236',
  },
  {
    city: '乍得',
    code: '+235',
  },
  {
    city: '智利',
    code: '+56',
  },
  {
    city: '哥伦比亚',
    code: '+57',
  },
  {
    city: '科摩罗',
    code: '+269',
  },
  {
    city: '刚果共和国',
    code: '+242',
  },
  {
    city: '刚果民主共和国',
    code: '+243',
  },
  {
    city: '库克群岛',
    code: '+682',
  },
  {
    city: '哥斯达黎加',
    code: '+506',
  },
  {
    city: '科特迪沃',
    code: '+225',
  },
  {
    city: '克罗地亚',
    code: '+385',
  },
  {
    city: '古巴',
    code: '+53',
  },
  {
    city: '塞浦路斯',
    code: '+357',
  },
  {
    city: '+捷克共和国',
    code: '+420',
  },
  {
    city: '丹麦',
    code: '+45',
  },
  {
    city: '吉布提',
    code: '+253',
  },
  {
    city: '多米尼加',
    code: '+1767',
  },
  {
    city: '多米尼加共和国',
    code: '+1809',
  },
  {
    city: '厄瓜多尔',
    code: '+593',
  },
  {
    city: '埃及',
    code: '+20',
  },
  {
    city: '艾萨尔瓦多',
    code: '+503',
  },
  {
    city: '爱沙尼亚',
    code: '+372',
  },
  {
    city: '埃塞俄比亚',
    code: '+251',
  },
  {
    city: '法罗群岛',
    code: '+298',
  },
  {
    city: '斐济',
    code: '+679',
  },
  {
    city: '芬兰',
    code: '+358',
  },
  {
    city: '法国',
    code: '+33',
  },
  {
    city: '法属圭亚那',
    code: '+594',
  },
  {
    city: '法属波利尼西亚',
    code: '+689',
  },
  {
    city: '加蓬',
    code: '+241',
  },
  {
    city: '冈比亚',
    code: '+220',
  },
  {
    city: '格鲁吉亚',
    code: '+995',
  },
  {
    city: '德国',
    code: '+94',
  },
  {
    city: '加纳',
    code: '+233',
  },
  {
    city: '直布罗陀',
    code: '+350',
  },
  {
    city: '希腊',
    code: '+30',
  },
  {
    city: '格陵兰',
    code: '+299',
  },
  {
    city: '格林纳达',
    code: '+1473',
  },
  {
    city: '瓜德罗普',
    code: '+590',
  },
  {
    city: '关岛',
    code: '+1671',
  },
  {
    city: '危地马拉',
    code: '+502',
  },
  {
    city: '几内亚',
    code: '+240',
  },
  {
    city: '根西',
    code: '+44',
  },
  {
    city: '几内亚',
    code: '+224',
  },
  {
    city: '圭亚那',
    code: '+592',
  },
  {
    city: '海地',
    code: '+509',
  },
  {
    city: '洪都拉斯',
    code: '+504',
  },
  {
    city: '缅甸',
    code: '+95',
  },
  {
    city: '匈牙利',
    code: '+36',
  },
  {
    city: '冰岛',
    code: '+354',
  },
  {
    city: '印度',
    code: '+91',
  },
  {
    city: '印度尼西亚',
    code: '+62',
  },
  {
    city: '伊朗',
    code: '+98',
  },
  {
    city: '伊拉克',
    code: '+964',
  },
  {
    city: '爱尔兰',
    code: '+353',
  },
  {
    city: '马恩岛',
    code: '+44',
  },
  {
    city: '以色列',
    code: '+972',
  },
  {
    city: '意大利',
    code: '+93',
  },
  {
    city: '牙买加',
    code: '+1876',
  },
  {
    city: '日本',
    code: '+81',
  },
  {
    city: '泽西岛',
    code: '+44',
  },
  {
    city: '约旦',
    code: '+962',
  },
  {
    city: '哈萨克斯坦',
    code: '+7',
  },
  {
    city: '肯尼亚',
    code: '+254',
  },
  {
    city: '科索沃',
    code: '+383',
  },
  {
    city: '科威特',
    code: '+965',
  },
  {
    city: '吉尔吉斯斯坦',
    code: '+996',
  },
  {
    city: '老挝',
    code: '+856',
  },
  {
    city: '拉脱维亚',
    code: '+371',
  },
  {
    city: '黎巴嫩',
    code: '+961',
  },
  {
    city: '莱索托',
    code: '+266',
  },
  {
    city: '利比里亚',
    code: '+231',
  },
  {
    city: '利比亚',
    code: '+218',
  },
  {
    city: '列支敦士登',
    code: '+423',
  },
  {
    city: '立陶宛',
    code: '+370',
  },
  {
    city: '卢森堡',
    code: '+352',
  },
  {
    city: '马其顿',
    code: '+389',
  },
  {
    city: '马达加斯加',
    code: '+261',
  },
  {
    city: '马拉维',
    code: '+265',
  },
  {
    city: '马来西亚',
    code: '+60',
  },
  {
    city: '马尔代夫',
    code: '+960',
  },
  {
    city: '马里',
    code: '+223',
  },
  {
    city: '马耳他',
    code: '+356',
  },
  {
    city: '马提尼克',
    code: '+596',
  },
  {
    city: '毛里塔尼亚',
    code: '+222',
  },
  {
    city: '毛里求斯',
    code: '+230',
  },
  {
    city: '马约特',
    code: '+262',
  },
  {
    city: '墨西哥',
    code: '+52',
  },
  {
    city: '摩尔多瓦',
    code: '+373',
  },
  {
    city: '摩纳哥',
    code: '+377',
  },
  {
    city: '蒙古',
    code: '+976',
  },
  {
    city: '黑山',
    code: '+382',
  },
  {
    city: '蒙特塞拉特',
    code: '+1664',
  },
  {
    city: '摩洛哥',
    code: '+212',
  },
  {
    city: '莫桑比克',
    code: '+258',
  },
  {
    city: '纳米比亚',
    code: '+264',
  },
  {
    city: '尼泊尔',
    code: '+977',
  },
  {
    city: '荷兰',
    code: '+31',
  },
  {
    city: '荷属安的列斯',
    code: '+599',
  },
  {
    city: '新喀里多尼亚',
    code: '+687',
  },
  {
    city: '新西兰',
    code: '+64',
  },
  {
    city: '尼加拉瓜',
    code: '+505',
  },
  {
    city: '尼日尔',
    code: '+227',
  },
  {
    city: '尼日利亚',
    code: '+234',
  },
  {
    city: '挪威',
    code: '+47',
  },
  {
    city: '阿曼',
    code: '+968',
  },
  {
    city: '巴基斯坦',
    code: '+92',
  },
  {
    city: '巴勒斯坦',
    code: '+970',
  },
  {
    city: '巴拿马',
    code: '+507',
  },
  {
    city: '巴布亚新几内亚',
    code: '+675',
  },
  {
    city: '巴拉圭',
    code: '+595',
  },
  {
    city: '秘鲁',
    code: '+51',
  },
  {
    city: '菲律宾',
    code: '+63',
  },
  {
    city: '波兰',
    code: '+48',
  },
  {
    city: '葡萄牙',
    code: '+351',
  },
  {
    city: '波多黎各',
    code: '+1',
  },
  {
    city: '库塔',
    code: '+974',
  },
  {
    city: '留尼汪',
    code: '+262',
  },
  {
    city: '罗马尼亚',
    code: '+40',
  },
  {
    city: '俄罗斯',
    code: '+7',
  },
  {
    city: '卢旺达',
    code: '+250',
  },
  {
    city: '萨摩亚东部',
    code: '+684',
  },
  {
    city: '萨摩亚西部',
    code: '+685',
  },
  {
    city: '圣马力诺',
    code: '+378',
  },
  {
    city: '圣多美和普林西比',
    code: '+239',
  },
  {
    city: '沙特阿拉伯',
    code: '+966',
  },
  {
    city: '塞内加尔',
    code: '+221',
  },
  {
    city: '塞尔维亚',
    code: '+381',
  },
  {
    city: '塞舌尔',
    code: '+248',
  },
  {
    city: '塞拉利昂',
    code: '+232',
  },
  {
    city: '斯洛伐克',
    code: '+421',
  },
  {
    city: '斯洛文尼亚',
    code: '+386',
  },
  {
    city: '南非',
    code: '+27',
  },
  {
    city: '韩国',
    code: '+82',
  },
  {
    city: '西班牙',
    code: '+34',
  },
  {
    city: '斯里兰卡',
    code: '+94',
  },
  {
    city: '圣基茨和尼维斯',
    code: '+1869',
  },
  {
    city: '圣卢西亚',
    code: '+1758',
  },
  {
    city: '圣文森特',
    code: '+1784',
  },
  {
    city: '苏丹',
    code: '+249',
  },
  {
    city: '苏里南',
    code: '+597',
  },
  {
    city: '斯威士兰',
    code: '+268',
  },
  {
    city: '瑞典',
    code: '+46',
  },
  {
    city: '瑞士',
    code: '+41',
  },
  {
    city: '叙利亚',
    code: '+963',
  },
  {
    city: '塔吉克斯坦',
    code: '+992',
  },
  {
    city: '坦桑尼亚',
    code: '+255',
  },
  {
    city: '泰国',
    code: '+66',
  },
  {
    city: '东帝汶',
    code: '+670',
  },
  {
    city: '多哥',
    code: '+228',
  },
  {
    city: '汤加',
    code: '+676',
  },
  {
    city: '特立尼达和多巴哥',
    code: '+1868',
  },
  {
    city: '突尼斯',
    code: '+216',
  },
  {
    city: '土耳其',
    code: '+90',
  },
  {
    city: '土库曼斯坦',
    code: '+993',
  },
  {
    city: '特克斯和凯科斯群岛',
    code: '+1649',
  },
  {
    city: '乌干达',
    code: '+256',
  },
  {
    city: '乌克兰',
    code: '+380',
  },
  {
    city: '阿拉伯联合酋长国',
    code: '+971',
  },
  {
    city: '英国',
    code: '+44',
  },
  {
    city: '美国',
    code: '+1',
  },
  {
    city: '乌拉圭',
    code: '+598',
  },
  {
    city: '乌兹别克斯坦',
    code: '+998',
  },
  {
    city: '瓦努阿图',
    code: '+678',
  },
  {
    city: '委内瑞拉',
    code: '+58',
  },
  {
    city: '越南',
    code: '+84',
  },
  {
    city: '维尔京群岛',
    code: '+1340',
  },
  {
    city: '也门',
    code: '+967',
  },
  {
    city: '赞比亚',
    code: '+260',
  },
  {
    city: '津巴布韦',
    code: '+263',
  },
];

class CustomerInformation extends Component {
  formAddFilterModalRef = React.createRef();
  formSearchFilterRef = React.createRef();

  constructor(props) {
    super(props);
    this.state = {
      tableLoading: true,
      defaultSelectValue: '',
      addCustomerInformationStatus: true,
      editorCustomerInformationStatus: false,
      contactCustomerStatus: false,
      dataSource: [],
      total: 0,
      pageNum: 1,
      pageSize: 10,
      channelOptions: [],
      groupOptions: [],
      mediaDefList: [],
      customerExtList: [],

      customerName: null, // 客户姓名 查询条件
      customerEmail: null, // 客户邮箱 查询条件
      companyName: null, // 公司名称 查询条件
      channels: null, // 渠道 查询条件
      customerInfo: null, // 客户信息 查询条件
      // openMore: false, //显示更多字段弹窗
      customer: {
        crmCustomerMediaInstMap: {},
        crmCustomerExtInstList: [],
      }, // 用于保存客户信息
      contactCustomer: {}, // 用于保存联系客户信息
      areaNumList: [
        {
          city: '中国大陆',
          code: '+86',
        },
        {
          city: '中国香港',
          code: '+852',
        },
        {
          city: '中国澳门',
          code: '+853',
        },
        {
          city: '中国台湾',
          code: '+886',
        },
        {
          city: '新加坡',
          code: '+65',
        },
        {
          city: '阿富汗',
          code: '+93',
        },
        {
          city: '阿尔巴尼亚',
          code: '+355',
        },
        {
          city: '阿尔格拉',
          code: '+213',
        },
        {
          city: '安道尔',
          code: '+376',
        },
        {
          city: '安哥拉',
          code: '+244',
        },
        {
          city: '安圭拉',
          code: '+1264',
        },
        {
          city: '阿森松岛',
          code: '+247',
        },
        {
          city: '安提瓜和巴布达',
          code: '+1268',
        },
        {
          city: '阿根廷',
          code: '+54',
        },
        {
          city: '亚美尼亚',
          code: '+374',
        },
        {
          city: '阿鲁巴',
          code: '+297',
        },
        {
          city: '澳大利亚',
          code: '+61',
        },
        {
          city: '奥地利',
          code: '+43',
        },
        {
          city: '阿塞拜疆',
          code: '+994',
        },
        {
          city: '巴哈马',
          code: '+1242',
        },
        {
          city: '巴林',
          code: '+973',
        },
        {
          city: '孟加拉国',
          code: '+880',
        },
        {
          city: '巴巴多斯',
          code: '+1246',
        },
        {
          city: '白俄罗斯',
          code: '+375',
        },
        {
          city: '比利时',
          code: '+32',
        },
        {
          city: '伯利兹',
          code: '+501',
        },
        {
          city: '贝宁',
          code: '+229',
        },
        {
          city: '百慕大',
          code: '+1441',
        },
        {
          city: '不丹',
          code: '+975',
        },
        {
          city: '玻利维亚',
          code: '+591',
        },
        {
          city: '波斯尼亚和黑塞哥维那',
          code: '+387',
        },
        {
          city: '博茨瓦纳',
          code: '+267',
        },
        {
          city: '巴西',
          code: '+55',
        },
        {
          city: '文莱',
          code: '+673',
        },
        {
          city: '保加利亚',
          code: '+359',
        },
        {
          city: '布基纳法索',
          code: '+226',
        },
        {
          city: '布隆迪',
          code: '+257',
        },
        {
          city: '柬埔寨',
          code: '+855',
        },
        {
          city: '喀麦隆',
          code: '+237',
        },
        {
          city: '加拿大',
          code: '+1',
        },
        {
          city: '佛得角',
          code: '+238',
        },
        {
          city: '开曼群岛',
          code: '+1345',
        },
        {
          city: '中非共和国',
          code: '+236',
        },
        {
          city: '乍得',
          code: '+235',
        },
        {
          city: '智利',
          code: '+56',
        },
        {
          city: '哥伦比亚',
          code: '+57',
        },
        {
          city: '科摩罗',
          code: '+269',
        },
        {
          city: '刚果共和国',
          code: '+242',
        },
        {
          city: '刚果民主共和国',
          code: '+243',
        },
        {
          city: '库克群岛',
          code: '+682',
        },
        {
          city: '哥斯达黎加',
          code: '+506',
        },
        {
          city: '科特迪沃',
          code: '+225',
        },
        {
          city: '克罗地亚',
          code: '+385',
        },
        {
          city: '古巴',
          code: '+53',
        },
        {
          city: '塞浦路斯',
          code: '+357',
        },
        {
          city: '+捷克共和国',
          code: '+420',
        },
        {
          city: '丹麦',
          code: '+45',
        },
        {
          city: '吉布提',
          code: '+253',
        },
        {
          city: '多米尼加',
          code: '+1767',
        },
        {
          city: '多米尼加共和国',
          code: '+1809',
        },
        {
          city: '厄瓜多尔',
          code: '+593',
        },
        {
          city: '埃及',
          code: '+20',
        },
        {
          city: '艾萨尔瓦多',
          code: '+503',
        },
        {
          city: '爱沙尼亚',
          code: '+372',
        },
        {
          city: '埃塞俄比亚',
          code: '+251',
        },
        {
          city: '法罗群岛',
          code: '+298',
        },
        {
          city: '斐济',
          code: '+679',
        },
        {
          city: '芬兰',
          code: '+358',
        },
        {
          city: '法国',
          code: '+33',
        },
        {
          city: '法属圭亚那',
          code: '+594',
        },
        {
          city: '法属波利尼西亚',
          code: '+689',
        },
        {
          city: '加蓬',
          code: '+241',
        },
        {
          city: '冈比亚',
          code: '+220',
        },
        {
          city: '格鲁吉亚',
          code: '+995',
        },
        {
          city: '德国',
          code: '+94',
        },
        {
          city: '加纳',
          code: '+233',
        },
        {
          city: '直布罗陀',
          code: '+350',
        },
        {
          city: '希腊',
          code: '+30',
        },
        {
          city: '格陵兰',
          code: '+299',
        },
        {
          city: '格林纳达',
          code: '+1473',
        },
        {
          city: '瓜德罗普',
          code: '+590',
        },
        {
          city: '关岛',
          code: '+1671',
        },
        {
          city: '危地马拉',
          code: '+502',
        },
        {
          city: '几内亚',
          code: '+240',
        },
        {
          city: '根西',
          code: '+44',
        },
        {
          city: '几内亚',
          code: '+224',
        },
        {
          city: '圭亚那',
          code: '+592',
        },
        {
          city: '海地',
          code: '+509',
        },
        {
          city: '洪都拉斯',
          code: '+504',
        },
        {
          city: '缅甸',
          code: '+95',
        },
        {
          city: '匈牙利',
          code: '+36',
        },
        {
          city: '冰岛',
          code: '+354',
        },
        {
          city: '印度',
          code: '+91',
        },
        {
          city: '印度尼西亚',
          code: '+62',
        },
        {
          city: '伊朗',
          code: '+98',
        },
        {
          city: '伊拉克',
          code: '+964',
        },
        {
          city: '爱尔兰',
          code: '+353',
        },
        {
          city: '马恩岛',
          code: '+44',
        },
        {
          city: '以色列',
          code: '+972',
        },
        {
          city: '意大利',
          code: '+93',
        },
        {
          city: '牙买加',
          code: '+1876',
        },
        {
          city: '日本',
          code: '+81',
        },
        {
          city: '泽西岛',
          code: '+44',
        },
        {
          city: '约旦',
          code: '+962',
        },
        {
          city: '哈萨克斯坦',
          code: '+7',
        },
        {
          city: '肯尼亚',
          code: '+254',
        },
        {
          city: '科索沃',
          code: '+383',
        },
        {
          city: '科威特',
          code: '+965',
        },
        {
          city: '吉尔吉斯斯坦',
          code: '+996',
        },
        {
          city: '老挝',
          code: '+856',
        },
        {
          city: '拉脱维亚',
          code: '+371',
        },
        {
          city: '黎巴嫩',
          code: '+961',
        },
        {
          city: '莱索托',
          code: '+266',
        },
        {
          city: '利比里亚',
          code: '+231',
        },
        {
          city: '利比亚',
          code: '+218',
        },
        {
          city: '列支敦士登',
          code: '+423',
        },
        {
          city: '立陶宛',
          code: '+370',
        },
        {
          city: '卢森堡',
          code: '+352',
        },
        {
          city: '马其顿',
          code: '+389',
        },
        {
          city: '马达加斯加',
          code: '+261',
        },
        {
          city: '马拉维',
          code: '+265',
        },
        {
          city: '马来西亚',
          code: '+60',
        },
        {
          city: '马尔代夫',
          code: '+960',
        },
        {
          city: '马里',
          code: '+223',
        },
        {
          city: '马耳他',
          code: '+356',
        },
        {
          city: '马提尼克',
          code: '+596',
        },
        {
          city: '毛里塔尼亚',
          code: '+222',
        },
        {
          city: '毛里求斯',
          code: '+230',
        },
        {
          city: '马约特',
          code: '+262',
        },
        {
          city: '墨西哥',
          code: '+52',
        },
        {
          city: '摩尔多瓦',
          code: '+373',
        },
        {
          city: '摩纳哥',
          code: '+377',
        },
        {
          city: '蒙古',
          code: '+976',
        },
        {
          city: '黑山',
          code: '+382',
        },
        {
          city: '蒙特塞拉特',
          code: '+1664',
        },
        {
          city: '摩洛哥',
          code: '+212',
        },
        {
          city: '莫桑比克',
          code: '+258',
        },
        {
          city: '纳米比亚',
          code: '+264',
        },
        {
          city: '尼泊尔',
          code: '+977',
        },
        {
          city: '荷兰',
          code: '+31',
        },
        {
          city: '荷属安的列斯',
          code: '+599',
        },
        {
          city: '新喀里多尼亚',
          code: '+687',
        },
        {
          city: '新西兰',
          code: '+64',
        },
        {
          city: '尼加拉瓜',
          code: '+505',
        },
        {
          city: '尼日尔',
          code: '+227',
        },
        {
          city: '尼日利亚',
          code: '+234',
        },
        {
          city: '挪威',
          code: '+47',
        },
        {
          city: '阿曼',
          code: '+968',
        },
        {
          city: '巴基斯坦',
          code: '+92',
        },
        {
          city: '巴勒斯坦',
          code: '+970',
        },
        {
          city: '巴拿马',
          code: '+507',
        },
        {
          city: '巴布亚新几内亚',
          code: '+675',
        },
        {
          city: '巴拉圭',
          code: '+595',
        },
        {
          city: '秘鲁',
          code: '+51',
        },
        {
          city: '菲律宾',
          code: '+63',
        },
        {
          city: '波兰',
          code: '+48',
        },
        {
          city: '葡萄牙',
          code: '+351',
        },
        {
          city: '波多黎各',
          code: '+1',
        },
        {
          city: '库塔',
          code: '+974',
        },
        {
          city: '留尼汪',
          code: '+262',
        },
        {
          city: '罗马尼亚',
          code: '+40',
        },
        {
          city: '俄罗斯',
          code: '+7',
        },
        {
          city: '卢旺达',
          code: '+250',
        },
        {
          city: '萨摩亚东部',
          code: '+684',
        },
        {
          city: '萨摩亚西部',
          code: '+685',
        },
        {
          city: '圣马力诺',
          code: '+378',
        },
        {
          city: '圣多美和普林西比',
          code: '+239',
        },
        {
          city: '沙特阿拉伯',
          code: '+966',
        },
        {
          city: '塞内加尔',
          code: '+221',
        },
        {
          city: '塞尔维亚',
          code: '+381',
        },
        {
          city: '塞舌尔',
          code: '+248',
        },
        {
          city: '塞拉利昂',
          code: '+232',
        },
        {
          city: '斯洛伐克',
          code: '+421',
        },
        {
          city: '斯洛文尼亚',
          code: '+386',
        },
        {
          city: '南非',
          code: '+27',
        },
        {
          city: '韩国',
          code: '+82',
        },
        {
          city: '西班牙',
          code: '+34',
        },
        {
          city: '斯里兰卡',
          code: '+94',
        },
        {
          city: '圣基茨和尼维斯',
          code: '+1869',
        },
        {
          city: '圣卢西亚',
          code: '+1758',
        },
        {
          city: '圣文森特',
          code: '+1784',
        },
        {
          city: '苏丹',
          code: '+249',
        },
        {
          city: '苏里南',
          code: '+597',
        },
        {
          city: '斯威士兰',
          code: '+268',
        },
        {
          city: '瑞典',
          code: '+46',
        },
        {
          city: '瑞士',
          code: '+41',
        },
        {
          city: '叙利亚',
          code: '+963',
        },
        {
          city: '塔吉克斯坦',
          code: '+992',
        },
        {
          city: '坦桑尼亚',
          code: '+255',
        },
        {
          city: '泰国',
          code: '+66',
        },
        {
          city: '东帝汶',
          code: '+670',
        },
        {
          city: '多哥',
          code: '+228',
        },
        {
          city: '汤加',
          code: '+676',
        },
        {
          city: '特立尼达和多巴哥',
          code: '+1868',
        },
        {
          city: '突尼斯',
          code: '+216',
        },
        {
          city: '土耳其',
          code: '+90',
        },
        {
          city: '土库曼斯坦',
          code: '+993',
        },
        {
          city: '特克斯和凯科斯群岛',
          code: '+1649',
        },
        {
          city: '乌干达',
          code: '+256',
        },
        {
          city: '乌克兰',
          code: '+380',
        },
        {
          city: '阿拉伯联合酋长国',
          code: '+971',
        },
        {
          city: '英国',
          code: '+44',
        },
        {
          city: '美国',
          code: '+1',
        },
        {
          city: '乌拉圭',
          code: '+598',
        },
        {
          city: '乌兹别克斯坦',
          code: '+998',
        },
        {
          city: '瓦努阿图',
          code: '+678',
        },
        {
          city: '委内瑞拉',
          code: '+58',
        },
        {
          city: '越南',
          code: '+84',
        },
        {
          city: '维尔京群岛',
          code: '+1340',
        },
        {
          city: '也门',
          code: '+967',
        },
        {
          city: '赞比亚',
          code: '+260',
        },
        {
          city: '津巴布韦',
          code: '+263',
        },
      ], //区号列表
      openAddFilter: false,
      createFilterLoading: false,
      openColumnOptions: false, //显示列选项弹窗
      workRecordTotal: 0,
      workRecordPageNum: 1,
      workRecordPageSize: 5,
      workRecordList: [],
      tags: [],
      assignWorkOrderList: {},
      textInput: '',
      changeGroupingModal: false,
      batchImportModal: false,
      selectedRowKeys: [],
      selectFilterList: '',
      fileList: [],
      uploading: false,
      customerGradeList: [],
      groupIdList: [],
      saveLoading: false,
      saveFilterLoading: true,
      filterConditionList: [],
      openMore: false, //显示更多字段弹窗
      filterCustomerExtList: [],
      customerFilterName: '',
      defaultFilter: '',
      customerFilterId: '',
      filterCriteriaList: {},
      defaultValueArray: [],
      defaultValueArrayTable: [],
      dynamicColumn: [],
    };
  }
  formRef = React.createRef();
  wangEditorRef = React.createRef();
  componentDidMount() {
    this.initData();
    this.getChannels();
    this.getCustomerGroupList();
    this.getCustomerMediaDef();
    this.getCustomerExt();
    this.getGradeList();
    this.getCustomerExtList();
    console.log(this.props.customerId);
    this.editorCustomerInformation(this.props.customerId);
  }

  componentDidUpdate(prevProps, prevState, snapshot) {
    // console.log(prevState);
    // 父组件参数发生变化
    if (this.props.selectedConnect?.connectId !== this.state.connectId) {
      this.setState({
        connectId: this.props.selectedConnect.connectId,
      });
    }
  }
  initData = () => {
    // this.setState({
    //   tableLoading: true,
    // });
    // const { pageSize, pageNum } = this.state;
    // const payload = {
    //   pageInfo: {
    //     pageNum: pageNum,
    //     pageSize: pageSize,
    //   },
    //   data: {},
    //   // customerGroupName: groupName,
    //   // customerName: this.state.customerName,
    //   // customerEmail: this.state.customerEmail,
    //   // companyName: this.state.companyName,
    //   // channels: this.state.channels,
    //   // customerInfo: this.state.customerInfo,
    // };
    // this.props.dispatch({
    //   type: 'customerInformationManagement/customerinfoList',
    //   payload: payload,
    //   callback: res => {
    //     if (res.code === 200) {
    //       let data = res.data.rows;
    //       data.forEach(item => {
    //         item.extInstVoList?.map(itemSon => {
    //           item[itemSon.code] = itemSon.value;
    //         });
    //       });
    //       this.setState({
    //         dataSource: data,
    //         total: res.data.total,
    //         tableLoading: false,
    //       });
    //     }
    //   },
    // });
    // this.setState({
    //   tableLoading: false,
    // });
  };
  /**
   * 获取用户登录后动态列code
   */
  getDynamicColumn = () => {
    const { filterCustomerExtList } = this.state;
    let newColumns;
    this.props.dispatch({
      type: 'customerInformationManagement/getCustomerProps',
      callback: res => {
        if (res.code === 200) {
          //如果为空则展示默认字段，并且列选项中勾选住
          if (res.data === null) {
            newColumns = [
              {
                title: (
                  <FormattedMessage
                    id="customerInformation.table.customerName"
                    defaultMessage="客户名称"
                  />
                ),
                dataIndex: 'name',
                key: 'name',
                width: 200,
                ellipsis: true,
              },

              {
                title: (
                  <FormattedMessage
                    id="customerInformation.table.mailAddress"
                    defaultMessage="邮箱地址"
                  />
                ),
                dataIndex: 'emailAddress',
                key: 'emailAddress',
                width: 200,
                ellipsis: true,
              },
              {
                title: (
                  <FormattedMessage
                    id="customerInformation.table.mobilePhoneNumber"
                    defaultMessage="手机号码"
                  />
                ),
                dataIndex: 'telephone',
                key: 'telephone',
                width: 200,
                ellipsis: true,
              },
              {
                title: (
                  <FormattedMessage
                    id="customerInformation.table.groupName"
                    defaultMessage="分组名称"
                  />
                ),
                dataIndex: 'groupNameConcat',
                key: 'groupNameConcat',
                width: 200,
                ellipsis: true,
              },
              {
                title: (
                  <FormattedMessage
                    id="customerInformation.table.sourceChannel"
                    defaultMessage="来源渠道"
                  />
                ),
                dataIndex: 'channelNameConcat',
                key: 'channelNameConcat',
                width: 200,
                ellipsis: true,
              },
              // {
              //   title: (
              //     <FormattedMessage
              //       id="customerInformation.table.createTime"
              //       defaultMessage="创建时间"
              //     />
              //   ),
              //   dataIndex: 'createTime',
              //   key: 'createTime',
              //   width: 200,
              //   ellipsis: true,
              // },
              // {
              //   title: (
              //     <FormattedMessage
              //       id="customerInformation.table.address"
              //       defaultMessage="邮寄地址"
              //     />
              //   ),
              //   dataIndex: 'mailingAddress',
              //   key: 'mailingAddress',
              //   width: 200,
              //   ellipsis: true,
              // },
              {
                title: (
                  <FormattedMessage
                    id="customerInformation.table.operation"
                    defaultMessage="操作"
                  />
                ),
                dataIndex: 'operation',
                key: 'operation',
                width: 200,
                fixed: 'right',
                render: (text, record) => (
                  <div>
                    <a
                      onClick={() => this.contactCustomer(record)}
                      disabled={this.state.contactLoading}
                    >
                      <FormattedMessage
                        id="customerInformation.table.contactCustomer"
                        defaultMessage="联系客户"
                      />
                    </a>
                    <a
                      onClick={() =>
                        this.editorCustomerInformation(record.customerId)
                      }
                      disabled={this.state.editorLoading}
                    >
                      <FormattedMessage
                        id="customerInformation.table.editor"
                        defaultMessage="修改"
                      />
                    </a>
                  </div>
                ),
              },
            ];
          } else {
            let data = res.data;
            //动态渲染
            newColumns = data.map(item => {
              let columnsItem = { ...item };
              if (item.code === 'groupIds') {
                columnsItem.code = 'groupNameConcat';
              } else if (item.code === 'channelIds') {
                columnsItem.code = 'channelNameConcat';
              } else if (item.code === 'sex') {
                return {
                  title: columnsItem.name,
                  dataIndex: columnsItem.code,
                  key: columnsItem.code,
                  width: 200,
                  ellipsis: true,
                  render: value => {
                    let sex =
                      value === 0
                        ? getIntl().formatMessage({
                            id: 'user.management.operation.table.sex.0',
                          })
                        : value === 1
                        ? getIntl().formatMessage({
                            id: 'user.management.operation.table.sex.1',
                          })
                        : value === 2
                        ? getIntl().formatMessage({
                            id: 'user.management.operation.table.sex.2',
                          })
                        : '';
                    return (
                      <div>
                        <span>{sex}</span>
                      </div>
                    );
                  },
                };
              } else if (item.code === 'gradeId') {
                return {
                  title: columnsItem.name,
                  dataIndex: columnsItem.code,
                  key: columnsItem.code,
                  width: 200,
                  ellipsis: true,
                  render: value => {
                    let sex =
                      value === '1'
                        ? getIntl().formatMessage({
                            id: 'user.management.operation.table.user.1',
                          })
                        : value === '2'
                        ? getIntl().formatMessage({
                            id: 'user.management.operation.table.user.2',
                          })
                        : '';
                    return (
                      <div>
                        <span>{sex}</span>
                      </div>
                    );
                  },
                };
              } else if (item.code === 'birthday') {
                return {
                  title: columnsItem.name,
                  dataIndex: columnsItem.code,
                  key: columnsItem.code,
                  width: 200,
                  ellipsis: true,
                  render: value => {
                    let birthday = value ? value.split(' ')[0] : '';
                    return (
                      <div>
                        <span>{birthday}</span>
                      </div>
                    );
                  },
                };
              }
              return {
                title: columnsItem.name,
                dataIndex: columnsItem.code,
                key: columnsItem.code,
                width: 200,
                ellipsis: true,
              };
            });
            //插入操作列
            newColumns.push({
              title: (
                <FormattedMessage
                  id="customerInformation.table.operation"
                  defaultMessage="操作"
                />
              ),
              dataIndex: 'operation',
              key: 'operation',
              width: 200,
              fixed: 'right',
              render: (text, record) => (
                <div>
                  <a
                    onClick={() => this.contactCustomer(record)}
                    disabled={this.state.contactLoading}
                  >
                    <FormattedMessage
                      id="customerInformation.table.contactCustomer"
                      defaultMessage="联系客户"
                    />
                  </a>
                  <a
                    onClick={() =>
                      this.editorCustomerInformation(record.customerId)
                    }
                    disabled={this.state.editorLoading}
                  >
                    <FormattedMessage
                      id="customerInformation.table.editor"
                      defaultMessage="修改"
                    />
                  </a>
                </div>
              ),
            });
          }
          console.log(newColumns);
          let newFilterConditionList = filterCustomerExtList.filter(obj1 =>
            newColumns.some(obj2 => {
              //名称不一致的情况
              if (['groupNameConcat', 'channelNameConcat'].includes(obj2.key)) {
                return (
                  obj1.customerExtDefCode === 'groupIds' ||
                  obj1.customerExtDefCode === 'channelIds'
                );
              } else {
                return obj2.key === obj1.customerExtDefCode;
              }
            }),
          );
          //获取到id返回给多选框默认状态
          let defaultValueArrayTable = newFilterConditionList?.map(item => {
            return item.customerExtDefId;
          });
          this.setState({
            dynamicColumn: newColumns,
            defaultValueArrayTable: defaultValueArrayTable,
          });
        }
      },
    });
  };
  getChannels = () => {
    this.props.dispatch({
      type: 'channel/getChannel',
      payload: { pageSize: 1000, pageNum: 1 },
      callback: res => {
        if (res.code === 200) {
          this.setState({
            channelOptions: res.data.rows,
          });
        }
      },
    });
  };

  getCustomerGroupList = () => {
    this.props.dispatch({
      type: 'customerInformationManagement/getCustomerGroupList',
      payload: { pageSize: 1000, pageNum: 1, customerGroupName: '' },
      callback: res => {
        if (res.code === 200) {
          this.setState({
            groupOptions: res.data.rows,
          });
        }
      },
    });
  };

  getCustomerMediaDef = () => {
    this.props.dispatch({
      type: 'customerInformationManagement/getCustomerMediaInst',
      payload: {},
      callback: res => {
        if (res.code === 200) {
          this.setState({
            mediaDefList: res.data,
          });
          let mediaDefMap = res.data;
          Object.keys(mediaDefMap)?.map(key => {
            let list = mediaDefMap[key];
            this.state.customer.crmCustomerMediaInstMap[key] = [];
            list.map((item, index) => {
              this.state.customer.crmCustomerMediaInstMap[key][index] = {};
            });
          });
        }
      },
    });
  };

  // 获取客户等级列表
  getGradeList = () => {
    this.props.dispatch({
      type: 'customerInformationManagement/getGradeList',
      payload: {},
      callback: res => {
        if (res.code === 200) {
          console.log(res);
          this.setState({
            customerGradeList: res.data,
          });
        } else {
          notification.error({
            message: res.msg,
          });
        }
      },
    });
  };

  getCustomerExt = () => {
    this.props.dispatch({
      type: 'customerInformationManagement/queryCustomerExt',
      payload: {},
      callback: res => {
        if (res.code === 200) {
          this.setState({
            customerExtList: res.data,
          });
          let customerExtList = res.data;
          console.log(customerExtList);
          customerExtList?.map((item, index) => {
            this.state.customer.crmCustomerExtInstList[index] = {};
          });
        }
      },
    });
  };
  onSearch = value => {
    // this.setState({
    //   customerInfo: value,
    // });
    // this.initData();
  };
  /**
   * 勾选更多字段
   */
  onChangeCheckbox = checkedValues => {
    let { filterCustomerExtList, defaultValueArray } = this.state;
    console.log(checkedValues, filterCustomerExtList, defaultValueArray);
    let temporarilyCheckList = [];
    for (let i = 0; i < checkedValues.length; i++) {
      for (let n = 0; n < filterCustomerExtList.length; n++) {
        if (checkedValues[i] == filterCustomerExtList[n].customerExtDefId) {
          temporarilyCheckList.push(filterCustomerExtList[n]);
        }
      }
    }
    this.setState({
      temporarilyCheckList,
      defaultValueArray: checkedValues,
    });
    console.log(checkedValues);
  };
  /**
   * 勾选列表展示的值
   */
  onChangeCheckboxTable = checkedValues => {
    let { filterCustomerExtList, defaultValueArray } = this.state;
    console.log(checkedValues, filterCustomerExtList, defaultValueArray);
    let temporarilyCheckListTable = [];
    for (let i = 0; i < checkedValues.length; i++) {
      for (let n = 0; n < filterCustomerExtList.length; n++) {
        if (checkedValues[i] == filterCustomerExtList[n].customerExtDefId) {
          temporarilyCheckListTable.push(filterCustomerExtList[n]);
        }
      }
    }
    this.setState({
      temporarilyCheckListTable,
      defaultValueArrayTable: checkedValues,
    });
    console.log(defaultValueArray);
  };
  handleChange = e => {
    this.setState({
      [e.target.name]: e.target.value,
    });
  };
  handleChangeSelect = (value, name) => {
    this.setState({
      [name]: value,
    });
  };
  handleChannelsSelect = value => {
    this.setState(
      {
        channels: value.join(','),
      },
      () => {
        this.initData();
      },
    );
  };

  /**
   * 页码切换事件
   */
  handleTableChange = pagination => {
    this.setState(
      {
        pageSize: pagination.pageSize,
        pageNum: pagination.current,
      },
      () => {
        this.initData();
      },
    );
  };

  handleAddSelectChange = (value, name) => {
    const customer = this.state.customer;
    if (typeof value.join === 'function') {
      customer[name] = value.join(',');
    } else {
      customer[name] = value;
    }
    this.setState({
      customer: customer,
    });
  };
  handleAddInputChange = (e, index, type, ...args) => {
    console.log(e, index, type, args);
    const customer = this.state.customer;
    console.log(customer.crmCustomerExtInstList[index]);
    if (type === 'media') {
      const inputName = e.target.name.split(':');
      let crmCustomerMediaInst =
        customer.crmCustomerMediaInstMap[inputName[0]][index];
      crmCustomerMediaInst.code = inputName[1];
      crmCustomerMediaInst.name = inputName[2];
      crmCustomerMediaInst.value = e.target.value;
    } else if (type === 'ext') {
      let crmCustomerExtInst = customer.crmCustomerExtInstList[index];
      const inputName = e.target.name.split(':');
      crmCustomerExtInst.code = inputName[0];
      crmCustomerExtInst.name = inputName[1];
      crmCustomerExtInst.propType =
        customer.crmCustomerExtInstList[index].propertyTypeId;
      if (args[0] == '1004') {
        crmCustomerExtInst.value = e.target.value.join(',');
      } else {
        crmCustomerExtInst.value = e.target.value;
      }
      console.log(crmCustomerExtInst.value);
    } else {
      customer[e.target.name] = e.target.value;
    }
    this.setState({
      customer: customer,
    });
  };
  //时间选择表单
  handleAddDateChange = (date, dateString, name, index) => {
    const customer = this.state.customer;
    if (index != 'noExt') {
      let crmCustomerExtInst = customer.crmCustomerExtInstList[index];
      const inputName = name.split(':');
      crmCustomerExtInst.code = inputName[0];
      crmCustomerExtInst.name = inputName[1];
      crmCustomerExtInst.value = dateString;
      crmCustomerExtInst.propType =
        customer.crmCustomerExtInstList[index].propertyTypeId;
    } else {
      customer[name] = moment(date).format('yyyy-MM-DD');
    }
    this.setState({
      customer: customer,
    });
  };
  //时间范围选择表单
  handleAddRangeChange = (date, dateString, name, index) => {
    console.log(date, dateString);
    const customer = this.state.customer;
    let crmCustomerExtInst = customer.crmCustomerExtInstList[index];
    const inputName = name.split(':');
    crmCustomerExtInst.code = inputName[0];
    crmCustomerExtInst.name = inputName[1];
    crmCustomerExtInst.value = dateString[0] + ',' + dateString[1];
    crmCustomerExtInst.propType =
      customer.crmCustomerExtInstList[index].propertyTypeId;
    this.setState({
      customer: customer,
    });
  };
  // 显示添加客户资料内容
  addCustomerInformation = () => {
    this.formRef.current?.resetFields();
    this.setState({
      addCustomerInformationStatus: true,
      isEdit: false,
    });
  };
  // 修改客户资料
  editorCustomerInformation = customerId => {
    this.setState({
      editorLoading: true,
    });
    this.props.dispatch({
      type: 'customerInformationManagement/getCustomerDetail',
      payload: { customerId: customerId },
      callback: res => {
        if (res.code === 200) {
          let customerDetail = this.handleCustomerDetail(res.data);
          let customer = this.handleSaveCustomer(customerDetail);
          this.setState({
            addCustomerInformationStatus: true,
            isModalOpen: false,
            isEdit: true,
            customer: customer,
            editorLoading: false,
          });
          console.log(customerDetail, customer);
          this.formRef.current?.resetFields();
          this.formRef.current?.setFieldsValue(customerDetail);
        } else {
          notification.error({
            message: res.msg,
          });
        }
      },
    });
  };
  //处理回显数据
  handleCustomerDetail = result => {
    console.log(result);
    let customerDetail = result['crmCustomer'];
    //处理分组回显
    customerDetail.customerGroupId = customerDetail.customerGroupId.split(',');
    //处理渠道回显
    customerDetail.channels = customerDetail.channelIds
      ? customerDetail.channelIds.split(',')
      : null;
    //处理区号
    if (
      customerDetail.telephonePrefixId &&
      customerDetail.telephonePrefixId != null &&
      customerDetail.telephonePrefixId !== ''
    ) {
      customerDetail.telephonePrefixId = customerDetail.telephonePrefixId;
    } else {
      customerDetail.telephonePrefixId = '';
    }
    //处理生日
    if (
      customerDetail.birthday &&
      customerDetail.birthday != null &&
      customerDetail.birthday !== ''
    ) {
      customerDetail.birthday = moment(customerDetail.birthday);
    } else {
      customerDetail.birthday = '';
    }
    //处理社媒信息
    let crmCustomerMediaInstList = result['crmCustomerMediaInstList'];
    crmCustomerMediaInstList?.map((item, index) => {
      customerDetail[
        item.mediaChannelCode + ':' + item.code + ':' + item.name
      ] = item.value;
    });
    //处理扩展信息
    let crmCustomerExtInstList = result['crmCustomerExtInstList'];

    let disposeArr = this.state.customerExtList?.map(item => {
      if (item.propertyTypeId === '1004') {
        return item.customerExtDefCode;
      }
    });
    let disposeArrRange = this.state.customerExtList?.map(item => {
      if (item.propertyTypeId === '1009') {
        return item.customerExtDefCode;
      }
    });
    console.log(disposeArr, disposeArrRange, crmCustomerExtInstList);
    crmCustomerExtInstList?.map((item, index) => {
      if (disposeArr.includes(item.code)) {
        customerDetail[item.code + ':' + item.name] = item.value?.split(',');
      } else if (disposeArrRange.includes(item.code)) {
        customerDetail[item.code + ':' + item.name] = item.value?.split(',');
        customerDetail[item.code + ':' + item.name][0] = moment(
          customerDetail[item.code + ':' + item.name][0],
        );
        customerDetail[item.code + ':' + item.name][1] = moment(
          customerDetail[item.code + ':' + item.name][1],
        );
      } else {
        customerDetail[item.code + ':' + item.name] = item.value;
      }
    });
    customerDetail.crmCustomerExtInstList = crmCustomerExtInstList;
    // 处理tags信息
    if (
      customerDetail.customerLabel &&
      customerDetail.customerLabel != null &&
      customerDetail.customerLabel !== ''
    ) {
      let customerLabel = customerDetail.customerLabel;
      let tags = customerLabel.split(',');
      console.log(tags);
      this.setState({
        tags: tags,
      });
    } else {
      this.setState({
        tags: [],
      });
    }
    return customerDetail;
  };
  handleSaveCustomer = customerDetail => {
    let customer = this.state.customer;
    customer.customerId = customerDetail.customerId;
    //处理基本信息
    customer.name = customerDetail.name;
    if (
      customerDetail.birthday !== null &&
      customerDetail.birthday !== '' &&
      customerDetail.birthday !== undefined
    ) {
      customer.birthday = customerDetail.birthday.format('yyyy-MM-DD');
    } else {
      customer.birthday = '';
    }
    customer.sex = customerDetail.sex;
    customer.emailAddress = customerDetail.emailAddress;
    customer.telephonePrefixId = customerDetail.telephonePrefixId;
    customer.telephone = customerDetail.telephone;
    customer.companyName = customerDetail.companyName;
    customer.post = customerDetail.post;
    customer.mailingAddress = customerDetail.mailingAddress;
    customer.orderAddress = customerDetail.orderAddress;
    customer.deliveryAddress = customerDetail.deliveryAddress;
    customer.otherAddress = customerDetail.otherAddress;
    customer.remark = customerDetail.remark;
    customer.customerLabel = customerDetail.customerLabel;
    customer.gradeId = customerDetail.gradeId;

    //处理分组
    customer.customerGroupIds = customerDetail.customerGroupId?.join(',');
    //处理渠道
    customer.channels = customerDetail.channelIds;
    //处理扩展信息
    let crmCustomerExtInstList = this.state.customerExtList;

    crmCustomerExtInstList?.map((item, index) => {
      item.value =
        customerDetail[item.customerExtDefCode + ':' + item.customerExtDefName];
      item.propType = item.propertyTypeId;
      console.log(item);
      customer.crmCustomerExtInstList[index] = item;
    });
    //处理社媒信息
    let crmCustomerMediaInstMap = this.state.mediaDefList;
    Object.keys(crmCustomerMediaInstMap)?.map(key => {
      let list = crmCustomerMediaInstMap[key];
      list.map((item, index) => {
        item.value = customerDetail[key + ':' + item.code + ':' + item.name];
        customer.crmCustomerMediaInstMap[key][index] = item;
      });
    });
    return customer;
  };
  saveCustomerInformation = () => {
    this.formRef.current?.validateFields().then(values => {
      this.setState({
        saveLoading: true,
      });
      let customer = this.state.customer;
      console.log(this.state.customer, customer);
      //处理社媒信息
      let crmCustomerMediaInstMap = customer.crmCustomerMediaInstMap;
      let crmCustomerMediaInstList = [];
      Object.keys(crmCustomerMediaInstMap)?.map(key => {
        let list = crmCustomerMediaInstMap[key];
        list.map((item, index) => {
          let media = {
            code: item.code,
            name: item.name,
            value: item.value,
            mediaChannelCode: key,
          };
          crmCustomerMediaInstList.push(media);
        });
      });
      customer.crmCustomerMediaInstList = crmCustomerMediaInstList;
      // 处理扩展信息
      customer.crmCustomerExtInstList?.forEach((item, index) => {
        if (item.propertyTypeId === '1004' && Array.isArray(item.value)) {
          customer.crmCustomerExtInstList[
            index
          ].value = customer.crmCustomerExtInstList[index].value.join(',');
        } else if (
          item.propertyTypeId === '1009' &&
          Array.isArray(item.value)
        ) {
          customer.crmCustomerExtInstList[index].value =
            customer.crmCustomerExtInstList[index].value[0].format(
              'YYYY-MM-DD',
            ) +
            ',' +
            customer.crmCustomerExtInstList[index].value[1].format(
              'YYYY-MM-DD',
            );
        }
      });
      console.log(customer);
      //先校验是否存在联系方式重复的情况
      this.props.dispatch({
        type: 'customerInformationManagement/checkCustomerInfo',
        payload: customer,
        callback: res => {
          if (res.code === 200) {
            this.props.dispatch({
              type: 'customerInformationManagement/saveCustomer',
              payload: customer,
              callback: res => {
                if (res.code === 200) {
                  notification.success({
                    message: res.msg,
                  });
                } else {
                  notification.error({
                    message: res.msg,
                  });
                }
                //保存完成清空数据
                let customer = this.handleSaveCustomer({});
                this.setState({
                  addCustomerInformationStatus: false,
                  saveLoading: false,
                  tags: [],
                });
                this.initData();
              },
            });
          } else {
            this.setState({
              isModalOpen: true,
              savemsg: res.msg,
              repetCustomerId: res.data,
              saveLoading: false,
            });
          }
        },
      });
    });
  };
  //取消
  cancelCustomerInformation = () => {
    this.handleSaveCustomer({});
    this.setState({
      addCustomerInformationStatus: false,
      tags: [],
    });
    this.initData();
  };
  cancelCustomerCreate = () => {
    this.setState({
      isModalOpenConfirm: false,
      tags: [],
    });
  };
  //联系客户
  contactCustomer = record => {
    this.setState({
      contactLoading: true,
      contactCustomerStatus: true,
      contactCustomer: record,
      workRecordLoading: true,
    });
    this.props.dispatch({
      type: 'customerInformationManagement/queryHistoryCustomerWorkOrder',
      payload: record.customerId,
      // pageNum: this.state.workRecordPageNum,
      // pageSize: this.state.workRecordPageSize,
      //   customerId: ,
      // },
      callback: res => {
        if (res.code === 200) {
          this.setState({
            // workRecordTotal: res.data.total,
            workRecordList: res.data.rows,
          });
        } else {
          notification.error({
            message: res.msg,
          });
        }
        this.setState({
          contactLoading: false,
          workRecordLoading: false,
        });
      },
    });
  };
  /**
   * 页码切换事件
   */
  handleWorkRecordTableChange = pagination => {
    this.setState(
      {
        workRecordPageSize: pagination.pageSize,
        workRecordPageNum: pagination.current,
      },
      () => {
        this.contactCustomer(this.state.contactCustomer);
      },
    );
  };
  handleReturn = () => {
    this.setState({
      contactCustomerStatus: false,
      contactLoading: false,
      subject: null,
      contactChannelId: null,
      contactCustomer: {},
    });
  };
  contactCustomerByActive = () => {
    let content = this.wangEditorRef.current?.getHtml();
    let channelId = this.state.contactChannelId;
    let customerId = this.state.contactCustomer.customerId;
    let subject = this.state.subject;
    let payload = {
      content: content,
      channelId: channelId,
      connectId: this.state.connectId,
      customerId: customerId,
      subject: subject,
      desAddress: this.state.contactCustomer.emailAddress,
    };
    console.log(payload);
    this.setState({
      replayLoading: true,
    });
    this.props.dispatch({
      type: 'customerInformationManagement/contactCustomer',
      payload: payload,
      callback: res => {
        if (res.code === 200) {
          notification.success({
            message: res.msg,
          });
          this.contactCustomer(this.state.contactCustomer);
          this.wangEditorRef.current?.editHtml('');
          this.setState({
            subject: null,
            contactChannelId: null,
          });
        } else {
          notification.error({
            message: res.msg,
          });
        }
        this.setState({
          replayLoading: false,
        });
      },
    });
  };

  customRequest = async ({ file }) => {
    // const formData = new FormData();
    // formData.append('file', file);
    // form 对象 就是我们上传接口需要的参数
    this.props.dispatch({
      type: 'customerInformationManagement/batchImport',
      // payload: formData,
      payload: dataList,
      callback: ({ code, data, msg }) => {
        if (200 === code) {
          //操作成功之后刷新当前页面，这样就可以展示新导入的那些数据啦
          notification.success({
            message: msg,
          });
          this.initData();
          this.setState({
            batchImportModal: false,
          });
        } else {
          notification.error({
            message: msg,
          });
          // this.initData();
          this.setState({
            batchImportModal: false,
          });
        }
      },
    });
    // 拿到调取接口返回的数据
  };
  uploadProps = {
    name: 'file', //取值必须和接口参数中的文件参数名相同： MultipartFile file
    multiple: false,
    showUploadList: false,
    accept:
      '.csv,.xls,.xlsx, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel',
    //上传前的校验
    beforeUpload: (file, fileList) => {
      let { size } = file;
      let fileSize = size / 1024 / 1024;
      if (fileSize > 10) {
        notification.error({
          message: getIntl().formatMessage(
            {
              id: 'message.upload.size',
            },
            {
              fileSize: 10,
            },
          ),
        });
        return false;
      } else {
        // return new Promise((resolve, reject) => {
        return new Promise((resolve, reject) => {
          //判断重复数据
          let phoneArr = [];
          let emailArr = [];

          let reader = new FileReader();
          reader.onload = function(e) {
            let fileObj = e.target.result;
            let workbook = XLSX.read(fileObj, {
              type: 'binary',
              cellDates: true, //设为true，将天数的时间戳转为时间格式
            });
            let first_worksheet = workbook.Sheets[workbook.SheetNames[0]];
            // 将excel转换成数组，判断data.length 就可以限制excel的行数
            let data = XLSX.utils.sheet_to_json(first_worksheet, {
              // 使用指定的字符串作为行对象中的键，如下key值顺序和excel表头的顺序要保持一致，如果根据key值获取数据为空，说明未填写数据
              header: [
                'name',
                'lastName',
                'emailAddress',
                'birthday',
                'sex',
                'telephonePrefixId',
                'telephone',
                'companyName',
                'post',
                'customerGroupIds',
                'channels',
                // 'mailingAddress',
                'orderAddress',
                'deliveryAddress',
                'otherAddress',
                'remark',
              ],
            });
            if (data && data.length < 2) {
              //如果少于2说明只有表头
              notification.error({
                message: '请在表格中添加客户信息后再批量导入',
              });
              reject(false);
            } else {
              //处理数据，不用管标题行
              for (let i = 1; i < data.length; i++) {
                let idx = i - 1;
                let lineNum = i + 1; //excel中的默认行号
                //获取每个数据，进行非空校验、格式校验或长度校验(姓名、分组和渠道是必须选择的，其他的都可以不填写；客户名称可以重复，但是手机号和邮箱不能重复)
                let name = data[i].name; //不能为空
                let lastName = data[i].lastName; //不能为空
                let emailAddress = data[i].emailAddress; //这个有格式要求
                let birthday = data[i].birthday; //这个有格式要求 yyyy-MM-dd
                let sex = data[i].sex;
                let telephonePrefixId = data[i].telephonePrefixId;
                let telephone = data[i].telephone; //这个有格式要求
                let companyName = data[i].companyName;
                let post = data[i].post;
                let customerGroupIds = data[i].customerGroupIds; //不能为空
                let channels = data[i].channels; //不能为空
                // let mailingAddress = data[i].mailingAddress;
                let orderAddress = data[i].orderAddress;
                let deliveryAddress = data[i].deliveryAddress;
                let otherAddress = data[i].otherAddress;
                let remark = data[i].remark;

                //合规性校验
                if (!name) {
                  notification.error({
                    //   message: '客户名称不能为空',
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.name.not.empty',
                      },
                      {
                        lineNum: lineNum,
                      },
                    ),
                  });
                  return;
                }
                if (name.trim().length == 0) {
                  notification.error({
                    //   message: '客户名称不能为空',
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.name.not.empty',
                      },
                      {
                        lineNum: lineNum,
                      },
                    ),
                  });
                  return;
                }
                //姓名格式,仅允许中、英文、数字及'.'
                let textPattern = /^[.0-9A-Za-z\u4e00-\u9fa5]+$/;
                if (!textPattern.test(name)) {
                  notification.error({
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.name.input.limit',
                      },
                      {
                        lineNum: lineNum,
                      },
                    ),
                  });
                  return;
                }
                if (name.trim().length > 200) {
                  notification.error({
                    // message: '客户名称最多200个字符',
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.name.length.limit',
                      },
                      {
                        lineNum: lineNum,
                      },
                    ),
                  });

                  return;
                }

                if (!lastName) {
                  notification.error({
                    //   message: '客户名称不能为空',
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.last.name.not.empty',
                      },
                      {
                        lineNum: lineNum,
                      },
                    ),
                  });
                  return;
                }
                if (lastName.trim().length == 0) {
                  notification.error({
                    //   message: '客户名称不能为空',
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.last.name.not.empty',
                      },
                      {
                        lineNum: lineNum,
                      },
                    ),
                  });
                  return;
                }
                //姓名格式,仅允许中、英文、数字及'.'
                // let textPattern = /^[.0-9A-Za-z\u4e00-\u9fa5]+$/;
                if (!textPattern.test(lastName)) {
                  notification.error({
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.last.name.input.limit',
                      },
                      {
                        lineNum: lineNum,
                      },
                    ),
                  });
                  return;
                }
                if (lastName.trim().length > 200) {
                  notification.error({
                    // message: '客户名称最多200个字符',
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.last.name.length.limit',
                      },
                      {
                        lineNum: lineNum,
                      },
                    ),
                  });

                  return;
                }

                //分组和渠道是必须选择的，如果是多个，要用#号分隔（先不用逗号了，避免excel填写时出现中英文逗号区分问题）
                if (!customerGroupIds) {
                  notification.error({
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.group.not.empty',
                      },
                      {
                        lineNum: lineNum,
                      },
                    ),
                  });
                  return;
                }
                if (customerGroupIds.trim().length == 0) {
                  notification.error({
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.group.not.empty',
                      },
                      {
                        lineNum: lineNum,
                      },
                    ),
                  });
                  return;
                }
                if (!channels) {
                  notification.error({
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.channel.not.empty',
                      },
                      {
                        lineNum: lineNum,
                      },
                    ),
                  });
                  return;
                }
                if (channels.trim().length == 0) {
                  notification.error({
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.channel.not.empty',
                      },
                      {
                        lineNum: lineNum,
                      },
                    ),
                  });
                  return;
                }
                // let emailPattern = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
                let emailPattern = /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
                if (emailAddress) {
                  if (emailPattern) {
                    if (!emailPattern.test(emailAddress)) {
                      notification.error({
                        message: '邮箱格式不正确',
                      });
                      return;
                    }
                  }
                  if (emailAddress.trim().length > 40) {
                    notification.error({
                      message: '邮箱最多40个字符',
                    });
                    return;
                  }
                  emailArr.push(emailAddress);
                }

                let phonePattern = /^[0-9]*$/;
                if (telephone) {
                  if (!phonePattern.test(telephone)) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.phone.input.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                  if (telephone.trim().length > 40) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.phone.length.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                  phoneArr.push(telephone);
                }
                //判断其他非必填项
                let birthdayFormat;
                if (birthday) {
                  //有格式要求yyyy-MM-dd
                  // 注意的点：xlsx将excel中的时间内容解析后，会小一天
                  // 如2020/11/3，xlsx会解析成  Mon Nov 02 2020 23:59:17 GMT+0800    小了43秒
                  // 当再用moment转换成日期时：
                  // Mon Nov 02 2020 23:59:17 GMT+0800  会转成2020/11/2     所以需要在moment转换后＋1天
                  if (
                    birthday === undefined ||
                    birthday === null ||
                    birthday === ''
                  ) {
                    birthdayFormat = null;
                  }
                  //非时间格式问题  返回Invalid date
                  let retFormat = moment(birthday).format('YYYY-MM-DD');
                  if (retFormat === 'Invalid date') {
                    //此处进行提醒
                    notification.error({
                      message: '生日格式不正确',
                    });
                    return;
                  }
                  birthdayFormat = moment(birthday)
                    .add(1, 'days')
                    .format('YYYY-MM-DD');
                }
                if (sex) {
                  if (sex != 0 && sex != 1 && sex != 2) {
                    //此处进行提醒
                    notification.error({
                      message: '性别信息只能用数字0、1、2进行标识',
                    });
                    return;
                  }
                }
                if (telephonePrefixId) {
                  let isTelephonePrefix = false;
                  for (let i = 0; i < telephonePrefixNumList.length; i++) {
                    let code = telephonePrefixNumList[i].code;
                    if (telephonePrefixId == code) {
                      isTelephonePrefix = true;
                      break;
                    }
                  }
                  if (!isTelephonePrefix) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.phone.prefix.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                }
                if (companyName) {
                  if (!textPattern.test(companyName)) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.company.input.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                  if (companyName.trim().length > 80) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.company.length.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                }
                if (post) {
                  if (!textPattern.test(post)) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.post.input.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                  if (post.trim().length > 80) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.post.length.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                }
                // if (mailingAddress) {
                //   if (!textPattern.test(mailingAddress)) {
                //     notification.error({
                //       message: getIntl().formatMessage(
                //         {
                //           id: 'customerInformation.mailingAddress.input.limit',
                //         },
                //         {
                //           lineNum: lineNum,
                //         },
                //       ),
                //     });
                //     return;
                //   }
                //   if (mailingAddress.trim().length > 200) {
                //     notification.error({
                //       message: getIntl().formatMessage(
                //         {
                //           id: 'customerInformation.mailingAddress.length.limit',
                //         },
                //         {
                //           lineNum: lineNum,
                //         },
                //       ),
                //     });
                //     return;
                //   }
                // }
                if (orderAddress) {
                  if (!textPattern.test(orderAddress)) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.orderAddress.input.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                  if (orderAddress.trim().length > 200) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.orderAddress.length.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                }
                if (deliveryAddress) {
                  if (!textPattern.test(deliveryAddress)) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.deliveryAddress.input.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                  if (deliveryAddress.trim().length > 200) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id:
                            'customerInformation.deliveryAddress.length.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                }
                if (otherAddress) {
                  if (!textPattern.test(otherAddress)) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.otherAddress.input.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                  if (otherAddress.trim().length > 200) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.otherAddress.length.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                }
                if (remark) {
                  if (!textPattern.test(remark)) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.remark.input.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                  if (remark.trim().length > 200) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.remark.length.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                }
                //封装数据，到后台进行保存
                dataList[idx] = {
                  name: name,
                  lastName: lastName,
                  emailAddress: emailAddress,
                  birthday: birthdayFormat,
                  sex: sex,
                  telephonePrefixId: telephonePrefixId,
                  telephone: telephone,
                  companyName: companyName,
                  post: post,
                  customerGroupIds: customerGroupIds,
                  channels: channels,
                  // mailingAddress: mailingAddress,
                  orderAddress: orderAddress,
                  deliveryAddress: deliveryAddress,
                  otherAddress: otherAddress,
                  remark: remark,
                };
              }

              //判断手机号或者邮箱是否重复
              phoneArr = phoneArr.sort();
              for (let i = 0; i < phoneArr.length; i++) {
                if (phoneArr[i] == phoneArr[i + 1]) {
                  let repeatPhone = phoneArr[i];
                  notification.error({
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.repeat.phone.tips',
                      },
                      {
                        repeatPhone: repeatPhone,
                      },
                    ),
                  });
                  return;
                }
              }

              emailArr = emailArr.sort();
              for (let i = 0; i < emailArr.length; i++) {
                if (emailArr[i] == emailArr[i + 1]) {
                  let repeatEmail = emailArr[i];
                  notification.error({
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.repeat.email.tips',
                      },
                      {
                        repeatEmail: repeatEmail,
                      },
                    ),
                  });
                  return;
                }
              }
              resolve(file);
            }
          };
          reader.readAsBinaryString(file);
        });
      }
      return true;
    },
    customRequest: this.customRequest,
  };
  newProps = {
    name: 'file', //取值必须和接口参数中的文件参数名相同： MultipartFile file
    multiple: false,
    showUploadList: true,
    accept:
      '.csv,.xls,.xlsx, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel',
    onRemove: file => {
      this.setState(state => {
        const index = state.fileList.indexOf(file);
        const newFileList = state.fileList.slice();
        newFileList.splice(index, 1);
        return {
          fileList: newFileList,
        };
      });
    },
    //上传前的校验
    beforeUpload: (file, fileList) => {
      let { size } = file;
      let fileSize = size / 1024 / 1024;
      if (fileSize > 10) {
        notification.error({
          message: getIntl().formatMessage(
            {
              id: 'message.upload.size',
            },
            {
              fileSize: 10,
            },
          ),
        });
        return false;
      } else {
        // return new Promise((resolve, reject) => {
        return new Promise((resolve, reject) => {
          //判断重复数据
          let phoneArr = [];
          let emailArr = [];

          let reader = new FileReader();
          reader.onload = function(e) {
            let fileObj = e.target.result;
            let workbook = XLSX.read(fileObj, {
              type: 'binary',
              cellDates: true, //设为true，将天数的时间戳转为时间格式
            });
            let first_worksheet = workbook.Sheets[workbook.SheetNames[0]];
            // 将excel转换成数组，判断data.length 就可以限制excel的行数
            let data = XLSX.utils.sheet_to_json(first_worksheet, {
              // 使用指定的字符串作为行对象中的键，如下key值顺序和excel表头的顺序要保持一致，如果根据key值获取数据为空，说明未填写数据
              header: [
                'name',
                'lastName',
                'emailAddress',
                'birthday',
                'sex',
                'telephonePrefixId',
                'telephone',
                'companyName',
                'post',
                'customerGroupIds',
                'channels',
                // 'mailingAddress',
                'orderAddress',
                'deliveryAddress',
                'otherAddress',
                'remark',
              ],
            });
            if (data && data.length < 2) {
              //如果少于2说明只有表头
              notification.error({
                message: '请在表格中添加客户信息后再批量导入',
              });
              reject(false);
            } else {
              //处理数据，不用管标题行
              for (let i = 1; i < data.length; i++) {
                let idx = i - 1;
                let lineNum = i + 1; //excel中的默认行号
                //获取每个数据，进行非空校验、格式校验或长度校验(姓名、分组和渠道是必须选择的，其他的都可以不填写；客户名称可以重复，但是手机号和邮箱不能重复)
                let name = data[i].name; //不能为空
                let lastName = data[i].lastName; //不能为空
                let emailAddress = data[i].emailAddress; //这个有格式要求
                let birthday = data[i].birthday; //这个有格式要求 yyyy-MM-dd
                let sex = data[i].sex;
                let telephonePrefixId = data[i].telephonePrefixId;
                let telephone = data[i].telephone; //这个有格式要求
                let companyName = data[i].companyName;
                let post = data[i].post;
                let customerGroupIds = data[i].customerGroupIds; //不能为空
                let channels = data[i].channels; //不能为空
                // let mailingAddress = data[i].mailingAddress;
                let orderAddress = data[i].orderAddress;
                let deliveryAddress = data[i].deliveryAddress;
                let otherAddress = data[i].otherAddress;
                let remark = data[i].remark;

                //合规性校验
                if (!name) {
                  notification.error({
                    //   message: '客户名称不能为空',
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.name.not.empty',
                      },
                      {
                        lineNum: lineNum,
                      },
                    ),
                  });
                  return;
                }
                if (name.trim().length == 0) {
                  notification.error({
                    //   message: '客户名称不能为空',
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.name.not.empty',
                      },
                      {
                        lineNum: lineNum,
                      },
                    ),
                  });
                  return;
                }
                //姓名格式,仅允许中、英文、数字及'.'
                let textPattern = /^[.0-9A-Za-z\u4e00-\u9fa5]+$/;
                if (!textPattern.test(name)) {
                  notification.error({
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.name.input.limit',
                      },
                      {
                        lineNum: lineNum,
                      },
                    ),
                  });
                  return;
                }
                if (name.trim().length > 200) {
                  notification.error({
                    // message: '客户名称最多200个字符',
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.name.length.limit',
                      },
                      {
                        lineNum: lineNum,
                      },
                    ),
                  });

                  return;
                }

                if (!lastName) {
                  notification.error({
                    //   message: '客户名称不能为空',
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.last.name.not.empty',
                      },
                      {
                        lineNum: lineNum,
                      },
                    ),
                  });
                  return;
                }
                if (lastName.trim().length == 0) {
                  notification.error({
                    //   message: '客户名称不能为空',
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.last.name.not.empty',
                      },
                      {
                        lineNum: lineNum,
                      },
                    ),
                  });
                  return;
                }
                //姓名格式,仅允许中、英文、数字及'.'
                // let textPattern = /^[.0-9A-Za-z\u4e00-\u9fa5]+$/;
                if (!textPattern.test(lastName)) {
                  notification.error({
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.last.name.input.limit',
                      },
                      {
                        lineNum: lineNum,
                      },
                    ),
                  });
                  return;
                }
                if (lastName.trim().length > 200) {
                  notification.error({
                    // message: '客户名称最多200个字符',
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.last.name.length.limit',
                      },
                      {
                        lineNum: lineNum,
                      },
                    ),
                  });

                  return;
                }
                //分组和渠道是必须选择的，如果是多个，要用#号分隔（先不用逗号了，避免excel填写时出现中英文逗号区分问题）
                if (!customerGroupIds) {
                  notification.error({
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.group.not.empty',
                      },
                      {
                        lineNum: lineNum,
                      },
                    ),
                  });
                  return;
                }
                if (customerGroupIds.trim().length == 0) {
                  notification.error({
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.group.not.empty',
                      },
                      {
                        lineNum: lineNum,
                      },
                    ),
                  });
                  return;
                }
                if (!channels) {
                  notification.error({
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.channel.not.empty',
                      },
                      {
                        lineNum: lineNum,
                      },
                    ),
                  });
                  return;
                }
                if (channels.trim().length == 0) {
                  notification.error({
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.channel.not.empty',
                      },
                      {
                        lineNum: lineNum,
                      },
                    ),
                  });
                  return;
                }
                // let emailPattern = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
                let emailPattern = /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
                if (emailAddress) {
                  if (emailPattern) {
                    if (!emailPattern.test(emailAddress)) {
                      notification.error({
                        message: '邮箱格式不正确',
                      });
                      return;
                    }
                  }
                  if (emailAddress.trim().length > 40) {
                    notification.error({
                      message: '邮箱最多40个字符',
                    });
                    return;
                  }
                  emailArr.push(emailAddress);
                }

                let phonePattern = /^[0-9]*$/;
                if (telephone) {
                  if (!phonePattern.test(telephone)) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.phone.input.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                  if (telephone.trim().length > 40) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.phone.length.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                  phoneArr.push(telephone);
                }
                //判断其他非必填项
                let birthdayFormat;
                if (birthday) {
                  //有格式要求yyyy-MM-dd
                  // 注意的点：xlsx将excel中的时间内容解析后，会小一天
                  // 如2020/11/3，xlsx会解析成  Mon Nov 02 2020 23:59:17 GMT+0800    小了43秒
                  // 当再用moment转换成日期时：
                  // Mon Nov 02 2020 23:59:17 GMT+0800  会转成2020/11/2     所以需要在moment转换后＋1天
                  if (
                    birthday === undefined ||
                    birthday === null ||
                    birthday === ''
                  ) {
                    birthdayFormat = null;
                  }
                  //非时间格式问题  返回Invalid date
                  let retFormat = moment(birthday).format('YYYY-MM-DD');
                  if (retFormat === 'Invalid date') {
                    //此处进行提醒
                    notification.error({
                      message: '生日格式不正确',
                    });
                    return;
                  }
                  birthdayFormat = moment(birthday)
                    .add(1, 'days')
                    .format('YYYY-MM-DD');
                }
                if (sex) {
                  if (sex != 0 && sex != 1 && sex != 2) {
                    //此处进行提醒
                    notification.error({
                      message: '性别信息只能用数字0、1、2进行标识',
                    });
                    return;
                  }
                }
                if (telephonePrefixId) {
                  let isTelephonePrefix = false;
                  for (let i = 0; i < telephonePrefixNumList.length; i++) {
                    let code = telephonePrefixNumList[i].code;
                    if (telephonePrefixId == code) {
                      isTelephonePrefix = true;
                      break;
                    }
                  }
                  if (!isTelephonePrefix) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.phone.prefix.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                }
                if (companyName) {
                  if (!textPattern.test(companyName)) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.company.input.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                  if (companyName.trim().length > 80) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.company.length.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                }
                if (post) {
                  if (!textPattern.test(post)) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.post.input.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                  if (post.trim().length > 80) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.post.length.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                }
                // if (mailingAddress) {
                //   if (!textPattern.test(mailingAddress)) {
                //     notification.error({
                //       message: getIntl().formatMessage(
                //         {
                //           id: 'customerInformation.mailingAddress.input.limit',
                //         },
                //         {
                //           lineNum: lineNum,
                //         },
                //       ),
                //     });
                //     return;
                //   }
                //   if (mailingAddress.trim().length > 200) {
                //     notification.error({
                //       message: getIntl().formatMessage(
                //         {
                //           id: 'customerInformation.mailingAddress.length.limit',
                //         },
                //         {
                //           lineNum: lineNum,
                //         },
                //       ),
                //     });
                //     return;
                //   }
                // }
                if (orderAddress) {
                  if (!textPattern.test(orderAddress)) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.orderAddress.input.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                  if (orderAddress.trim().length > 200) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.orderAddress.length.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                }
                if (deliveryAddress) {
                  if (!textPattern.test(deliveryAddress)) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.deliveryAddress.input.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                  if (deliveryAddress.trim().length > 200) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id:
                            'customerInformation.deliveryAddress.length.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                }
                if (otherAddress) {
                  if (!textPattern.test(otherAddress)) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.otherAddress.input.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                  if (otherAddress.trim().length > 200) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.otherAddress.length.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                }
                if (remark) {
                  if (!textPattern.test(remark)) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.remark.input.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                  if (remark.trim().length > 200) {
                    notification.error({
                      message: getIntl().formatMessage(
                        {
                          id: 'customerInformation.remark.length.limit',
                        },
                        {
                          lineNum: lineNum,
                        },
                      ),
                    });
                    return;
                  }
                }
                //封装数据，到后台进行保存
                dataList[idx] = {
                  name: name,
                  lastName: lastName,
                  emailAddress: emailAddress,
                  birthday: birthdayFormat,
                  sex: sex,
                  telephonePrefixId: telephonePrefixId,
                  telephone: telephone,
                  companyName: companyName,
                  post: post,
                  customerGroupIds: customerGroupIds,
                  channels: channels,
                  // mailingAddress: mailingAddress,
                  orderAddress: orderAddress,
                  deliveryAddress: deliveryAddress,
                  otherAddress: otherAddress,
                  remark: remark,
                };
              }

              //判断手机号或者邮箱是否重复
              phoneArr = phoneArr.sort();
              for (let i = 0; i < phoneArr.length; i++) {
                if (phoneArr[i] == phoneArr[i + 1]) {
                  let repeatPhone = phoneArr[i];
                  notification.error({
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.repeat.phone.tips',
                      },
                      {
                        repeatPhone: repeatPhone,
                      },
                    ),
                  });
                  return;
                }
              }

              emailArr = emailArr.sort();
              for (let i = 0; i < emailArr.length; i++) {
                if (emailArr[i] == emailArr[i + 1]) {
                  let repeatEmail = emailArr[i];
                  notification.error({
                    message: getIntl().formatMessage(
                      {
                        id: 'customerInformation.repeat.email.tips',
                      },
                      {
                        repeatEmail: repeatEmail,
                      },
                    ),
                  });
                  return;
                }
              }
              resolve(file);
            }
          };
          reader.readAsBinaryString(file);
        });
      }
      return false;
    },
  };

  exportCustomer = () => {
    const { filterCriteriaList, filterConditionList } = this.state;
    const entries = Object.entries(filterCriteriaList);
    let extInstVoList = entries.map((item, index) => {
      let propType = filterConditionList.filter(
        item => item.customerExtDefCode == 'name',
      )[0].propertyTypeId;
      let isSystemDefault = filterConditionList.filter(
        item => item.customerExtDefCode == 'name',
      )[0].isSystemDefault;
      return {
        propType: propType,
        isSystemDefault,
        code: item[0],
        value: item[1],
      };
    });
    this.props.dispatch({
      type: 'customerInformationManagement/updateExportLoading',
      payload: true,
    });
    const payload = {
      extInstVoList: extInstVoList,
    };
    this.props.dispatch({
      type: 'customerInformationManagement/exportCustomer',
      payload: payload,
      callback: response => {},
    });
  };

  onPressEnter = e => {
    const customer = this.state.customer;
    let valueList = this.state.tags;
    let value = e.target.value;

    if (valueList.includes(value) || value === '') {
      return;
    }

    if (value.length > 20) {
      message.warning(
        getIntl().formatMessage({
          id: 'hotWord.length',
        }),
      );
      return;
    }

    valueList.push(value);
    let stringValueList = valueList.join(',');
    customer['customerLabel'] = stringValueList;
    console.log(customer);
    this.setState({
      tags: valueList,
      customer: customer,
    });
    if (value.length <= 0) {
      this.setState({
        tagsFlag: true,
      });
    } else {
      this.setState({
        tagsFlag: false,
      });
    }
    this.setState({
      textInput: '',
    });
  };
  onChangeTextInput = e => {
    let value = e.target.value;
    this.setState({
      textInput: value,
    });
  };
  handleClose = removedTag => {
    const customer = this.state.customer;
    const tags = this.state.tags.filter(tag => tag !== removedTag);
    customer['inputTags'] = tags;
    this.setState({
      tags,
      customer,
    });
  };

  // 显示批量修改分组弹窗
  showModal = () => {
    console.log(this.state.groupIdList);
    const { selectedRowKeys } = this.state;
    if (selectedRowKeys.length > 0) {
      this.setState({
        changeGroupingModal: true,
      });
    } else {
      notification.error({
        message: getIntl().formatMessage({
          id: 'customerInformation.option.error',
        }),
      });
    }
  };

  // 保存批量修改分组
  handleOk = e => {
    this.setState({
      saveLoading: true,
    });
    this.props.dispatch({
      type: 'customerInformationManagement/queryBatchModifyGrouping',
      payload: {
        customerIdList: this.state.selectedRowKeys,
        groupIdList: this.state.groupIdList,
      },
      callback: res => {
        if (res.code === 200) {
          notification.success({
            message: res.data,
          });
          this.setState({
            changeGroupingModal: false,
            selectedRowKeys: [],
            groupIdList: [],
            saveLoading: false,
          });
          this.initData();
        } else {
          notification.error({
            message: res.msg,
          });
          this.setState({
            changeGroupingModal: false,
            selectedRowKeys: [],
            groupIdList: [],
            saveLoading: false,
          });
        }
      },
    });
  };

  // 取消修改批量分组
  handleCancel = e => {
    this.setState({
      changeGroupingModal: false,
      selectedRowKeys: [],
      groupIdList: [],
      customerGroupIds: {},
    });
  };

  // 批量选择table
  onSelectChange = selectedRowKeys => {
    this.setState({ selectedRowKeys });
  };
  // 批量修改分组弹窗的选择分组事件
  batchChangeGroupingSelect = (value, name) => {
    console.log(value);
    this.setState({
      groupIdList: value,
    });
  };

  // 显示批量导入弹窗
  showBatchImportModal = () => {
    this.setState({
      batchImportModal: true,
    });
  };

  // 保存批量导入
  // handleBatchImportModal = e => {
  //   this.customRequest
  //   this.setState({
  //     batchImportModal: false,
  //   });
  // };

  // 取消批量导入
  handleCancelBatchImportModal = e => {
    this.setState({
      batchImportModal: false,
    });
  };
  /**
   * 9.25 二期
   */
  // 查询客户属性定义扩展
  getCustomerExtList = () => {
    this.props.dispatch({
      type: 'customerInformationManagement/getCustomerExtList',
      callback: response => {
        if (response.code == 200) {
          this.getCustomerFiltersList();
          let filterCustomerExtList = response.data;
          this.getDynamicColumn();
          if (filterCustomerExtList) {
            this.setState({ filterCustomerExtList });
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 加载筛选器列表
  getCustomerFiltersList = () => {
    console.log('dsada');
    this.props.dispatch({
      type: 'customerInformationManagement/getCustomerFiltersList',
      callback: response => {
        if (response.code == 200) {
          let selectFilterList = response.data;
          let customerFilterName;
          let defaultFilter = [];
          let customerFilterId;
          if (selectFilterList && selectFilterList.length > 0) {
            for (let i = 0; i < selectFilterList.length; i++) {
              if (selectFilterList[i].defaultFilter == '1') {
                customerFilterName = selectFilterList[i].customerFilterName;
                defaultFilter = selectFilterList[i].defaultFilter;
                customerFilterId = selectFilterList[i].customerFilterId;
              } else {
                customerFilterName = selectFilterList[0].customerFilterName;
                defaultFilter = selectFilterList[0].defaultFilter;
                customerFilterId = selectFilterList[0].customerFilterId;
              }
            }
          }
          this.setState({
            defaultSelectValue: customerFilterName,
            customerFilterName: customerFilterName,
            defaultFilter: defaultFilter,
            customerFilterId: customerFilterId,
            selectFilterList: selectFilterList,
          });
          this.queryFilterCondition(customerFilterId);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 切换筛选器
  handleChangeFilter = value => {
    let { selectFilterList } = this.state;
    let defaultFilter = '';
    let customerFilterId = '';
    let customerFilterName = '';
    for (let i = 0; i < selectFilterList.length; i++) {
      if (value == selectFilterList[i].customerFilterId) {
        defaultFilter = selectFilterList[i].defaultFilter;
        customerFilterId = selectFilterList[i].customerFilterId;
        customerFilterName = selectFilterList[i].customerFilterName;
      }
    }
    this.formSearchFilterRef.current?.resetFields();
    this.setState({
      defaultSelectValue: value,
      defaultFilter,
      customerFilterId,
      customerFilterName,
      filterCriteriaList: {},
    });
    this.queryFilterCondition(value);
  };
  // 查询选择筛选器的筛选条件列表
  queryFilterCondition = filterId => {
    this.setState({
      saveFilterLoading: true,
    });
    let { filterCustomerExtList, selectFilterList } = this.state;
    let filterConditionList;
    let newFilterConditionList;
    let defaultValueArray = [];
    let dispose = [];
    if (selectFilterList && selectFilterList.length > 0) {
      //filterConditionList勾中的动态表单
      selectFilterList.forEach(item => {
        if (item.customerFilterId === filterId) {
          filterConditionList = item.filterConditionVoList;
        }
      });
      console.log(filterCustomerExtList, filterConditionList);
      //newFilterConditionList从大表单里找出勾中的动态表单的全部信息
      newFilterConditionList = filterCustomerExtList.filter(obj1 =>
        filterConditionList.some(
          obj2 => obj2.customerExtDefCode === obj1.customerExtDefCode,
        ),
      );
      for (let i = 0; i < newFilterConditionList.length; i++) {
        //需要特殊处理的字段存入dispose
        if (
          ['1004', '1006', '1008', '1009'].includes(
            newFilterConditionList[i].propertyTypeId,
          )
        )
          dispose.push({
            code: newFilterConditionList[i].customerExtDefCode,
            typeId: newFilterConditionList[i].propertyTypeId,
          });
        defaultValueArray.push(newFilterConditionList[i].customerExtDefId);
      }
    }
    //筛选器初始化赋值
    let initValue = {};
    filterConditionList?.forEach(item => {
      dispose?.forEach(itemSon => {
        if (item.customerExtDefCode === itemSon.code) {
          if (itemSon.typeId === '1008') {
            item.customerExtDefValue = item.customerExtDefValue
              ? moment(item.customerExtDefValue)
              : '';
            console.log(item.customerExtDefValue);
          } else if (
            itemSon.typeId === '1009' &&
            item.customerExtDefValue.length > 0
          ) {
            console.log(item.customerExtDefValue);
            item.customerExtDefValue = Array.isArray(item.customerExtDefValue)
              ? item.customerExtDefValue
              : [
                  moment(item.customerExtDefValue?.split(',')[0]),
                  moment(item.customerExtDefValue?.split(',')[1]),
                ];
          } else if (itemSon.typeId === '1004') {
            item.customerExtDefValue =
              item.customerExtDefValue.length > 0
                ? Array.isArray(item.customerExtDefValue)
                  ? item.customerExtDefValue
                  : item.customerExtDefValue?.split(',')
                : [];
          }
        }
      });
      initValue[item.customerExtDefCode] = item.customerExtDefValue;
    });
    this.formSearchFilterRef.current?.setFieldsValue(initValue);

    console.log(
      newFilterConditionList,
      filterConditionList,
      initValue,
      dispose,
    );
    this.setState({
      filterConditionList: newFilterConditionList,
      initFilterConditionList: newFilterConditionList,
      defaultValueArray: defaultValueArray,
      saveFilterLoading: false,
      filterCriteriaList: initValue,
    });
    this.filterCriteriaSearch();
  };
  // 重置当前筛选器
  resetFilter = () => {
    let {
      initFilterConditionList,
      defaultSelectValue,
      filterCriteriaList,
    } = this.state;
    let defaultValueArray = [];
    for (let i = 0; i < initFilterConditionList.length; i++) {
      defaultValueArray.push(initFilterConditionList[i].workRecordExtDefId);
    }
    // this.queryFilterCondition(defaultSelectValue);
    this.formSearchFilterRef.current?.resetFields();
    this.setState({
      filterConditionList: initFilterConditionList,
      defaultValueArray: defaultValueArray,
      filterCriteriaList: {},
    });
  };
  //更多字段事件
  handleOpenChange = () => {
    this.setState({
      openMore: true,
    });
  };
  // 渲染更多字段下拉弹窗
  moreFieldsContent = () => {
    let { filterCustomerExtList, defaultValueArray } = this.state;
    console.log(defaultValueArray);
    return;
  };
  // 取消更多字段添加
  hide = () => {
    let { filterConditionList } = this.state;
    let defaultValueArray = [];
    for (let i = 0; i < filterConditionList.length; i++) {
      defaultValueArray.push(filterConditionList[i].customerExtDefId);
    }
    this.setState({
      openMore: false,
      defaultValueArray: defaultValueArray,
    });
  };
  // 添加更多字段
  addMoreField = () => {
    console.log(this.state.temporarilyCheckList);
    this.setState({
      openMore: false,
      filterConditionList: this.state.temporarilyCheckList,
    });
  };
  /**
   * 保存表格自定义展示字段
   */
  addMoreFieldTable = () => {
    let data = this.state.temporarilyCheckListTable?.map(item => {
      return {
        code: item.customerExtDefCode,
        name: item.customerExtDefName,
      };
    });
    this.props.dispatch({
      type: 'customerInformationManagement/saveCustomerProps',
      payload: data,
      callback: res => {
        if (res.code === 200) {
          notification.success({
            message: getIntl().formatMessage({
              id: 'customer.ext.info.save.success',
            }),
          });
          this.getDynamicColumn();
        } else {
          notification.error({
            message: res.msg,
          });
        }
        this.setState({
          openColumnOptions: false,
        });
      },
    });
  };
  //另存筛选器弹窗
  showAddFilterModal = () => {
    this.setState({
      openAddFilter: true,
    });
  };
  //关闭筛选器弹窗
  closeAddFilterModal = () => {
    this.handleResetAddFilter({});
    this.setState({
      openAddFilter: false,
      assignWorkOrderList: {},
    });
  };
  // 保存筛选器（修改）
  saveFilter = () => {
    let {
      customerFilterId,
      customerFilterName,
      filterConditionList,
      defaultFilter,
      filterCriteriaList,
    } = this.state;
    const entries = Object.entries(filterCriteriaList);
    let conditionList = [];
    for (let i = 0; i < filterConditionList.length; i++) {
      let value = '';
      entries.forEach((item, index) => {
        if (item[0] === filterConditionList[i].customerExtDefCode) {
          value = item[1];
        }
      });
      console.log(filterConditionList[i], filterCriteriaList, value);
      let item = {
        customerExtDefCode: filterConditionList[i].customerExtDefCode,
        customerExtDefValue:
          filterConditionList[i].propertyTypeId == '1009' &&
          value.length > 0 &&
          Array.isArray(value)
            ? value[0] + ',' + value[1]
            : filterConditionList[i].propertyTypeId == '1004' &&
              Array.isArray(value)
            ? value.join(',')
            : value,
      };
      conditionList.push(item);
    }
    let params = {
      customerFilterName: customerFilterName,
      customerFilterId: customerFilterId,
      defaultFilter: defaultFilter,
      filterConditionVoList: conditionList,
    };
    this.props.dispatch({
      type: 'customerInformationManagement/updateCustomerFilters',
      payload: params,
      callback: response => {
        if (response.code == 200) {
          this.formSearchFilterRef.current?.resetFields();
          notification.success({
            message: response.msg,
          });
          this.getCustomerFiltersList();
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 另存为筛选器
  handleResetAddFilter = workOrderDetail => {
    let assignWorkOrderList = this.state.assignWorkOrderList;
    assignWorkOrderList.filterName = workOrderDetail.filterName;

    this.formAddFilterModalRef.current?.setFieldsValue(assignWorkOrderList);
    // this.formSearchFilterRef.current?.resetFields();
    this.setState({
      // filterCriteriaList: {},
    });
    return assignWorkOrderList;
  };

  //保存筛选器
  onFinishAddFilter = values => {
    this.setState({
      createFilterLoading: true,
    });
    let { defaultFilter, filterConditionList, filterCriteriaList } = this.state;
    let conditionList = [];
    const entries = Object.entries(filterCriteriaList);
    for (let i = 0; i < filterConditionList.length; i++) {
      let value = '';
      entries.forEach((item, index) => {
        if (item[0] === filterConditionList[i].customerExtDefCode) {
          value = item[1];
        }
      });
      let item = {
        customerExtDefCode: filterConditionList[i].customerExtDefCode,
        customerExtDefValue:
          filterConditionList[i].propertyTypeId == '1009' &&
          value.length > 0 &&
          Array.isArray(value)
            ? value[0] + ',' + value[1]
            : filterConditionList[i].propertyTypeId == '1004' &&
              Array.isArray(value)
            ? value.join(',')
            : value,
      };
      conditionList.push(item);
    }
    let params = {
      customerFilterName: values.filterName,
      filterConditionVoList: conditionList,
      defaultFilter: defaultFilter,
    };
    this.props.dispatch({
      type: 'customerInformationManagement/customerFilters',
      payload: params,
      callback: response => {
        this.setState({
          createFilterLoading: false,
        });
        if (response.code == 200) {
          notification.success({
            message: response.msg,
          });
          this.handleResetAddFilter({});
          this.setState({
            openAddFilter: false,
            assignWorkOrderList: {},
          });
          this.getCustomerFiltersList();
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 多选下拉框取值
  handleChangeMultipleSelectFilter = (value, name) => {
    let { filterCriteriaList } = this.state;
    console.log(name);
    if (typeof value.join === 'function') {
      filterCriteriaList[name] = value.join(',');
    } else {
      filterCriteriaList[name] = value;
    }
    this.setState({
      filterCriteriaList: filterCriteriaList,
    });
  };
  // 单选下拉框取值
  handleChangeSelectFilter = (value, name) => {
    let { filterCriteriaList } = this.state;
    filterCriteriaList[name] = value;
    this.setState({
      filterCriteriaList: filterCriteriaList,
    });
  };
  // input框取值
  handleAddInputChangeFilter = (e, index, type) => {
    console.log(e);
    let { filterCriteriaList } = this.state;
    filterCriteriaList[e.target.name] = e.target.value;
    this.setState({
      filterCriteriaList: filterCriteriaList,
    });
  };
  // 时间框取值
  handleAddDateChangeFilter = (e, value, type) => {
    let { filterCriteriaList } = this.state;
    filterCriteriaList[type] = value;
    this.setState({
      filterCriteriaList: filterCriteriaList,
    });
  };
  //查询
  filterCriteriaSearch = () => {
    this.setState({ tableLoading: true });
    const {
      pageSize,
      pageNum,
      filterCriteriaList,
      filterConditionList,
    } = this.state;
    const entries = Object.entries(filterCriteriaList);
    let pageInfo = {
      pageNum: pageNum,
      pageSize: pageSize,
    };
    console.log(entries, filterConditionList);
    let extInstVoList = entries.map((item, index) => {
      console.log(
        filterConditionList.filter(
          item1 => item1.customerExtDefCode == item[0],
        )[0],
      );
      let valueDispose = '';
      let propType = filterConditionList.filter(
        item1 => item1.customerExtDefCode == item[0],
      )[0]?.propertyTypeId;

      let isSystemDefault = filterConditionList.filter(
        item1 => item1.customerExtDefCode == item[0],
      )[0]?.isSystemDefault;
      console.log(item[1]);
      if (propType == '1008') {
        valueDispose = item[1]
          ? typeof item[1] === 'string'
            ? item[1]
            : item[1].format('YYYY-MM-DD')
          : item[1];
      } else if (propType == '1009') {
        valueDispose = [
          item[1][0]
            ? typeof item[1][0] === 'string'
              ? item[1][0]
              : item[1][0].format('YYYY-MM-DD')
            : item[1][0],
          item[1][1]
            ? typeof item[1][1] === 'string'
              ? item[1][1]
              : item[1][1].format('YYYY-MM-DD')
            : item[1][1],
        ];
      }
      return {
        propType: propType,
        isSystemDefault,
        code: item[0],
        value: valueDispose ? valueDispose : item[1],
      };
    });
    let payload = {
      pageInfo: pageInfo,
      data: {
        extInstVoList: extInstVoList,
      },
    };
    this.props.dispatch({
      type: 'customerInformationManagement/customerinfoList',
      payload: payload,
      callback: res => {
        if (res.code === 200) {
          let data = res.data.rows;
          data.forEach(item => {
            item.extInstVoList?.map(itemSon => {
              item[itemSon.code] = itemSon.value;
            });
          });
          this.setState({
            dataSource: data,
            total: res.data.total,
          });
        }
        this.setState({
          tableLoading: false,
        });
      },
    });
    console.log(
      this.state.filterConditionList,
      this.state.filterCriteriaList,
      extInstVoList,
    );
  };
  // 列选项事件
  handleOpenColumnOptions = () => {
    this.setState({
      openColumnOptions: true,
    });
  };
  hideColumnOptions = () => {
    this.setState({
      openColumnOptions: false,
    });
  };

  render() {
    const {
      total,
      pageSize,
      pageNum,
      openAddFilter,
      addCustomerInformationStatus,
      editorCustomerInformationStatus,
      contactCustomerStatus,
      locale,
      mediaDefList,
      openColumnOptions,
      customerExtList,
      areaNumList,
      contactCustomer,
      workRecordTotal,
      workRecordPageNum,
      workRecordPageSize,
      defaultSelectValue,
      changeGroupingModal,
      selectFilterList,
      selectedRowKeys,
      batchImportModal,
      uploading,
      fileList,
      customerGradeList,
      saveFilterLoading,
      filterConditionList,
      openMore,
      filterCustomerExtList,
      defaultValueArray,
      dynamicColumn,
    } = this.state;
    const rowSelection = {
      selectedRowKeys,
      onChange: this.onSelectChange,
    };

    return (
      <div className={styles.customerInformationContent}>
        {/* <div
          className={styles.customerInformationSelectContent}
          style={{
            display:
              addCustomerInformationStatus ||
              editorCustomerInformationStatus ||
              contactCustomerStatus
                ? 'none'
                : 'block',
          }}
        >
          客户资料列表查询条件   9.25修改
           <div className={styles.selectItem}>
            <Tooltip
              title={getIntl().formatMessage({
                id: 'customerInformation.customerName',
              })}
            >
              <label>
                <FormattedMessage
                  id="customerInformation.customerName"
                  defaultMessage="客户名称："
                />
              </label>
            </Tooltip>
            <Input
              placeholder={getIntl().formatMessage({
                id: 'customerInformation.customerName.placeholder',
                defaultValue: '请输入客户名称，回车搜索',
              })}
              name="customerName"
              onChange={this.handleChange}
              onPressEnter={this.initData}
            />
            <Tooltip
              title={getIntl().formatMessage({
                id: 'customerInformation.emailAddress',
              })}
            >
              <label>
                <FormattedMessage
                  id="customerInformation.emailAddress"
                  defaultMessage="邮箱地址："
                />
              </label>
            </Tooltip>
            <Input
              placeholder={getIntl().formatMessage({
                id: 'customerInformation.emailAddress.placeholder',
                defaultValue: '请输入邮箱地址，回车搜索',
              })}
              name="customerEmail"
              onChange={this.handleChange}
              onPressEnter={this.initData}
            />
            <Tooltip
              title={getIntl().formatMessage({
                id: 'customerInformation.customerBusinessName',
              })}
            >
              <label>
                <FormattedMessage
                  id="customerInformation.customerBusinessName"
                  defaultMessage="客户企业名称："
                />
              </label>
            </Tooltip>
            <Input
              placeholder={getIntl().formatMessage({
                id: 'customerInformation.customerBusinessName.placeholder',
                defaultValue: '请输入客户企业名称',
              })}
              name="companyName"
              onChange={this.handleChange}
              onPressEnter={this.initData}
            />
          </div>
          <div className={styles.selectItem}>
            <Tooltip
              title={getIntl().formatMessage({
                id: 'customerInformation.sourceChannel',
              })}
            >
              <label>
                <FormattedMessage
                  id="customerInformation.sourceChannel"
                  defaultMessage="来源渠道："
                />
              </label>
            </Tooltip>
            <Select
              mode="multiple"
              allowClear
              showArrow
              placeholder="请选项来源渠道"
              fieldNames={{
                label: 'name',
                value: 'channelId',
                key: 'channelId',
              }}
              // value={}
              options={this.state.channelOptions}
              onChange={this.handleChannelsSelect}
            />
            <Search
              placeholder={getIntl().formatMessage({
                id: 'customerInformation.searchPlaceholder',
                defaultValue: '请输入客户资料信息，回车搜索',
              })}
              name="customerInfo"
              onChange={this.handleChange}
              onSearch={this.onSearch}
            />
          </div>
        </div> */}
        {/* 筛选器 */}
        <div
          className={styles.selectContent}
          style={{
            display:
              addCustomerInformationStatus ||
              editorCustomerInformationStatus ||
              contactCustomerStatus
                ? 'none'
                : 'block',
          }}
        >
          <div className={styles.selectTitleContent}>
            <div className={styles.line}></div>
            <Select
              allowClear={true}
              value={defaultSelectValue}
              popupClassName="selectFilterContent"
              style={{
                width: 120,
              }}
              showSearch
              filterOption={(inputValue, option) =>
                option.customerFilterName
                  .toLowerCase()
                  .indexOf(inputValue.toLowerCase()) >= 0
              }
              onChange={this.handleChangeFilter}
              fieldNames={{
                label: 'customerFilterName',
                value: 'customerFilterId',
                key: 'customerFilterId',
              }}
              options={selectFilterList}
            />
            <div className={styles.filterBtnContent}>
              <Tooltip placement="top" title={'另存为'}>
                <div
                  onClick={this.showAddFilterModal}
                  className={styles.saveAs}
                ></div>
              </Tooltip>
              <Tooltip placement="top" title={'保存'}>
                <div
                  onClick={this.saveFilter}
                  style={{
                    display: this.state.defaultFilter == '1' ? 'none' : 'block',
                  }}
                  className={styles.save}
                ></div>
              </Tooltip>
              <Tooltip placement="top" title={'重置'}>
                <div
                  onClick={this.resetFilter}
                  className={styles.resetting}
                ></div>
              </Tooltip>
            </div>
          </div>
          <Spin spinning={saveFilterLoading}>
            <div className={styles.selectItemContent}>
              <Form
                name="basic"
                autoComplete="off"
                labelAlign="left"
                ref={this.formSearchFilterRef}
              >
                {filterConditionList?.map((item, index) => {
                  if (item.propertyTypeId == '1001') {
                    // 1001,单行输入框
                    return (
                      <InputDiv
                        key={index}
                        item={item}
                        prompt={item.prompt}
                        handleAddInputChangeFilter={
                          this.handleAddInputChangeFilter
                        }
                      />
                    );
                  } else if (item.propertyTypeId == '1002') {
                    // 1002,多行输入框
                    return (
                      <InputTextareaDiv
                        key={index}
                        item={item}
                        prompt={item.prompt}
                        handleAddInputChangeFilter={
                          this.handleAddInputChangeFilter
                        }
                      />
                    );
                  } else if (item.propertyTypeId == '1003') {
                    // 1003,单选下拉框
                    return (
                      <SelectDiv
                        item={item}
                        key={index}
                        prompt={item.prompt}
                        handleChangeSelectFilter={this.handleChangeSelectFilter}
                      />
                    );
                  } else if (item.propertyTypeId == '1004') {
                    // 1004,多选下拉框
                    return (
                      <SelectMultipleDiv
                        item={item}
                        key={index}
                        prompt={item.prompt}
                        handleChangeMultipleSelectFilter={
                          this.handleChangeMultipleSelectFilter
                        }
                      />
                    );
                  } else if (item.propertyTypeId == '1005') {
                    // 1005,单选框
                    return (
                      <RadioDiv
                        item={item}
                        key={index}
                        prompt={item.prompt}
                        handleChangeSelectFilter={this.handleChangeSelectFilter}
                      />
                    );
                  } else if (item.propertyTypeId == '1006') {
                    // 1006,多选框
                    return (
                      <CheckboxDiv
                        item={item}
                        key={index}
                        name={
                          item.customerExtDefCode +
                          ':' +
                          item.customerExtDefName
                        }
                        prompt={item.prompt}
                        // handleAddInputChangeFilter={e =>
                        //   this.handleAddInputChange(e, index, 'ext')
                        // }
                        handleAddInputChangeFilter={
                          this.handleAddInputChangeFilter
                        }
                      />
                    );
                  } else if (item.propertyTypeId == '1007') {
                    // 1007,开关
                    return (
                      <SwitchDiv
                        item={item}
                        key={index}
                        handleAddInputChangeFilter={
                          this.handleAddInputChangeFilter
                        }
                      />
                    );
                  } else if (item.propertyTypeId == '1008') {
                    // 1008,时间选择
                    return (
                      <DatePickerDiv
                        item={item}
                        key={index}
                        handleAddInputChangeFilter={
                          this.handleAddDateChangeFilter
                        }
                      />
                    );
                  } else if (item.propertyTypeId == '1009') {
                    // 1009,时间范围选择
                    return (
                      <RangePickerDiv
                        item={item}
                        key={index}
                        handleAddInputChangeFilter={
                          this.handleAddDateChangeFilter
                        }
                      />
                    );
                  }
                })}
              </Form>
            </div>
          </Spin>
          <div className={styles.selectBtnContent}>
            <Popover
              content={() => {
                return (
                  <div>
                    <div className="moreFieldSearch">
                      <Search
                        placeholder={getIntl().formatMessage({
                          id: 'work.order.management.fuzzy.query',
                          defaultMessage: '模糊查询',
                        })}
                        onSearch={this.onSearch}
                      />
                    </div>
                    <div className="moreFieldList">
                      <Checkbox.Group
                        value={defaultValueArray}
                        onChange={this.onChangeCheckbox}
                      >
                        {filterCustomerExtList?.map(item => {
                          return (
                            <Checkbox
                              value={item.customerExtDefId}
                              key={item.customerExtDefId}
                            >
                              {item.customerExtDefName}
                            </Checkbox>
                          );
                        })}
                      </Checkbox.Group>
                    </div>
                    <div className="moreFieldBtn">
                      <Button onClick={this.hide}>
                        <FormattedMessage id="work.order.management.btn.cancel" />
                      </Button>
                      <Button type="primary" onClick={this.addMoreField}>
                        <FormattedMessage id="work.order.management.btn.sure" />
                      </Button>
                    </div>
                  </div>
                );
              }}
              overlayClassName="moreFieldsContentPopover"
              title={null}
              trigger="click"
              open={openMore}
              onOpenChange={this.handleOpenChange}
            >
              <Button icon={<PlusOutlined />}>
                <FormattedMessage id="work.order.management.more.fields" />
              </Button>
            </Popover>
            <Button
              type="primary"
              icon={<SearchOutlined />}
              onClick={this.filterCriteriaSearch}
            >
              <FormattedMessage id="work.order.management.search" />
            </Button>
          </div>
        </div>
        {/* 表格 */}
        <div
          className={styles.tableContent}
          style={{
            display:
              addCustomerInformationStatus ||
              editorCustomerInformationStatus ||
              contactCustomerStatus
                ? 'none'
                : 'block',
          }}
        >
          <div className={styles.tableTitle}>
            <p>
              <FormattedMessage
                id="customerInformation.customerInformation1"
                defaultMessage="客户资料"
              />
            </p>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={this.addCustomerInformation}
            >
              <FormattedMessage
                id="customerInformation.customerInformation"
                defaultMessage=" 客户资料"
              />
            </Button>
            <Button
              className={styles.exportBtn}
              icon={<ExportIcon />}
              onClick={this.exportCustomer}
              loading={this.props.exportLoading}
            >
              <FormattedMessage
                id="customerInformation.export"
                defaultMessage="导出"
              />
            </Button>
            <Button
              className={styles.importBtn}
              icon={<ImportIcon />}
              loading={this.props.exportLoading}
              onClick={this.showBatchImportModal}
            >
              <FormattedMessage
                id="customerInformation.batchImport"
                defaultMessage="批量导入"
              />
            </Button>
            <Button
              className={styles.importBtn}
              onClick={this.showModal}
              icon={<EditorGroupIcon />}
            >
              <FormattedMessage
                id="customerInformation.modal.batch.change.grouping"
                defaultMessage="批量修改分组"
              />
            </Button>
            <Popover
              content={() => {
                return (
                  <div>
                    <div className="moreFieldSearch">
                      <Search
                        placeholder={getIntl().formatMessage({
                          id: 'work.order.management.fuzzy.query',
                          defaultMessage: '模糊查询',
                        })}
                        onSearch={this.onSearch1}
                      />
                    </div>
                    <div className="moreFieldList">
                      <Checkbox.Group
                        value={this.state.defaultValueArrayTable}
                        onChange={this.onChangeCheckboxTable}
                      >
                        {filterCustomerExtList?.map(item => {
                          return (
                            <Checkbox
                              value={item.customerExtDefId}
                              key={item.customerExtDefId}
                            >
                              {item.customerExtDefName}
                            </Checkbox>
                          );
                        })}
                      </Checkbox.Group>
                    </div>
                    <div className="moreFieldBtn">
                      <Button onClick={this.hideColumnOptions}>
                        <FormattedMessage id="work.order.management.btn.cancel" />
                      </Button>
                      <Button type="primary" onClick={this.addMoreFieldTable}>
                        <FormattedMessage id="work.order.management.btn.sure" />
                      </Button>
                    </div>
                  </div>
                );
              }}
              overlayClassName="moreFieldsContentPopover"
              title={null}
              trigger="click"
              open={openColumnOptions}
              onOpenChange={this.handleOpenColumnOptions}
            >
              <Button icon={<ColumnOptionsIcon />} className={styles.importBtn}>
                <FormattedMessage id="work.order.management.column" />
              </Button>
            </Popover>
          </div>
          <div className={styles.detailTableContent}>
            <Table
              rowSelection={rowSelection}
              dataSource={this.state.dataSource}
              columns={dynamicColumn}
              rowKey={record => record.customerId}
              loading={this.state.tableLoading}
              onChange={this.handleTableChange}
              scroll={{
                x: 1300,
                y: 'calc(100vh - 450px)',
              }}
              pagination={{
                total: total,
                pageSize: pageSize,
                current: pageNum,
                showSizeChanger: true,
                pageSizeOptions: [5, 10, 20, 50, 100],
                showTotal: total => (
                  <FormattedMessage
                    id="studentManagement.altogether"
                    defaultMessage={`共 ${total} 条`}
                    values={{ total }}
                  />
                ),
              }}
            />
          </div>
        </div>
        {/*客户资料表单*/}
        <div
          className={styles.addCustomerInformation}
          style={{ display: addCustomerInformationStatus ? 'block' : 'none' }}
          // style={{ display: 'block'}}
        >
          <Form
            name="basic"
            autoComplete="off"
            labelAlign="right"
            scrollToFirstError={true}
            // initialValues={customerDetail}
            ref={this.formRef}
            disabled={true}
            // onFinish={this.saveCustomerInformation}
          >
            <div className={styles.basicInformationContent}>
              <div className={styles.title}>
                <div className={styles.line}></div>
                <span>
                  <FormattedMessage
                    id="customerInformation.add.basicInformation.title"
                    defaultMessage="基本信息"
                  />
                </span>
              </div>
              <div className={styles.formContent}>
                <Row className={styles.lengthItem} gutter={24}>
                  <Col span={24}>
                    <Form.Item
                      label={getIntl().formatMessage({
                        id: 'customerInformation.sourceChannel',
                        defaultValue: '来源渠道',
                      })}
                      name="channels"
                      id={'channels'}
                      rules={[
                        {
                          required: true,
                          message: (
                            <FormattedMessage
                              id="customerInformation.add.basicInformation.sourceChannel.required"
                              defaultValue="请选择来源渠道"
                            />
                          ),
                        },
                      ]}
                    >
                      <Select
                        placeholder={getIntl().formatMessage({
                          id:
                            'customerInformation.add.basicInformation.sourceChannel.placeholder',
                          defaultValue: '请选择来源渠道',
                        })}
                        mode="multiple"
                        allowClear
                        showArrow
                        showSearch
                        filterOption={(inputValue, option) =>
                          option.name
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) >= 0
                        }
                        fieldNames={{
                          label: 'name',
                          value: 'channelId',
                          key: 'channelId',
                        }}
                        options={this.state.channelOptions}
                        onChange={value =>
                          this.handleAddSelectChange(value, 'channels')
                        }
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <Row className={styles.lengthItem} gutter={24}>
                  <Col span={24}>
                    <Form.Item
                      label={getIntl().formatMessage({
                        id:
                          'customerInformation.add.basicInformation.selectiveGrouping',
                        defaultValue: '选择分组',
                      })}
                      id={'customerGroupId'}
                      name="customerGroupId"
                      rules={[
                        {
                          required: true,
                          message: (
                            <FormattedMessage
                              id="customerInformation.add.basicInformation.selectiveGrouping.required"
                              defaultValue="请选择分组"
                            />
                          ),
                        },
                      ]}
                    >
                      <Select
                        mode="multiple"
                        allowClear
                        placeholder={getIntl().formatMessage({
                          id:
                            'customerInformation.add.basicInformation.selectiveGrouping.placeholder',
                          defaultValue: '请选择分组',
                        })}
                        showArrow
                        showSearch
                        fieldNames={{
                          label: 'customerGroupName',
                          value: 'customerGroupId',
                          key: 'customerGroupId',
                        }}
                        filterOption={(inputValue, option) =>
                          option.customerGroupName
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) >= 0
                        }
                        options={this.state.groupOptions}
                        onChange={value =>
                          this.handleAddSelectChange(value, 'customerGroupIds')
                        }
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row className={styles.smallItem} gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      label={getIntl().formatMessage({
                        id: 'customerInformation.add.basicInformation.name',
                        defaultValue: '姓名',
                      })}
                      name="name"
                      id={'name'}
                      rules={[
                        {
                          required: true,
                          message: (
                            <FormattedMessage
                              id="customerInformation.add.basicInformation.name.required"
                              defaultMessage="请输入姓名"
                            />
                          ),
                        },
                        {
                          pattern: '^[\u4e00-\u9fa5a-zA-Z0-9\\s-_\\.]+$',
                          message: (
                            <FormattedMessage
                              id="customerInformation.add.basicInformation.name.pattern"
                              defaultMessage="姓名格式不正确，仅允许中、英文、数字、空格、横线、下划线及'.'"
                            />
                          ),
                        },
                        {
                          max: 200,
                          message: (
                            <FormattedMessage
                              id="customerInformation.add.basicInformation.name.maxlength"
                              defaultMessage="姓名最多200个字符"
                            />
                          ),
                        },
                      ]}
                    >
                      <Input
                        placeholder={getIntl().formatMessage({
                          id:
                            'customerInformation.add.basicInformation.name.placeholder',
                          defaultValue: '请选输入姓名',
                        })}
                        name={'name'}
                        onChange={this.handleAddInputChange}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label={getIntl().formatMessage({
                        id: 'customerInformation.add.basicInformation.sex',
                        defaultValue: '性别',
                      })}
                      name="sex"
                      id={'sex'}
                    >
                      <Select
                        allowClear={true}
                        placeholder={getIntl().formatMessage({
                          id:
                            'customerInformation.add.basicInformation.sex.placeholder',
                          defaultValue: '请选择性别',
                        })}
                        options={[
                          {
                            value: 1,
                            label: getIntl().formatMessage({
                              id: 'contact.customers.basic.information.sex1',
                              defaultValue: '男',
                            }),
                          },
                          {
                            value: 0,
                            label: getIntl().formatMessage({
                              id: 'contact.customers.basic.information.sex0',
                              defaultValue: '女',
                            }),
                          },
                          {
                            value: 2,
                            label: getIntl().formatMessage({
                              id: 'contact.customers.basic.information.sex2',
                              defaultValue: '其他',
                            }),
                          },
                        ]}
                        onChange={value =>
                          this.handleAddSelectChange(value, 'sex')
                        }
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <Row className={styles.smallItem} gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      label={getIntl().formatMessage({
                        id:
                          'customerInformation.add.basicInformation.dateBirth',
                        defaultValue: '出生日期',
                      })}
                      name="birthday"
                      id={'birthday'}
                    >
                      <DatePicker
                        locale={locale}
                        name={'birthday'}
                        onChange={(a, b) =>
                          this.handleAddDateChange(a, b, 'birthday', 'noExt')
                        }
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label={getIntl().formatMessage({
                        id:
                          'customerInformation.add.basicInformation.emailAddress',
                        defaultValue: '邮箱地址',
                      })}
                      rules={[
                        {
                          min: 1,
                          max: 40,
                          type: 'email',
                          message: (
                            <FormattedMessage
                              id="customerInformation.add.basicInformation.emailAddress.pattern"
                              defaultValue="邮箱地址格式不正确"
                            />
                          ),
                        },
                      ]}
                      name={'emailAddress'}
                    >
                      <Input
                        placeholder={getIntl().formatMessage({
                          id:
                            'customerInformation.add.basicInformation.emailAddress.placeholder',
                          defaultValue: '请输入邮箱地址，回车搜索',
                        })}
                        name={'emailAddress'}
                        onChange={this.handleAddInputChange}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <Row className={styles.smallItem} gutter={24}>
                  <Col span={6} style={{ maxWidth: '25%' }}>
                    <Form.Item
                      label={getIntl().formatMessage({
                        id:
                          'customerInformation.add.basicInformation.contactNumber',
                        defaultValue: '联系电话',
                      })}
                      name={'telephonePrefixId'}
                      id={'telephonePrefixId'}
                    >
                      <Select
                        // style={{ width: '22%', float: 'left' }}
                        placeholder={getIntl().formatMessage({
                          id:
                            'customerInformation.add.basicInformation.Address.placeholder',
                          defaultValue: '请选择地域',
                        })}
                        showSearch
                        allowClear={true}
                        dropdownMatchSelectWidth={false}
                        popupClassName="select-drop-down-platform"
                        onChange={value =>
                          this.handleAddSelectChange(value, 'telephonePrefixId')
                        }
                        style={{ width: '100%' }}
                      >
                        {areaNumList?.map((item, index) => {
                          return (
                            <Option value={item.code} key={index}>
                              {item.city + '(' + item.code + ')'}
                            </Option>
                          );
                        })}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      rules={[
                        {
                          pattern: '^[0-9]*$',
                          message: (
                            <FormattedMessage
                              id="customerInformation.add.basicInformation.contactNumber.pattern"
                              defaultValue="联系电话格式不正确,仅允许数字"
                            />
                          ),
                        },
                        {
                          max: 40,
                          message: (
                            <FormattedMessage
                              id="customerInformation.add.basicInformation.contactNumber.maxlength"
                              defaultValue="联系电话最多40个字符"
                            />
                          ),
                        },
                      ]}
                      name={'telephone'}
                      id={'telephone'}
                    >
                      <Input
                        placeholder={getIntl().formatMessage({
                          id:
                            'customerInformation.add.basicInformation.contactNumber.placeholder',
                          defaultValue: '请输入联系电话',
                        })}
                        name={'telephone'}
                        onChange={this.handleAddInputChange}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12} style={{ paddingLeft: '12px' }}>
                    <Form.Item
                      label={getIntl().formatMessage({
                        id:
                          'customerInformation.add.basicInformation.companyName',
                        defaultValue: '公司名称',
                      })}
                      name={'companyName'}
                      rules={[
                        {
                          pattern: '^[.0-9A-Za-z\u4e00-\u9fa5]+$',
                          message: (
                            <FormattedMessage
                              id="customerInformation.add.basicInformation.pattern"
                              defaultValue="格式不正确,仅允许中、英文、数字及'.'"
                            />
                          ),
                        },
                        {
                          max: 80,
                          message: (
                            <FormattedMessage
                              id="customerInformation.add.basicInformation.maxlength"
                              defaultValue="长度不能超过80个字符"
                            />
                          ),
                        },
                      ]}
                    >
                      <Input
                        placeholder={getIntl().formatMessage({
                          id:
                            'customerInformation.add.basicInformation.companyName.placeholder',
                          defaultValue: '请输入公司名称',
                        })}
                        name={'companyName'}
                        onChange={this.handleAddInputChange}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <Row className={styles.smallItem} gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      label={getIntl().formatMessage({
                        id: 'customerInformation.add.basicInformation.position',
                        defaultValue: '职务',
                      })}
                      name={'post'}
                      rules={[
                        {
                          pattern: '^[.0-9A-Za-z\u4e00-\u9fa5]+$',
                          message: (
                            <FormattedMessage
                              id="customerInformation.add.basicInformation.pattern"
                              defaultValue="格式不正确,仅允许中、英文、数字及'.'"
                            />
                          ),
                        },
                        {
                          max: 80,
                          message: (
                            <FormattedMessage
                              id="customerInformation.add.basicInformation.maxlength"
                              defaultValue="长度不能超过80个字符"
                            />
                          ),
                        },
                      ]}
                    >
                      <Input
                        placeholder={getIntl().formatMessage({
                          id:
                            'customerInformation.add.basicInformation.position.placeholder',
                          defaultValue: '请输入职务',
                        })}
                        name={'post'}
                        onChange={this.handleAddInputChange}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label={getIntl().formatMessage({
                        id:
                          'customerInformation.add.basicInformation.customerLevel',
                        defaultValue: '客户等级',
                      })}
                      id={'gradeId'}
                      name="gradeId"
                      rules={[
                        {
                          required: false,
                          message: (
                            <FormattedMessage
                              id="customerInformation.add.basicInformation.customerLevel.required"
                              defaultValue="请选择客户等级"
                            />
                          ),
                        },
                      ]}
                    >
                      <Select
                        placeholder={getIntl().formatMessage({
                          id:
                            'customerInformation.add.basicInformation.customerLevel.placeholder',
                          defaultValue: '请选择客户等级',
                        })}
                        allowClear={true}
                        showSearch
                        onChange={value =>
                          this.handleAddSelectChange(value, 'gradeId')
                        }
                        filterOption={(inputValue, option) =>
                          option.gradeName
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) >= 0
                        }
                      >
                        {customerGradeList?.map((item, index) => {
                          return (
                            <Option value={item.gradeId} key={index}>
                              {item.gradeName}
                            </Option>
                          );
                        })}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>

                <Row
                  className={styles.lengthItem}
                  gutter={24}
                  style={{ height: '80px' }}
                >
                  <Col span={24}>
                    <Form.Item
                      label={getIntl().formatMessage({
                        id: 'customerInformation.add.basicInformation.tags',
                        defaultValue: '标签',
                      })}
                      name={'inputTags'}
                      rules={[
                        {
                          pattern: '^[.0-9A-Za-z\u4e00-\u9fa5]+$',
                          message: (
                            <FormattedMessage
                              id="customerInformation.add.basicInformation.pattern"
                              defaultValue="格式不正确,仅允许中、英文、数字及'.'"
                            />
                          ),
                        },
                        {
                          max: 2000,
                          message: (
                            <FormattedMessage
                              id="customerInformation.add.basicInformation.maxlength3"
                              defaultValue="长度不能超过2000个字符"
                            />
                          ),
                        },
                      ]}
                    >
                      <div className={styles.inputTags}>
                        <Input
                          onPressEnter={this.onPressEnter}
                          placeholder={getIntl().formatMessage({
                            id:
                              'customerInformation.add.basicInformation.tags.placeholder',
                            defaultValue: '请输入标签并回车',
                          })}
                          name={'inputTags'}
                          onChange={this.onChangeTextInput}
                          value={this.state.textInput}
                          style={{ float: 'left' }}
                        />
                        <div className={styles.tagsContent}>
                          {this.state.tags?.map((item, index) => {
                            return (
                              <Tag
                                key={item}
                                closable
                                onClose={() => this.handleClose(item)}
                              >
                                {item}
                              </Tag>
                            );
                          })}
                        </div>
                      </div>
                    </Form.Item>
                  </Col>
                </Row>
                {/*<Row className={styles.lengthItem} gutter={24}>*/}
                {/*  <Col span={24}>*/}
                {/*    <Form.Item*/}
                {/*      label={getIntl().formatMessage({*/}
                {/*        id:*/}
                {/*          'customerInformation.add.basicInformation.mailingAddress',*/}
                {/*        defaultValue: '邮寄地址',*/}
                {/*      })}*/}
                {/*      name={'mailingAddress'}*/}
                {/*      rules={[*/}
                {/*        {*/}
                {/*          pattern: '^[.0-9A-Za-z\u4e00-\u9fa5]+$',*/}
                {/*          message: (*/}
                {/*            <FormattedMessage*/}
                {/*              id="customerInformation.add.basicInformation.pattern"*/}
                {/*              defaultValue="格式不正确,仅允许中、英文、数字及'.'"*/}
                {/*            />*/}
                {/*          ),*/}
                {/*        },*/}
                {/*        {*/}
                {/*          max: 200,*/}
                {/*          message: (*/}
                {/*            <FormattedMessage*/}
                {/*              id="customerInformation.add.basicInformation.maxlength2"*/}
                {/*              defaultValue="长度不能超过200个字符"*/}
                {/*            />*/}
                {/*          ),*/}
                {/*        },*/}
                {/*      ]}*/}
                {/*    >*/}
                {/*      <TextArea*/}
                {/*        // showCount*/}
                {/*        maxLength={200}*/}
                {/*        autoSize={{*/}
                {/*          minRows: 1,*/}
                {/*          maxRows: 1,*/}
                {/*        }}*/}
                {/*        placeholder={getIntl().formatMessage({*/}
                {/*          id:*/}
                {/*            'customerInformation.add.basicInformation.mailingAddress.placeholder',*/}
                {/*          defaultValue: '请输入邮寄地址',*/}
                {/*        })}*/}
                {/*        name={'mailingAddress'}*/}
                {/*        onChange={this.handleAddInputChange}*/}
                {/*      />*/}
                {/*    </Form.Item>*/}
                {/*  </Col>*/}
                {/*</Row>*/}
                <Row className={styles.lengthItem} gutter={24}>
                  <Col span={24}>
                    <Form.Item
                      label={getIntl().formatMessage({
                        id:
                          'customerInformation.add.basicInformation.orderAddress',
                        defaultValue: '账单地址',
                      })}
                      name={'orderAddress'}
                      rules={[
                        {
                          pattern: '^[.0-9A-Za-z\u4e00-\u9fa5]+$',
                          message: (
                            <FormattedMessage
                              id="customerInformation.add.basicInformation.pattern"
                              defaultValue="格式不正确,仅允许中、英文、数字及'.'"
                            />
                          ),
                        },
                        {
                          max: 200,
                          message: (
                            <FormattedMessage
                              id="customerInformation.add.basicInformation.maxlength2"
                              defaultValue="长度不能超过200个字符"
                            />
                          ),
                        },
                      ]}
                    >
                      <TextArea
                        // showCount
                        maxLength={200}
                        autoSize={{
                          minRows: 1,
                          maxRows: 1,
                        }}
                        name={'orderAddress'}
                        onChange={this.handleAddInputChange}
                        placeholder={getIntl().formatMessage({
                          id:
                            'customerInformation.add.basicInformation.orderAddress.placeholder',
                          defaultValue: '请输入账单地址',
                        })}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                {/*<Row className={styles.lengthItem} gutter={24}>*/}
                {/*  <Col span={24}>*/}
                {/*    /!*<label>配送地址:</label>*!/*/}
                {/*    <Form.Item*/}
                {/*      label={getIntl().formatMessage({*/}
                {/*        id:*/}
                {/*          'customerInformation.add.basicInformation.deliveryAddress',*/}
                {/*        defaultValue: '配送地址',*/}
                {/*      })}*/}
                {/*      name={'deliveryAddress'}*/}
                {/*      rules={[*/}
                {/*        {*/}
                {/*          pattern: '^[.0-9A-Za-z\u4e00-\u9fa5]+$',*/}
                {/*          message: (*/}
                {/*            <FormattedMessage*/}
                {/*              id="customerInformation.add.basicInformation.pattern"*/}
                {/*              defaultValue="格式不正确,仅允许中、英文、数字及'.'"*/}
                {/*            />*/}
                {/*          ),*/}
                {/*        },*/}
                {/*        {*/}
                {/*          max: 200,*/}
                {/*          message: (*/}
                {/*            <FormattedMessage*/}
                {/*              id="customerInformation.add.basicInformation.maxlength2"*/}
                {/*              defaultValue="长度不能超过200个字符"*/}
                {/*            />*/}
                {/*          ),*/}
                {/*        },*/}
                {/*      ]}*/}
                {/*    >*/}
                {/*      <TextArea*/}
                {/*        // showCount*/}
                {/*        maxLength={200}*/}
                {/*        autoSize={{*/}
                {/*          minRows: 1,*/}
                {/*          maxRows: 1,*/}
                {/*        }}*/}
                {/*        name={'deliveryAddress'}*/}
                {/*        onChange={this.handleAddInputChange}*/}
                {/*        placeholder={getIntl().formatMessage({*/}
                {/*          id:*/}
                {/*            'customerInformation.add.basicInformation.deliveryAddress.placeholder',*/}
                {/*          defaultValue: '请输入配送地址',*/}
                {/*        })}*/}
                {/*      />*/}
                {/*    </Form.Item>*/}
                {/*  </Col>*/}
                {/*</Row>*/}
                <Row className={styles.lengthItem} gutter={24}>
                  <Col span={24}>
                    <Form.Item
                      label={getIntl().formatMessage({
                        id:
                          'customerInformation.add.basicInformation.otherAddress',
                        defaultValue: '其他地址',
                      })}
                      name={'otherAddress'}
                      rules={[
                        {
                          pattern: '^[.0-9A-Za-z\u4e00-\u9fa5]+$',
                          message: (
                            <FormattedMessage
                              id="customerInformation.add.basicInformation.pattern"
                              defaultValue="格式不正确,仅允许中、英文、数字及'.'"
                            />
                          ),
                        },
                        {
                          max: 200,
                          message: (
                            <FormattedMessage
                              id="customerInformation.add.basicInformation.maxlength2"
                              defaultValue="长度不能超过200个字符"
                            />
                          ),
                        },
                      ]}
                    >
                      <TextArea
                        // showCount
                        maxLength={200}
                        autoSize={{
                          minRows: 1,
                          maxRows: 1,
                        }}
                        name={'otherAddress'}
                        onChange={this.handleAddInputChange}
                        placeholder={getIntl().formatMessage({
                          id:
                            'customerInformation.add.basicInformation.otherAddress.placeholder',
                          defaultValue: '请输入其他地址',
                        })}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row className={styles.lengthItem} gutter={24}>
                  <Col span={24}>
                    <Form.Item
                      label={getIntl().formatMessage({
                        id: 'customerInformation.add.basicInformation.remark',
                        defaultValue: '备注',
                      })}
                      name={'remark'}
                      rules={[
                        {
                          pattern: '^[.0-9A-Za-z\u4e00-\u9fa5]+$',
                          message: (
                            <FormattedMessage
                              id="customerInformation.add.basicInformation.pattern"
                              defaultValue="格式不正确,仅允许中、英文、数字及'.'"
                            />
                          ),
                        },
                        {
                          max: 2000,
                          message: (
                            <FormattedMessage
                              id="customerInformation.add.basicInformation.maxlength3"
                              defaultValue="长度不能超过2000个字符"
                            />
                          ),
                        },
                      ]}
                    >
                      <TextArea
                        autoSize={{
                          minRows: 3,
                          maxRows: 3,
                        }}
                        // showCount
                        maxLength={2000}
                        name={'remark'}
                        onChange={this.handleAddInputChange}
                        placeholder={getIntl().formatMessage({
                          id:
                            'customerInformation.add.basicInformation.remark.placeholder',
                          defaultValue: '请输入备注',
                        })}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </div>
            </div>
            <div className={styles.customerSocialMediaInformationContent}>
              <div className={styles.title}>
                <div className={styles.line}></div>
                <span>
                  <FormattedMessage
                    id="customerInformation.add.basicInformation.title2"
                    defaultMessage="客户社媒信息"
                  />
                </span>
              </div>
              {Object.keys(mediaDefList)?.map(key => {
                return (
                  <div className={styles.facebookContent} key={key}>
                    <div className={styles.leftTitle}>
                      <p>
                        {/* <span>{key}</span> */}
                        <img
                          src={getSvgComponent(key)}
                          style={{ width: 'auto', height: 38 }}
                        />
                      </p>
                    </div>
                    <div className={styles.rightFacebookContent}>
                      {mediaDefList[key]?.map((item, index) => {
                        return (
                          <div className={styles.itemContent} key={item.code}>
                            {/*<label>{item.name}</label>*/}
                            <Form.Item
                              label={item.name}
                              name={key + ':' + item.code + ':' + item.name}
                              rules={[
                                {
                                  pattern: '^[.0-9A-Za-z\u4e00-\u9fa5]+$',
                                  message: (
                                    <FormattedMessage
                                      id="customerInformation.add.basicInformation.pattern"
                                      defaultValue="格式不正确,仅允许中、英文、数字及'.'"
                                    />
                                  ),
                                },
                                {
                                  max: 200,
                                  message: (
                                    <FormattedMessage
                                      id="customerInformation.add.basicInformation.maxlength2"
                                      defaultValue="长度不能超过200个字符"
                                    />
                                  ),
                                },
                              ]}
                            >
                              <Input
                                placeholder={getIntl().formatMessage({
                                  id:
                                    'customerInformation.add.basicInformation.placeholder',
                                  defaultValue: '请输入',
                                })}
                                name={key + ':' + item.code + ':' + item.name}
                                onChange={e =>
                                  this.handleAddInputChange(e, index, 'media')
                                }
                              />
                            </Form.Item>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                );
              })}
            </div>
            <div className={styles.customerExtensionInformationContent}>
              <div className={styles.title}>
                <div className={styles.line}></div>
                <span>
                  <FormattedMessage
                    id="customerInformation.add.basicInformation.title3"
                    defaultMessage="客户扩展信息"
                  />
                </span>
              </div>
              <div className={styles.countryContent}>
                {customerExtList?.map((item, index) => {
                  return (
                    <div
                      className={styles.itemContent}
                      key={item.customerExtDefCode}
                    >
                      {/*<label>{item.name}</label>*/}
                      <Form.Item
                        name={
                          item.customerExtDefCode +
                          ':' +
                          item.customerExtDefName
                        }
                        //   rules={[
                        //     {
                        //       pattern: '^[.0-9A-Za-z\u4e00-\u9fa5]+$',
                        //       message: (
                        //         <FormattedMessage
                        //           id="customerInformation.add.basicInformation.pattern"
                        //           defaultValue="格式不正确,仅允许中、英文、数字及'.'"
                        //         />
                        //       ),
                        //     },
                        //     {
                        //       max: 200,
                        //       message: (
                        //         <FormattedMessage
                        //           id="customerInformation.add.basicInformation.maxlength2"
                        //           defaultValue="长度不能超过200个字符"
                        //         />
                        //       ),
                        //     },
                        //   ]}
                      >
                        {/* <Input

                          name={
                            item.customerExtDefCode +
                            ':' +
                            item.customerExtDefName
                          }
                          onChange={e =>
                            this.handleAddInputChange(e, index, 'ext')
                          }

                        /> */}
                        <div className={styles.selectItemContent}>
                          {item.propertyTypeId == '1001' ? (
                            // 1001,单行输入框
                            <InputDiv
                              item={item}
                              prompt={item.prompt}
                              name={
                                item.customerExtDefCode +
                                ':' +
                                item.customerExtDefName
                              }
                              isRequired={item.isRequired}
                              handleAddInputChangeFilter={e =>
                                this.handleAddInputChange(e, index, 'ext')
                              }
                            />
                          ) : item.propertyTypeId == '1002' ? (
                            // 1002,多行输入框
                            <InputTextareaDiv
                              item={item}
                              prompt={item.prompt}
                              name={
                                item.customerExtDefCode +
                                ':' +
                                item.customerExtDefName
                              }
                              isRequired={item.isRequired}
                              handleAddInputChangeFilter={e =>
                                this.handleAddInputChange(e, index, 'ext')
                              }
                            />
                          ) : item.propertyTypeId == '1003' ? (
                            // 1003,单选下拉框
                            <SelectDiv
                              item={item}
                              prompt={item.prompt}
                              name={
                                item.customerExtDefCode +
                                ':' +
                                item.customerExtDefName
                              }
                              isRequired={item.isRequired}
                              handleChangeSelectFilter={e =>
                                this.handleAddInputChange(
                                  {
                                    target: {
                                      name:
                                        item.customerExtDefCode +
                                        ':' +
                                        item.customerExtDefName,
                                      value: e,
                                    },
                                  },
                                  index,
                                  'ext',
                                )
                              }
                            />
                          ) : item.propertyTypeId == '1004' ? (
                            // 1004,多选下拉框
                            <SelectMultipleDiv
                              item={item}
                              prompt={item.prompt}
                              name={
                                item.customerExtDefCode +
                                ':' +
                                item.customerExtDefName
                              }
                              isRequired={item.isRequired}
                              handleChangeMultipleSelectFilter={e =>
                                this.handleAddInputChange(
                                  {
                                    target: {
                                      name:
                                        item.customerExtDefCode +
                                        ':' +
                                        item.customerExtDefName,
                                      value: e,
                                    },
                                  },
                                  index,
                                  'ext',
                                  item.propertyTypeId,
                                )
                              }
                            />
                          ) : item.propertyTypeId == '1005' ? (
                            // 1005,单选框
                            <RadioDiv
                              item={item}
                              prompt={item.prompt}
                              name={
                                item.customerExtDefCode +
                                ':' +
                                item.customerExtDefName
                              }
                              isRequired={item.isRequired}
                              handleChangeSelectFilter={e =>
                                this.handleAddInputChange(e, index, 'ext')
                              }
                            />
                          ) : item.propertyTypeId == '1006' ? (
                            // 1006,多选框
                            <CheckboxDiv
                              item={item}
                              name={
                                item.customerExtDefCode +
                                ':' +
                                item.customerExtDefName
                              }
                              isRequired={item.isRequired}
                              prompt={item.prompt}
                              handleAddInputChangeFilter={e =>
                                this.handleAddInputChange(e, index, 'ext')
                              }
                            />
                          ) : item.propertyTypeId == '1007' ? (
                            // 1007,开关
                            <SwitchDiv
                              item={item}
                              name={
                                item.customerExtDefCode +
                                ':' +
                                item.customerExtDefName
                              }
                              isRequired={item.isRequired}
                              handleAddInputChangeFilter={e =>
                                this.handleAddInputChange(e, index, 'ext')
                              }
                            />
                          ) : item.propertyTypeId == '1008' ? (
                            // 1008,时间选择
                            <DatePickerDiv
                              item={item}
                              name={
                                item.customerExtDefCode +
                                ':' +
                                item.customerExtDefName
                              }
                              isRequired={item.isRequired}
                              handleAddInputChangeFilter={(e, value) =>
                                this.handleAddDateChange(
                                  e,
                                  value,
                                  item.customerExtDefCode +
                                    ':' +
                                    item.customerExtDefName,
                                  index,
                                )
                              }
                            /> // 1009,时间范围选择
                          ) : (
                            <RangePickerDiv
                              item={item}
                              name={
                                item.customerExtDefCode +
                                ':' +
                                item.customerExtDefName
                              }
                              isRequired={item.isRequired}
                              handleAddInputChangeFilter={(e, value) =>
                                this.handleAddRangeChange(
                                  e,
                                  value,
                                  item.customerExtDefCode +
                                    ':' +
                                    item.customerExtDefName,
                                  index,
                                )
                              }
                            />
                          )}
                        </div>
                      </Form.Item>
                    </div>
                  );
                })}
              </div>
            </div>
            <div style={{ display: 'none' }}>
              <Form.Item style={{ textAlign: 'center', marginTop: '20px' }}>
                <Popconfirm
                  // title="返回将清空表单，确定返回么？"
                  title={getIntl().formatMessage({
                    id:
                      'customerInformation.add.basicInformation.return.confirm',
                    defaultValue: '返回将清空表单，确定返回么？',
                  })}
                  onConfirm={this.cancelCustomerInformation}
                  // onOpenChange={() => console.log('open change')}
                  style={{ marginRight: '20px' }}
                >
                  <Button htmlType="submit" style={{ marginRight: '15px' }}>
                    <FormattedMessage
                      id="customerInformation.add.basicInformation.button.return"
                      defaultValue="返回"
                    />
                  </Button>
                </Popconfirm>
                <Button
                  type="primary"
                  htmlType="submit"
                  onClick={this.saveCustomerInformation}
                  loading={this.state.saveLoading}
                >
                  <FormattedMessage
                    id="customerInformation.add.basicInformation.button.save"
                    defaultValue="保存"
                  />
                </Button>
              </Form.Item>
            </div>
          </Form>
        </div>
        <Modal
          title={getIntl().formatMessage({
            id: 'customerInformation.modal.basicInformation.tips',
            defaultValue: '提示',
          })}
          open={this.state.isModalOpen}
          onOk={() =>
            this.editorCustomerInformation(this.state.repetCustomerId)
          }
          okText={getIntl().formatMessage({
            id: 'customerInformation.modal.basicInformation.button.ok',
            defaultValue: '查看客户信息',
          })}
          cancelText={getIntl().formatMessage({
            id: 'customerInformation.modal.basicInformation.button.cancel',
            defaultValue: '取消',
          })}
          onCancel={this.cancelCustomerCreate}
          confirmLoading={this.state.editorLoading}
        >
          <p>{this.state.savemsg}</p>
        </Modal>
        {/*联系客户*/}
        <div
          className={styles.contactCustomer}
          style={{ display: contactCustomerStatus ? 'block' : 'none' }}
        >
          <div className={styles.leftContactCustomer}>
            <div className={styles.leftTitle}>
              <p>
                <FormattedMessage
                  id="customerInformation.contactCustomer.info.title"
                  defaultValue="客户信息"
                />
              </p>
              <div className={styles.returnIcon}>
                <span></span>
                <Button type={'link'} onClick={this.handleReturn}>
                  <FormattedMessage
                    id="customerInformation.add.basicInformation.button.return"
                    defaultValue="返回"
                  />
                </Button>
                {/*<img src={RightArrowImg} />*/}
              </div>
            </div>
            <div className={styles.leftCustomerContent}>
              <p>
                <FormattedMessage
                  id="customerInformation.contactCustomer.info.customerName"
                  defaultValue="客户名称："
                />
                {contactCustomer.name}
              </p>
              <p>
                <FormattedMessage
                  id="customerInformation.contactCustomer.info.telephone"
                  defaultValue="联系电话："
                />
                {contactCustomer.telephone}
              </p>
              <p>
                <FormattedMessage
                  id="customerInformation.contactCustomer.info.emailAddress"
                  defaultValue="邮箱地址："
                />
                {contactCustomer.emailAddress}
              </p>
              <p>
                <FormattedMessage
                  id="customerInformation.contactCustomer.info.channelName"
                  defaultValue="渠道："
                />
                {contactCustomer.channelName}
              </p>
              <p>
                <FormattedMessage
                  id="customerInformation.contactCustomer.info.customerGroupName"
                  defaultValue="分组："
                />
                {contactCustomer.customerGroupName}
              </p>
              {/*<p>*/}
              {/*  <FormattedMessage*/}
              {/*    id="customerInformation.contactCustomer.info.mailingAddress"*/}
              {/*    defaultValue="邮寄地址："*/}
              {/*  />*/}
              {/*  {contactCustomer.mailingAddress}*/}
              {/*</p>*/}

              {/*<p>国籍：中国</p>*/}
              {/*<p>FaceBookId：100075642916467</p>*/}
            </div>
          </div>
          <div className={styles.rightContactCustomer}>
            <div className={styles.rightTopContent}>
              <Select
                placeholder={getIntl().formatMessage({
                  id: 'customerInformation.contactCustomer.channel.placeholder',
                  defaultValue: '请选择渠道',
                })}
                allowClear
                showArrow
                showSearch
                fieldNames={{
                  label: 'name',
                  value: 'channelId',
                  key: 'channelId',
                }}
                filterOption={(inputValue, option) =>
                  option.name.toLowerCase().indexOf(inputValue.toLowerCase()) >=
                  0
                }
                value={this.state.contactChannelId}
                options={this.state.channelOptions}
                onChange={value =>
                  this.handleChangeSelect(value, 'contactChannelId')
                }
              />
              <Input
                placeholder={getIntl().formatMessage({
                  id: 'customerInformation.contactCustomer.subject.placeholder',
                  defaultValue: '请输入邮件主题',
                })}
                name={'subject'}
                onChange={this.handleChange}
                value={this.state.subject}
                style={{
                  borderRadius: '6px',
                  marginTop: '15px',
                  height: '30px',
                }}
              />
              {/*<WangEditor ref={this.wangEditorRef} />*/}
              <Button
                type="primary"
                onClick={this.contactCustomerByActive}
                loading={this.state.replayLoading}
              >
                <FormattedMessage
                  id="customerInformation.contactCustomer.button.send"
                  defaultValue="发送"
                />
              </Button>
            </div>
            <div className={styles.rightBottomContent}>
              <p className={styles.titles}>
                <FormattedMessage
                  id="customerInformation.contactCustomer.record"
                  defaultValue="客户咨询记录"
                />
              </p>
              <div className={styles.tableBody}>
                <Table
                  columns={workRecordColumns}
                  scroll={{
                    x: 1300,
                    y: 'calc(100vh - 450px)',
                  }}
                  dataSource={this.state.workRecordList}
                  rowKey={record => record.workRecordId}
                  loading={this.state.workRecordLoading}
                  onChange={this.handleWorkRecordTableChange}
                  pagination={false}
                  // pagination={{
                  //   total: workRecordTotal,
                  //   pageSize: workRecordPageSize,
                  //   current: workRecordPageNum,
                  //   showSizeChanger: true,
                  //   pageSizeOptions: [5, 10, 20, 50, 100],
                  //   showTotal: total => (
                  //     <FormattedMessage
                  //       id="studentManagement.altogether"
                  //       defaultMessage={`共 ${total} 条`}
                  //       values={{ total }}
                  //     />
                  //   ),
                  // }}
                />
              </div>
            </div>
          </div>
        </div>
        {/*另存为筛选器弹窗*/}
        <Modal
          title={getIntl().formatMessage({
            id: 'work.order.filter.name.add',
            defaultValue: '新增筛选器',
          })}
          className="AddFilterModal"
          footer={null}
          open={openAddFilter}
          onCancel={this.closeAddFilterModal}
        >
          <Form
            name="basic"
            autoComplete="off"
            labelAlign="right"
            ref={this.formAddFilterModalRef}
            onFinish={this.onFinishAddFilter}
          >
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  label={getIntl().formatMessage({
                    id: 'work.order.filter.name',
                    defaultValue: '筛选器名称：',
                  })}
                  name="filterName"
                  rules={[
                    {
                      required: true,
                      message: (
                        <FormattedMessage
                          id="work.order.filter.name.required"
                          defaultValue="请输入筛选器名称"
                        />
                      ),
                    },
                  ]}
                >
                  <Input
                    name="filterName"
                    placeholder={getIntl().formatMessage({
                      id: 'work.order.filter.name.required',
                      defaultValue: '请输入筛选器名称',
                    })}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24} style={{ textAlign: 'center' }}>
              <Col span={24}>
                <Form.Item>
                  <Button onClick={this.closeAddFilterModal}>
                    <FormattedMessage
                      id="work.order.management.btn.cancel"
                      defaultMessage="取消"
                    />
                  </Button>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={this.state.createFilterLoading}
                  >
                    <FormattedMessage
                      id="work.order.management.btn.sure"
                      defaultMessage="确定"
                    />
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Modal>
        {/*批量修改分组*/}
        <Modal
          title={getIntl().formatMessage({
            id: 'customerInformation.modal.batch.change.grouping',
            defaultValue: '批量修改分组',
          })}
          visible={changeGroupingModal}
          onOk={this.handleOk}
          onCancel={this.handleCancel}
          footer={null}
          className="changeGroupingModal"
        >
          <label>
            <FormattedMessage
              id="customerDataGroupManagement.groupName"
              defaultMessage="分组名称："
            />
          </label>
          <Select
            style={{ width: '80%' }}
            mode="multiple"
            allowClear
            placeholder={getIntl().formatMessage({
              id:
                'customerInformation.add.basicInformation.selectiveGrouping.placeholder',
              defaultValue: '请选择分组',
            })}
            showSearch
            showArrow
            name="customerGroupIds"
            fieldNames={{
              label: 'customerGroupName',
              value: 'customerGroupId',
              key: 'customerGroupId',
            }}
            filterOption={(inputValue, option) =>
              option.customerGroupName
                .toLowerCase()
                .indexOf(inputValue.toLowerCase()) >= 0
            }
            options={this.state.groupOptions}
            value={this.state.groupIdList}
            onChange={value =>
              this.batchChangeGroupingSelect(value, 'customerGroupIds')
            }
          />

          <div className="changeGroupingFooter">
            <Button onClick={this.handleCancel} style={{ marginRight: '10px' }}>
              <FormattedMessage
                id="customerDataGroupManagement.cancel.btn"
                defaultMessage="取消"
              />
            </Button>
            <Button
              onClick={this.handleOk}
              type="primary"
              loading={this.state.saveLoading}
            >
              <FormattedMessage
                id="customerDataGroupManagement.sure.btn"
                defaultMessage="确定"
              />
            </Button>
          </div>
        </Modal>
        {/*批量导入弹窗*/}
        <Modal
          title={getIntl().formatMessage({
            id: 'customerInformation.modal.batch.import',
            defaultValue: '批量导入',
          })}
          visible={batchImportModal}
          onOk={this.customRequest}
          onCancel={this.handleCancelBatchImportModal}
          className="batchImportModal"
        >
          <div className="importContent">
            <Upload {...this.newProps}>
              <div className="uploadDiv">
                <img src={UploadIcon} />
                <p>
                  <FormattedMessage
                    id="customerInformation.upload.file"
                    defaultMessage="只能上传.xlsx文件"
                  />
                  <span>
                    {' '}
                    <FormattedMessage
                      id="customerInformation.upload.btn"
                      defaultMessage="点击上传"
                    />
                  </span>
                </p>
              </div>
            </Upload>
            <div className="detailDownload">
              <img src={FileIcon} />
              <span>客户资料批量导入模板.xlsx</span>
              <a
                href={
                  'https://crm-email-file.s3.cn-north-1.amazonaws.com.cn/%E5%AE%A2%E6%88%B7%E8%B5%84%E6%96%99%E6%89%B9%E9%87%8F%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx'
                }
              >
                点击下载
              </a>
            </div>
          </div>
        </Modal>
      </div>
    );
  }
}

const mapStateToProps = ({
  customerInformationManagement,
  channel,
  customerDataGroupManagement,
  layouts,
  workOrderCenter,
}) => {
  return {
    ...customerInformationManagement,
    ...channel,
    ...customerDataGroupManagement,
    ...layouts,
    ...workOrderCenter,
  };
};
export default connect(mapStateToProps)(CustomerInformation);

const SelectMultipleDiv = props => (
  <div className={styles.selectItem}>
    <Form.Item
      label={props.item.customerExtDefName}
      name={props.name ? props.name : props.item.customerExtDefCode}
      rules={[
        {
          required: props.isRequired,
        },
      ]}
    >
      <Select
        mode="multiple"
        popupClassName="selectFilterContent"
        allowClear
        showSearch
        placeholder={props.prompt}
        // defaultValue={['a10', 'c12']}
        fieldNames={{
          label: 'optionName',
          value: 'optionValue',
          key: 'optionValue',
        }}
        filterOption={(inputValue, option) =>
          option.optionName.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
        }
        options={props.item.customerExtOptionList}
        onChange={value =>
          props.handleChangeMultipleSelectFilter(
            value,
            props.name ? props.name : props.item.customerExtDefCode,
          )
        }
      />
    </Form.Item>
  </div>
);
const InputDiv = props => (
  <div className={styles.selectItem}>
    <Form.Item
      label={props.item.customerExtDefName}
      name={props.name ? props.name : props.item.customerExtDefCode}
      rules={[
        {
          required: props.isRequired,
        },
      ]}
    >
      <Input
        placeholder={props.prompt}
        onChange={e => props.handleAddInputChangeFilter(e)}
        name={props.name ? props.name : props.item.customerExtDefCode}
      />
    </Form.Item>
  </div>
);
const InputTextareaDiv = props => (
  <div className={styles.selectItem}>
    <Form.Item
      label={props.item.customerExtDefName}
      name={props.name ? props.name : props.item.customerExtDefCode}
      rules={[
        {
          required: props.isRequired,
        },
      ]}
    >
      <Input.TextArea
        placeholder={props.prompt}
        onChange={e => props.handleAddInputChangeFilter(e)}
        rows={4}
        name={props.name ? props.name : props.item.customerExtDefCode}
      />
    </Form.Item>
  </div>
);
const SelectDiv = props => (
  <div className={styles.selectItem}>
    <Form.Item
      label={props.item.customerExtDefName}
      name={props.name ? props.name : props.item.customerExtDefCode}
      rules={[
        {
          required: props.isRequired,
        },
      ]}
    >
      <Select
        allowClear={true}
        popupClassName="selectFilterContent"
        showSearch
        fieldNames={{
          label: 'optionName',
          value: 'optionValue',
          key: 'optionValue',
        }}
        filterOption={(inputValue, option) =>
          option.optionName.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
        }
        placeholder={props.prompt}
        options={props.item.customerExtOptionList}
        onChange={value =>
          props.handleChangeSelectFilter(
            value,
            props.name ? props.name : props.item.customerExtDefCode,
          )
        }
      />
    </Form.Item>
  </div>
);
const RadioDiv = props => {
  return (
    <div className={styles.selectItem}>
      <Form.Item
        label={props.item.customerExtDefName}
        name={props.name ? props.name : props.item.customerExtDefCode}
        rules={[
          {
            required: props.isRequired,
          },
        ]}
      >
        <Radio
          onChange={value =>
            props.handleAddInputChangeFilter(
              value,
              props.name ? props.name : props.item.customerExtDefCode,
            )
          }
        >
          Radio
        </Radio>
      </Form.Item>
    </div>
  );
};
const CheckboxDiv = props => {
  const onChange = e => {
    console.log(`checked = ${e.target.checked}`);
  };
  return (
    <div className={styles.selectItem}>
      <Form.Item
        label={props.item.customerExtDefName}
        name={props.name ? props.name : props.item.customerExtDefCode}
        rules={[
          {
            required: props.isRequired,
          },
        ]}
      >
        <Checkbox
          onChange={value =>
            props.handleAddInputChangeFilter(
              value,
              props.name ? props.name : props.item.customerExtDefCode,
            )
          }
        >
          Checkbox
        </Checkbox>
      </Form.Item>
    </div>
  );
};

const SwitchDiv = props => {
  return (
    <div className={styles.selectItem}>
      <Form.Item
        label={props.item.customerExtDefName}
        name={props.name ? props.name : props.item.customerExtDefCode}
        rules={[
          {
            required: props.isRequired,
          },
        ]}
      >
        <Switch
          defaultChecked
          onChange={value =>
            props.handleAddInputChangeFilter(
              value,
              props.name ? props.name : props.item.customerExtDefCode,
            )
          }
        />
      </Form.Item>
    </div>
  );
};
const DatePickerDiv = props => {
  return (
    <div className={styles.selectItem}>
      <Form.Item
        label={props.item.customerExtDefName}
        name={props.name ? props.name : props.item.customerExtDefCode}
        rules={[
          {
            required: props.isRequired,
          },
        ]}
      >
        <DatePicker
          onChange={(a, b) =>
            props.handleAddInputChangeFilter(
              a,
              b,
              props.item.customerExtDefCode,
            )
          }
        />
      </Form.Item>
    </div>
  );
};
const RangePickerDiv = props => {
  return (
    <div className={styles.selectItem}>
      <Form.Item
        label={props.item.customerExtDefName}
        name={props.name ? props.name : props.item.customerExtDefCode}
        rules={[
          {
            required: props.isRequired,
          },
        ]}
      >
        <RangePicker
          onChange={(a, b) =>
            props.handleAddInputChangeFilter(
              a,
              b,
              props.item.customerExtDefCode,
            )
          }
          format="YYYY-MM-DD"
        />
      </Form.Item>
    </div>
  );
};
