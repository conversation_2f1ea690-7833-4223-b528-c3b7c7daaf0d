import React, { useState, useRef, useEffect } from 'react';
import {
  useDispatch,
  FormattedMessage,
  getIntl,
  history,
  Link,
  useSelector,
} from 'umi';
import {
  Button,
  Spin,
  Input,
  Select,
  Switch,
  notification,
  Pagination,
  Popconfirm,
  Row,
  Col,
  message,
  Modal,
} from 'antd';
import { QRCodeCanvas } from 'qrcode.react';
import styles from './index.less';
import HOCAuth from '@/components/HOCAuth/index';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import { EyeOutlined } from '@ant-design/icons';

import NewWebChatIcon from '../../../assets/new-web-chat-icon.svg';
import NewAppChatIcon from '../../../assets/new-app-chat-icon.svg';
import NewPhoneIcon from '../../../assets/new-phone-icon.svg';
import NewWebOnlineVoiceIcon from '../../../assets/web-online-voice-icon.svg';
import NewAppOnlineVoiceIcon from '../../../assets/app-online-voice-icon.svg';
import NewWebOnlineVideoIcon from '../../../assets/web-online-video-icon.svg';
import NewAppOnlineVideoIcon from '../../../assets/app-online-video-icon.svg';
import NewWhatsAppIcon from '../../../assets/new-whatsapp-icon.svg';
import NewFaceBookIcon from '../../../assets/new-facebook-icon.svg';
import NewInstagramIcon from '../../../assets/ins.svg';

import NewLineIcon from '../../../assets/new-line-icon.svg';
import NewTwitterIcon from '../../../assets/new-twitter-icon.svg';
import NewTelegramIcon from '../../../assets/new-telegram-icon.svg';
import NewWeComIcon from '../../../assets/new-wecom-icon.svg';
import NewWeChatMiniProgramIcon from '../../../assets/new-wechat-mini-program-icon.svg';
import NewWechatOfficialAccountIcon from '../../../assets/new-wechat-official-account-icon.svg';
import NewEmailIcon from '../../../assets/new-email-icon.svg';
import NewAmazonMessageIcon from '../../../assets/new-amazon-message-icon.svg';
import NewShopifyIcon from '../../../assets/new-shopify-icon.svg';
import NewGooglePlayIcon from '../../../assets/google-play-icon.svg';
import NewAddChannelIcon from '../../../assets/new-add-channel-icon.svg';
import NewArrangeIcon from '../../../assets/new-channel-arrange-icon.svg';
import NewArrangeDisableIcon from '../../../assets/new-channel-arrange-disable-icon.svg';
import NewArrangeEnableIcon from '../../../assets/new-channel-arrange-disable-icon.svg';
import NewEditIcon from '../../../assets/new-channel-edit-icon.svg';
import NewEditDisableIcon from '../../../assets/new-channel-edit-disable-icon.svg';
import NewDeleteIcon from '../../../assets/new-channel-delete-icon.svg';
import NewDeleteDisableIcon from '../../../assets/new-channel-delete-disable-icon.svg';
import NewTikTokIcon from '../../../assets/new-tiktok-icon.svg';
import NewDiscordIcon from '../../../assets/new-discord-icon.svg';

const ReturnIcon = () => (
  <svg
    style={{ float: 'left', marginRight: '3px', marginTop: '1px' }}
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.9433 6.16426H2.71921L6.71847 2.60947C6.91205 2.43741 6.92949 2.14102 6.75742 1.94744C6.58536 1.75391 6.28902 1.7364 6.09542 1.90849L1.19417 6.26504C1.08541 6.35092 1.0155 6.48385 1.0155 6.63319C1.0155 6.6334 1.01552 6.63361 1.01552 6.63381C1.01552 6.63401 1.0155 6.63421 1.0155 6.63441C1.0155 6.64181 1.01574 6.6492 1.01609 6.65657C1.01611 6.65682 1.01611 6.65708 1.01611 6.65733C1.02225 6.78282 1.0785 6.90101 1.1729 6.98487L6.0954 11.3603C6.18473 11.4398 6.29595 11.4788 6.40674 11.4788C6.53603 11.4788 6.66478 11.4256 6.7574 11.3214C6.92946 11.1278 6.91203 10.8314 6.71845 10.6594L2.71645 7.10209H10.9433C12.6499 7.10209 14.0382 8.49046 14.0382 10.197C14.0382 11.9036 12.6499 13.2919 10.9433 13.2919H5.53634C5.27737 13.2919 5.06741 13.5019 5.06741 13.7609C5.06741 14.0198 5.27737 14.2298 5.53634 14.2298H10.9433C13.167 14.2298 14.9761 12.4207 14.9761 10.197C14.9761 7.97334 13.167 6.16426 10.9433 6.16426Z"
      fill="#3463FC"
    />
  </svg>
);

const NewChannelConfigurationDetailList = () => {
  const dispatch = useDispatch();
  const { connectId } = useSelector(({ layouts }) => ({
    connectId: layouts.selectedConnect.connectId,
  }));
  const [spinning, setSpinning] = useState(false);
  const [channelName, setChannelName] = useState('');
  const [language, setLanguage] = useState('');
  const [channelType, setChannelType] = useState('');
  // 网站域名
  const [websiteDomainName, setWebsiteDomainName] = useState('');
  const [phone, setPhone] = useState('');
  const [email, setEmail] = useState('');
  const [facebookName, setFacebookName] = useState('');
  const [facebookId, setFacebookId] = useState('');
  const [instagramName, setInstagramName] = useState('');
  const [instagramId, setInstagramId] = useState('');
  const [lineId, setLineId] = useState('');
  const [enterpriseId, setEnterpriseId] = useState('');
  const [enterpriseName, setEnterpriseName] = useState('');
  const [appId, setAppId] = useState('');
  const [showText, setShowText] = useState([false, false]);
  // 卖家地址
  const [sellerAddress, setSellerAddress] = useState('');
  const [pageSize, setPageSize] = useState(10);
  const [pageNum, setPageNum] = useState(1);
  const [detailList, setDetailList] = useState([]);
  const [total, setTotal] = useState(0);
  const [languageList, setLanguageList] = useState([]);
  const [amazonRegionList, setAmazonRegionList] = useState([]);
  const [botName, setBotName] = useState('');

  const [tiktokName, setTiktokName] = useState('');
  const [
    configurationInformationList,
    setConfigurationInformationList,
  ] = useState([]);
  const [script, setScript] = useState('');
  const [httpUrl, setHttpUrl] = useState('');
  const [open, setOpen] = useState(false);
  const [open1, setOpen1] = useState(false);

  useEffect(() => {
    const googleLanguage = JSON.parse(localStorage.getItem('languageLocal'));
    setLanguageList(googleLanguage);
    if (history.location.state) {
      let channelType = history.location.state.channelType;
      setChannelType(channelType);
    } else {
      let channelType = localStorage.getItem('newChannelType');
      setChannelType(channelType);
    }
  }, []);
  useEffect(() => {
    if (channelType) {
      dispatch({
        type: 'chatChannelConfiguration/saveChatConnectId',
        payload: null,
      });
      dispatch({
        type: 'chatChannelConfiguration/saveChatChannelId',
        payload: null,
      });
      dispatch({
        type: 'chatChannelConfiguration/saveChatVoiceConnectId',
        payload: null,
      });
      dispatch({
        type: 'chatChannelConfiguration/saveChatVoiceChannelId',
        payload: null,
      });
      localStorage.setItem('chatConnectId', '');
      localStorage.setItem('chatChannelId', '');
      localStorage.setItem('chatVoiceChannelId', '');
      localStorage.setItem('chatVoiceConnectId', '');

      newChannelList();
    }
  }, [
    channelType,
    channelName,
    language,
    websiteDomainName,
    email,
    phone,
    facebookName,
    facebookId,
    instagramName,
    instagramId,
    lineId,
    enterpriseName,
    enterpriseId,
    appId,
    sellerAddress,
    pageSize,
    pageNum,
    botName,
    tiktokName,
  ]);

  // 新版渠道类型详情页查询渠道
  const newChannelList = async () => {
    setSpinning(true);
    let params;
    if (
      channelType == '8' ||
      channelType == '9' ||
      channelType == '10' ||
      channelType == '11' ||
      channelType == '17' ||
      channelType == '18' ||
      channelType == '21' ||
      channelType == '22'
    ) {
      // WEB&APP聊天、视频、语音、微信小程序、shopify
      params = {
        channelType: channelType,
        name: channelName,
        language: language,
        websiteDomainName: websiteDomainName,
        pageSize: pageSize,
        pageNum: pageNum,
      };
    } else if (channelType == '1') {
      // 邮件
      params = {
        channelType: channelType,
        name: channelName,
        email: email,
        pageSize: pageSize,
        pageNum: pageNum,
      };
    } else if (channelType == '4' || channelType == '7') {
      // whatsApp、电话
      params = {
        channelType: channelType,
        name: channelName,
        phone: phone,
        pageSize: pageSize,
        pageNum: pageNum,
      };
    } else if (channelType == '3') {
      // facebook
      params = {
        channelType: channelType,
        name: channelName,
        facebookName: facebookName,
        facebookId: facebookId,
        pageSize: pageSize,
        pageNum: pageNum,
      };
    } else if (channelType == '13') {
      // Instagram
      params = {
        channelType: channelType,
        name: channelName,
        instagramName: instagramName,
        instagramId: instagramId,
        pageSize: pageSize,
        pageNum: pageNum,
      };
    } else if (channelType == '14') {
      // Line
      params = {
        channelType: channelType,
        name: channelName,
        lineId: lineId,
        pageSize: pageSize,
        pageNum: pageNum,
      };
    } else if (channelType == '24') {
      // tiktok
      params = {
        channelType: channelType,
        name: channelName,
        shopName: tiktokName,
        pageSize: pageSize,
        pageNum: pageNum,
      };
    } else if (channelType == '15') {
      // 企业微信
      params = {
        channelType: channelType,
        name: channelName,
        enterpriseName: enterpriseName,
        enterpriseId: enterpriseId,
        pageSize: pageSize,
        pageNum: pageNum,
      };
    } else if (channelType == '16') {
      // 微信公众号
      params = {
        channelType: channelType,
        name: channelName,
        appId: appId,
        pageSize: pageSize,
        pageNum: pageNum,
      };
    } else if (channelType == '12') {
      // 亚马逊站内信
      params = {
        channelType: channelType,
        name: channelName,
        sellerAddress: sellerAddress,
        pageSize: pageSize,
        pageNum: pageNum,
      };
      await queryAmazonMarketRegion();
    } else if (channelType == '23') {
      // googlePlay
      params = {
        channelType: channelType,
        name: channelName,
        pageSize: pageSize,
        pageNum: pageNum,
      };
      dispatch({
        type: 'faceBookConfiguration/getAllAppInfo',
        payload: params,
        callback: response => {
          setSpinning(false);
          let { code, data, msg } = response;
          if (code === 200) {
            let newData = data?.records?.map(item => {
              return {
                channelId: item.gpChannelId,
                channelName: item.gpChannelName,
                channelType: '23',
                gpSecret: item.gpSecret,
              };
            });
            setDetailList(newData);
            setTotal(response.data.total);
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
      return;
    } else if (channelType == '25') {
      // discord
      params = {
        channelType: channelType,
        name: channelName,
        pageSize: pageSize,
        pageNum: pageNum,
        botName: botName,
      };
    }
    await dispatch({
      type: 'newChannelConfiguration/newChannelList',
      payload: params,
      callback: response => {
        setSpinning(false);
        if (response.code == 200) {
          let newData = response.data.records;
          if (newData) {
            setDetailList(newData);
            setTotal(response.data.total);
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 渠道名称回车搜索
  const handleKeyDownAskCustomer = e => {
    if (event.keyCode === 13 && !event.shiftKey) {
      // 检测到回车键且没有按下 Shift 键
      event.preventDefault(); // 阻止默认的换行行为
      setChannelName(e.target.value);
    }
  };
  // 网站域名回车搜索
  const handleKeyDownWebsiteDomainName = e => {
    if (event.keyCode === 13 && !event.shiftKey) {
      // 检测到回车键且没有按下 Shift 键
      event.preventDefault(); // 阻止默认的换行行为
      setWebsiteDomainName(e.target.value);
    }
  };
  // 电话和WhatsApp回车搜索
  const handleKeyDownPhone = e => {
    if (event.keyCode === 13 && !event.shiftKey) {
      // 检测到回车键且没有按下 Shift 键
      event.preventDefault(); // 阻止默认的换行行为
      setPhone(e.target.value);
    }
  };
  // 电话和WhatsApp回车搜索
  const handleKeyDownEmail = e => {
    if (event.keyCode === 13 && !event.shiftKey) {
      // 检测到回车键且没有按下 Shift 键
      event.preventDefault(); // 阻止默认的换行行为
      setEmail(e.target.value);
    }
  };
  // Facebook Name回车搜索
  const handleKeyDownFacebookName = e => {
    if (event.keyCode === 13 && !event.shiftKey) {
      // 检测到回车键且没有按下 Shift 键
      event.preventDefault(); // 阻止默认的换行行为
      setFacebookName(e.target.value);
    }
  };
  // Facebook Id回车搜索
  const handleKeyDownFacebookId = e => {
    if (event.keyCode === 13 && !event.shiftKey) {
      // 检测到回车键且没有按下 Shift 键
      event.preventDefault(); // 阻止默认的换行行为
      setFacebookId(e.target.value);
    }
  };
  // Instagram Name回车搜索
  const handleKeyDownInstagramName = e => {
    if (event.keyCode === 13 && !event.shiftKey) {
      // 检测到回车键且没有按下 Shift 键
      event.preventDefault(); // 阻止默认的换行行为
      setInstagramName(e.target.value);
    }
  };
  // Instagram Id回车搜索
  const handleKeyDownInstagramId = e => {
    console.log('------handleKeyDownInstagramId-------', e.target.value);
    if (event.keyCode === 13 && !event.shiftKey) {
      // 检测到回车键且没有按下 Shift 键
      event.preventDefault(); // 阻止默认的换行行为
      setInstagramId(e.target.value);
    }
  };
  // Line Id回车搜索
  const handleKeyDownLineId = e => {
    if (event.keyCode === 13 && !event.shiftKey) {
      // 检测到回车键且没有按下 Shift 键
      event.preventDefault(); // 阻止默认的换行行为
      setLineId(e.target.value);
    }
  };
  // 企业ID回车搜索
  const handleKeyDownEnterpriseId = e => {
    if (event.keyCode === 13 && !event.shiftKey) {
      // 检测到回车键且没有按下 Shift 键
      event.preventDefault(); // 阻止默认的换行行为
      setEnterpriseId(e.target.value);
    }
  };
  // 企业全称回车搜索
  const handleKeyDownEnterpriseName = e => {
    if (event.keyCode === 13 && !event.shiftKey) {
      // 检测到回车键且没有按下 Shift 键
      event.preventDefault(); // 阻止默认的换行行为
      setEnterpriseName(e.target.value);
    }
  };
  // 微信公众号ID回车搜索
  const handleKeyDownAppId = e => {
    if (event.keyCode === 13 && !event.shiftKey) {
      // 检测到回车键且没有按下 Shift 键
      event.preventDefault(); // 阻止默认的换行行为
      setAppId(e.target.value);
    }
  };
  // Tiktok店铺名称回车搜索
  const handleKeyDownTiktokName = e => {
    if (event.keyCode === 13 && !event.shiftKey) {
      // 检测到回车键且没有按下 Shift 键
      event.preventDefault(); // 阻止默认的换行行为
      setTiktokName(e.target.value);
    }
  };
  // 卖家地址回车搜索
  const handleKeyDownSellerRegion = e => {
    // if (event.keyCode === 13 && !event.shiftKey) {
    //   // 检测到回车键且没有按下 Shift 键
    //   event.preventDefault(); // 阻止默认的换行行为
    setSellerAddress(e);
    // }
  };
  // 机器人名称回车搜索
  const handleKeyDownBotName = e => {
    if (event.keyCode === 13 && !event.shiftKey) {
      // 检测到回车键且没有按下 Shift 键
      event.preventDefault(); // 阻止默认的换行行为
      setBotName(e.target.value);
    }
  };

  // 切换语言
  const handleChangeLanguage = value => {
    if (value !== undefined) {
      setLanguage(value);
    } else {
      setLanguage('');
    }
  };

  // 修改通道
  const updataChannel = async (id, type, connectInstanceId, gpSecret) => {
    console.log(type);
    if (type == 1) {
      dispatch({
        type: 'emailChannelConfiguration/saveEmailConnectId',
        payload: connectId,
      });
      dispatch({
        type: 'emailChannelConfiguration/saveEmailChannelId',
        payload: id,
      });
      dispatch({
        type: 'emailChannelConfiguration/saveEmailEditStatus',
        payload: 1,
      });
      localStorage.setItem('emailChannelId', id);
      localStorage.setItem('editStatus', '1');
      localStorage.setItem('emailConnectId', connectId);
      history.push('/channelAllocation/emailChannelConfiguration');
    } else if (type == 8 || type == 9 || type == 21 || type == 22) {
      dispatch({
        type: 'chatChannelConfiguration/saveChatConnectId',
        payload: connectInstanceId,
      });
      dispatch({
        type: 'chatChannelConfiguration/saveChatChannelId',
        payload: id,
      });
      dispatch({
        type: 'chatChannelConfiguration/saveChatEditStatus',
        payload: type,
      });
      localStorage.setItem('chatChannelId', id);
      localStorage.setItem('editStatus', type);
      localStorage.setItem('chatConnectId', connectInstanceId);
      history.push(`/channelAllocation/chatChannelConfiguration?type=${type}`);
    } else if (type == 17 || type == 18 || type == 10 || type == 11) {
      dispatch({
        type: 'chatChannelConfiguration/saveChatVoiceConnectId',
        payload: connectInstanceId,
      });
      dispatch({
        type: 'chatChannelConfiguration/saveChatVoiceChannelId',
        payload: id,
      });
      localStorage.setItem('chatVoiceChannelId', id);
      localStorage.setItem('chatVoiceConnectId', connectInstanceId);
      history.push(
        `/channelAllocation/chatVoiceChannelConfiguration?type=${type}`,
      );
    } else if (type == 12) {
      dispatch({
        type: 'whatsAppChannelConfiguration/saveAwsConnectId',
        payload: connectId,
      });
      dispatch({
        type: 'whatsAppChannelConfiguration/saveAmazonChannelId',
        payload: id,
      });
      history.push(
        `/channelAllocation/amazonRegionConfiguration?id=${connectId}&&channelId=${id}`,
      );
    } else if (type == 13) {
      dispatch({
        type: 'whatsAppChannelConfiguration/saveInstagramConnectId',
        payload: connectId,
      });
      dispatch({
        type: 'whatsAppChannelConfiguration/saveInstagramChannelId',
        payload: id,
      });
      history.push(
        `/channelAllocation/instagramConfiguration?id=${connectId}&&channelId=${id}`,
      );
    } else if (type == 15) {
      dispatch({
        type: 'whatsAppChannelConfiguration/saveWeChatConnectId',
        payload: connectId,
      });
      dispatch({
        type: 'whatsAppChannelConfiguration/saveWeChatChannelId',
        payload: id,
      });
      history.push(
        `/channelAllocation/wechatConfiguration?id=${connectId}&&channelId=${id}`,
      );
    } else if (type == 3) {
      dispatch({
        type: 'whatsAppChannelConfiguration/saveFacebookConnectId',
        payload: connectId,
      });
      dispatch({
        type: 'whatsAppChannelConfiguration/saveFacebookChannelId',
        payload: id,
      });
      history.push(
        `/channelAllocation/faceBookConfiguration?id=${connectId}&&channelId=${id}`,
      );
    } else if (type == 4) {
      dispatch({
        type: 'whatsAppChannelConfiguration/saveWhatsAppConnectId',
        payload: connectId,
      });
      dispatch({
        type: 'whatsAppChannelConfiguration/saveWhatsAppChannelId',
        payload: id,
      });
      history.push(
        `/channelAllocation/whatsAppChannelConfiguration?id=${connectId}&&channelId=${id}`,
      );
    } else if (type == 14) {
      dispatch({
        type: 'whatsAppChannelConfiguration/saveLineChannelId',
        payload: id,
      });
      history.push(
        `/channelAllocation/lineChannelConfiguration?channelId=${id}`,
      );
    } else if (type == 16) {
      dispatch({
        type: 'whatsAppChannelConfiguration/saveWechatOfficialAccountChannelId',
        payload: id,
      });
      history.push(
        `/channelAllocation/weChatOfficialAccountChannelConfiguration?channelId=${id}`,
      );
    } else if (type == 23) {
      dispatch({
        type: 'whatsAppChannelConfiguration/saveGooglePlayChannelId',
        payload: id,
      });
      history.push(
        `/channelAllocation/googlePlayConfiguration?channelId=${id}&gpChannelName=${connectInstanceId}&gpSecret=${gpSecret}`,
      );
    } else if (type == 25) {
      history.push(`/channelAllocation/discordConfiguration?channelId=${id}`);
    } else if (type == 24) {
      history.push(`/channelAllocation/tiktokConfiguration?channelId=${id}`);
    }
  };
  /**
   * 启用/禁用
   */
  const handleEnableDisable = (id, number) => {
    dispatch({
      type: 'channel/updateStatus',
      payload: { id: id, channelStatus: number },
      callback: response => {
        let { code, data, msg } = response;
        if (code === 200) {
          notification.success({
            message: msg,
          });
          newChannelList();
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };
  /**
   * 部署
   */
  const deployChannel = (id, type) => {
    dispatch({
      type: 'chatChannelConfiguration/deploy',
      payload: { channelId: id },
      callback: response => {
        let { code, data, msg } = response;
        if (code === 200) {
          setScript(data.script);
          setHttpUrl(data.httpUrl);
          setOpen(true);
        }
      },
    });
  };
  // 配置信息
  const queryPartConfig = (channelTypeCode, channelId) => {
    // queryPartConfig
    let params = {
      channelId: channelId,
      channelTypeCode: channelTypeCode,
    };
    dispatch({
      type: 'channel/queryPartConfig',
      payload: params,
      callback: response => {
        let { code, data, msg } = response;
        if (code === 200) {
          setConfigurationInformationList(data);
          // setScript(data.script);
          // setHttpUrl(data.httpUrl);
          setOpen1(true);
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };
  /**
   * 关闭弹窗
   */
  const handleCancel = () => {
    setOpen(false);
    setOpen1(false);
    setShowText([false, false]);
  };
  /**
   * 删除通道
   */
  const deleteChannel = (id, type) => {
    setSpinning(true);
    if (type === 'google') {
      let data = { gpChannelId: id.channelId };

      dispatch({
        type: 'faceBookConfiguration/delApp',
        payload: data,
        callback: response => {
          setSpinning(false);
          let { code, data, msg } = response;
          if (code === 200) {
            notification.success({
              message: getIntl().formatMessage({
                id: 'user.management.remove.success',
              }),
            });
            newChannelList();
          } else {
            notification.error({
              message: msg,
            });
          }
        },
      });
    } else {
      dispatch({
        type: 'channel/deleteChannel',
        payload: { id: id },
        callback: response => {
          setSpinning(false);
          let { code, data, msg } = response;
          if (code === 200) {
            notification.success({
              message: msg,
            });
            newChannelList();
          } else {
            notification.error({
              message: msg,
            });
          }
        },
      });
    }
  };
  /**
   * 查询国家地区
   */
  const queryAmazonMarketRegion = async () => {
    dispatch({
      type: 'amazonRegionConfiguration/queryAmazonMarketRegion',
      callback: response => {
        if (response.code == 200) {
          let list = [];
          response.data?.forEach(item => {
            list.push(item.regionDefList);
          });
          let newList = list.flat(Infinity);
          setAmazonRegionList(newList);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  /**
   * google渠道查看所有应用
   */
  const queryAllApps = id => {
    history.push(`/googlePlayAllApps?channelId=${id}`);
  };
  // 切换列表展示条数
  const onShowSizeChange = (current, pageSize) => {
    setPageSize(pageSize);
    setPageNum(current);
  };
  // 切换分页
  const onChangePage = (page, pageSize) => {
    setPageSize(pageSize);
    setPageNum(page);
  };
  // 返回列表
  const handleReturnList = () => {
    localStorage.removeItem('newChannelType');
    history.push('/channelConfigurationList');
  };
  // 新增渠道
  const handleAddChannel = () => {
    console.log(channelType);
    // channelType
    if (channelType == '1') {
      dispatch({
        type: 'emailChannelConfiguration/saveEmailChannelId',
        payload: '',
      });
      dispatch({
        type: 'emailChannelConfiguration/saveEmailConnectId',
        payload: connectId,
      });
      dispatch({
        type: 'emailChannelConfiguration/saveEmailEditStatus',
        payload: 0,
      });
      localStorage.setItem('editStatus', '0');
      localStorage.setItem('emailConnectId', connectId);
      history.push('/channelAllocation/emailChannelConfiguration');
    } else if (
      channelType == '8' ||
      channelType == '9' ||
      channelType == '21' ||
      channelType == '22'
    ) {
      history.push(
        `/channelAllocation/chatChannelConfiguration?type=${channelType}`,
      );
    } else if (
      channelType == '17' ||
      channelType == '18' ||
      channelType == '10' ||
      channelType == '11'
    ) {
      history.push(
        `/channelAllocation/chatVoiceChannelConfiguration?type=${channelType}`,
      );
    } else if (channelType == '12') {
      dispatch({
        type: 'whatsAppChannelConfiguration/saveAwsConnectId',
        payload: connectId,
      });
      dispatch({
        type: 'whatsAppChannelConfiguration/saveAmazonChannelId',
        payload: '',
      });
      history.push(
        `/channelAllocation/amazonRegionConfiguration?id=${connectId}`,
      );
    } else if (channelType == '4') {
      dispatch({
        type: 'whatsAppChannelConfiguration/saveWhatsAppConnectId',
        payload: connectId,
      });
      dispatch({
        type: 'whatsAppChannelConfiguration/saveWhatsAppChannelId',
        payload: '',
      });
      history.push(
        `/channelAllocation/whatsAppChannelConfiguration?id=${connectId}`,
      );
    } else if (channelType == '14') {
      dispatch({
        type: 'whatsAppChannelConfiguration/saveLineChannelId',
        payload: '',
      });
      history.push(`/channelAllocation/lineChannelConfiguration`);
    } else if (channelType == '16') {
      dispatch({
        type: 'whatsAppChannelConfiguration/saveWechatOfficialAccountChannelId',
        payload: '',
      });
      history.push(
        `/channelAllocation/weChatOfficialAccountChannelConfiguration`,
      );
    } else if (channelType == '3') {
      dispatch({
        type: 'whatsAppChannelConfiguration/saveFacebookConnectId',
        payload: connectId,
      });
      dispatch({
        type: 'whatsAppChannelConfiguration/saveFacebookChannelId',
        payload: '',
      });
      history.push(`/channelAllocation/faceBookConfiguration?id=${connectId}`);
    } else if (channelType == '15') {
      dispatch({
        type: 'whatsAppChannelConfiguration/saveWeChatConnectId',
        payload: connectId,
      });
      dispatch({
        type: 'whatsAppChannelConfiguration/saveWeChatChannelId',
        payload: '',
      });
      history.push(`/channelAllocation/wechatConfiguration?id=${connectId}`);
    } else if (channelType == '13') {
      dispatch({
        type: 'whatsAppChannelConfiguration/saveInstagramConnectId',
        payload: connectId,
      });
      dispatch({
        type: 'whatsAppChannelConfiguration/saveInstagramChannelId',
        payload: '',
      });
      history.push(`/channelAllocation/instagramConfiguration?id=${connectId}`);
    } else if (channelType == '23') {
      dispatch({
        type: 'whatsAppChannelConfiguration/saveGooglePlayChannelId',
        payload: '',
      });
      history.push(
        `/channelAllocation/googlePlayConfiguration?id=${connectId}`,
      );
    } else if (channelType == '24') {
      history.push(`/channelAllocation/tiktokConfiguration`);
    } else if (channelType == '25') {
      history.push(`/channelAllocation/discordConfiguration`);
    }
  };
  const doDownload = (url, fileName) => {
    console.log(url, fileName);
    const a = document.createElement('a');
    a.download = fileName;
    a.href = url;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };
  const downloadCanvasQRCode = () => {
    const canvas = document.getElementById('myqrcode');
    if (canvas) {
      const url = canvas.toDataURL();
      doDownload(url, 'QRCode.png');
    }
  };
  return (
    <div>
      <Button
        onClick={handleReturnList}
        style={{ float: 'left', marginTop: '20px', marginLeft: '20px' }}
        icon={<ReturnIcon />}
      >
        <FormattedMessage
          id="document.knowledge.base.return.list.btn"
          defaultMessage="返回列表"
        />
      </Button>
      <div className={styles.newChannelConfigurationDetailList}>
        <Spin spinning={spinning}>
          <p
            className="blueBorder"
            style={{ display: channelType == '1' ? 'block' : 'none' }}
          >
            <FormattedMessage
              id="channel.allocation.detail.email.title"
              defaultMessage="邮件渠道配置"
            />
          </p>
          <p
            className="blueBorder"
            style={{ display: channelType == '3' ? 'block' : 'none' }}
          >
            <FormattedMessage
              id="channel.allocation.detail.facebook.title"
              defaultMessage="Facebook Message渠道配置"
            />
          </p>
          <p
            className="blueBorder"
            style={{ display: channelType == '4' ? 'block' : 'none' }}
          >
            <FormattedMessage
              id="channel.allocation.detail.whats.app.title"
              defaultMessage="WhatsApp渠道配置"
            />
          </p>
          <p
            className="blueBorder"
            style={{ display: channelType == '7' ? 'block' : 'none' }}
          >
            <FormattedMessage
              id="channel.allocation.detail.phone.title"
              defaultMessage="电话渠道配置"
            />
          </p>
          <p
            className="blueBorder"
            style={{ display: channelType == '8' ? 'block' : 'none' }}
          >
            <FormattedMessage
              id="channel.allocation.detail.web.online.chat.title"
              defaultMessage="WEB聊天配置渠道"
            />
          </p>
          <p
            className="blueBorder"
            style={{ display: channelType == '9' ? 'block' : 'none' }}
          >
            <FormattedMessage
              id="channel.allocation.detail.app.online.chat.title"
              defaultMessage="APP聊天渠道配置"
            />
          </p>
          <p
            className="blueBorder"
            style={{ display: channelType == '10' ? 'block' : 'none' }}
          >
            <FormattedMessage
              id="channel.allocation.detail.web.online.video.title"
              defaultMessage="WEB视频配置渠道"
            />
          </p>
          <p
            className="blueBorder"
            style={{ display: channelType == '11' ? 'block' : 'none' }}
          >
            <FormattedMessage
              id="channel.allocation.detail.app.online.video.title"
              defaultMessage="APP视频渠道配置"
            />
          </p>
          <p
            className="blueBorder"
            style={{ display: channelType == '12' ? 'block' : 'none' }}
          >
            <FormattedMessage
              id="channel.allocation.detail.amazon.message.title"
              defaultMessage="Amazon Message渠道配置"
            />
          </p>
          <p
            className="blueBorder"
            style={{ display: channelType == '13' ? 'block' : 'none' }}
          >
            <FormattedMessage
              id="channel.allocation.detail.instagram.title"
              defaultMessage="Instagram渠道配置"
            />
          </p>
          <p
            className="blueBorder"
            style={{ display: channelType == '14' ? 'block' : 'none' }}
          >
            <FormattedMessage
              id="channel.allocation.detail.line.title"
              defaultMessage="Line渠道配置"
            />
          </p>
          <p
            className="blueBorder"
            style={{ display: channelType == '15' ? 'block' : 'none' }}
          >
            <FormattedMessage
              id="channel.allocation.detail.we.com.title"
              defaultMessage="企业微信渠道配置"
            />
          </p>
          <p
            className="blueBorder"
            style={{ display: channelType == '16' ? 'block' : 'none' }}
          >
            <FormattedMessage
              id="channel.allocation.detail.wechat.official.account.title"
              defaultMessage="微信公众号渠道配置"
            />
          </p>
          <p
            className="blueBorder"
            style={{ display: channelType == '17' ? 'block' : 'none' }}
          >
            <FormattedMessage
              id="channel.allocation.detail.web.online.voice.title"
              defaultMessage="WEB语音配置渠道"
            />
          </p>
          <p
            className="blueBorder"
            style={{ display: channelType == '18' ? 'block' : 'none' }}
          >
            <FormattedMessage
              id="channel.allocation.detail.app.online.voice.title"
              defaultMessage="APP语音渠道配置"
            />
          </p>
          <p
            className="blueBorder"
            style={{ display: channelType == '19' ? 'block' : 'none' }}
          >
            <FormattedMessage
              id="channel.allocation.detail.twitter.title"
              defaultMessage="Twitter渠道配置"
            />
          </p>
          <p
            className="blueBorder"
            style={{ display: channelType == '20' ? 'block' : 'none' }}
          >
            <FormattedMessage
              id="channel.allocation.detail.telegram.title"
              defaultMessage="Telegram渠道配置"
            />
          </p>
          <p
            className="blueBorder"
            style={{ display: channelType == '21' ? 'block' : 'none' }}
          >
            <FormattedMessage
              id="channel.allocation.detail.wechat.mini.program.title"
              defaultMessage="微信小程序渠道配置"
            />
          </p>
          <p
            className="blueBorder"
            style={{ display: channelType == '22' ? 'block' : 'none' }}
          >
            <FormattedMessage
              id="channel.allocation.detail.shopify.title"
              defaultMessage="Shopify渠道配置"
            />
          </p>
          <p
            className="blueBorder"
            style={{ display: channelType == '23' ? 'block' : 'none' }}
          >
            <FormattedMessage
              id="channel.allocation.detail.google.play.title"
              defaultMessage="Google Play渠道配置"
            />
          </p>
          <p
            className="blueBorder"
            style={{ display: channelType == '24' ? 'block' : 'none' }}
          >
            <FormattedMessage
              id="channel.allocation.detail.tiktok.title"
              defaultMessage="Tiktok配置渠道"
            />
          </p>
          <p
            className="blueBorder"
            style={{ display: channelType == '25' ? 'block' : 'none' }}
          >
            <FormattedMessage
              id="channel.allocation.detail.discord.title"
              defaultMessage="Discord渠道配置"
            />
          </p>
          <div className={styles.selectContent}>
            <div className={styles.selectItem}>
              <p
                className={styles.selectText}
                title={getIntl().formatMessage({
                  id: 'channel.allocation.detail.select.channel.name',
                  defaultValue: '渠道名称：',
                })}
              >
                <FormattedMessage
                  id="channel.allocation.detail.select.channel.name"
                  defaultMessage="渠道名称："
                />
              </p>
              <Input
                onPressEnter={e => handleKeyDownAskCustomer(e)}
                placeholder={getIntl().formatMessage({
                  id:
                    'channel.allocation.detail.select.channel.name.placeholder',
                  defaultValue: '请输入渠道名称，回车搜索',
                })}
              />
            </div>
            <div
              className={styles.selectItem}
              style={{
                display:
                  channelType == '8' ||
                  channelType == '9' ||
                  channelType == '10' ||
                  channelType == '11' ||
                  channelType == '17' ||
                  channelType == '18' ||
                  channelType == '21' ||
                  channelType == '22'
                    ? 'block'
                    : 'none',
              }}
            >
              <p
                className={styles.selectText}
                title={getIntl().formatMessage({
                  id: 'channel.allocation.detail.select.language',
                  defaultValue: '语言：',
                })}
              >
                <FormattedMessage
                  id="channel.allocation.detail.select.language"
                  defaultMessage="语言："
                />
              </p>
              <Select
                placeholder={getIntl().formatMessage({
                  id: 'channel.allocation.detail.select.language.placeholder',
                  defaultValue: '请选择语言',
                })}
                onChange={handleChangeLanguage}
                allowClear
                options={languageList}
                showSearch
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) >= 0
                }
              />
            </div>
            <div
              className={styles.selectItem}
              style={{
                display:
                  channelType == '8' ||
                  channelType == '9' ||
                  channelType == '10' ||
                  channelType == '11' ||
                  channelType == '17' ||
                  channelType == '18' ||
                  channelType == '21' ||
                  channelType == '22'
                    ? 'block'
                    : 'none',
              }}
            >
              <p
                className={styles.selectText}
                title={getIntl().formatMessage({
                  id: 'channel.allocation.detail.select.website.domain.name',
                  defaultValue: '网站域名：',
                })}
              >
                <FormattedMessage
                  id="channel.allocation.detail.select.website.domain.name"
                  defaultMessage="网站域名："
                />
              </p>
              <Input
                onPressEnter={e => handleKeyDownWebsiteDomainName(e)}
                placeholder={getIntl().formatMessage({
                  id:
                    'channel.allocation.detail.select.website.domain.name.placeholder',
                  defaultValue: '请输入网站域名，回车搜索',
                })}
              />
            </div>
            <div
              className={styles.selectItem}
              style={{ display: channelType == '7' ? 'block' : 'none' }}
            >
              <p
                className={styles.selectText}
                title={getIntl().formatMessage({
                  id: 'channel.allocation.detail.select.phone.number',
                  defaultValue: '电话号码：',
                })}
              >
                <FormattedMessage
                  id="channel.allocation.detail.select.phone.number"
                  defaultMessage="电话号码："
                />
              </p>
              <Input
                onPressEnter={e => handleKeyDownPhone(e)}
                placeholder={getIntl().formatMessage({
                  id:
                    'channel.allocation.detail.select.phone.number.placeholder',
                  defaultValue: '请输入电话号码，回车搜索',
                })}
              />
            </div>
            <div
              className={styles.selectItem}
              style={{ display: channelType == '1' ? 'block' : 'none' }}
            >
              <p
                className={styles.selectText}
                title={getIntl().formatMessage({
                  id: 'channel.allocation.detail.select.email.account',
                  defaultValue: 'Email账户：',
                })}
              >
                <FormattedMessage
                  id="channel.allocation.detail.select.email.account"
                  defaultMessage="Email账户："
                />
              </p>
              <Input
                onPressEnter={e => handleKeyDownEmail(e)}
                placeholder={getIntl().formatMessage({
                  id:
                    'channel.allocation.detail.select.email.account.placeholder',
                  defaultValue: '请输入Email账户，回车搜索',
                })}
              />
            </div>
            <div
              className={styles.selectItem}
              style={{ display: channelType == '4' ? 'block' : 'none' }}
            >
              <p
                className={styles.selectText}
                title={getIntl().formatMessage({
                  id: 'channel.allocation.detail.select.bind.phone.number',
                  defaultValue: '绑定电话：',
                })}
              >
                <FormattedMessage
                  id="channel.allocation.detail.select.bind.phone.number"
                  defaultMessage="绑定电话："
                />
              </p>
              <Input
                onPressEnter={e => handleKeyDownPhone(e)}
                placeholder={getIntl().formatMessage({
                  id:
                    'channel.allocation.detail.select.bind.phone.number.placeholder',
                  defaultValue: '请输入绑定电话，回车搜索',
                })}
              />
            </div>
            <div
              className={styles.selectItem}
              style={{ display: channelType == '3' ? 'block' : 'none' }}
            >
              <p
                className={styles.selectText}
                title={getIntl().formatMessage({
                  id: 'channel.allocation.detail.select.account.name',
                  defaultValue: '账号名称：',
                })}
              >
                <FormattedMessage
                  id="channel.allocation.detail.select.account.name"
                  defaultMessage="账号名称："
                />
              </p>
              <Input
                onPressEnter={e => handleKeyDownFacebookName(e)}
                placeholder={getIntl().formatMessage({
                  id:
                    'channel.allocation.detail.select.account.name.placeholder',
                  defaultValue: '请输入账号名称，回车搜索',
                })}
              />
            </div>
            <div
              className={styles.selectItem}
              style={{ display: channelType == '13' ? 'block' : 'none' }}
            >
              <p
                className={styles.selectText}
                title={getIntl().formatMessage({
                  id: 'channel.allocation.detail.select.account.name',
                  defaultValue: '账号名称：',
                })}
              >
                <FormattedMessage
                  id="channel.allocation.detail.select.account.name"
                  defaultMessage="账号名称："
                />
              </p>
              <Input
                onPressEnter={e => handleKeyDownInstagramName(e)}
                placeholder={getIntl().formatMessage({
                  id:
                    'channel.allocation.detail.select.account.name.placeholder',
                  defaultValue: '请输入账号名称，回车搜索',
                })}
              />
            </div>
            <div
              className={styles.selectItem}
              style={{ display: channelType == '3' ? 'block' : 'none' }}
            >
              <p
                className={styles.selectText}
                title={getIntl().formatMessage({
                  id: 'channel.allocation.detail.select.account.id',
                  defaultValue: '账号ID：',
                })}
              >
                <FormattedMessage
                  id="channel.allocation.detail.select.account.id"
                  defaultMessage="账号ID："
                />
              </p>
              <Input
                onPressEnter={e => handleKeyDownFacebookId(e)}
                placeholder={getIntl().formatMessage({
                  id: 'channel.allocation.detail.select.account.id.placeholder',
                  defaultValue: '请输入账号ID，回车搜索',
                })}
              />
            </div>
            <div
              className={styles.selectItem}
              style={{ display: channelType == '13' ? 'block' : 'none' }}
            >
              <p
                className={styles.selectText}
                title={getIntl().formatMessage({
                  id: 'channel.allocation.detail.select.account.id',
                  defaultValue: '账号ID：',
                })}
              >
                <FormattedMessage
                  id="channel.allocation.detail.select.account.id"
                  defaultMessage="账号ID："
                />
              </p>
              <Input
                onPressEnter={e => handleKeyDownInstagramId(e)}
                placeholder={getIntl().formatMessage({
                  id: 'channel.allocation.detail.select.account.id.placeholder',
                  defaultValue: '请输入账号ID，回车搜索',
                })}
              />
            </div>
            <div
              className={styles.selectItem}
              style={{ display: channelType == '14' ? 'block' : 'none' }}
            >
              <p
                className={styles.selectText}
                title={getIntl().formatMessage({
                  id: 'channel.allocation.detail.select.line.id',
                  defaultValue: 'Line ID：',
                })}
              >
                <FormattedMessage
                  id="channel.allocation.detail.select.line.id"
                  defaultMessage="Line ID："
                />
              </p>
              <Input
                onPressEnter={e => handleKeyDownLineId(e)}
                placeholder={getIntl().formatMessage({
                  id: 'channel.allocation.detail.select.line.id.placeholder',
                  defaultValue: '请输入Line ID，回车搜索',
                })}
              />
            </div>
            <div
              className={styles.selectItem}
              style={{ display: channelType == '15' ? 'block' : 'none' }}
            >
              <p
                className={styles.selectText}
                title={getIntl().formatMessage({
                  id: 'channel.allocation.detail.select.enterprise.name',
                  defaultValue: '企业全称：',
                })}
              >
                <FormattedMessage
                  id="channel.allocation.detail.select.enterprise.name"
                  defaultMessage="企业全称："
                />
              </p>
              <Input
                onPressEnter={e => handleKeyDownEnterpriseName(e)}
                placeholder={getIntl().formatMessage({
                  id:
                    'channel.allocation.detail.select.enterprise.name.placeholder',
                  defaultValue: '请输入企业全称，回车搜索',
                })}
              />
            </div>
            <div
              className={styles.selectItem}
              style={{ display: channelType == '15' ? 'block' : 'none' }}
            >
              <p
                className={styles.selectText}
                title={getIntl().formatMessage({
                  id: 'channel.allocation.detail.select.enterprise.id',
                  defaultValue: '企业ID：',
                })}
              >
                <FormattedMessage
                  id="channel.allocation.detail.select.enterprise.id"
                  defaultMessage="企业ID："
                />
              </p>
              <Input
                onPressEnter={e => handleKeyDownEnterpriseId(e)}
                placeholder={getIntl().formatMessage({
                  id:
                    'channel.allocation.detail.select.enterprise.id.placeholder',
                  defaultValue: '请输入企业ID，回车搜索',
                })}
              />
            </div>
            <div
              className={styles.selectItem}
              style={{ display: channelType == '16' ? 'block' : 'none' }}
            >
              <p
                className={styles.selectText}
                title={getIntl().formatMessage({
                  id: 'channel.allocation.detail.select.app.id',
                  defaultValue: 'APP ID：',
                })}
              >
                <FormattedMessage
                  id="channel.allocation.detail.select.app.id"
                  defaultMessage="APP ID："
                />
              </p>
              <Input
                onPressEnter={e => handleKeyDownAppId(e)}
                placeholder={getIntl().formatMessage({
                  id: 'channel.allocation.detail.select.app.id.placeholder',
                  defaultValue: '请输入APP ID，回车搜索',
                })}
              />
            </div>
            <div
              className={styles.selectItem}
              style={{ display: channelType == '12' ? 'block' : 'none' }}
            >
              <p
                className={styles.selectText}
                title={getIntl().formatMessage({
                  id: 'channel.allocation.detail.select.seller.region',
                  defaultValue: '卖家地区：',
                })}
              >
                <FormattedMessage
                  id="channel.allocation.detail.select.seller.region"
                  defaultMessage="卖家地区："
                />
              </p>
              <Select
                placeholder={getIntl().formatMessage({
                  id:
                    'channel.allocation.detail.select.seller.region.placeholder',
                  defaultValue: '请输入卖家地区，回车搜索',
                })}
                onChange={e => handleKeyDownSellerRegion(e)}
                allowClear
                options={amazonRegionList}
                showSearch
                fieldNames={{
                  label: 'countryName',
                  value: 'id',
                }}
                filterOption={(inputValue, option) =>
                  option.countryName
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) >= 0
                }
              />
            </div>
            <div
              className={styles.selectItem}
              style={{ display: channelType == '24' ? 'block' : 'none' }}
            >
              <p
                className={styles.selectText}
                title={getIntl().formatMessage({
                  id: 'channel.allocation.detail.select.account.tiktok.name',
                  defaultValue: '店铺名称：',
                })}
              >
                <FormattedMessage
                  id="channel.allocation.detail.select.account.tiktok.name"
                  defaultMessage="店铺名称："
                />
              </p>
              <Input
                onPressEnter={e => handleKeyDownTiktokName(e)}
                placeholder={getIntl().formatMessage({
                  id:
                    'channel.allocation.detail.select.account.tiktok.name.placeholder',
                  defaultValue: '请输入店铺名称，回车搜索',
                })}
              />
            </div>
            <div
              className={styles.selectItem}
              style={{ display: channelType == '25' ? 'block' : 'none' }}
            >
              <p
                className={styles.selectText}
                title={getIntl().formatMessage({
                  id: 'channel.allocation.detail.input.bot.name',
                  defaultValue: '机器人名称：',
                })}
              >
                <FormattedMessage
                  id="channel.allocation.detail.input.bot.name"
                  defaultMessage="机器人名称："
                />
              </p>
              <Input
                onPressEnter={e => handleKeyDownBotName(e)}
                placeholder={getIntl().formatMessage({
                  id: 'channel.allocation.detail.input.bot.name.placeholder',
                  defaultValue: '请输入机器人名称，回车搜索',
                })}
              />
            </div>
          </div>
          <div className={styles.channelListContent}>
            <div className={styles.addChannelItem} onClick={handleAddChannel}>
              <img src={NewAddChannelIcon} />
              <p>
                <FormattedMessage
                  id="channel.allocation.detail.add.channel.text"
                  defaultMessage="添加渠道"
                />
              </p>
            </div>
            {detailList?.map(item => {
              if (item.channelType == '1') {
                return (
                  // 邮件
                  <div className={styles.languageChannelItem}>
                    <img src={NewEmailIcon} />
                    <p className={styles.channelNameText}>
                      {item.channelName ? item.channelName : '--'}
                    </p>
                    <div className={styles.selectDetail}>
                      <div className={styles.detailSelectText}>
                        <FormattedMessage
                          id="channel.allocation.detail.select.email.account"
                          defaultMessage="Email账户："
                        />
                        {item.email ? item.email : '--'}
                      </div>
                    </div>
                    <div className={styles.operationItem}>
                      <HOCAuth authKey={'modify_channel'}>
                        {authAccess => (
                          <p
                            className={styles.itemsUpdate}
                            onClick={() =>
                              updataChannel(
                                item.channelId,
                                item.channelType,
                                item.connectInstanceId,
                              )
                            }
                            style={{
                              pointerEvents:
                                item.enableStatus === 0 ? 'none' : '',
                            }}
                          >
                            <img
                              src={
                                item.enableStatus === 0
                                  ? NewEditDisableIcon
                                  : NewEditIcon
                              }
                            />
                            <span
                              style={{
                                color: item.enableStatus === 0 ? '#999' : '',
                              }}
                            >
                              <FormattedMessage
                                id="update.channel"
                                defaultMessage="修改"
                              />
                            </span>
                          </p>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'delete_channel'}>
                        {authAccess => (
                          <Popconfirm
                            title={getIntl().formatMessage({
                              id: 'delete.channel.confirm',
                              defaultValue: '确定删除么？',
                            })}
                            onConfirm={() => deleteChannel(item.channelId)}
                            disabled={item.enableStatus === 0}
                            style={{
                              marginRight: '20px',
                            }}
                          >
                            <p
                              className={styles.itemsDelete}
                              style={{
                                pointerEvents:
                                  item.enableStatus === 0 ? 'none' : '',
                              }}
                            >
                              <img
                                src={
                                  item.enableStatus === 0
                                    ? NewDeleteDisableIcon
                                    : NewDeleteIcon
                                }
                              />
                              <span
                                style={{
                                  color: item.enableStatus === 0 ? '#999' : '',
                                }}
                              >
                                <FormattedMessage
                                  id="delete.channel"
                                  defaultMessage="删除"
                                />
                              </span>
                            </p>
                          </Popconfirm>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'disable_channel'}>
                        {authAccess => (
                          <Switch
                            checkedChildren={getIntl().formatMessage({
                              id: 'disable.channel',
                              defaultValue: '禁用',
                            })}
                            unCheckedChildren={getIntl().formatMessage({
                              id: 'enable.channel',
                              defaultValue: '启用',
                            })}
                            defaultChecked={item.enableStatus}
                            onChange={() =>
                              handleEnableDisable(
                                item.channelId,
                                item.enableStatus === 1 ? 0 : 1,
                              )
                            }
                          />
                        )}
                      </HOCAuth>
                    </div>
                  </div>
                );
              } else if (
                item.channelType == '8' ||
                item.channelType == '9' ||
                item.channelType == '10' ||
                item.channelType == '11' ||
                item.channelType == '17' ||
                item.channelType == '18' ||
                item.channelType == '21' ||
                item.channelType == '22'
              ) {
                let icon = null;
                if (item.channelType == '8') {
                  icon = NewWebChatIcon;
                } else if (item.channelType == '9') {
                  icon = NewAppChatIcon;
                } else if (item.channelType == '10') {
                  icon = NewWebOnlineVideoIcon;
                } else if (item.channelType == '11') {
                  icon = NewAppOnlineVideoIcon;
                } else if (item.channelType == '17') {
                  icon = NewWebOnlineVoiceIcon;
                } else if (item.channelType == '18') {
                  icon = NewAppOnlineVoiceIcon;
                } else if (item.channelType == '21') {
                  icon = NewWeChatMiniProgramIcon;
                } else if (item.channelType == '22') {
                  icon = NewShopifyIcon;
                }
                return (
                  <div className={styles.languageChannelItem}>
                    <img src={icon} />
                    <p className={styles.channelNameText}>
                      {item.channelName ? item.channelName : '--'}
                    </p>
                    <div className={styles.selectDetail}>
                      <div className={styles.detailSelectText}>
                        <FormattedMessage
                          id="channel.allocation.detail.select.language"
                          defaultMessage="语言："
                        />
                        {item.language ? item.language : '--'}
                      </div>
                      <div className={styles.detailSelectText}>
                        <FormattedMessage
                          id="channel.allocation.detail.select.website.domain.name"
                          defaultMessage="网站域名："
                        />
                        {item.websiteDomainName ? item.websiteDomainName : '--'}
                      </div>
                    </div>
                    <div className={styles.operationItem}>
                      <HOCAuth authKey={'channel_deployment'}>
                        {authAccess => (
                          <p
                            className={styles.itemsArrange}
                            onClick={() =>
                              deployChannel(item.channelId, item.channelType)
                            }
                            style={{
                              pointerEvents:
                                item.enableStatus === 0 ? 'none' : '',
                            }}
                          >
                            <img
                              src={
                                item.enableStatus === 0
                                  ? NewArrangeDisableIcon
                                  : NewArrangeIcon
                              }
                            />
                            <span
                              style={{
                                color: item.enableStatus === 0 ? '#999' : '',
                              }}
                            >
                              <FormattedMessage
                                id="statistics.data.details.channel.operation.arrange"
                                defaultMessage="部署"
                              />
                            </span>
                          </p>
                        )}
                      </HOCAuth>

                      <HOCAuth authKey={'modify_channel'}>
                        {authAccess => (
                          <p
                            className={styles.itemsUpdate}
                            onClick={() =>
                              updataChannel(
                                item.channelId,
                                item.channelType,
                                item.connectInstanceId,
                              )
                            }
                            style={{
                              pointerEvents:
                                item.enableStatus === 0 ? 'none' : '',
                            }}
                          >
                            <img
                              src={
                                item.enableStatus === 0
                                  ? NewEditDisableIcon
                                  : NewEditIcon
                              }
                            />
                            <span
                              style={{
                                color: item.enableStatus === 0 ? '#999' : '',
                              }}
                            >
                              <FormattedMessage
                                id="update.channel"
                                defaultMessage="修改"
                              />
                            </span>
                          </p>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'delete_channel'}>
                        {authAccess => (
                          <Popconfirm
                            title={getIntl().formatMessage({
                              id: 'delete.channel.confirm',
                              defaultValue: '确定删除么？',
                            })}
                            onConfirm={() => deleteChannel(item.channelId)}
                            disabled={item.enableStatus === 0}
                            style={{
                              marginRight: '20px',
                            }}
                          >
                            <p
                              className={styles.itemsDelete}
                              style={{
                                pointerEvents:
                                  item.enableStatus === 0 ? 'none' : '',
                              }}
                            >
                              <img
                                src={
                                  item.enableStatus === 0
                                    ? NewDeleteDisableIcon
                                    : NewDeleteIcon
                                }
                              />
                              <span
                                style={{
                                  color: item.enableStatus === 0 ? '#999' : '',
                                }}
                              >
                                <FormattedMessage
                                  id="delete.channel"
                                  defaultMessage="删除"
                                />
                              </span>
                            </p>
                          </Popconfirm>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'disable_channel'}>
                        {authAccess => (
                          <Switch
                            checkedChildren={getIntl().formatMessage({
                              id: 'disable.channel',
                              defaultValue: '禁用',
                            })}
                            unCheckedChildren={getIntl().formatMessage({
                              id: 'enable.channel',
                              defaultValue: '启用',
                            })}
                            defaultChecked={item.enableStatus}
                            onChange={() =>
                              handleEnableDisable(
                                item.channelId,
                                item.enableStatus === 1 ? 0 : 1,
                              )
                            }
                          />
                        )}
                      </HOCAuth>
                    </div>
                  </div>
                );
              } else if (item.channelType == '3') {
                return (
                  <div className={styles.languageChannelItem}>
                    <img src={NewFaceBookIcon} />
                    <p className={styles.channelNameText}>
                      {item.channelName ? item.channelName : '--'}
                    </p>
                    <div className={styles.selectDetail}>
                      <div className={styles.detailSelectText1}>
                        <FormattedMessage
                          id="channel.allocation.detail.select.account.name"
                          defaultMessage="账号名称："
                        />
                        {item.facebookName ? item.facebookName : '--'}
                      </div>
                      <div className={styles.detailSelectText2}>
                        <FormattedMessage
                          id="channel.allocation.detail.select.account.id"
                          defaultMessage="账号ID："
                        />
                        {item.facebookId ? item.facebookId : '--'}
                      </div>
                    </div>
                    <div className={styles.operationItem}>
                      <HOCAuth authKey={'modify_channel'}>
                        {authAccess => (
                          <p
                            className={styles.itemsUpdate}
                            onClick={() =>
                              updataChannel(
                                item.channelId,
                                item.channelType,
                                item.connectInstanceId,
                              )
                            }
                            style={{
                              pointerEvents:
                                item.enableStatus === 0 ? 'none' : '',
                            }}
                          >
                            <img
                              src={
                                item.enableStatus === 0
                                  ? NewEditDisableIcon
                                  : NewEditIcon
                              }
                            />
                            <span
                              style={{
                                color: item.enableStatus === 0 ? '#999' : '',
                              }}
                            >
                              <FormattedMessage
                                id="update.channel"
                                defaultMessage="修改"
                              />
                            </span>
                          </p>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'delete_channel'}>
                        {authAccess => (
                          <Popconfirm
                            title={getIntl().formatMessage({
                              id: 'delete.channel.confirm',
                              defaultValue: '确定删除么？',
                            })}
                            onConfirm={() => deleteChannel(item.channelId)}
                            disabled={item.enableStatus === 0}
                            style={{
                              marginRight: '20px',
                            }}
                          >
                            <p
                              className={styles.itemsDelete}
                              style={{
                                pointerEvents:
                                  item.enableStatus === 0 ? 'none' : '',
                              }}
                            >
                              <img
                                src={
                                  item.enableStatus === 0
                                    ? NewDeleteDisableIcon
                                    : NewDeleteIcon
                                }
                              />
                              <span
                                style={{
                                  color: item.enableStatus === 0 ? '#999' : '',
                                }}
                              >
                                <FormattedMessage
                                  id="delete.channel"
                                  defaultMessage="删除"
                                />
                              </span>
                            </p>
                          </Popconfirm>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'disable_channel'}>
                        {authAccess => (
                          <Switch
                            checkedChildren={getIntl().formatMessage({
                              id: 'disable.channel',
                              defaultValue: '禁用',
                            })}
                            unCheckedChildren={getIntl().formatMessage({
                              id: 'enable.channel',
                              defaultValue: '启用',
                            })}
                            defaultChecked={item.enableStatus}
                            onChange={() =>
                              handleEnableDisable(
                                item.channelId,
                                item.enableStatus === 1 ? 0 : 1,
                              )
                            }
                          />
                        )}
                      </HOCAuth>
                    </div>
                  </div>
                );
              } else if (item.channelType == '4') {
                return (
                  <div className={styles.languageChannelItem}>
                    <img src={NewWhatsAppIcon} />
                    <p className={styles.channelNameText}>
                      {item.channelName ? item.channelName : '--'}
                    </p>
                    <div className={styles.selectDetail}>
                      <div className={styles.detailSelectText}>
                        <FormattedMessage
                          id="channel.allocation.detail.select.bind.phone.number"
                          defaultMessage="绑定电话："
                        />
                        {item.phone ? item.phone : '--'}
                      </div>
                    </div>
                    <div className={styles.operationItem}>
                      <HOCAuth authKey={'modify_channel'}>
                        {authAccess => (
                          <p
                            className={styles.itemsUpdate}
                            onClick={() =>
                              updataChannel(
                                item.channelId,
                                item.channelType,
                                item.connectInstanceId,
                              )
                            }
                            style={{
                              pointerEvents:
                                item.enableStatus === 0 ? 'none' : '',
                            }}
                          >
                            <img
                              src={
                                item.enableStatus === 0
                                  ? NewEditDisableIcon
                                  : NewEditIcon
                              }
                            />
                            <span
                              style={{
                                color: item.enableStatus === 0 ? '#999' : '',
                              }}
                            >
                              <FormattedMessage
                                id="update.channel"
                                defaultMessage="修改"
                              />
                            </span>
                          </p>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'delete_channel'}>
                        {authAccess => (
                          <Popconfirm
                            title={getIntl().formatMessage({
                              id: 'delete.channel.confirm',
                              defaultValue: '确定删除么？',
                            })}
                            onConfirm={() => deleteChannel(item.channelId)}
                            disabled={item.enableStatus === 0}
                            style={{
                              marginRight: '20px',
                            }}
                          >
                            <p
                              className={styles.itemsDelete}
                              style={{
                                pointerEvents:
                                  item.enableStatus === 0 ? 'none' : '',
                              }}
                            >
                              <img
                                src={
                                  item.enableStatus === 0
                                    ? NewDeleteDisableIcon
                                    : NewDeleteIcon
                                }
                              />
                              <span
                                style={{
                                  color: item.enableStatus === 0 ? '#999' : '',
                                }}
                              >
                                <FormattedMessage
                                  id="delete.channel"
                                  defaultMessage="删除"
                                />
                              </span>
                            </p>
                          </Popconfirm>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'disable_channel'}>
                        {authAccess => (
                          <Switch
                            checkedChildren={getIntl().formatMessage({
                              id: 'disable.channel',
                              defaultValue: '禁用',
                            })}
                            unCheckedChildren={getIntl().formatMessage({
                              id: 'enable.channel',
                              defaultValue: '启用',
                            })}
                            defaultChecked={item.enableStatus}
                            onChange={() =>
                              handleEnableDisable(
                                item.channelId,
                                item.enableStatus === 1 ? 0 : 1,
                              )
                            }
                          />
                        )}
                      </HOCAuth>
                    </div>
                  </div>
                );
              } else if (item.channelType == '7') {
                return (
                  <div className={styles.languageChannelItem}>
                    <img src={NewPhoneIcon} />
                    <p className={styles.channelNameText}>
                      {item.channelName ? item.channelName : '--'}
                    </p>
                    <div className={styles.selectDetail}>
                      <div className={styles.detailSelectText}>
                        <FormattedMessage
                          id="channel.allocation.detail.select.phone.number"
                          defaultMessage="电话号码："
                        />
                        {item.phone ? item.phone : '--'}
                      </div>
                    </div>
                    <div className={styles.operationItem}>
                      <HOCAuth authKey={'channel_deployment'}>
                        {authAccess => (
                          <p
                            className={styles.itemsArrange}
                            onClick={() =>
                              deployChannel(item.channelId, item.channelType)
                            }
                            style={{
                              pointerEvents:
                                item.enableStatus === 0 ? 'none' : '',
                            }}
                          >
                            <img
                              src={
                                item.enableStatus === 0
                                  ? NewArrangeDisableIcon
                                  : NewArrangeIcon
                              }
                            />
                            <span
                              style={{
                                color: item.enableStatus === 0 ? '#999' : '',
                              }}
                            >
                              <FormattedMessage
                                id="statistics.data.details.channel.operation.arrange"
                                defaultMessage="部署"
                              />
                            </span>
                          </p>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'modify_channel'}>
                        {authAccess => (
                          <p
                            className={styles.itemsUpdate}
                            onClick={() =>
                              updataChannel(
                                item.channelId,
                                item.channelType,
                                item.connectInstanceId,
                              )
                            }
                            style={{
                              pointerEvents:
                                item.enableStatus === 0 ? 'none' : '',
                            }}
                          >
                            <img
                              src={
                                item.enableStatus === 0
                                  ? NewEditDisableIcon
                                  : NewEditIcon
                              }
                            />
                            <span
                              style={{
                                color: item.enableStatus === 0 ? '#999' : '',
                              }}
                            >
                              <FormattedMessage
                                id="update.channel"
                                defaultMessage="修改"
                              />
                            </span>
                          </p>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'delete_channel'}>
                        {authAccess => (
                          <Popconfirm
                            title={getIntl().formatMessage({
                              id: 'delete.channel.confirm',
                              defaultValue: '确定删除么？',
                            })}
                            onConfirm={() => deleteChannel(item.channelId)}
                            disabled={item.enableStatus === 0}
                            style={{
                              marginRight: '20px',
                            }}
                          >
                            <p
                              className={styles.itemsDelete}
                              style={{
                                pointerEvents:
                                  item.enableStatus === 0 ? 'none' : '',
                              }}
                            >
                              <img
                                src={
                                  item.enableStatus === 0
                                    ? NewDeleteDisableIcon
                                    : NewDeleteIcon
                                }
                              />
                              <span
                                style={{
                                  color: item.enableStatus === 0 ? '#999' : '',
                                }}
                              >
                                <FormattedMessage
                                  id="delete.channel"
                                  defaultMessage="删除"
                                />
                              </span>
                            </p>
                          </Popconfirm>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'disable_channel'}>
                        {authAccess => (
                          <Switch
                            checkedChildren={getIntl().formatMessage({
                              id: 'disable.channel',
                              defaultValue: '禁用',
                            })}
                            unCheckedChildren={getIntl().formatMessage({
                              id: 'enable.channel',
                              defaultValue: '启用',
                            })}
                            defaultChecked={item.enableStatus}
                            onChange={() =>
                              handleEnableDisable(
                                item.channelId,
                                item.enableStatus === 1 ? 0 : 1,
                              )
                            }
                          />
                        )}
                      </HOCAuth>
                    </div>
                  </div>
                );
              } else if (item.channelType == '12') {
                return (
                  <div className={styles.languageChannelItem}>
                    <img src={NewAmazonMessageIcon} />
                    <p className={styles.channelNameText}>
                      {item.channelName ? item.channelName : '--'}
                    </p>
                    <div className={styles.selectDetail}>
                      <div className={styles.detailSelectText}>
                        <FormattedMessage
                          id="channel.allocation.detail.select.seller.region"
                          defaultMessage="卖家地区："
                        />
                        {item.sellerAddress
                          ? amazonRegionList?.filter(
                              itemSon => item.sellerAddress === itemSon.id,
                            )[0]?.countryName || '--'
                          : '--'}
                      </div>
                    </div>
                    <div className={styles.operationItem}>
                      <HOCAuth authKey={'modify_channel'}>
                        {authAccess => (
                          <p
                            className={styles.itemsUpdate}
                            onClick={() =>
                              updataChannel(
                                item.channelId,
                                item.channelType,
                                item.connectInstanceId,
                              )
                            }
                            style={{
                              pointerEvents:
                                item.enableStatus === 0 ? 'none' : '',
                            }}
                          >
                            <img
                              src={
                                item.enableStatus === 0
                                  ? NewEditDisableIcon
                                  : NewEditIcon
                              }
                            />
                            <span
                              style={{
                                color: item.enableStatus === 0 ? '#999' : '',
                              }}
                            >
                              <FormattedMessage
                                id="update.channel"
                                defaultMessage="修改"
                              />
                            </span>
                          </p>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'delete_channel'}>
                        {authAccess => (
                          <Popconfirm
                            title={getIntl().formatMessage({
                              id: 'delete.channel.confirm',
                              defaultValue: '确定删除么？',
                            })}
                            onConfirm={() => deleteChannel(item.channelId)}
                            disabled={item.enableStatus === 0}
                            style={{
                              marginRight: '20px',
                            }}
                          >
                            <p
                              className={styles.itemsDelete}
                              style={{
                                pointerEvents:
                                  item.enableStatus === 0 ? 'none' : '',
                              }}
                            >
                              <img
                                src={
                                  item.enableStatus === 0
                                    ? NewDeleteDisableIcon
                                    : NewDeleteIcon
                                }
                              />
                              <span
                                style={{
                                  color: item.enableStatus === 0 ? '#999' : '',
                                }}
                              >
                                <FormattedMessage
                                  id="delete.channel"
                                  defaultMessage="删除"
                                />
                              </span>
                            </p>
                          </Popconfirm>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'disable_channel'}>
                        {authAccess => (
                          <Switch
                            checkedChildren={getIntl().formatMessage({
                              id: 'disable.channel',
                              defaultValue: '禁用',
                            })}
                            unCheckedChildren={getIntl().formatMessage({
                              id: 'enable.channel',
                              defaultValue: '启用',
                            })}
                            defaultChecked={item.enableStatus}
                            onChange={() =>
                              handleEnableDisable(
                                item.channelId,
                                item.enableStatus === 1 ? 0 : 1,
                              )
                            }
                          />
                        )}
                      </HOCAuth>
                    </div>
                  </div>
                );
              } else if (item.channelType == '13') {
                return (
                  <div className={styles.languageChannelItem}>
                    <img src={NewInstagramIcon} />
                    <p className={styles.channelNameText}>
                      {item.channelName ? item.channelName : '--'}
                    </p>
                    <div className={styles.selectDetail}>
                      <div className={styles.detailSelectText1}>
                        <FormattedMessage
                          id="channel.allocation.detail.select.account.name"
                          defaultMessage="账号名称："
                        />
                        {item.instagramName ? item.instagramName : '--'}
                      </div>
                      <div className={styles.detailSelectText2}>
                        <FormattedMessage
                          id="channel.allocation.detail.select.account.id"
                          defaultMessage="账号ID："
                        />
                        {item.instagramId ? item.instagramId : '--'}
                      </div>
                    </div>
                    <div className={styles.operationItem}>
                      <HOCAuth authKey={'modify_channel'}>
                        {authAccess => (
                          <p
                            className={styles.itemsUpdate}
                            onClick={() =>
                              updataChannel(
                                item.channelId,
                                item.channelType,
                                item.connectInstanceId,
                              )
                            }
                            style={{
                              pointerEvents:
                                item.enableStatus === 0 ? 'none' : '',
                            }}
                          >
                            <img
                              src={
                                item.enableStatus === 0
                                  ? NewEditDisableIcon
                                  : NewEditIcon
                              }
                            />
                            <span
                              style={{
                                color: item.enableStatus === 0 ? '#999' : '',
                              }}
                            >
                              <FormattedMessage
                                id="update.channel"
                                defaultMessage="修改"
                              />
                            </span>
                          </p>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'delete_channel'}>
                        {authAccess => (
                          <Popconfirm
                            title={getIntl().formatMessage({
                              id: 'delete.channel.confirm',
                              defaultValue: '确定删除么？',
                            })}
                            onConfirm={() => deleteChannel(item.channelId)}
                            disabled={item.enableStatus === 0}
                            style={{
                              marginRight: '20px',
                            }}
                          >
                            <p
                              className={styles.itemsDelete}
                              style={{
                                pointerEvents:
                                  item.enableStatus === 0 ? 'none' : '',
                              }}
                            >
                              <img
                                src={
                                  item.enableStatus === 0
                                    ? NewDeleteDisableIcon
                                    : NewDeleteIcon
                                }
                              />
                              <span
                                style={{
                                  color: item.enableStatus === 0 ? '#999' : '',
                                }}
                              >
                                <FormattedMessage
                                  id="delete.channel"
                                  defaultMessage="删除"
                                />
                              </span>
                            </p>
                          </Popconfirm>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'disable_channel'}>
                        {authAccess => (
                          <Switch
                            checkedChildren={getIntl().formatMessage({
                              id: 'disable.channel',
                              defaultValue: '禁用',
                            })}
                            unCheckedChildren={getIntl().formatMessage({
                              id: 'enable.channel',
                              defaultValue: '启用',
                            })}
                            defaultChecked={item.enableStatus}
                            onChange={() =>
                              handleEnableDisable(
                                item.channelId,
                                item.enableStatus === 1 ? 0 : 1,
                              )
                            }
                          />
                        )}
                      </HOCAuth>
                    </div>
                  </div>
                );
              } else if (item.channelType == '14') {
                return (
                  <div className={styles.languageChannelItem}>
                    <img src={NewLineIcon} />
                    <p className={styles.channelNameText}>
                      {item.channelName ? item.channelName : '--'}
                    </p>
                    <div className={styles.selectDetail}>
                      <div className={styles.detailSelectText}>
                        <FormattedMessage
                          id="channel.allocation.detail.select.line.id"
                          defaultMessage="Line ID："
                        />
                        {item.lineId ? item.lineId : '--'}
                      </div>
                    </div>
                    <div className={styles.operationItem}>
                      <p
                        className={styles.itemsArrange}
                        onClick={() =>
                          queryPartConfig(item.channelType, item.channelId)
                        }
                        style={{
                          pointerEvents: item.enableStatus === 0 ? 'none' : '',
                        }}
                      >
                        <img
                          src={
                            item.enableStatus === 0
                              ? NewArrangeDisableIcon
                              : NewArrangeIcon
                          }
                        />
                        <span
                          style={{
                            color: item.enableStatus === 0 ? '#999' : '',
                          }}
                        >
                          <FormattedMessage
                            id="statistics.data.details.channel.configuration.information"
                            defaultMessage="配置信息"
                          />
                        </span>
                      </p>
                      <HOCAuth authKey={'modify_channel'}>
                        {authAccess => (
                          <p
                            className={styles.itemsUpdate}
                            onClick={() =>
                              updataChannel(
                                item.channelId,
                                item.channelType,
                                item.connectInstanceId,
                              )
                            }
                            style={{
                              pointerEvents:
                                item.enableStatus === 0 ? 'none' : '',
                            }}
                          >
                            <img
                              src={
                                item.enableStatus === 0
                                  ? NewEditDisableIcon
                                  : NewEditIcon
                              }
                            />
                            <span
                              style={{
                                color: item.enableStatus === 0 ? '#999' : '',
                              }}
                            >
                              <FormattedMessage
                                id="update.channel"
                                defaultMessage="修改"
                              />
                            </span>
                          </p>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'delete_channel'}>
                        {authAccess => (
                          <Popconfirm
                            title={getIntl().formatMessage({
                              id: 'delete.channel.confirm',
                              defaultValue: '确定删除么？',
                            })}
                            onConfirm={() => deleteChannel(item.channelId)}
                            disabled={item.enableStatus === 0}
                            style={{
                              marginRight: '20px',
                            }}
                          >
                            <p
                              className={styles.itemsDelete}
                              style={{
                                pointerEvents:
                                  item.enableStatus === 0 ? 'none' : '',
                              }}
                            >
                              <img
                                src={
                                  item.enableStatus === 0
                                    ? NewDeleteDisableIcon
                                    : NewDeleteIcon
                                }
                              />
                              <span
                                style={{
                                  color: item.enableStatus === 0 ? '#999' : '',
                                }}
                              >
                                <FormattedMessage
                                  id="delete.channel"
                                  defaultMessage="删除"
                                />
                              </span>
                            </p>
                          </Popconfirm>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'disable_channel'}>
                        {authAccess => (
                          <Switch
                            checkedChildren={getIntl().formatMessage({
                              id: 'disable.channel',
                              defaultValue: '禁用',
                            })}
                            unCheckedChildren={getIntl().formatMessage({
                              id: 'enable.channel',
                              defaultValue: '启用',
                            })}
                            defaultChecked={item.enableStatus}
                            onChange={() =>
                              handleEnableDisable(
                                item.channelId,
                                item.enableStatus === 1 ? 0 : 1,
                              )
                            }
                          />
                        )}
                      </HOCAuth>
                    </div>
                  </div>
                );
              } else if (item.channelType == '15') {
                return (
                  <div className={styles.languageChannelItem}>
                    <img src={NewWeComIcon} />
                    <p className={styles.channelNameText}>
                      {item.channelName ? item.channelName : '--'}
                    </p>
                    <div className={styles.selectDetail}>
                      <div className={styles.detailSelectText1}>
                        <FormattedMessage
                          id="channel.allocation.detail.select.enterprise.name"
                          defaultMessage="企业全称："
                        />
                        {item.enterpriseName ? item.enterpriseName : '--'}
                      </div>
                      <div className={styles.detailSelectText2}>
                        <FormattedMessage
                          id="channel.allocation.detail.select.enterprise.id"
                          defaultMessage="客服名称："
                        />
                        {item.customerName ? item.customerName : '--'}
                      </div>
                    </div>
                    <div className={styles.operationItem}>
                      <HOCAuth authKey={'channel_deployment'}>
                        {authAccess => (
                          <p
                            className={styles.itemsArrange}
                            onClick={() =>
                              queryPartConfig(item.channelType, item.channelId)
                            }
                            style={{
                              pointerEvents:
                                item.enableStatus === 0 ? 'none' : '',
                            }}
                          >
                            <img
                              src={
                                item.enableStatus === 0
                                  ? NewArrangeDisableIcon
                                  : NewArrangeIcon
                              }
                            />
                            <span
                              style={{
                                color: item.enableStatus === 0 ? '#999' : '',
                              }}
                            >
                              <FormattedMessage
                                id="channel.allocation.detail.access.link"
                                defaultMessage="接入链接"
                              />
                            </span>
                          </p>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'modify_channel'}>
                        {authAccess => (
                          <p
                            className={styles.itemsUpdate}
                            onClick={() =>
                              updataChannel(
                                item.channelId,
                                item.channelType,
                                item.connectInstanceId,
                              )
                            }
                            style={{
                              pointerEvents:
                                item.enableStatus === 0 ? 'none' : '',
                            }}
                          >
                            <img
                              src={
                                item.enableStatus === 0
                                  ? NewEditDisableIcon
                                  : NewEditIcon
                              }
                            />
                            <span
                              style={{
                                color: item.enableStatus === 0 ? '#999' : '',
                              }}
                            >
                              <FormattedMessage
                                id="update.channel"
                                defaultMessage="修改"
                              />
                            </span>
                          </p>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'delete_channel'}>
                        {authAccess => (
                          <Popconfirm
                            title={getIntl().formatMessage({
                              id: 'delete.channel.confirm',
                              defaultValue: '确定删除么？',
                            })}
                            onConfirm={() => deleteChannel(item.channelId)}
                            disabled={item.enableStatus === 0}
                            style={{
                              marginRight: '20px',
                            }}
                          >
                            <p
                              className={styles.itemsDelete}
                              style={{
                                pointerEvents:
                                  item.enableStatus === 0 ? 'none' : '',
                              }}
                            >
                              <img
                                src={
                                  item.enableStatus === 0
                                    ? NewDeleteDisableIcon
                                    : NewDeleteIcon
                                }
                              />
                              <span
                                style={{
                                  color: item.enableStatus === 0 ? '#999' : '',
                                }}
                              >
                                <FormattedMessage
                                  id="delete.channel"
                                  defaultMessage="删除"
                                />
                              </span>
                            </p>
                          </Popconfirm>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'disable_channel'}>
                        {authAccess => (
                          <Switch
                            checkedChildren={getIntl().formatMessage({
                              id: 'disable.channel',
                              defaultValue: '禁用',
                            })}
                            unCheckedChildren={getIntl().formatMessage({
                              id: 'enable.channel',
                              defaultValue: '启用',
                            })}
                            defaultChecked={item.enableStatus}
                            onChange={() =>
                              handleEnableDisable(
                                item.channelId,
                                item.enableStatus === 1 ? 0 : 1,
                              )
                            }
                          />
                        )}
                      </HOCAuth>
                    </div>
                  </div>
                );
              } else if (item.channelType == '16') {
                return (
                  <div className={styles.languageChannelItem}>
                    <img src={NewWechatOfficialAccountIcon} />
                    <p className={styles.channelNameText}>
                      {item.channelName ? item.channelName : '--'}
                    </p>
                    <div className={styles.selectDetail}>
                      <div className={styles.detailSelectText}>
                        <FormattedMessage
                          id="channel.allocation.detail.select.app.id"
                          defaultMessage="APP ID："
                        />
                        {item.appId ? item.appId : '--'}
                      </div>
                    </div>
                    <div className={styles.operationItem}>
                      <p
                        onClick={() =>
                          queryPartConfig(item.channelType, item.channelId)
                        }
                        className={styles.itemsArrange}
                        style={{
                          pointerEvents: item.enableStatus === 0 ? 'none' : '',
                        }}
                      >
                        <img
                          src={
                            item.enableStatus === 0
                              ? NewArrangeDisableIcon
                              : NewArrangeIcon
                          }
                        />
                        <span
                          style={{
                            color: item.enableStatus === 0 ? '#999' : '',
                          }}
                        >
                          <FormattedMessage
                            id="statistics.data.details.channel.configuration.information"
                            defaultMessage="配置信息"
                          />
                        </span>
                      </p>
                      <HOCAuth authKey={'modify_channel'}>
                        {authAccess => (
                          <p
                            className={styles.itemsUpdate}
                            onClick={() =>
                              updataChannel(
                                item.channelId,
                                item.channelType,
                                item.connectInstanceId,
                              )
                            }
                            style={{
                              pointerEvents:
                                item.enableStatus === 0 ? 'none' : '',
                            }}
                          >
                            <img
                              src={
                                item.enableStatus === 0
                                  ? NewEditDisableIcon
                                  : NewEditIcon
                              }
                            />
                            <span
                              style={{
                                color: item.enableStatus === 0 ? '#999' : '',
                              }}
                            >
                              <FormattedMessage
                                id="update.channel"
                                defaultMessage="修改"
                              />
                            </span>
                          </p>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'delete_channel'}>
                        {authAccess => (
                          <Popconfirm
                            title={getIntl().formatMessage({
                              id: 'delete.channel.confirm',
                              defaultValue: '确定删除么？',
                            })}
                            onConfirm={() => deleteChannel(item.channelId)}
                            disabled={item.enableStatus === 0}
                            style={{
                              marginRight: '20px',
                            }}
                          >
                            <p
                              className={styles.itemsDelete}
                              style={{
                                pointerEvents:
                                  item.enableStatus === 0 ? 'none' : '',
                              }}
                            >
                              <img
                                src={
                                  item.enableStatus === 0
                                    ? NewDeleteDisableIcon
                                    : NewDeleteIcon
                                }
                              />
                              <span
                                style={{
                                  color: item.enableStatus === 0 ? '#999' : '',
                                }}
                              >
                                <FormattedMessage
                                  id="delete.channel"
                                  defaultMessage="删除"
                                />
                              </span>
                            </p>
                          </Popconfirm>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'disable_channel'}>
                        {authAccess => (
                          <Switch
                            checkedChildren={getIntl().formatMessage({
                              id: 'disable.channel',
                              defaultValue: '禁用',
                            })}
                            unCheckedChildren={getIntl().formatMessage({
                              id: 'enable.channel',
                              defaultValue: '启用',
                            })}
                            defaultChecked={item.enableStatus}
                            onChange={() =>
                              handleEnableDisable(
                                item.channelId,
                                item.enableStatus === 1 ? 0 : 1,
                              )
                            }
                          />
                        )}
                      </HOCAuth>
                    </div>
                  </div>
                );
              } else if (item.channelType == '25') {
                return (
                  <div className={styles.languageChannelItem}>
                    <img src={NewDiscordIcon} />
                    <p className={styles.channelNameText}>
                      {item.channelName ? item.channelName : '--'}
                    </p>
                    <div className={styles.selectDetail}>
                      <div className={styles.detailSelectText}>
                        <FormattedMessage
                          id="channel.allocation.detail.input.bot.name"
                          defaultMessage="机器人名称："
                        />
                        {item.botName ? item.botName : '--'}
                      </div>
                    </div>
                    <div className={styles.operationItem}>
                      <HOCAuth authKey={'modify_channel'}>
                        {authAccess => (
                          <p
                            className={styles.itemsUpdate}
                            onClick={() =>
                              updataChannel(
                                item.channelId,
                                item.channelType,
                                item.connectInstanceId,
                              )
                            }
                            style={{
                              pointerEvents:
                                item.enableStatus === 0 ? 'none' : '',
                            }}
                          >
                            <img
                              src={
                                item.enableStatus === 0
                                  ? NewEditDisableIcon
                                  : NewEditIcon
                              }
                            />
                            <span
                              style={{
                                color: item.enableStatus === 0 ? '#999' : '',
                              }}
                            >
                              <FormattedMessage
                                id="update.channel"
                                defaultMessage="修改"
                              />
                            </span>
                          </p>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'delete_channel'}>
                        {authAccess => (
                          <Popconfirm
                            title={getIntl().formatMessage({
                              id: 'delete.channel.confirm',
                              defaultValue: '确定删除么？',
                            })}
                            onConfirm={() => deleteChannel(item.channelId)}
                            disabled={item.enableStatus === 0}
                            style={{
                              marginRight: '20px',
                            }}
                          >
                            <p
                              className={styles.itemsDelete}
                              style={{
                                pointerEvents:
                                  item.enableStatus === 0 ? 'none' : '',
                              }}
                            >
                              <img
                                src={
                                  item.enableStatus === 0
                                    ? NewDeleteDisableIcon
                                    : NewDeleteIcon
                                }
                              />
                              <span
                                style={{
                                  color: item.enableStatus === 0 ? '#999' : '',
                                }}
                              >
                                <FormattedMessage
                                  id="delete.channel"
                                  defaultMessage="删除"
                                />
                              </span>
                            </p>
                          </Popconfirm>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'disable_channel'}>
                        {authAccess => (
                          <Switch
                            checkedChildren={getIntl().formatMessage({
                              id: 'disable.channel',
                              defaultValue: '禁用',
                            })}
                            unCheckedChildren={getIntl().formatMessage({
                              id: 'enable.channel',
                              defaultValue: '启用',
                            })}
                            defaultChecked={item.enableStatus}
                            onChange={() =>
                              handleEnableDisable(
                                item.channelId,
                                item.enableStatus === 1 ? 0 : 1,
                              )
                            }
                          />
                        )}
                      </HOCAuth>
                    </div>
                  </div>
                );
              } else if (item.channelType == '24') {
                return (
                  <div className={styles.languageChannelItem}>
                    <img src={NewTikTokIcon} />
                    <p className={styles.channelNameText}>
                      {item.channelName ? item.channelName : '--'}
                    </p>
                    <div className={styles.selectDetail}>
                      <div
                        className={styles.detailSelectText1}
                        style={{
                          maxWidth: '88%',
                        }}
                      >
                        <FormattedMessage
                          id="channel.allocation.detail.select.account.tiktok.shopName"
                          defaultMessage="店铺名称："
                        />
                        {item.shopName ? item.shopName : '--'}
                      </div>
                    </div>
                    <div className={styles.operationItem}>
                      <HOCAuth authKey={'modify_channel'}>
                        {authAccess => (
                          <p
                            className={styles.itemsUpdate}
                            onClick={() =>
                              updataChannel(
                                item.channelId,
                                item.channelType,
                                item.connectInstanceId,
                              )
                            }
                            style={{
                              pointerEvents:
                                item.enableStatus === 0 ? 'none' : '',
                            }}
                          >
                            <img
                              src={
                                item.enableStatus === 0
                                  ? NewEditDisableIcon
                                  : NewEditIcon
                              }
                            />
                            <span
                              style={{
                                color: item.enableStatus === 0 ? '#999' : '',
                              }}
                            >
                              <FormattedMessage
                                id="update.channel"
                                defaultMessage="修改"
                              />
                            </span>
                          </p>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'delete_channel'}>
                        {authAccess => (
                          <Popconfirm
                            title={getIntl().formatMessage({
                              id: 'delete.channel.confirm',
                              defaultValue: '确定删除么？',
                            })}
                            onConfirm={() => deleteChannel(item.channelId)}
                            disabled={item.enableStatus === 0}
                            style={{
                              marginRight: '20px',
                            }}
                          >
                            <p
                              className={styles.itemsDelete}
                              style={{
                                pointerEvents:
                                  item.enableStatus === 0 ? 'none' : '',
                              }}
                            >
                              <img
                                src={
                                  item.enableStatus === 0
                                    ? NewDeleteDisableIcon
                                    : NewDeleteIcon
                                }
                              />
                              <span
                                style={{
                                  color: item.enableStatus === 0 ? '#999' : '',
                                }}
                              >
                                <FormattedMessage
                                  id="delete.channel"
                                  defaultMessage="删除"
                                />
                              </span>
                            </p>
                          </Popconfirm>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'disable_channel'}>
                        {authAccess => (
                          <Switch
                            checkedChildren={getIntl().formatMessage({
                              id: 'disable.channel',
                              defaultValue: '禁用',
                            })}
                            unCheckedChildren={getIntl().formatMessage({
                              id: 'enable.channel',
                              defaultValue: '启用',
                            })}
                            defaultChecked={item.enableStatus}
                            onChange={() =>
                              handleEnableDisable(
                                item.channelId,
                                item.enableStatus === 1 ? 0 : 1,
                              )
                            }
                          />
                        )}
                      </HOCAuth>
                    </div>
                  </div>
                );
              } else {
                return (
                  <div className={styles.languageChannelItem}>
                    <img src={NewGooglePlayIcon} />
                    <p className={styles.channelNameText}>
                      {item.channelName ? item.channelName : '--'}
                    </p>

                    <div className={styles.operationItem}>
                      <p
                        className={styles.itemsArrange}
                        onClick={() => queryAllApps(item.channelId)}
                      >
                        <img
                          src={
                            item.enableStatus === 0
                              ? NewArrangeDisableIcon
                              : NewArrangeIcon
                          }
                        />
                        <span>
                          <FormattedMessage
                            id="statistics.data.details.channel.view.applications"
                            defaultMessage="查看所有应用"
                          />
                        </span>
                      </p>
                      <HOCAuth authKey={'modify_channel'}>
                        {authAccess => (
                          <p
                            className={styles.itemsUpdate}
                            onClick={() =>
                              updataChannel(
                                item.channelId,
                                item.channelType,
                                item.channelName,
                                item.gpSecret,
                              )
                            }
                            style={{
                              pointerEvents:
                                item.enableStatus === 0 ? 'none' : '',
                            }}
                          >
                            <img
                              src={
                                item.enableStatus === 0
                                  ? NewEditDisableIcon
                                  : NewEditIcon
                              }
                            />
                            <span
                              style={{
                                color: item.enableStatus === 0 ? '#999' : '',
                              }}
                            >
                              <FormattedMessage
                                id="update.channel"
                                defaultMessage="修改"
                              />
                            </span>
                          </p>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'delete_channel'}>
                        {authAccess => (
                          <Popconfirm
                            title={getIntl().formatMessage({
                              id: 'delete.channel.confirm',
                              defaultValue: '确定删除么？',
                            })}
                            onConfirm={() => deleteChannel(item, 'google')}
                            disabled={item.enableStatus === 0}
                            style={{
                              marginRight: '20px',
                            }}
                          >
                            <p
                              className={styles.itemsDelete}
                              style={{
                                pointerEvents:
                                  item.enableStatus === 0 ? 'none' : '',
                              }}
                            >
                              <img
                                src={
                                  item.enableStatus === 0
                                    ? NewDeleteDisableIcon
                                    : NewDeleteIcon
                                }
                              />
                              <span
                                style={{
                                  color: item.enableStatus === 0 ? '#999' : '',
                                }}
                              >
                                <FormattedMessage
                                  id="delete.channel"
                                  defaultMessage="删除"
                                />
                              </span>
                            </p>
                          </Popconfirm>
                        )}
                      </HOCAuth>
                      <HOCAuth authKey={'disable_channel'}>
                        {authAccess => (
                          <Switch
                            checkedChildren={getIntl().formatMessage({
                              id: 'disable.channel',
                              defaultValue: '禁用',
                            })}
                            unCheckedChildren={getIntl().formatMessage({
                              id: 'enable.channel',
                              defaultValue: '启用',
                            })}
                            defaultChecked={item.enableStatus}
                            onChange={() =>
                              handleEnableDisable(
                                item.channelId,
                                item.enableStatus === 1 ? 0 : 1,
                              )
                            }
                          />
                        )}
                      </HOCAuth>
                    </div>
                  </div>
                );
              }
            })}
          </div>
          <div className={styles.paginationContent}>
            <Pagination
              current={pageNum}
              total={total}
              pageSize={pageSize}
              pageSizeOptions={[5, 10, 15, 20]}
              showTotal={total => (
                <FormattedMessage
                  id="studentManagement.altogether"
                  defaultMessage={`共 ${total} 条`}
                  values={{ total }}
                />
              )}
              showSizeChanger
              onShowSizeChange={onShowSizeChange}
              onChange={onChangePage}
            />
          </div>
        </Spin>

        <Modal
          open={open}
          title=""
          onCancel={handleCancel}
          footer={false}
          width={'50vw'}
        >
          <div>
            <p style={{ fontSize: 20 }}>
              <FormattedMessage
                id="chat.channel.configuration.chat5.message.Settings"
                defaultMessage="部署设置"
              />
            </p>
            <Row gutter={24}>
              <Col span={24}>
                <p style={{ fontSize: 12 }}>
                  <FormattedMessage
                    id="chat.channel.configuration.chat5.message"
                    defaultMessage="组件代码：复制以下代码，嵌入到您网站的</body>标签之前。"
                  />
                </p>
                <div className={styles.preCode}>
                  <div
                    style={{
                      cursor: 'pointer',
                      position: 'absolute',
                      top: 0,
                      right: 0,
                      display: 'inline-block',
                    }}
                  >
                    <CopyToClipboard
                      text={`${script}`}
                      onCopy={(_, result) => {
                        if (result) {
                          message.success(
                            getIntl().formatMessage({
                              id: 'work.order.detail.copy.success',
                              defaultValue: '复制成功',
                            }),
                          );
                        } else {
                          message.error(
                            getIntl().formatMessage({
                              id: 'work.order.detail.copy.error',
                              defaultValue: '复制失败，请稍后再试',
                            }),
                          );
                        }
                      }}
                    >
                      <div className={styles.copyContent}>
                        <FormattedMessage
                          id="knowledge.QA.tabs.3.list.btn.tips1"
                          defaultMessage="复制"
                        />
                      </div>
                    </CopyToClipboard>
                  </div>
                  <div style={{ width: '97%' }}>{`${script}`}</div>
                </div>
              </Col>
            </Row>
          </div>
        </Modal>
        <Modal
          open={open1}
          title=""
          onCancel={handleCancel}
          footer={false}
          width={'50vw'}
        >
          <div>
            <p style={{ fontSize: 20 }}>
              {configurationInformationList[0]?.code ===
              'work_weixin_open_kf_url' ? (
                <FormattedMessage
                  id="channel.allocation.detail.access.link"
                  defaultMessage="接入链接"
                />
              ) : (
                <FormattedMessage
                  id="statistics.data.details.channel.configuration.information"
                  defaultMessage="配置信息"
                />
              )}
            </p>
            <Row gutter={24}>
              <Col span={24}>
                {configurationInformationList[0]?.code ===
                'work_weixin_open_kf_url' ? null : configurationInformationList[0]
                    ?.code === 'weixin_webhook_url' ? (
                  <div style={{ fontSize: 12 }}>
                    <FormattedMessage
                      id="add.web.chat.official.account.channel.configuration.tips.4.1"
                      defaultMessage="请复制以下链接粘贴至"
                    />
                    <a href="https://mp.weixin.qq.com/" target="_blank">
                      <FormattedMessage
                        id="add.web.chat.official.account.channel.configuration.tips.4.2"
                        defaultMessage="「微信公众平台」"
                      />
                    </a>
                    <FormattedMessage
                      id="add.web.chat.official.account.channel.configuration.tips.4.3"
                      defaultMessage="，点击"
                    />
                    <Link
                      to={'/weChatOfficialAccountHelpDocument'}
                      target="_blank"
                    >
                      <FormattedMessage
                        id="add.web.chat.official.account.channel.configuration.tips.4.4"
                        defaultMessage="「帮助文档」"
                      />
                    </Link>
                    <FormattedMessage
                      id="add.web.chat.official.account.channel.configuration.tips.4.5"
                      defaultMessage="查看如何配置 Webhook URL。"
                    />
                  </div>
                ) : configurationInformationList[0]?.code ===
                  'line_webhook_url' ? (
                  <div style={{ fontSize: 12 }}>
                    <FormattedMessage
                      id="line.channel.configuration.title.tips.4.1"
                      defaultMessage="请复制以下链接粘贴至 Line 官方账号的 Webhook URL，并点击「Verify」。"
                    />

                    <Link
                      to={'/lineChannelConfigurationHelpDocument'}
                      target="_blank"
                    >
                      <FormattedMessage
                        id="line.channel.configuration.title.tips.4.2"
                        defaultMessage="「帮助文档」"
                      />
                    </Link>
                    <FormattedMessage
                      id="line.channel.configuration.title.tips.4.3"
                      defaultMessage="查看如何配置 Webhook URL。"
                    />
                  </div>
                ) : (
                  <p style={{ fontSize: 12 }}>
                    <FormattedMessage
                      id="chat.channel.configuration.chat5.message"
                      defaultMessage="组件代码：复制以下代码，嵌入到您网站的</body>标签之前。"
                    />
                  </p>
                )}
                {configurationInformationList?.map(item => {
                  if (item.code === 'weixin_webhook_url') {
                    return (
                      <div
                        style={{ marginBottom: '10px' }}
                        className={styles.preCode}
                      >
                        <div
                          style={{
                            cursor: 'pointer',
                            position: 'absolute',
                            top: 0,
                            right: 0,
                            display: 'inline-block',
                          }}
                        >
                          <CopyToClipboard
                            text={`${item.name}`}
                            onCopy={(_, result) => {
                              if (result) {
                                message.success(
                                  getIntl().formatMessage({
                                    id: 'work.order.detail.copy.success',
                                    defaultValue: '复制成功',
                                  }),
                                );
                              } else {
                                message.error(
                                  getIntl().formatMessage({
                                    id: 'work.order.detail.copy.error',
                                    defaultValue: '复制失败，请稍后再试',
                                  }),
                                );
                              }
                            }}
                          >
                            <div className={styles.copyContent}>
                              <FormattedMessage
                                id="knowledge.QA.tabs.3.list.btn.tips1"
                                defaultMessage="复制"
                              />
                            </div>
                          </CopyToClipboard>
                        </div>
                        <div style={{ width: '97%' }}>
                          URL
                          <span>
                            <FormattedMessage
                              id="work.order.detail.colon"
                              defaultMessage="："
                            />
                            {item.name}
                          </span>
                        </div>
                      </div>
                    );
                  } else if (item.code === 'weixin_token') {
                    const maskedText = '************';
                    return (
                      <div
                        style={{ marginBottom: '10px' }}
                        className={styles.preCode}
                      >
                        <div
                          style={{
                            cursor: 'pointer',
                            position: 'absolute',
                            top: 0,
                            right: 0,
                            display: 'inline-block',
                          }}
                        >
                          <CopyToClipboard
                            text={`${item.name}`}
                            onCopy={(_, result) => {
                              if (result) {
                                message.success(
                                  getIntl().formatMessage({
                                    id: 'work.order.detail.copy.success',
                                    defaultValue: '复制成功',
                                  }),
                                );
                              } else {
                                message.error(
                                  getIntl().formatMessage({
                                    id: 'work.order.detail.copy.error',
                                    defaultValue: '复制失败，请稍后再试',
                                  }),
                                );
                              }
                            }}
                          >
                            <div className={styles.copyContent}>
                              <FormattedMessage
                                id="knowledge.QA.tabs.3.list.btn.tips1"
                                defaultMessage="复制"
                              />
                            </div>
                          </CopyToClipboard>
                        </div>
                        <div
                          style={{
                            width: '97%',
                            display: 'flex',
                            alignItems: 'baseline',
                          }}
                        >
                          Token
                          <FormattedMessage
                            id="work.order.detail.colon"
                            defaultMessage="："
                          />
                          {showText[0] ? (
                            <span>{item.name}</span>
                          ) : (
                            <span>{maskedText}</span>
                          )}
                          <EyeOutlined
                            onClick={() =>
                              setShowText([!showText[0], showText[1]])
                            }
                            style={{ marginLeft: 10 }}
                          />
                        </div>
                      </div>
                    );
                  } else if (item.code === 'weixin_encodingAESKey') {
                    const maskedText = '************';
                    return (
                      <div
                        style={{ marginBottom: '10px' }}
                        className={styles.preCode}
                      >
                        <div
                          style={{
                            cursor: 'pointer',
                            position: 'absolute',
                            top: 0,
                            right: 0,
                            display: 'inline-block',
                          }}
                        >
                          <CopyToClipboard
                            text={`${item.name}`}
                            onCopy={(_, result) => {
                              if (result) {
                                message.success(
                                  getIntl().formatMessage({
                                    id: 'work.order.detail.copy.success',
                                    defaultValue: '复制成功',
                                  }),
                                );
                              } else {
                                message.error(
                                  getIntl().formatMessage({
                                    id: 'work.order.detail.copy.error',
                                    defaultValue: '复制失败，请稍后再试',
                                  }),
                                );
                              }
                            }}
                          >
                            <div className={styles.copyContent}>
                              <FormattedMessage
                                id="knowledge.QA.tabs.3.list.btn.tips1"
                                defaultMessage="复制"
                              />
                            </div>
                          </CopyToClipboard>
                        </div>
                        <div
                          style={{
                            width: '97%',
                            display: 'flex',
                            alignItems: 'baseline',
                          }}
                        >
                          EncondingAESKey
                          <FormattedMessage
                            id="work.order.detail.colon"
                            defaultMessage="："
                          />
                          {showText[1] ? (
                            <span>{item.name}</span>
                          ) : (
                            <span>{maskedText}</span>
                          )}
                          <EyeOutlined
                            onClick={() =>
                              setShowText([showText[0], !showText[1]])
                            }
                            style={{ marginLeft: 10 }}
                          />
                        </div>
                      </div>
                    );
                  } else if (item.code === 'work_weixin_open_kf_url') {
                    return (
                      <div>
                        <div
                          style={{ marginBottom: '10px' }}
                          className={styles.preCode}
                        >
                          <div
                            style={{
                              cursor: 'pointer',
                              position: 'absolute',
                              top: 0,
                              right: 0,
                              display: 'inline-block',
                            }}
                          >
                            <CopyToClipboard
                              text={`${item.name}`}
                              onCopy={(_, result) => {
                                if (result) {
                                  message.success(
                                    getIntl().formatMessage({
                                      id: 'work.order.detail.copy.success',
                                      defaultValue: '复制成功',
                                    }),
                                  );
                                } else {
                                  message.error(
                                    getIntl().formatMessage({
                                      id: 'work.order.detail.copy.error',
                                      defaultValue: '复制失败，请稍后再试',
                                    }),
                                  );
                                }
                              }}
                            >
                              <div className={styles.copyContent}>
                                <FormattedMessage
                                  id="knowledge.QA.tabs.3.list.btn.tips1"
                                  defaultMessage="复制"
                                />
                              </div>
                            </CopyToClipboard>
                          </div>
                          <div style={{ width: '97%' }}>{`${item.name}`}</div>
                        </div>
                        <div>
                          <p
                            style={{ fontSize: 12, margin: '20px 0px 10px 0' }}
                          >
                            <FormattedMessage
                              id="channel.allocation.detail.access.QRCode"
                              defaultMessage="生成的二维码"
                            />
                          </p>
                          <p
                            style={{ display: 'flex', alignItems: 'flex-end' }}
                          >
                            <QRCodeCanvas
                              level="H"
                              value={item.name}
                              id="myqrcode"
                            />
                            <Button
                              onClick={() => downloadCanvasQRCode()}
                              style={{ marginLeft: 20 }}
                            >
                              <FormattedMessage
                                id="document.knowledge.base.table.operation.download"
                                defaultMessage="下载"
                              />
                            </Button>
                          </p>
                        </div>
                      </div>
                    );
                  } else {
                    return (
                      <div
                        style={{ marginBottom: '10px' }}
                        className={styles.preCode}
                      >
                        <div
                          style={{
                            cursor: 'pointer',
                            position: 'absolute',
                            top: 0,
                            right: 0,
                            display: 'inline-block',
                          }}
                        >
                          <CopyToClipboard
                            text={`${item.name}`}
                            onCopy={(_, result) => {
                              if (result) {
                                message.success(
                                  getIntl().formatMessage({
                                    id: 'work.order.detail.copy.success',
                                    defaultValue: '复制成功',
                                  }),
                                );
                              } else {
                                message.error(
                                  getIntl().formatMessage({
                                    id: 'work.order.detail.copy.error',
                                    defaultValue: '复制失败，请稍后再试',
                                  }),
                                );
                              }
                            }}
                          >
                            <div className={styles.copyContent}>
                              <FormattedMessage
                                id="knowledge.QA.tabs.3.list.btn.tips1"
                                defaultMessage="复制"
                              />
                            </div>
                          </CopyToClipboard>
                        </div>
                        <div style={{ width: '97%' }}>{`${item.name}`}</div>
                      </div>
                    );
                  }
                })}
              </Col>
            </Row>
          </div>
        </Modal>
      </div>
    </div>
  );
};

export default NewChannelConfigurationDetailList;
