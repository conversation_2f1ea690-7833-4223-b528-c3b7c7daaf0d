import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  connect,
  useDispatch,
  useSelector,
  getIntl,
  FormattedMessage,
  history,
} from 'umi';
import styles from './index.less';
import moment from 'moment';
import { Select, DatePicker, notification, Spin } from 'antd';
import { formatThousand } from '../../../utils/utils';
import PieChart from './pieChart/index';
import LiquidChart from './liquidChart/index';
import LiquidChart1 from './LiquidChart1/index';
import ColumnChart from './columnChart/index';
import NoDataImg from '@/assets/no-data-img.jpg';
import UpArrowGreen from '@/assets/up_arrow_green.png';
import DownArrowGreen from '@/assets/down_arrow_green.png';
import Dashboard from './dashboard/index';
import tv from '@/assets/tv.png';
import dropDown from '@/assets/dropDown.png';

import { RefreshIcon, SettingIcon, ErrorTipsIcon } from './icon';
import { ChannelTypeSelect } from '@/components/channelSelect'; // 导入通用组件
const { formatMessage } = getIntl();
const { RangePicker } = DatePicker;
const { Option, OptGroup } = Select;

const HotlineKeyIndicators = () => {
  const dispatch = useDispatch();
  const [spinning, setSpinning] = useState(false);
  const [spinning1, setSpinning1] = useState(false);
  const [spinning2, setSpinning2] = useState(false);
  const [spinning3, setSpinning3] = useState(false);
  const [spinning4, setSpinning4] = useState(false);
  const [spinning5, setSpinning5] = useState(false);
  const [spinning6, setSpinning6] = useState(false);
  const [spinning7, setSpinning7] = useState(false);
  const [updataDate, setUpdataDate] = useState(''); //定期更新时间
  // 选择tab时间
  const [selectedDateIndex, setSelectedDateIndex] = useState(0);
  const [selectTime, setSelectTime] = useState(false);
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  // 使用 useState 来存储日期范围
  const [dates, setDates] = useState(null);
  // 接通率选择值
  const [selectedCallCompletionRate, setSelectedCallCompletionRate] = useState(
    '0',
  );
  // 接通率下拉菜单
  const callCompletionRateList = [
    {
      value: '0',
      label: formatMessage({
        id: 'hotlineKeyIndicators.call.completion.rate.all',
        defaultValue: '所有',
      }),
    },
    {
      value: '1',
      label: formatMessage({
        id: 'hotlineKeyIndicators.call.completion.rate.1',
        defaultValue: '呼入接通率',
      }),
    },
    {
      value: '2',
      label: formatMessage({
        id: 'hotlineKeyIndicators.call.completion.rate.2',
        defaultValue: '服务时间呼入接通率',
      }),
    },
    {
      value: '3',
      label: formatMessage({
        id: 'hotlineKeyIndicators.call.completion.rate.3',
        defaultValue: '呼出接通率',
      }),
    },
  ];
  // 热线呼损情况选择值
  const [
    selectedCallLossSituationRate,
    setSelectedCallLossSituationRate,
  ] = useState('1');
  // 热线呼损情况下拉菜单
  const callLossSituationRateList = [
    {
      value: '1',
      label: formatMessage({
        id: 'hotlineKeyIndicators.call.loss.situation.1',
        defaultValue: '呼损量',
      }),
    },
    {
      value: '2',
      label: formatMessage({
        id: 'hotlineKeyIndicators.call.loss.situation.2',
        defaultValue: '呼损率',
      }),
    },
  ];
  // 满意度平均分选择值
  const [
    selectedAverageSatisfactionScore,
    setSelectedAverageSatisfactionScore,
  ] = useState('0');
  // 满意度平均分下拉菜单
  const averageSatisfactionScoreList = [
    {
      value: '0',
      label: formatMessage({
        id: 'hotlineKeyIndicators.average.satisfaction.score.1',
        defaultValue: '所有',
      }),
    },
    {
      value: '1',
      label: formatMessage({
        id: 'hotlineKeyIndicators.average.satisfaction.score.2',
        defaultValue: '呼入满意度',
      }),
    },
    {
      value: '2',
      label: formatMessage({
        id: 'hotlineKeyIndicators.average.satisfaction.score.3',
        defaultValue: '呼出满意度',
      }),
    },
  ];
  // 平均通话时长选择值
  const [
    selectedAverageCallDuration,
    setSelectedAverageCallDuration,
  ] = useState('0');
  // 平均通话时长下拉菜单
  const averageCallDurationList = [
    {
      value: '0',
      label: formatMessage({
        id: 'hotlineKeyIndicators.average.call.duration.1',
        defaultValue: '所有',
      }),
    },
    {
      value: '1',
      label: formatMessage({
        id: 'hotlineKeyIndicators.average.call.duration.2',
        defaultValue: '呼入时长',
      }),
    },
    {
      value: '2',
      label: formatMessage({
        id: 'hotlineKeyIndicators.average.call.duration.3',
        defaultValue: '呼出时长',
      }),
    },
  ];
  // 联络热线下拉表
  const [connectListList, setConnectListList] = useState([]);
  const [connectListValue, setConnectListValue] = useState(''); // 联络热线名称
  const [connectId, setConnectId] = useState(''); // 联络热线ID
  const [callTeamList, setCallTeamList] = useState([]); // 团队
  const [callTeamValue, setCallTeamValue] = useState(''); // 团队选择值
  const [channelTypeList, setChannelTypeList] = useState([]); // 渠道类型
  const [channelTypeId, setChannelTypeId] = useState(''); // 渠道类型
  const [inboundResultData, setInboundResultData] = useState({}); // 呼入来电图表数据
  const [connectionRateResultData, setConnectionRateResultData] = useState({}); // 接通率图表数据
  const [totalCallsResultData, setTotalCallsResultData] = useState({}); // 总通话量图表数据
  const [callLossResultData, setCallLossResultData] = useState({}); // 热线呼损情况图表数据
  const [avgSatisfactionResultData, setAvgSatisfactionResultData] = useState(
    [],
  ); // 满意度平均分图表数据
  const [avgCallTimeResultData, setAvgCallTimeResultData] = useState({}); // 平均通话时长图表数据
  const [teamBusinessNoTrendData, setTeamBusinessNoTrendData] = useState({}); // 业务团队指标-查询不涉及到趋势的变化的板块
  const [
    teamBusinessTrendPartOneData,
    setTeamBusinessTrendPartOneData,
  ] = useState({}); // 业务团队指标-总呼入时长-总呼出时长-10秒座席应答量
  const [
    teamBusinessTrendPartTwoData,
    setTeamBusinessTrendPartTwoData,
  ] = useState({}); // 业务团队指标-平均呼入时长-平均呼出时长-平均呼入排队时长
  const [
    teamBusinessTrendPartThreeData,
    setTeamBusinessTrendPartThreeData,
  ] = useState({}); // 业务团队指标-平均呼入互动时长-平均呼出互动时长-平均呼入ACW时长-平均呼出ACW时长

  const dateKeys = [
    'hotlineKeyIndicators.date.today',
    'hotlineKeyIndicators.date.one',
    'hotlineKeyIndicators.date.two',
    'hotlineKeyIndicators.date.three',
    'hotlineKeyIndicators.date.seven',
    'hotlineKeyIndicators.date.thirty',
    'hotlineKeyIndicators.date.custom',
  ];

  useEffect(() => {
    getUpdateDate();
    getAllConnectList();
    queryChannelTypeList();
    queryTeamList();
  }, []);

  // 获取当前时间
  const getUpdateDate = () => {
    const now = new Date();
    const formattedTime = now.toLocaleString(); // 格式化时间为本地时间字符串
    setUpdataDate(formattedTime);
  };
  // 刷新当前时间
  const handleRefresh = () => {
    getUpdateDate();
    let params = {
      timeRangeCode: selectedDateIndex,
      startTime: startDate,
      endTime: endDate,
      channelTypeId: channelTypeId,
      connectAlias: connectListValue,
      deptId: callTeamValue,
      connectId: connectId,
    };
    queryInboundResult(params);
    queryTotalCallsResult(params);
    queryTeamBusinessNoTrend(params);
    queryTeamBusinessTrendPartOne(params);
    queryTeamBusinessTrendPartTwo(params);
    queryTeamBusinessTrendPartThree(params);
    let params1 = {
      timeRangeCode: selectedDateIndex,
      startTime: startDate,
      endTime: endDate,
      channelTypeId: channelTypeId,
      connectAlias: connectListValue,
      deptId: callTeamValue,
      connectId: connectId,
      showType: selectedCallCompletionRate,
    };
    queryConnectionRateResult(params1);
    let params2 = {
      timeRangeCode: selectedDateIndex,
      startTime: startDate,
      endTime: endDate,
      channelTypeId: channelTypeId,
      connectAlias: connectListValue,
      deptId: callTeamValue,
      connectId: connectId,
      showType: selectedCallLossSituationRate,
    };
    queryCallLossResult(params2);
    let params3 = {
      timeRangeCode: selectedDateIndex,
      startTime: startDate,
      endTime: endDate,
      channelTypeId: channelTypeId,
      connectAlias: connectListValue,
      deptId: callTeamValue,
      connectId: connectId,
      showType: selectedAverageCallDuration,
    };
    queryAvgCallTimeResult(params3);
  };
  const onDateChange = i => {
    setSelectedDateIndex(i);
    if (i !== 6) {
      setStartDate('');
      setEndDate('');
      setDates(null);
    }
  };
  // 查询联络明细
  const getAllConnectList = () => {
    setSpinning(true);
    dispatch({
      type: 'hotlineKeyIndicatorsConfig/getAllConnectList',
      payload: null,
      callback: response => {
        setSpinning(false);
        let { code, data, msg } = response;
        if (200 === code) {
          let connectList;
          if (data) {
            connectList = data.map(item => ({
              value: JSON.stringify(item), // 这里的对象必须序列化
              label: item.connectAlias,
            }));
          } else {
            connectList = [];
          }
          setConnectListList(connectList);
        }
      },
    });
  };
  // 查询团队列表
  const queryTeamList = () => {
    setSpinning(true);
    dispatch({
      type: 'workOrderCenter/queryCallDeptList',
      callback: response => {
        setSpinning(false);
        if (response.code == 200) {
          setCallTeamList(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询渠道类型列表
  const queryChannelTypeList = () => {
    setSpinning(true);
    dispatch({
      type: 'newChannelConfiguration/querySpecialChannelDefList',
      payload: '7,10,11,17,18',
      callback: response => {
        setSpinning(false);
        if (response.code == 200) {
          setChannelTypeList(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 选择团队
  const handleChangeTeam = value => {
    setCallTeamValue(value);
  };
  // 选择渠道类型
  const handleChangeChannelType = value => {
    setChannelTypeId(value);
  };
  // 选择联络热线
  const handleChangeConnectList = value => {
    if (value) {
      setConnectListValue(JSON.parse(value).connectAlias);
      setConnectId(JSON.parse(value).connectId);
    } else {
      setConnectListValue('');
      setConnectId('');
    }
  };
  // 时间选择框事件
  const rangePickerChange = (value, dateString) => {
    setStartDate(dateString[0]);
    setEndDate(dateString[1]);
    setDates(value);
  };
  const onOpenChange = open => {
    setSelectTime(open);
  };

  useEffect(() => {
    let params = {
      timeRangeCode: selectedDateIndex,
      startTime: startDate,
      endTime: endDate,
      channelTypeId: channelTypeId,
      connectAlias: connectListValue,
      deptId: callTeamValue,
      connectId: connectId,
    };
    queryInboundResult(params);
    queryTotalCallsResult(params);
    queryTeamBusinessNoTrend(params);
    queryTeamBusinessTrendPartOne(params);
    queryTeamBusinessTrendPartTwo(params);
    queryTeamBusinessTrendPartThree(params);
  }, [
    selectedDateIndex,
    startDate,
    endDate,
    channelTypeId,
    connectListValue,
    callTeamValue,
    connectId,
  ]);

  // 查询呼入来电
  const queryInboundResult = params => {
    setSpinning2(true);
    dispatch({
      type: 'hotlineKeyIndicatorsConfig/queryInboundResult',
      payload: params,
      callback: response => {
        setSpinning2(false);
        if (response.code === 200) {
          setInboundResultData(response.data);
          getUpdateDate();
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 总通话量
  const queryTotalCallsResult = params => {
    setSpinning1(true);
    dispatch({
      type: 'hotlineKeyIndicatorsConfig/queryTotalCallsResult',
      payload: params,
      callback: response => {
        setSpinning1(false);
        if (response.code === 200) {
          setTotalCallsResultData(response.data);
          getUpdateDate();
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 热线关键指标看板--业务团队指标-查询不涉及到趋势的变化的板块
  const queryTeamBusinessNoTrend = params => {
    setSpinning6(true);
    dispatch({
      type: 'hotlineKeyIndicatorsConfig/queryTeamBusinessNoTrend',
      payload: params,
      callback: response => {
        setSpinning6(false);
        if (response.code === 200) {
          getUpdateDate();
          setTeamBusinessNoTrendData(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 热线关键指标看板-业务团队指标-总呼入时长-总呼出时长-10秒座席应答量
  const queryTeamBusinessTrendPartOne = params => {
    setSpinning6(true);
    dispatch({
      type: 'hotlineKeyIndicatorsConfig/queryTeamBusinessTrendPartOne',
      payload: params,
      callback: response => {
        setSpinning6(false);
        if (response.code === 200) {
          getUpdateDate();
          setTeamBusinessTrendPartOneData(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 热线关键指标看板-业务团队指标-平均呼入时长-平均呼出时长-平均呼入排队时长
  const queryTeamBusinessTrendPartTwo = params => {
    setSpinning6(true);
    dispatch({
      type: 'hotlineKeyIndicatorsConfig/queryTeamBusinessTrendPartTwo',
      payload: params,
      callback: response => {
        setSpinning6(false);
        if (response.code === 200) {
          getUpdateDate();
          setTeamBusinessTrendPartTwoData(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 热线关键指标看板-业务团队指标-平均呼入互动时长-平均呼出互动时长-平均呼入ACW时长-平均呼出ACW时长
  const queryTeamBusinessTrendPartThree = params => {
    setSpinning6(true);
    dispatch({
      type: 'hotlineKeyIndicatorsConfig/queryTeamBusinessTrendPartThree',
      payload: params,
      callback: response => {
        setSpinning6(false);
        if (response.code === 200) {
          getUpdateDate();
          setTeamBusinessTrendPartThreeData(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  useEffect(() => {
    let params = {
      timeRangeCode: selectedDateIndex,
      startTime: startDate,
      endTime: endDate,
      channelTypeId: channelTypeId,
      connectAlias: connectListValue,
      deptId: callTeamValue,
      connectId: connectId,
      showType: selectedCallCompletionRate,
    };
    queryConnectionRateResult(params);
  }, [
    selectedDateIndex,
    startDate,
    endDate,
    channelTypeId,
    connectListValue,
    callTeamValue,
    connectId,
    selectedCallCompletionRate,
  ]);
  // 接通率
  const queryConnectionRateResult = params => {
    setSpinning3(true);
    dispatch({
      type: 'hotlineKeyIndicatorsConfig/queryConnectionRateResult',
      payload: params,
      callback: response => {
        setSpinning3(false);
        if (response.code === 200) {
          getUpdateDate();
          setConnectionRateResultData(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  useEffect(() => {
    let params = {
      timeRangeCode: selectedDateIndex,
      startTime: startDate,
      endTime: endDate,
      channelTypeId: channelTypeId,
      connectAlias: connectListValue,
      deptId: callTeamValue,
      connectId: connectId,
      showType: selectedCallLossSituationRate,
    };
    queryCallLossResult(params);
  }, [
    selectedDateIndex,
    startDate,
    endDate,
    channelTypeId,
    connectListValue,
    callTeamValue,
    connectId,
    selectedCallLossSituationRate,
  ]);
  // 热线呼损情况
  const queryCallLossResult = params => {
    setSpinning4(true);
    dispatch({
      type: 'hotlineKeyIndicatorsConfig/queryCallLossResult',
      payload: params,
      callback: response => {
        setSpinning4(false);
        if (response.code === 200) {
          getUpdateDate();
          setCallLossResultData(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  useEffect(() => {
    let params = {
      timeRangeCode: selectedDateIndex,
      startTime: startDate,
      endTime: endDate,
      channelTypeId: channelTypeId,
      connectAlias: connectListValue,
      deptId: callTeamValue,
      connectId: connectId,
      showType: selectedAverageSatisfactionScore,
    };
    queryAvgSatisfactionResult(params);
  }, [
    selectedDateIndex,
    startDate,
    endDate,
    channelTypeId,
    connectListValue,
    callTeamValue,
    connectId,
    selectedAverageSatisfactionScore,
  ]);
  // 满意度评分
  const queryAvgSatisfactionResult = params => {
    setSpinning5(true);
    dispatch({
      type: 'hotlineKeyIndicatorsConfig/queryAvgSatisfactionResult',
      payload: params,
      callback: response => {
        setSpinning5(false);
        if (response.code === 200) {
          getUpdateDate();
          setAvgSatisfactionResultData(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  useEffect(() => {
    let params = {
      timeRangeCode: selectedDateIndex,
      startTime: startDate,
      endTime: endDate,
      channelTypeId: channelTypeId,
      connectAlias: connectListValue,
      deptId: callTeamValue,
      connectId: connectId,
      showType: selectedAverageCallDuration,
    };
    queryAvgCallTimeResult(params);
  }, [
    selectedDateIndex,
    startDate,
    endDate,
    channelTypeId,
    connectListValue,
    callTeamValue,
    connectId,
    selectedAverageCallDuration,
  ]);
  // 平均通话时长
  const queryAvgCallTimeResult = params => {
    setSpinning7(true);
    dispatch({
      type: 'hotlineKeyIndicatorsConfig/queryAvgCallTimeResult',
      payload: params,
      callback: response => {
        setSpinning7(false);
        if (response.code === 200) {
          getUpdateDate();
          setAvgCallTimeResultData(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 接通率选择下拉框事件
  const handleChangeCallCompletionRate = value => {
    setSelectedCallCompletionRate(value);
  };
  // 热线呼损率
  const handleChangeCallLossSituationRate = value => {
    setSelectedCallLossSituationRate(value);
  };
  // 满意度平均分
  const handleChangeAverageSatisfactionScore = value => {
    setSelectedAverageSatisfactionScore(value);
  };
  // 平均通话时长
  const handleChangeAverageCallDuration = value => {
    setSelectedAverageCallDuration(value);
  };

  // 跳转设置页面
  const handleJumpSetting = module => {
    history.push({
      pathname: '/hotlineKeyIndicatorsConfig',
      state: {
        position: module,
      },
    });
  };

  const dateCards = useMemo(() => {
    const cards = [];
    for (let i = 0; i < dateKeys?.length; i++) {
      const key = dateKeys[i];
      if (key === 'hotlineKeyIndicators.date.custom') {
        cards.push(
          <div
            onClick={() => onDateChange(i)}
            key={key}
            className={`${styles.tabCard} ${styles.tabCardCustom} ${
              selectedDateIndex === i ? styles.tabCardSelected : ''
            }`}
          >
            <span style={{ marginRight: '10px' }}>
              {formatMessage({ id: key })}
            </span>
            <RangePicker
              style={{ height: '28px' }}
              allowClear={false}
              onOpenChange={onOpenChange}
              onChange={rangePickerChange}
              value={dates}
              // 显示时分秒
              showTime={{
                format: 'HH:mm:ss',
              }}
            />
          </div>,
        );
      } else {
        cards.push(
          <div
            onClick={() => onDateChange(i)}
            key={key}
            className={`${styles.tabCard} ${
              selectedDateIndex === i ? styles.tabCardSelected : ''
            }`}
          >
            {formatMessage({ id: key })}
          </div>,
        );
      }
    }
    return cards;
  }, [formatMessage, dateKeys]);

  return (
    <Spin spinning={spinning}>
      <div className={styles.hotlineKeyIndicators} id="hotlineKeyIndicators">
        <p
          className="blueBorder"
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            width: '100%',
          }}
        >
          <FormattedMessage
            id="hotlineKeyIndicators.title"
            defaultValue="热线关键指标看板"
          />
          <span className={styles.updataDate}>
            <FormattedMessage
              id="hotlineKeyIndicators.title.right"
              defaultValue="更新数据时间："
            />
            {updataDate}
            <div className={styles.refreshIcon} onClick={handleRefresh}>
              <div className={styles.filled}></div>
              {RefreshIcon()}
            </div>
            <div onClick={handleJumpSetting} className={styles.settingIcon}>
              <div className={styles.filled}></div>
              {SettingIcon()}
            </div>
          </span>
        </p>

        <div style={{ display: 'flex', borderBottom: '1px solid #94ACFB' }}>
          {dateCards}
        </div>

        <div style={{ marginTop: '20px' }}>
          <span>
            <span className={styles.optionLabel}>
              <FormattedMessage id="hotlineKeyIndicators.option.channel" />
            </span>

            <ChannelTypeSelect
              defaultStyle={{ width: 176, marginRight: '20px' }}
              popupClassName="selectFilterContent"
              onChange={handleChangeChannelType}
              channelTypeList={channelTypeList}
            ></ChannelTypeSelect>
          </span>

          <span className={styles.option}>
            <span className={styles.optionLabel}>
              <FormattedMessage id="hotlineKeyIndicators.option.contact.line" />
            </span>
            <Select
              style={{ width: 176, marginRight: '20px' }}
              placeholder={getIntl().formatMessage({
                id: 'hotlineKeyIndicators.option.contact.line.placeholder',
                defaultValue: '请选择联络线路',
              })}
              options={connectListList}
              showSearch
              filterOption={(inputValue, option) =>
                option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >=
                0
              }
              allowClear
              onChange={handleChangeConnectList}
            />
          </span>

          <span className={styles.option}>
            <span className={styles.optionLabel}>
              <FormattedMessage id="hotlineKeyIndicators.option.team" />
            </span>
            <Select
              style={{ width: 176, marginRight: '20px' }}
              placeholder={getIntl().formatMessage({
                id: 'hotlineKeyIndicators.option.teamPlaceholder',
                defaultValue: '请选择渠道类型',
              })}
              options={callTeamList}
              filterOption={(inputValue, option) =>
                option.deptName
                  .toLowerCase()
                  .indexOf(inputValue.toLowerCase()) >= 0
              }
              fieldNames={{
                label: 'deptName',
                value: 'deptId',
                key: 'deptId',
              }}
              allowClear
              onChange={handleChangeTeam}
            />
          </span>
        </div>

        <div className={styles.cardContainerTop}>
          <div className={styles.card}>
            <Spin spinning={spinning2}>
              <div className={styles.cardTitleLine}>
                <div className={styles.cardTitle}>
                  <FormattedMessage
                    id="hotlineKeyIndicators.card.title"
                    defaultValue="呼入来电"
                  ></FormattedMessage>
                </div>
                {/*<img className={styles.settingOutline} src={settingOutline} />*/}
              </div>
              {Object.keys(inboundResultData)?.length > 0 ? (
                <PieChart
                  pieType={1}
                  newData={inboundResultData}
                  text={getIntl().formatMessage({
                    id: 'hotlineKeyIndicators.agent.online.text',
                  })}
                />
              ) : (
                <div
                  className={styles.noDataContent}
                  style={{ height: 150, marginTop: '15px' }}
                >
                  <img
                    src={NoDataImg}
                    style={{ width: '50%', marginLeft: '25%' }}
                  />
                  <p>
                    <FormattedMessage
                      id="work.order.reply.no.data"
                      defaultMessage="暂无数据"
                    />
                  </p>
                </div>
              )}
            </Spin>
          </div>
          <div
            className={styles.card}
            style={{ marginLeft: '1.5%', marginRight: '1.5%' }}
          >
            <Spin spinning={spinning3}>
              <div className={styles.cardTitleLine}>
                <div className={styles.cardTitle}>
                  <FormattedMessage
                    id="hotlineKeyIndicators.card.title.1"
                    defaultValue="接通率"
                  ></FormattedMessage>
                </div>
                <div className={styles.select}>
                  <div className={styles.dot}></div>
                  <Select
                    bordered={false}
                    value={selectedCallCompletionRate}
                    options={callCompletionRateList}
                    onChange={handleChangeCallCompletionRate}
                  />
                  <img src={dropDown} />
                </div>
                <div
                  onClick={() => handleJumpSetting('callCompletionRate')}
                  className={styles.settingOutline}
                >
                  <div className={styles.filled}></div>
                  {SettingIcon()}
                </div>
              </div>
              {Object.keys(connectionRateResultData)?.length > 0 &&
              connectionRateResultData.totalNum > 0 ? (
                <div style={{ display: 'flex', justifyContent: 'center' }}>
                  <LiquidChart newData={connectionRateResultData} />
                </div>
              ) : (
                <div
                  className={styles.noDataContent}
                  style={{ height: 150, marginTop: '15px' }}
                >
                  <img
                    src={NoDataImg}
                    style={{ width: '50%', marginLeft: '25%' }}
                  />
                  <p>
                    <FormattedMessage
                      id="work.order.reply.no.data"
                      defaultMessage="暂无数据"
                    />
                  </p>
                </div>
              )}
            </Spin>
          </div>
          <div className={styles.card}>
            <Spin spinning={spinning1}>
              <div className={styles.cardTitleLine}>
                <div className={styles.cardTitle}>
                  <FormattedMessage
                    id="hotlineKeyIndicators.card.title.2"
                    defaultValue="总通话量"
                  ></FormattedMessage>
                </div>
                <div
                  onClick={() => handleJumpSetting('incomingCall')}
                  className={styles.settingOutline}
                >
                  <div className={styles.filled}></div>
                  {SettingIcon()}
                </div>
                {totalCallsResultData?.showSurgeFlag > 0 ? (
                  <div className={styles.showSurgeFlag}>
                    {ErrorTipsIcon()}
                    <span>
                      <FormattedMessage
                        id="hotlineKeyIndicators.sudden.increase.incoming.call.volume"
                        defaultMessage="呼入量激增"
                      />
                    </span>
                  </div>
                ) : null}
              </div>
              {Object.keys(totalCallsResultData)?.length > 0 &&
              totalCallsResultData.totalNum > 0 ? (
                <PieChart
                  pieType={2}
                  newData={totalCallsResultData}
                  text={getIntl().formatMessage({
                    id: 'hotlineKeyIndicators.card.title.2',
                  })}
                />
              ) : (
                <div
                  className={styles.noDataContent}
                  style={{ height: 150, marginTop: '15px' }}
                >
                  <img
                    src={NoDataImg}
                    style={{ width: '50%', marginLeft: '25%' }}
                  />
                  <p>
                    <FormattedMessage
                      id="work.order.reply.no.data"
                      defaultMessage="暂无数据"
                    />
                  </p>
                </div>
              )}
            </Spin>
          </div>
        </div>

        <div className={styles.cardContainerCenter}>
          <div className={styles.cardLeft} style={{ marginRight: '1.5%' }}>
            <Spin spinning={spinning4}>
              <div className={styles.cardTitleLine}>
                <div className={styles.cardTitle}>
                  <FormattedMessage
                    id="hotlineKeyIndicators.card.title.3"
                    defaultValue="热线呼损情况"
                  ></FormattedMessage>
                </div>
                <div className={styles.select} style={{ width: '20%' }}>
                  <div className={styles.dot}></div>
                  <Select
                    style={{ width: '45%' }}
                    bordered={false}
                    value={selectedCallLossSituationRate}
                    options={callLossSituationRateList}
                    onChange={handleChangeCallLossSituationRate}
                  />
                  <img src={dropDown} />
                </div>
                <div
                  onClick={() => handleJumpSetting('callLoss')}
                  className={styles.settingOutline}
                >
                  <div className={styles.filled}></div>
                  {SettingIcon()}
                </div>
              </div>
              {Object.keys(callLossResultData)?.length > 0 &&
              callLossResultData.callLossList.length > 0 &&
              callLossResultData.totalCallLoss.totalNum > 0 ? (
                <div
                  style={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  <ColumnChart newData={callLossResultData.callLossList} />
                  <LiquidChart1
                    selectedCallLossSituationRate={
                      selectedCallLossSituationRate
                    }
                    newData={callLossResultData.totalCallLoss}
                  />
                </div>
              ) : (
                <div
                  className={styles.noDataContent}
                  style={{ height: 150, marginTop: '55px' }}
                >
                  <img
                    src={NoDataImg}
                    style={{ width: '50%', marginLeft: '25%' }}
                  />
                  <p>
                    <FormattedMessage
                      id="work.order.reply.no.data"
                      defaultMessage="暂无数据"
                    />
                  </p>
                </div>
              )}
            </Spin>
          </div>
          <div className={styles.cardRight}>
            <div
              className={styles.card}
              style={{ marginBottom: '20px', width: '100%' }}
            >
              <Spin spinning={spinning5}>
                <div className={styles.cardTitleLine}>
                  <div className={styles.cardTitle}>
                    <FormattedMessage
                      id="hotlineKeyIndicators.card.title.4"
                      defaultValue="满意度平均分"
                    ></FormattedMessage>
                  </div>
                  <div className={styles.select}>
                    <div className={styles.dot}></div>
                    <Select
                      bordered={false}
                      value={selectedAverageSatisfactionScore}
                      options={averageSatisfactionScoreList}
                      onChange={handleChangeAverageSatisfactionScore}
                    />
                    <img src={dropDown} />
                  </div>
                  <div
                    onClick={() =>
                      handleJumpSetting('averageSatisfactionScore')
                    }
                    className={styles.settingOutline}
                  >
                    <div className={styles.filled}></div>
                    {SettingIcon()}
                  </div>
                </div>
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    width: '100%',
                  }}
                >
                  {avgSatisfactionResultData?.avgScore ? (
                    <>
                      <div
                        className={styles.contentText}
                        style={{
                          color: avgSatisfactionResultData.colorCode
                            ? avgSatisfactionResultData.colorCode
                            : '#3463fc',
                        }}
                      >
                        <p style={{ marginBottom: '0px' }}>
                          {avgSatisfactionResultData.avgScore}
                          <span>
                            <FormattedMessage id="satisfaction.report.average.satisfaction.score" />
                          </span>
                        </p>
                      </div>
                      {/*//上升还是下降 1-下降 2-上升 3-相等 4-不显示（分母为0的时候）*/}
                      <div
                        className={styles.tip}
                        style={{
                          display:
                            avgSatisfactionResultData.riseOrFall === 3 ||
                            avgSatisfactionResultData.riseOrFall === 4
                              ? 'none'
                              : 'block',
                          background:
                            avgSatisfactionResultData.riseOrFall == 1
                              ? 'rgba(242, 36, 23, 0.1)'
                              : 'rgba(103, 231, 39, 0.1)',
                        }}
                      >
                        {/*dropDown*/}
                        <img
                          src={
                            avgSatisfactionResultData.riseOrFall == 1
                              ? DownArrowGreen
                              : UpArrowGreen
                          }
                          className={styles.UpArrowGreen}
                        />
                        <span
                          style={{
                            color:
                              avgSatisfactionResultData.riseOrFall == 1
                                ? '#F22417'
                                : '#67e727',
                          }}
                        >
                          {avgSatisfactionResultData.rate
                            ? avgSatisfactionResultData.rate
                            : 0}{' '}
                          %
                        </span>
                      </div>
                      <div
                        style={{
                          display:
                            avgSatisfactionResultData.riseOrFall === 3 ||
                            avgSatisfactionResultData.riseOrFall === 4
                              ? 'none'
                              : 'block',
                        }}
                        className={styles.tipText}
                      >
                        <FormattedMessage
                          id="hotlineKeyIndicators.average.satisfaction.score.4"
                          defaultMessage="与之前30天相比"
                        />
                      </div>
                    </>
                  ) : (
                    <div className={styles.contentTextNo}>
                      <span>-- </span>
                      <span>
                        <FormattedMessage id="satisfaction.report.average.satisfaction.score" />
                      </span>
                    </div>
                  )}
                </div>
              </Spin>
            </div>

            <div className={styles.card} style={{ width: '100%' }}>
              <Spin spinning={spinning7}>
                <div className={styles.cardTitleLine}>
                  <div className={styles.cardTitle}>
                    <FormattedMessage
                      id="hotlineKeyIndicators.card.title.5"
                      defaultValue="平均通话时长"
                    ></FormattedMessage>
                  </div>
                  <div className={styles.select}>
                    <div className={styles.dot}></div>
                    <Select
                      bordered={false}
                      value={selectedAverageCallDuration}
                      options={averageCallDurationList}
                      onChange={handleChangeAverageCallDuration}
                    />
                    <img src={dropDown} />
                  </div>
                </div>
                {avgCallTimeResultData?.avgCallTotalTime > 0 ? (
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      width: '100%',
                    }}
                  >
                    <div
                      className={styles.contentText}
                      style={{
                        color: avgCallTimeResultData.colorCode
                          ? avgCallTimeResultData.colorCode
                          : '#3463fc',
                      }}
                    >
                      {avgCallTimeResultData.avgCallTime}
                    </div>
                    <div
                      className={styles.tip}
                      style={{
                        background:
                          avgCallTimeResultData.riseOrFall == 1
                            ? 'rgba(242, 36, 23, 0.1)'
                            : 'rgba(103, 231, 39, 0.1)',
                        display:
                          avgCallTimeResultData.riseOrFall === 3 ||
                          avgCallTimeResultData.riseOrFall === 4
                            ? 'none'
                            : 'block',
                      }}
                    >
                      <img
                        src={
                          avgCallTimeResultData.riseOrFall == 1
                            ? DownArrowGreen
                            : UpArrowGreen
                        }
                        className={styles.UpArrowGreen}
                      />
                      <span
                        style={{
                          color:
                            avgCallTimeResultData.riseOrFall == 1
                              ? '#F22417'
                              : '#67e727',
                        }}
                      >
                        {avgCallTimeResultData.rate
                          ? avgCallTimeResultData.rate
                          : 0}{' '}
                        %
                      </span>
                    </div>
                    <div
                      className={styles.tipText}
                      style={{
                        display:
                          avgCallTimeResultData.riseOrFall === 3 ||
                          avgCallTimeResultData.riseOrFall === 4
                            ? 'none'
                            : 'block',
                      }}
                    >
                      <FormattedMessage
                        id="hotlineKeyIndicators.average.satisfaction.score.4"
                        defaultMessage="与之前30天相比"
                      />
                    </div>
                  </div>
                ) : (
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                    }}
                  >
                    <div className={styles.contentTextNo}>
                      <span>-- : -- : --</span>
                    </div>
                  </div>
                )}
              </Spin>
            </div>
          </div>
        </div>

        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            margin: ' 22px 0',
            alignItems: 'center',
          }}
        >
          <div>
            <img
              style={{ width: '28px', height: '28px', display: 'inline-block' }}
              src={tv}
            ></img>
            <span
              style={{
                color: '#333',
                fontWeight: 700,
                fontSize: '16px',
                marginLeft: '10px',
              }}
            >
              <FormattedMessage
                id="hotlineKeyIndicators.card.title.6"
                defaultValue="团队业务指标"
              ></FormattedMessage>
            </span>
          </div>
          <div style={{ color: '#999', fontSize: '14px' }}>
            <FormattedMessage
              id="hotlineKeyIndicators.card.second.title.6"
              defaultValue="与之前24小时相比"
            ></FormattedMessage>
          </div>
        </div>
        <Spin spinning={spinning6}>
          <Dashboard
            teamBusinessNoTrendData={teamBusinessNoTrendData}
            teamBusinessTrendPartOneData={teamBusinessTrendPartOneData}
            teamBusinessTrendPartTwoData={teamBusinessTrendPartTwoData}
            teamBusinessTrendPartThreeData={teamBusinessTrendPartThreeData}
          />
        </Spin>
      </div>
    </Spin>
  );
};

export default HotlineKeyIndicators;
