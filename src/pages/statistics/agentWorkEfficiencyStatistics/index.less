.agentWorkEfficiencyStatistics {
  margin: 20px 20px 0px 20px;
  //padding: 20px;
  overflow: hidden;
  overflow-y: scroll;
  /* 隐藏滚动条 */
  scrollbar-width: none;
  /* firefox */
  -ms-overflow-style: none;

  .selectContent {
    float: right;
    span {
      color: #333;
      font-family: 'Poppins', sans-serif;
      font-size: 12px;
      font-style: normal;
    }
    label {
      color: #333;
      font-family: 'Poppins', sans-serif;
      font-size: 12px;
      font-style: normal;
      line-height: 32px;
    }
    :global {
      .ant-picker {
        border-radius: 4px;
        //border: 1px solid #e6e6e6;
        background: #fff;
        box-shadow: none;
      }
    }
  }
  .topOneContent {
    width: 100%;
    height: 180px;
    .averageResolutionTime {
      width: 30.5%;
      height: 180px;
      float: left;
      border-radius: 4px;
      background-image: url('../../../assets/averageResolutionTime.png');
      background-size: cover;
      background-repeat: no-repeat;
      /* 模块阴影 */
      box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.15);
      padding: 20px;
      .secondTitle {
        color: #333;
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 800;
        .detailTipsIcon {
          width: 20px;
          height: 20px;
          margin-top: -4px;
          margin-left: 4px;
          cursor: pointer;
        }
      }
      .averageResolutionTimeText {
        color: #3463fc;
        font-family: 'Poppins', sans-serif;
        font-size: 40px;
        font-style: normal;
        font-weight: 800;
        line-height: 80px; /* 150% */
        text-align: center;
      }
    }
    .dataInsight {
      width: 68%;
      float: left;
      height: 180px;
      margin-left: 1.5%;
      border-radius: 4px;
      background-image: url('../../../assets/dataInsight.png');
      background-size: cover;
      background-repeat: no-repeat;
      /* 模块阴影 */
      box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.15);
      padding: 20px;
      .secondTitle {
        color: #333;
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 800;
        margin-bottom: 15px;
        .detailTipsIcon {
          width: 20px;
          height: 20px;
          margin-top: -4px;
          margin-left: 4px;
          cursor: pointer;
        }
      }
      .dataInsightText {
        color: #000;
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        span {
          color: #3463fc;
          font-family: 'Poppins', sans-serif;
          font-size: 16px;
          font-style: normal;
          font-weight: 800;
          line-height: 24px;
        }
      }
    }
  }
  .topOneContentEn {
    width: 100%;
    height: 210px;
    .averageResolutionTime {
      width: 30.5%;
      height: 210px;
      float: left;
      border-radius: 4px;
      background-image: url('../../../assets/averageResolutionTime.png');
      background-size: cover;
      background-repeat: no-repeat;
      /* 模块阴影 */
      box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.15);
      padding: 20px;
      .secondTitle {
        color: #333;
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 800;
        .detailTipsIcon {
          width: 20px;
          height: 20px;
          margin-top: -4px;
          margin-left: 4px;
          cursor: pointer;
        }
      }
      .averageResolutionTimeText {
        color: #3463fc;
        font-family: 'Poppins', sans-serif;
        font-size: 40px;
        font-style: normal;
        font-weight: 800;
        line-height: 110px; /* 150% */
        text-align: center;
      }
    }
    .dataInsight {
      width: 68%;
      float: left;
      height: 210px;
      margin-left: 1.5%;
      border-radius: 4px;
      background-image: url('../../../assets/dataInsight.png');
      background-size: cover;
      background-repeat: no-repeat;
      /* 模块阴影 */
      box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.15);
      padding: 20px;
      .secondTitle {
        color: #333;
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 800;
        margin-bottom: 15px;
        .detailTipsIcon {
          width: 20px;
          height: 20px;
          margin-top: -4px;
          margin-left: 4px;
          cursor: pointer;
        }
      }
      .dataInsightText {
        color: #000;
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        span {
          color: #3463fc;
          font-family: 'Poppins', sans-serif;
          font-size: 16px;
          font-style: normal;
          font-weight: 800;
          line-height: 24px;
        }
      }
    }
  }
  .topTwoContent {
    width: 100%;
    height: 172px;
    margin-top: 17px;
    .twoContentItem {
      width: 23.8%;
      height: 100%;
      float: left;
      border-radius: var(--4, 4px);
      background: #fff;
      padding: 20px;
      margin-right: 1.5%;
      /* 模块阴影 */
      box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.15);
      .secondTitle {
        color: #333;
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 800;
        .secondTitleDetail {
          width: 90%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          float: left;
        }
        .detailTipsIcon {
          width: 20px;
          height: 20px;
          margin-top: -4px;
          margin-left: 4px;
          cursor: pointer;
        }
      }
      .twoDetailItem {
        width: 100%;
        .oneItem {
          width: 100%;
          height: 24px;
          border-bottom: 1px solid #e6e6e6;
          .leftItem {
            width: 50%;
            height: 24px;
            float: left;
            color: #666;
            font-family: 'Poppins', sans-serif;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px;
            text-align: center;
          }
          .rightItem {
            width: 50%;
            height: 24px;
            float: left;
            color: #666;
            font-family: 'Poppins', sans-serif;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px;
            text-align: center;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .centerItem {
            width: 0%;
            height: 24px;
            float: left;
          }
        }
        .twoItem {
          width: 100%;
          height: 24px;
          line-height: 24px;
          margin-top: 1px;
          cursor: pointer;
          .leftItem {
            width: 50%;
            height: 24px;
            float: left;
            text-align: center;
            img {
              width: 16px;
              margin-right: 3px;
            }
          }
          .rightItem {
            width: 50%;
            height: 24px;
            float: left;
            text-align: center;
          }
          .centerItem {
            width: 0%;
            height: 24px;
            float: left;
          }
        }
        .twoItem:hover {
          background-color: rgba(52, 99, 252, 0.05);
        }
      }
    }
    .twoContentItemLast {
      width: 24%;
      height: 100%;
      float: left;
      border-radius: var(--4, 4px);
      background: #fff;
      padding: 20px;
      /* 模块阴影 */
      box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.15);
      .secondTitle {
        color: #333;
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 800;
        .secondTitleDetail {
          width: 90%;
          float: left;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .detailTipsIcon {
          width: 20px;
          height: 20px;
          margin-top: -4px;
          margin-left: 4px;
          cursor: pointer;
        }
      }
      .twoDetailItem {
        width: 100%;
        .oneItem {
          width: 100%;
          height: 24px;
          border-bottom: 1px solid #e6e6e6;
          .leftItem {
            width: 50%;
            height: 24px;
            float: left;
            color: #666;
            font-family: 'Poppins', sans-serif;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px;
            text-align: center;
          }
          .rightItem {
            width: 50%;
            height: 24px;
            float: left;
            color: #666;
            font-family: 'Poppins', sans-serif;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px;
            text-align: center;
          }
          .centerItem {
            width: 0%;
            height: 24px;
            float: left;
          }
        }
        .twoItem {
          width: 100%;
          height: 24px;
          line-height: 24px;
          margin-top: 1px;
          cursor: pointer;
          .leftItem {
            width: 50%;
            height: 24px;
            float: left;
            text-align: center;
            img {
              width: 16px;
              margin-right: 3px;
            }
          }
          .rightItem {
            width: 50%;
            height: 24px;
            float: left;
            text-align: center;
          }
          .centerItem {
            width: 0%;
            height: 24px;
            float: left;
          }
        }
        .twoItem:hover {
          background-color: rgba(52, 99, 252, 0.05);
        }
      }
    }
  }
  .topTwoContentEn {
    width: 100%;
    height: 172px;
    margin-top: 17px;
    .twoContentItem {
      width: 23.8%;
      height: 100%;
      float: left;
      border-radius: var(--4, 4px);
      background: #fff;
      padding: 20px;
      margin-right: 1.5%;
      /* 模块阴影 */
      box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.15);
      .secondTitle {
        color: #333;
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 800;
        .secondTitleDetail {
          width: 90%;
          float: left;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .detailTipsIcon {
          width: 20px;
          height: 20px;
          margin-top: -4px;
          margin-left: 4px;
          cursor: pointer;
        }
      }
      .twoDetailItem {
        width: 100%;
        .oneItem {
          width: 100%;
          height: 24px;
          border-bottom: 1px solid #e6e6e6;
          .leftItem {
            width: 50%;
            height: 24px;
            float: left;
            text-align: center;
            img {
              width: 16px;
              margin-right: 3px;
            }
          }
          .rightItem {
            width: 50%;
            height: 24px;
            float: left;
            text-align: center;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            img {
              width: 16px;
              margin-right: 3px;
            }
          }
          .centerItem {
            width: 0%;
            height: 24px;
            float: left;
          }
        }
        .twoItem {
          width: 100%;
          height: 24px;
          line-height: 24px;
          margin-top: 1px;
          cursor: pointer;
          .leftItem {
            width: 50%;
            height: 24px;
            float: left;
            text-align: center;
            img {
              width: 16px;
              margin-right: 3px;
            }
          }
          .rightItem {
            width: 50%;
            height: 24px;
            float: left;
            text-align: center;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            img {
              width: 16px;
              margin-right: 3px;
            }
          }
          .centerItem {
            width: 0%;
            height: 24px;
            float: left;
          }
        }
        .twoItem:hover {
          background-color: rgba(52, 99, 252, 0.05);
        }
      }
    }
    .twoContentItemLast {
      width: 24%;
      height: 100%;
      float: left;
      border-radius: var(--4, 4px);
      background: #fff;
      padding: 20px;
      /* 模块阴影 */
      box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.15);
      .secondTitle {
        color: #333;
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 800;
        .secondTitleDetail {
          width: 90%;
          float: left;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .detailTipsIcon {
          width: 20px;
          height: 20px;
          margin-top: -4px;
          margin-left: 4px;
          cursor: pointer;
        }
      }
      .twoDetailItem {
        width: 100%;
        .oneItem {
          width: 100%;
          height: 24px;
          border-bottom: 1px solid #e6e6e6;
          .leftItem {
            width: 50%;
            height: 24px;
            float: left;
            text-align: center;
            img {
              width: 16px;
              margin-right: 3px;
            }
          }
          .rightItem {
            width: 50%;
            height: 24px;
            float: left;
            text-align: center;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            img {
              width: 16px;
              margin-right: 3px;
            }
          }
          .centerItem {
            width: 0%;
            height: 24px;
            float: left;
          }
        }
        .twoItem {
          width: 100%;
          height: 24px;
          line-height: 24px;
          margin-top: 1px;
          cursor: pointer;
          .leftItem {
            width: 50%;
            height: 24px;
            float: left;
            text-align: center;
            img {
              width: 16px;
              margin-right: 3px;
            }
          }
          .rightItem {
            width: 50%;
            height: 24px;
            float: left;
            text-align: center;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            img {
              width: 16px;
              margin-right: 3px;
            }
          }
          .centerItem {
            width: 0%;
            height: 24px;
            float: left;
          }
        }
        .twoItem:hover {
          background-color: rgba(52, 99, 252, 0.05);
        }
      }
    }
  }
  .topThreeContent {
    width: 100%;
    height: 366px;
    margin-top: 20px;
    border-radius: 4px;
    background: #fff;
    padding: 20px;
    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
    .threeHeaderContent {
      width: 100%;
      height: 32px;
      margin-bottom: 10px;
      .secondTitle {
        color: #333;
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 800;
        float: left;
        line-height: 32px;
        .detailTipsIcon {
          width: 20px;
          height: 20px;
          margin-top: -4px;
          margin-left: 4px;
          margin-right: 12px;
          cursor: pointer;
        }
      }
      .batchExport {
        float: right;
        margin-right: 20px;
      }

      .chartImg {
        width: 16px;
        margin-top: -3px;
      }
      :global {
        .ant-radio-group {
          float: right;
          border: 1px solid #3968fc;
          border-radius: 4px;
        }
        .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before {
          background-color: #fff;
        }
        .ant-radio-button-wrapper:not(:first-child)::before {
          width: 0px;
        }
        .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
          border: none;
          box-shadow: none;
          //border-radius:4px 0px 0px 4px;
          background: rgba(52, 99, 252, 0.2);
        }
        .ant-radio-button-wrapper {
          border: none;
          box-shadow: none;
        }
        .ant-radio-button-wrapper {
          height: 30px;
        }
      }
    }
    .selectAgentName {
      float: left;
      width: 100%;
      margin-bottom: 15px;
      span {
        color: #333;
        font-family: 'Poppins', sans-serif;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        //line-height: 32px;
      }

      :global {
        .ant-select {
          width: 50%;
        }
        .ant-select:not(.ant-select-customize-input) .ant-select-selector {
          border-radius: 6px;
          //border: 1px solid #e6e6e6;
          background: #fff;
          box-shadow: none;
          height: 32px;
          overflow: hidden;
          overflow-y: scroll;
          /* 隐藏滚动条 */
          scrollbar-width: none;
          /* firefox */
          -ms-overflow-style: none;
        }
        .ant-select:not(.ant-select-customize-input)
          .ant-select-selector::-webkit-scrollbar {
          display: none;
          /* Chrome Safari */
        }
        .ant-select-selection-placeholder {
          color: #666666 !important;
        }
      }
    }
  }
  .topFourContent {
    width: 100%;
    height: 368px;
    margin-top: 20px;
    border-radius: 4px;
    background: #fff;
    padding: 20px;
    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
    .fourHeaderContent {
      width: 100%;
      height: 32px;
      margin-bottom: 10px;
      .secondTitle {
        color: #333;
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 800;
        float: left;
        line-height: 32px;
        .detailTipsIcon {
          width: 20px;
          height: 20px;
          margin-top: -4px;
          margin-left: 4px;
          margin-right: 12px;
          cursor: pointer;
        }
      }
      .batchExport {
        float: right;
        margin-right: 20px;
      }

      .chartImg {
        width: 16px;
        margin-top: -3px;
      }
      :global {
        .ant-radio-group {
          float: right;
          border: 1px solid #3968fc;
          border-radius: 4px;
        }
        .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before {
          background-color: #fff;
        }
        .ant-radio-button-wrapper:not(:first-child)::before {
          width: 0px;
        }
        .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
          border: none;
          box-shadow: none;
          //border-radius:4px 0px 0px 4px;
          background: rgba(52, 99, 252, 0.2);
        }
        .ant-radio-button-wrapper {
          border: none;
          box-shadow: none;
          height: 30px;
          color: #666;
          font-weight: 400;
          font-size: 12px;
        }
        .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
          color: #3463fc;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
        }
      }
    }
    .selectAgentName {
      float: left;
      width: 100%;
      margin-bottom: 15px;
      span {
        color: #333;
        font-family: 'Poppins', sans-serif;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        //line-height: 32px;
      }

      :global {
        .ant-select {
          width: 50%;
        }
        .ant-select:not(.ant-select-customize-input) .ant-select-selector {
          border-radius: 6px;
          //border: 1px solid #e6e6e6;
          background: #fff;
          box-shadow: none;
          height: 32px;
          overflow: hidden;
          overflow-y: scroll;
          /* 隐藏滚动条 */
          scrollbar-width: none;
          /* firefox */
          -ms-overflow-style: none;
        }
        .ant-select:not(.ant-select-customize-input)
          .ant-select-selector::-webkit-scrollbar {
          display: none;
          /* Chrome Safari */
        }
        .ant-select-selection-placeholder {
          color: #666666 !important;
        }
      }
    }
  }
  .topFiveContent {
    width: 100%;
    height: 374px;
    margin-top: 20px;
    border-radius: 4px;
    background: #fff;
    padding: 20px;
    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
    .fiveHeaderContent {
      width: 100%;
      height: 32px;
      margin-bottom: 10px;
      .secondTitle {
        color: #333;
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 800;
        float: left;
        line-height: 32px;
        .detailTipsIcon {
          width: 20px;
          height: 20px;
          margin-top: -4px;
          margin-left: 4px;
          margin-right: 12px;
          cursor: pointer;
        }
      }
      .batchExport {
        float: right;
        margin-right: 20px;
      }

      .chartImg {
        width: 16px;
        margin-top: -3px;
      }
      :global {
        .ant-radio-group {
          float: right;
          border: 1px solid #3968fc;
          border-radius: 4px;
        }
        .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before {
          background-color: #fff;
        }
        .ant-radio-button-wrapper:not(:first-child)::before {
          width: 0px;
        }
        .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
          border: none;
          box-shadow: none;
          //border-radius:4px 0px 0px 4px;
          background: rgba(52, 99, 252, 0.2);
        }
        .ant-radio-button-wrapper {
          border: none;
          box-shadow: none;
        }
        .ant-radio-button-wrapper {
          height: 30px;
        }
      }
    }
    .selectAgentName {
      float: left;
      width: 100%;
      margin-bottom: 15px;
      span {
        color: #333;
        font-family: 'Poppins', sans-serif;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        //line-height: 32px;
      }

      :global {
        .ant-select {
          width: 50%;
        }
        .ant-select:not(.ant-select-customize-input) .ant-select-selector {
          border-radius: 6px;
          //border: 1px solid #e6e6e6;
          background: #fff;
          box-shadow: none;
          height: 32px;
          overflow: hidden;
          overflow-y: scroll;
          /* 隐藏滚动条 */
          scrollbar-width: none;
          /* firefox */
          -ms-overflow-style: none;
        }
        .ant-select:not(.ant-select-customize-input)
          .ant-select-selector::-webkit-scrollbar {
          display: none;
          /* Chrome Safari */
        }
        .ant-select-selection-placeholder {
          color: #666666 !important;
        }
      }
    }
  }
  .topSixContent {
    width: 100%;
    height: 374px;
    margin-top: 20px;
    border-radius: 4px;
    background: #fff;
    padding: 20px;
    margin-bottom: 15px;
    /* 阴影 */
    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
    .sixHeaderContent {
      width: 100%;
      height: 32px;
      margin-bottom: 10px;
      .secondTitle {
        color: #333;
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 800;
        float: left;
        line-height: 32px;
        .detailTipsIcon {
          width: 20px;
          height: 20px;
          margin-top: -4px;
          margin-left: 4px;
          margin-right: 12px;
          cursor: pointer;
        }
      }
      .batchExport {
        float: right;
        margin-right: 20px;
      }

      .chartImg {
        width: 16px;
        margin-top: -3px;
      }
      :global {
        .ant-radio-group {
          float: right;
          border: 1px solid #3968fc;
          border-radius: 4px;
        }
        .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before {
          background-color: #fff;
        }
        .ant-radio-button-wrapper:not(:first-child)::before {
          width: 0px;
        }
        .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
          border: none;
          box-shadow: none;
          //border-radius:4px 0px 0px 4px;
          background: rgba(52, 99, 252, 0.2);
        }
        .ant-radio-button-wrapper {
          border: none;
          box-shadow: none;
        }
        .ant-radio-button-wrapper {
          height: 30px;
        }
      }
    }
    .selectAgentName {
      float: left;
      width: 100%;
      margin-bottom: 15px;
      span {
        color: #333;
        font-family: 'Poppins', sans-serif;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        //line-height: 32px;
      }

      :global {
        .ant-select {
          width: 50%;
        }
        .ant-select:not(.ant-select-customize-input) .ant-select-selector {
          border-radius: 6px;
          //border: 1px solid #e6e6e6;
          background: #fff;
          box-shadow: none;
          height: 32px;
          overflow: hidden;
          overflow-y: scroll;
          /* 隐藏滚动条 */
          scrollbar-width: none;
          /* firefox */
          -ms-overflow-style: none;
        }
        .ant-select:not(.ant-select-customize-input)
          .ant-select-selector::-webkit-scrollbar {
          display: none;
          /* Chrome Safari */
        }
        .ant-select-selection-placeholder {
          color: #666666 !important;
        }
      }
    }
  }

  .noDataContent {
    width: 100%;
    height: 280px;
    display: grid;
    justify-content: center;
    align-content: center;

    img {
      width: 30%;
      margin-left: 35%;
    }

    p {
      color: #999;
      margin-top: 10px;
      text-align: center;
    }
  }
}
.agentWorkEfficiencyStatistics::-webkit-scrollbar {
  display: none;
  /* Chrome Safari */
}
