.homeContent {
  margin: 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: scroll;

  .noDataContent {
    width: 100%;
    height: 280px;
    display: grid;
    justify-content: center;
    align-content: center;

    img {
      width: 30%;
      margin-left: 35%;
    }

    p {
      color: #999;
      margin-top: 10px;
      text-align: center;
    }
  }

  .updataDate {
    color: #333;
    text-align: right;
    font-family: 'Poppins', sans-serif;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    display: flex;
    align-items: center;
  }

  :global {
    .ant-picker {
      border-radius: 4px;
      //border: 1px solid #e6e6e6;
      background: #fff;
      height: 28px;
      margin-left: 3px;
    }
  }

  .agentWorkloadReport1 {
    display: flex;
    gap: 20px;
  }

  .agentWorkloadReport2 {
    display: flex;
    gap: 20px;
    height: 180px;
  }

  .agentWorkloadReport5 {
    display: flex;
    height: 100%;
    gap: 20px;
  }
}

.agentWorkloadReportCard {
  border-radius: 4px;
  background: #fff;
  margin-bottom: 20px;
  /* 模块阴影 */
  box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.15);
  // padding: 20px;

  .agentWorkloadReportCardTitle {
    color: #333;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    margin-top: 15px;
    margin-left: 20px;
    margin-right: 20px;
    margin-bottom: 15px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    z-index: 999;

    .agentWorkloadReportCardTitleSpan {
      display: flex;
      align-items: center;
    }

    .agentWorkloadReportCardTitleSpanSpan {
      overflow: hidden;
      text-overflow: ellipsis;
      display: inline-block;
    }

    /* 21px */
    .detailTipsIcon {
      // max-width: 20%;
      width: 20px;
      height: 20px;
      margin-left: 4px;
      cursor: pointer;
    }

    .chartImg {
      width: 16px;
      margin-top: -3px;
    }

    :global {
      .ant-radio-group {
        border: 1px solid #3968fc;
        border-radius: 4px;
      }

      .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled)::before {
        background-color: #fff;
      }

      .ant-radio-button-wrapper:not(:first-child)::before {
        width: 0px;
      }

      .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled) {
        border: none;
        box-shadow: none;
        //border-radius:4px 0px 0px 4px;
        background: rgba(52, 99, 252, 0.2);
      }

      .ant-radio-button-wrapper {
        border: none;
        box-shadow: none;
      }

      .ant-radio-button-wrapper {
        height: 30px;
        color: #666;
        font-weight: normal;
      }

      .ant-radio-button-wrapper-checked {
        font-weight: normal;
        color: #3463fc;
      }
    }
  }

  .selectAgentName {
    margin-left: 20px;
    margin-top: -10px;
    width: 60%;

    span {
      color: #333;
      font-family: 'Poppins', sans-serif;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      //line-height: 32px;
    }

    :global {
      .ant-select {
        width: 80%;
      }

      .ant-select-selection-placeholder {
        color: #999;
      }

      .ant-select:not(.ant-select-customize-input) .ant-select-selector {
        border-radius: 6px;
        //border: 1px solid #e6e6e6;
        background: #fff;
        box-shadow: none;
      }
    }
  }

  :global {
    .ant-table {
      margin-top: 20px;
      margin-left: 20px;
      margin-right: 20px;
    }
  }

  .dataInsightText {
    color: #000;
    font-family: 'Poppins', sans-serif;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin: -5px 20px;
    margin-bottom: 10px;
    // overflow: scroll;
    // height: 100px;

    span {
      color: #3463fc;
      font-family: 'Poppins', sans-serif;
      font-size: 14px;
      font-style: normal;
      font-weight: 800;
      line-height: 24px;
    }
  }

  .channelBoxSon {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: -15px;
    // position: absolute;
    // left: 18%;
    // top: 30%;
  }

  .channelBoxSonTop {
    color: #3463fc;
    font-family: 'Poppins', sans-serif;
    font-size: 32px;
    font-style: normal;
    font-weight: 700;
    line-height: 60px;
    /* 150% */
  }

  .channelBoxSonMiddle {
    margin-bottom: 10px;
  }

  .channelBoxSonBottom {
    color: #333;
    opacity: 0.6;
    text-align: center;
    font-family: 'Poppins', sans-serif;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    /* 18px */
  }

  .greenTop {
    display: inline-flex;
    padding: 3px 8px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: 4px;
    background: rgba(103, 231, 39, 0.1);
  }

  .redBottom {
    display: inline-flex;
    padding: 3px 8px;
    justify-content: center;
    align-items: center;
    gap: 4px;
    border-radius: 4px;
    background: rgba(238, 59, 49, 0.1);
  }
}

.twoDetailItem {
  width: 100%;

  .oneItem {
    width: 100%;
    height: 24px;
    border-bottom: 1px solid #e6e6e6;

    .leftItem {
      width: 42%;
      height: 24px;
      float: left;
      color: #666;
      font-family: 'Poppins', sans-serif;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
      text-align: left;
      padding-left: 6%;
    }

    .rightItem {
      // width: 42%;
      height: 24px;
      float: left;
      color: #666;
      font-family: 'Poppins', sans-serif;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
      text-align: center;
    }

    .centerItem {
      width: 10%;
      height: 24px;
      float: left;
    }
  }

  .twoItem {
    width: 100%;
    height: 24px;
    line-height: 24px;
    margin-top: 1px;
    cursor: pointer;

    .leftleftItem {
      width: 12%;
      height: 24px;
      float: left;
    }

    .leftItem {
      width: 60%;
      height: 24px;
      float: left;
      text-align: left;
      padding-left: 5%;

      img {
        width: 16px;
        margin-right: 3px;
      }
    }

    .rightItem {
      width: 40%;
      height: 24px;
      float: left;
      text-align: center;
      padding-right: 10%;

      img {
        width: 16px;
        margin-right: 3px;
      }
    }

    .centerItem {
      width: 16%;
      height: 24px;
      float: left;
    }
  }

  .twoItem:hover {
    background-color: rgba(52, 99, 252, 0.05);
  }
}
