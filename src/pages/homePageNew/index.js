import React, { useState, useEffect, useRef, use } from 'react';
import Footer from '@/components/Footer';
import Header from '@/components/Header';
import { FormattedMessage, Link, getLocale, useIntl, history } from 'umi';
import { Button, Image } from 'antd';
import styles from './index.less';
import anime from './anime.less';
import DynamicText from './DynamicText';
import AOS from 'aos';
import 'aos/dist/aos.css';
import ChatWidget from '@/components/chatWidget';
import testLogo from '@/assets/testLogo.jpg';
import TocCardBg from '@/components/TocCardBg';
import MotionFade from '@/components/MotionFade';
import { motion } from 'framer-motion';
import { getInstanceId } from '@/utils/utils';
const HomePageNew = () => {
  let instanceId = getInstanceId();
  const [currentImg, setCurrentImg] = useState(
    require('@/assets/homePageNew/product/jieru.png'),
  );
  useEffect(() => {
    const isMobile = /Android|iPhone|iPad|iPod|Windows Phone|BlackBerry/i.test(
      navigator.userAgent,
    );
    if (isMobile) {
      history.replace('/homePage');
    }
  }, []);
  const productList = [
    {
      title: 'home.page.new.product.channel.title',
      desc: 'home.page.new.product.channel.desc',
      img: require('@/assets/homePageNew/product/jieru.png'),
      path: '/productChannel',
    },
    {
      title: 'home.page.new.product.callcenter.title',
      desc: 'home.page.new.product.callcenter.desc',
      img: require('@/assets/homePageNew/product/aihujiao.png'),
      path: '/productAiCallCenter',
    },
    {
      title: 'home.page.new.product.agent.title',
      desc: 'home.page.new.product.agent.desc',
      img: require('@/assets/homePageNew/product/agent.png'),
      path: '/productAiAgent',
    },
    {
      title: 'home.page.new.product.aigc.title',
      desc: 'home.page.new.product.aigc.desc',
      img: require('@/assets/homePageNew/product/kefu.png'),
      path: '/productAIGCCustomerService',
    },
    {
      title: 'home.page.new.product.assist.title',
      desc: 'home.page.new.product.assist.desc',
      img: require('@/assets/homePageNew/product/zuoxifuzhu.png'),
      path: '/productAIGCAssistant',
    },
    {
      title: 'home.page.new.product.ticket.title',
      desc: 'home.page.new.product.ticket.desc',
      img: require('@/assets/homePageNew/product/zhinenggongdan.png'),
      path: '/productSmartWorkOrder',
    },
    {
      title: 'home.page.new.product.voice.title',
      desc: 'home.page.new.product.voice.desc',
      img: require('@/assets/homePageNew/product/jiqiren.png'),
      path: '/productAiVoiceRobot',
    },
    {
      title: 'home.page.new.product.marketing.title',
      desc: 'home.page.new.product.marketing.desc',
      img: require('@/assets/homePageNew/product/marketing.png'),
      path: '/productAIGCMarketing',
    },
    {
      title: 'home.page.new.product.video.title',
      desc: 'home.page.new.product.video.desc',
      img: require('@/assets/homePageNew/product/video.png'),
      path: '/productVideoCustomerService',
    },
    {
      title: 'home.page.new.product.report.title',
      desc: 'home.page.new.product.report.desc',
      img: require('@/assets/homePageNew/product/dataReport.png'),
      path: '/productDataReport',
    },
  ];

  const marqueeList = [
    {
      title: '制造业',
      id: 'home.page.new.carousel.item.manufacturing',
      img: require('@/assets/homePageNew/zhizaoye.png'),
    },
    {
      title: '餐饮业',
      id: 'home.page.new.carousel.item.restaurant',
      img: require('@/assets/homePageNew/canyin.png'),
    },
    {
      title: '互联网',
      id: 'home.page.new.carousel.item.internet',
      img: require('@/assets/homePageNew/internet.png'),
    },
    {
      title: '教育行业',
      id: 'home.page.new.carousel.item.education',
      img: require('@/assets/homePageNew/edu.png'),
    },
    {
      title: '能源行业',
      id: 'home.page.new.carousel.item.energy',
      img: require('@/assets/homePageNew/nengyuan.png'),
    },
    {
      title: '零售电商',
      id: 'home.page.new.carousel.item.retail',
      img: require('@/assets/homePageNew/lingshou.png'),
    },
    {
      title: '游戏行业',
      id: 'home.page.new.carousel.item.game',
      img: require('@/assets/homePageNew/game.png'),
    },
  ];
  const locale = getLocale();
  const intl = useIntl();
  const [dynamicText, setDynamicText] = useState([
    'AIGC',
    'RAG',
    'AI Agent',
    'API',
    '全球线路',
    '云平台',
  ]);
  // 是否是首次加载
  const [isFirstLoad, setIsFirstLoad] = useState(false);
  useEffect(() => {
    const visited = sessionStorage.getItem('isFirstLoad');
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        sessionStorage.removeItem('isFirstLoad');
      }
    };
    document.addEventListener('visibilitychange', handleVisibilityChange);
    if (!visited) {
      // 如果没有标记 (即首次访问)
      setIsFirstLoad(true); // 设置状态以显示元素
      sessionStorage.setItem('isFirstLoad', true); // 在 sessionStorage 中标记已访问
    } else {
      // 如果已经标记过 (非首次访问)
      setIsFirstLoad(false); // 确保不显示元素
    }
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);
  useEffect(() => {
    if (locale === 'zh-CN') {
      setDynamicText(['AIGC', 'RAG', 'AI Agent', 'API', '全球线路', '云平台']);
    } else {
      setDynamicText(['AIGC', 'RAG', 'AI Agent', 'API', 'Global', 'Cloud']);
    }
  }, [locale, intl]);

  useEffect(() => {
    AOS.refresh();
  }, [currentImg]);

  const handleMouseEnter = newImg => {
    setCurrentImg(newImg);
  };

  return (
    <>
      <div className={styles.homePageNew}>
        <Header />
        <div className={styles.bannerContent}>
          <div className={styles.left}>
            <div className={styles.bannerTitle}>
              <FormattedMessage id="home.page.new.banner.title" />
            </div>
            <div
              className={`${styles.bannerSubTitle} ${locale != 'zh-CN' &&
                styles.bannerSubTitleEn}`}
            >
              <FormattedMessage id="home.page.new.banner.subTitle" />
            </div>
            <div className={styles.desc}>
              <FormattedMessage id="home.page.new.banner.desc" />
            </div>
            <div className={styles.btnContent}>
              <Link to="/login" className={styles.free}>
                <FormattedMessage id="home.page.new.banner.free.trial" />
              </Link>
            </div>
            {/* <img className={styles.testLogo} src={testLogo} /> */}
          </div>
          <div className={styles.right} data-aos="fade-up">
            <Image
              preview={false}
              src={require('@/assets/homePageNew/homebanner.png')}
              placeholder={
                <Image
                  preview={false}
                  width={756}
                  src={require('@/assets/homePageNew/homebanner.png')}
                />
              }
            />
          </div>
        </div>
        {/* ConnectNow已成功应用于各个行业 轮播 */}
        <div className={styles.carouselContent}>
          {/* <div className={styles.carouselTitle}>
          <FormattedMessage id="home.page.new.carousel.title" />
        </div> */}
          <div className={styles.marquee}>
            <div className={styles.marqueeList}>
              {marqueeList.map((item, index) => (
                <div className={styles.carouselItem} key={index}>
                  <img src={item.img} />
                  <FormattedMessage id={item.id} />
                </div>
              ))}
            </div>
            <div className={styles.marqueeList}>
              {marqueeList.map((item, index) => (
                <div className={styles.carouselItem} key={index}>
                  <img src={item.img} />
                  <FormattedMessage id={item.id} />
                </div>
              ))}
            </div>
          </div>
        </div>
        {/* 客户体验 */}
        <div className={styles.customerExperience}>
          <div className={styles.customerExperienceTitle} data-aos="fade-up">
            <FormattedMessage id="home.page.new.customer.experience.title" />
          </div>
          <div className={styles.customerExperienceDesc} data-aos="fade-up">
            <FormattedMessage id="home.page.new.customer.experience.desc" />
          </div>
          <div className={anime.customerExperienceContent}>
            <div className={anime.customerExperienceList}>
              {dynamicText.map((item, index) => (
                <DynamicText key={index} text={item} />
              ))}
            </div>
            <Image
              data-aos="fade-up"
              preview={false}
              src={require('@/assets/homePageNew/kehutiyan.png')}
            />
          </div>
        </div>
        {/* 产品 */}
        <div className={styles.productDescTitle} data-aos="fade-up">
          <FormattedMessage id="home.page.new.product.title" />
        </div>
        <div className={styles.productContent}>
          <div className={styles.productImg}>
            <img key={currentImg} src={currentImg} data-aos="fade-up" />
          </div>
          <div className={styles.productDesc}>
            <div className={styles.productList} data-aos="fade-up">
              {productList.map((item, index) => (
                <Link
                  to={item.path}
                  className={styles.productItem}
                  key={item.title}
                  onMouseEnter={() => handleMouseEnter(item.img)}
                >
                  <div>
                    <div className={styles.productItemTitle}>
                      <FormattedMessage id={item.title} />
                      <div className={styles.arrowWrapper}>
                        <div className={styles.arrow}></div>
                      </div>
                    </div>
                    <div className={styles.productItemDesc}>
                      <FormattedMessage id={item.desc} />
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
        <div className={styles.amazonContent}>
          <TocCardBg
            titleId="home.page.new.amazon.title"
            descId="home.page.new.amazon.desc"
            children={
              <MotionFade
                as={motion.img}
                type="fadeRight"
                style={{ width: '100%' }}
                src={require('@/assets/homePageNew/amazon.png')}
                alt="amazon"
              />
            }
          />
        </div>
        <div className={styles.startContent}>
          <div className={styles.startTips} data-aos="fade-up">
            <FormattedMessage id="home.page.new.text.start.tips" />
          </div>
          <Button className={styles.startBtn}>
            <Link to="/login">
              <FormattedMessage id="home.page.new.text.start.btn" />
            </Link>
          </Button>
        </div>
        <Footer />
      </div>
      {/* {isFirstLoad && <ChatWidget instanceId={instanceId}></ChatWidget>} */}
      <ChatWidget instanceId={instanceId}></ChatWidget>
    </>
  );
};

export default HomePageNew;
