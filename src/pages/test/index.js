import React, { useState, useRef } from 'react';

const ImagePasteInput = () => {
  const [image, setImage] = useState(null);
  const inputRef = useRef(null);

  const handlePaste = event => {
    const items = event.clipboardData.items;
    for (let item of items) {
      if (item.type.indexOf('image') !== -1) {
        const file = item.getAsFile();
        const reader = new FileReader();
        reader.onload = e => {
          setImage(e.target.result);
        };
        reader.readAsDataURL(file);
        break;
      }
    }
  };

  const handleSend = () => {
    if (image) {
      alert('图片已发送！'); // 实际应用可上传到服务器
      setImage(null);
    }
  };

  return (
    <div className="p-4">
      <textarea
        ref={inputRef}
        className="border p-2 w-full h-24"
        placeholder="粘贴图片到此处..."
        onPaste={handlePaste}
      />
      {image && (
        <div className="mt-4">
          <img src={image} alt="Pasted" className="max-w-full h-40 border" />
          <button
            onClick={handleSend}
            className="block mt-2 bg-blue-500 text-white px-4 py-2 rounded"
          >
            发送图片
          </button>
        </div>
      )}
    </div>
  );
};

export default ImagePasteInput;
