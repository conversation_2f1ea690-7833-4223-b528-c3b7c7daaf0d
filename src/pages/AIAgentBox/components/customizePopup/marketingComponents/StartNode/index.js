import styles from './index.less';
import {
  Input,
  Select,
  notification,
  Spin,
  Checkbox,
  TimePicker,
  DatePicker,
  InputNumber,
} from 'antd';
import { useReactFlow } from 'react-flow-renderer';
import { FormattedMessage, getIntl, useDispatch } from 'umi';
import { useEffect, useState } from 'react';
import { CloseOutlined, PlusOutlined } from '@ant-design/icons';
import moment from 'moment';

const { Option } = Select;

const StartNode = ({ node }) => {
  const { id, data } = node;
  const { customizForms } = data || {};
  const [spinLoading, setSpinLoading] = useState(false);
  const [customerSegmentOptions, setCustomerSegmentOptions] = useState([]);
  const [formData, setFormData] = useState({
    customerSegment: customizForms?.[0]?.customerSegment || null,
    sendFrequency: customizForms?.[0]?.sendFrequency || 'immediate',
    useRecipientTimezone: customizForms?.[0]?.useRecipientTimezone || true,
    fallbackTimezone: customizForms?.[0]?.fallbackTimezone || 'UTC+0',
    // 时间配置
    dailyTimes: customizForms?.[0]?.dailyTimes || [
      moment('12:00', 'HH:mm'),
      moment('18:00', 'HH:mm'),
    ],
    weeklyConfig: customizForms?.[0]?.weeklyConfig || {
      days: [1, 2, 3, 5], // 周一到周五默认选中
      times: [moment('12:00', 'HH:mm')],
    },
    monthlyConfig: customizForms?.[0]?.monthlyConfig || {
      day: 16,
      time: moment('19:00', 'HH:mm'),
    },
    customSchedules: customizForms?.[0]?.customSchedules || [
      { startDate: null, endDate: null, time: moment('19:00', 'HH:mm') },
    ],
  });

  const { setNodes } = useReactFlow();
  const dispatch = useDispatch();

  // 发送频率选项
  const sendFrequencyOptions = [
    {
      value: 'immediate',
      label: getIntl().formatMessage({
        id: 'marketing.start.node.send.frequency.immediate',
      }),
    },
    {
      value: 'daily',
      label: getIntl().formatMessage({
        id: 'marketing.start.node.send.frequency.daily',
      }),
    },
    {
      value: 'weekly',
      label: getIntl().formatMessage({
        id: 'marketing.start.node.send.frequency.weekly',
      }),
    },
    {
      value: 'monthly',
      label: getIntl().formatMessage({
        id: 'marketing.start.node.send.frequency.monthly',
      }),
    },
    {
      value: 'custom',
      label: getIntl().formatMessage({
        id: 'marketing.start.node.send.frequency.custom',
      }),
    },
  ];

  // 时区选项
  const timezoneOptions = [
    {
      value: 'UTC+0',
      label: getIntl().formatMessage({
        id: 'marketing.start.node.timezone.utc0',
      }),
    },
    {
      value: 'UTC+8',
      label: getIntl().formatMessage({
        id: 'marketing.start.node.timezone.utc8',
      }),
    },
    {
      value: 'UTC-5',
      label: getIntl().formatMessage({
        id: 'marketing.start.node.timezone.utc5',
      }),
    },
    {
      value: 'UTC-8',
      label: getIntl().formatMessage({
        id: 'marketing.start.node.timezone.utc8.pacific',
      }),
    },
  ];

  // 周几选项
  const weekDayOptions = [
    {
      value: 1,
      label: getIntl().formatMessage({
        id: 'marketing.start.node.week.monday',
      }),
    },
    {
      value: 2,
      label: getIntl().formatMessage({
        id: 'marketing.start.node.week.tuesday',
      }),
    },
    {
      value: 3,
      label: getIntl().formatMessage({
        id: 'marketing.start.node.week.wednesday',
      }),
    },
    {
      value: 4,
      label: getIntl().formatMessage({
        id: 'marketing.start.node.week.thursday',
      }),
    },
    {
      value: 5,
      label: getIntl().formatMessage({
        id: 'marketing.start.node.week.friday',
      }),
    },
    {
      value: 6,
      label: getIntl().formatMessage({
        id: 'marketing.start.node.week.saturday',
      }),
    },
    {
      value: 0,
      label: getIntl().formatMessage({
        id: 'marketing.start.node.week.sunday',
      }),
    },
  ];

  useEffect(() => {
    // 获取客户细分列表
    getCustomerSegments();
  }, []);

  // 更新节点数据
  useEffect(() => {
    updateNodeData();
  }, [formData]);

  const getCustomerSegments = () => {
    setSpinLoading(true);
    // 模拟客户细分数据，实际应该从后端获取
    setTimeout(() => {
      setCustomerSegmentOptions([
        {
          id: 'segment1',
          name: getIntl().formatMessage({
            id: 'marketing.start.node.customer.segment.one',
          }),
        },
        {
          id: 'segment2',
          name: getIntl().formatMessage({
            id: 'marketing.start.node.customer.segment.two',
          }),
        },
        {
          id: 'segment3',
          name: getIntl().formatMessage({
            id: 'marketing.start.node.customer.segment.high.value',
          }),
        },
        {
          id: 'segment4',
          name: getIntl().formatMessage({
            id: 'marketing.start.node.customer.segment.potential',
          }),
        },
      ]);
      setSpinLoading(false);
    }, 500);
  };

  const updateNodeData = () => {
    setNodes(nds => {
      return nds?.map(item => {
        if (item.id === id && item.data.customizForms) {
          const updatedCustomizForms = [
            {
              ...item.data.customizForms[0],
              ...formData,
              label: getIntl().formatMessage({
                id: 'marketing.start.node.config.label',
              }),
            },
          ];

          return {
            ...item,
            data: {
              ...item.data,
              customizForms: updatedCustomizForms,
            },
          };
        }
        return item;
      });
    });
  };

  const handleCustomerSegmentChange = value => {
    const selectedSegment = customerSegmentOptions.find(
      item => item.id === value,
    );
    setFormData(prev => ({
      ...prev,
      customerSegment: selectedSegment,
    }));
  };

  const handleSendFrequencyChange = value => {
    setFormData(prev => ({
      ...prev,
      sendFrequency: value,
    }));
  };

  const handleTimezoneTypeChange = checked => {
    console.log('checked', checked);

    setFormData(prev => ({
      ...prev,
      useRecipientTimezone: checked,
    }));
  };

  const handleFallbackTimezoneChange = value => {
    setFormData(prev => ({
      ...prev,
      fallbackTimezone: value,
    }));
  };

  // 每日时间配置处理
  const handleDailyTimeChange = (index, value) => {
    const newDailyTimes = [...formData.dailyTimes];
    newDailyTimes[index] = value;
    setFormData(prev => ({
      ...prev,
      dailyTimes: newDailyTimes,
    }));
  };

  const addDailyTime = () => {
    setFormData(prev => ({
      ...prev,
      dailyTimes: [...prev.dailyTimes, moment('12:00', 'HH:mm')],
    }));
  };

  const removeDailyTime = index => {
    const newDailyTimes = formData.dailyTimes.filter((_, i) => i !== index);
    setFormData(prev => ({
      ...prev,
      dailyTimes: newDailyTimes,
    }));
  };

  // 每周配置处理
  const handleWeeklyDaysChange = checkedValues => {
    setFormData(prev => ({
      ...prev,
      weeklyConfig: {
        ...prev.weeklyConfig,
        days: checkedValues,
      },
    }));
  };

  const handleWeeklyTimeChange = (index, time) => {
    const newTimes = [...formData.weeklyConfig.times];
    newTimes[index] = time;
    setFormData(prev => ({
      ...prev,
      weeklyConfig: {
        ...prev.weeklyConfig,
        times: newTimes,
      },
    }));
  };

  const addWeeklyTime = () => {
    setFormData(prev => ({
      ...prev,
      weeklyConfig: {
        ...prev.weeklyConfig,
        times: [...prev.weeklyConfig.times, moment('12:00', 'HH:mm')],
      },
    }));
  };

  const removeWeeklyTime = index => {
    const newTimes = formData.weeklyConfig.times.filter((_, i) => i !== index);
    setFormData(prev => ({
      ...prev,
      weeklyConfig: {
        ...prev.weeklyConfig,
        times: newTimes,
      },
    }));
  };

  // 每月配置处理
  const handleMonthlyDayChange = value => {
    setFormData(prev => ({
      ...prev,
      monthlyConfig: {
        ...prev.monthlyConfig,
        day: value || 1, // 确保值不为null或undefined时设置默认值1
      },
    }));
  };

  const handleMonthlyTimeChange = time => {
    setFormData(prev => ({
      ...prev,
      monthlyConfig: {
        ...prev.monthlyConfig,
        time: time,
      },
    }));
  };

  // 自定义计划处理
  const handleCustomScheduleChange = (index, field, value) => {
    const newSchedules = [...formData.customSchedules];
    newSchedules[index] = {
      ...newSchedules[index],
      [field]: value,
    };
    setFormData(prev => ({
      ...prev,
      customSchedules: newSchedules,
    }));
  };

  const addCustomSchedule = () => {
    setFormData(prev => ({
      ...prev,
      customSchedules: [
        ...prev.customSchedules,
        { startDate: null, endDate: null, time: moment('19:00', 'HH:mm') },
      ],
    }));
  };

  const removeCustomSchedule = index => {
    const newSchedules = formData.customSchedules.filter((_, i) => i !== index);
    setFormData(prev => ({
      ...prev,
      customSchedules: newSchedules,
    }));
  };

  // 渲染时间配置UI
  const renderTimeConfiguration = () => {
    switch (formData.sendFrequency) {
      case 'daily':
        return (
          <div className={styles.formGroup}>
            <span className={styles.label}>
              <FormattedMessage id="marketing.start.node.send.time.select" />
            </span>
            <div className={styles.timeInputGroup}>
              {formData.dailyTimes.map((time, index) => (
                <div key={index} className={styles.timeInputRow}>
                  <TimePicker
                    className={styles.timeInput}
                    value={time}
                    onChange={time => handleDailyTimeChange(index, time)}
                    format="HH:mm"
                    placeholder={getIntl().formatMessage({
                      id: 'marketing.target.customer.time.placeholder',
                    })}
                  />
                  {formData.dailyTimes.length > 1 && (
                    <CloseOutlined
                      className={styles.removeTimeBtn}
                      onClick={() => removeDailyTime(index)}
                    />
                  )}
                </div>
              ))}
              <div className={styles.addTimeBtn} onClick={addDailyTime}>
                <PlusOutlined />
                <span>
                  <FormattedMessage id="marketing.start.node.send.time.add" />
                </span>
              </div>
            </div>
          </div>
        );

      case 'weekly':
        return (
          <div className={styles.formGroup}>
            <span className={styles.label}>
              <FormattedMessage id="marketing.start.node.send.time.select" />
            </span>
            <div className={styles.weeklyConfig}>
              <Checkbox.Group
                className={styles.weekDaysGroup}
                options={weekDayOptions}
                value={formData.weeklyConfig.days}
                onChange={handleWeeklyDaysChange}
              />
              <div className={styles.timeInputGroup}>
                {formData.weeklyConfig.times.map((time, index) => (
                  <div key={index} className={styles.timeInputRow}>
                    <TimePicker
                      className={styles.timeInput}
                      value={time}
                      onChange={time => handleWeeklyTimeChange(index, time)}
                      format="HH:mm"
                      placeholder={getIntl().formatMessage({
                        id: 'marketing.target.customer.time.placeholder',
                      })}
                    />
                    {formData.weeklyConfig.times.length > 1 && (
                      <CloseOutlined
                        className={styles.removeTimeBtn}
                        onClick={() => removeWeeklyTime(index)}
                      />
                    )}
                  </div>
                ))}
                <div className={styles.addTimeBtn} onClick={addWeeklyTime}>
                  <PlusOutlined />
                  <span>
                    <FormattedMessage id="marketing.start.node.send.time.add" />
                  </span>
                </div>
              </div>
            </div>
          </div>
        );

      case 'monthly':
        return (
          <div className={styles.formGroup}>
            <span className={styles.label}>
              <FormattedMessage id="marketing.start.node.send.time.select" />
            </span>
            <div className={styles.monthlyConfig}>
              <div className={styles.monthlyInputGroup}>
                <span className={styles.monthText}>
                  <FormattedMessage id="marketing.start.node.month.text" />
                </span>
                <InputNumber
                  className={styles.dayInput}
                  min={1}
                  max={31}
                  value={formData.monthlyConfig.day}
                  onChange={value => handleMonthlyDayChange(value)}
                />
                <span className={styles.dayText}>
                  <FormattedMessage id="marketing.start.node.day.text" />
                </span>
                <TimePicker
                  className={styles.timeInput}
                  value={formData.monthlyConfig.time}
                  onChange={handleMonthlyTimeChange}
                  format="HH:mm"
                  placeholder={getIntl().formatMessage({
                    id: 'marketing.start.node.time.placeholder',
                  })}
                />
              </div>
            </div>
          </div>
        );

      case 'custom':
        return (
          <div className={styles.formGroup}>
            <span className={styles.label}>
              <FormattedMessage id="marketing.start.node.send.time.select" />
            </span>
            <div className={styles.customConfig}>
              {formData.customSchedules.map((schedule, index) => (
                <div key={index} className={styles.customScheduleRow}>
                  <div className={styles.dateRangeGroup}>
                    <DatePicker
                      className={styles.dateInput}
                      value={
                        schedule.startDate ? moment(schedule.startDate) : null
                      }
                      onChange={date =>
                        handleCustomScheduleChange(
                          index,
                          'startDate',
                          date?.format('YYYY-MM-DD'),
                        )
                      }
                      placeholder="2023-12-16"
                      suffixIcon={<div className={styles.calendarIcon}>📅</div>}
                    />
                    <TimePicker
                      className={styles.timeInput}
                      value={schedule.time}
                      onChange={time =>
                        handleCustomScheduleChange(index, 'time', time)
                      }
                      format="HH:mm"
                      placeholder="19:00"
                    />
                  </div>
                  {formData.customSchedules.length > 1 && (
                    <CloseOutlined
                      className={styles.removeScheduleBtn}
                      onClick={() => removeCustomSchedule(index)}
                    />
                  )}
                </div>
              ))}
              <div className={styles.addTimeBtn} onClick={addCustomSchedule}>
                <PlusOutlined />
                <span>
                  <FormattedMessage id="marketing.start.node.send.time.add" />
                </span>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Spin spinning={spinLoading}>
      <div className={styles.marketingStartNode}>
        {/* 客户细分选择 */}
        <div className={styles.formGroup}>
          <div className={styles.labelRow}>
            <span className={styles.label}>
              <FormattedMessage id="marketing.start.node.customer.segment.select" />
            </span>
            <span className={styles.addLink}>
              <FormattedMessage id="marketing.start.node.customer.segment.add" />
            </span>
          </div>
          <Select
            className={styles.selectInput}
            placeholder={getIntl().formatMessage({
              id: 'marketing.start.node.customer.segment.placeholder',
            })}
            value={formData.customerSegment?.id}
            onChange={handleCustomerSegmentChange}
            suffixIcon={<div className={styles.dropdownIcon}>▼</div>}
          >
            {customerSegmentOptions.map(item => (
              <Option key={item.id} value={item.id}>
                {item.name}
              </Option>
            ))}
          </Select>
        </div>

        <div className={styles.divider}></div>

        {/* 发送频率选择 */}
        <div className={styles.formGroup}>
          <span className={styles.label}>
            <FormattedMessage id="marketing.start.node.send.frequency.select" />
          </span>
          <Select
            className={styles.selectInput}
            value={formData.sendFrequency}
            onChange={handleSendFrequencyChange}
            suffixIcon={<div className={styles.dropdownIcon}>▼</div>}
          >
            {sendFrequencyOptions.map(item => (
              <Option key={item.value} value={item.value}>
                {item.label}
              </Option>
            ))}
          </Select>
          <div className={styles.note}>
            <FormattedMessage id="marketing.start.node.note" />
          </div>
        </div>

        {/* 时间配置 - 根据发送频率动态渲染 */}
        {renderTimeConfiguration()}

        {/* 时区选择 */}
        <div className={styles.formGroup}>
          <span className={styles.label}>
            <FormattedMessage id="marketing.start.node.timezone.select" />
          </span>

          <div className={styles.timezoneCheckboxGroup}>
            <div className={styles.checkboxOption}>
              <Checkbox
                checked={formData.useRecipientTimezone}
                onChange={e => handleTimezoneTypeChange(e.target.checked)}
              >
                <FormattedMessage id="marketing.start.node.timezone.recipient" />
              </Checkbox>
            </div>

            <div className={styles.fallbackOption}>
              <div className={styles.fallbackLabel}>
                <FormattedMessage id="marketing.start.node.timezone.fallback" />
              </div>
              <Select
                className={styles.timezoneSelect}
                value={formData.fallbackTimezone}
                onChange={handleFallbackTimezoneChange}
                suffixIcon={<div className={styles.dropdownIcon}>▼</div>}
              >
                {timezoneOptions.map(item => (
                  <Option key={item.value} value={item.value}>
                    {item.label}
                  </Option>
                ))}
              </Select>
            </div>
          </div>
        </div>
      </div>
    </Spin>
  );
};

export default StartNode;
