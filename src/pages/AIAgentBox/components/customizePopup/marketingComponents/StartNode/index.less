.marketingStartNode {
  width: 380px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  // padding: 16px;
  background: #ffffff;
  border-radius: 8px;

  .formGroup {
    display: flex;
    flex-direction: column;
    gap: 10px;

    .labelRow {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .label {
        font-family: 'Microsoft YaHei UI';
        font-weight: 700;
        font-size: 14px;
        line-height: 1.5em;
        color: #333333;
      }

      .addLink {
        font-family: 'Microsoft YaHei';
        font-weight: 400;
        font-size: 12px;
        line-height: 1.25em;
        color: #3463fc;
        cursor: pointer;

        &:hover {
          text-decoration: underline;
        }
      }
    }

    .label {
      font-family: 'Microsoft YaHei UI';
      font-weight: 700;
      font-size: 14px;
      line-height: 1.5em;
      color: #333333;
    }

    .selectInput {
      width: 380px;
      height: 32px;
      // background: #FFFFFF;
      // border: 1px solid #E6E6E6;
      border-radius: 6px;
    }

    .dropdownIcon {
      font-size: 12px;
      color: #999999;
      user-select: none;
    }

    .note {
      font-family: 'Microsoft YaHei';
      font-weight: 400;
      font-size: 12px;
      line-height: 1.5em;
      color: #999999;
    }

    .timezoneCheckboxGroup {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .checkboxOption {
        .ant-checkbox-wrapper {
          font-family: 'Microsoft YaHei';
          font-weight: 400;
          font-size: 12px;
          line-height: 1.5em;
          color: #333333;

          .ant-checkbox {
            .ant-checkbox-inner {
              width: 16px;
              height: 16px;
              border: 1px solid #e6e6e6;
              background: #ffffff;
              border-radius: 2px;

              &::after {
                width: 11.43px;
                height: 8.1px;
                border: 2px solid #ffffff;
                border-top: 0;
                border-left: 0;
                transform: rotate(45deg) scale(1) translate(-50%, -50%);
                top: 3.43px;
                left: 2.29px;
              }
            }

            &.ant-checkbox-checked .ant-checkbox-inner {
              border-color: #3463fc;
              background: #3463fc;
            }
          }
        }
      }

      .fallbackOption {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .fallbackLabel {
          font-family: 'Microsoft YaHei';
          font-weight: 400;
          font-size: 12px;
          line-height: 1.5em;
          color: #333333;
        }

        .timezoneSelect {
          width: 380px;
          height: 32px;
          border-radius: 6px;
        }
      }
    }

    // 时间配置相关样式
    .timeInputGroup {
      display: flex;
      flex-direction: column;
      gap: 10px;
      width: 380px;

      .timeInputRow {
        display: flex;
        align-items: center;
        gap: 10px;
      }
    }

    .timeInput {
      width: 185px;
      height: 32px;

      .ant-picker-input {
        input {
          font-family: 'Microsoft YaHei';
          font-weight: 400;
          font-size: 14px;
          color: #333333;

          &::placeholder {
            color: #999999;
          }
        }
      }

      .ant-picker-suffix {
        .clockIcon {
          font-size: 16px;
          color: #999999;
        }
      }

      &:hover {
        border-color: #3463fc;
      }

      &.ant-picker-focused {
        border-color: #3463fc;
        box-shadow: 0 0 0 2px rgba(52, 99, 252, 0.1);
      }

      .ant-input-suffix {
        .clockIcon {
          font-size: 16px;
          color: #999999;
        }
      }
    }

    .removeTimeBtn {
      color: #f22417;
      cursor: pointer;
      font-size: 16px;
      padding: 4px;

      &:hover {
        color: #ff4d4f;
        background: rgba(242, 36, 23, 0.1);
        border-radius: 4px;
      }
    }

    .removeScheduleBtn {
      color: #f22417;
      cursor: pointer;
      font-size: 16px;
      padding: 4px;

      &:hover {
        color: #ff4d4f;
        background: rgba(242, 36, 23, 0.1);
        border-radius: 4px;
      }
    }

    .addTimeBtn {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 0 4px;
      width: 380px;
      height: 18px;
      cursor: pointer;
      font-family: 'Microsoft YaHei UI';
      font-weight: 400;
      font-size: 12px;
      color: #3463fc;

      &:hover {
        text-decoration: underline;
      }

      .anticon {
        font-size: 10px;
      }
    }

    // 每周配置样式
    .weeklyConfig {
      display: flex;
      flex-direction: column;
      gap: 10px;
      width: 380px;

      .weekDaysGroup {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        .ant-checkbox-wrapper {
          font-family: 'Microsoft YaHei';
          font-weight: 400;
          font-size: 14px;
          color: #333333;
          width: 68px;
          height: 22px;
          display: flex;
          align-items: center;

          .ant-checkbox {
            .ant-checkbox-inner {
              width: 16px;
              height: 16px;
              border: 1px solid #dcdfe6;
              border-radius: 2px;
              background: #ffffff;

              &::after {
                width: 11.43px;
                height: 8.1px;
                border: 2px solid #ffffff;
                border-top: 0;
                border-left: 0;
                transform: rotate(45deg) scale(1) translate(-50%, -50%);
                top: 3.43px;
                left: 2.29px;
              }
            }

            &.ant-checkbox-checked .ant-checkbox-inner {
              background-color: #3463fc;
              border-color: #3463fc;
            }
          }

          span:last-child {
            margin-left: 8px;
          }
        }
      }
    }

    // 每月配置样式
    .monthlyConfig {
      width: 380px;

      .monthlyInputGroup {
        display: flex;
        align-items: center;
        gap: 4px;

        .monthText {
          font-family: 'Microsoft YaHei';
          font-weight: 400;
          font-size: 12px;
          color: #333333;
          width: 24px;
        }

        .dayInput {
          width: 56px;
          height: 32px;
        }

        .timeInput {
          width: 286px;

          .ant-picker-input {
            input {
              font-family: 'Microsoft YaHei';
              font-weight: 400;
              font-size: 14px;
              color: #333333;

              &::placeholder {
                color: #999999;
              }
            }
          }

          .ant-picker-suffix {
            .clockIcon {
              font-size: 16px;
              color: #999999;
            }
          }

          &:hover {
            border-color: #3463fc;
          }

          &.ant-picker-focused {
            border-color: #3463fc;
            box-shadow: 0 0 0 2px rgba(52, 99, 252, 0.1);
          }
        }
      }
    }

    // 自定义配置样式
    .customConfig {
      display: flex;
      flex-direction: column;
      gap: 10px;
      width: 380px;

      .customScheduleRow {
        display: flex;
        align-items: center;
        gap: 10px;

        .dateRangeGroup {
          display: flex;
          align-items: center;
          gap: 10px;

          .dateInput {
            width: 185px;
            height: 32px;

            .ant-picker-input {
              input {
                font-family: 'Microsoft YaHei';
                font-weight: 400;
                font-size: 14px;
                color: #333333;

                &::placeholder {
                  color: #999999;
                }
              }
            }

            .ant-picker-suffix {
              .calendarIcon {
                font-size: 14px;
                color: #999999;
              }
            }

            &:hover {
              border-color: #3463fc;
            }

            &.ant-picker-focused {
              border-color: #3463fc;
              box-shadow: 0 0 0 2px rgba(52, 99, 252, 0.1);
            }
          }
        }
      }
    }
  }

  .divider {
    width: 380px;
    height: 1px;
    background: rgba(52, 99, 252, 0.5);
    margin: 0;
  }
}

// 下拉选项样式
.ant-select-dropdown {
  .ant-select-item {
    font-family: 'Microsoft YaHei';
    font-weight: 400;
    font-size: 12px;
    line-height: 1.5em;
    color: #333333;
    padding: 8px 12px;

    &:hover {
      background: rgba(52, 99, 252, 0.1);
    }

    &.ant-select-item-option-selected {
      background: rgba(52, 99, 252, 0.1);
      color: #3463fc;
      font-weight: 500;
    }
  }
}
