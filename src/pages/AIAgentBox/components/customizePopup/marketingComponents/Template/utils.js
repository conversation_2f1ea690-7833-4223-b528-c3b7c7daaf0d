// 提取文本中的变量
export const extractVariables = text => {
  if (!text) return [];
  const regex = /\{\{[^}]+\}\}/g;
  const matches = text.match(regex) || [];
  return [...new Set(matches)]; // 去重
};

// 替换文本中的变量
export const replaceVariables = (text, variables) => {
  if (!text || !variables) return text;
  let replacedText = text;
  Object.keys(variables).forEach(key => {
    const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
    replacedText = replacedText.replace(regex, variables[key] || `{{${key}}}`);
  });
  return replacedText;
};

// 从所有模版内容中提取变量
export const extractAllVariables = templateContentList => {
  if (!templateContentList) return [];

  let allVariables = [];

  templateContentList.forEach(content => {
    if (content.text) {
      allVariables = [...allVariables, ...extractVariables(content.text)];
    }

    // 处理按钮中的变量
    if (content.buttons) {
      try {
        const buttons = JSON.parse(content.buttons);
        buttons.forEach(button => {
          if (button.text) {
            allVariables = [...allVariables, ...extractVariables(button.text)];
          }
          if (button.url) {
            allVariables = [...allVariables, ...extractVariables(button.url)];
          }
        });
      } catch (error) {
        console.error('解析按钮数据失败:', error);
      }
    }
  });

  return [...new Set(allVariables)]; // 去重
};

// 按模版内容类型分类提取变量
export const extractVariablesByType = templateContentList => {
  if (!templateContentList) return { HEADER: [], BODY: [], BUTTONS: [] };

  const variablesByType = {
    HEADER: [],
    BODY: [],
    BUTTONS: [],
  };

  templateContentList.forEach(content => {
    const { type } = content;

    // 只处理 HEADER、BODY、BUTTONS 这三种类型
    if (!['HEADER', 'BODY', 'BUTTONS'].includes(type)) {
      return;
    }

    // 提取文本中的变量
    if (content.text) {
      const variables = extractVariables(content.text);
      variablesByType[type] = [...variablesByType[type], ...variables];
    }

    // 处理按钮中的变量（仅对BUTTONS类型）
    if (type === 'BUTTONS' && content.buttons) {
      try {
        const buttons = JSON.parse(content.buttons);
        buttons.forEach(button => {
          if (button.text) {
            const textVars = extractVariables(button.text);
            variablesByType[type] = [...variablesByType[type], ...textVars];
          }
          if (button.url) {
            const urlVars = extractVariables(button.url);
            variablesByType[type] = [...variablesByType[type], ...urlVars];
          }
        });
      } catch (error) {
        console.error('解析按钮数据失败:', error);
      }
    }
  });

  // 对每个类型的变量进行去重
  Object.keys(variablesByType).forEach(type => {
    variablesByType[type] = [...new Set(variablesByType[type])];
  });

  return variablesByType;
};

// 解析按钮数据
export const parseButtons = buttonsStr => {
  if (!buttonsStr) return [];
  try {
    return JSON.parse(buttonsStr);
  } catch (error) {
    console.error('解析按钮数据失败:', error);
    return [];
  }
};

// 根据类型获取模版内容
export const getContentByType = (templateContentList, type) => {
  if (!templateContentList) return null;
  return templateContentList.find(content => content.type === type);
};

// 生成变量的默认值
export const generateDefaultVariables = variables => {
  const defaultValues = {};
  variables.forEach(variable => {
    // 移除 {{ 和 }}
    const key = variable.replace(/[{}]/g, '');
    defaultValues[key] = '';
  });
  return defaultValues;
};
