.templateContainer {
  background: #fff;
  border-radius: 8px;
  width: 400px;

  .templateSelector {
    :global {
      .ant-select-selector {
        border-radius: 6px;
      }
    }
    .selectorHeader {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .selectorTitle {
        font-size: 14px;
        font-weight: 700;
        color: #333;
        line-height: 1.5;
      }

      .addTemplateBtn {
        display: flex;
        align-items: center;
        gap: 4px;
        cursor: pointer;
        color: #3463fc;
        font-size: 12px;
        font-weight: 400;
        line-height: 1.17;
        transition: all 0.2s ease;
      }
    }

    :global(.ant-select) {
      .ant-select-selector {
        border-radius: 6px;
        border: 1px solid #e6e6e6;
        height: 32px;
      }

      .ant-select-selection-placeholder {
        color: #333;
        font-size: 12px;
      }
    }
  }

  .templatePreview {
    background: #f9f9f9;
    max-width: 280px;
    border-radius: 10px;
    border: 1px solid #e6e6e6;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.1);
    padding: 5px;
    position: relative;
    .previewTitle {
      font-size: 12px;
      font-weight: 700;
      color: #333;
      line-height: 1.25;
      margin-bottom: 10px;
      border-radius: 6px;
      opacity: 0.9;
      padding-top: 5px;
    }
  }
}
