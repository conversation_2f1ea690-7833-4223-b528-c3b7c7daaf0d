import React, { useState, useEffect } from 'react';
import { Select, Divider, notification, Spin, Popover } from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';
import { FormattedMessage, getIntl } from 'umi';
import WhatsAppMessage from './components/WhatsAppMessage';
import VariableEditor from './components/VariableEditor';
import {
  extractAllVariables,
  extractVariablesByType,
  generateDefaultVariables,
} from './utils';
import styles from './index.less';
import { useAsyncDispatchWithLoading } from '@/hooks/useAsyncDispatchWithLoading';
const { Option } = Select;
const MarketingMessage = ({ node, onChange }) => {
  const { id, data } = node;
  const { customizForms } = data || {};
  const {
    loading,
    error,
    dispatchAllWithLoading,
  } = useAsyncDispatchWithLoading({
    onError: error => {
      notification.error({
        message: error.message,
      });
    },
  });
  // 状态管理
  const [selectedTemplate, setSelectedTemplate] = useState();
  const [variables, setVariables] = useState([]);
  const [variablesByType, setVariablesByType] = useState({
    HEADER: [],
    BODY: [],
    BUTTONS: [],
  });
  // 变量值分别存储在 body/buttons
  const [bodyVariableValues, setBodyVariableValues] = useState({});
  const [buttonsVariableValues, setButtonsVariableValues] = useState({});
  const [templateList, setTemplateList] = useState([]); // 模版列表

  // 初始化数据
  useEffect(() => {
    // 获取模版列表
    const fetchTemplateList = async () => {
      const [templateList] = await dispatchAllWithLoading([
        {
          type: 'marketingActivities/queryMarketWhatsTemplateNameByChannelId',
          payload: '1fe9dc49a81af84dbe88a501ffd4eb59',
        },
      ]);
      setTemplateList(templateList?.data || []);
    };
    fetchTemplateList();
    // 从节点数据中恢复状态
    if (customizForms && customizForms.length > 0) {
      // 从节点数据中恢复状态
      const templateItem = customizForms.find(
        item => item.id === 'MarketingMessage',
      );

      if (templateItem && templateItem.templateContentList) {
        // 重建完整的模版对象
        const template = {
          templateContentList: templateItem.templateContentList,
        };
        setSelectedTemplate(template);

        const extractedVars = extractAllVariables(
          templateItem.templateContentList,
        );
        const extractedVarsByType = extractVariablesByType(
          templateItem.templateContentList,
        );
        setVariables(extractedVars);
        setVariablesByType(extractedVarsByType);
        // 分别恢复 body/buttons 的变量
        const bodyContent = templateItem.templateContentList.find(
          c => c.type === 'BODY',
        );
        const buttonsContent = templateItem.templateContentList.find(
          c => c.type === 'BUTTONS',
        );
        setBodyVariableValues(bodyContent?.variableValues || {});
        setButtonsVariableValues(buttonsContent?.variableValues || {});
      }
    }
  }, [customizForms]);

  // 模版选择处理
  const handleTemplateSelect = async templateUuid => {
    // 获取模版详情
    const [templateDetail] = await dispatchAllWithLoading([
      {
        type: 'marketingActivities/queryMarketWhatsTemplateDetail',
        payload: templateUuid,
      },
    ]);
    if (templateDetail?.data) {
      const template = templateDetail?.data;
      setSelectedTemplate(template);
      // 提取变量
      const extractedVars = extractAllVariables(template.templateContentList);
      const extractedVarsByType = extractVariablesByType(
        template.templateContentList,
      );
      setVariables(extractedVars);
      setVariablesByType(extractedVarsByType);
      // 生成默认变量值
      const bodyVars = extractedVarsByType.BODY || [];
      const buttonsVars = extractedVarsByType.BUTTONS || [];
      const defaultBody = {};
      const defaultButtons = {};
      bodyVars.forEach(v => {
        defaultBody[v.replace(/[{}]/g, '')] = '';
      });
      buttonsVars.forEach(v => {
        defaultButtons[v.replace(/[{}]/g, '')] = '';
      });
      setBodyVariableValues(defaultBody);
      setButtonsVariableValues(defaultButtons);
      // 通知父组件数据变化
      handleDataChange(template, defaultBody, defaultButtons);
    }
  };

  // 变量值变化处理（区分 body/buttons）
  const handleVariableChange = (key, value, type) => {
    if (type === 'BODY') {
      const newBody = { ...bodyVariableValues, [key]: value };
      setBodyVariableValues(newBody);
      handleDataChange(selectedTemplate, newBody, buttonsVariableValues);
    } else if (type === 'BUTTONS') {
      const newButtons = { ...buttonsVariableValues, [key]: value };
      setButtonsVariableValues(newButtons);
      handleDataChange(selectedTemplate, bodyVariableValues, newButtons);
    }
  };

  // 数据变化通知
  const handleDataChange = (template, bodyVars, buttonsVars) => {
    const updatedCustomizForms = customizForms
      ? [...customizForms]
      : [
          {
            id: 'MarketingMessage',
            templateContentList: null,
          },
        ];
    let templateItem = updatedCustomizForms.find(
      item => item.id === 'MarketingMessage',
    );
    if (!templateItem) {
      templateItem = {
        id: 'MarketingMessage',
        templateContentList: null,
      };
      updatedCustomizForms.push(templateItem);
    }
    // 更新模版和变量数据
    templateItem.templateContentList = template?.templateContentList;
    templateItem.templateUuid = template?.templateUuid;
    // 分别写入 body/buttons 的变量
    if (templateItem.templateContentList) {
      templateItem.templateContentList.forEach(content => {
        if (content.type === 'BODY') {
          content.variableValues = bodyVars;
        } else if (content.type === 'BUTTONS') {
          content.variableValues = buttonsVars;
        }
      });
    }
    onChange?.({
      ...node,
      data: {
        ...data,
        customizForms: updatedCustomizForms,
      },
    });
  };

  // 添加新模版（预留接口）
  const handleAddTemplate = () => {
    window.open('https://business.facebook.com/business/loginpage', '_blank');
  };

  return (
    <Spin spinning={loading}>
      <div className={styles.templateContainer}>
        {/* 模版选择区域 */}
        <div className={styles.templateSelector}>
          <div className={styles.selectorHeader}>
            <span className={styles.selectorTitle}>
              <FormattedMessage id="marketing.template.select.title" />
            </span>
            <div className={styles.addTemplateBtn} onClick={handleAddTemplate}>
              <span>
                <FormattedMessage id="marketing.template.add" />
              </span>
              <div className={styles.addIcon}>
                <Popover
                  content={
                    <div
                      style={{
                        color: '#FFFFFF',
                        fontFamily: 'Microsoft YaHei',
                        fontSize: '12px',
                        padding: '0',
                        maxWidth: '265px',
                      }}
                    >
                      <FormattedMessage id="marketing.template.tooltip" />
                    </div>
                  }
                  placement="topRight"
                  overlayClassName="custom-popover-tooltip"
                  overlayStyle={{
                    maxWidth: '280px',
                  }}
                  overlayInnerStyle={{
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    border: '1px solid #FFFFFF',
                    borderRadius: '4px',
                    boxShadow: '0px 2px 5px 0px rgba(0, 0, 0, 0.15)',
                    backdropFilter: 'blur(5px)',
                  }}
                >
                  <InfoCircleOutlined />
                </Popover>
              </div>
            </div>
          </div>

          <Select
            placeholder={getIntl().formatMessage({
              id: 'marketing.template.select.placeholder',
            })}
            style={{ width: '100%', marginBottom: '20px' }}
            value={selectedTemplate?.templateUuid}
            onChange={handleTemplateSelect}
            showSearch
            allowClear
            filterOption={(input, option) =>
              option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
          >
            {templateList?.map(template => (
              <Option key={template.templateUuid} value={template.templateUuid}>
                {template.templateName}
              </Option>
            ))}
          </Select>
        </div>

        {/* 模版预览区域 */}
        <div className={styles.templatePreview}>
          <div className={styles.previewTitle}>
            <FormattedMessage id="marketing.template.preview.title" />
          </div>
          <WhatsAppMessage
            templateContentList={selectedTemplate?.templateContentList}
            variables={bodyVariableValues}
          />
        </div>

        <Divider style={{ margin: '20px 0' }} />

        {/* 变量编辑区域 */}
        {variables.length > 0 && (
          <div className={styles.variableSection}>
            <VariableEditor
              variables={variables}
              variablesByType={variablesByType}
              variableValues={{
                ...bodyVariableValues,
                ...buttonsVariableValues,
              }}
              onVariableChange={(key, value, type) =>
                handleVariableChange(key, value, type)
              }
            />
          </div>
        )}
      </div>
    </Spin>
  );
};

export default MarketingMessage;
