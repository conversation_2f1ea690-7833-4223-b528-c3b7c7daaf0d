import React from 'react';
import { Button } from 'antd';
import ReactMarkdown from 'react-markdown';
import { FormattedMessage } from 'umi';
import { getContentByType, parseButtons, replaceVariables } from '../utils';
import styles from './WhatsAppMessage.less';

const WhatsAppMessage = ({ templateContentList, variables = {} }) => {
  if (!templateContentList || templateContentList.length === 0) {
    return (
      <div className={styles.whatsappMessage}>
        <div className={styles.emptyTemplate}>
          <FormattedMessage id="marketing.template.empty" />
        </div>
      </div>
    );
  }

  const headerContent = getContentByType(templateContentList, 'HEADER');
  const bodyContent = getContentByType(templateContentList, 'BODY');
  const footerContent = getContentByType(templateContentList, 'FOOTER');
  const buttonsContent = getContentByType(templateContentList, 'BUTTONS');

  const renderHeader = () => {
    if (!headerContent) return null;

    if (headerContent.format === 'IMAGE' && headerContent.text) {
      return (
        <div className={styles.messageHeader}>
          <img
            src={headerContent.text}
            alt="Template header"
            className={styles.headerImage}
            onError={e => {
              e.target.style.display = 'none';
            }}
          />
        </div>
      );
    }

    if (headerContent.text) {
      return (
        <div className={styles.messageHeader}>
          <div className={styles.headerText}>
            {replaceVariables(headerContent.text, variables)}
          </div>
        </div>
      );
    }

    return null;
  };

  const renderBody = () => {
    if (!bodyContent || !bodyContent.text) return null;

    const bodyText = replaceVariables(bodyContent.text, variables);

    return (
      <div className={styles.messageBody}>
        <ReactMarkdown>{bodyText}</ReactMarkdown>
      </div>
    );
  };

  const renderFooter = () => {
    if (!footerContent || !footerContent.text) return null;

    return (
      <div className={styles.messageFooter}>
        {replaceVariables(footerContent.text, variables)}
      </div>
    );
  };

  const renderButtons = () => {
    if (!buttonsContent || !buttonsContent.buttons) return null;

    const buttons = parseButtons(buttonsContent.buttons);
    if (buttons.length === 0) return null;

    return (
      <div className={styles.messageButtons}>
        {buttons.map((button, index) => {
          const buttonText = replaceVariables(button.text, variables);
          const buttonUrl = button.url
            ? replaceVariables(button.url, variables)
            : '';
          return (
            <div key={index} className={styles.buttonWrapper}>
              <Button type="text">{buttonText}</Button>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className={styles.whatsappMessage}>
      {/* <div className={styles.messageTime}>11:59</div> */}
      <div className={styles.messageContent}>
        {renderHeader()}
        {renderBody()}
        {renderFooter()}
      </div>
      {renderButtons()}
    </div>
  );
};

export default WhatsAppMessage;
