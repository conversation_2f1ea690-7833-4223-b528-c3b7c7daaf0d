.whatsappMessage {
  display: flex;
  flex-direction: column;
  border-radius: 10px;
  border: 1px solid #e6e6e6;
  min-height: 200px;
  width: 100%;
  background-color: #fff;
  justify-content: center;
  .emptyTemplate {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    color: #999;
    font-size: 14px;
  }
}

.messageTime {
  font-size: 12px;
  color: #999;
  text-align: right;
  margin-bottom: 5px;
}

.messageContent {
  padding: 10px;

  .messageHeader {
    margin-bottom: 8px;

    .headerImage {
      width: 100%;
      max-width: 260px;
      height: auto;
      border-radius: 6px;
      object-fit: cover;
    }

    .headerText {
      font-weight: bold;
      color: #333;
      font-size: 14px;
      line-height: 1.4;
    }
  }

  .messageBody {
    margin-bottom: 8px;
    color: #333;
    font-size: 12px;
    line-height: 1.4;
    word-wrap: break-word;
  }

  .messageFooter {
    color: #666;
    font-size: 12px;
    line-height: 1.4;
    margin-bottom: 8px;
  }
}

.messageButtons {
  padding-top: 8px;
  margin-top: 8px;
  width: 100%;
  .buttonWrapper {
    width: 100%;
    height: auto;
    padding: 8px;
    text-align: center;
    border-top: 1px solid #e6e6e6;
    color: #3463fc;
    font-size: 14px;
    font-weight: 500;
    :global {
      .ant-btn {
        border: none;
        &:hover {
          background: none;
        }
      }
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
}
