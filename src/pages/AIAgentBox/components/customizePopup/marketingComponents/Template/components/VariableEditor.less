.variableEditor {
  .emptyVariables {
    padding: 20px;
    text-align: center;
    color: #999;
    font-size: 14px;
  }
}

.variableGroup {
  margin-bottom: 20px;

  .groupTitle {
    font-size: 14px;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
  }
}

.variableItem {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 15px 0;
  &:last-child {
    border-bottom: none;
  }
}

.variableHeader {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  min-width: 84px;

  .variableLabel {
    font-size: 12px;
    font-weight: 700;
    color: #333;
    line-height: 1.5;
    text-align: center;
  }

  .variableTag {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 2px 8px;
    background: #3463fc;
    color: #ffffff;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    line-height: 1.5;
  }
}

.variableInputSection {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;

  .inputLabel {
    font-size: 12px;
    font-weight: 700;
    color: #333;
    line-height: 1.5;
  }

  .inputGroup {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    :global {
      .ant-dropdown-menu {
        max-height: 350px;
        overflow-y: scroll;
      }
    }
  }
}

// 系统变量标签样式
.systemVarTag {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2px 8px;
  background: #eb903b;
  color: #ffffff;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  line-height: 1.5;
  margin-left: 4px;
}
