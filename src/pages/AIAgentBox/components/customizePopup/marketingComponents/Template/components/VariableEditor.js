import React from 'react';
import { FormattedMessage, getIntl } from 'umi';
import styles from './VariableEditor.less';
import HighlightInput from '../../../components/highLightInput';

const VariableEditor = ({
  variables = [],
  variablesByType = { HEADER: [], BODY: [], BUTTONS: [] },
  variableValues = {},
  onVariableChange,
}) => {
  // 新增参数 type，区分 BODY/BUTTONS
  const handleVariableChange = (value, index, index2, setKey, type) => {
    onVariableChange?.(setKey, value, type);
  };

  const renderVariableInput = (variable, index, type) => {
    const key = variable.replace(/[{}]/g, '');
    const currentValue = variableValues[key] || '';

    return (
      <div key={index} className={styles.variableItem}>
        <div className={styles.variableHeader}>
          <span className={styles.variableLabel}>
            <FormattedMessage id="marketing.variable.need.replace" />
          </span>
          <div className={styles.variableTag}>{variable}</div>
        </div>

        <div className={styles.variableInputSection}>
          <span className={styles.inputLabel}>
            <FormattedMessage id="marketing.variable.replace.input" />
          </span>
          <div className={styles.inputGroup}>
            <HighlightInput
              setText={(v, i, i2, k) => handleVariableChange(v, i, i2, k, type)}
              text={currentValue}
              setKey={key}
              noEmoji={true}
              backgroundColor={'#fff'}
              placeholder={getIntl().formatMessage({
                id: 'marketing.variable.select.placeholder',
              })}
            />
          </div>
        </div>
      </div>
    );
  };

  const renderVariableGroup = (titleId, variables, groupKey) => {
    if (variables.length === 0) return null;

    return (
      <div className={styles.variableGroup}>
        <div className={styles.groupTitle}>
          <FormattedMessage id={titleId} />
        </div>
        {variables.map((variable, index) =>
          renderVariableInput(variable, `${groupKey}-${index}`, groupKey),
        )}
      </div>
    );
  };

  // 检查是否有任何变量
  const hasAnyVariables =
    variablesByType.HEADER.length > 0 ||
    variablesByType.BODY.length > 0 ||
    variablesByType.BUTTONS.length > 0;

  return (
    <div className={styles.variableEditor}>
      {!hasAnyVariables ? (
        <div className={styles.emptyVariables}>
          <span>
            <FormattedMessage id="marketing.variable.no.variables" />
          </span>
        </div>
      ) : (
        <>
          {renderVariableGroup(
            'marketing.variable.header.title',
            variablesByType.HEADER,
            'HEADER',
          )}
          {renderVariableGroup(
            'marketing.variable.body.title',
            variablesByType.BODY,
            'BODY',
          )}
          {renderVariableGroup(
            'marketing.variable.buttons.title',
            variablesByType.BUTTONS,
            'BUTTONS',
          )}
        </>
      )}
    </div>
  );
};

export default VariableEditor;
