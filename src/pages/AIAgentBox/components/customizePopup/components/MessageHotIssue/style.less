.messageTextBox {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 400px;
  .emojiDetailContainer {
    position: absolute;
    z-index: 2;
    left: 0;
    top: 20px;
    height: 33vh;
    overflow: hidden;
    overflow-y: scroll;
  }

  .constDetailContainer {
    position: absolute;
    z-index: 2;
    left: 0;
    top: 20px;
  }
  .translateButton {
    width: 25%;
    height: 29px;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    padding: 6px 18px;
    background: linear-gradient(56deg, #187cfc 20.15%, #7700fe 79.85%);
    color: #fff;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
  .messageTextBox_title {
    color: #333;
    font-size: 12px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    /* 21px */
  }
  .askQuestionText_line {
    width: 100%;
    min-height: 1px;
    background: rgba(137, 116, 255, 0.5);
  }
  .formTitle {
    color: red;
  }
  .moreVar {
    position: absolute;
    top: -28px;
    right: 0;
    font-size: 12px;
    color: #3463fc;
    cursor: pointer;
  }
  :global {
    .ant-tabs-tab-btn {
      font-size: 12px;
    }
    .ant-tabs-tab {
      padding: 4px 10px !important;
      border-top-left-radius: 4px !important;
      border-top-right-radius: 4px !important;
      // background: #e6e6e6 !important;
    }
    .ant-picker {
      border-radius: 6px;
    }
    .ant-form-item {
      margin-bottom: 10px;
      position: relative;
    }
    .ant-col-1 {
      padding-left: 0px !important;
    }
    .ant-input {
      font-size: 12px;
      height: 32px;
      box-shadow: none;
      border-radius: 6px;
      background: #fff;
    }
    .ant-select {
      font-size: 12px;
    }
    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      height: 32px;
      box-shadow: none;
      border-radius: 6px;
      background: #fff;
    }
    textarea.ant-input {
      font-size: 12px;
    }
    .ant-input-number {
      width: 100%;
      font-size: 12px;
      height: 32px;
      box-shadow: none;
      border-radius: 6px;
      background: #fff;
    }
    .ant-input-number-handler-wrap {
      border-radius: 0 6px 6px 0;
    }
    .ant-input-number-handler-down {
      border-bottom-right-radius: 6px;
    }
    .ant-input-number-handler-up {
      border-top-right-radius: 6px;
    }
    .ant-form-item-label > label {
      color: #333;
      font-size: 12px;
      font-style: normal;
      // font-weight: 700;
      line-height: 150%; /* 18px */
    }
    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      display: none;
    }
    .ant-form-item:nth-child(1)
      > .ant-form-item-label
      > label.ant-form-item-required {
      font-weight: bold;
    }
    .ant-form-item-label > label.ant-form-item-required {
      font-weight: 100;
    }
    .ant-form-item:nth-child(1)
      .ant-form-item-label
      label.ant-form-item-required {
      font-weight: bold !important;
    }
    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::after {
      display: inline-block;
      margin-left: 2px;
      color: #ff4d4f;
      font-size: 14px;
      font-family: SimSun, sans-serif;
      line-height: 1;
      content: '*';
      font-weight: bold;
    }

    .ant-radio-checked .ant-radio-inner {
      border-color: #3463fc;
    }
    .ant-radio-inner::after {
      background-color: #3463fc;
    }
    .ant-radio-wrapper {
      font-size: 12px !important;
    }
    .ant-select-multiple .ant-select-selection-item {
      border-radius: 4px;
      border: 1px solid rgba(52, 99, 252, 0.5);
      background: linear-gradient(
          0deg,
          rgba(52, 99, 252, 0.1) 0%,
          rgba(52, 99, 252, 0.1) 100%
        ),
        #fff;
      .anticon {
        color: #3463fc;
      }
    }
    .ant-select-multiple .ant-select-selection-item-content {
      color: #3463fc;
      font-size: 12px;
    }
  }

  :global {
    .transModalContent {
      :global {
        .ant-select-selection-item {
          border: 1px solid #1890ff; /* 边框颜色改为蓝色 */
          background-color: transparent; /* 背景透明 */
          color: #1890ff; /* 文字颜色改为蓝色 */
          border-radius: 4px; /* 圆角 */
          padding: 0 6px; /* 内边距 */
          height: 28px; /* 高度 */
          line-height: 26px; /* 行高，比高度小 2px 以视觉上居中 */
        }
        .ant-select-selection-item-close {
          color: #1890ff; /* 关闭按钮颜色改为蓝色 */
          margin-left: 4px;
        }
      }
    }

    .ant-modal-content {
      .ant-modal-body {
        padding: 10px 20px 0px 20px !important;
      }
    }
    .ant-modal-content {
      .transModalClose {
        position: absolute;
        right: 0;
        top: 0;
        cursor: pointer;
        transition: all 0.3s ease-in-out;
      }
    }
    .ant-input {
      height: 120;
      border-radius: 6px;
      border: 1px solid #e6e6e6;
      background: #fff;
    }

    .ant-input:focus,
    .ant-input-focused {
      box-shadow: none;
    }

    .ant-input:hover {
      box-shadow: none;
    }
  }
}
.messageTextBoxForm {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  border: 1px solid #e6e6e6;
  padding: 5px;
  border-radius: 10px;
  margin-bottom: 10px;
  background: rgba(52, 99, 252, 0.05);
  width: 100%;
  .messageTextBox_title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: row;
    flex-wrap: nowrap;
    .messageTextBox_title_switchIcon {
      cursor: pointer;
    }
  }
  .emojiDetailContainer {
    position: absolute;
    z-index: 2;
    left: 0;
    top: 20px;
    height: 33vh;
    overflow: hidden;
    overflow-y: scroll;
  }

  .constDetailContainer {
    position: absolute;
    z-index: 2;
    left: 0;
    top: 20px;
  }

  .messageTextBox_title {
    color: #333;
    font-size: 12px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    /* 21px */
  }
  .askQuestionText_line {
    width: 100%;
    min-height: 1px;
    background: rgba(137, 116, 255, 0.5);
  }
  .moreVar {
    position: absolute;
    top: -28px;
    right: 0;
    font-size: 12px;
    color: #3463fc;
    cursor: pointer;
  }
  :global {
    .ant-picker {
      border-radius: 6px;
    }
    .ant-form-item {
      margin-bottom: 10px;
    }
    .ant-col-1 {
      padding-left: 0px !important;
    }
    .ant-input {
      font-size: 12px;
      height: 32px;
      box-shadow: none;
      border-radius: 6px;
      background: #fff;
    }
    .ant-select {
      font-size: 12px;
    }
    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      height: 32px;
      box-shadow: none;
      border-radius: 6px;
      background: #fff;
    }
    textarea.ant-input {
      font-size: 12px;
    }
    .ant-input-number {
      width: 100%;
      font-size: 12px;
      height: 32px;
      box-shadow: none;
      border-radius: 6px;
      background: #fff;
    }
    .ant-input-number-handler-wrap {
      border-radius: 0 6px 6px 0;
    }
    .ant-input-number-handler-down {
      border-bottom-right-radius: 6px;
    }
    .ant-input-number-handler-up {
      border-top-right-radius: 6px;
    }
    .ant-form-item-label > label {
      color: #333;
      font-size: 12px;
      font-style: normal;
      font-weight: 700;
      line-height: 150%; /* 18px */
    }
    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      display: none;
    }
    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::after {
      display: inline-block;
      margin-left: 2px;
      color: #ff4d4f;
      font-size: 14px;
      font-family: SimSun, sans-serif;
      line-height: 1;
      content: '*';
    }

    .ant-radio-checked .ant-radio-inner {
      border-color: #3463fc;
    }
    .ant-radio-inner::after {
      background-color: #3463fc;
    }
    .ant-radio-wrapper {
      font-size: 12px !important;
    }
    .ant-select-multiple .ant-select-selection-item {
      border-radius: 4px;
      border: 1px solid rgba(52, 99, 252, 0.5);
      background: linear-gradient(
          0deg,
          rgba(52, 99, 252, 0.1) 0%,
          rgba(52, 99, 252, 0.1) 100%
        ),
        #fff;
      .anticon {
        color: #3463fc;
      }
    }
    .ant-select-multiple .ant-select-selection-item-content {
      color: #3463fc;
      font-size: 12px;
    }
  }
  :global {
    .ant-input {
      height: 120;
      border-radius: 6px;
      border: 1px solid #e6e6e6;
      background: #fff;
    }

    .ant-input:focus,
    .ant-input-focused {
      box-shadow: none;
    }

    .ant-input:hover {
      box-shadow: none;
    }
  }
}
.addButton {
  display: flex;
  justify-content: flex-end;
  cursor: pointer;
  color: #3463fc;
  font-size: 12px;
}
