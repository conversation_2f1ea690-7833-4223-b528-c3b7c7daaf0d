import styles from './style.less';
import { Input, Spin, Form, Select, Checkbox, Tabs, Radio, Modal } from 'antd';
import { useReactFlow } from 'react-flow-renderer';
import { FormattedMessage, getIntl, useDispatch } from 'umi';
import { useEffect, useState } from 'react';
import { CloseOutlined } from '@ant-design/icons';
import HighLightTextarea from '../highLightTextarea';
import { notification } from '@/utils/utils';
import { ReactComponent as ArrowIcon } from '@/assets/arrowOpen.svg'; //文档
import { ReactComponent as DelIcon } from '@/assets/new-channel-delete-icon.svg';
import { getHotTranslationList } from '@/service/aiAgent';
const MessageHotIssue = ({ node, setIsModalOpenVariables }) => {
  const { id, data } = node;
  const { customizForms } = data || {};
  const [loading, setLoading] = useState(false);
  const [activeKey, setActiveKey] = useState(customizForms[0]?.originLanguage);
  const [configType, setConfigType] = useState(customizForms[0]?.configType); // 配置方式
  const [showType, setShowType] = useState(customizForms[0]?.showType); // 展示形式
  const [recentDay, setRecentDay] = useState(customizForms[0]?.recentDay); // 最近x天
  const [topFAQ, setTopFAQ] = useState(customizForms[0]?.topFAQ); // 最多的Top X个 FAQ
  const [originLanguage, setOriginLanguage] = useState(
    customizForms[0]?.originLanguage,
  ); // 原始语言
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [keyRange, setKeyRange] = useState(1);
  // 展示表情包
  const [loadingVar, setLoadingVar] = useState(false);
  const [items, setItems] = useState([]);
  const { setNodes } = useReactFlow();
  const dispatch = useDispatch();
  const [collList, setCollList] = useState(customizForms[0]?.issues || []);
  const [transList, setTransList] = useState([]);
  const changeColl = (value, index, key, eleIndex) => {
    let arr = [...collList];
    let itemIndex = arr.findIndex(item => item.language === activeKey);
    if (key === 'issueContents') {
      arr[itemIndex].issueClassifys[index][key][eleIndex] = value;
    } else {
      arr[itemIndex].issueClassifys[index][key] = value;
    }
    setCollList(arr);
  };
  const [modalLoading, setModalLoading] = useState(false);
  const [transKey, setTransKey] = useState(0);
  const autoConfigJson = {
    configType: '2', // 1 手动；2 自动
    showType: '1', // 1 横；2 竖
    recentDay: '', //最近x天
    topFAQ: '', // 最多的Top X个 FAQ
  };
  const customConfigJson = {
    id: 'MessageHotIssue',
    handle: 'MessageHotIssue',
    configType: '1', // 1 手动；2 自动
    showType: '1', // 1 横；2 竖
    originLanguage: '', //原始语言
    issues: [], //问题列表
  };
  const TransIcon = () => {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
      >
        <rect
          x="7.65793"
          y="2.67386"
          width="0.472509"
          height="3.30756"
          fill="white"
          stroke="#020272"
          stroke-width="0.214777"
        />
        <circle
          cx="7.8682"
          cy="2.40433"
          r="0.720895"
          fill="white"
          stroke="#020272"
          stroke-width="0.214777"
        />
        <g filter="url(#filter0_i_2944_2810)">
          <ellipse
            cx="2.13832"
            cy="8.81761"
            rx="1.13832"
            ry="1.6323"
            fill="white"
          />
        </g>
        <path
          d="M2.13867 7.29272C2.40798 7.29286 2.66335 7.44896 2.85547 7.72437C3.04736 7.99952 3.16886 8.38582 3.16895 8.81714C3.16895 9.24839 3.04723 9.63469 2.85547 9.90991C2.66334 10.1854 2.40803 10.3424 2.13867 10.3425C1.86918 10.3425 1.61409 10.1855 1.42188 9.90991C1.22998 9.63466 1.10742 9.24857 1.10742 8.81714C1.10751 8.38582 1.22999 7.99952 1.42188 7.72437C1.61405 7.44904 1.86934 7.29272 2.13867 7.29272Z"
          stroke="#030178"
          stroke-width="0.214777"
        />
        <g filter="url(#filter1_i_2944_2810)">
          <ellipse
            cx="13.6504"
            cy="8.81761"
            rx="1.13832"
            ry="1.6323"
            fill="white"
          />
        </g>
        <path
          d="M13.6508 7.29272C13.9201 7.29286 14.1754 7.44896 14.3676 7.72437C14.5594 7.99952 14.6809 8.38582 14.681 8.81714C14.681 9.24839 14.5593 9.63469 14.3676 9.90991C14.1754 10.1854 13.9201 10.3424 13.6508 10.3425C13.3813 10.3425 13.1262 10.1855 12.934 9.90991C12.7421 9.63466 12.6195 9.24857 12.6195 8.81714C12.6196 8.38582 12.7421 7.99952 12.934 7.72437C13.1261 7.44904 13.3814 7.29272 13.6508 7.29272Z"
          stroke="#030178"
          stroke-width="0.214777"
        />
        <g filter="url(#filter2_i_2944_2810)">
          <ellipse
            cx="7.93727"
            cy="8.66713"
            rx="5.82045"
            ry="4.83247"
            fill="white"
          />
        </g>
        <path
          d="M7.93713 3.94208C11.1118 3.94208 13.6497 6.07513 13.65 8.66669C13.65 11.2584 11.1119 13.3923 7.93713 13.3923C4.7624 13.3922 2.22424 11.2584 2.22424 8.66669C2.22453 6.07516 4.76258 3.94214 7.93713 3.94208Z"
          stroke="#030178"
          stroke-width="0.214777"
        />
        <g filter="url(#filter3_i_2944_2810)">
          <path
            d="M12.512 9.10791C12.512 11.1901 10.4639 10.6465 7.93729 10.6465C5.41073 10.6465 3.36255 11.1901 3.36255 9.10791C3.36255 7.02569 5.41073 5.33771 7.93729 5.33771C10.4639 5.33771 12.512 7.02569 12.512 9.10791Z"
            fill="#020272"
          />
        </g>
        <path
          d="M7.93774 5.44513C10.4251 5.44533 12.4045 7.10392 12.4045 9.10822C12.4045 9.61134 12.2806 9.93677 12.0745 10.1522C11.8666 10.3693 11.5594 10.491 11.1565 10.5516C10.7534 10.6121 10.2692 10.61 9.72192 10.5916C9.17791 10.5733 8.5716 10.5389 7.93774 10.5389C7.30372 10.5389 6.69673 10.5733 6.15259 10.5916C5.60534 10.61 5.12108 10.6121 4.71802 10.5516C4.31508 10.491 4.00792 10.3693 3.80005 10.1522C3.59388 9.93677 3.47002 9.61134 3.46997 9.10822C3.46997 7.1038 5.45013 5.44513 7.93774 5.44513Z"
          stroke="#030178"
          stroke-width="0.214777"
        />
        <g filter="url(#filter4_d_2944_2810)">
          <path
            d="M6.60633 8.93347C6.72902 8.95493 6.84887 8.87184 6.8393 8.74765C6.835 8.69178 6.82549 8.63627 6.8108 8.58186C6.77644 8.45456 6.71459 8.33634 6.62961 8.23552C6.54463 8.13471 6.43858 8.05375 6.31893 7.99835C6.19928 7.94295 6.06894 7.91445 5.93708 7.91486C5.80523 7.91527 5.67507 7.94459 5.55576 8.00074C5.43646 8.05689 5.33092 8.13851 5.24657 8.23985C5.16222 8.3412 5.10111 8.4598 5.06755 8.58731C5.05321 8.64182 5.04404 8.69739 5.04009 8.75328C5.0313 8.87752 5.15167 8.95987 5.27422 8.93764L5.30228 8.93255C5.40933 8.91314 5.47604 8.80733 5.50373 8.70212C5.52051 8.63836 5.55106 8.57906 5.59324 8.52838C5.63541 8.47771 5.68818 8.4369 5.74783 8.40883C5.80748 8.38075 5.87257 8.3661 5.93849 8.36589C6.00442 8.36568 6.06959 8.37993 6.12942 8.40763C6.18924 8.43533 6.24227 8.47581 6.28476 8.52622C6.32725 8.57663 6.35817 8.63574 6.37535 8.69939C6.4037 8.80443 6.47107 8.90982 6.57824 8.92856L6.60633 8.93347Z"
            fill="#01E3FB"
          />
          <path
            d="M10.5582 8.93338C10.6809 8.95484 10.8008 8.87174 10.7912 8.74756C10.7869 8.69169 10.7774 8.63618 10.7627 8.58177C10.7283 8.45447 10.6665 8.33625 10.5815 8.23543C10.4965 8.13462 10.3905 8.05366 10.2708 7.99826C10.1512 7.94285 10.0208 7.91436 9.88899 7.91477C9.75713 7.91518 9.62697 7.94449 9.50767 8.00064C9.38837 8.05679 9.28283 8.13842 9.19848 8.23976C9.11413 8.3411 9.05302 8.45971 9.01946 8.58722C9.00511 8.64173 8.99595 8.6973 8.99199 8.75319C8.98321 8.87743 9.10357 8.95977 9.22612 8.93755L9.25418 8.93246C9.36123 8.91305 9.42794 8.80724 9.45563 8.70202C9.47241 8.63827 9.50297 8.57897 9.54514 8.52829C9.58732 8.47762 9.64009 8.43681 9.69974 8.40874C9.75939 8.38066 9.82447 8.366 9.8904 8.3658C9.95632 8.36559 10.0215 8.37984 10.0813 8.40754C10.1411 8.43524 10.1942 8.47572 10.2367 8.52613C10.2792 8.57654 10.3101 8.63565 10.3273 8.6993C10.3556 8.80433 10.423 8.90972 10.5301 8.92847L10.5582 8.93338Z"
            fill="#01E3FB"
          />
        </g>
        <defs>
          <filter
            id="filter0_i_2944_2810"
            x="1"
            y="7.09939"
            width="2.27661"
            height="3.35053"
            filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB"
          >
            <feFlood flood-opacity="0" result="BackgroundImageFix" />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="BackgroundImageFix"
              result="shape"
            />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dy="-1.11684" />
            <feGaussianBlur stdDeviation="0.0429553" />
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0.592157 0 0 0 0 0.588235 0 0 0 0 0.682353 0 0 0 1 0"
            />
            <feBlend
              mode="normal"
              in2="shape"
              result="effect1_innerShadow_2944_2810"
            />
          </filter>
          <filter
            id="filter1_i_2944_2810"
            x="12.5121"
            y="7.09939"
            width="2.27661"
            height="3.35053"
            filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB"
          >
            <feFlood flood-opacity="0" result="BackgroundImageFix" />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="BackgroundImageFix"
              result="shape"
            />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dy="-1.11684" />
            <feGaussianBlur stdDeviation="0.0429553" />
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0.592157 0 0 0 0 0.588235 0 0 0 0 0.682353 0 0 0 1 0"
            />
            <feBlend
              mode="normal"
              in2="shape"
              result="effect1_innerShadow_2944_2810"
            />
          </filter>
          <filter
            id="filter2_i_2944_2810"
            x="2.11682"
            y="3.74875"
            width="11.6409"
            height="9.75086"
            filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB"
          >
            <feFlood flood-opacity="0" result="BackgroundImageFix" />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="BackgroundImageFix"
              result="shape"
            />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dy="-1.11684" />
            <feGaussianBlur stdDeviation="0.0429553" />
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0.592157 0 0 0 0 0.588235 0 0 0 0 0.682353 0 0 0 1 0"
            />
            <feBlend
              mode="normal"
              in2="shape"
              result="effect1_innerShadow_2944_2810"
            />
          </filter>
          <filter
            id="filter3_i_2944_2810"
            x="3.36255"
            y="5.33771"
            width="9.14954"
            height="5.41576"
            filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB"
          >
            <feFlood flood-opacity="0" result="BackgroundImageFix" />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="BackgroundImageFix"
              result="shape"
            />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dy="0.773196" />
            <feGaussianBlur stdDeviation="0.0214777" />
            <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0.501961 0 0 0 0 0.517647 0 0 0 0 0.752941 0 0 0 1 0"
            />
            <feBlend
              mode="normal"
              in2="shape"
              result="effect1_innerShadow_2944_2810"
            />
          </filter>
          <filter
            id="filter4_d_2944_2810"
            x="3.75101"
            y="6.66906"
            width="8.3294"
            height="3.60381"
            filterUnits="userSpaceOnUse"
            color-interpolation-filters="sRGB"
          >
            <feFlood flood-opacity="0" result="BackgroundImageFix" />
            <feColorMatrix
              in="SourceAlpha"
              type="matrix"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
              result="hardAlpha"
            />
            <feOffset dy="0.0429553" />
            <feGaussianBlur stdDeviation="0.64433" />
            <feComposite in2="hardAlpha" operator="out" />
            <feColorMatrix
              type="matrix"
              values="0 0 0 0 0.00392157 0 0 0 0 0.890196 0 0 0 0 0.984314 0 0 0 1 0"
            />
            <feBlend
              mode="normal"
              in2="BackgroundImageFix"
              result="effect1_dropShadow_2944_2810"
            />
            <feBlend
              mode="normal"
              in="SourceGraphic"
              in2="effect1_dropShadow_2944_2810"
              result="shape"
            />
          </filter>
        </defs>
      </svg>
    );
  };
  //编辑组件内容
  useEffect(() => {
    // console.log('MessageHotIssue>>>', customizForms);
    setNodes(nds => {
      let newNds = nds?.map(item => {
        let it = item;
        if (item.id === id && item.data.customizForms) {
          item.data.customizForms[0] = {
            ...item.data.customizForms[0],
            issues: collList,
            configType: configType,
            showType: showType,
            originLanguage: originLanguage,
            recentDay: recentDay,
            topFAQ: topFAQ,
          };
        }
        return it;
      });
      // console.log('MessageHotIssue>>>newNds', newNds);
      return newNds;
    });
    // // 即时响应
    dispatch({
      type: 'aiagent/setCurrentVariables',
      payload:
        configType +
        showType +
        originLanguage +
        collList.length +
        recentDay +
        topFAQ,
    }); // tab值存入全局
    // console.log('askQuestionFrom>>>setNodes', collList);
  }, [
    collList,
    setNodes,
    configType,
    showType,
    originLanguage,
    setCollList,
    recentDay,
    topFAQ,
  ]);
  //请求接口
  useEffect(() => {
    queryCurrentVar();
  }, []);
  // 选择属性格式校验

  //查询变量
  const queryCurrentVar = () => {
    setLoadingVar(true);
    try {
      let aiAgentId = localStorage.getItem('aiAgentId');
      dispatch({
        // type: 'aiagent/queryGroupVariables',
        type: 'aiagent/queryCurrentVar',
        payload: {
          id: aiAgentId, //智能体ID
        },
        callback: response => {
          setLoadingVar(false);
          let { code, data, msg } = response;
          if (200 === code) {
            let arr3 = [];
            let arr2 = [];
            let arr4 = [];
            let arr1 = [];
            //变量类型（1.系统内置变量，2当前会话变量，3当前智能体变量，4全局变量）
            data?.forEach(item => {
              if (item.variableType === 3) {
                arr3.push(item);
              } else if (item.variableType === 2) {
                arr2.push(item);
              } else if (item.variableType === 4) {
                arr4.push(item);
              } else if (item.variableType === 1) {
                arr1.push(item);
              }
            });
            setItems([
              {
                key: '3',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.1',
                  defaultValue: '当前智能体变量',
                }),
                options: arr3.map(item => {
                  return {
                    value: 'var_agent.' + item.variableName,
                    label: item.variableName,
                  };
                }),
              },
              {
                key: '2',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.2',
                  defaultValue: '当前会话变量',
                }),
                options: arr2.map(item => {
                  return {
                    value: 'var_session.' + item.variableName,
                    label: item.variableName,
                  };
                }),
              },
              {
                key: '4',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.3',
                  defaultValue: '全局变量',
                }),
                options: arr4.map(item => {
                  return {
                    value: 'var_global.' + item.variableName,
                    label: item.variableName,
                  };
                }),
              },
              {
                key: '1',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.4',
                  defaultValue: '系统内置变量',
                }),
                options: arr1.map(item => {
                  return {
                    value: 'var_system.' + item.variableName,
                    label: item.variableName,
                  };
                }),
              },
            ]);
          } else {
            notification.error({
              message: msg,
            });
          }
        },
      });
    } catch {
      setLoadingVar(false);
    }
  };
  const openColl = index => {
    setCollList(prevCollList => {
      let arr = [...prevCollList];
      let itemIndex = arr.findIndex(item => item.language === activeKey);
      arr[itemIndex].issueClassifys[index].isOpen = !arr[itemIndex]
        .issueClassifys[index].isOpen;
      console.log('arr', arr[index]);
      return arr;
    });
  };
  const delColl = index => {
    setCollList(prevCollList => {
      let arr = [...prevCollList];
      let itemIndex = arr.findIndex(item => item.language === activeKey);
      arr[itemIndex].issueClassifys.splice(index, 1);
      return arr;
    });
  };
  const delQuestion = (index1, index2) => {
    setCollList(prevCollList => {
      let arr = [...prevCollList];
      let itemIndex = arr.findIndex(item => item.language === activeKey);
      arr[itemIndex].issueClassifys[index1].issueContents.splice(index2, 1);
      return arr;
    });
  };
  const onEdit = (targetKey, action) => {
    console.log('targetKey', targetKey);
    console.log('action', action);
    if (action === 'add') {
      // add();
    } else {
      setCollList(prev => {
        let index = prev.findIndex(item => item.language === targetKey);
        prev.splice(index, 1);
        return prev;
      });
    }
    setTransKey(transKey + 1);
  };
  return (
    <Spin spinning={loadingVar}>
      <div className={styles.messageTextBox}>
        <span className={styles.messageTextBox_title}>
          <FormattedMessage
            id="ai.agent.nodes.MessageHotIssue.title1"
            defaultValue="配置方式"
          ></FormattedMessage>
        </span>
        <Radio.Group
          defaultValue={configType}
          onChange={e => {
            setConfigType(e.target.value);
          }}
        >
          <Radio value={'1'}>
            <FormattedMessage
              id="ai.agent.nodes.MessageHotIssue.title1.type1"
              defaultValue="手动编辑"
            ></FormattedMessage>
          </Radio>
          <Radio value={'2'}>
            <FormattedMessage
              id="ai.agent.nodes.MessageHotIssue.title1.type2"
              defaultValue="自动推荐"
            ></FormattedMessage>
          </Radio>
        </Radio.Group>

        {configType === '1' && (
          <>
            <span className={styles.messageTextBox_title}>
              <FormattedMessage
                id="ai.agent.nodes.MessageHotIssue.title2"
                defaultValue="展示形式"
              ></FormattedMessage>
            </span>
            <Radio.Group
              defaultValue={showType}
              onChange={e => {
                setShowType(e.target.value);
              }}
            >
              <Radio value={'1'}>
                <FormattedMessage
                  id="ai.agent.nodes.MessageHotIssue.title2.type1"
                  defaultValue="横版"
                ></FormattedMessage>
              </Radio>
              <Radio value={'2'}>
                <FormattedMessage
                  id="ai.agent.nodes.MessageHotIssue.title2.type2"
                  defaultValue="竖版"
                ></FormattedMessage>
              </Radio>
            </Radio.Group>
            <span className={styles.messageTextBox_title}>
              <FormattedMessage
                id="ai.agent.nodes.MessageHotIssue.title3"
                defaultValue="选择初始语言"
              ></FormattedMessage>
            </span>
            <Select
              options={JSON.parse(localStorage.getItem('languageLocal'))}
              defaultValue={originLanguage}
              showSearch
              filterOption={(input, option) => {
                return (
                  option?.label?.toLowerCase().indexOf(input.toLowerCase()) >= 0
                );
              }}
              onChange={value => {
                console.log('value', value);
                let item = JSON.parse(
                  localStorage.getItem('languageLocal'),
                ).find(item => item.value === value);
                setOriginLanguage(value);
                setActiveKey(value);
                if (collList.length === 0) {
                  setCollList(prev => {
                    prev.push({
                      language: value,
                      languageText: item.label,
                      issueClassifys: [
                        {
                          issueName: '',
                          issueContents: [''],
                          isOpen: true,
                        },
                      ],
                    });
                    return prev;
                  });
                } else {
                  setCollList(prev => {
                    // console.log('prev', prev);
                    // console.log('value', value);
                    let index = prev.findIndex(item => item.language === value);
                    // console.log('index', index);
                    let arr = [...prev];
                    if (index !== -1) {
                      // 把找到的index放到arr的第一个
                      let obj = { ...arr[index] };
                      arr.splice(index, 1);
                      arr.unshift(obj);
                    } else {
                      arr[0].language = value;
                      arr[0].languageText = item.label;
                    }
                    setTransKey(transKey + 1);
                    return arr;
                  });
                }
              }}
            />
            {collList.length > 0 && (
              <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                <Tabs
                  key={transKey}
                  tabBarGutter={10}
                  style={{ width: '75%' }}
                  type="editable-card"
                  hideAdd
                  size={'small'}
                  activeKey={activeKey}
                  onChange={v => {
                    setActiveKey(v);
                    setKeyRange(prev => prev + 1);
                  }}
                  onEdit={onEdit}
                  items={collList.map((item, index) => {
                    return {
                      label: item.languageText,
                      key: item.language,
                      closable: index === 0 ? false : true,
                    };
                  })}
                />
                <div
                  className={styles.translateButton}
                  onClick={() => setIsModalOpen(true)}
                >
                  <TransIcon />
                  <FormattedMessage
                    id="ai.agent.nodes.MessageHotIssue.title10"
                    defaultValue="智能翻译"
                  ></FormattedMessage>
                </div>
              </div>
            )}
            <div key={keyRange}>
              {collList
                ?.find(item => item.language === activeKey)
                ?.issueClassifys?.map((itemSon, itemSonIndex) => {
                  return (
                    <div
                      key={itemSonIndex}
                      className={styles.messageTextBoxForm}
                      style={{ marginTop: '-5px' }}
                    >
                      <div className={styles.messageTextBox_title}>
                        <span>
                          {itemSon.issueName || ''}
                          {itemSonIndex == 0 ? (
                            ''
                          ) : (
                            <DelIcon
                              onClick={() => delColl(itemSonIndex)}
                              style={{ marginLeft: '5px' }}
                            ></DelIcon>
                          )}
                        </span>
                        <ArrowIcon
                          style={{
                            transform: itemSon.isOpen ? '' : 'rotate(180deg)',
                          }}
                          onClick={() => {
                            openColl(itemSonIndex);
                          }}
                          className={styles.messageTextBox_title_switchIcon}
                        />
                      </div>
                      {itemSon.isOpen ? (
                        <Form
                          name="basic"
                          labelCol={{
                            span: 24,
                          }}
                          wrapperCol={{
                            span: 24,
                          }}
                          style={{
                            width: '100%',
                          }}
                          initialValues={{
                            remember: true,
                          }}
                          autoComplete="off"
                          layout="vertical"
                          className={styles.formTitle}
                        >
                          <Form.Item
                            label={
                              <div>
                                {getIntl().formatMessage({
                                  id: 'ai.agent.nodes.MessageHotIssue.title4',
                                  defaultValue: '问题分类名称',
                                })}
                                <span
                                  style={{ color: '#ff4d4f', marginLeft: 4 }}
                                >
                                  *
                                </span>
                              </div>
                            }
                            name="issueName"
                            rules={[
                              {
                                required: false,
                                message: getIntl().formatMessage({
                                  id:
                                    'ai.agent.nodes.MessageHotIssue.title4.tip',
                                  defaultValue: '请输入问题分类名称',
                                }),
                              },
                            ]}
                          >
                            <Input
                              defaultValue={itemSon.issueName}
                              onChange={e => {
                                const value = e.target.value;
                                // // 使用正则表达式过滤，只保留字母
                                // e.target.value = value.replace(/[^a-zA-Z]/g, '');
                                changeColl(
                                  e.target.value,
                                  itemSonIndex,
                                  'issueName',
                                );
                              }}
                            ></Input>
                          </Form.Item>
                          {itemSon?.issueContents?.length > 0 &&
                            itemSon.issueContents?.map((ele, eleIndex) => {
                              return (
                                <Form.Item
                                  label={
                                    <div>
                                      {getIntl().formatMessage({
                                        id:
                                          'ai.agent.nodes.MessageHotIssue.title5',
                                        defaultValue: '问题',
                                      })}
                                      <span
                                        style={{
                                          color: '#ff4d4f',
                                          marginLeft: 4,
                                        }}
                                      >
                                        *
                                      </span>
                                      {eleIndex > 0 && (
                                        <DelIcon
                                          style={{ cursor: 'pointer' }}
                                          onClick={() => {
                                            delQuestion(itemSonIndex, eleIndex);
                                          }}
                                        ></DelIcon>
                                      )}
                                    </div>
                                  }
                                  rules={[
                                    {
                                      required: true,
                                      message: getIntl().formatMessage({
                                        id:
                                          'ai.agent.nodes.MessageHotIssue.title5.tip',
                                        defaultValue: '请输入问题',
                                      }),
                                    },
                                  ]}
                                >
                                  <Input
                                    defaultValue={ele}
                                    onChange={e => {
                                      const value = e.target.value;
                                      // // 使用正则表达式过滤，只保留字母
                                      // e.target.value = value.replace(/[^a-zA-Z]/g, '');
                                      let arr = [...collList];
                                      let itemIndex = arr.findIndex(
                                        item => item.language === activeKey,
                                      );
                                      arr[itemIndex].issueClassifys[
                                        itemSonIndex
                                      ].issueContents[eleIndex] = value;
                                      setCollList(arr);
                                    }}
                                  ></Input>
                                </Form.Item>
                              );
                            })}
                          <div
                            className={styles.addButton}
                            onClick={() => {
                              setCollList(prev => {
                                let arr = [...prev];
                                let itemIndex = arr.findIndex(
                                  item => item.language === activeKey,
                                );
                                arr[itemIndex].issueClassifys[
                                  itemSonIndex
                                ].issueContents.push('');
                                return arr;
                              });
                            }}
                          >
                            +
                            <FormattedMessage
                              id="ai.agent.nodes.MessageHotIssue.title6"
                              defaultValue="添加问题"
                            ></FormattedMessage>
                          </div>
                        </Form>
                      ) : (
                        ''
                      )}
                    </div>
                  );
                })}
            </div>
            {collList.length > 0 && (
              <div
                className={styles.addButton}
                onClick={() => {
                  setCollList(prev => {
                    prev[0].issueClassifys.push({
                      issueName: '',
                      issueContents: [''],
                      isOpen: true,
                    });
                    return prev;
                  });
                  setKeyRange(prev => prev + 1);
                }}
              >
                +
                <FormattedMessage
                  id="ai.agent.nodes.MessageHotIssue.title7"
                  defaultValue="添加问题分类"
                ></FormattedMessage>
              </div>
            )}
          </>
        )}
        {configType === '2' && (
          <>
            <span className={styles.messageTextBox_title}>
              <FormattedMessage
                id="ai.agent.nodes.MessageHotIssue.title12"
                defaultValue="自动推荐FAQ规则"
              ></FormattedMessage>
            </span>
            <div>
              <FormattedMessage
                id="ai.agent.nodes.MessageHotIssue.title12.tip1"
                defaultValue="展示形式"
              ></FormattedMessage>
              <Select
                style={{ width: 60, margin: '0 5px' }}
                options={Array.from({ length: 365 }, (_, i) => ({
                  label: `${i + 1}`,
                  value: i + 1,
                }))}
                defaultValue={recentDay}
                onChange={value => {
                  setRecentDay(value);
                }}
              />
              <FormattedMessage
                id="ai.agent.nodes.MessageHotIssue.title12.tip2"
                defaultValue="天提问最多的Top"
              ></FormattedMessage>
              <Select
                style={{ width: 60, margin: '0 5px' }}
                options={Array.from({ length: 10 }, (_, i) => ({
                  label: `${i + 1}`,
                  value: i + 1,
                }))}
                defaultValue={topFAQ}
                onChange={value => {
                  setTopFAQ(value);
                }}
              />
              <FormattedMessage
                id="ai.agent.nodes.MessageHotIssue.title12.tip3"
                defaultValue="的FAQ"
              ></FormattedMessage>
              <div style={{ marginTop: 10, color: '#999999' }}>
                <FormattedMessage
                  id="ai.agent.nodes.MessageHotIssue.title12.remark"
                  defaultValue="备注：如果一个FAQ有多种不同的问法，仅展示第一个"
                ></FormattedMessage>
              </div>
            </div>
          </>
        )}
      </div>
      <Modal
        className="createTageClassification"
        confirmLoading={modalLoading}
        keyboard={false}
        open={isModalOpen}
        onCancel={() => {
          if (modalLoading) return;
          setIsModalOpen(false);
          setTransList([]);
        }}
        onOk={() => {
          setModalLoading(true);
          getHotTranslationList({
            language: activeKey,
            issueClassifys: collList[0].issueClassifys,
            targetLanguages: transList,
          }).then(res => {
            if (res.code === 200) {
              // console.log(res);
              let arr = [...collList];
              let data = [...res.data];
              data.forEach(e => {
                let index = JSON.parse(
                  localStorage.getItem('languageLocal'),
                ).findIndex(item => item.value === e.language);
                let lanT = JSON.parse(localStorage.getItem('languageLocal'))[
                  index
                ].label;
                let index2 = arr.findIndex(item => item.languageText === lanT);
                let obj = {
                  ...e,
                  languageText: lanT,
                  isOpen: true,
                };
                if (index2 === -1) {
                  arr.push(obj);
                } else {
                  arr[index2] = obj;
                }
              });
              setCollList(arr);
            }
            setIsModalOpen(false);
            setTransList([]);
            setModalLoading(false);
          });
        }}
      >
        <div
          className={styles.transModalTitle}
          style={{ fontSize: 16, fontWeight: 700 }}
        >
          <FormattedMessage
            id="ai.agent.nodes.MessageHotIssue.title10"
            defaultValue="智能翻译"
          ></FormattedMessage>
        </div>
        <CloseOutlined
          style={{ position: 'absolute', right: 20, top: 20 }}
          className={styles.transModalClose}
          onClick={() => setIsModalOpen(false)}
        />
        <div className={styles.transModalContent}>
          <div
            className={styles.transModalContent_title}
            style={{ margin: '10px 0' }}
          >
            <FormattedMessage
              id="ai.agent.nodes.MessageHotIssue.title11"
              defaultValue="目标语言"
            ></FormattedMessage>
          </div>
          <Select
            style={{ width: '100%' }}
            mode="multiple"
            // 支持搜索名字
            filterOption={(input, option) =>
              option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
            maxTagCount={3}
            value={transList}
            onChange={value => {
              setTransList(value);
            }}
            options={JSON.parse(localStorage.getItem('languageLocal'))}
          />
        </div>
      </Modal>
    </Spin>
  );
};

export default MessageHotIssue;
