.messageTextBox {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 320px;
  .emojiDetailContainer {
    position: absolute;
    z-index: 2;
    left: 0;
    top: 20px;
    height: 33vh;
    overflow: hidden;
    overflow-y: scroll;
  }

  .constDetailContainer {
    position: absolute;
    z-index: 2;
    left: 0;
    top: 20px;
  }

  .messageTextBox_title {
    color: #333;
    font-size: 12px;
    font-style: normal;
    font-weight: 700;
    line-height: 150%;
    /* 21px */
  }
  .askQuestionText_line {
    // margin-top: 25px;
    width: 100%;
    min-height: 1px;
    background: rgba(137, 116, 255, 0.5);
  }
  .formTitle {
    color: red;
  }
  .moreVar {
    position: absolute;
    right: 0;
    font-size: 12px;
    color: #3463fc;
    cursor: pointer;
  }
  .cardData {
    position: relative;
    > div {
      margin-bottom: 10px;
    }
    .cardData_item {
      display: flex;
      align-items: center;
    }
    .cardData_items {
      position: relative;
      margin-left: 40px;
      > div {
        margin-top: 5px;
      }
    }
    .cardData_items::before {
      position: absolute;
      content: '';
      display: block;
      width: 33px;
      height: 1px;
      left: -33px;
      top: 40px;
      background: rgba(137, 116, 255, 0.5);
    }
    .cardDataLine {
      width: 1px;
      height: 410px;
      position: absolute;
      left: 7px;
      top: 22px;
      background: rgba(137, 116, 255, 0.5);
    }
  }
  :global {
    .ant-picker {
      border-radius: 6px;
    }
    .ant-form-item {
      margin-bottom: 10px;
      position: relative;
    }
    .ant-col-1 {
      padding-left: 0px !important;
    }
    .ant-input {
      font-size: 12px;
      height: 32px;
      box-shadow: none;
      border-radius: 6px;
      background: #fff;
    }
    .ant-select {
      font-size: 12px;
    }
    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      height: 32px;
      box-shadow: none;
      border-radius: 6px;
      background: #fff;
    }
    textarea.ant-input {
      font-size: 12px;
    }
    .ant-input-number {
      width: 100%;
      font-size: 12px;
      height: 32px;
      box-shadow: none;
      border-radius: 6px;
      background: #fff;
    }
    .ant-input-number-handler-wrap {
      border-radius: 0 6px 6px 0;
    }
    .ant-input-number-handler-down {
      border-bottom-right-radius: 6px;
    }
    .ant-input-number-handler-up {
      border-top-right-radius: 6px;
    }
    .ant-form-item-label > label {
      color: #333;
      font-size: 12px;
      font-style: normal;
      // font-weight: 700;
      line-height: 150%; /* 18px */
    }
    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      display: none;
    }
    .ant-form-item:nth-child(1)
      > .ant-form-item-label
      > label.ant-form-item-required {
      font-weight: bold;
    }
    .ant-form-item-label > label.ant-form-item-required {
      font-weight: 100;
    }
    .ant-form-item:nth-child(1)
      .ant-form-item-label
      label.ant-form-item-required {
      font-weight: bold !important;
    }
    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::after {
      display: inline-block;
      margin-left: 2px;
      color: #ff4d4f;
      font-size: 14px;
      font-family: SimSun, sans-serif;
      line-height: 1;
      content: '*';
      font-weight: bold;
    }

    .ant-radio-checked .ant-radio-inner {
      border-color: #3463fc;
    }
    .ant-radio-inner::after {
      background-color: #3463fc;
    }
    .ant-radio-wrapper {
      font-size: 12px !important;
    }
    .ant-select-multiple .ant-select-selection-item {
      border-radius: 4px;
      border: 1px solid rgba(52, 99, 252, 0.5);
      background: linear-gradient(
          0deg,
          rgba(52, 99, 252, 0.1) 0%,
          rgba(52, 99, 252, 0.1) 100%
        ),
        #fff;
      .anticon {
        color: #3463fc;
      }
    }
    .ant-select-multiple .ant-select-selection-item-content {
      color: #3463fc;
      font-size: 12px;
    }
  }
  :global {
    .ant-input {
      height: 120;
      border-radius: 6px;
      border: 1px solid #e6e6e6;
      background: #fff;
    }

    .ant-input:focus,
    .ant-input-focused {
      box-shadow: none;
    }

    .ant-input:hover {
      box-shadow: none;
    }
  }
}
