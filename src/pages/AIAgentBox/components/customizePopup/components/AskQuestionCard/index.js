import styles from './style.less';
import { Spin, Select, Radio, Tooltip } from 'antd';
import { useReactFlow } from 'react-flow-renderer';
import { FormattedMessage, getIntl, useDispatch } from 'umi';
import { useEffect, useState } from 'react';
import HighLightTextarea from '../highLightTextarea';
import HighlightInput from '../highLightInput';
import { InfoCircleOutlined } from '@ant-design/icons';
import { notification } from '@/utils/utils';
const AskQuestionCard = ({ node, setIsModalOpenVariables }) => {
  const { id, data } = node;
  const { customizForms } = data || {};
  const [loading, setLoading] = useState(false);
  //  通用start
  const [botMessage, setBotMessage] = useState(
    customizForms[0].botMessage || '',
  ); //机器人话术
  const [cardLayout, setCardLayout] = useState(
    customizForms[0].cardLayout || '1',
  );
  const [cardListAttr, setCardListAttr] = useState(
    customizForms[0].cardListAttr || '',
  );
  const [cardID, setCardID] = useState(customizForms[0].cardID || '');
  const [cardImageUrl, setCardImageUrl] = useState(
    customizForms[0].cardImageUrl || '',
  );
  const [cardTitle, setCardTitle] = useState(customizForms[0].cardTitle || '');
  const [cardPrice, setCardPrice] = useState(customizForms[0].cardPrice || '');
  const [cardNumber, setCardNumber] = useState(
    customizForms[0].cardNumber || '',
  );
  const [cardStatus, setCardStatus] = useState(
    customizForms[0].cardStatus || '',
  );
  const [saveSelectedToAttr, setSaveSelectedToAttr] = useState(
    customizForms[0].saveSelectedToAttr || '',
  );
  // 展示表情包
  const [loadingVar, setLoadingVar] = useState(false);
  const [items, setItems] = useState([]);
  const { setNodes } = useReactFlow();
  const dispatch = useDispatch();
  const ListCircle = () => {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
      >
        <circle cx="8" cy="8" r="4.5" fill="white" stroke="#8974FF" />
        <circle cx="8" cy="8" r="2.5" fill="white" stroke="#8974FF" />
      </svg>
    );
  };
  //编辑组件内容ƒ
  useEffect(() => {
    console.log('AskQuestionCard>>>', id);
    setNodes(nds => {
      let newNds = nds?.map(item => {
        let it = item;
        if (item.id === id && item.data.customizForms) {
          it.data.customizForms[0] = {
            ...it.data.customizForms[0],
            botMessage: botMessage,
            cardLayout: cardLayout,
            cardID: cardID,
            cardListAttr: cardListAttr,
            cardImageUrl: cardImageUrl,
            cardTitle: cardTitle,
            cardPrice: cardPrice,
            cardNumber: cardNumber,
            cardStatus: cardStatus,
            saveSelectedToAttr: saveSelectedToAttr,
          };
        }
        console.log('AskQuestionCard>>>', it);
        return it;
      });
      return newNds;
    });
    dispatch({
      type: 'aiagent/setCurrentVariables',
      payload:
        botMessage +
        cardLayout +
        cardListAttr +
        cardImageUrl +
        cardTitle +
        cardPrice +
        cardNumber +
        cardStatus +
        cardID +
        saveSelectedToAttr,
    }); // tab值存入全局
  }, [
    botMessage,
    cardLayout,
    cardListAttr,
    cardImageUrl,
    cardTitle,
    cardPrice,
    cardNumber,
    cardStatus,
    cardID,
    saveSelectedToAttr,
  ]);
  //请求接口
  useEffect(() => {
    queryCurrentVar();
  }, []);
  //查询变量
  const queryCurrentVar = () => {
    setLoadingVar(true);
    try {
      let aiAgentId = localStorage.getItem('aiAgentId');
      dispatch({
        // type: 'aiagent/queryGroupVariables',
        type: 'aiagent/queryCurrentVar',
        payload: {
          id: aiAgentId, //智能体ID
        },
        callback: response => {
          setLoadingVar(false);
          let { code, data, msg } = response;
          if (200 === code) {
            let arr3 = [];
            let arr2 = [];
            let arr4 = [];
            let arr1 = [];
            //变量类型（1.系统内置变量，2当前会话变量，3当前智能体变量，4全局变量）
            data?.forEach(item => {
              if (item.variableType === 3) {
                arr3.push(item);
              } else if (item.variableType === 2) {
                arr2.push(item);
              } else if (item.variableType === 4) {
                arr4.push(item);
              } else if (item.variableType === 1) {
                arr1.push(item);
              }
            });
            setItems([
              {
                key: '3',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.1',
                  defaultValue: '当前智能体变量',
                }),
                options: arr3.map(item => {
                  return {
                    value: 'var_agent.' + item.variableName,
                    label: item.variableName,
                  };
                }),
              },
              {
                key: '2',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.2',
                  defaultValue: '当前会话变量',
                }),
                options: arr2.map(item => {
                  return {
                    value: 'var_session.' + item.variableName,
                    label: item.variableName,
                  };
                }),
              },
              {
                key: '4',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.3',
                  defaultValue: '全局变量',
                }),
                options: arr4.map(item => {
                  return {
                    value: 'var_global.' + item.variableName,
                    label: item.variableName,
                  };
                }),
              },
              {
                key: '1',
                type: 'group',
                label: getIntl().formatMessage({
                  id: 'ai.agent.var.tabs.4',
                  defaultValue: '系统内置变量',
                }),
                options: arr1.map(item => {
                  return {
                    value: 'var_system.' + item.variableName,
                    label: item.variableName,
                  };
                }),
              },
            ]);
          } else {
            notification.error({
              message: msg,
            });
          }
        },
      });
    } catch {
      setLoadingVar(false);
    }
  };

  return (
    <Spin spinning={loadingVar}>
      <div className={styles.messageTextBox}>
        <span className={styles.messageTextBox_title}>
          <FormattedMessage
            id="ai.agent.nodes.form.popup.messageText.title"
            defaultValue="机器人话术"
          ></FormattedMessage>
        </span>
        <HighLightTextarea
          setText={setBotMessage}
          text={botMessage}
          backgroundColor="#fff"
        ></HighLightTextarea>
        <div className={styles.askQuestionText_line}></div>
        <span className={styles.messageTextBox_title}>
          <FormattedMessage
            id="ai.agent.nodes.AskQuestionCard.title2"
            defaultValue="卡片布局"
          ></FormattedMessage>
        </span>
        <Radio.Group
          defaultValue={cardLayout}
          onChange={e => {
            setCardLayout(e.target.value);
          }}
        >
          <Radio value={'1'}>
            <FormattedMessage
              id="ai.agent.nodes.AskQuestionCard.title6"
              defaultValue="轮播"
            ></FormattedMessage>
          </Radio>
          <Radio value={'2'}>
            <FormattedMessage
              id="ai.agent.nodes.AskQuestionCard.title7"
              defaultValue="列表"
            ></FormattedMessage>
          </Radio>
        </Radio.Group>
        <span className={styles.messageTextBox_title}>
          <FormattedMessage
            id="ai.agent.nodes.AskQuestionCard.title8"
            defaultValue="卡片数据"
          ></FormattedMessage>
          <Tooltip
            placement="right"
            title={getIntl().formatMessage({
              id: 'ai.agent.nodes.AskQuestionCard.tip1',
              defaultValue: '',
            })}
          >
            <InfoCircleOutlined
              style={{ marginLeft: 5, color: '#3463FC', cursor: 'pointer' }}
            />
          </Tooltip>
        </span>
        <div className={styles.cardData}>
          <div className={styles.cardData_item}>
            <ListCircle />
            <HighlightInput
              setText={setCardListAttr}
              text={cardListAttr}
              backgroundColor="#fff"
              noEmoji={true}
              showEnd={'LIST'}
            ></HighlightInput>
          </div>
          <div className={styles.cardData_items}>
            <span>ID</span>
            <HighlightInput
              setText={setCardID}
              text={cardID}
              backgroundColor="#fff"
              noEmoji={true}
              noVar={true}
              showEnd={'STRING'}
            ></HighlightInput>
          </div>
          <div className={styles.cardData_items}>
            <FormattedMessage
              id="ai.agent.nodes.AskQuestionCard.title9"
              defaultValue="图片地址"
            ></FormattedMessage>
            <HighlightInput
              setText={setCardImageUrl}
              text={cardImageUrl}
              backgroundColor="#fff"
              noEmoji={true}
              noVar={true}
              showEnd={'STRING'}
            ></HighlightInput>
          </div>
          <div className={styles.cardData_items}>
            <FormattedMessage
              id="ai.agent.nodes.AskQuestionCard.title10"
              defaultValue="商品标题"
            ></FormattedMessage>
            <HighlightInput
              setText={setCardTitle}
              text={cardTitle}
              backgroundColor="#fff"
              noEmoji={true}
              showEnd={'STRING'}
              noVar={true}
            ></HighlightInput>
          </div>
          <div className={styles.cardData_items}>
            <FormattedMessage
              id="ai.agent.nodes.AskQuestionCard.title11"
              defaultValue="商品价格（非必填）"
            ></FormattedMessage>
            <HighlightInput
              setText={setCardPrice}
              text={cardPrice}
              backgroundColor="#fff"
              noEmoji={true}
              noVar={true}
              showEnd={'STRING'}
            ></HighlightInput>
          </div>
          <div className={styles.cardData_items}>
            <FormattedMessage
              id="ai.agent.nodes.AskQuestionCard.title12"
              defaultValue="商品数量（非必填）"
            ></FormattedMessage>
            <HighlightInput
              setText={setCardNumber}
              text={cardNumber}
              backgroundColor="#fff"
              noEmoji={true}
              noVar={true}
              showEnd={'STRING'}
            ></HighlightInput>
          </div>
          <div className={styles.cardData_items}>
            <FormattedMessage
              id="ai.agent.nodes.AskQuestionCard.title13"
              defaultValue="订单状态（非必填）"
            ></FormattedMessage>
            <HighlightInput
              setText={setCardStatus}
              text={cardStatus}
              backgroundColor="#fff"
              noEmoji={true}
              noVar={true}
              showEnd={'STRING'}
            ></HighlightInput>
          </div>

          <div className={styles.cardDataLine}></div>
        </div>
        <div className={styles.askQuestionText_line}></div>
        <div className={styles.messageTextBox_title}>
          <FormattedMessage
            id="ai.agent.nodes.AskQuestionText.SaveValue"
            defaultValue="存储到变量"
          ></FormattedMessage>
          <span
            className={styles.moreVar}
            onClick={() => {
              setIsModalOpenVariables(true);
            }}
          >
            <FormattedMessage
              id="ai.agent.var"
              defaultValue="变量管理"
            ></FormattedMessage>
          </span>
        </div>
        <Select
          style={{ width: '100%' }}
          showSearch
          allowClear
          defaultValue={saveSelectedToAttr}
          options={items}
          onFocus={() => {
            queryCurrentVar();
          }}
          onChange={value => {
            // console.log(value);
            setSaveSelectedToAttr(value || '');
          }}
        ></Select>
        <div style={{ color: '#999', fontSize: '12px' }}>
          <FormattedMessage
            id="ai.agent.nodes.AskQuestionCard.title14"
            defaultValue="备注：此处为一个JSON变量"
          ></FormattedMessage>
          <Tooltip
            placement="right"
            title={
              getIntl().formatMessage({
                id: 'ai.agent.nodes.AskQuestionCard.title15',
                defaultValue: '',
              }) +
              '{cardID:123445689,cardImageUrl:"https://example.com/products/product1.jpg",cardTitle:"高级无线耳机",cardPrice:"¥899.00",cardNumber:"1",cardStatus:"closed"}'
            }
          >
            <InfoCircleOutlined
              style={{ marginLeft: 5, color: '#3463FC', cursor: 'pointer' }}
            />
          </Tooltip>
        </div>
      </div>
    </Spin>
  );
};

export default AskQuestionCard;
