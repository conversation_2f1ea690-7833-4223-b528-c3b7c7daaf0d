import { useState, useRef, useEffect } from 'react';
import {
  FormattedMessage,
  history,
  getIntl,
  setLocale,
  Link,
  useDispatch,
  useIntl,
} from 'umi';
import LoginReturn from '../../assets/login-return.png';
import LangIcon from '../../assets/langIconWhite.svg';
import styles from './index.less';
import leftImg from '../../assets/newLogin.png';
import leftImgEn from '../../assets/newLogin2.png';
import {
  Dropdown,
  Input,
  Button,
  Form,
  Modal,
  Row,
  Col,
  Divider,
  notification,
  Checkbox,
} from 'antd';
import CookieConsent from '@/components/CookieConsent';
const RegisterNew = () => {
  const [langText, setLangText] = useState('中文');
  const [language, setLanguage] = useState('zh-CN');
  const [defaultSelectedKeys, setDefaultSelectedKeys] = useState('1');
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [btnLoading, setBtnLoading] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const formRef = useRef(null);
  const [awsIdentification, setAwsIdentification] = useState('');
  const intl = useIntl(); // 获取 intl 对象，用于获取链接文本
  // 获取翻译后的链接文本
  const termsText = intl.formatMessage({ id: 'register.footer.link' });
  const privacyText = intl.formatMessage({ id: 'register.footer.link2' });

  const [marketingConsent, setMarketingConsent] = useState(true);

  const items = [
    {
      key: '1',
      label: '中文',
    },
    {
      key: '2',
      label: 'English',
    },
    {
      key: '3',
      label: 'Deutsch',
    },
    {
      key: '4',
      label: '日本語',
    },
    {
      key: '5',
      label: 'Indonesia',
    },
  ];
  const dispatch = useDispatch();
  useEffect(() => {
    const urlParams = history.location.query?.awsIdentification;
    setAwsIdentification(urlParams ? urlParams : null);
    const lang = localStorage.getItem('lang');
    if (lang) {
      setLanguage(lang);
      setLocale(lang, false);
      if (lang === 'zh-CN') {
        setLangText('中文');
        setDefaultSelectedKeys('1');
      } else if (lang === 'en-US') {
        setLangText('English');
        setDefaultSelectedKeys('2');
      } else if (lang === 'de-DE') {
        setLangText('Deutsch');
        setDefaultSelectedKeys('3');
      } else if (lang === 'ja') {
        setLangText('日本語');
        setDefaultSelectedKeys('4');
      } else if (lang === 'id-ID') {
        setLangText('Indonesia');
        setDefaultSelectedKeys('5');
      }
    } else {
      setLanguage('zh-CN');
      setLangText('中文');
      setDefaultSelectedKeys('1');
    }
  }, []);
  const goHomePage = () => {
    history.push('/home');
  };
  const handleMenuClick = e => {
    setDefaultSelectedKeys(e.key);
    if (e.key === '1') {
      setLangText('中文');
      setLanguage('zh-CN');
      setLocale('zh-CN', false);
      localStorage.setItem('lang', 'zh-CN');
    } else if (e.key === '2') {
      setLangText('English');
      setLanguage('en-US');
      setLocale('en-US', false);
      localStorage.setItem('lang', 'en-US');
    } else if (e.key === '3') {
      setLangText('Deutsch');
      setLanguage('de-DE');
      setLocale('de-DE', false);
      localStorage.setItem('lang', 'de-DE');
    } else if (e.key === '4') {
      setLangText('日本語');
      setLanguage('ja');
      setLocale('ja', false);
      localStorage.setItem('lang', 'ja');
    } else if (e.key === '5') {
      setLangText('Indonesia');
      setLanguage('id-ID');
      setLocale('id-ID', false);
      localStorage.setItem('lang', 'id-ID');
    }
  };

  // 获取验证码
  const handleGetVerifyCode = () => {
    form.validateFields(['email']).then(values => {
      setLoading(true);
      // TODO: 调用获取验证码接口
      dispatch({
        type: 'login/captcha',
        payload: values.email,
        callback: response => {
          let { code, data, msg } = response;
          if (code === 200) {
            setLoading(false);
            notification.success({
              message: msg,
            });
            setCountdown(60);
            const timer = setInterval(() => {
              setCountdown(prev => {
                if (prev <= 1) {
                  clearInterval(timer);
                  return 0;
                }
                return prev - 1;
              });
            }, 1000);
          } else {
            setLoading(false);
            notification.error({
              message: msg,
            });
          }
        },
      });
    });
  };

  // 提交表单
  const onFinish = values => {
    setBtnLoading(true);
    console.log('Success:', values);
    let newData = {
      ...values,
      // postName: '',
      // companyAddress:'',
      awsIdentification: awsIdentification,
      marketingConsent: marketingConsent ? 1 : 2,
    };
    dispatch({
      type: 'login/register',
      payload: newData,
      callback: response => {
        let { code, data, msg } = response;
        if (code === 200) {
          Modal.success({
            content: getIntl().formatMessage({
              id: 'register.success.modal',
            }),
            onOk: () => {
              setBtnLoading(false);
              history.replace('/login');
            },
          });
        } else {
          setBtnLoading(false);
          notification.error({
            message: msg,
          });
        }
      },
    });
  };

  const handleMarketingConsentChange = e => {
    setMarketingConsent(e.target.checked);
  };

  return (
    <>
      <CookieConsent />

      <div className={styles.loginContent}>
        <div className={styles.goHome} onClick={goHomePage}>
          <img src={LoginReturn} />
          <span>
            <FormattedMessage id="home.page.menu.home" defaultMessage="首页" />
          </span>
        </div>
        <div className={styles.languageIcon}>
          <Dropdown
            menu={{
              items,
              selectable: true,
              defaultSelectedKeys: [defaultSelectedKeys],
              onClick: handleMenuClick,
            }}
            overlayClassName="langDownPersonal"
          >
            <div className={styles.langContent}>
              <img src={LangIcon} />
              <span>{langText}</span>
            </div>
          </Dropdown>
        </div>

        <div className={styles.loginDetailContent}>
          <div className={styles.leftLoginContent}>
            <img src={language === 'zh-CN' ? leftImg : leftImgEn}></img>
          </div>
          <div className={styles.rightLoginContent}>
            <div className={styles.formContent}>
              <div className={styles.formContentTitle}>
                <FormattedMessage id="register.title" />
                <span>ConnectNow</span>
              </div>

              <Form
                form={form}
                name="basic"
                layout="vertical"
                onFinish={onFinish}
                labelCol={{
                  span: 0,
                }}
                wrapperCol={{
                  span: 20,
                }}
                style={{
                  maxWidth: 600,
                  marginTop: 20,
                }}
                initialValues={{
                  remember: false,
                }}
                ref={formRef}
              >
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      name="userName"
                      label={getIntl().formatMessage({
                        id: 'register.user.name',
                      })}
                      rules={[
                        {
                          required: true,
                          message: (
                            <FormattedMessage id="register.user.name.input" />
                          ),
                        },
                      ]}
                    >
                      <Input
                        placeholder={getIntl().formatMessage({
                          id: 'register.user.name.input',
                        })}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="lastName"
                      label={getIntl().formatMessage({
                        id: 'register.last.name',
                      })}
                      rules={[
                        {
                          required: true,
                          message: (
                            <FormattedMessage id="register.last.name.input" />
                          ),
                        },
                      ]}
                    >
                      <Input
                        placeholder={getIntl().formatMessage({
                          id: 'register.last.name.input',
                        })}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item
                  name="email"
                  label={getIntl().formatMessage({
                    id: 'register.email',
                  })}
                  rules={[
                    {
                      required: true,
                      message: <FormattedMessage id="login.user.email.input" />,
                    },
                    {
                      min: 1,
                      max: 40,
                      type: 'email',
                      message: (
                        <FormattedMessage id="login.user.email.pattern" />
                      ),
                    },
                  ]}
                >
                  <Input
                    placeholder={getIntl().formatMessage({
                      id: 'login.user.email.input',
                    })}
                  />
                </Form.Item>
                <Form.Item className={styles.emailTips}>
                  <p>
                    <FormattedMessage id="register.email.notice" />
                  </p>
                </Form.Item>
                <Row gutter={16}>
                  <Col span={17}>
                    <Form.Item
                      name="captcha"
                      label={getIntl().formatMessage({
                        id: 'register.captcha',
                      })}
                      rules={[
                        {
                          required: true,
                          message: (
                            <FormattedMessage id="login.user.captcha.input" />
                          ),
                        },
                      ]}
                    >
                      <Input
                        placeholder={getIntl().formatMessage({
                          id: 'login.user.captcha.input',
                        })}
                      />
                    </Form.Item>
                  </Col>
                  <Col
                    span={7}
                    style={{ display: 'flex', alignItems: 'flex-end' }}
                  >
                    <Button
                      type="primary"
                      onClick={handleGetVerifyCode}
                      disabled={countdown > 0}
                      loading={loading}
                      block
                      style={{
                        fontSize: '14px',
                        marginBottom: '12px',
                        height: '40px',
                      }}
                    >
                      {countdown > 0
                        ? `${countdown}s`
                        : getIntl().formatMessage({
                            id: 'register.email.captcha',
                          })}
                    </Button>
                  </Col>
                </Row>

                <Form.Item
                  name="companyName"
                  label={getIntl().formatMessage({
                    id: 'register.company.name',
                  })}
                  rules={[
                    {
                      required: true,
                      message: (
                        <FormattedMessage id="register.company.name.input" />
                      ),
                    },
                    {
                      max: 80,
                      message: (
                        <FormattedMessage id="register.company.name.length.input" />
                      ),
                    },
                  ]}
                >
                  <Input
                    placeholder={getIntl().formatMessage({
                      id: 'register.company.name.input',
                    })}
                  />
                </Form.Item>

                <Form.Item
                  name="phonenumber"
                  label={getIntl().formatMessage({
                    id: 'register.phone',
                  })}
                  rules={[
                    {
                      required: true,
                      message: (
                        <FormattedMessage id="register.user.phone.input" />
                      ),
                    },
                    {
                      max: 30,
                      pattern: '^(\\+\\d{1,3})?[1-9]\\d{9,14}$',
                      message: (
                        <FormattedMessage id="register.user.phone.length.input" />
                      ),
                    },
                  ]}
                >
                  <Input
                    placeholder={getIntl().formatMessage({
                      id: 'register.user.phone.input',
                    })}
                  />
                </Form.Item>

                <Form.Item>
                  <Button
                    className={styles.normalLogin}
                    type="primary"
                    htmlType="submit"
                    loading={btnLoading}
                  >
                    <FormattedMessage id="register.button" />
                  </Button>
                </Form.Item>
              </Form>
              <div className={styles.footer}>
                <Checkbox
                  defaultChecked={marketingConsent}
                  onChange={handleMarketingConsentChange}
                >
                  <div className={styles.footerText}>
                    <FormattedMessage id="register.footer.text.marketing" />
                  </div>
                </Checkbox>
                <Checkbox defaultChecked={true} disabled>
                  <div className={styles.footerText}>
                    <FormattedMessage
                      id="register.footer.text"
                      values={{
                        link: (
                          <a
                            href={`${window.location.origin}/#/userTerms`}
                            rel="noreferrer"
                            target="_blank"
                          >
                            {termsText}
                          </a>
                        ),
                        link2: (
                          <a
                            href={`${window.location.origin}/#/privacyPolicy`}
                            rel="noreferrer"
                            target="_blank"
                          >
                            {privacyText}
                          </a>
                        ),
                      }}
                    />
                  </div>
                </Checkbox>
                <Divider>or</Divider>
                <div className={styles.loginNow}>
                  <FormattedMessage id="register.login.now" />
                  <Link to="/login">
                    <FormattedMessage id="register.login.now.link" />
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default RegisterNew;
