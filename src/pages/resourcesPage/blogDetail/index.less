.blogDetailNav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6.25rem 12.8125rem 0.625rem 12.8125rem;
  margin-bottom: 2.5rem;
  border-bottom: 1px solid #e5e7eb;

  .blogDetailNavBack {
    cursor: pointer;
    color: #999;
    font-family: 'Microsoft YaHei UI';
    font-size: 1rem;
    font-style: normal;
    font-weight: 400;
  }

  .blogDetailNavPath {
    color: #999;
    font-family: 'Microsoft YaHei UI';
    font-size: 1rem;
    font-style: normal;
    font-weight: 400;

    .blogDetailNavPathTitle {
      color: #333;
    }
  }
}

.blogDetailContainer {
  padding: 0 12.8125rem 1.25rem 12.8125rem;
  width: 100%;
  display: flex;
  gap: 30px;

  .blogDetailLeft {
    flex: 1;

    .blogDetailTitle {
      color: #333;
      font-family: 'Microsoft YaHei';
      font-size: 2rem;
      font-style: normal;
      font-weight: 400;
      line-height: 150%;
      /* 48px */
    }

    .blogDetailInfo {
      display: flex;
      justify-content: space-between;
      padding: 1.5rem 1.25rem;
      border-top: 1px solid #e5e7eb;
      border-bottom: 1px solid #e5e7eb;
      margin: 1.875rem 0;
      align-items: center;

      .author {
        display: flex;
        align-items: center;
        gap: 1.875rem;

        .authorAvatar {
          width: 7rem;
          // border-radius: 25%;
        }

        .authorInfo {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          text-align: left;

          .name {
            color: #000;
            font-family: 'Microsoft YaHei';
            font-size: 1.3333rem;
            font-style: normal;
            font-weight: 400;
          }

          .careers {
            color: #6b7280;
            font-family: Inter;
            font-size: 1.1667rem;
            font-style: normal;
            font-weight: 400;
          }
        }
      }

      .createTime {
        color: #6b7280;
        font-family: Inter;
        font-size: 1rem;
        font-style: normal;
        font-weight: 400;
        display: flex;
        align-items: center;
      }
    }

    .blogDetailContent {
      color: #333;
      font-family: 'Microsoft YaHei';
      font-size: 1.25rem;
      font-style: normal;
      font-weight: 400;
      line-height: 200%;
      /* 30px */
      img {
        max-width: 75%;
        height: auto;
        display: block;
        margin: 1.875rem auto;
      }

      /* 表格样式 */
      table {
        width: 100%;
        border-collapse: collapse;
        margin: 1.25rem 0;
        overflow-x: auto;
        box-shadow: 0 0 0.625rem rgba(0, 0, 0, 0.05);
        border-radius: 0.3125rem;
      }

      /* 表头样式 */
      thead {
        background-color: #f5f7fa;
      }

      th {
        border: 1px solid #e5e7eb;
        padding: 0.75rem;
        font-weight: 600;
        color: #333;
        text-align: left;
      }

      /* 表格行和单元格样式 */
      td {
        border: 1px solid #e5e7eb;
        padding: 0.75rem;
        vertical-align: top;
      }

      tr:nth-child(even) {
        background-color: #f9fafb;
      }

      tr:hover {
        background-color: #f0f4f8;
      }

      /* 响应式表格 */
      @media (max-width: 768px) {
        table {
          display: block;
          overflow-x: auto;
          white-space: nowrap;
        }
      }
    }
  }

  .blogDetailRight {
    width: 25rem;

    .blogDetailCatalog {
      padding: 1.3125rem;
      background-color: #f9f9f9;
      border-radius: 0.625rem;
      .blogDetailCatalogTitle {
        color: #000;
        font-family: 'Microsoft YaHei';
        font-size: 1.25rem;
        font-style: normal;
        font-weight: bold;
        margin-bottom: 1.25rem;
      }

      .blogDetailCatalogList {
        display: flex;
        flex-direction: column;
        gap: 0.625rem;
        color: #4b5563;
        font-family: 'Microsoft YaHei';
        font-size: 1.25rem;
        font-style: normal;
        font-weight: 400;
        .blogDetailCatalogItem {
          cursor: pointer;
          &:hover {
            color: #3463fc;
          }
        }
      }
    }

    .blogContentHot {
      display: flex;
      flex-direction: column;
      gap: 1.25rem;
      margin-top: 1.25rem;

      .blogContentHotTitle {
        text-align: left;
        font-size: 1.25rem;
        color: #333333;
        padding-bottom: 0.625rem;
        border-bottom: 1px solid #e6e6e6;
        font-weight: bold;
      }

      .blogContentHotItem {
        display: flex;
        padding: 1.25rem;
        background: #ffffff;
        border-radius: 0.625rem;
        gap: 1.25rem;
        box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.15);
        justify-content: space-between;
        cursor: pointer;
        img {
          width: 8.75rem;
          height: 8.75rem;
          border-radius: 0.625rem;
          object-fit: cover;
        }
        .blogContentHotItemContent {
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          text-align: left;
          .blogContentHotItemContentTitle {
            font-size: 1.25rem;
            color: #333333;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .blogContentHotItemContentTime {
            font-size: 1rem;
            color: #999999;
          }
        }
      }
    }
  }
}
