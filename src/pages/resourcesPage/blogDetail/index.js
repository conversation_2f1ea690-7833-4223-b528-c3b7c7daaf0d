import React, { useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';
import { useDispatch, useLocation, useHistory, FormattedMessage } from 'umi';
import ChatWidget from '@/components/chatWidget';
import { getInstanceId } from '@/utils/utils';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Skeleton } from 'antd';
import { DoubleLeftOutlined } from '@ant-design/icons';
import styles from './index.less';
import ClockIcon from '@/assets/clock-icon.svg';
import { getCurrentDomain } from '@/utils/utils';
// 自定义图片渲染组件
const ImageRenderer = ({ src, alt }) => {
  return <img src={src} alt={alt} />;
};
const BlogDetail = () => {
  const [info, setInfo] = useState({});
  const dispatch = useDispatch();
  const history = useHistory();
  const location = useLocation();
  const [articleId, setArticleId] = useState();
  const instanceId = getInstanceId();
  const [hotArticleList, setHotArticleList] = useState([]);
  const [loading, setLoading] = useState(true);
  const [hotLoading, setHotLoading] = useState(true);
  const [catalogs, setCatalogs] = useState([]);

  // 处理目录点击事件
  const handleCatalogClick = id => {
    // 查找ID为指定值的元素
    const targetElement = document.getElementById(id);
    if (targetElement) {
      // 找到后滚动到该元素
      targetElement.scrollIntoView({ behavior: 'smooth' });
    } else {
      // 备用方案：查找内容中可能包含该ID的元素
      console.log(
        'Element with ID',
        id,
        'not found, falling back to content search',
      );
    }
  };

  const getHotArticleList = type => {
    setHotLoading(true);
    dispatch({
      type: 'blog/getHotArticleList',
      payload: { type: type },
      callback: response => {
        setHotLoading(false);
        if (response.code === 200) {
          let data = response.data;
          console.log(data, 'data');
          const domain = getCurrentDomain();
          data.forEach(item => {
            if (item.coverImageUrl) {
              item.coverImageUrl = item.coverImageUrl.replace(
                /\${image_url}/g,
                domain,
              );
            }
          });
          setHotArticleList(data);
        }
      },
    });
  };
  // 处理目录
  const handleCatalog = content => {
    // 创建一个临时的DOM元素来解析HTML内容
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = content;

    // 查找所有带有数字ID的div元素
    const divs = tempDiv.querySelectorAll('div[id]');
    console.log(divs, 'divs');
    const result = [];

    divs.forEach(div => {
      const id = div.id;
      // 只处理纯数字ID的div，如"01", "02"等
      if (/^\d+$/.test(id)) {
        // 查找div中的所有h3元素
        const h3Elements = div.querySelectorAll('h3');
        let titleText = '';

        // 如果有至少两个h3元素，第一个通常包含编号，第二个包含标题
        if (h3Elements.length >= 2) {
          // 获取第二个h3中的文本
          let rawTitle = h3Elements[1].textContent.trim();
          // 清理文本，去除多余空格和换行
          titleText = rawTitle.replace(/\s+/g, ' ');
        }
        // 如果只有一个h3元素，则直接使用h3元素的文本
        if (h3Elements.length === 1) {
          titleText = h3Elements[0].textContent.trim();
        }

        result.push({
          id: id,
          title: titleText,
          fullTitle: id + '. ' + titleText,
        });
      }
    });

    return result;
  };
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    setArticleId(searchParams.get('articleId'));
    // 确保articleId存在
    if (!articleId) return;

    // 模拟从后端获取Markdown内容
    // 在实际项目中，这里应该是API请求
    setLoading(true);
    dispatch({
      type: 'blog/getArticleDetail',
      payload: { articleId: articleId },
      callback: response => {
        setLoading(false);
        if (response.code === 200) {
          // 使用正则表达式替换coverImageUrl中的${image_url}为当前域名
          const domain = getCurrentDomain();
          response.data.content = response.data.content.replace(
            /\${image_url}/g,
            domain,
          );
          setInfo(response.data);
          getHotArticleList(response.data.type);
          // 提取目录
          if (response.data.content) {
            console.log(handleCatalog(response.data.content), 'matches');
            setCatalogs(handleCatalog(response.data.content));
          }
        }
      },
    });
  }, [articleId]);

  // 渲染博客详情骨架屏
  const renderDetailSkeleton = () => {
    return (
      <>
        <div className={styles.blogDetailTitle}>
          <Skeleton active paragraph={false} title={{ width: '80%' }} />
        </div>
        <div className={styles.blogDetailInfo}>
          <div className={styles.author}>
            <Skeleton.Avatar active size={64} shape="square" />
            <div className={styles.authorInfo}>
              <Skeleton active paragraph={false} title={{ width: '60%' }} />
              <Skeleton active paragraph={false} title={{ width: '40%' }} />
            </div>
          </div>
          <Skeleton active paragraph={false} title={{ width: '30%' }} />
        </div>
        <div className={styles.blogDetailContent}>
          <Skeleton active paragraph={{ rows: 10 }} />
        </div>
      </>
    );
  };

  // 渲染热门文章骨架屏
  const renderHotArticleSkeleton = () => {
    return Array(3)
      .fill()
      .map((_, index) => (
        <div
          className={styles.blogContentHotItem}
          key={`hot-skeleton-${index}`}
        >
          <div className={styles.blogContentHotItemContent}>
            <div className={styles.blogContentHotItemContentTitle}>
              <Skeleton active paragraph={false} title={{ width: '100%' }} />
            </div>
            <div className={styles.blogContentHotItemContentTime}>
              <Skeleton active paragraph={false} title={{ width: '50%' }} />
            </div>
          </div>
          <Skeleton.Image
            active
            style={{ width: '140px', height: '140px', borderRadius: '10px' }}
          />
        </div>
      ));
  };
  const handleArticleClick = articleId => {
    // 更新URL，同时携带articleId作为query参数
    history.push({
      pathname: '/resourcesBlogDetail',
      search: `?articleId=${articleId}`,
    });
    // 更新本地状态
    setArticleId(articleId);
  };
  return (
    <>
      <Header />
      {/* 导航栏 */}
      <div className={styles.blogDetailNav}>
        <div
          className={styles.blogDetailNavBack}
          onClick={() => history.push('/resourcesBlog')}
        >
          <DoubleLeftOutlined />
          返回
        </div>
        <div className={styles.blogDetailNavPath}>
          资源 {'>'} 博客 {'>'}{' '}
          <span className={styles.blogDetailNavPathTitle}>详情</span>
        </div>
      </div>
      <div className={styles.blogDetailContainer}>
        <div className={styles.blogDetailLeft}>
          {loading ? (
            renderDetailSkeleton()
          ) : (
            <>
              <div className={styles.blogDetailTitle}>{info.title}</div>
              <div className={styles.blogDetailInfo}>
                <div className={styles.author}>
                  <img
                    className={styles.authorAvatar}
                    src={require('@/assets/logo.png')}
                    alt="avatar"
                  />
                  <div className={styles.authorInfo}>
                    {/* <div className={styles.name}>{info.author}</div> */}
                    <div className={styles.careers}>{info?.tags}</div>
                  </div>
                </div>
                <div className={styles.createTime}>
                  <img
                    src={ClockIcon}
                    alt="时钟图标"
                    style={{
                      display: 'inline',
                      marginRight: '5px',
                      verticalAlign: 'middle',
                    }}
                  />
                  {info.createTime}
                </div>
              </div>
              <div className={styles.blogDetailContent}>
                <ReactMarkdown
                  components={{ img: ImageRenderer }}
                  remarkPlugins={[remarkGfm]}
                  rehypePlugins={[rehypeRaw]}
                >
                  {info.content}
                </ReactMarkdown>
              </div>
            </>
          )}
        </div>
        <div className={styles.blogDetailRight}>
          {/* 目录 */}
          <div className={styles.blogDetailCatalog}>
            <div className={styles.blogDetailCatalogTitle}>
              <FormattedMessage
                id="resources.blog.catalog.title"
                defaultMessage="目录"
              />
            </div>
            <div className={styles.blogDetailCatalogList}>
              {catalogs.length > 0 ? (
                catalogs.map(item => (
                  <div
                    className={styles.blogDetailCatalogItem}
                    key={item.id}
                    onClick={() => handleCatalogClick(item.id)}
                    style={{ cursor: 'pointer' }}
                  >
                    <div className={styles.blogDetailCatalogItemTitle}>
                      {item.fullTitle}
                    </div>
                  </div>
                ))
              ) : (
                <div className={styles.blogDetailCatalogItem}>
                  <div className={styles.blogDetailCatalogItemTitle}>
                    {info.title}
                  </div>
                </div>
              )}
            </div>
          </div>
          {/* 热门文章 */}
          <div className={styles.blogContentHot}>
            <div className={styles.blogContentHotTitle}>
              <FormattedMessage
                id="resources.blog.hot.title"
                defaultMessage="热门文章"
              />
            </div>
            {hotLoading
              ? renderHotArticleSkeleton()
              : hotArticleList.map(item => (
                  <div
                    className={styles.blogContentHotItem}
                    key={item.articleId}
                    onClick={() => handleArticleClick(item.articleId)}
                  >
                    <div className={styles.blogContentHotItemContent}>
                      <div className={styles.blogContentHotItemContentTitle}>
                        {item.title}
                      </div>
                      <div className={styles.blogContentHotItemContentTime}>
                        {item.createTime}
                      </div>
                    </div>
                    <img src={item.coverImageUrl} alt="博客" />
                  </div>
                ))}
          </div>
        </div>
      </div>
      <Footer />
      <ChatWidget instanceId={instanceId}></ChatWidget>
    </>
  );
};

export default BlogDetail;
