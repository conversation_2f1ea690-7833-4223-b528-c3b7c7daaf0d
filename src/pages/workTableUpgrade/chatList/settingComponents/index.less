.contentBoxSetting {
  border-radius: 0px 0px 0px 4px;
  box-shadow: -1px 1px 1px 1px rgba(0, 0, 0, 0.05);
  background-color: rgba(255, 255, 255, 0.6);
  padding: 20px;

  .fuTitle {
    color: #333;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 150%;
    margin-top: 10px;
    margin-bottom: 0;
  }

  .title {
    color: rgba(0, 0, 0, 0.88);
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 14px;
  }

  .fs12 {
    font-size: 12px;
  }

  .color999 {
    color: #999;
  }

  .mt_20 {
    margin-bottom: 20px;
  }

  .mt_10 {
    margin-top: 10px;
  }

  .box_content {
    margin-top: 0.5rem;
    padding: 0.5rem;
    color: #000;
    font-size: 0.75rem;
    border-radius: 0.2rem 0.5rem 0.5rem 0.5rem;
    box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    overflow-wrap: break-word;
  }

  .contentBoxSettingTitle {
    color: rgba(0, 0, 0, 0.88);
    font-size: 12px;
    font-style: normal;
    line-height: 14px;
    margin-top: 10px;
    /* 100% */
  }

  :global {
    .ant-steps-item-title {
      color: rgba(0, 0, 0, 0.88) !important;
      font-family: 'Poppins', sans-serif;
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
      line-height: 14px;
      /* 100% */
    }

    .ant-steps-item-finish
      > .ant-steps-item-container
      > .ant-steps-item-tail::after {
      background-color: #ad30e5;
    }

    .ant-steps-item-finish:last-child {
      .ant-steps-icon-dot:last-of-type {
        display: none;
      }
    }

    .ant-steps-item-finish:nth-last-of-type(2) {
      .ant-steps-item-container > .ant-steps-item-tail::after {
        background: linear-gradient(180deg, #ad30e5, #fff) !important;
      }
    }

    .ant-steps-item-icon {
      width: 8px !important;
      height: 8px !important;
    }

    .ant-steps-icon-dot {
      background-color: rgb(24, 144, 255) !important;
    }

    .ant-spin-container {
      height: auto !important;
    }

    .ant-spin-nested-loading {
      height: auto !important;
    }

    .ant-form-item {
      margin-bottom: 0 !important;
    }

    .ant-form-item-label {
      padding: 0 !important;
      /* 100% */
    }

    .ant-select {
      border-radius: 6px;
      background: rgba(255, 255, 255, 0.5);
      font-size: 12px;
      height: 32px;
    }

    .ant-select:not(.ant-select-disabled):hover .ant-select-selector {
      border-color: #40a9ff;
      border-right-width: 1px;
      box-shadow: none !important;
    }

    .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input)
      .ant-select-selector {
      box-shadow: none !important;
    }

    .ant-select-selector {
      border-radius: 6px !important;
      // border: 1px solid #e6e6e6;
      box-shadow: none !important;
      background: #fff;
      font-size: 12px;
    }
  }
}
