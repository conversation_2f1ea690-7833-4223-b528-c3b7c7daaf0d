import React, { useEffect, useRef, useState } from 'react';
import styles from './index.less';
import {
  Button,
  Form,
  Row,
  Col,
  Checkbox,
  Popover,
  Select,
  Table,
  Spin,
  notification,
  Tag,
  Modal,
  Popconfirm,
  Tooltip,
  Steps,
} from 'antd';
import { PlusOutlined, MenuOutlined } from '@ant-design/icons';
import 'amazon-connect-streams';
import 'amazon-connect-chatjs';
import { DownloadSvg, Edit, Delete } from '../icon.js';
import blueBg from '@/assets/blueBgWen.svg';
import workTableSettingAi from '@/assets/workTable_setting_ai.svg';
import CloseIcon from '@/assets/close-icon-error.png';
import wily from '@/assets/wily.svg';
import {
  SortableContainer,
  SortableElement,
  SortableHandle,
} from 'react-sortable-hoc';
import { arrayMoveImmutable } from 'array-move';

import { getIntl, FormattedMessage, useSelector, useDispatch } from 'umi';
const { Option, OptGroup } = Select;

const SettingComponents = () => {
  const formAddChannelRef = useRef(null);
  const { user } = useSelector(({ layouts }) => ({
    user: layouts.user,
  }));
  const dispatch = useDispatch();
  const [translationCode, setTranslationCode] = useState('');
  const [current, setCurrent] = useState(10);

  const [translationEnable, setTranslationEnable] = useState(false);
  const [tableDataSource, setTableDataSource] = useState([]);
  const [isSort, setIsSort] = useState(false); // true需要拖拽排序
  const SortableItem = SortableElement(props => <tr {...props} />);
  const SortableBody = SortableContainer(props => <tbody {...props} />);

  const DragHandle = SortableHandle(() => (
    <MenuOutlined
      style={{
        cursor: 'grab',
        color: '#999',
      }}
    />
  ));
  const colorList = [
    {
      key: 'color1',
      value: '#3463FC',
      label: '#3463FC',
    },
    {
      key: 'color2',
      value: '#00B900',
      label: '#00B900',
    },
    {
      key: 'color3',
      value: '#AD30E5',
      label: '#AD30E5',
    },
    {
      key: 'color4',
      value: '#2D6973',
      label: '#2D6973',
    },
    {
      key: 'color5',
      value: '#F22417',
      label: '#F22417',
    },
    {
      key: 'color6',
      value: '#D7CE1E',
      label: '#D7CE1E',
    },
    {
      key: 'color7',
      value: '#86BF00',
      label: '#86BF00',
    },
    {
      key: 'color8',
      value: '#FB8F83',
      label: '#FB8F83',
    },
    {
      key: 'color12',
      value: '#EE6521',
      label: '#EE6521',
    },
    {
      key: 'color13',
      value: '#FB83F5',
      label: '#FB83F5',
    },
    {
      key: 'color9',
      value: '#64635E',
      label: '#64635E',
    },
    {
      key: 'color10',
      value: '#9C9992',
      label: '#9C9992',
    },
  ];
  const [loading, setLoading] = useState(false);
  const [loadingRule, setLoadingRule] = useState(false);
  const [createTagOpen, setCreateTagOpen] = useState(false);
  const [lastTagList, setLastTagList] = useState([]);
  const [selectOptionList, setSelectOptionList] = useState([]);
  const [tagColor, setTagColor] = useState('');
  const [editPhotoId, setEditPhotoId] = useState('');
  const [selectTagAttr, setSelectTagAttr] = useState([]);
  //这里是初始化的逻辑
  useEffect(() => {
    //查头像规则
    queryAvatorRuleList();
    queryTagSelectList();
    let aiFlag = localStorage.getItem('worktable_setting_ai') === 'true';
    let translationFlag =
      localStorage.getItem('worktable_setting_translation') === 'true';
    let translationCode = localStorage.getItem(
      'worktable_setting_translation_code',
    );
    setTranslationEnable(translationFlag);
    setTranslationCode(translationCode);
    formAddChannelRef.current?.setFieldsValue({
      ai_agent_enable: aiFlag,
      translation_enable: translationFlag,
    });
  }, []);
  //查头像规则
  const queryAvatorRuleList = () => {
    setLoading(true);
    dispatch({
      type: 'avatorService/queryAllPhotoSettingList',
      callback: response => {
        setLoading(false);
        if (response.code == 200) {
          setTableDataSource(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 标签列表
  const queryTagSelectList = () => {
    dispatch({
      type: 'allocation/queryAllStandardTag',
      callback: response => {
        let { code, data, msg } = response;
        if (code === 200) {
          setSelectOptionList(data);
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };
  //这里是下载日志
  const downLoadLoggor = () => {
    connect?.getLog()?.download();
  };
  //这里是修改表单的逻辑
  const handleFormValuesChange = (value, allValues) => {
    console.log(allValues, 'handleFormValuesChange');
    if (allValues.ai_agent_enable) {
      localStorage.setItem('worktable_setting_ai', 'true');
    } else {
      localStorage.setItem('worktable_setting_ai', 'false');
    }
    if (allValues.translation_enable) {
      setTranslationEnable(true);
      localStorage.setItem('worktable_setting_translation', 'true');
      dispatch({
        type: 'worktable/saveWorkTableSettingTranslation',
        payload: true,
      });
    } else {
      setTranslationEnable(false);
      localStorage.setItem('worktable_setting_translation', 'false');
      dispatch({
        type: 'worktable/saveWorkTableSettingTranslation',
        payload: false,
      });
    }
  };
  // 这里是选择翻译语言的逻辑
  const handleTranslationCodeChange = value => {
    setTranslationCode(value);
    localStorage.setItem('worktable_setting_translation_code', value);
    dispatch({
      type: 'worktable/saveWorkTableSettingTranslationCode',
      payload: value,
    });
  };

  const [columns, setColumns] = useState([
    {
      title: <DragHandle />,
      dataIndex: 'sort',
      key: 'sort',
      align: 'center',
      width: 30,
      className: 'drag-visible',
      render: (text, record) => {
        if (+record.defaultSetting === 1) {
          return (
            <span>
              <FormattedMessage
                id="allocation.applicable.channel.types.for.default"
                defaultMessage="默认"
              />
            </span>
          );
        } else {
          return <DragHandle />;
        }
      },
    },
    {
      title: getIntl().formatMessage({
        id: 'workerOffers.chatList.setting.customer.avatar.color.table.order',
        defaultValue: '匹配顺序',
      }),
      dataIndex: 'photoOrder',
      key: 'photoOrder',
      ellipsis: true,
      align: 'center',
      width: 30,
    },
    {
      title: getIntl().formatMessage({
        id: 'allocation.more.rules.detail.customer.tag',
        defaultValue: '客户标签',
      }),
      dataIndex: 'workRecordList',
      key: 'workRecordList',
      ellipsis: true,
      width: 200,
      render: (text, record) => {
        if (+record.defaultSetting === 1) {
          return (
            <div>
              <span
                style={{
                  fontSize: '12px',
                  fontWeight: '700',
                  color: '#333',
                }}
              >
                <FormattedMessage
                  id="workerOffers.chatList.setting.customer.avatar.color.table.other.tag"
                  defaultMessage="其他标签"
                />
              </span>
            </div>
          );
        } else {
          return (
            <div>
              {record.customerTagList?.map((item, index) => {
                return index <= 2 ? (
                  <div
                    style={{
                      display: 'inline-block',
                      border: `1px solid ${item.tagColor}`,
                      padding: '3px 10px',
                      borderRadius: '4px',
                      background: `linear-gradient(0deg, ${item.tagColor +
                        '1E'} 0%, ${item.tagColor + '1E'} 100%), #FFF`,
                      fontSize: '12px',
                      width: 'max-content',
                      marginRight: 10,
                    }}
                  >
                    <span style={{ color: item.tagColor }}>
                      {item.categoryContent}
                    </span>
                    /{item.tagContent}
                  </div>
                ) : (
                  ''
                );
              })}
              {record.customerTagList?.length > 3 && (
                <Tooltip
                  color={'#fff'}
                  title={() =>
                    record.customerTagList?.map((item, index) => {
                      return (
                        index > 2 && (
                          <div
                            style={{
                              display: 'inline-block',
                              border: `1px solid ${item.tagColor}`,
                              padding: '3px 10px',
                              borderRadius: '4px',
                              background: `linear-gradient(0deg, ${item.tagColor +
                                '1E'} 0%, ${item.tagColor + '1E'} 100%), #FFF`,
                              fontSize: '12px',
                              width: 'max-content',
                              marginRight: 10,
                              color: '#000',
                              marginTop: 10,
                            }}
                          >
                            <span style={{ color: item.tagColor }}>
                              {item.categoryContent}
                            </span>
                            /{item.tagContent}
                          </div>
                        )
                      );
                    })
                  }
                >
                  <div
                    style={{
                      display: 'inline-block',
                      border: `1px solid #3463FC`,
                      padding: '0px 10px',
                      borderRadius: '4px',
                      background: `linear-gradient(0deg, #3463FC1E 0%, #3463FC1E 100%), #FFF`,
                      fontSize: '16px',
                      width: 'max-content',
                      marginRight: 10,
                      fontWeight: '700',
                      color: '#3463FC',
                      cursor: 'pointer',
                    }}
                  >
                    ·&nbsp;·&nbsp;·
                  </div>
                </Tooltip>
              )}
            </div>
          );
        }
      },
    },
    {
      title: getIntl().formatMessage({
        id:
          'workerOffers.chatList.setting.customer.avatar.color.table.customer.color',
        defaultValue: '客户头像颜色',
      }),
      dataIndex: 'photoColor',
      key: 'photoColor',
      ellipsis: true,
      width: 60,
      render: (text, record) => {
        if (+record.defaultSetting === 1) {
          return (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span
                style={{
                  width: 12,
                  height: 12,
                  borderRadius: 2,
                  background: '#3463FC',
                  display: 'inline-block',
                  marginRight: 5,
                }}
              ></span>
              <span
                style={{
                  fontSize: '12px',
                  color: '#333',
                }}
              >
                #3463FC
              </span>
            </div>
          );
        } else {
          return (
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span
                style={{
                  width: 12,
                  height: 12,
                  borderRadius: 2,
                  background: record.photoColor,
                  display: 'inline-block',
                  marginRight: 5,
                }}
              ></span>
              <span
                style={{
                  fontSize: '12px',
                  color: '#333',
                }}
              >
                {record.photoColor}
              </span>
            </div>
          );
        }
      },
    },
    {
      title: getIntl().formatMessage({
        id: 'alloctionRule.operation.operation',
        defaultValue: '操作',
      }),
      dataIndex: 'operation',
      key: 'operation',
      ellipsis: true,
      fixed: 'right',
      width: 60,
      render: (text, record) => {
        return (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            {/* <HOCAuth authKey={'addOrUpdate_settingTicket'}>
              {authAccess => ( */}
            {+record.defaultSetting !== 1 ? (
              <p
                onClick={() => handleEditModalOpen(record)}
                style={{
                  marginRight: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  color: '#3463FC',
                  marginBottom: 0,
                  cursor: 'pointer',
                }}
                // className={authAccess && 'disabledFont'}
              >
                <span
                  style={{ marginRight: '4px', marginTop: '4px' }}
                  // className={authAccess && 'disabledFont'}
                >
                  {Edit()}
                </span>
                <FormattedMessage
                  style={{ marginLeft: '4px' }}
                  id="alloctionRule.table.editor"
                  defaultMessage="修改"
                />
              </p>
            ) : (
              ''
            )}
            {/* )} */}
            {/* </HOCAuth> */}
            {+record.defaultSetting !== 1 ? (
              // <HOCAuth authKey={'remove_settingTicket'}>
              // {authAccess => (
              <Popconfirm
                title={getIntl().formatMessage({
                  id: 'delete.channel.confirm.rule',
                  defaultValue: '确定删除么？',
                })}
                onConfirm={() => handleDeleteTableIt(record)}
                disabled={record.enableStatus === 0}
                style={{
                  marginRight: '20px',
                }}
              >
                <p
                  style={{
                    marginRight: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    color: '#F22417',
                    marginBottom: 0,
                    cursor: 'pointer',
                  }}
                  // className={authAccess && 'disabledFont'}
                >
                  <span
                    style={{ marginRight: '4px', marginTop: '4px' }}
                    // className={authAccess && 'disabledFont'}
                  >
                    {Delete()}
                  </span>
                  <FormattedMessage
                    style={{ marginLeft: '4px' }}
                    id="alloctionRule.table.delete"
                    defaultMessage="删除"
                  />
                </p>
              </Popconfirm>
            ) : (
              // )}
              // </HOCAuth>
              ''
            )}
          </div>
        );
      },
    },
  ]);
  const DraggableContainer = props => (
    <SortableBody
      useDragHandle
      disableAutoscroll
      helperClass="row-dragging"
      onSortEnd={onSortEnd}
      {...props}
    />
  );
  const DraggableBodyRow = ({ className, style, ...restProps }) => {
    // function findIndex base on Table rowKey props and should always be a right array index
    let index = tableDataSource.findIndex(
      x => x.photoId === restProps['data-row-key'],
    );
    let currentItem = tableDataSource.find(
      x => x.photoId === restProps['data-row-key'],
    );
    if (+currentItem?.defaultSetting === 1) {
      return <tr className={className} {...restProps} />;
    }
    return (
      <SortableItem
        index={index}
        {...restProps}
        style={{ border: '1px solid #F9F9F9' }}
      />
    );
  };
  const onSortEnd = ({ oldIndex, newIndex }) => {
    if (oldIndex !== newIndex) {
      const newData = arrayMoveImmutable(
        tableDataSource.slice(),
        oldIndex,
        newIndex,
      ).filter(el => !!el);
      setTableDataSource(newData);
      updatePhotoOrder(newData);
      console.log(newData, 'onSortEndnewData');
    }
  };
  //排序
  const updatePhotoOrder = newData => {
    setLoading(true);
    let payload = newData.map((item, index) => {
      return {
        photoId: item.photoId,
      };
    });
    dispatch({
      type: 'avatorService/updatePhotoOrder',
      payload: payload,
      callback: response => {
        let { code, data, msg } = response;
        setLoading(false);
        if (code === 200) {
          queryAvatorRuleList();
          queryTagSelectList();
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };
  // 选择标签
  const handleSelectTag = selectedValue => {
    let flag = false;
    selectTagAttr.forEach(item => {
      if (item.tagId === selectedValue) {
        flag = true;
      }
    });
    //如果存在不做处理
    if (flag) {
      return;
    }
    let categoryContent = '';
    let tagContent = '';
    let tagColor = '';
    let tagId = '';
    selectOptionList.forEach(item => {
      item.tagList.forEach(child => {
        if (child.tagId === selectedValue) {
          categoryContent = item.categoryContent;
          tagContent = child.tagContent;
          tagColor = child.tagColor;
          tagId = child.tagId;
        }
      });
    });
    console.log(categoryContent, tagContent, tagColor, 'handleSelectTag');
    setSelectTagAttr([
      ...selectTagAttr,
      {
        categoryContent,
        tagContent,
        tagColor,
        tagId,
      },
    ]);
  };
  // 切换颜色
  const handleChangeColor = value => {
    setTagColor(value);
  };
  // 取消标签
  const handleCancelTag = () => {
    setSelectTagAttr([]);
    setTagColor('');
    setCreateTagOpen(false);
    setEditPhotoId('');
  };
  // 保存标签
  const handleSaveTag = () => {
    setLoadingRule(true);
    dispatch({
      type: 'avatorService/addPhotoSetting',
      payload: {
        photoColor: tagColor, //头像颜色
        tagInfoList: selectTagAttr, //所选的客户标签集合
      },
      callback: response => {
        let { code, data, msg } = response;
        setLoadingRule(false);
        if (code === 200) {
          handleCancelTag();
          queryAvatorRuleList();
          queryTagSelectList();
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };
  // 删除表格项
  const handleDeleteTableIt = item => {
    dispatch({
      type: 'avatorService/removePhotoSetting',
      payload: {
        photoId: item.photoId,
      },
      callback: response => {
        let { code, data, msg } = response;
        if (code === 200) {
          queryAvatorRuleList();
          queryTagSelectList();
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };
  const handleEditModalOpen = item => {
    setEditPhotoId(item.photoId);
    setCreateTagOpen(true);
    setSelectTagAttr(item.customerTagList);
    setTagColor(item.photoColor);
  };
  // 修改表格项
  const handleEditTableIt = () => {
    setLoadingRule(true);
    dispatch({
      type: 'avatorService/updatePhotoSetting',
      payload: {
        photoId: editPhotoId,
        photoColor: tagColor, //头像颜色
        tagInfoList: selectTagAttr, //所选的客户标签集合
      },
      callback: response => {
        let { code, data, msg } = response;
        setLoadingRule(false);
        if (code === 200) {
          handleCancelTag();
          queryAvatorRuleList();
          queryTagSelectList();
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };
  // 删除标签
  const handleDeleteTag = item => {
    setSelectTagAttr(selectTagAttr.filter(tag => tag.tagId !== item.tagId));
  };
  return (
    <div className={styles.contentBoxSetting}>
      <Spin spinning={loading}>
        <Form
          name="basic"
          autoComplete="off"
          layout="vertical"
          ref={formAddChannelRef}
          // onFinish={e => onFinish(e)}
          onValuesChange={(e, allValues) =>
            handleFormValuesChange(e, allValues)
          }
        >
          <Steps
            progressDot
            current={current}
            direction="vertical"
            items={[
              user.agentAccessChannel?.search('1') != -1 && {
                title: (
                  <span className={styles.title}>
                    <FormattedMessage
                      id="new.worktable.setting.title"
                      defaultMessage="聊天设置"
                    />
                  </span>
                ),
                description: (
                  <div>
                    <Row style={{ flexDirection: 'column' }}>
                      <Form.Item
                        label={
                          <div>
                            <p className={styles.fuTitle}>
                              <FormattedMessage
                                id="new.worktable.setting.content"
                                defaultMessage="工单详情展示命中的意图（适用于聊天和邮件渠道）"
                              />
                              <Popover
                                content={() => (
                                  <div>
                                    <p>
                                      <FormattedMessage
                                        id="chat.channel.configuration.chat4.workers.position"
                                        defaultMessage="所在位置"
                                      />
                                    </p>
                                    <div
                                      style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        alignItems: 'baseline',
                                      }}
                                    >
                                      <div>
                                        <img
                                          src={wily}
                                          style={{ width: 20, height: 20 }}
                                        />
                                      </div>
                                      <div
                                        style={{
                                          display: 'flex',
                                          flexDirection: 'column',
                                          marginLeft: 5,
                                          width: 250,
                                        }}
                                      >
                                        <div style={{ fontSize: 12 }}>Wily</div>
                                        <div
                                          className={styles.box_content}
                                          style={{
                                            backgroundColor: '#C2DFFC',
                                            marginTop: '0.5rem',
                                            padding: '0.5rem',
                                            color: '#000',
                                            fontSize: '12px',
                                            borderRadius:
                                              '0.2rem 0.5rem 0.5rem 0.5rem',
                                            boxShadow:
                                              '0 2px 10px 0 rgba(0, 0, 0, 0.15)',
                                            display: 'flex',
                                            overflowWrap: 'break-word',
                                            fleDxirection: 'column',
                                          }}
                                        >
                                          <span>
                                            <FormattedMessage
                                              id="new.worktable.setting.popover.content"
                                              defaultMessage="你们的退货政策是什么？我买的衣服太小了，我想退掉"
                                            />
                                          </span>
                                        </div>
                                        <div
                                          style={{
                                            color: '#999',
                                            fontSize: 10,
                                            padding: '2px 4px',
                                            width: 'fit-content',
                                            marginTop: 10,
                                          }}
                                        >
                                          <img
                                            src={workTableSettingAi}
                                            width={12}
                                            height={12}
                                            style={{
                                              display: 'inline-block',
                                              marginRight: 5,
                                              marginBottom: 3,
                                            }}
                                          />
                                          <FormattedMessage
                                            id="new.worktable.setting.popover.content.tips"
                                            defaultMessage="检测到「退货意图」，执行「退货智能体」"
                                          />
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                )}
                                trigger="click"
                                placement="right"
                              >
                                <img
                                  src={blueBg}
                                  style={{
                                    marginLeft: 10,
                                    marginBottom: 2,
                                    width: 16,
                                    height: 16,
                                    cursor: 'pointer',
                                  }}
                                />
                              </Popover>
                            </p>
                          </div>
                        }
                        name="ai_agent_enable"
                        valuePropName="checked"
                      >
                        <Checkbox>
                          <FormattedMessage
                            id="chat.channel.configuration.work.panels.checkbox"
                            defaultMessage="开启"
                          />
                        </Checkbox>
                      </Form.Item>
                      <p
                        className={`${styles.fs12} ${styles.color999}  ${styles.mt_20}`}
                      >
                        <FormattedMessage
                          id="new.worktable.setting.content.tips"
                          defaultMessage="开启之后，在工单详情页面以及座席工作台页面，会自动在客户聊天信息下方展示命中的意图"
                        />
                      </p>
                    </Row>
                    <Row style={{ flexDirection: 'column' }}>
                      <Form.Item
                        label={
                          <div>
                            <span className={styles.fuTitle}>
                              <FormattedMessage
                                id="worktable.live.translation"
                                defaultMessage="实时翻译"
                              />
                            </span>
                          </div>
                        }
                        name="translation_enable"
                        valuePropName="checked"
                      >
                        <Checkbox>
                          <FormattedMessage
                            id="chat.channel.configuration.work.panels.checkbox"
                            defaultMessage="开启"
                          />
                        </Checkbox>
                      </Form.Item>
                      {translationEnable && (
                        <div>
                          <Select
                            showSearch
                            placeholder={
                              <FormattedMessage
                                id="work.order.reply.select.language"
                                defaultMessage="选择目标语言"
                              />
                            }
                            value={translationCode}
                            onChange={e => handleTranslationCodeChange(e)}
                            filterOption={(inputValue, option) =>
                              option.label
                                .toLowerCase()
                                .indexOf(inputValue.toLowerCase()) >= 0
                            }
                            options={JSON.parse(
                              localStorage.getItem('languageLocal'),
                            )}
                          />
                        </div>
                      )}
                      <p
                        className={`${styles.fs12} ${styles.color999}  ${styles.mt_20}  ${styles.mt_10}`}
                      >
                        <FormattedMessage
                          id="workerOffers.chatList.setting.translation.tips"
                          defaultMessage="开启之后，您与客户的每次聊天都会进行实时翻译"
                        />
                      </p>
                    </Row>
                  </div>
                ),
              },
              user.agentAccessChannel?.search('1') != -1 && {
                title: (
                  <span className={styles.title}>
                    <FormattedMessage
                      id="workerOffers.chatList.setting.customer.avatar.color"
                      defaultMessage="客户头像颜色设置"
                    />
                  </span>
                ),
                description: (
                  <Row style={{ flexDirection: 'column' }}>
                    <Form.Item
                      label={false}
                      name="ai_agent_enable"
                      valuePropName="checked"
                    >
                      <Table
                        dataSource={tableDataSource}
                        columns={columns}
                        // scroll={{ x: '100%', y: 500 }}
                        className="ruleStyle"
                        rowKey="photoId"
                        components={{
                          body: {
                            wrapper: DraggableContainer,
                            row: DraggableBodyRow,
                          },
                        }}
                        pagination={false}
                      />
                      <Button
                        type="primary"
                        style={{ marginTop: 10, marginBottom: 20 }}
                        icon={
                          <PlusOutlined
                            style={{ fontSize: '12px', marginRight: 5 }}
                          />
                        }
                        onClick={() => setCreateTagOpen(true)}
                      >
                        <FormattedMessage
                          id="workerOffers.chatList.setting.customer.avatar.color.table.add"
                          defaultMessage="创建规则"
                        />
                      </Button>
                    </Form.Item>
                  </Row>
                ),
              },
              user.agentAccessChannel?.search('3') != -1 && {
                title: (
                  <span className={styles.title}>
                    <FormattedMessage
                      id="new.worktable.setting.phone.title"
                      defaultMessage="电话设置"
                    />
                  </span>
                ),
                description: (
                  <>
                    <p className={styles.contentBoxSettingTitle}>
                      <FormattedMessage
                        id="workTable.setting.loggor.title"
                        defaultMessage="日志"
                      />
                    </p>
                    <p>
                      <Button
                        type="primary"
                        onClick={() => downLoadLoggor()}
                        icon={<DownloadSvg style={{ fontSize: '12px' }} />}
                      >
                        <span style={{ fontSize: 12, marginLeft: 5 }}>
                          <FormattedMessage
                            id="workTable.setting.loggor.button"
                            defaultMessage="下载日志"
                          />
                        </span>
                      </Button>
                    </p>
                  </>
                ),
              },
              {
                title: null,
                description: null,
              },
            ]}
          />
        </Form>
      </Spin>
      {/*新建/修改标签*/}
      <Modal
        title={
          editPhotoId
            ? getIntl().formatMessage({
                id:
                  'workerOffers.chatList.setting.customer.avatar.color.table.tag.edit',
                defaultValue: '修改规则',
              })
            : getIntl().formatMessage({
                id:
                  'workerOffers.chatList.setting.customer.avatar.color.table.tag.add',
                defaultValue: '创建规则',
              })
        }
        open={createTagOpen}
        mask={null}
        footer={null}
        className="tagOperationModal"
      >
        <div
          className="tagOperationDetail"
          style={{ display: 'flex', alignItems: 'baseline' }}
        >
          <div className="leftTagContainer" style={{ width: '70%' }}>
            <div>
              <span style={{ fontSize: 12, marginLeft: 5 }}>
                <FormattedMessage
                  id="workerOffers.chatList.setting.customer.avatar.color.table.tag.title"
                  defaultMessage="标签名称："
                />
              </span>
              <div
                style={{
                  width: '85%',
                  display: 'inline-flex',
                  flexDirection: 'column',
                }}
              >
                <Select
                  value={lastTagList}
                  showSearch
                  style={{ width: '100%' }}
                  tagRender={tagRender}
                  placeholder={getIntl().formatMessage({
                    id: 'tag.work.table.agent.select.tips',
                    defaultValue: '请选择标签',
                  })}
                  onSelect={e => handleSelectTag(e)}
                  filterOption={(inputValue, option) =>
                    option.label
                      .toLowerCase()
                      .indexOf(inputValue.toLowerCase()) >= 0
                  }
                  popupClassName="workTableTagSelectColor"
                >
                  {selectOptionList?.map(item => {
                    return (
                      <OptGroup
                        key={item.categoryContent}
                        label={
                          item.categoryContent !== 'private_tag_category_code'
                            ? item.categoryContent
                            : getIntl().formatMessage({
                                id: 'tag.management.tab.private',
                                defaultValue: '私有标签',
                              })
                        }
                      >
                        {item.tagList?.map(items => {
                          return (
                            <Option key={items.tagId} label={items.tagContent}>
                              <div className={items.tagColorCode}></div>
                              <span>{items.tagContent}</span>
                            </Option>
                          );
                        })}
                      </OptGroup>
                    );
                  })}
                </Select>
                <div>
                  {selectTagAttr?.map(item => {
                    return (
                      <div
                        style={{
                          display: 'inline-block',
                          border: `1px solid ${item.tagColor}`,
                          padding: '3px 10px',
                          borderRadius: '4px',
                          background: `linear-gradient(0deg, ${item.tagColor +
                            '1E'} 0%, ${item.tagColor + '1E'} 100%), #FFF`,
                          fontSize: '12px',
                          marginTop: 10,
                          width: 'max-content',
                          marginRight: 10,
                        }}
                      >
                        <span style={{ color: item.tagColor }}>
                          {item.categoryContent}
                        </span>
                        /{item.tagContent}
                        <span
                          onClick={() => handleDeleteTag(item)}
                          style={{ marginLeft: 10, cursor: 'pointer' }}
                        >
                          <img
                            src={CloseIcon}
                            style={{ width: 14, height: 14 }}
                          />
                        </span>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
          <div className="rightTagContainer" style={{ marginLeft: 10 }}>
            <Select
              value={tagColor}
              popupClassName="tagColorSelect"
              onChange={e => handleChangeColor(e)}
              virtual={false}
              style={{ width: 150 }}
              placeholder={getIntl().formatMessage({
                id:
                  'workerOffers.chatList.setting.customer.avatar.color.table.tag.color.placeholder',
                defaultValue: '请选择颜色',
              })}
            >
              {colorList.map((item, index) => {
                return (
                  <Option value={item.value} key={index}>
                    <div className={item.key}></div>
                    <span>{item.label}</span>
                  </Option>
                );
              })}
            </Select>
          </div>
        </div>
        <Row
          style={{ display: 'flex', justifyContent: 'center', marginTop: 20 }}
        >
          <Button
            onClick={() => handleCancelTag()}
            style={{ marginRight: '15px' }}
          >
            <FormattedMessage
              id="work.order.management.btn.cancel"
              defaultMessage="取消"
            />
          </Button>
          <Button
            loading={loadingRule}
            type="primary"
            onClick={() =>
              editPhotoId ? handleEditTableIt() : handleSaveTag()
            }
          >
            <FormattedMessage
              id="customerInformation.add.basicInformation.button.save"
              defaultValue="保存"
            />
          </Button>
        </Row>
      </Modal>
    </div>
  );
};
const tagRender = props => {
  const { label, value, closable, onClose } = props;
  const onPreventMouseDown = event => {
    event.preventDefault();
    event.stopPropagation();
  };
  return (
    <Tag
      className={value}
      onMouseDown={onPreventMouseDown}
      closable={closable}
      onClose={onClose}
      style={{
        marginRight: 3,
      }}
    >
      {label}
    </Tag>
  );
};
export default SettingComponents;
