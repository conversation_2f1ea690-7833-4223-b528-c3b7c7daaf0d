.helpDocumentContainer {
  width: 100%;
  height: auto;
  background: linear-gradient(72deg, #f6f9ff 22.53%, #fdf6ff 100%), #fff;

  .placeholder {
    height: calc(64px + 1.625rem);
  }
  .contentContainer {
    width: 100%;
    //height: auto;
    margin: 0 auto;
    display: flex;
    //gap: 2.5rem;
    //padding-top: calc(64px + 1.625rem);

    .leftNav {
      width: 20rem;
      position: sticky;
      top: 5rem;
      height: fit-content;
      padding: 0 0.52rem;

      .navTitle {
        color: #333;
        font-family: 'Microsoft YaHei';
        font-size: 1.25rem;
        font-style: normal;
        font-weight: 700;
        //font-size: 1.125rem;
        margin-bottom: 1.25rem;
        //padding: 0.625rem 0.9375rem;
        //border-bottom: 0.0625rem solid #e6e6e6;
      }

      :global {
        .ant-menu {
          background: none;
        }
        .ant-menu-inline,
        .ant-menu-vertical,
        .ant-menu-vertical-left {
          border-right: none;
        }
        .ant-menu-inline .ant-menu-item-group-list .ant-menu-submenu-title,
        .ant-menu-inline .ant-menu-submenu-title {
          padding-left: 10px !important;
        }
        .ant-menu-sub.ant-menu-inline > .ant-menu-item,
        .ant-menu-sub.ant-menu-inline
          > .ant-menu-submenu
          > .ant-menu-submenu-title {
          padding-left: 8px !important;
          padding-right: 0px;
          margin-left: -2px;
        }
        .ant-menu-submenu.ant-menu-submenu-inline.ant-menu-submenu-open
          .ant-menu-submenu-title
          .ant-menu-title-content {
          color: #3463fc;
          font-weight: 700;
        }
        .ant-menu-inline .ant-menu-title-content:hover {
          color: #3463fc;
        }
        .ant-menu-submenu.ant-menu-submenu-inline.ant-menu-submenu-open
          .ant-menu-submenu-expand-icon,
        .ant-menu-submenu.ant-menu-submenu-inline.ant-menu-submenu-open
          .ant-menu-submenu-arrow {
          color: #3463fc;
        }
        .ant-menu-submenu.ant-menu-submenu-inline:hover {
          border-radius: 4px;
          background: #f6f6f6;
        }
        .ant-menu-submenu.ant-menu-submenu-inline.ant-menu-submenu-open:hover {
          background: none;
        }
        .ant-menu-submenu.ant-menu-submenu-inline
          .ant-menu-submenu-title
          .ant-menu-title-content {
          color: rgba(102, 102, 102, 1);
          font-weight: 700;
        }
        .ant-menu-submenu.ant-menu-submenu-inline:hover
          .ant-menu-submenu-title
          .ant-menu-title-content {
          color: #333;
          font-weight: 700;
        }
        .ant-menu-submenu.ant-menu-submenu-inline:hover.ant-menu-submenu-open
          .ant-menu-submenu-title
          .ant-menu-title-content {
          color: #3463fc;
        }
        .ant-menu-submenu.ant-menu-submenu-inline .ant-menu-submenu-arrow {
          color: rgba(102, 102, 102, 1);
        }
        .ant-menu.ant-menu-sub.ant-menu-inline {
          border-left: 2px solid #e6e6e6;
          margin-left: 8%;
          width: 92%;
        }
        .ant-menu-vertical .ant-menu-item::after,
        .ant-menu-vertical-left .ant-menu-item::after,
        .ant-menu-vertical-right .ant-menu-item::after,
        .ant-menu-inline .ant-menu-item::after {
          border-right: none;
          display: none;
        }
        .ant-menu-item-only-child.ant-menu-item-selected,
        .ant-menu-item-only-child:hover.ant-menu-item-selected {
          border-left: 2px solid #3463fc;
        }
        .ant-menu-item-selected .ant-menu-title-content {
          color: #3463fc;
        }
        .ant-menu-item-only-child:hover {
          border-radius: 0px 4px 4px 0px;
          border-left: 2px solid #3463fc;
          background: linear-gradient(0deg, #f9f9f9 0%, #f9f9f9 100%),
            rgba(52, 99, 252, 0.05);
        }
      }
    }
    .rightContainer {
      flex: 1;
      padding: 0 2.5rem 3.125rem 2.5rem;
      border-left: 1px solid #e6e6e6;
      .privacyPolicy {
        width: 100%;
        margin: 0 auto;
        padding-bottom: 5rem;

        .privacyPolicyTitle {
          font-family: 'MicrosoftYaHei';
          font-size: 2.5rem;
          font-style: normal;
          font-weight: 700;
          padding-bottom: 15px;
          border-bottom: 0.0625rem solid #e6e6e6;
          color: #333;
          padding: 0 1.25rem;
          margin-bottom: 30px;
        }

        .contentWrapper {
          display: flex;
          gap: 2.5rem;
        }

        .nav {
          width: 18rem;
          position: sticky;
          top: 5rem;
          height: fit-content;

          .navTitle {
            font-size: 1.125rem;
            font-weight: bold;
            margin-bottom: 2.25rem;
            padding: 0.625rem 0.9375rem;
            color: #333;
            border-bottom: 0.0625rem solid #e6e6e6;
          }

          ul {
            list-style: none;
            padding: 0;
            margin: 0;
            border-left: 0.0625rem solid #e6e6e6;

            li {
              line-height: 3rem;
              padding-left: 1rem;
              display: flex;
              align-items: flex-start;

              a {
                color: #333;
                text-decoration: none;
                font-weight: 400;
                transition: color 0.3s;
                overflow: hidden;
                text-overflow: ellipsis;
                font-family: 'MicrosoftYaHei Regular';
                font-size: 1.125rem;
                font-style: normal;
                display: flex;
                span {
                  display: block;
                }
                &:hover {
                  color: #1890ff;
                }
              }
            }
          }
          .whatsAppMenu {
            li span {
              //color: #666;
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              margin-left: 15px;
            }
            .firstTitle span {
              //color: #333;
              font-size: 14px;
              font-style: normal;
              font-weight: 700;
              margin-left: 0px;
            }
          }
        }

        table {
          width: 100%;
          border-collapse: collapse;
          margin: 1rem 0;

          th,
          td {
            border: 1px solid #e6e6e6;
            padding: 0.75rem;
            text-align: left;
            font-family: 'MicrosoftYaHei Regular';
            font-size: 1rem;
            line-height: 1.5;
            white-space: pre-wrap;
          }

          th {
            background-color: #f5f5f5;
            font-weight: bold;
          }
        }

        .content {
          flex: 1;
          //padding: 0.9375rem 2.5rem 3.125rem 2.5rem;
          //border-left: 0.0625rem solid #e6e6e6;

          p {
            color: #333;
            font-family: 'MicrosoftYaHei Regular';
            font-size: 1rem;
            font-style: normal;
            font-weight: 400;
            line-height: 2rem;
            margin-bottom: 10px;
          }
          img {
            max-width: 100%;
            margin-bottom: 15px;
          }

          .section {
            margin-bottom: 2.5rem;

            h2 {
              color: #333;
              font-family: 'MicrosoftYaHei';
              font-size: 1.6875rem;
              font-style: normal;
              font-weight: 700;
              margin-bottom: 10px;
            }

            h3 {
              color: #333;
              font-family: 'MicrosoftYaHei';
              font-size: 1.3875rem;
              font-style: normal;
              font-weight: 700;
              margin-bottom: 10px;
            }

            h4 {
              color: #333;
              font-family: 'MicrosoftYaHei';
              font-size: 1.1875rem;
              font-style: normal;
              font-weight: 700;
              margin-bottom: 10px;
            }

            p {
              color: #333;
              font-family: 'MicrosoftYaHei Regular';
              font-size: 1rem;
              font-style: normal;
              font-weight: 400;
              line-height: 2rem;
              margin-bottom: 10px;
            }
          }
        }

        .active {
          color: #3463fc !important;
          font-weight: bold;
        }
        .active span {
          color: #3463fc !important;
          font-weight: bold;
        }

        .date {
          display: flex;
          padding: 0.9375rem 0;
          gap: 3.125rem;
          border-top: 1px solid #e6e6e6;
          color: #999;
          font-family: 'MicrosoftYaHei Regular';
          font-size: 1rem;
          font-style: normal;
          font-weight: 400;
        }
      }
    }
  }
}
