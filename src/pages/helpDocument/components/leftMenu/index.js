import React, { useState, useRef, useEffect } from 'react';
import { Menu, notification } from 'antd';
import styles from '../../index.less';
import { useDispatch, getIntl, useLocation, history } from 'umi';
const LeftMenu = () => {
  const items = [
    {
      label: getIntl().formatMessage({ id: 'channel.allocation' }),
      key: 'sub1',
      children: [
        {
          label: getIntl().formatMessage({
            id: 'help.document.whats.app.title',
            defaultValue: '如何注册WhatsApp Business Account',
          }),
          key: 1,
        },
        {
          label: getIntl().formatMessage({
            id: 'help.document.line.title',
            defaultValue: '如何注册Line渠道',
          }),
          key: 2,
        },
        {
          label: getIntl().formatMessage({
            id: 'help.document.we.chat.official.title',
            defaultValue: '如何注册微信公众号',
          }),
          key: 3,
        },
        {
          label: getIntl().formatMessage({
            id: 'channel.allocation.discord.document.title',
            defaultValue: 'Discord：集成ConnectNow',
          }),
          key: 4,
        },
      ],
    },
  ];
  const rootSubmenuKeys = ['sub1', 'sub2', 'sub4'];
  const [openKeys, setOpenKeys] = useState(['sub1']);
  const onOpenChange = keys => {
    const latestOpenKey = keys.find(key => openKeys.indexOf(key) === -1);
    if (rootSubmenuKeys.indexOf(latestOpenKey) === -1) {
      setOpenKeys(keys);
    } else {
      setOpenKeys(latestOpenKey ? [latestOpenKey] : []);
    }
  };

  const location = useLocation();
  const [selectedKeys, setSelectedKeys] = useState([]);

  useEffect(() => {
    const path = location.pathname;
    // 这里需要根据实际的路由规则来匹配key
    // 例如，如果路由是 /helpDocument/whatsapp, 那么key是 '1'
    if (path.includes('whatsAppChannelDocument')) {
      setSelectedKeys(['1']);
    } else if (path.includes('lineChannelDocument')) {
      setSelectedKeys(['2']);
    } else if (path.includes('weChatOfficialDocument')) {
      setSelectedKeys(['3']);
    } else if (path.includes('discordHelpDocument')) {
      setSelectedKeys(['4']);
    } else {
      setSelectedKeys([]);
    }
  }, [location.pathname]);

  const onMenuItemClick = ({ key }) => {
    // 根据key进行路由跳转
    switch (key) {
      case '1':
        history.push('/whatsAppChannelDocument');
        break;
      case '2':
        history.push('/lineChannelDocument');
        break;
      case '3':
        history.push('/weChatOfficialDocument');
        break;
      case '4':
        history.push('/discordHelpDocument');
        break;
      default:
        break;
    }
  };

  return (
    <Menu
      mode="inline"
      openKeys={openKeys}
      onOpenChange={onOpenChange}
      selectedKeys={selectedKeys}
      onClick={onMenuItemClick}
      items={items}
    />
  );
};

export default LeftMenu;
