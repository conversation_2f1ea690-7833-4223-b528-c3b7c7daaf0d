import React, { useState, useRef, useEffect } from 'react';
import { Button, notification } from 'antd';
import styles from '../index.less';
import {
  useDispatch,
  getIntl,
  FormattedMessage,
  Link as LinkUrl,
  useIntl,
} from 'umi';
import Header from '@/components/Header';
import LeftMenu from '../components/leftMenu';
import { Element, Events, Link } from 'react-scroll';
import { isCNDomain } from '@/utils/utils';

const WeChatOfficialDocument = () => {
  const dispatch = useDispatch();
  const editorRef = useRef(null);
  const [htmlContent, setHtmlContent] = useState('');

  useEffect(() => {
    Events.scrollEvent.register('begin', (...args) => {
      console.log('begin', args);
    });

    Events.scrollEvent.register('end', (...args) => {
      console.log('end', args);
    });

    return () => {
      Events.scrollEvent.remove('begin');
      Events.scrollEvent.remove('end');
    };
  }, []);
  const intl = useIntl();
  useEffect(() => {}, [intl]);

  return (
    <div className={styles.helpDocumentContainer}>
      <Header />
      <div className={styles.placeholder}></div>
      <div className={styles.contentContainer}>
        <div className={styles.leftNav}>
          <div className={styles.navTitle}>
            <FormattedMessage id="help.document.title" />
          </div>
          <LeftMenu />
        </div>
        <div className={styles.rightContainer}>
          <div className={styles.privacyPolicy}>
            <div className={styles.privacyPolicyTitle}>
              <FormattedMessage id="help.document.we.chat.official.title" />
            </div>
            <div className={styles.contentWrapper}>
              <div className={styles.content}>
                <Element name="one" className={styles.element}>
                  <h2>
                    <FormattedMessage id="userTerms.section.one.prefix" />
                    <FormattedMessage id="help.document.we.chat.official.left.menu" />
                  </h2>
                  <p>
                    <FormattedMessage
                      id="help.document.we.chat.official.step.1.text"
                      values={{
                        a: chunks => (
                          <a
                            href="https://mp.weixin.qq.com/"
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            {chunks}
                          </a>
                        ),
                      }}
                    />
                  </p>
                  <img src="https://www.connectnowai.com/doc/wechat/1.png#pic_center" />
                  <p>
                    <FormattedMessage id="help.document.we.chat.official.step.1.text.1" />
                  </p>
                  <img src={'https://www.connectnowai.com/doc/wechat/2.png'} />
                </Element>
                <Element name="two" className={styles.element}>
                  <h2>
                    <FormattedMessage id="userTerms.section.two.prefix" />
                    <FormattedMessage id="help.document.we.chat.official.left.menu.1" />
                  </h2>
                  <p>
                    <FormattedMessage id="help.document.we.chat.official.step.2.text" />
                  </p>
                  <img src={'https://www.connectnowai.com/doc/wechat/3.png'} />
                  <p>
                    <FormattedMessage id="help.document.we.chat.official.step.2.text.1" />
                  </p>
                </Element>
                <Element name="three" className={styles.element}>
                  <h2>
                    <FormattedMessage id="userTerms.section.three.prefix" />
                    <FormattedMessage id="help.document.we.chat.official.left.menu.2" />
                  </h2>
                  <p>
                    <FormattedMessage id="help.document.we.chat.official.step.3.text" />
                  </p>
                  <img src={'https://www.connectnowai.com/doc/wechat/6.png'} />
                  <p>
                    <FormattedMessage id="help.document.we.chat.official.step.3.text.1" />
                  </p>
                  <img src={'https://www.connectnowai.com/doc/wechat/5.png'} />
                  <p>
                    <FormattedMessage id="help.document.we.chat.official.step.3.text.2" />
                  </p>
                  <img src={'https://www.connectnowai.com/doc/wechat/4.png'} />
                </Element>
                {/* 日期 */}
                {/*<div className={styles.date}>*/}
                {/*  <div className={styles.updateDate}>*/}
                {/*    <FormattedMessage id="document.updateDate" />*/}
                {/*  </div>*/}
                {/*  <div className={styles.effectiveDate}>*/}
                {/*    <FormattedMessage id="document.effectiveDate" />*/}
                {/*  </div>*/}
                {/*</div>*/}
              </div>
              <div className={styles.nav}>
                {/*<div className={styles.navTitle}>*/}
                {/*  <FormattedMessage id="userTerms.nav.title" />*/}
                {/*</div>*/}
                <ul>
                  <li>
                    <Link
                      activeClass={styles.active}
                      to="one"
                      spy={true}
                      smooth={true}
                    >
                      <FormattedMessage id="userTerms.section.one.prefix" />
                      <span>
                        <FormattedMessage id="help.document.we.chat.official.left.menu" />
                      </span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      activeClass={styles.active}
                      to="two"
                      spy={true}
                      smooth={true}
                    >
                      <FormattedMessage id="userTerms.section.two.prefix" />
                      <span>
                        <FormattedMessage id="help.document.we.chat.official.left.menu.1" />
                      </span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      activeClass={styles.active}
                      to="three"
                      spy={true}
                      smooth={true}
                    >
                      <FormattedMessage id="userTerms.section.three.prefix" />
                      <span>
                        <FormattedMessage id="help.document.we.chat.official.left.menu.2" />
                      </span>
                    </Link>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WeChatOfficialDocument;
