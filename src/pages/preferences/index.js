import React, { Component } from 'react';
import { connect, getIntl, FormattedMessage, getLocale, setLocale } from 'umi';
import styles from './index.less';
import { Button, Form, Select, Spin } from 'antd';
import { notification } from '@/utils/utils';
import HOCAuth from '@/components/HOCAuth/index';
import LanguageEnUS from '@/locales/language-en-US.json';
import LanguageJa from '@/locales/language-ja.json';
import LanguageZhCN from '@/locales/language-zh-CN.json';
import LanguageDeDE from '@/locales/language-de-DE.json';
import LanguageIdID from '@/locales/language-id-ID.json';
class preferences extends Component {
  constructor(props) {
    super(props);
    this.state = {
      initLoading: true,
      timeZoneList: [],
      saveLoading: false,
      languageList: [],
    };
    this.formRef = React.createRef();
  }

  componentDidMount() {
    this.initData();
  }
  /**
   * 获取回显信息
   */
  async initData() {
    this.setState({
      initLoading: true,
    });
    /**时区下拉 */
    await this.props.dispatch({
      type: 'personalCenter/listTimeZone',
      callback: response => {
        let { code, data, msg } = response;
        if (code === 200) {
          this.setState({
            timeZoneList: data,
          });
        } else {
          notification.error({
            message: msg,
          });
        }
        this.setState({
          initLoading: false,
        });
      },
    });
    /**语言下拉 */
    await this.props.dispatch({
      type: 'personalCenter/listLanguage',
      callback: response => {
        let { code, data, msg } = response;
        if (code === 200) {
          this.setState({
            languageList: data,
          });
        } else {
          notification.error({
            message: msg,
          });
        }
        this.setState({
          initLoading: false,
        });
      },
    });
    await this.getInfo();
  }
  /**
   * 获取回显信息
   */
  async getInfo() {
    this.setState({
      initLoading: true,
    });
    /**回显 */
    await this.props.dispatch({
      type: 'personalCenter/queryUserPreference',
      callback: response => {
        let { code, data, msg } = response;
        if (code === 200) {
          let info = {};
          data?.forEach(item => {
            info[item.code] = item.value;
          });

          // 切换国际化
          let language = getLocale();
          setLocale(info.language);
          localStorage.setItem('lang', info.language);
          //修改面包屑
          sessionStorage.setItem(
            'currentMenu',
            `${getIntl().formatMessage({
              id: 'home.preferences',
            })}`,
          );
          sessionStorage.setItem('currentUrl', '/preferences');

          if (info.language == 'zh-CN') {
            localStorage.setItem('languageLocal', JSON.stringify(LanguageZhCN));
          } else if (info.language == 'en-US') {
            localStorage.setItem('languageLocal', JSON.stringify(LanguageEnUS));
          } else if (info.language == 'de-DE') {
            localStorage.setItem('languageLocal', JSON.stringify(LanguageDeDE));
          } else if (info.language == 'ja') {
            localStorage.setItem('languageLocal', JSON.stringify(LanguageJa));
          } else if (info.language == 'id-ID') {
            localStorage.setItem('languageLocal', JSON.stringify(LanguageIdID));
          }
          this.formRef.current?.setFieldsValue(info);
        } else {
          notification.error({
            message: msg,
          });
        }
        this.setState({
          initLoading: false,
        });
      },
    });
  }
  /**
   * 修改个人偏好设置
   * @type {(function(*=): void)|*}
   */
  onFinish = values => {
    this.setState({
      saveLoading: true,
    });
    console.log(values);
    let data = Object.entries(values).map(item => {
      return {
        code: item[0],
        value: item[1],
      };
    });
    this.state.timeZoneList.forEach(item => {
      if (item.timeZoneId === values.timezoneId) {
        data.push({
          code: 'timezone',
          value: item.timeZoneCode,
        });
      }
    });

    this.props.dispatch({
      type: 'personalCenter/updateUserPreference',
      payload: data,
      callback: response => {
        let { code, msg } = response;
        if (200 === code) {
          notification.success({
            message: getIntl().formatMessage({
              id: 'user.management.operation.success',
            }),
          });
          this.getInfo();
          this.formRef.current.resetFields();
        } else {
          notification.error({
            message: msg,
          });
        }
        this.setState({
          saveLoading: false,
        });
      },
    });
  };
  /**
   * 返回
   */
  cancelCustomerInformation = () => {
    window.history.back();
  };

  render() {
    const { languageList } = this.state;
    return (
      <div className={styles.personalContent}>
        <div className={styles.personalLeft}>
          <div className={styles.personalLeftTop}>
            <div className={styles.personalLeftTopTitle}>
              <FormattedMessage id="home.preferences" />
            </div>
          </div>
          <Spin spinning={this.state.initLoading}>
            <div className={styles.personalLeftCenter}>
              <Form
                name="passwordForm"
                ref={this.formRef}
                labelCol={{
                  span: 5,
                }}
                wrapperCol={{
                  span: 14,
                }}
                labelAlign="right"
                labelWrap
                onFinish={this.onFinish}
                style={{ maxWidth: 600, marginTop: 20 }}
              >
                {/* 设置语言 */}
                <Form.Item
                  label={getIntl().formatMessage({
                    id: 'home.set.language',
                  })}
                  name="language"
                  rules={[
                    {
                      required: true,
                    },
                  ]}
                >
                  <Select
                    placeholder={getIntl().formatMessage({
                      id: 'home.set.language.select',
                      defaultValue: '请选择语言',
                    })}
                    options={languageList.map(item => ({
                      label: item.languageName,
                      value: item.languageCode,
                      key: item.languageCode,
                    }))}
                    // onChange={value =>
                    //   this.handleGenderChange(value, 'gender')
                    // }
                  />
                </Form.Item>

                {/* 设置时区 */}
                <Form.Item
                  rules={[{ required: true }]}
                  label={getIntl().formatMessage({
                    id: 'home.set.time.zone',
                  })}
                  validateTrigger="onBlur"
                  name="timezoneId"
                >
                  <Select
                    placeholder={getIntl().formatMessage({
                      id: 'home.set.time.zone.select',
                      defaultValue: '请选择时区',
                    })}
                    showSearch
                    filterOption={(inputValue, option) =>
                      option.label
                        .toLowerCase()
                        .indexOf(inputValue.toLowerCase()) >= 0
                    }
                    options={this.state.timeZoneList.map(item => ({
                      label: `${item.timeZoneName}（${item.timeZoneCode}）`,
                      value: item.timeZoneId,
                      key: item.timeZoneId,
                    }))}
                    fieldNames={{ label: 'label', value: 'value' }}
                    // onChange={value =>
                    //   this.handleGenderChange(value, 'gender')
                    // }
                  />
                </Form.Item>
                {/* 提示 */}
                <Form.Item
                  label=""
                  wrapperCol={{
                    span: 19,
                  }}
                  className={styles.formTips}
                >
                  <span
                    style={{
                      fontSize: 12,
                      color: '#999999',
                    }}
                  >
                    <FormattedMessage
                      id="personal.form.tips"
                      defaultValue="注：设置时区后，系统将按照该时区展示所有时间"
                    />
                  </span>
                </Form.Item>

                <Form.Item
                  wrapperCol={{
                    offset: 8,
                    span: 16,
                  }}
                  style={{ marginTop: 20 }}
                >
                  <Button
                    onClick={this.cancelCustomerInformation}
                    style={{ marginRight: 20 }}
                  >
                    <FormattedMessage
                      id="customerInformation.add.basicInformation.button.return"
                      defaultValue="返回"
                    />
                  </Button>
                  <HOCAuth authKey={'save_preference_settings'}>
                    {authAccess => (
                      <Button
                        type="primary"
                        htmlType="submit"
                        loading={this.state.saveLoading}
                        disabled={authAccess}
                      >
                        <FormattedMessage
                          id="customerInformation.add.basicInformation.button.save"
                          defaultValue="保存"
                        />
                      </Button>
                    )}
                  </HOCAuth>
                </Form.Item>
              </Form>
            </div>
          </Spin>
        </div>
      </div>
    );
  }
}

const mapStateToProps = ({ preferences }) => {
  return {
    ...preferences,
  };
};

export default connect(mapStateToProps)(preferences);
