import Header from '@/components/Header';
import Footer from '@/components/Footer';
import TocCardBg from '@/components/TocCardBg';
import '../index.less';
import { Link, FormattedMessage, getLocale, useIntl } from 'umi';
import styles from '../index.less';
import currentStyle from './index.less';
import MotionFade from '@/components/MotionFade';
import { motion } from 'framer-motion';
import { useEffect } from 'react';
import Banner from '../components/banner';
import { isCNDomain } from '@/utils/utils';
const Finance = () => {
  const locale = getLocale();
  const intl = useIntl();
  useEffect(() => {
    console.log(locale, intl);
  }, [locale, intl]);
  const renderCard1 = () => (
    <div className={currentStyle.financeCard1}>
      <MotionFade
        as={motion.img}
        type="fadeRight"
        src={require('@/assets/solution/finance1.png')}
        alt="finance1"
        scale={0.8}
      />
      <MotionFade
        as={motion.img}
        type="fadeUp"
        src={require('@/assets/solution/finance11.png')}
        alt="finance11"
      />
    </div>
  );

  const renderCard2 = () => (
    <div className={currentStyle.financeCard2}>
      <MotionFade
        as={motion.img}
        type="fadeLeft"
        src={require('@/assets/solution/finance2.png')}
        alt="finance2"
        scale={0.8}
      />
      <MotionFade
        as={motion.img}
        type="fadeLeft"
        delay={0.15}
        src={require('@/assets/solution/finance21.png')}
        alt="finance21"
      />
    </div>
  );

  return (
    <div className={styles.solutionPage}>
      <Header />
      <div
        className={styles.solutionPageBanner}
        style={{ backgroundColor: '#edf1fe' }}
      >
        <Banner
          titleFirstId="solution-finance-banner-title"
          subtitleId="solution-finance-banner-subtitle"
          image={require('@/assets/solution/financeBg.png')}
          videoSrc={
            isCNDomain()
              ? `https://connectnowai.com/video/finance_cn.mp4`
              : `https://connectnowai.com/video/finance.mp4`
          }
        />
      </div>
      <TocCardBg
        titleId="solution-finance-title-1"
        descId="solution-finance-desc-1"
        children={renderCard1()}
      />
      <TocCardBg
        titleId="solution-finance-title-2"
        descId="solution-finance-desc-2"
        bgColor="#edf1fe"
        children={renderCard2()}
      />
      <TocCardBg
        titleId="solution-finance-title-3"
        descId="solution-finance-desc-3"
        children={
          <MotionFade
            as={motion.img}
            type="fadeRight"
            style={{ width: '100%' }}
            src={require('@/assets/solution/finance3.png')}
            alt="finance3"
          />
        }
      />
      <TocCardBg
        titleId="solution-finance-title-4"
        descId="solution-finance-desc-4"
        bgColor="#edf1fe"
        children={
          <MotionFade
            as={motion.img}
            type="fadeLeft"
            style={{ width: '100%' }}
            src={require('@/assets/solution/finance4.png')}
            alt="finance4"
          />
        }
      />
      <TocCardBg
        titleId="solution-finance-title-5"
        descId="solution-finance-desc-5"
        children={
          <MotionFade
            as={motion.img}
            type="fadeRight"
            style={{ width: '100%' }}
            src={require('@/assets/solution/finance5.png')}
            alt="finance5"
          />
        }
      />
      <Footer />
    </div>
  );
};

export default Finance;
