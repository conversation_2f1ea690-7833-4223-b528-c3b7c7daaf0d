.siteRoot {
  background-image: url('../../assets/site.png');
  width: 100%;
  height: 100%;
  position: relative;
  background-size: 100% 100%;
  .languageIcon {
    position: fixed;
    right: 0;
    top: 0;
    float: right;
    margin-top: 16px;
    height: 30px;
    cursor: pointer;
    .langContent {
      float: left;
      margin-right: 30px;

      img {
        width: 18px;
      }

      span {
        color: #fff;
        text-align: center;
        font-size: 12px;
        margin-left: 5px;
      }
    }
  }
  .siteContent {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    // width: 800px;
    // height: 500px;
    border-radius: 20px;
    background: #fff;
    box-shadow: 2px 2px 48px 0px rgba(5, 15, 50, 0.4);
    padding: 40px 40px 20px 40px;
    .choseArea {
      display: flex;
      align-items: center;
      justify-self: center;
      margin-top: 30px;
      gap: 40px;
      margin-bottom: 30px;
      .itemTab {
        padding: 19px 43px;
        border-radius: 4px;
        border: 1px solid #e6e6e6;
        background: #fff;
        word-break: break-word;
        white-space: nowrap;
        flex-grow: 1;
        text-align: center;
        span {
          color: #333;
          font-size: 16px;
          font-style: normal;
          font-weight: 700;
        }
        &:hover {
          border: 1px solid #3463fc;
        }
        &.checked {
          border: 1px solid #3463fc;
          background: rgba(52, 99, 252, 0.1);
        }
      }
    }
    .choseAreaImg {
      margin-bottom: 30px;
      // width: 711px;
      // height: 360px;
    }
    .footerBtn {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 4px;

      .operation {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }
    }
  }
}
