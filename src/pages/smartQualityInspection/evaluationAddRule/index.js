import { useState, useRef, useEffect } from 'react';
import {
  FormattedMessage,
  getIntl,
  history,
  useDispatch,
  useSelector,
  useLocation,
} from 'umi';
import styles from './index.less';
import {
  Spin,
  Form,
  Input,
  Select,
  InputNumber,
  Radio,
  Row,
  Col,
  Button,
  Typography,
  Table,
  message,
  TreeSelect,
  notification,
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import NewDeleteIcon from '../../../assets/new-delete-list-icon.png';
import DeleteReplyIcon from '@/assets/email-template-delete.png';
import EditorReplyIcon from '@/assets/email-template-editor.png';
import SaveReplyIcon from '@/assets/save-editor-content.png';
import CancelSaveReplyIcon from '@/assets/cancel-editor-content.png';
const { TextArea } = Input;
const { Option } = Select;
const AIGC_RULE_STYLE = {
  display: 'flex',
  flexDirection: 'column',
  gap: 8,
};
const EvaluationAddRule = () => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [scoreType, setScoreType] = useState('ai');
  const [checkPoints, setCheckPoints] = useState([{ id: 1, value: '' }]);
  // AIGC规则相关状态
  const [aigcRule2Condition, setAigcRule2Condition] = useState('gt'); // 大于、等于、小于
  const [aigcRuleType, setAigcRuleType] = useState('rule1'); // AIGC规则类型：rule1-出现任意一个质检点，rule2-出现多个质检点
  // 人工评分规则相关状态
  const [manualRules, setManualRules] = useState([]);
  const formRef = useRef(null);
  const { categoryList, evaluationFormData, isEditMode } = useSelector(
    state => state.smartQualityInspection,
  );
  const location = useLocation();
  const {
    selectedNode = {},
    ruleId,
    viewOnly = false,
    selectedVersionId = evaluationFormData.versionId || null,
  } = location.state || {};
  useEffect(() => {
    getLatestCategoryList();

    // 如果有ruleId，说明是编辑模式，需要获取规则详情
    console.log(ruleId, 'ruleId');
    if (ruleId) {
      getRuleDetail(ruleId);
    } else {
      console.log(selectedNode, 'selectedNode');
      formRef.current?.setFieldsValue({
        categoryId: selectedNode.categoryId,
      });
    }
  }, []);

  // 获取规则详情
  const getRuleDetail = ruleId => {
    setLoading(true);
    console.log(ruleId, 'ruleId');
    dispatch({
      type: 'smartQualityInspection/getRuleDetail',
      payload: { ruleId },
      callback: res => {
        if (res.code == 200) {
          const ruleData = res.data;
          // 设置基本信息回显
          formRef.current?.setFieldsValue({
            ruleName: ruleData.ruleName,
            description: ruleData.description,
            categoryId: ruleData.categoryId,
            totalScore: ruleData.score,
            reference: ruleData.reference,
            scoreType: ruleData.scoreType === 1 ? 'ai' : 'manual',
          });

          // 设置评分类型
          setScoreType(ruleData.scoreType === 1 ? 'ai' : 'manual');

          // 如果是AI评分方式
          if (ruleData.scoreType === 1) {
            try {
              // 解析AIGC规则JSON
              const aigcRule = JSON.parse(ruleData.scoreRule || '{}');

              // 设置AIGC规则类型
              setAigcRuleType(aigcRule.type === '1' ? 'rule1' : 'rule2');

              // 设置规则细节
              if (aigcRule.type === '1') {
                // 规则1: 任意出现
                formRef.current?.setFieldsValue({
                  aigcRuleType: 'rule1',
                  aigcRule1Score: aigcRule.deduct_per_time,
                  aigcRule1MaxScore: aigcRule.max_deduct,
                });
              } else {
                // 规则2: 多个出现
                const conditionMap = {
                  '>': 'gt',
                  '=': 'eq',
                  '<': 'lt',
                };
                setAigcRule2Condition(conditionMap[aigcRule.compare] || 'gt');
                formRef.current?.setFieldsValue({
                  aigcRuleType: 'rule2',
                  aigcRule2Condition: conditionMap[aigcRule.compare] || 'gt',
                  aigcRule2Threshold: aigcRule.times,
                  aigcRule2DeductScore: aigcRule.deduct,
                });
              }

              // 设置质检点
              if (ruleData.rulePointList && ruleData.rulePointList.length > 0) {
                const points = ruleData.rulePointList.map((point, index) => ({
                  id: index + 1,
                  value: point.pointDesc,
                }));
                setCheckPoints(points);

                // 设置质检点到表单
                points.forEach((point, index) => {
                  formRef.current?.setFieldsValue({
                    ['checkPoints']: points.map(p => p.value),
                  });
                });
              }
            } catch (error) {
              console.error('解析AIGC规则出错:', error);
            }
          } else {
            // 人工评分方式
            if (ruleData.rulePointList && ruleData.rulePointList.length > 0) {
              const rules = ruleData.rulePointList.map((point, index) => ({
                id: index + 1,
                pointDesc: point.pointDesc,
                score: point.score,
                isEditing: false,
              }));
              setManualRules(rules);

              // 设置人工评分规则到表单
              rules.forEach((rule, index) => {
                formRef.current?.setFieldsValue({
                  ['manualRules']: rules.map(r => ({
                    pointDesc: r.pointDesc,
                    score: r.score,
                  })),
                });
              });
            }
          }
        }
        setLoading(false);
      },
    });
  };

  const getLatestCategoryList = async () => {
    setLoading(true);
    await dispatch({
      type: 'smartQualityInspection/getCategoryTreeStructureByAssessmentId',
      payload: {
        assessmentId: evaluationFormData.assessmentId,
        versionId: selectedVersionId,
      },
      callback: res => {
        if (res.code == 200) {
          // 将分类列表添加到store中
          dispatch({
            type: 'smartQualityInspection/setCategoryList',
            payload: res.data,
          });
        }
      },
    });
    setLoading(false);
  };

  const handleCheck = async values => {
    return new Promise(async (resolve, reject) => {
      if (values.totalScore > evaluationFormData.totalScore) {
        message.warning(
          getIntl().formatMessage({
            id:
              evaluationFormData.scoreMechanism === 'add'
                ? 'smart.quality.evaluation.rule.total.score.exceed.error.add'
                : 'smart.quality.evaluation.rule.total.score.exceed.error.subtract',
          }),
        );
        reject();
        return;
      }
      if (aigcRuleType === 'rule1') {
        if (values.aigcRule1MaxScore > values.totalScore) {
          reject(
            getIntl().formatMessage({
              id:
                evaluationFormData.scoreMechanism === 'add'
                  ? 'smart.quality.evaluation.rule.aigc.rule1.score.exceed.error.add'
                  : 'smart.quality.evaluation.rule.aigc.rule1.score.exceed.error.subtract',
            }),
          );
          return;
        }
      } else {
        if (values.aigcRule2DeductScore > values.totalScore) {
          reject(
            getIntl().formatMessage({
              id:
                evaluationFormData.scoreMechanism === 'add'
                  ? 'smart.quality.evaluation.rule.aigc.rule2.score.exceed.error.add'
                  : 'smart.quality.evaluation.rule.aigc.rule2.score.exceed.error.subtract',
            }),
          );
          return;
        }
      }
      // 如果是人工评分 最少要有一条评分规则
      if (values.scoreType === 'manual' && manualRules.length === 0) {
        reject(
          getIntl().formatMessage({
            id: 'smart.quality.evaluation.rule.manual.option.required',
          }),
        );
        return;
      }
      // 人工评分 如果规则正在编辑状态 不能提交
      if (
        values.scoreType === 'manual' &&
        manualRules.some(rule => rule.isEditing)
      ) {
        reject(
          getIntl().formatMessage({
            id: 'smart.quality.evaluation.rule.manual.option.save.first',
          }),
        );
        return;
      }
      resolve();
    });
  };
  const onFinish = async values => {
    setLoading(true);
    try {
      // 将判断的都放在一个方法中 异步
      await handleCheck(values);
      values.scoreType = values.scoreType === 'ai' ? 1 : 2;
      values.score = values.totalScore;
      if (values.scoreType === 1) {
        values.rulePointList =
          values.checkPoints?.map(item => ({ pointDesc: item })) ?? [];
      } else {
        values.rulePointList = manualRules.map(item => ({
          pointDesc: item.pointDesc,
          score: item.score,
        }));
      }
      if (ruleId) {
        values.ruleId = ruleId;
      }
      // 构建AIGC得分规则JSON
      if (values.scoreType === 1) {
        // AIGC评分规则 数据处理 后端需要的是json格式
        const aigcRuleJSON = {
          type: aigcRuleType === 'rule1' ? '1' : '2', // 1-任意 2-多个
          deduct_per_time: values.aigcRule1Score, // 每出现一次扣分
          max_deduct: values.aigcRule1MaxScore, // 累计最多扣分
          compare:
            values.aigcRule2Condition === 'gt'
              ? '>'
              : values.aigcRule2Condition === 'eq'
              ? '='
              : '<', // >,<,=
          times: values.aigcRule2Threshold, // 出现次数
          deduct: values.aigcRule2DeductScore, // 扣分总数
        };
        values.scoreRule = JSON.stringify(aigcRuleJSON);
      }
      values.assessmentId = evaluationFormData.assessmentId;
      dispatch({
        type: 'smartQualityInspection/saveRule',
        payload: values,
        callback: res => {
          // 关闭loading状态
          setLoading(false);
          if (res.code == 200) {
            history.goBack();
          }
        },
      });
    } catch (error) {
      console.log(error);
      notification.error({
        message: error,
      });
      // 错误时也关闭loading状态
      setLoading(false);
    }
  };

  const handleCancel = () => {
    // 返回上一页
    history.goBack();
  };

  // 添加质检点
  const addCheckPoint = () => {
    if (viewOnly || checkPoints.length >= 15) {
      return;
    }
    const newId =
      checkPoints.length > 0
        ? Math.max(...checkPoints.map(cp => cp.id)) + 1
        : 1;
    setCheckPoints([...checkPoints, { id: newId, value: '' }]);
  };

  // 删除质检点
  const removeCheckPoint = index => {
    if (viewOnly) return;
    let list = checkPoints.filter((cp, i) => i !== index);
    setCheckPoints([...list]);
    formRef.current?.setFieldsValue({
      checkPoints: list.map(point => point.value),
    });
  };

  // 用来同步表单中的checkPoints字段
  const handleCheckPointChange = (e, index) => {
    const newCheckPoints = [...checkPoints];
    newCheckPoints[index].value = e.target.value;
    setCheckPoints(newCheckPoints);
  };

  // 渲染AI评估方法的内容
  const renderAIEvaluationContent = () => {
    return (
      <>
        {/* 1. 规则总分 */}
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              label={
                <FormattedMessage id="smart.quality.evaluation.rule.total.score" />
              }
              name="totalScore"
              rules={[
                {
                  required: true,
                  message: (
                    <FormattedMessage id="smart.quality.evaluation.rule.total.score.required" />
                  ),
                },
              ]}
            >
              <InputNumber
                min={1}
                placeholder={getIntl().formatMessage({
                  id: 'smart.quality.evaluation.rule.total.score.placeholder',
                })}
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>
        </Row>

        {/* 2. 质检点 */}
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              label={
                <FormattedMessage id="smart.quality.evaluation.rule.check.points" />
              }
            >
              {checkPoints.map((cp, index) => (
                <div
                  key={cp.id}
                  style={{
                    display: 'flex',
                    marginBottom: '8px',
                    position: 'relative',
                  }}
                >
                  <Form.Item
                    name={['checkPoints', index]}
                    style={{ flex: 1, marginBottom: 0 }}
                    rules={[
                      {
                        required: true,
                        message: (
                          <FormattedMessage id="smart.quality.evaluation.rule.check.point.required" />
                        ),
                      },
                    ]}
                  >
                    <Input
                      placeholder={getIntl().formatMessage({
                        id:
                          'smart.quality.evaluation.rule.check.point.placeholder',
                      })}
                      onChange={e => handleCheckPointChange(e, index)}
                    />
                  </Form.Item>
                  {index > 0 && (
                    <img
                      src={NewDeleteIcon}
                      alt="delete"
                      onClick={() => removeCheckPoint(index)}
                      style={{
                        position: 'absolute',
                        right: '0',
                        transform: 'translateX(120%) translateY(50%)',
                        width: '16px',
                        height: '16px',
                        cursor: 'pointer',
                      }}
                    />
                  )}
                </div>
              ))}
              {!viewOnly && checkPoints.length < 15 && (
                <Button
                  type="link"
                  onClick={addCheckPoint}
                  icon={<PlusOutlined />}
                >
                  <FormattedMessage id="smart.quality.evaluation.rule.add.check.point" />
                </Button>
              )}
              {checkPoints.length >= 15 && (
                <Typography.Text type="secondary">
                  <FormattedMessage id="smart.quality.evaluation.rule.check.points.max" />
                </Typography.Text>
              )}
            </Form.Item>
          </Col>
        </Row>

        {/* 3. AIGC得分规则 */}
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="aigcRuleType"
              label={
                <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rules" />
              }
              required
              rules={[
                {
                  required: true,
                  message: (
                    <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rules.required" />
                  ),
                },
              ]}
            >
              <Radio.Group
                onChange={e => setAigcRuleType(e.target.value)}
                style={AIGC_RULE_STYLE}
                value={aigcRuleType}
              >
                <Radio value="rule1">
                  <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rule1.title" />
                  <span className={styles.aigcRule}>
                    {aigcRuleType === 'rule1' && (
                      <>
                        <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rule1.prefix" />
                        <span>
                          <b className={styles.scoreSign}>
                            {evaluationFormData.scoreMechanism == 'add' ? (
                              <FormattedMessage id="smart.quality.evaluation.rule.add.score.add" />
                            ) : (
                              <FormattedMessage id="smart.quality.evaluation.rule.add.score.subtract" />
                            )}
                          </b>
                        </span>
                        <Form.Item
                          name="aigcRule1Score"
                          rules={[
                            {
                              required: true,
                              message: (
                                <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rule1.score.required" />
                              ),
                            },
                          ]}
                        >
                          <InputNumber
                            min={0}
                            style={{ width: 80, margin: '0 8px' }}
                          />
                        </Form.Item>

                        <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rule1.middle" />
                        <span>
                          <b className={styles.scoreSign}>
                            {evaluationFormData.scoreMechanism == 'add' ? (
                              <FormattedMessage id="smart.quality.evaluation.rule.add.score.add" />
                            ) : (
                              <FormattedMessage id="smart.quality.evaluation.rule.add.score.subtract" />
                            )}
                          </b>
                        </span>
                        <Form.Item
                          name="aigcRule1MaxScore"
                          rules={[
                            {
                              required: true,
                              message: (
                                <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rule1.max.score.required" />
                              ),
                            },
                          ]}
                        >
                          <InputNumber
                            min={0}
                            style={{ width: 80, margin: '0 8px' }}
                          />
                        </Form.Item>
                        <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rule1.suffix" />
                      </>
                    )}
                  </span>
                </Radio>
                <Radio value="rule2">
                  <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rule2.title" />
                  <span
                    onClick={e => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                    className={styles.aigcRule}
                  >
                    {aigcRuleType === 'rule2' && (
                      <>
                        <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rule2.prefix" />
                        <Form.Item
                          name="aigcRule2Condition"
                          rules={[
                            {
                              required: true,
                              message: (
                                <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rule2.condition.required" />
                              ),
                            },
                          ]}
                        >
                          <Select
                            style={{ width: 100, margin: '0 8px' }}
                            value={aigcRule2Condition}
                            onChange={value => setAigcRule2Condition(value)}
                          >
                            <Option value="gt">
                              <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rule2.condition.gt" />
                            </Option>
                            <Option value="eq">
                              <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rule2.condition.eq" />
                            </Option>
                            <Option value="lt">
                              <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rule2.condition.lt" />
                            </Option>
                          </Select>
                        </Form.Item>
                        <Form.Item
                          name="aigcRule2Threshold"
                          rules={[
                            {
                              required: true,
                              message: (
                                <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rule2.threshold.required" />
                              ),
                            },
                          ]}
                        >
                          <InputNumber
                            min={0}
                            style={{ width: 80, margin: '0 8px' }}
                          />
                        </Form.Item>
                        <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rule2.middle" />
                        <span>
                          <b className={styles.scoreSign}>
                            {evaluationFormData.scoreMechanism == 'add' ? (
                              <FormattedMessage id="smart.quality.evaluation.rule.add.score.add" />
                            ) : (
                              <FormattedMessage id="smart.quality.evaluation.rule.add.score.subtract" />
                            )}
                          </b>
                        </span>
                        <Form.Item
                          name="aigcRule2DeductScore"
                          rules={[
                            {
                              required: true,
                              message: (
                                <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rule2.score.required" />
                              ),
                            },
                          ]}
                        >
                          <InputNumber
                            min={0}
                            style={{ width: 80, margin: '0 8px' }}
                          />
                        </Form.Item>
                        <FormattedMessage id="smart.quality.evaluation.add.rule.aigc.rule2.suffix" />
                      </>
                    )}
                  </span>
                </Radio>
              </Radio.Group>
            </Form.Item>
          </Col>
        </Row>
      </>
    );
  };

  // 渲染人工评分方法的内容
  const renderManualEvaluationContent = () => {
    // 添加新的评分规则选项
    const addManualRule = () => {
      if (viewOnly) return;
      const newId =
        manualRules.length > 0
          ? Math.max(...manualRules.map(rule => rule.id)) + 1
          : 1;
      const updatedRules = [
        ...manualRules,
        { id: newId, pointDesc: '', score: 0, isEditing: true },
      ];
      setManualRules(updatedRules);

      // 添加后更新表单字段，确保表单数据与manualRules数组同步
      formRef.current?.setFieldsValue({
        manualRules: updatedRules.map(rule => ({
          pointDesc: rule.pointDesc,
          score: rule.score,
        })),
      });
    };

    // 删除评分规则选项
    const deleteManualRule = id => {
      if (viewOnly) return;
      const updatedRules = manualRules.filter(rule => rule.id !== id);
      setManualRules(updatedRules);

      // 删除后更新表单字段，确保表单数据与manualRules数组同步
      formRef.current?.setFieldsValue({
        manualRules: updatedRules.map(rule => ({
          pointDesc: rule.pointDesc,
          score: rule.score,
        })),
      });
    };

    // 切换编辑状态
    const toggleEditMode = async (id, type) => {
      if (viewOnly) return;
      if (type === 'edit') {
        try {
          // 获取当前编辑行的索引
          const currentIndex = manualRules.findIndex(rule => rule.id === id);
          // 校验特定字段
          await formRef.current?.validateFields([
            ['manualRules', currentIndex, 'pointDesc'],
          ]);

          // 获取当前编辑的规则名称
          const currentPointDesc = formRef.current?.getFieldValue([
            'manualRules',
            currentIndex,
            'pointDesc',
          ]);

          // 检查是否与其他规则重复
          const isDuplicate = manualRules.some(
            (rule, idx) =>
              idx !== currentIndex && rule.pointDesc === currentPointDesc,
          );

          if (isDuplicate) {
            message.error(
              getIntl().formatMessage({
                id:
                  'smart.quality.evaluation.add.rule.manual.option.name.duplicate',
                defaultMessage: '规则名称不能重复',
              }),
            );
            return;
          }

          setManualRules(
            manualRules.map(rule =>
              rule.id === id ? { ...rule, isEditing: !rule.isEditing } : rule,
            ),
          );
        } catch (error) {
          // 校验失败，不切换编辑状态
          return;
        }
      } else {
        setManualRules(
          manualRules.map(rule =>
            rule.id === id ? { ...rule, isEditing: !rule.isEditing } : rule,
          ),
        );
      }
    };

    // 取消编辑
    const cancelEdit = (id, originalRule) => {
      setManualRules(
        manualRules.map(rule =>
          rule.id === id ? { ...originalRule, isEditing: false } : rule,
        ),
      );
    };

    // 更新规则名称
    const updateRulePointDesc = (id, pointDesc) => {
      setManualRules(
        manualRules.map(rule =>
          rule.id === id ? { ...rule, pointDesc } : rule,
        ),
      );
    };

    // 更新规则得分
    const updateRuleScore = (id, score) => {
      setManualRules(
        manualRules.map(rule => (rule.id === id ? { ...rule, score } : rule)),
      );
    };

    // 定义表格列配置
    const columns = [
      {
        title: (
          <FormattedMessage id="smart.quality.evaluation.add.rule.manual.option.name" />
        ),
        dataIndex: 'pointDesc',
        key: 'pointDesc',
        width: '50%',
        render: (text, record, index) => {
          return record.isEditing ? (
            <Form.Item
              name={['manualRules', index, 'pointDesc']}
              style={{ margin: 0 }}
              rules={[
                {
                  required: true,
                  message: getIntl().formatMessage({
                    id:
                      'smart.quality.evaluation.add.rule.manual.option.name.required',
                  }),
                },
                {
                  validator: (_, value) => {
                    if (!value) return Promise.resolve();

                    // 检查是否有重复的规则名称
                    const isDuplicate = manualRules.some(
                      (rule, idx) => idx !== index && rule.pointDesc === value,
                    );

                    if (isDuplicate) {
                      return Promise.reject(
                        getIntl().formatMessage({
                          id:
                            'smart.quality.evaluation.add.rule.manual.option.name.duplicate',
                          defaultMessage: '规则名称不能重复',
                        }),
                      );
                    }

                    return Promise.resolve();
                  },
                },
              ]}
            >
              <Input
                value={record.pointDesc}
                onChange={e => updateRulePointDesc(record.id, e.target.value)}
                placeholder={getIntl().formatMessage({
                  id:
                    'smart.quality.evaluation.add.rule.manual.option.name.placeholder',
                })}
              />
            </Form.Item>
          ) : (
            record.pointDesc || ''
          );
        },
      },
      {
        title: (
          <FormattedMessage id="smart.quality.evaluation.add.rule.manual.option.score" />
        ),
        dataIndex: 'score',
        key: 'score',
        width: '30%',
        render: (text, record, index) => {
          return record.isEditing ? (
            <Form.Item
              name={['manualRules', index, 'score']}
              style={{ margin: 0 }}
              rules={[
                {
                  required: true,
                  message: getIntl().formatMessage({
                    id:
                      'smart.quality.evaluation.add.rule.manual.option.score.required',
                  }),
                },
              ]}
            >
              <InputNumber
                min={0}
                value={record.score}
                onChange={value => updateRuleScore(record.id, value)}
                style={{ width: '100%' }}
              />
            </Form.Item>
          ) : (
            <span>
              <b className={styles.scoreSign}>
                {evaluationFormData.scoreMechanism == 'add' ? (
                  <FormattedMessage id="smart.quality.evaluation.rule.add.score.add" />
                ) : (
                  <FormattedMessage id="smart.quality.evaluation.rule.add.score.subtract" />
                )}
              </b>
              {record.score}
            </span>
          );
        },
      },
      {
        title: (
          <FormattedMessage id="smart.quality.evaluation.add.rule.manual.option.operation" />
        ),
        key: 'action',
        width: '20%',
        align: 'center',
        render: (text, record) => {
          // 在只读模式下不显示任何操作按钮
          if (viewOnly) return null;

          return record.isEditing ? (
            <>
              <img
                src={SaveReplyIcon}
                onClick={() => toggleEditMode(record.id, 'edit')}
                style={{
                  cursor: 'pointer',
                  marginRight: '8px',
                  width: '16px',
                  height: '16px',
                }}
              />
              <img
                src={CancelSaveReplyIcon}
                onClick={() => {
                  const originalRule = manualRules.find(
                    r => r.id === record.id,
                  );
                  if (
                    originalRule.pointDesc === '' &&
                    originalRule.score === 0
                  ) {
                    deleteManualRule(record.id);
                  } else {
                    cancelEdit(record.id, originalRule);
                  }
                }}
                style={{ cursor: 'pointer', width: '16px', height: '16px' }}
              />
            </>
          ) : (
            <>
              <img
                src={EditorReplyIcon}
                onClick={() => toggleEditMode(record.id)}
                style={{
                  cursor: 'pointer',
                  marginRight: '8px',
                  width: '16px',
                  height: '16px',
                }}
              />
              <img
                src={DeleteReplyIcon}
                onClick={() => deleteManualRule(record.id)}
                style={{ cursor: 'pointer', width: '16px', height: '16px' }}
              />
            </>
          );
        },
      },
    ];

    return (
      <>
        {/* 1. 人工评分规则表格 */}
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              label={
                <FormattedMessage id="smart.quality.evaluation.add.rule.manual.rules" />
              }
              required
            >
              <div className={styles.manualRuleTable}>
                <Table
                  columns={columns}
                  dataSource={manualRules}
                  rowKey="id"
                  pagination={false}
                  size="middle"
                />
                {!viewOnly && (
                  <Button
                    type="link"
                    onClick={addManualRule}
                    style={{ marginTop: '16px' }}
                    icon={<PlusOutlined />}
                  >
                    <FormattedMessage id="smart.quality.evaluation.add.rule.manual.add.option" />
                  </Button>
                )}
              </div>
            </Form.Item>
          </Col>
        </Row>

        {/* 2. 评估标准参考 */}
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              label={
                <FormattedMessage id="smart.quality.evaluation.add.rule.manual.standard" />
              }
              name="reference"
              rules={[
                {
                  required: true,
                  message: (
                    <FormattedMessage id="smart.quality.evaluation.add.rule.manual.standard.required" />
                  ),
                },
              ]}
            >
              <TextArea
                rows={4}
                placeholder={getIntl().formatMessage({
                  id:
                    'smart.quality.evaluation.add.rule.manual.standard.placeholder',
                })}
              />
            </Form.Item>
          </Col>
        </Row>
      </>
    );
  };

  return (
    <div className={styles.evaluationAddRuleContainer} id="evaluationAddRule">
      <Spin spinning={loading}>
        <p className="blueBorder">
          <FormattedMessage
            id={
              viewOnly
                ? 'smart.quality.evaluation.rule.detail.title'
                : ruleId
                ? 'smart.quality.evaluation.rule.edit.title'
                : 'smart.quality.evaluation.rule.add.title'
            }
            defaultMessage={
              viewOnly ? '规则详情' : ruleId ? '编辑规则' : '新增规则'
            }
          />
        </p>
        <div className={styles.addContainer}>
          <div className={styles.addDetailContainer}>
            <div className={styles.line}></div>
            <div className={styles.categoryList}>
              <Form
                name="ruleForm"
                layout="vertical"
                ref={formRef}
                onFinish={onFinish}
                autoComplete="off"
                disabled={viewOnly}
                initialValues={{
                  scoreType,
                  aigcRule1Action: 'appear',
                  aigcRule2Condition: 'gt',
                  aigcRuleType: 'rule1',
                }}
              >
                <div className={styles.secondTitle}>
                  <div className={styles.circleContainer}></div>
                  <FormattedMessage id="smart.quality.evaluation.rule.add.baseinfo" />
                </div>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      label={
                        <FormattedMessage id="smart.quality.evaluation.rule.category" />
                      }
                      name="categoryId"
                      rules={[
                        {
                          required: true,
                          message: (
                            <FormattedMessage id="smart.quality.evaluation.rule.category.required" />
                          ),
                        },
                      ]}
                    >
                      <TreeSelect
                        showSearch
                        style={{ width: '100%' }}
                        fieldNames={{
                          label: 'categoryName',
                          value: 'categoryId',
                        }}
                        styles={{
                          popup: { root: { maxHeight: 400, overflow: 'auto' } },
                        }}
                        allowClear
                        treeDefaultExpandAll
                        treeData={categoryList}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      label={
                        <FormattedMessage id="smart.quality.evaluation.rule.name" />
                      }
                      name="ruleName"
                      rules={[
                        {
                          required: true,
                          message: (
                            <FormattedMessage id="smart.quality.evaluation.rule.name.required" />
                          ),
                        },
                        {
                          max: 80,
                          message: (
                            <FormattedMessage id="smart.quality.evaluation.rule.name.max" />
                          ),
                        },
                      ]}
                    >
                      <Input
                        maxLength={80}
                        placeholder={getIntl().formatMessage({
                          id: 'smart.quality.evaluation.rule.name.placeholder',
                        })}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      label={
                        <FormattedMessage id="smart.quality.evaluation.rule.description" />
                      }
                      name="description"
                      rules={[
                        {
                          required: true,
                          message: (
                            <FormattedMessage id="smart.quality.evaluation.rule.description.required" />
                          ),
                        },
                        {
                          max: 200,
                          message: (
                            <FormattedMessage id="smart.quality.evaluation.rule.description.max" />
                          ),
                        },
                      ]}
                    >
                      <TextArea
                        rows={4}
                        maxLength={200}
                        placeholder={getIntl().formatMessage({
                          id:
                            'smart.quality.evaluation.rule.description.placeholder',
                        })}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      label={
                        <FormattedMessage id="smart.quality.evaluation.rule.evaluation.method" />
                      }
                      name="scoreType"
                    >
                      <Radio.Group onChange={e => setScoreType(e.target.value)}>
                        <Radio value="ai">
                          <FormattedMessage id="smart.quality.evaluation.rule.evaluation.method.ai" />
                        </Radio>
                        <Radio value="manual">
                          <FormattedMessage id="smart.quality.evaluation.rule.evaluation.method.manual" />
                        </Radio>
                      </Radio.Group>
                    </Form.Item>
                  </Col>
                </Row>

                <div className={styles.secondTitle}>
                  <div className={styles.circleContainer}></div>
                  {scoreType === 'ai' ? (
                    <FormattedMessage id="smart.quality.evaluation.rule.ai.settings" />
                  ) : (
                    <FormattedMessage id="smart.quality.evaluation.rule.manual.settings" />
                  )}
                </div>

                {scoreType === 'ai'
                  ? renderAIEvaluationContent()
                  : renderManualEvaluationContent()}

                <Row>
                  <Col span={24}>
                    <Form.Item className={styles.formButtons}>
                      <Button
                        style={{ marginRight: 16 }}
                        onClick={handleCancel}
                        disabled={false}
                      >
                        <FormattedMessage id="smart.quality.evaluation.add.rule.button.return" />
                      </Button>
                      {!viewOnly && (
                        <Button type="primary" htmlType="submit">
                          <FormattedMessage id="smart.quality.evaluation.add.rule.button.save" />
                        </Button>
                      )}
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
            </div>
          </div>
        </div>
      </Spin>
    </div>
  );
};

export default EvaluationAddRule;
