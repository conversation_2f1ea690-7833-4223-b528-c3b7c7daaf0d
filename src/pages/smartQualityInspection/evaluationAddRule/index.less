.evaluationAddRuleContainer {
  margin: 20px;
  min-height: 88vh;
  border-radius: 4px;
  background: #fff;
  box-shadow: 0px 2px 30px 0px rgba(0, 0, 0, 0.02);
  padding: 20px;

  :global {
    .ant-form-item {
      margin-bottom: 10px;
    }

    .ant-col-1 {
      padding-left: 0px !important;
    }

    .ant-input {
      font-size: 12px;
      height: 32px;
      box-shadow: none;
      border-radius: 6px;
      background: #fff;
    }

    .ant-select {
      font-size: 12px;
    }

    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      height: 32px;
      box-shadow: none;
      border-radius: 6px;
      background: #fff;
      overflow: hidden;
      overflow-y: scroll;
    }

    textarea.ant-input {
      font-size: 12px;
      min-height: 80px;
    }

    .ant-input-number {
      width: 100%;
      font-size: 12px;
      height: 32px;
      box-shadow: none;
      border-radius: 6px;
      background: #fff;
    }

    .ant-input-number-handler-wrap {
      border-radius: 0 6px 6px 0;
    }

    .ant-input-number-handler-down {
      border-bottom-right-radius: 6px;
    }

    .ant-input-number-handler-up {
      border-top-right-radius: 6px;
    }

    .ant-form-item-label > label {
      color: #333;
      font-size: 12px;
      font-style: normal;
      font-weight: 700;
      line-height: 150%;
    }

    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      display: none;
    }

    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::after {
      display: inline-block;
      margin-left: 2px;
      color: #ff4d4f;
      font-size: 14px;
      font-family: SimSun, sans-serif;
      line-height: 1;
      content: '*';
    }

    .ant-radio-checked .ant-radio-inner {
      border-color: #3463fc;
    }

    .ant-radio-inner::after {
      background-color: #3463fc;
    }

    .ant-radio-wrapper {
      font-size: 12px !important;
    }
  }
}

.pageTitle {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 32px;
  color: #333;
}

.addContainer {
  width: 100%;
  height: 80vh;
  padding-left: 5px;
  overflow: hidden;
  overflow-y: scroll;

  .addDetailContainer {
    width: 100%;
    display: flex;
    float: left;

    .line {
      width: 1px;
      float: left;
      margin-top: 5px;
      background: linear-gradient(rgba(173, 48, 229, 1), rgba(173, 48, 229, 0));
    }

    .categoryList {
      flex: 1;
      margin-left: 15px;

      .secondTitle {
        position: relative;
        color: #333;
        font-size: 14px;
        font-style: normal;
        font-weight: 700;
        line-height: 150%;
        margin-bottom: 10px;
        margin-top: 20px;

        &:first-child {
          margin-top: 0;
        }

        .circleContainer {
          width: 10px;
          height: 10px;
          background: #3463fc;
          border-radius: 50%;
          position: absolute;
          left: -20.5px;
          top: 5px;
        }
      }
    }
  }
}

.formButtons {
  margin-top: 40px;
  text-align: center;
}

.checkPointItem {
  display: flex;
  align-items: center;
  margin-bottom: 8px;

  .deleteButton {
    margin-left: 8px;
  }
}

.aigcRule {
  :global {
    .ant-form-item {
      margin-bottom: 0;
      display: inline-block;
      vertical-align: baseline;
    }
  }
}

.scoreSign {
  color: #3463fc;
  font-family: 'MicrosoftYaHei';
  font-size: 12px;
  font-style: normal;
  font-weight: 700;
  margin-right: 4px;
}
:global {
  .ant-select-tree-node-selected {
    background-color: #e4cbff !important;
  }
}
