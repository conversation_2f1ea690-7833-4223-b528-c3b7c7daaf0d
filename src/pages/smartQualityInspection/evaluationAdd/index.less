.evaluationAddContainer {
  margin: 20px;
  min-height: 88vh;
  border-radius: 4px;
  background: #fff;
  box-shadow: 0px 2px 30px 0px rgba(0, 0, 0, 0.02);
  padding: 20px;

  :global {
    .ant-form-item {
      margin-bottom: 10px;
    }
    .ant-tag {
      font-size: 12px;
    }
    .ant-tag:first-child {
      margin-left: 0;
    }
    .ant-col-1 {
      padding-left: 0px !important;
    }

    .ant-input {
      font-size: 12px;
      height: 32px;
      box-shadow: none;
      border-radius: 6px;
      background: #fff;
    }

    .ant-select {
      font-size: 12px;
    }

    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      min-height: 32px;
      height: auto;
      box-shadow: none;
      border-radius: 6px;
      background: #fff;
      overflow: hidden;
      overflow-y: scroll;
    }

    textarea.ant-input {
      font-size: 12px;
    }

    .ant-input-number {
      width: 100%;
      font-size: 12px;
      height: 32px;
      box-shadow: none;
      border-radius: 6px;
      background: #fff;
    }

    .ant-input-number-handler-wrap {
      border-radius: 0 6px 6px 0;
    }

    .ant-input-number-handler-down {
      border-bottom-right-radius: 6px;
    }

    .ant-input-number-handler-up {
      border-top-right-radius: 6px;
    }

    .ant-form-item-label > label {
      color: #333;
      font-size: 12px;
      font-style: normal;
      font-weight: 700;
      line-height: 150%;
      /* 18px */
    }

    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      display: none;
    }

    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::after {
      display: inline-block;
      margin-left: 2px;
      color: #ff4d4f;
      font-size: 14px;
      font-family: SimSun, sans-serif;
      line-height: 1;
      content: '*';
    }

    .ant-radio-checked .ant-radio-inner {
      border-color: #3463fc;
    }

    .ant-radio-inner::after {
      background-color: #3463fc;
    }

    .ant-radio-wrapper {
      font-size: 12px !important;
    }

    .ant-select-multiple .ant-select-selection-item {
      border-radius: 4px;
      border: 1px solid rgba(52, 99, 252, 0.5);
      background: linear-gradient(
          0deg,
          rgba(52, 99, 252, 0.1) 0%,
          rgba(52, 99, 252, 0.1) 100%
        ),
        #fff;

      .anticon {
        color: #3463fc;
      }
    }

    .ant-select-multiple .ant-select-selection-item-content {
      color: #3463fc;
      font-size: 12px;
    }

    .ant-tooltip {
      min-width: max-content;
    }

    .ant-tooltip-inner {
      padding: 14px 17px;
      border-radius: 4px;
    }

    .ant-select-multiple {
      .ant-select-selection-item {
        border: 1px solid #3463fc;
        background: #3463fc1a;
        border-radius: 4px;
        color: #3463fc;
      }

      .ant-select-selection-item-remove {
        svg path {
          fill: #3463fc;
        }
      }

      .ant-select-selector {
        font-size: 12px !important;
        border-radius: 6px !important;
        border: 1px solid #e6e6e6 !important;
      }
    }
  }
}
.allOption {
  position: relative;
  border-bottom: 1px solid #888;
}
.pageTitle {
  font-size: 20px;
  font-weight: 700;
  margin-bottom: 32px;
  color: #333;
}

.addContainer {
  width: 100%;
  height: 80vh;
  padding-left: 5px;
  overflow: hidden;
  overflow-y: scroll;

  .addDetailContainer {
    width: 100%;
    display: flex;
    float: left;

    .line {
      width: 1px;
      float: left;
      margin-top: 5px;
      background: linear-gradient(rgba(173, 48, 229, 1), rgba(173, 48, 229, 0));
    }

    .categoryList {
      flex: 1;
      margin-left: 15px;
      img {
        height: 14px !important;
      }

      .secondTitle {
        position: relative;
        color: #333;
        font-size: 14px;
        font-style: normal;
        font-weight: 700;
        line-height: 150%;
        margin-bottom: 10px;

        &:last-child {
          margin-bottom: 0;
        }

        .circleContainer {
          width: 10px;
          height: 10px;
          background: #3463fc;
          border-radius: 50%;
          position: absolute;
          left: -20.5px;
          top: 5px;
        }
      }
    }
  }
}

.scoreMechanismTooltip {
  color: #333;
  div {
    margin-bottom: 5px;
  }
}

.formButtons {
  margin-top: 40px;
  text-align: center;
}

.stepContent {
  padding-left: 4px;
  margin-bottom: 15px;
}
