import { useState, useRef, useEffect } from 'react';
import {
  FormattedMessage,
  getIntl,
  history,
  useDispatch,
  useSelector,
} from 'umi';
import styles from './index.less';
import {
  Spin,
  Form,
  Input,
  Select,
  InputNumber,
  Radio,
  Tooltip,
  Row,
  Col,
  Button,
  Popconfirm,
  Tag,
  message,
  notification,
} from 'antd';
import { QuestionCircleOutlined } from '@ant-design/icons';
import Slider from '../../statistics/hotlineKeyIndicatorsConfig/slider';
import sliderRedIcon from '../../../assets/sliderRed.png';
import sliderBlueIcon from '../../../assets/sliderBlue.png';

import EmailIcon from '../../../assets/email.svg';
import AllchannelIcon from '../../../assets/allchannel.svg';
import FacebookIcon from '../../../assets/facebook.svg';
import WhatsAppIcon from '../../../assets/whats-app.svg';
import TwitterIcon from '../../../assets/twitter.svg';
import LineIcon from '../../../assets/line.svg';
import PhoneIcon from '../../../assets/phone.svg';
import ChatIcon from '../../../assets/chat-icon.jpg';
import AppChatOutlinedIcon from '../../../assets/AppChatOutlined.svg';
import WebVideoOutlinedIcon from '../../../assets/WebVideoOutlined.svg';
import AppVideoOutlinedIcon from '../../../assets/AppVideoOutlined.svg';
import AwsChannelIcon from '../../../assets/aws-channel-icon.svg';
import NewInstagramIcon from '../../../assets/ins.svg';
import NewLineIcon from '../../../assets/new-line-icon.svg';
import NewTwitterIcon from '../../../assets/new-twitter-icon.svg';
import NewTelegramIcon from '../../../assets/new-telegram-icon.svg';
import NewWeComIcon from '../../../assets/new-wecom-icon.svg';
import NewWeChatMiniProgramIcon from '../../../assets/new-wechat-mini-program-icon.svg';
import NewWechatOfficialAccountIcon from '../../../assets/new-wechat-official-account-icon.svg';
import NewShopifyIcon from '../../../assets/new-shopify-icon.svg';
import NewGooglePlayIcon from '../../../assets/google-play-icon.svg';
import NewWebOnlineVoiceIcon from '../../../assets/web-online-voice-icon.svg';
import NewAppOnlineVoiceIcon from '../../../assets/app-online-voice-icon.svg';
const blue = '#3463FC',
  green = '#13C825',
  red = '#F22417';

const EvaluationAdd = () => {
  const dispatch = useDispatch();
  const { OptGroup, Option } = Select;
  const [loading, setLoading] = useState(false);
  const [submitError, setSubmitError] = useState(false);
  const [workRecordTypeList, setWorkRecordTypeList] = useState([]);
  const [channelOptions, setChannelOptions] = useState([]);
  const [rate, setRate] = useState([50, 80]);
  const formRef = useRef(null);
  const [scoreUserList, setScoreUserList] = useState([]);
  const [isChannelAllSelected, setIsChannelAllSelected] = useState(false);
  const [isTicketTypeAllSelected, setIsTicketTypeAllSelected] = useState(false);

  const { isEditMode, evaluationFormData, assessmentInfo } = useSelector(
    state => state.smartQualityInspection,
  );
  const onFinish = async values => {
    setLoading(true);
    console.log(values.channel, 'values.channel');

    // 如果选择的是"所有"选项，将对应字段设置为null
    if (values.channel && values.channel.includes('all')) {
      values.channelList = null;
    } else {
      values.channelList = values.channel;
    }

    // 如果工单类型选择的是"所有"，将其设置为null
    if (values.ticketType && values.ticketType.includes('all')) {
      values.ticketType = null;
    }

    // 设置colorList格式
    let colorList = [
      {
        startNum: 0,
        endNum: rate[0],
        colorCode: red,
      },
      {
        startNum: rate[0],
        endNum: rate[1],
        colorCode: blue,
      },
      {
        startNum: rate[1],
        endNum: 100,
        colorCode: green,
      },
    ];
    values.colorList = colorList;
    try {
      if (!isEditMode) {
        const assessmentId = await handleSave(values);
        values.assessmentId = assessmentId;
      } else {
        await handleSave(values);
        // 修改的话，回填版本id和评估表id
        values.versionId = assessmentInfo?.versionId ?? null;
        values.assessmentId = assessmentInfo?.assessmentId ?? null;
      }
      dispatch({
        type: 'smartQualityInspection/setEvaluationFormData',
        payload: values,
      });
      if (isEditMode) {
        history.push({
          pathname: '/evaluationList',
        });
      } else {
        history.push({
          pathname: '/evaluationRuleList',
        });
      }
    } catch (error) {
      setLoading(false);
      message.error(error);
      console.log(error);
    }
  };
  const handleSave = values => {
    return new Promise((resolve, reject) => {
      const formInfo = {
        assessmentName: values.name,
        ticketType: values.ticketType,
        fullScore: values.totalScore,
        raters: values.scorer,
        scoringRule: values.scoreMechanism == 'add' ? 1 : 2,
        channelList: values.channelList,
        scoreColor: JSON.stringify(values.colorList),
        assessmentId: assessmentInfo?.assessmentId ?? null,
      };
      dispatch({
        type: 'smartQualityInspection/saveQualityInspectionRule',
        payload: formInfo,
        callback: res => {
          console.log(res);
          if (res.code == 200) {
            resolve(res.data.assessmentId);
          } else {
            reject(res.msg);
          }
        },
      });
    });
  };

  const handleCancel = () => {
    // 处理取消操作，例如返回上一页
    history.goBack();
    dispatch({
      type: 'smartQualityInspection/resetAll',
    });
  };
  const updateSubmitError = v => {
    setSubmitError(v);
  };
  const handleSliderRate = (index, value) => {
    const _rate = [...rate];
    _rate[index] = value;
    console.log(_rate);
    setRate(_rate);
  };

  const getScoreUser = async () => {
    return new Promise((resolve, reject) => {
      dispatch({
        type: 'documentKnowledgeBase/queryDocumentKnowledgeAllUser',
        payload: '',
        callback: res => {
          if (res.data) {
            setScoreUserList(res.data);
            resolve();
          } else {
            reject('Failed to get score user list');
          }
        },
      });
    });
  };
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        await getScoreUser();
        await newGetChannels();
        await queryWorkRecordType();
        if (isEditMode) {
          console.log(assessmentInfo, 'assessmentInfo');

          // 处理回显数据
          const formData = { ...assessmentInfo };
          formData.colorList = JSON.parse(formData.scoreColor);
          let startNum = formData.colorList[1].startNum;
          let endNum = formData.colorList[1].endNum;
          setRate([startNum, endNum]);
          // 如果 channelList 为 null，表示选择了"所有"
          if (
            formData.channelList === null ||
            formData.channelList === undefined
          ) {
            formData.channel = ['all'];
            setIsChannelAllSelected(true);
          }

          // 如果 ticketType 为 null，表示选择了"所有"
          if (
            formData.ticketType === null ||
            formData.ticketType === undefined
          ) {
            formData.ticketType = ['all'];
            setIsTicketTypeAllSelected(true);
          }

          formRef.current.setFieldsValue(formData);
        } else {
          // 非编辑模式下的初始化
          const initialData = { ...evaluationFormData };

          // 如果 channelList 为 null，表示选择了"所有"
          if (
            initialData.channelList === null ||
            initialData.channelList === undefined
          ) {
            initialData.channel = ['all'];
            setIsChannelAllSelected(true);
          }

          // 如果 ticketType 为 null，表示选择了"所有"
          if (
            initialData.ticketType === null ||
            initialData.ticketType === undefined
          ) {
            initialData.ticketType = ['all'];
            setIsTicketTypeAllSelected(true);
          }

          formRef.current.setFieldsValue(initialData);
        }
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setLoading(false);
      }
      setLoading(false);
    };
    fetchData();
  }, []);

  const scoreMechanismTooltip = () => {
    return (
      <div className={styles.scoreMechanismTooltip}>
        <div>
          <b>
            <FormattedMessage
              id="smart.quality.evaluation.add.score.mechanism.tooltip.add.title"
              defaultMessage="加分机制"
            />
          </b>
        </div>
        <div>
          <FormattedMessage id="smart.quality.evaluation.add.score.mechanism.tooltip.add.1" />
        </div>
        <div>
          <FormattedMessage id="smart.quality.evaluation.add.score.mechanism.tooltip.add.2" />
        </div>
        <div>
          <FormattedMessage id="smart.quality.evaluation.add.score.mechanism.tooltip.add.3" />
        </div>
        <div>
          <b>
            <FormattedMessage
              id="smart.quality.evaluation.add.score.mechanism.tooltip.subtract.title"
              defaultMessage="减分机制"
            />
          </b>
        </div>
        <div>
          <FormattedMessage id="smart.quality.evaluation.add.score.mechanism.tooltip.subtract.1" />
        </div>
        <div>
          <FormattedMessage id="smart.quality.evaluation.add.score.mechanism.tooltip.subtract.2" />
        </div>
        <div>
          <FormattedMessage id="smart.quality.evaluation.add.score.mechanism.tooltip.subtract.3" />
        </div>
      </div>
    );
  };
  const findChannelById = channelId => {
    for (let option of channelOptions) {
      for (let op of option.channelVOList) {
        if (op.channelId == channelId) {
          return { sonItem: op, parentItem: option };
        }
      }
    }
    return {}; // 如果没有找到匹配的标签，返回 null
  };

  // 查询渠道
  const newGetChannels = async () => {
    return new Promise((resolve, reject) => {
      dispatch({
        type: 'channel/getChannelGroup',
        callback: res => {
          if (res.code === 200) {
            let result = [...res.data];

            // 不再在第一个渠道组中添加"所有"选项
            // 而是在后续渲染时单独处理

            setChannelOptions(result);
            resolve(); // 成功时调用 resolve
          } else {
            setLoading(false);
            reject(res.msg); // 失败时调用 reject
          }
        },
      });
    });
  };
  // 查询工单类型列表
  const queryWorkRecordType = () => {
    return new Promise((resolve, reject) => {
      dispatch({
        type: 'workOrderCenter/queryWorkRecordType',
        callback: response => {
          if (response.code == 200) {
            // 不再添加"所有"选项到列表中
            // 而是在渲染时单独处理
            setWorkRecordTypeList(response.data);
            resolve(); // 成功时调用 resolve
          } else {
            notification.error({
              message: response.msg,
            });
            reject(response.msg); // 失败时调用 reject
          }
        },
      });
    });
  };

  // 处理渠道选择变化
  const handleChannelChange = value => {
    if (value.includes('all')) {
      // 如果选择了"所有"，只保留"所有"选项
      formRef.current.setFieldsValue({
        channel: ['all'],
      });
      setIsChannelAllSelected(true);
    } else {
      // 如果选择了其他选项且原来有"所有"，则移除"所有"
      const currentValues = formRef.current.getFieldValue('channel') || [];
      if (currentValues.includes('all')) {
        const filteredValues = value.filter(v => v !== 'all');
        formRef.current.setFieldsValue({
          channel: filteredValues,
        });
      }
      setIsChannelAllSelected(false);
    }
  };

  // 处理工单类型选择变化
  const handleTicketTypeChange = value => {
    if (value.includes('all')) {
      // 如果选择了"所有"，只保留"所有"选项
      formRef.current.setFieldsValue({
        ticketType: ['all'],
      });
      setIsTicketTypeAllSelected(true);
    } else {
      // 如果选择了其他选项且原来有"所有"，则移除"所有"
      const currentValues = formRef.current.getFieldValue('ticketType') || [];
      if (currentValues.includes('all')) {
        const filteredValues = value.filter(v => v !== 'all');
        formRef.current.setFieldsValue({
          ticketType: filteredValues,
        });
      }
      setIsTicketTypeAllSelected(false);
    }
  };

  return (
    <div className={styles.evaluationAddContainer} id="evaluationAdd">
      <Spin spinning={loading}>
        <p className="blueBorder">
          <FormattedMessage
            id="smart.quality.evaluation.add"
            defaultMessage="添加评估表"
          />
        </p>
        <div className={styles.addContainer}>
          <div className={styles.addDetailContainer}>
            <div className={styles.line}></div>
            <div className={styles.categoryList}>
              <Form
                name="basic"
                layout="vertical"
                ref={formRef}
                onFinish={onFinish}
                autoComplete="off"
                initialValues={{
                  scoreMechanism:
                    assessmentInfo.scoringRule == 2 ? 'subtract' : 'add',
                  name: assessmentInfo.assessmentName,
                  totalScore: assessmentInfo.fullScore,
                  channel:
                    assessmentInfo.channelList === null
                      ? ['all']
                      : assessmentInfo.channelList,
                  ticketType:
                    assessmentInfo.ticketType === null
                      ? ['all']
                      : assessmentInfo.ticketType,
                  scorer: assessmentInfo.raters,
                }}
              >
                <div className={styles.secondTitle}>
                  <div className={styles.circleContainer}></div>
                  <FormattedMessage
                    id="smart.quality.evaluation.add.baseinfo"
                    defaultMessage="基本信息"
                  />
                </div>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      label={
                        <FormattedMessage
                          id="smart.quality.evaluation.add.name"
                          defaultMessage="评估表名称"
                        />
                      }
                      name="name"
                      rules={[
                        {
                          required: true,
                          message: (
                            <FormattedMessage
                              id="smart.quality.evaluation.add.name.required"
                              defaultMessage="请输入评估表名称"
                            />
                          ),
                        },
                        {
                          max: 80,
                          message: (
                            <FormattedMessage
                              id="smart.quality.evaluation.add.name.max"
                              defaultMessage="长度不能超过80个字符"
                            />
                          ),
                        },
                      ]}
                    >
                      <Input
                        maxLength={80}
                        placeholder={getIntl().formatMessage({
                          id: 'smart.quality.evaluation.add.name.placeholder',
                          defaultValue: '请输入评估表名称',
                        })}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      label={
                        <FormattedMessage
                          id="smart.quality.evaluation.add.channel"
                          defaultMessage="适用渠道"
                        />
                      }
                      name="channel"
                      rules={[
                        {
                          required: true,
                          message: (
                            <FormattedMessage
                              id="smart.quality.evaluation.add.channel.required"
                              defaultMessage="请选择适用渠道"
                            />
                          ),
                        },
                      ]}
                    >
                      <Select
                        mode="multiple"
                        optionLabelProp="label"
                        allowClear
                        disabled={isEditMode}
                        placeholder={getIntl().formatMessage({
                          id: 'feedbackPerformance.card.5.select.placeholder',
                          defaultValue: '请选择渠道',
                        })}
                        onChange={handleChannelChange}
                        showSearch
                        optionFilterProp="label"
                      >
                        {/* 添加独立的"所有"选项 */}
                        <Option
                          key="all"
                          value="all"
                          label={getIntl().formatMessage({
                            id: 'smart.quality.evaluation.add.channel.all',
                            defaultMessage: '所有',
                          })}
                          className={styles.allOption}
                        >
                          <span>
                            {getIntl().formatMessage({
                              id: 'smart.quality.evaluation.add.channel.all',
                              defaultMessage: '所有',
                            })}
                          </span>
                        </Option>

                        {/* 分隔线 - 将通过CSS添加 */}

                        {/* 渲染原有的渠道组，根据是否选择了"所有"来设置禁用状态 */}
                        {channelOptions?.map(group => (
                          <OptGroup
                            key={group.code}
                            label={(() => {
                              if (group.code == '0') {
                                return (
                                  <>
                                    <img
                                      src={AllchannelIcon}
                                      width={16}
                                      height={16}
                                    />
                                    <span style={{ marginLeft: 5 }}>
                                      {group.name}
                                    </span>
                                  </>
                                );
                              } else if (group.code == '1') {
                                return (
                                  <>
                                    <img
                                      src={EmailIcon}
                                      width={16}
                                      height={16}
                                    />
                                    <span style={{ marginLeft: 5 }}>
                                      {group.name}
                                    </span>
                                  </>
                                );
                              } else if (group.code == '3') {
                                return (
                                  <>
                                    <img
                                      src={FacebookIcon}
                                      width={16}
                                      height={16}
                                    />
                                    <span style={{ marginLeft: 5 }}>
                                      {group.name}
                                    </span>
                                  </>
                                );
                              } else if (group.code == '4') {
                                return (
                                  <>
                                    <img
                                      src={WhatsAppIcon}
                                      width={16}
                                      height={16}
                                    />
                                    <span style={{ marginLeft: 5 }}>
                                      {group.name}
                                    </span>
                                  </>
                                );
                              } else if (group.code == '5') {
                                return (
                                  <>
                                    <img
                                      src={TwitterIcon}
                                      width={16}
                                      height={16}
                                    />
                                    <span style={{ marginLeft: 5 }}>
                                      {group.name}
                                    </span>
                                  </>
                                );
                              } else if (group.code == '6') {
                                return (
                                  <>
                                    <img
                                      src={LineIcon}
                                      width={16}
                                      height={16}
                                    />{' '}
                                    <span style={{ marginLeft: 5 }}>
                                      {group.name}
                                    </span>
                                  </>
                                );
                              } else if (group.code == '7') {
                                return (
                                  <>
                                    <img
                                      src={PhoneIcon}
                                      width={16}
                                      height={16}
                                    />{' '}
                                    <span style={{ marginLeft: 5 }}>
                                      {group.name}
                                    </span>
                                  </>
                                );
                              } else if (group.code == '8') {
                                return (
                                  <>
                                    <img
                                      src={ChatIcon}
                                      width={16}
                                      height={16}
                                    />
                                    <span style={{ marginLeft: 5 }}>
                                      {group.name}
                                    </span>
                                  </>
                                );
                              } else if (group.code == '9') {
                                return (
                                  <>
                                    <img
                                      src={AppChatOutlinedIcon}
                                      width={16}
                                      height={16}
                                    />
                                    <span style={{ marginLeft: 5 }}>
                                      {group.name}
                                    </span>
                                  </>
                                );
                              } else if (group.code == '10') {
                                return (
                                  <>
                                    <img
                                      src={WebVideoOutlinedIcon}
                                      width={16}
                                      height={16}
                                    />
                                    <span style={{ marginLeft: 5 }}>
                                      {group.name}
                                    </span>
                                  </>
                                );
                              } else if (group.code == '11') {
                                return (
                                  <>
                                    <img
                                      src={AppVideoOutlinedIcon}
                                      width={16}
                                      height={16}
                                    />
                                    <span style={{ marginLeft: 5 }}>
                                      {group.name}
                                    </span>
                                  </>
                                );
                              } else if (group.code == '12') {
                                return (
                                  <>
                                    <img
                                      src={AwsChannelIcon}
                                      width={16}
                                      height={16}
                                    />
                                    <span style={{ marginLeft: 5 }}>
                                      {group.name}
                                    </span>
                                  </>
                                );
                              } else if (group.code == '13') {
                                return (
                                  <>
                                    <img
                                      src={NewInstagramIcon}
                                      width={16}
                                      height={16}
                                    />
                                    <span style={{ marginLeft: 5 }}>
                                      {group.name}
                                    </span>
                                  </>
                                );
                              } else if (group.code == '14') {
                                return (
                                  <>
                                    <img
                                      src={NewLineIcon}
                                      width={16}
                                      height={16}
                                    />
                                    <span style={{ marginLeft: 5 }}>
                                      {group.name}
                                    </span>
                                  </>
                                );
                              } else if (group.code == '15') {
                                return (
                                  <>
                                    <img
                                      src={NewWeComIcon}
                                      width={16}
                                      height={16}
                                    />
                                    <span style={{ marginLeft: 5 }}>
                                      {group.name}
                                    </span>
                                  </>
                                );
                              } else if (group.code == '16') {
                                return (
                                  <>
                                    <img
                                      src={NewWechatOfficialAccountIcon}
                                      width={16}
                                      height={16}
                                    />
                                    <span style={{ marginLeft: 5 }}>
                                      {group.name}
                                    </span>
                                  </>
                                );
                              } else if (group.code == '17') {
                                return (
                                  <>
                                    <img
                                      src={NewWebOnlineVoiceIcon}
                                      width={16}
                                      height={16}
                                    />
                                    <span style={{ marginLeft: 5 }}>
                                      {group.name}
                                    </span>
                                  </>
                                );
                              } else if (group.code == '18') {
                                return (
                                  <>
                                    <img
                                      src={NewAppOnlineVoiceIcon}
                                      width={16}
                                      height={16}
                                    />
                                    <span style={{ marginLeft: 5 }}>
                                      {group.name}
                                    </span>
                                  </>
                                );
                              } else if (group.code == '19') {
                                return (
                                  <>
                                    <img
                                      src={NewTwitterIcon}
                                      width={16}
                                      height={16}
                                    />
                                    <span style={{ marginLeft: 5 }}>
                                      {group.name}
                                    </span>
                                  </>
                                );
                              } else if (group.code == '20') {
                                return (
                                  <>
                                    <img
                                      src={NewTelegramIcon}
                                      width={16}
                                      height={16}
                                    />
                                    <span style={{ marginLeft: 5 }}>
                                      {group.name}
                                    </span>
                                  </>
                                );
                              } else if (group.code == '21') {
                                return (
                                  <>
                                    <img
                                      src={NewWeChatMiniProgramIcon}
                                      width={16}
                                      height={16}
                                    />
                                    <span style={{ marginLeft: 5 }}>
                                      {group.name}
                                    </span>
                                  </>
                                );
                              } else if (group.code == '22') {
                                return (
                                  <>
                                    <img
                                      src={NewShopifyIcon}
                                      width={16}
                                      height={16}
                                    />
                                    <span style={{ marginLeft: 5 }}>
                                      {group.name}
                                    </span>
                                  </>
                                );
                              } else if (group.code == '23') {
                                return (
                                  <>
                                    <img
                                      src={NewGooglePlayIcon}
                                      width={16}
                                      height={16}
                                    />
                                    <span style={{ marginLeft: 5 }}>
                                      {group.name}
                                    </span>
                                  </>
                                );
                              } else {
                                return <>{group.name}</>;
                              }
                            })()}
                          >
                            {group?.channelVOList?.map(option => {
                              const isDisabled =
                                isChannelAllSelected &&
                                option.channelId !== 'all';
                              return (
                                <Option
                                  key={option.channelId}
                                  value={option.channelId}
                                  label={option.name}
                                  disabled={isDisabled}
                                >
                                  <span>{option.name}</span>
                                </Option>
                              );
                            })}
                          </OptGroup>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      label={
                        <FormattedMessage
                          id="smart.quality.evaluation.add.ticket.type"
                          defaultMessage="适用工单类型"
                        />
                      }
                      name="ticketType"
                      rules={[
                        {
                          required: true,
                          message: (
                            <FormattedMessage
                              id="smart.quality.evaluation.add.ticket.type.required"
                              defaultMessage="请选择适用工单类型"
                            />
                          ),
                        },
                      ]}
                    >
                      <Select
                        placeholder={
                          <FormattedMessage
                            id="smart.quality.evaluation.add.ticket.type.placeholder"
                            defaultMessage="请选择适用工单类型"
                          />
                        }
                        disabled={isEditMode}
                        mode="multiple"
                        allowClear
                        showSearch
                        filterOption={(inputValue, option) =>
                          option.label
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) >= 0
                        }
                        onChange={handleTicketTypeChange}
                      >
                        {/* 添加独立的"所有"选项 */}
                        <Option
                          key="all"
                          value="all"
                          className={styles.allOption}
                          label={getIntl().formatMessage({
                            id: 'smart.quality.evaluation.add.ticket.type.all',
                            defaultMessage: '所有',
                          })}
                        >
                          {getIntl().formatMessage({
                            id: 'smart.quality.evaluation.add.ticket.type.all',
                            defaultMessage: '所有',
                          })}
                        </Option>

                        {/* 分隔线 - 将通过CSS添加 */}

                        {/* 渲染工单类型列表，根据是否选择了"所有"来设置禁用状态 */}
                        {workRecordTypeList.map(item => {
                          const isDisabled = isTicketTypeAllSelected;
                          return (
                            <Option
                              key={item.workRecordTypeId}
                              value={item.workRecordTypeId}
                              disabled={isDisabled}
                              label={item.workRecordTypeName}
                            >
                              {item.workRecordTypeName}
                            </Option>
                          );
                        })}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
                <div className={styles.secondTitle}>
                  <div className={styles.circleContainer}></div>
                  <FormattedMessage
                    id="smart.quality.evaluation.add.rule"
                    defaultMessage="评分规则"
                  />
                </div>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      label={
                        <FormattedMessage
                          id="smart.quality.evaluation.add.total.score"
                          defaultMessage="满分"
                        />
                      }
                      name="totalScore"
                      rules={[
                        {
                          required: true,
                          message: (
                            <FormattedMessage
                              id="smart.quality.evaluation.add.total.score.required"
                              defaultMessage="请输入满分"
                            />
                          ),
                        },
                      ]}
                    >
                      <InputNumber
                        disabled={isEditMode}
                        min={1}
                        placeholder={getIntl().formatMessage({
                          id:
                            'smart.quality.evaluation.add.total.score.placeholder',
                          defaultValue: '请输入满分',
                        })}
                        style={{ width: '100%' }}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <div
                  className={styles.stepContent}
                  style={{ marginTop: '40px' }}
                >
                  <Slider
                    color={[red, blue, green]}
                    startIcon={sliderRedIcon}
                    endIcon={sliderBlueIcon}
                    updateSubmitError={updateSubmitError}
                    updateRange={handleSliderRate}
                    range={rate}
                    endSuffix={'%'}
                    startSuffix={'%'}
                  />
                </div>
                <Form.Item
                  label={
                    <span>
                      <FormattedMessage
                        id="smart.quality.evaluation.add.score.mechanism"
                        defaultMessage="评分机制"
                      />
                      <Tooltip
                        title={scoreMechanismTooltip()}
                        placement="right"
                        color="#fff"
                        getPopupContainer={() =>
                          document.getElementById('evaluationAdd')
                        }
                      >
                        <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                      </Tooltip>
                    </span>
                  }
                  name="scoreMechanism"
                  rules={[
                    {
                      required: true,
                      message: (
                        <FormattedMessage
                          id="smart.quality.evaluation.add.score.mechanism.required"
                          defaultMessage="请选择评分机制"
                        />
                      ),
                    },
                  ]}
                >
                  <Radio.Group disabled={isEditMode}>
                    <Radio value="add">
                      <FormattedMessage
                        id="smart.quality.evaluation.add.score.mechanism.add"
                        defaultMessage="加分机制"
                      />
                    </Radio>
                    <Radio value="subtract">
                      <FormattedMessage
                        id="smart.quality.evaluation.add.score.mechanism.subtract"
                        defaultMessage="减分机制"
                      />
                    </Radio>
                  </Radio.Group>
                </Form.Item>
                <div className={styles.secondTitle}>
                  <div className={styles.circleContainer}></div>
                  <FormattedMessage
                    id="smart.quality.evaluation.add.permission"
                    defaultMessage="权限设置"
                  />
                </div>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      label={
                        <FormattedMessage
                          id="smart.quality.evaluation.add.permission.scorer"
                          defaultMessage="评分人"
                        />
                      }
                      name="scorer"
                      tooltip={{
                        title: (
                          <span style={{ color: '#333' }}>
                            <FormattedMessage
                              id="smart.quality.evaluation.add.permission.scorer.tooltip"
                              defaultMessage="此处定义谁有权限基于当前评估表评分"
                            />
                          </span>
                        ),
                        placement: 'right',
                        color: '#fff',
                      }}
                      rules={[
                        {
                          required: true,
                          message: (
                            <FormattedMessage
                              id="smart.quality.evaluation.add.permission.scorer.required"
                              defaultMessage="请选择评分人"
                            />
                          ),
                        },
                      ]}
                    >
                      <Select
                        placeholder={
                          <FormattedMessage
                            id="smart.quality.evaluation.add.permission.scorer.placeholder"
                            defaultMessage="请选择评分人"
                          />
                        }
                        mode="multiple"
                        options={scoreUserList}
                        fieldNames={{
                          label: 'userName',
                          value: 'userId',
                          key: 'userId',
                        }}
                        showSearch
                        filterOption={(inputValue, option) =>
                          option.userName
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) >= 0
                        }
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <Row>
                  <Col span={24}>
                    <Form.Item className={styles.formButtons}>
                      <Popconfirm
                        title={
                          <FormattedMessage
                            id="smart.quality.evaluation.add.cancel.confirm"
                            defaultMessage="取消将清空表单，确定取消吗？"
                          />
                        }
                        onConfirm={handleCancel}
                        okText={
                          <FormattedMessage
                            id="smart.quality.common.yes"
                            defaultMessage="是"
                          />
                        }
                        cancelText={
                          <FormattedMessage
                            id="smart.quality.common.no"
                            defaultMessage="否"
                          />
                        }
                      >
                        <Button style={{ marginRight: 16 }}>
                          <FormattedMessage
                            id="smart.quality.common.cancel"
                            defaultMessage="取消"
                          />
                        </Button>
                      </Popconfirm>
                      <Button type="primary" htmlType="submit">
                        <FormattedMessage
                          id="smart.quality.common.next"
                          defaultMessage="下一步"
                        />
                      </Button>
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
            </div>
          </div>
        </div>
      </Spin>
    </div>
  );
};

export default EvaluationAdd;
