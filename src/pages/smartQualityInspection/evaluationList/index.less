.evaluationListContainer {
  margin: 20px;
  padding: 20px;
  min-height: 88vh;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0px 2px 30px 0px rgba(0, 0, 0, 0.02);
  .evaluationListHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
  }
  .evaluationListTitle {
    font-size: 20px;
    font-weight: 600;
  }
  .tableOperation {
    display: flex;
    gap: 8px;
    align-items: center;
    img {
      width: 16px;
      height: 16px;
      object-fit: contain;
      vertical-align: middle;
      margin-right: 4px;
    }
  }
  :global(.ant-table-thead th) {
    font-weight: 600;
  }
  :global(.ant-table-tbody > tr > td) {
    height: 80px;
  }
  :global(.ant-table-tbody > tr.disabled-row > td) {
    background: #f5f5f5;
  }
  :global(.ant-btn[disabled]) {
    background: none !important;
  }
  :global(.ant-btn) {
    display: flex;
    align-items: center;
    border: none;
    &:hover {
      border: none;
      background: none;
    }
    &:focus {
      border: none;
      outline: none;
    }
  }
  :global {
    .ant-tag {
      font-size: 12px;
      color: #3463fc;
      float: left;
      border-radius: 4px;
      border: 1px solid rgba(52, 99, 252, 0.5);
      background: linear-gradient(
          0deg,
          rgba(52, 99, 252, 0.1) 0%,
          rgba(52, 99, 252, 0.1) 100%
        ),
        #fff;
    }
    .ant-tag:first-child {
      margin-left: 0px;
    }
  }
}

.tagContainer {
  .moreIcon {
    float: left;
    cursor: pointer;
  }
}
.tableReleaseStatus {
  display: flex;
  align-items: center;
  img {
    width: 16px;
    height: 16px;
  }
}
