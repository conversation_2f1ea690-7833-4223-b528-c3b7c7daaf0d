import React, { useState, useEffect } from 'react';
import {
  Button,
  Table,
  Popconfirm,
  notification,
  Spin,
  Tag,
  Popover,
} from 'antd';
import { getIntl, FormattedMessage, history, useDispatch } from 'umi';
import styles from './index.less';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import NewDeleteIcon from '../../../assets/new-delete-list-icon.png';
import TableDetailIcon from '../../../assets/table-details.svg';
import DeployIcon from '../../../assets/external-deploy.png';
import GreyDeployIcon from '../../../assets/grey-deploy.png';
import { MoreIcon } from '../../knowledgeQA/icon';
import AllchannelIcon from '../../../assets/allchannel.svg';
import HOCAuth from '@/components/HOCAuth/index';
const statusMap = {
  1: getIntl().formatMessage({
    id: 'smart.quality.evaluation.table.status.published',
    defaultMessage: '已发布',
  }),
  0: getIntl().formatMessage({
    id: 'smart.quality.evaluation.table.status.disabled',
    defaultMessage: '未发布',
  }),
};

const EvaluationList = () => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [dataMap, setDataMap] = useState({});
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(5);

  useEffect(() => {
    setLoading(true);
    fetchData();
  }, []);
  const fetchData = async (pageNum = current, size = pageSize) => {
    try {
      setLoading(true);
      await new Promise((resolve, reject) => {
        dispatch({
          type: 'smartQualityInspection/getQualityInspectionRuleList',
          payload: {
            pageNum: pageNum,
            pageSize: size,
          },
          callback: res => {
            try {
              console.log(res);
              if (res.code === 200) {
                setDataSource(res.data?.records || []);
                setDataMap(res.data || {});
              }
              resolve(res);
            } catch (callbackError) {
              console.error('回调处理失败:', callbackError);
              reject(callbackError);
            }
          },
        });
      });
    } catch (error) {
      console.error('获取数据失败:', error);
    } finally {
      setLoading(false);
    }
  };
  const handleAdd = () => {
    dispatch({
      type: 'smartQualityInspection/resetAll',
    });

    history.push('/evaluationAdd');
  };
  const handleEnable = async record => {
    setLoading(true);
    await dispatch({
      type: 'smartQualityInspection/enableOrDisableQualityInspectionRule',
      payload: {
        assessmentId: record.assessmentId,
      },
      callback: res => {
        if (res.code === 200) {
          fetchData();
          if (record.status === 0) {
            notification.success({
              message: getIntl().formatMessage({
                id: 'smart.quality.evaluation.table.enable.success',
                defaultMessage: '启用成功',
              }),
            });
          } else {
            notification.success({
              message: getIntl().formatMessage({
                id: 'smart.quality.evaluation.table.disable.success',
                defaultMessage: '禁用成功',
              }),
            });
          }
        }
      },
    });
  };
  // 修改
  const handleEdit = record => {
    dispatch({
      type: 'smartQualityInspection/setEditMode',
      payload: true,
    });
    // 回显evaluationAdd用的
    dispatch({
      type: 'smartQualityInspection/setAssessmentInfo',
      payload: record,
    });
    history.push('/evaluationAdd');
  };
  const handleEditRule = record => {
    console.log(record);
    dispatch({
      type: 'smartQualityInspection/setEditMode',
      payload: true,
    });

    // 编辑数据结构处理，后面回显提交都用得到 不需要再做处理
    const newEditRecord = {
      ...record,
      scoreMechanism: record.scoringRule == 1 ? 'add' : 'subtract',
      totalScore: record.fullScore,
      name: record.assessmentName,
    };
    dispatch({
      type: 'smartQualityInspection/setEvaluationFormData',
      payload: newEditRecord,
    });
    history.push('/evaluationRuleList');
  };

  const handleHistory = record => {
    history.push({
      pathname: '/evaluationHistory',
      state: {
        assessmentId: record.assessmentId,
        versionId: record.versionId,
      },
    });
  };

  const handleDelete = async record => {
    setLoading(true);
    try {
      await new Promise((resolve, reject) => {
        dispatch({
          type: 'smartQualityInspection/deleteQualityInspectionRule',
          payload: {
            assessmentId: record.assessmentId,
          },
          callback: res => {
            try {
              if (res.code === 200) {
                fetchData();
                notification.success({
                  message: getIntl().formatMessage({
                    id: 'smart.quality.evaluation.table.delete.success',
                    defaultMessage: '删除成功',
                  }),
                });
              }
              resolve(res);
            } catch (callbackError) {
              console.error('回调处理失败:', callbackError);
              reject(callbackError);
            }
          },
        });
      });
    } catch (error) {
      console.error('删除失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderTicketTypes = ticketTypes => {
    if (!ticketTypes || ticketTypes.length === 0 || !ticketTypes[0])
      return <Tag>所有</Tag>;
    if (ticketTypes.length === 1) {
      return <Tag>{ticketTypes[0]}</Tag>;
    }
    return (
      <div className={styles.tagContainer}>
        <Tag style={{ marginRight: 3 }}>{ticketTypes[0]}</Tag>
        <Popover
          overlayClassName="ticketTypePopover"
          content={
            <div>
              {ticketTypes.map((type, index) => (
                <Tag key={index} style={{ marginRight: 3 }}>
                  {type}
                </Tag>
              ))}
            </div>
          }
          title={null}
        >
          <span className={styles.moreIcon}>{MoreIcon()}</span>
        </Popover>
      </div>
    );
  };

  const columns = [
    {
      title: (
        <FormattedMessage
          id="smart.quality.evaluation.table.name"
          defaultMessage="评估表名称"
        />
      ),
      dataIndex: 'assessmentName',
      key: 'assessmentName',
      width: 150,
    },
    {
      title: (
        <FormattedMessage
          id="smart.quality.evaluation.table.channel"
          defaultMessage="渠道"
        />
      ),
      dataIndex: 'channelInfoList',
      key: 'channelInfoList',
      width: 220,
      render: (text, record) => {
        if (!record.channelInfoList || record.channelInfoList?.length <= 0) {
          return (
            <div>
              <img
                src={AllchannelIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span
                style={{
                  fontSize: '12px',
                  fontWeight: '700',
                  color: '#333',
                }}
              >
                <FormattedMessage
                  id="smart.quality.evaluation.table.channel.all"
                  defaultMessage="所有渠道"
                />
              </span>
            </div>
          );
        } else {
          return (
            <div>
              {record.channelInfoList?.map(channelType => {
                return (
                  <div key={channelType.id}>
                    <span>{channelType.name}</span>
                    <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
                    <span
                      style={{
                        marginLeft: '4px',
                        color: '#3463FC',
                        fontSize: '12px',
                      }}
                    >
                      {channelType.channelVOList?.map((channelVO, index) => {
                        return (
                          <span key={channelVO.id}>
                            {channelVO.name}
                            {index !== channelType.channelVOList.length - 1 &&
                              ','}
                          </span>
                        );
                      })}
                    </span>
                  </div>
                );
              })}
            </div>
          );
        }
      },
    },
    {
      title: (
        <FormattedMessage
          id="smart.quality.evaluation.table.ticket.type"
          defaultMessage="工单类型"
        />
      ),
      dataIndex: 'ticketTypeName',
      key: 'ticketTypeName',
      width: 240,
      render: text => renderTicketTypes(Array.isArray(text) ? text : [text]),
    },
    {
      title: (
        <FormattedMessage
          id="smart.quality.evaluation.table.status"
          defaultMessage="发布状态"
        />
      ),
      dataIndex: 'deployStatus',
      key: 'deployStatus',
      render: (text, record) => {
        return (
          <div className={styles.tableReleaseStatus}>
            <img
              src={record.deployStatus === 1 ? DeployIcon : GreyDeployIcon}
              alt="deploy"
            />
            {statusMap[record.deployStatus || 0]}{' '}
            {record.deployStatus == 1 && record.latestVersionNo}
          </div>
        );
      },
      width: 220,
    },
    {
      title: (
        <FormattedMessage
          id="smart.quality.evaluation.table.rule.count"
          defaultMessage="评分规则数量"
        />
      ),
      dataIndex: 'ruleCount',
      key: 'ruleCount',
      render: text => text || 0,
    },
    {
      title: (
        <FormattedMessage
          id="smart.quality.evaluation.table.total.score"
          defaultMessage="总分"
        />
      ),
      dataIndex: 'fullScore',
      key: 'fullScore',
      render: (score, record) => (
        <span style={{ color: '#13C825', fontWeight: 'bold' }}>{score}</span>
      ),
      width: 200,
    },
    {
      title: (
        <FormattedMessage
          id="smart.quality.evaluation.table.operation"
          defaultMessage="操作"
        />
      ),
      key: 'operation',
      // width: 320,
      fixed: 'right',
      render: (_, record) => (
        <div className={styles.tableOperation}>
          {record.status === 0 ? (
            <HOCAuth authKey={'assessment_form_config'}>
              {authAccess => (
                <Button
                  type="link"
                  onClick={() => handleEnable(record)}
                  disabled={authAccess}
                  title={
                    authAccess
                      ? getIntl().formatMessage({
                          id: 'auth.access.no.1',
                          defaultValue: '您的当前版本不支持此功能',
                        })
                      : ''
                  }
                >
                  <FormattedMessage
                    id="smart.quality.evaluation.table.enable"
                    defaultMessage="启用"
                  />
                </Button>
              )}
            </HOCAuth>
          ) : (
            <HOCAuth authKey={'assessment_form_config'}>
              {authAccess => (
                <Button
                  type="link"
                  onClick={() => handleEnable(record)}
                  disabled={authAccess}
                  title={
                    authAccess
                      ? getIntl().formatMessage({
                          id: 'auth.access.no.1',
                          defaultValue: '您的当前版本不支持此功能',
                        })
                      : ''
                  }
                >
                  <FormattedMessage
                    id="smart.quality.evaluation.table.disable"
                    defaultMessage="禁用"
                  />
                </Button>
              )}
            </HOCAuth>
          )}
          <HOCAuth authKey={'assessment_form_config'}>
            {authAccess => (
              <Button
                type="link"
                icon={<EditOutlined />}
                onClick={() => handleEditRule(record)}
                disabled={authAccess}
                title={
                  authAccess
                    ? getIntl().formatMessage({
                        id: 'auth.access.no.1',
                        defaultValue: '您的当前版本不支持此功能',
                      })
                    : ''
                }
              >
                <FormattedMessage id="smart.quality.evaluation.table.edit.rule" />
              </Button>
            )}
          </HOCAuth>
          <HOCAuth authKey={'assessment_form_config'}>
            {authAccess => (
              <Button
                type="link"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
                disabled={authAccess}
                title={
                  authAccess
                    ? getIntl().formatMessage({
                        id: 'auth.access.no.1',
                        defaultValue: '您的当前版本不支持此功能',
                      })
                    : ''
                }
              >
                <FormattedMessage id="smart.quality.evaluation.table.edit" />
              </Button>
            )}
          </HOCAuth>
          <Button
            type="link"
            icon={<img src={TableDetailIcon} alt="history" />}
            onClick={() => handleHistory(record)}
          >
            <FormattedMessage
              id="smart.quality.evaluation.table.history"
              defaultMessage="评估历史记录"
            />
          </Button>
          <HOCAuth authKey={'assessment_form_config'}>
            {authAccess => (
              <Popconfirm
                disabled={authAccess}
                title={
                  <FormattedMessage
                    id="smart.quality.evaluation.table.delete.confirm"
                    defaultMessage="是否删除该评估表？"
                  />
                }
                onConfirm={() => handleDelete(record)}
                okText={
                  <FormattedMessage
                    id="smart.quality.evaluation.table.delete.ok"
                    defaultMessage="是"
                  />
                }
                cancelText={
                  <FormattedMessage
                    id="smart.quality.evaluation.table.delete.cancel"
                    defaultMessage="否"
                  />
                }
              >
                <Button
                  type="link"
                  icon={<img src={NewDeleteIcon} alt="delete" />}
                  danger
                  disabled={authAccess}
                  title={
                    authAccess
                      ? getIntl().formatMessage({
                          id: 'auth.access.no.1',
                          defaultValue: '您的当前版本不支持此功能',
                        })
                      : ''
                  }
                >
                  <FormattedMessage
                    id="smart.quality.evaluation.table.delete"
                    defaultMessage="删除"
                  />
                </Button>
              </Popconfirm>
            )}
          </HOCAuth>
        </div>
      ),
    },
  ];

  return (
    <div className={styles.evaluationListContainer}>
      <div className={styles.evaluationListHeader}>
        <div className="blueBorder">
          <FormattedMessage
            id="smart.quality.evaluation.table.title"
            defaultMessage="智能质检评估表"
          />
        </div>
        <HOCAuth authKey={'assessment_form_config'}>
          {authAccess => (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
              disabled={authAccess}
              title={
                authAccess
                  ? getIntl().formatMessage({
                      id: 'auth.access.no.1',
                      defaultValue: '您的当前版本不支持此功能',
                    })
                  : ''
              }
            >
              <FormattedMessage
                id="smart.quality.evaluation.table.add"
                defaultMessage="添加评估表"
              />
            </Button>
          )}
        </HOCAuth>
      </div>
      <Spin spinning={loading}>
        <Table
          dataSource={dataSource}
          columns={columns}
          scroll={{ x: 'max-content' }}
          rowClassName={record => (record.status === 0 ? 'disabled-row' : '')}
          rowKey={record => record.assessmentId}
          pagination={{
            total: dataMap?.total,
            pageSize,
            current,
            showSizeChanger: true,
            pageSizeOptions: [5, 10, 20, 50, 100],
            onChange: (page, size) => {
              setLoading(true);
              setCurrent(page);
              setPageSize(size);
              fetchData(page, size);
            },
            showTotal: total => (
              <FormattedMessage
                id="smart.quality.evaluation.list.page.total.num"
                defaultMessage={`共 ${total} 条`}
                values={{ total }}
              />
            ),
          }}
        />
      </Spin>
    </div>
  );
};

export default EvaluationList;
