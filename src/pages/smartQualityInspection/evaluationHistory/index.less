.evaluationHistoryContainer {
  margin: 20px;
  min-height: 88vh;
  overflow: hidden;
  overflow-y: scroll;
  /* 隐藏滚动条 */
  scrollbar-width: none;
  /* firefox */
  -ms-overflow-style: none;
  .searchContainer {
    margin-top: 20px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0px 2px 30px 0px rgba(0, 0, 0, 0.02);
    margin-bottom: 20px;

    .headerWithTitle {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
    }

    .searchForm {
      .rangeSeparator {
        display: inline-block;
        width: 10%;
        text-align: center;
        line-height: 32px;
      }

      :global {
        .ant-form-item {
          margin-bottom: 16px;
        }

        .ant-form-item-label > label {
          font-size: 12px;
          color: #333;
          font-weight: 700;
        }

        .ant-select {
          width: 100%;
        }

        .ant-picker {
          width: 100%;
        }

        .ant-btn {
          span {
            margin-right: 8px;
          }
        }
      }
    }

    :global {
      .ant-form-item {
        margin-bottom: 10px;
      }

      .ant-col-1 {
        padding-left: 0px !important;
      }

      .ant-input,
      .ant-picker {
        font-size: 12px;
        height: 32px;
        box-shadow: none;
        border-radius: 6px;
        background: #fff;
      }

      .ant-select {
        font-size: 12px;
      }

      .ant-select:not(.ant-select-customize-input) .ant-select-selector {
        height: 32px;
        box-shadow: none;
        border-radius: 6px;
        background: #fff;
        overflow: hidden;
        overflow-y: scroll;
      }

      textarea.ant-input {
        font-size: 12px;
      }

      .ant-input-number {
        width: 100%;
        font-size: 12px;
        height: 32px;
        box-shadow: none;
        border-radius: 6px;
        background: #fff;
      }

      .ant-input-number-handler-wrap {
        border-radius: 0 6px 6px 0;
      }

      .ant-input-number-handler-down {
        border-bottom-right-radius: 6px;
      }

      .ant-input-number-handler-up {
        border-top-right-radius: 6px;
      }

      .ant-form-item-label > label {
        color: #333;
        font-size: 12px;
        font-style: normal;
        font-weight: 700;
        line-height: 150%;
      }

      .ant-form-item-label
        > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
        display: none;
      }

      .ant-form-item-label
        > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::after {
        display: inline-block;
        margin-left: 2px;
        color: #ff4d4f;
        font-size: 14px;
        font-family: SimSun, sans-serif;
        line-height: 1;
        content: '*';
      }

      .ant-radio-checked .ant-radio-inner {
        border-color: #3463fc;
      }

      .ant-radio-inner::after {
        background-color: #3463fc;
      }

      .ant-radio-wrapper {
        font-size: 12px !important;
      }

      .ant-select-multiple .ant-select-selection-item {
        border-radius: 4px;
        border: 1px solid rgba(52, 99, 252, 0.5);
        background: linear-gradient(
            0deg,
            rgba(52, 99, 252, 0.1) 0%,
            rgba(52, 99, 252, 0.1) 100%
          ),
          #fff;

        .anticon {
          color: #3463fc;
        }
      }

      .ant-select-multiple .ant-select-selection-item-content {
        color: #3463fc;
        font-size: 12px;
      }

      .ant-tooltip {
        min-width: max-content;
      }

      .ant-tooltip-inner {
        padding: 14px 17px;
        border-radius: 4px;
      }

      .ant-select-multiple {
        .ant-select-selection-item {
          border: 1px solid #3463fc;
          background: #3463fc1a;
          border-radius: 4px;
          color: #3463fc;
        }

        .ant-select-selection-item-remove {
          svg path {
            fill: #3463fc;
          }
        }

        .ant-select-selector {
          font-size: 12px !important;
          border-radius: 6px !important;
          border: 1px solid #e6e6e6 !important;
        }
      }
    }
  }

  .tableContainer {
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0px 2px 30px 0px rgba(0, 0, 0, 0.02);

    .tableOperation {
      display: flex;
      gap: 8px;
      align-items: center;

      img {
        width: 16px;
        height: 16px;
        object-fit: contain;
        vertical-align: middle;
        margin-right: 4px;
      }
    }

    :global(.ant-table-thead th) {
      font-weight: 600;
    }

    :global(.ant-table-tbody > tr > td) {
      height: 80px;
    }

    :global(.ant-btn[disabled]) {
      background: none !important;
    }

    :global(.ant-btn) {
      display: flex;
      align-items: center;
      border: none;

      &:hover {
        border: none;
        background: none;
      }

      &:focus {
        border: none;
        outline: none;
      }
    }

    :global {
      .ant-tag {
        font-size: 12px;
        color: #3463fc;
        float: left;
        border-radius: 4px;
        border: 1px solid rgba(52, 99, 252, 0.5);
        background: linear-gradient(
            0deg,
            rgba(52, 99, 252, 0.1) 0%,
            rgba(52, 99, 252, 0.1) 100%
          ),
          #fff;
      }

      .ant-tag:first-child {
        margin-left: 0px;
      }
    }
  }
}

.tagContainer {
  .moreIcon {
    float: left;
    cursor: pointer;
  }
}
