import React, { useState, useEffect } from 'react';
import {
  Button,
  Table,
  Spin,
  Tag,
  Popover,
  Row,
  Col,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Form,
  notification,
} from 'antd';
import { getIntl, FormattedMessage, history, useDispatch, Link } from 'umi';
import styles from './index.less';
import { MoreIcon } from '../../knowledgeQA/icon';
import AllchannelIcon from '../../../assets/allchannel.svg';
import TableDetailIcon from '../../../assets/table-details.svg';
import { SearchOutlined } from '@ant-design/icons';
const { RangePicker } = DatePicker;

const ReturnIcon = () => (
  <svg
    style={{ float: 'left', marginRight: '3px', marginTop: '1px' }}
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.9433 6.16426H2.71921L6.71847 2.60947C6.91205 2.43741 6.92949 2.14102 6.75742 1.94744C6.58536 1.75391 6.28902 1.7364 6.09542 1.90849L1.19417 6.26504C1.08541 6.35092 1.0155 6.48385 1.0155 6.63319C1.0155 6.6334 1.01552 6.63361 1.01552 6.63381C1.01552 6.63401 1.0155 6.63421 1.0155 6.63441C1.0155 6.64181 1.01574 6.6492 1.01609 6.65657C1.01611 6.65682 1.01611 6.65708 1.01611 6.65733C1.02225 6.78282 1.0785 6.90101 1.1729 6.98487L6.0954 11.3603C6.18473 11.4398 6.29595 11.4788 6.40674 11.4788C6.53603 11.4788 6.66478 11.4256 6.7574 11.3214C6.92946 11.1278 6.91203 10.8314 6.71845 10.6594L2.71645 7.10209H10.9433C12.6499 7.10209 14.0382 8.49046 14.0382 10.197C14.0382 11.9036 12.6499 13.2919 10.9433 13.2919H5.53634C5.27737 13.2919 5.06741 13.5019 5.06741 13.7609C5.06741 14.0198 5.27737 14.2298 5.53634 14.2298H10.9433C13.167 14.2298 14.9761 12.4207 14.9761 10.197C14.9761 7.97334 13.167 6.16426 10.9433 6.16426Z"
      fill="#3463FC"
    />
  </svg>
);
const BatchExportIcon = () => (
  <svg
    style={{ float: 'left', marginRight: '4px', marginTop: '1px' }}
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <path
      d="M12.6871 10.0422C12.4855 10.0454 12.323 10.211 12.3262 10.4125V11.3422C12.3262 11.7485 12.0043 12.0797 11.6074 12.0797H4.42461C4.02773 12.0797 3.70586 11.7485 3.70586 11.3422V4.70942C3.70586 4.30317 4.02773 3.97192 4.42461 3.97192H6.92461L7.85273 5.62349C7.92773 5.75474 8.06836 5.82192 8.20742 5.8063H11.6059C12.0027 5.8063 12.3246 6.13755 12.3246 6.5438V7.46567C12.3246 7.67036 12.4871 7.83599 12.6871 7.83599C12.8871 7.83599 13.048 7.67036 13.048 7.46567C13.048 7.4563 13.048 7.44692 13.0465 7.43755V6.5438C13.0465 5.72974 12.4027 5.0688 11.609 5.0688H8.37305L7.44648 3.42349C7.36367 3.27505 7.24492 3.23599 7.14023 3.23911L7.13867 3.23755H4.41992C3.62617 3.23755 2.98242 3.89849 2.98242 4.71255V11.3266C2.98242 12.1407 3.62617 12.8016 4.41992 12.8016H11.609C12.4027 12.8016 13.0465 12.1407 13.0465 11.3266V10.4407C13.048 10.4313 13.048 10.4219 13.048 10.4125C13.0496 10.3157 13.0121 10.2219 12.9434 10.1532C12.8762 10.0829 12.784 10.0438 12.6871 10.0422Z"
      fill="#3463FC"
    />
    <path
      d="M8.85371 7.3626C8.91152 7.3001 8.99121 7.26416 9.07715 7.26416C9.16152 7.26416 9.24277 7.3001 9.30059 7.3626L10.6318 8.77979L10.6396 8.7876H10.6412L10.649 8.79541C10.7693 8.92822 10.7693 9.13135 10.649 9.26416L9.31309 10.6892C9.25684 10.7517 9.17715 10.786 9.09277 10.7876C9.0084 10.7876 8.92871 10.7532 8.87246 10.6907L8.86621 10.6829C8.7459 10.5501 8.7459 10.347 8.86621 10.2142L9.6709 9.35479H6.01621C5.8459 9.35479 5.70684 9.20635 5.70684 9.0251C5.70684 8.84385 5.8459 8.69541 6.01621 8.69541H9.65684L8.85371 7.8376C8.73027 7.70479 8.73027 7.49697 8.85371 7.3626Z"
      fill="#3463FC"
    />
  </svg>
);
const EvaluationHistory = () => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState();
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [form] = Form.useForm();
  const [minScore, setMinScore] = useState(null);
  const [maxScore, setMaxScore] = useState(null);
  const [channelOptions, setChannelOptions] = useState([]);
  const [workRecordTypeList, setWorkRecordTypeList] = useState([]);
  const [scoreUserList, setScoreUserList] = useState([]);
  const [total, setTotal] = useState(0);
  // 定义统一的搜索参数状态
  const [searchParams, setSearchParams] = useState({
    assessmentFormName: '',
    channelId: '',
    ticketType: '',
    assessorId: '',
    assessedAgentId: '',
    ticketCode: '',
    scoreFirst: undefined,
    scoreLast: undefined,
    assessmentStartTime: '',
    assessmentEndTime: '',
  });
  const { assessmentId, versionId } = history.location.state || {};
  useEffect(() => {
    getAssessmentRecordPage(searchParams);
  }, [searchParams, current, pageSize]);
  // 获取评估人列表
  const getScoreUser = async () => {
    return new Promise((resolve, reject) => {
      dispatch({
        type: 'documentKnowledgeBase/queryDocumentKnowledgeAllUser',
        payload: '',
        callback: res => {
          if (res.data) {
            setScoreUserList(res.data);
            resolve();
          } else {
            reject('Failed to get score user list');
          }
        },
      });
    });
  };

  // 获取渠道列表
  const newGetChannels = async () => {
    return new Promise((resolve, reject) => {
      dispatch({
        type: 'channel/getChannelGroup',
        callback: res => {
          if (res.code === 200) {
            let result = [...res.data];
            setChannelOptions(result);
            resolve();
          } else {
            setLoading(false);
            reject(res.msg);
          }
        },
      });
    });
  };

  // 获取工单类型列表
  const queryWorkRecordType = () => {
    return new Promise((resolve, reject) => {
      dispatch({
        type: 'workOrderCenter/queryWorkRecordType',
        callback: response => {
          if (response.code == 200) {
            setWorkRecordTypeList(response.data);
            resolve();
          } else {
            notification.error({
              message: response.msg,
            });
            reject(response.msg);
          }
        },
      });
    });
  };

  const getAssessmentRecordPage = async (customParams = null) => {
    setLoading(true);
    try {
      // 如果传入了自定义参数，使用自定义参数，否则使用当前的searchParams状态
      const params = customParams || searchParams;
      params.assessmentId = assessmentId;
      params.assessmentVersionId = versionId;
      console.log(params);
      const res = await dispatch({
        type: 'smartQualityInspection/getAssessmentRecordPage',
        payload: {
          pageNum: current,
          pageSize: pageSize,
          ...params, // 展开参数
        },
        callback: res => {
          console.log(res);
          setDataSource(res.data.records);
          setTotal(res.data.total);
          setLoading(false);
        },
      });
    } catch (error) {
      console.error('Error fetching assessment records:', error);
      notification.error({
        message: getIntl().formatMessage({
          id: 'smart.quality.evaluation.history.load.error',
          defaultMessage: '加载数据失败',
        }),
      });
      setLoading(false);
    }
  };
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        await getScoreUser();
        await newGetChannels();
        await queryWorkRecordType();
        // 初始化时使用空的参数状态
        await getAssessmentRecordPage();
      } catch (error) {
        console.error('Error loading data:', error);
        notification.error({
          message: getIntl().formatMessage({
            id: 'smart.quality.evaluation.history.load.error',
            defaultMessage: '加载数据失败',
          }),
        });
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, []);
  const downloadPDF = (base64Data, filename = 'document.pdf') => {
    try {
      const binaryString = atob(base64Data);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      const blob = new Blob([bytes], { type: 'application/pdf' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      // 触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      // 清理
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('PDF下载失败:', error);
    } finally {
      setLoading(false);
    }
  };
  const handleExportPDF = record => {
    setLoading(true);
    dispatch({
      type: 'smartQualityInspection/exportAssessmentForm',
      payload: {
        assessmentId: record.assessmentRecordId,
      },
      callback: res => {
        console.log(res);
        if (res.code === 200) {
          downloadPDF(res.data, record.assessmentName + '.pdf');
        }
      },
    });
  };

  const handleSearch = values => {
    // 构建API请求参数
    const params = {
      assessmentFormName: values.assessmentFormName || '',
      channelId: values.channelId || '',
      ticketType: values.ticketType || '',
      assessorId: values.assessorId || '',
      assessedAgentId: values.assessedAgentId || '',
      ticketCode: values.ticketCode || '',
      scoreFirst: values.scoreFirst || null,
      scoreLast: values.scoreLast || null,
      assessmentStartTime: '',
      assessmentEndTime: '',
    };

    // 处理评估时间范围
    if (values.evaluationTime && values.evaluationTime.length === 2) {
      params.assessmentStartTime = values.evaluationTime[0].format(
        'YYYY-MM-DD',
      );
      params.assessmentEndTime = values.evaluationTime[1].format('YYYY-MM-DD');
    }
    console.log(params.scoreFirst, params.scoreLast);
    // 检查最大值和最小值必须同时存在或同时不存在 还有为null的情况
    if (
      (params.scoreFirst !== null && params.scoreLast === null) ||
      (params.scoreFirst === null && params.scoreLast !== null)
    ) {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'smart.quality.evaluation.history.score.both.required',
          defaultMessage: '最大值和最小值必须同时填写',
        }),
      });
      return;
    }
    if (
      params.scoreFirst !== null &&
      params.scoreLast !== null &&
      params.scoreFirst > params.scoreLast
    ) {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'smart.quality.evaluation.history.score.range.error',
          defaultMessage: '最大值不能小于最小值',
        }),
      });
      return;
    }

    console.log('Search params:', params);
    // 更新搜索参数状态
    setSearchParams(params);
    // 重置页码为第一页
    setCurrent(1);
  };

  const handleMinScoreChange = value => {
    console.log(value);
    if (value === null) {
      setMinScore(null);
      return;
    }
    setMinScore(value);
  };

  const handleMaxScoreChange = value => {
    if (value === null) {
      setMaxScore(null);
      return;
    }
    setMaxScore(value);
  };

  // Add custom score validation function
  const validateScoreFormat = (_, value) => {
    if (value === undefined || value === null) {
      return Promise.resolve();
    }

    // Check if it's a valid number with up to 2 decimal places
    const regEx = /^[0-9]+(\.[0-9]{1,2})?$/;
    if (value.toString().match(regEx)) {
      return Promise.resolve();
    }

    return Promise.reject(
      new Error(
        getIntl().formatMessage({
          id: 'smart.quality.evaluation.history.score.format.error',
          defaultMessage: '请输入有效的数值，最多支持2位小数',
        }),
      ),
    );
  };

  const columns = [
    {
      title: (
        <FormattedMessage
          id="smart.quality.evaluation.table.name"
          defaultMessage="评估表名称"
        />
      ),
      dataIndex: 'assessmentName',
      key: 'name',
      width: 150,
    },
    {
      title: (
        <FormattedMessage
          id="smart.quality.evaluation.table.channel"
          defaultMessage="渠道"
        />
      ),
      dataIndex: 'channelConfigName',
      key: 'channel',
      width: 220,
      render: (text, record) => {
        if (!text) {
          return (
            <div>
              <img
                src={AllchannelIcon}
                style={{
                  width: '12px',
                  height: '12px',
                  float: 'left',
                  marginTop: '3px',
                  marginRight: '5px',
                }}
              />
              <span
                style={{
                  fontSize: '12px',
                  fontWeight: '700',
                  color: '#333',
                }}
              >
                <FormattedMessage
                  id="smart.quality.evaluation.table.channel.all"
                  defaultMessage="所有渠道"
                />
              </span>
            </div>
          );
        } else {
          return (
            <div>
              <div>
                <span>{record.channelTypeName}</span>
                <span style={{ color: '#333', marginLeft: '4px' }}>/</span>
                <span
                  style={{
                    marginLeft: '4px',
                    color: '#3463FC',
                    fontSize: '12px',
                  }}
                >
                  {text}
                </span>
              </div>
            </div>
          );
        }
      },
    },
    {
      title: (
        <FormattedMessage
          id="smart.quality.evaluation.table.ticket.type"
          defaultMessage="工单类型"
        />
      ),
      dataIndex: 'ticketTypeName',
      key: 'ticketTypeName',
      width: 150,
    },
    {
      title: (
        <FormattedMessage
          id="smart.quality.evaluation.history.agent.name"
          defaultMessage="坐席名称"
        />
      ),
      dataIndex: 'assessedAgentName',
      key: 'agentName',
      width: 150,
    },
    {
      title: (
        <FormattedMessage
          id="smart.quality.evaluation.history.ticket.id"
          defaultMessage="工单ID"
        />
      ),
      dataIndex: 'ticketCode',
      key: 'ticketCode',
      width: 170,
    },
    // 评估时间
    {
      title: (
        <FormattedMessage
          id="smart.quality.evaluation.history.evaluation.time"
          defaultMessage="评估时间"
        />
      ),
      dataIndex: 'createTime',
      key: 'createTime',
      width: 150,
    },
    // 评估人
    {
      title: (
        <FormattedMessage
          id="smart.quality.evaluation.history.evaluator"
          defaultMessage="评估人"
        />
      ),
      dataIndex: 'assessorName',
      key: 'assessorName',
      width: 150,
    },
    // 得分
    {
      title: (
        <FormattedMessage
          id="smart.quality.evaluation.history.score"
          defaultMessage="得分"
        />
      ),
      dataIndex: 'score',
      key: 'score',
      width: 150,
      render: (text, record) => {
        return <span style={{ color: '#13C825' }}>{record.totalScore}</span>;
      },
    },
    {
      title: (
        <FormattedMessage
          id="smart.quality.evaluation.table.operation"
          defaultMessage="操作"
        />
      ),
      key: 'operation',
      fixed: 'right',
      width: 200,
      render: (_, record) => (
        <div className={styles.tableOperation}>
          <Link
            to={
              '/intelligentQualityInspection?ticketId=' +
              record.ticketId +
              '&assessmentStatus=' +
              record.assessmentStatus +
              '&assessorId=' +
              record.assessorId +
              '&assessmentRecordId=' +
              record.assessmentRecordId
            }
            target={'_blank'}
          >
            <Button type="link">
              <FormattedMessage
                id="work.order.management.table.detail"
                defaultMessage="详情"
              />
            </Button>
          </Link>
          <Button
            type="link"
            icon={<BatchExportIcon />}
            onClick={() => handleExportPDF(record)}
          >
            <FormattedMessage
              id="smart.quality.evaluation.history.export.pdf"
              defaultMessage="导出PDF"
            />
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className={styles.evaluationHistoryContainer}>
      <Button icon={<ReturnIcon />} onClick={() => history.goBack()}>
        <FormattedMessage
          id="smart.quality.evaluation.history.return.list"
          defaultMessage="返回列表"
        />
      </Button>
      <div className={styles.searchContainer}>
        <div className={styles.headerWithTitle}>
          <div className="blueBorder">
            <FormattedMessage
              id="smart.quality.evaluation.history.title"
              defaultMessage="评估历史记录"
            />
          </div>
        </div>
        <div className={styles.searchForm}>
          <Form
            form={form}
            layout="horizontal"
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
            onFinish={handleSearch}
          >
            <Row gutter={[24, 0]}>
              <Col span={8}>
                <Form.Item
                  label={
                    <FormattedMessage
                      id="smart.quality.evaluation.table.name"
                      defaultMessage="评估表名称"
                    />
                  }
                  name="assessmentFormName"
                >
                  <Input
                    placeholder={getIntl().formatMessage({
                      id: 'smart.quality.evaluation.history.name.placeholder',
                      defaultMessage: '请输入评估表名称',
                    })}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label={
                    <FormattedMessage
                      id="smart.quality.evaluation.table.channel"
                      defaultMessage="渠道"
                    />
                  }
                  name="channelId"
                >
                  <Select
                    placeholder={getIntl().formatMessage({
                      id:
                        'smart.quality.evaluation.history.channel.placeholder',
                      defaultMessage: '请选择渠道',
                    })}
                    showSearch
                    allowClear
                    optionFilterProp="children"
                  >
                    {channelOptions?.map(group =>
                      group.channelVOList?.map(channel => (
                        <Select.Option
                          key={channel.channelId}
                          value={channel.channelId}
                        >
                          {group.name}/{channel.name}
                        </Select.Option>
                      )),
                    )}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label={
                    <FormattedMessage
                      id="smart.quality.evaluation.table.ticket.type"
                      defaultMessage="工单类型"
                    />
                  }
                  name="ticketType"
                >
                  <Select
                    placeholder={getIntl().formatMessage({
                      id:
                        'smart.quality.evaluation.history.ticket.type.placeholder',
                      defaultMessage: '请选择工单类型',
                    })}
                    showSearch
                    allowClear
                    options={workRecordTypeList.map(item => ({
                      label: item.workRecordTypeName,
                      value: item.workRecordTypeId,
                    }))}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label={
                    <FormattedMessage
                      id="smart.quality.evaluation.history.agent.name"
                      defaultMessage="坐席名称"
                    />
                  }
                  name="assessedAgentId"
                >
                  <Select
                    placeholder={getIntl().formatMessage({
                      id:
                        'smart.quality.evaluation.history.agent.name.placeholder',
                      defaultMessage: '请选择坐席名称',
                    })}
                    showSearch
                    allowClear
                    options={scoreUserList.map(user => ({
                      label: user.userName,
                      value: user.userId,
                    }))}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label={
                    <FormattedMessage
                      id="smart.quality.evaluation.history.evaluation.time"
                      defaultMessage="评估时间"
                    />
                  }
                  name="evaluationTime"
                >
                  <RangePicker />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label={
                    <FormattedMessage
                      id="smart.quality.evaluation.history.evaluator"
                      defaultMessage="评估人"
                    />
                  }
                  name="assessorId"
                >
                  <Select
                    placeholder={getIntl().formatMessage({
                      id:
                        'smart.quality.evaluation.history.evaluator.placeholder',
                      defaultMessage: '请选择评估人',
                    })}
                    showSearch
                    allowClear
                    optionFilterProp="children"
                  >
                    {scoreUserList.map(user => (
                      <Select.Option key={user.userId} value={user.userId}>
                        {user.userName}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label={
                    <FormattedMessage
                      id="smart.quality.evaluation.history.ticket.id"
                      defaultMessage="工单编号"
                    />
                  }
                  name="ticketCode"
                >
                  <Input
                    placeholder={getIntl().formatMessage({
                      id:
                        'smart.quality.evaluation.history.ticket.id.placeholder',
                      defaultMessage: '请输入工单编号',
                    })}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label={
                    <FormattedMessage
                      id="smart.quality.evaluation.history.score.range"
                      defaultMessage="得分区间"
                    />
                  }
                >
                  <Input.Group compact>
                    <Form.Item
                      name="scoreFirst"
                      noStyle
                      // rules={[
                      //   {
                      //     validator: (_, value) => {
                      //       if (
                      //         value !== null &&
                      //         maxScore !== null &&
                      //         value > maxScore
                      //       ) {
                      //         return Promise.reject(
                      //           new Error(
                      //             getIntl().formatMessage({
                      //               id:
                      //                 'smart.quality.evaluation.history.score.min.error',
                      //               defaultMessage: '最小值不能大于最大值',
                      //             }),
                      //           ),
                      //         );
                      //       }
                      //       return Promise.resolve();
                      //     },
                      //   },
                      //   {
                      //     validator: validateScoreFormat,
                      //   },
                      // ]}
                    >
                      <InputNumber
                        placeholder={getIntl().formatMessage({
                          id: 'smart.quality.evaluation.history.score.min',
                          defaultMessage: '最小值',
                        })}
                        min={0}
                        precision={2}
                        step={0.01}
                        style={{ width: '45%' }}
                        onChange={handleMinScoreChange}
                        keyboard={false}
                      />
                    </Form.Item>
                    <span className={styles.rangeSeparator}>-</span>
                    <Form.Item
                      name="scoreLast"
                      noStyle
                      rules={[
                        {
                          validator: (_, value) => {
                            if (
                              value !== null &&
                              minScore !== null &&
                              value < minScore
                            ) {
                              return Promise.reject(
                                new Error(
                                  getIntl().formatMessage({
                                    id:
                                      'smart.quality.evaluation.history.score.max.error',
                                    defaultMessage: '最大值不能小于最小值',
                                  }),
                                ),
                              );
                            }
                            return Promise.resolve();
                          },
                        },
                        {
                          validator: validateScoreFormat,
                        },
                      ]}
                    >
                      <InputNumber
                        placeholder={getIntl().formatMessage({
                          id: 'smart.quality.evaluation.history.score.max',
                          defaultMessage: '最大值',
                        })}
                        min={0}
                        precision={2}
                        step={0.01}
                        style={{ width: '45%' }}
                        onChange={handleMaxScoreChange}
                        keyboard={false}
                      />
                    </Form.Item>
                  </Input.Group>
                </Form.Item>
              </Col>
              <Col span={8} style={{ textAlign: 'right' }}>
                <Form.Item wrapperCol={{ span: 24 }}>
                  <Button
                    icon={<SearchOutlined />}
                    type="primary"
                    htmlType="submit"
                  >
                    <FormattedMessage
                      id="smart.quality.evaluation.history.search"
                      defaultMessage="搜索"
                    />
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
      </div>

      <div className={styles.tableContainer}>
        <Spin spinning={loading}>
          <Table
            dataSource={dataSource}
            columns={columns}
            scroll={{ x: 'max-content' }}
            pagination={{
              total,
              pageSize,
              current,
              showSizeChanger: true,
              onChange: (page, pageSize) => {
                setCurrent(page);
                setPageSize(pageSize);
              },
              showTotal: total => (
                <FormattedMessage
                  id="smart.quality.evaluation.history.page.total.num"
                  defaultMessage={`共 ${total} 条`}
                  values={{ total }}
                />
              ),
            }}
          />
        </Spin>
      </div>
    </div>
  );
};

export default EvaluationHistory;
