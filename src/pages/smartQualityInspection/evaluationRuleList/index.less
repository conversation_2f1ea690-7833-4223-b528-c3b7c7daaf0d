.evaluationRuleList {
  margin: 20px;
  min-height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;

  .evaluationRuleListHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .headerLeft {
      display: flex;
      align-items: center;
    }

    .headerRight {
      display: flex;
      align-items: center;

      .deployStatus {
        margin-right: 20px;
        display: flex;
        align-items: center;

        .statusLabel {
          margin-right: 8px;
          color: #666;
        }

        .statusValue {
          &.published {
            color: #52c41a;
          }

          &.unpublished {
            color: #f22417;
          }
        }
      }

      .buttonGroup {
        display: flex;
        gap: 20px;

        button {
          padding: 5px 24px;
        }
      }
    }
  }
}
:global(.ant-dropdown) {
  :global(.ant-dropdown-menu) {
    // max-height: 500px;
    // overflow-y: auto;
    &::-webkit-scrollbar {
      width: 6px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #d9d9d9;
      border-radius: 3px;
    }
    &::-webkit-scrollbar-track {
      background-color: #f0f0f0;
    }
  }
}

.canvasFlow_header_deploy_dropdown {
  :global(.ant-dropdown-menu) {
    max-height: 500px;
    overflow-y: auto;
  }
}

.evaluationRuleListContainer {
  padding: 20px;
  flex: 1;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0px 2px 30px 0px rgba(0, 0, 0, 0.02);
  display: flex;

  .ruleListTreeContainer {
    margin-right: 20px;
    min-width: 22%;

    .addTopLevelContainer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 12px;
      background-color: #f9f9f9;
      border-radius: 4px;
      margin-bottom: 12px;

      .assessmentTitle {
        font-weight: 700;
        font-size: 12px;
        color: #333;
        svg {
          vertical-align: middle;
          margin-right: 4px;
        }
      }

      .addTopLevelButton {
        cursor: pointer;
      }
    }

    .customTree {
      :global {
        // 所有树节点的基础样式
        .ant-tree-treenode {
          display: flex;
          padding: 8px 0px 8px 0px;
          justify-content: space-between;
          align-items: center;
          width: 100%;
          align-items: center;
          position: relative;

          &:hover {
            background-color: rgba(228, 203, 255, 0.5);
          }

          .ant-tree-node-selected {
            background-color: transparent;
          }

          // 选中的节点 - 使用红色左边框
          &.tree-node-selected {
            transition: background-color 0.3s ease;
            background-color: #e4cbff !important;
            border-radius: 4px 4px 0 0;
          }

          // 选中节点的子节点 - 背景为黄色
          &.tree-node-highlighted {
            background-color: rgba(228, 203, 255, 0.5) !important;
          }
        }

        // 节点内容区域
        .ant-tree-node-content-wrapper {
          height: 100%;
          padding: 0 8px;
          margin-left: 0 !important; // 移除左边距
          flex: 1;
          display: flex;
          align-items: center;

          &.ant-tree-node-content-wrapper-selected {
            background-color: transparent !important; // 选中时内容区域背景为透明
          }

          &:hover {
            background-color: transparent;
          }
        }

        // 展开/收缩图标
        .ant-tree-switcher {
          display: flex;
          align-items: center;
          justify-content: center;
          line-height: 24px;
          width: 24px;
          z-index: 1;

          .anticon {
            font-size: 12px;
          }
        }

        // 叶子节点的样式调整
        .ant-tree-switcher-noop {
          width: 24px;
        }
      }
    }
  }

  .ruleListTableContainer {
    width: 78%;
    padding: 20px;
    border-left: 1px solid #e5e5e5;
    display: flex;
    flex-direction: column;

    .tableHeaderContainer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;

      .searchContainer {
        width: 300px;
        border-radius: 4px;
        overflow: hidden;
      }
      .addButton {
        span {
          margin-right: 5px;
        }
      }
    }
    .operationColumn {
      :global {
        .ant-btn {
          border: none;
          &:last-child {
            color: #f22417;
          }
        }
      }
      img {
        width: 16px;
        height: 16px;
      }
      img,
      span {
        margin-right: 4px;
      }
    }
  }
}

// 自定义节点包装器
.nodeWrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;

  .nodeContent {
    display: flex;
    align-items: center;
    flex: 1;

    .nodeTitle {
      margin-left: 8px;
      position: relative;
      display: inline-flex;
      align-items: center;
      .nodeTitleText {
        max-width: 140px;
        white-space: pre-wrap;
        word-break: break-all;
        font-size: 12px;
      }
      .editIcon {
        margin-left: 8px;
        color: #333;
        cursor: pointer;
        font-size: 14px;
        display: none; /* 默认隐藏编辑图标 */

        &:hover {
          color: #1890ff;
        }
      }

      &.editing {
        background-color: #fff;
        border-radius: 2px;
        padding: 0 4px;
      }
    }
  }

  .nodeActions {
    display: none; // 默认隐藏按钮
    width: 60px; // 固定操作区域宽度
    justify-content: flex-end;
    position: absolute;
    right: 8px;

    .actionBtn {
      margin-left: 12px;
      cursor: pointer;

      &.addBtn {
        color: #333;
      }

      &.deleteBtn {
        color: #f22417;
      }
    }
  }
}

/* 当悬停在树节点上时显示编辑图标 */
:global(.ant-tree-treenode:hover) {
  .nodeActions {
    display: flex !important;
  }

  .nodeTitle .editIcon {
    display: inline-flex !important;
  }
}

.scoreSign {
  color: #3463fc;
  font-family: 'MicrosoftYaHei';
  font-size: 12px;
  font-style: normal;
  font-weight: 700;
  margin-right: 4px;
}
