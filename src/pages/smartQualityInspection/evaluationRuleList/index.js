import React, { useState, useRef, useEffect } from 'react';
import {
  Spin,
  Tree,
  Input,
  Button,
  Table,
  Space,
  Dropdown,
  notification,
  Modal,
  Popconfirm,
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  SearchOutlined,
  EditOutlined,
  DownOutlined,
} from '@ant-design/icons';
import {
  FormattedMessage,
  useIntl,
  history,
  useDispatch,
  useSelector,
  getIntl,
} from 'umi';
import styles from './index.less';
import NewDeleteIcon from '../../../assets/new-delete-list-icon.png';
const levelOneSvg = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
  >
    <rect
      x="6"
      y="2.53033"
      width="5.25"
      height="5.25"
      rx="0.375"
      transform="rotate(45 6 2.53033)"
      stroke="#333333"
      strokeWidth="0.75"
    />
    <rect
      x="6"
      y="4.08838"
      width="3.04659"
      height="3.04659"
      rx="0.75"
      transform="rotate(45 6 4.08838)"
      fill="#333333"
    />
  </svg>
);
const levelTwoSvg = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
  >
    <rect
      x="4.69409"
      y="2.53033"
      width="4.47408"
      height="4.47408"
      rx="0.375"
      transform="rotate(45 4.69409 2.53033)"
      stroke="#333333"
      strokeWidth="0.75"
    />
    <rect
      x="7.30591"
      y="2.53033"
      width="4.47408"
      height="4.47408"
      rx="0.375"
      transform="rotate(45 7.30591 2.53033)"
      stroke="#333333"
      strokeWidth="0.75"
    />
  </svg>
);
const levelThreeSvg = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
  >
    <rect
      x="4.69409"
      y="2.53033"
      width="4.47408"
      height="4.47408"
      rx="0.375"
      transform="rotate(45 4.69409 2.53033)"
      stroke="#333333"
      strokeWidth="0.75"
    />
    <rect
      x="7.30591"
      y="2.53033"
      width="4.47408"
      height="4.47408"
      rx="0.375"
      transform="rotate(45 7.30591 2.53033)"
      stroke="#333333"
      strokeWidth="0.75"
    />
  </svg>
);
const AiSvg = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <path
      d="M4.28629 3.98035C4.54335 3.98041 4.76921 4.10593 4.91032 4.29871C5.01042 4.35464 5.09034 4.44079 5.13883 4.5448L5.15739 4.58875L8.44059 13.9452C8.54687 14.2467 8.35716 14.5837 8.01774 14.6981C7.69558 14.8067 7.35425 14.6764 7.22965 14.4042L7.21207 14.3602L6.36051 11.9354H2.10856L1.25797 14.3602C1.15158 14.6616 0.791061 14.8131 0.452309 14.6981C0.129587 14.5893 -0.0573393 14.2796 0.0157858 13.9901L0.0294577 13.9442L3.31266 4.58972C3.34527 4.50243 3.39821 4.42393 3.46793 4.36218C3.5376 4.30049 3.62165 4.25698 3.71207 4.23523C3.78435 4.1548 3.8731 4.09072 3.97184 4.04675C4.07059 4.00279 4.17823 3.98021 4.28629 3.98035ZM10.3244 7.96472C10.6653 7.96472 10.9452 8.13531 10.9728 8.35144L10.9748 8.38562V14.2772C10.9746 14.51 10.6826 14.6981 10.3244 14.6981C9.98342 14.698 9.70376 14.5282 9.67692 14.3114L9.67497 14.2772V8.3866C9.67503 8.15384 9.96575 7.96486 10.3244 7.96472ZM2.55485 10.661H5.91325L4.23454 5.87781L2.55485 10.661ZM11.2902 0.970583C11.4306 1.00832 11.5504 1.1006 11.6232 1.22644C11.696 1.35256 11.7155 1.50276 11.6779 1.64343C11.6402 1.78422 11.5483 1.90454 11.422 1.97742C11.3209 2.03576 11.2047 2.0598 11.09 2.04773C10.8644 2.2192 10.5314 2.48236 10.3088 2.70496C10.1461 2.86763 9.96216 3.08959 9.80778 3.28504C9.81644 3.29847 9.82514 3.31215 9.83317 3.32605C9.95944 3.54476 9.996 3.80386 9.93668 4.04871C10.089 4.15752 10.2492 4.26479 10.3908 4.34656C10.7174 4.53511 11.2117 4.73956 11.4543 4.83582C11.4645 4.82923 11.4749 4.8224 11.4855 4.81629C11.575 4.76467 11.6741 4.73114 11.7765 4.71765C11.8789 4.70422 11.9835 4.71146 12.0832 4.73816C12.1828 4.76492 12.2767 4.81113 12.3586 4.8739C12.4403 4.93671 12.5091 5.0151 12.5607 5.10437C12.6124 5.19382 12.6458 5.29298 12.6593 5.39539C12.6728 5.49783 12.6665 5.60222 12.6398 5.70203C12.6131 5.80186 12.566 5.89542 12.5031 5.97742C12.4402 6.05928 12.362 6.12794 12.2726 6.17957C12.1831 6.23125 12.0841 6.26471 11.9816 6.2782C11.8791 6.29168 11.7748 6.28541 11.675 6.25867C11.5752 6.2319 11.4815 6.18579 11.3996 6.12293C11.3176 6.06001 11.2491 5.98096 11.1974 5.89148C11.1459 5.8022 11.1123 5.70363 11.0988 5.60144C11.0939 5.56402 11.0915 5.52574 11.092 5.48816C10.8914 5.32917 10.4591 4.99531 10.1271 4.80359C9.98252 4.72012 9.80486 4.63339 9.63102 4.55457C9.58272 4.59704 9.52934 4.63521 9.47282 4.66785C9.24732 4.79793 8.97917 4.83289 8.7277 4.7655C8.47642 4.69801 8.26214 4.53379 8.132 4.30847C8.00187 4.083 7.96604 3.81483 8.03336 3.56336C8.10079 3.31182 8.26585 3.09688 8.49137 2.96668C8.71685 2.83658 8.98503 2.80168 9.23649 2.86902C9.25482 2.87393 9.27326 2.87969 9.29118 2.88562C9.50105 2.72201 9.75455 2.51307 9.93571 2.33191C10.161 2.10663 10.4285 1.76948 10.5998 1.54382C10.5951 1.48257 10.6002 1.41973 10.6164 1.35925C10.6541 1.21853 10.747 1.09812 10.8732 1.02527C10.9993 0.952668 11.1497 0.932957 11.2902 0.970583ZM8.95524 3.28504C8.66251 3.28508 8.42505 3.52258 8.42497 3.81531C8.42509 4.108 8.66253 4.34553 8.95524 4.34558C9.2478 4.34537 9.48539 4.10789 9.48551 3.81531C9.48543 3.52269 9.24783 3.28525 8.95524 3.28504Z"
      fill="#333333"
    />
    <path
      d="M4.28629 3.98035C4.54335 3.98041 4.76921 4.10593 4.91032 4.29871C5.01042 4.35464 5.09034 4.44079 5.13883 4.5448L5.15739 4.58875L8.44059 13.9452C8.54687 14.2467 8.35716 14.5837 8.01774 14.6981C7.69558 14.8067 7.35425 14.6764 7.22965 14.4042L7.21207 14.3602L6.36051 11.9354H2.10856L1.25797 14.3602C1.15158 14.6616 0.791061 14.8131 0.452309 14.6981C0.129587 14.5893 -0.0573393 14.2796 0.0157858 13.9901L0.0294577 13.9442L3.31266 4.58972C3.34527 4.50243 3.39821 4.42393 3.46793 4.36218C3.5376 4.30049 3.62165 4.25698 3.71207 4.23523C3.78435 4.1548 3.8731 4.09072 3.97184 4.04675C4.07059 4.00279 4.17823 3.98021 4.28629 3.98035ZM10.3244 7.96472C10.6653 7.96472 10.9452 8.13531 10.9728 8.35144L10.9748 8.38562V14.2772C10.9746 14.51 10.6826 14.6981 10.3244 14.6981C9.98342 14.698 9.70376 14.5282 9.67692 14.3114L9.67497 14.2772V8.3866C9.67503 8.15384 9.96575 7.96486 10.3244 7.96472ZM2.55485 10.661H5.91325L4.23454 5.87781L2.55485 10.661ZM11.2902 0.970583C11.4306 1.00832 11.5504 1.1006 11.6232 1.22644C11.696 1.35256 11.7155 1.50276 11.6779 1.64343C11.6402 1.78422 11.5483 1.90454 11.422 1.97742C11.3209 2.03576 11.2047 2.0598 11.09 2.04773C10.8644 2.2192 10.5314 2.48236 10.3088 2.70496C10.1461 2.86763 9.96216 3.08959 9.80778 3.28504C9.81644 3.29847 9.82514 3.31215 9.83317 3.32605C9.95944 3.54476 9.996 3.80386 9.93668 4.04871C10.089 4.15752 10.2492 4.26479 10.3908 4.34656C10.7174 4.53511 11.2117 4.73956 11.4543 4.83582C11.4645 4.82923 11.4749 4.8224 11.4855 4.81629C11.575 4.76467 11.6741 4.73114 11.7765 4.71765C11.8789 4.70422 11.9835 4.71146 12.0832 4.73816C12.1828 4.76492 12.2767 4.81113 12.3586 4.8739C12.4403 4.93671 12.5091 5.0151 12.5607 5.10437C12.6124 5.19382 12.6458 5.29298 12.6593 5.39539C12.6728 5.49783 12.6665 5.60222 12.6398 5.70203C12.6131 5.80186 12.566 5.89542 12.5031 5.97742C12.4402 6.05928 12.362 6.12794 12.2726 6.17957C12.1831 6.23125 12.0841 6.26471 11.9816 6.2782C11.8791 6.29168 11.7748 6.28541 11.675 6.25867C11.5752 6.2319 11.4815 6.18579 11.3996 6.12293C11.3176 6.06001 11.2491 5.98096 11.1974 5.89148C11.1459 5.8022 11.1123 5.70363 11.0988 5.60144C11.0939 5.56402 11.0915 5.52574 11.092 5.48816C10.8914 5.32917 10.4591 4.99531 10.1271 4.80359C9.98252 4.72012 9.80486 4.63339 9.63102 4.55457C9.58272 4.59704 9.52934 4.63521 9.47282 4.66785C9.24732 4.79793 8.97917 4.83289 8.7277 4.7655C8.47642 4.69801 8.26214 4.53379 8.132 4.30847C8.00187 4.083 7.96604 3.81483 8.03336 3.56336C8.10079 3.31182 8.26585 3.09688 8.49137 2.96668C8.71685 2.83658 8.98503 2.80168 9.23649 2.86902C9.25482 2.87393 9.27326 2.87969 9.29118 2.88562C9.50105 2.72201 9.75455 2.51307 9.93571 2.33191C10.161 2.10663 10.4285 1.76948 10.5998 1.54382C10.5951 1.48257 10.6002 1.41973 10.6164 1.35925C10.6541 1.21853 10.747 1.09812 10.8732 1.02527C10.9993 0.952668 11.1497 0.932957 11.2902 0.970583ZM8.95524 3.28504C8.66251 3.28508 8.42505 3.52258 8.42497 3.81531C8.42509 4.108 8.66253 4.34553 8.95524 4.34558C9.2478 4.34537 9.48539 4.10789 9.48551 3.81531C9.48543 3.52269 9.24783 3.28525 8.95524 3.28504Z"
      fill="url(#paint0_linear_3225_12642)"
    />
    <path
      d="M14.0924 1.37118C14.0785 1.35691 14.0614 1.34615 14.0425 1.33981C14.0269 1.33457 14.0104 1.33246 13.9939 1.33362C13.9775 1.33478 13.9615 1.33918 13.9467 1.34656C13.932 1.35394 13.9188 1.36416 13.908 1.37664C13.8972 1.38912 13.889 1.4036 13.8838 1.41927L13.6014 2.27138L12.7515 2.5704C12.7264 2.57907 12.7047 2.59549 12.6895 2.6173C12.6743 2.63911 12.6664 2.66519 12.667 2.69176C12.6676 2.71834 12.6766 2.74404 12.6927 2.76517C12.7088 2.7863 12.7312 2.80175 12.7567 2.80931L13.5998 3.06442L13.8645 3.91183C13.8724 3.93719 13.8881 3.9594 13.9094 3.97524C13.9307 3.99108 13.9565 3.99974 13.983 3.99997C14.0096 4.00021 14.0355 3.992 14.0571 3.97653C14.0786 3.96107 14.0948 3.93914 14.1031 3.91392L14.3861 3.06285L15.2527 2.8046C15.2785 2.79726 15.3013 2.78181 15.3177 2.76052C15.3341 2.73924 15.3432 2.71324 15.3437 2.68637C15.3443 2.6595 15.3362 2.63316 15.3206 2.61125C15.3051 2.58934 15.2829 2.573 15.2574 2.56465L14.3871 2.26929L14.1224 1.42188C14.1166 1.40283 14.1063 1.38545 14.0924 1.37118Z"
      fill="#333333"
    />
    <path
      d="M14.0924 1.37118C14.0785 1.35691 14.0614 1.34615 14.0425 1.33981C14.0269 1.33457 14.0104 1.33246 13.9939 1.33362C13.9775 1.33478 13.9615 1.33918 13.9467 1.34656C13.932 1.35394 13.9188 1.36416 13.908 1.37664C13.8972 1.38912 13.889 1.4036 13.8838 1.41927L13.6014 2.27138L12.7515 2.5704C12.7264 2.57907 12.7047 2.59549 12.6895 2.6173C12.6743 2.63911 12.6664 2.66519 12.667 2.69176C12.6676 2.71834 12.6766 2.74404 12.6927 2.76517C12.7088 2.7863 12.7312 2.80175 12.7567 2.80931L13.5998 3.06442L13.8645 3.91183C13.8724 3.93719 13.8881 3.9594 13.9094 3.97524C13.9307 3.99108 13.9565 3.99974 13.983 3.99997C14.0096 4.00021 14.0355 3.992 14.0571 3.97653C14.0786 3.96107 14.0948 3.93914 14.1031 3.91392L14.3861 3.06285L15.2527 2.8046C15.2785 2.79726 15.3013 2.78181 15.3177 2.76052C15.3341 2.73924 15.3432 2.71324 15.3437 2.68637C15.3443 2.6595 15.3362 2.63316 15.3206 2.61125C15.3051 2.58934 15.2829 2.573 15.2574 2.56465L14.3871 2.26929L14.1224 1.42188C14.1166 1.40283 14.1063 1.38545 14.0924 1.37118Z"
      fill="url(#paint1_linear_3225_12642)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_3225_12642"
        x1="-1.6857e-06"
        y1="8.32484"
        x2="8.52394"
        y2="3.01671"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#187CFC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_3225_12642"
        x1="12.667"
        y1="2.75962"
        x2="14.376"
        y2="1.59697"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#187CFC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
    </defs>
  </svg>
);
const ManualSvg = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <path
      d="M7.9998 4.19059C10.1038 4.19059 11.8093 5.89612 11.8093 8.00012H10.6665C10.6665 7.47659 10.5124 6.96466 10.2234 6.52813C9.93437 6.0916 9.52328 5.74978 9.04134 5.54531C8.55941 5.34083 8.02794 5.28274 7.51321 5.37826C6.99848 5.47379 6.52325 5.71872 6.14678 6.08251C5.7703 6.44629 5.50923 6.91285 5.39612 7.424C5.28301 7.93516 5.32286 8.4683 5.5107 8.95697C5.69854 9.44563 6.02606 9.86819 6.45243 10.172C6.8788 10.4758 7.38516 10.6473 7.90837 10.6653L7.9998 10.6668C10.4158 10.6668 12.4569 12.2733 13.1122 14.4763H11.9046C11.295 12.9155 9.77656 11.8096 7.9998 11.8096C6.22304 11.8096 4.70456 12.9155 4.09504 14.4763H2.88742C3.10785 13.7399 3.48446 13.0596 3.99158 12.4819C4.49871 11.9041 5.12442 11.4425 5.82609 11.1285C5.32083 10.7781 4.90806 10.3105 4.62315 9.76564C4.33825 9.22078 4.18972 8.61496 4.19028 8.00012C4.19028 5.89612 5.8958 4.19059 7.9998 4.19059ZM7.9998 1.52393C10.8135 1.52393 13.1183 3.70297 13.319 6.46526C13.6425 6.70298 13.8826 7.03682 14.0051 7.41911C14.1275 7.80141 14.126 8.21262 14.0009 8.59403C13.8757 8.97544 13.6332 9.30756 13.3081 9.54297C12.9829 9.77838 12.5917 9.90504 12.1903 9.90488L9.71409 9.90526C9.59416 10.0652 9.42695 10.1833 9.23615 10.2429C9.04536 10.3025 8.84064 10.3005 8.65101 10.2373C8.46138 10.1741 8.29645 10.0529 8.17957 9.8907C8.0627 9.72854 7.9998 9.53372 7.9998 9.33383C7.9998 9.13394 8.0627 8.93912 8.17957 8.77696C8.29645 8.61481 8.46138 8.49353 8.65101 8.43032C8.84064 8.36711 9.04536 8.36517 9.23615 8.42477C9.42695 8.48437 9.59416 8.60249 9.71409 8.7624L12.1903 8.76202C12.3923 8.76202 12.5861 8.68175 12.729 8.53886C12.8719 8.39598 12.9522 8.20219 12.9522 8.00012C12.9522 7.79805 12.8719 7.60425 12.729 7.46137C12.5861 7.31848 12.3923 7.23821 12.1903 7.23821V6.85726C12.1903 4.54297 10.3141 2.66678 7.9998 2.66678C5.68552 2.66678 3.80933 4.54297 3.80933 6.85726V7.23821C3.60726 7.23821 3.41346 7.31848 3.27058 7.46137C3.12769 7.60425 3.04742 7.79805 3.04742 8.00012C3.04742 8.20219 3.12769 8.39598 3.27058 8.53886C3.41346 8.68175 3.60726 8.76202 3.80933 8.76202V9.90488C3.408 9.90493 3.01691 9.77823 2.69186 9.54284C2.36682 9.30745 2.12442 8.9754 1.99928 8.59409C1.87413 8.21277 1.87262 7.80167 1.99496 7.41945C2.11731 7.03722 2.35725 6.70341 2.68056 6.46564C2.88133 3.70297 5.18609 1.52393 7.9998 1.52393Z"
      fill="url(#paint0_linear_2895_1647)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_2895_1647"
        x1="1.9043"
        y1="8.45172"
        x2="9.99729"
        y2="3.28904"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#187CFC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
    </defs>
  </svg>
);
const CustomTreeNode = ({
  nodeData,
  onRename,
  onAddChild,
  onDelete,
  checkLatestVersion,
  setLoading,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [inputValue, setInputValue] = useState(nodeData.categoryName);
  const inputRef = useRef(null);
  const dispatch = useDispatch();
  const { evaluationFormData } = useSelector(
    state => state.smartQualityInspection,
  );
  // 当节点数据更新时更新输入值
  useEffect(() => {
    setInputValue(nodeData.categoryName);
  }, [nodeData.categoryName]);
  // 根据节点级别获取对应的 SVG 图标
  const getLevelIcon = level => {
    switch (level) {
      case 1:
        return levelOneSvg;
      case 2:
        return levelTwoSvg;
      case 3:
        return levelThreeSvg;
      default:
        return levelOneSvg;
    }
  };

  const handleEditClick = e => {
    e.stopPropagation(); // 阻止事件冒泡，避免触发选择
    if (!checkLatestVersion()) {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'smart.quality.evaluation.rule.version.current.tip',
          defaultMessage: '请选择最新版本',
        }),
      });
      return;
    }
    setIsEditing(true);
    setTimeout(() => {
      inputRef.current && inputRef.current.focus();
    }, 100);
  };

  const handleInputChange = e => {
    setInputValue(e.target.value);
  };

  const handleInputBlur = () => {
    if (inputValue.trim() === '') {
      setInputValue(nodeData.categoryName);
      notification.warning({
        message: '名称不能为空',
      });
    } else if (inputValue !== nodeData.categoryName) {
      console.log(nodeData);
      onRename(nodeData.categoryId, inputValue);
    }
    setIsEditing(false);
  };

  const handleKeyPress = e => {
    if (e.key === 'Enter') {
      handleInputBlur();
    }
  };

  const handleAddClick = e => {
    e.stopPropagation();
    if (!checkLatestVersion()) {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'smart.quality.evaluation.rule.version.current.tip',
          defaultMessage: '请选择最新版本',
        }),
      });
      return;
    }
    onAddChild(nodeData.categoryId);
  };

  const handleDeleteClick = e => {
    e.stopPropagation();
    if (!checkLatestVersion()) {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'smart.quality.evaluation.rule.version.current.tip',
          defaultMessage: '请选择最新版本',
        }),
      });
      return;
    }

    // 检查当前分类是否有规则以及子级是否有规则
    const checkCategoryHasRules = async categoryId => {
      return new Promise(resolve => {
        dispatch({
          type: 'smartQualityInspection/getRuleTableList',
          payload: {
            categoryId: categoryId,
            pageNum: 1,
            pageSize: 1, // 只需要知道是否有规则，所以只查询一条
            assessmentId: evaluationFormData.assessmentId,
            versionId: evaluationFormData?.versionId || null,
          },
          callback: res => {
            if (res && res.code == 200) {
              // 如果有规则，返回true
              resolve(res.data.total > 0);
            } else {
              resolve(false);
            }
          },
        });
      });
    };

    // 递归检查子级是否有规则
    const checkChildrenHasRules = async node => {
      if (!node.children || node.children.length === 0) {
        return false;
      }

      for (const child of node.children) {
        // 检查当前子级是否有规则
        const hasRules = await checkCategoryHasRules(child.categoryId);
        if (hasRules) {
          return true;
        }

        // 递归检查子级的子级
        const childrenHasRules = await checkChildrenHasRules(child);
        if (childrenHasRules) {
          return true;
        }
      }

      return false;
    };

    // 执行检查
    const checkAndDelete = async () => {
      setLoading(true);
      try {
        // 检查当前分类是否有规则
        const hasRules = await checkCategoryHasRules(nodeData.categoryId);

        // 检查子级是否有规则
        const childrenHasRules = await checkChildrenHasRules(nodeData);

        setLoading(false);

        if (hasRules || childrenHasRules) {
          // 如果当前分类或子级有规则，显示警告提示
          Modal.confirm({
            title: getIntl().formatMessage({
              id: 'smart.quality.evaluation.rule.delete.category.tip.title',
              defaultMessage: '提示',
            }),
            content: hasRules
              ? getIntl().formatMessage({
                  id: 'smart.quality.evaluation.rule.delete.category.tip',
                  defaultMessage:
                    '当前分类下已有规则，继续删除将同时删除该分类下所有规则',
                })
              : getIntl().formatMessage({
                  id:
                    'smart.quality.evaluation.rule.delete.category.children.tip',
                  defaultMessage:
                    '当前分类的子级分类下已有规则，继续删除将同时删除所有子级分类及其规则',
                }),
            onOk: () => {
              onDelete(nodeData.categoryId);
            },
          });
        } else {
          // 如果没有规则，直接删除
          onDelete(nodeData.categoryId);
        }
      } catch (error) {
        setLoading(false);
        console.error('检查分类规则时出错:', error);
      }
    };

    // 开始检查
    checkAndDelete();
  };

  return (
    <div className={styles.nodeWrapper}>
      <div className={styles.nodeContent}>
        <span className={styles.levelIcon}>{getLevelIcon(nodeData.level)}</span>
        {isEditing ? (
          <Input
            ref={inputRef}
            value={inputValue}
            onChange={handleInputChange}
            onBlur={handleInputBlur}
            onKeyPress={handleKeyPress}
            className={styles.nodeTitle + ' ' + styles.editing}
            onClick={e => e.stopPropagation()}
          />
        ) : (
          <span className={styles.nodeTitle}>
            <span className={styles.nodeTitleText}>
              {nodeData.categoryName}
            </span>
            <EditOutlined
              className={styles.editIcon}
              onClick={handleEditClick}
            />
          </span>
        )}
      </div>
      <div className={styles.nodeActions}>
        {nodeData.level < 3 && (
          <PlusOutlined
            className={styles.actionBtn + ' ' + styles.addBtn}
            onClick={handleAddClick}
          />
        )}
        <DeleteOutlined
          className={styles.actionBtn + ' ' + styles.deleteBtn}
          onClick={handleDeleteClick}
        />
      </div>
    </div>
  );
};

const EvaluationRuleList = () => {
  const dispatch = useDispatch();
  const intl = useIntl();
  const [loading, setLoading] = useState(false);
  // 从location.state获取表单数据，从全局状态获取isEditMode
  const { isEditMode, evaluationFormData } = useSelector(
    state => state.smartQualityInspection,
  );
  const [currentTreeData, setCurrentTreeData] = useState({});
  // 添加当前选中的版本ID状态
  const [selectedVersionId, setSelectedVersionId] = useState(null);

  const [treeData, setTreeData] = useState([]);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [expandedKeys, setExpandedKeys] = useState(['1', '1-1']);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [highlightedChildKeys, setHighlightedChildKeys] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [ruleData, setRuleData] = useState([]);
  useEffect(() => {
    // 从sessionStorage恢复数据
    const savedFormData = sessionStorage.getItem('evaluationFormData');
    // 检查是否已经执行过初始化函数
    const initialFunctionsExecuted = sessionStorage.getItem(
      'initialFunctionsExecuted',
    );
    // 恢复evaluationFormData和isEditMode
    if (
      savedFormData &&
      (!evaluationFormData || Object.keys(evaluationFormData).length === 0)
    ) {
      try {
        const parsedData = JSON.parse(savedFormData);
        const { isEditMode, ...formDataWithoutEditMode } = parsedData;
        // 将恢复的数据设置到Redux状态
        dispatch({
          type: 'smartQualityInspection/setEvaluationFormData',
          payload: formDataWithoutEditMode,
        });
        // 设置isEditMode状态
        if (isEditMode !== undefined) {
          dispatch({
            type: 'smartQualityInspection/setEditMode',
            payload: isEditMode,
          });
        }
        // 标记为需要执行初始化函数，但由下一个useEffect触发
        sessionStorage.setItem('needInitialization', 'true');
      } catch (error) {
        console.error('解析sessionStorage中的evaluationFormData失败:', error);
        // 如果解析失败，并且还没执行过初始化函数，则直接执行
        if (!initialFunctionsExecuted) {
          if (isEditMode && evaluationFormData?.versionId) {
            getAssessmentVersionList();
          }
          getRuleTableList();
          getTreeData();
          // 标记为已执行
          sessionStorage.setItem('initialFunctionsExecuted', 'true');
        }
      }
    } else {
      // 如果不需要从sessionStorage恢复，并且还没执行过初始化函数，则直接执行
      if (!initialFunctionsExecuted) {
        if (isEditMode && evaluationFormData?.versionId) {
          getAssessmentVersionList();
        }
        getRuleTableList();
        getTreeData();
        // 标记为已执行
        sessionStorage.setItem('initialFunctionsExecuted', 'true');
      }
    }

    // 组件卸载时清除标记
    return () => {
      sessionStorage.removeItem('initialFunctionsExecuted');
      sessionStorage.removeItem('needInitialization');
    };
  }, []);

  // 添加新的useEffect，监听evaluationFormData变化
  // 当从sessionStorage恢复数据后，这个useEffect会被触发
  useEffect(() => {
    // 只有当evaluationFormData有数据且需要初始化时才执行
    const needInitialization = sessionStorage.getItem('needInitialization');

    if (
      evaluationFormData &&
      Object.keys(evaluationFormData).length > 0 &&
      needInitialization === 'true'
    ) {
      // 获取版本列表
      if (isEditMode && evaluationFormData.versionId) {
        getAssessmentVersionList();
      }

      // 调用接口获取规则列表
      getRuleTableList();

      // 调用treeData接口
      getTreeData();

      // 清除标记，避免重复调用
      sessionStorage.removeItem('needInitialization');
      sessionStorage.setItem('initialFunctionsExecuted', 'true');
    }
  }, [evaluationFormData]);

  // 添加监听页面刷新事件，保存数据到sessionStorage
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (evaluationFormData && Object.keys(evaluationFormData).length > 0) {
        // 将evaluationFormData和isEditMode合并保存到sessionStorage
        const dataToSave = {
          ...evaluationFormData,
          isEditMode: isEditMode, // 保存isEditMode状态
        };
        sessionStorage.setItem(
          'evaluationFormData',
          JSON.stringify(dataToSave),
        );
      }
    };
    // 添加事件监听
    window.addEventListener('beforeunload', handleBeforeUnload);
    // 组件卸载时移除事件监听
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [evaluationFormData, isEditMode]);

  // 检查是否是最新版本，如果不是则提示并返回false
  const checkLatestVersion = () => {
    if (
      selectedVersionId &&
      dropDownItem.length > 0 &&
      selectedVersionId !== dropDownItem[0].key
    ) {
      return false;
    }
    return true;
  };

  // 加载指定版本的数据
  const loadVersionData = versionId => {
    setLoading(true);
    // 获取树结构
    dispatch({
      type: 'smartQualityInspection/getCategoryTreeStructureByAssessmentId',
      payload: {
        assessmentId: evaluationFormData.assessmentId,
        versionId: versionId,
      },
      callback: res => {
        if (res.code == 200) {
          setTreeData(res.data);
          // 重置选中状态
          setSelectedKeys([]);
          setHighlightedChildKeys([]);
        }
        setLoading(false);
      },
    });

    // 获取规则列表
    getRuleTableList(versionId);
  };

  // 版本切换处理函数
  const handleVersionChange = e => {
    if (e && e.key) {
      setSelectedVersionId(e.key);
      // 根据选择的版本ID获取对应的数据
      loadVersionData(e.key);
    }
  };
  const getAllRuleList = async () => {
    return new Promise((resolve, reject) => {
      dispatch({
        type: 'smartQualityInspection/getAllRuleList',
        payload: { assessmentId: evaluationFormData.assessmentId },
        callback: res => {
          if (res.code == 200) {
            resolve(res.data);
          } else {
            reject(res.error);
          }
        },
      });
    });
  };
  const handleSaveAndPublish = async e => {
    setLoading(true);
    let ruleData = [];
    try {
      const res = await getAllRuleList(); // 获取所有channelList
      console.log(res, 'res');
      ruleData = res;
      const formInfo = {
        assessmentName: evaluationFormData.name,
        ticketType: evaluationFormData.ticketType,
        fullScore: evaluationFormData.totalScore,
        raters: evaluationFormData.scorer,
        scoringRule: evaluationFormData.scoreMechanism == 'add' ? 1 : 2,
        channelList: evaluationFormData.channelList,
        scoreColor: evaluationFormData.scoreColor,
      };

      ruleData.forEach(item => {
        delete item.status; // 后端不要传这个字段 说会影响其他的
      });
      let data = {
        formInfo,
        categoryVoList: treeData,
        ruleVoList: ruleData,
        deployStatus: 1,
        assessmentId: evaluationFormData.assessmentId,
      };
      // 如果是从下拉菜单选择，获取选择的 versionId
      if (e && e.key) {
        data.versionId = e.key;
        setSelectedVersionId(e.key);
      }

      dispatch({
        type: 'smartQualityInspection/saveAndDeployAssessmentVersion',
        payload: data,
        callback: res => {
          console.log(res);
          if (res.code == 200) {
            console.log(res);
            history.push('/evaluationList');
          }
          setLoading(false);
        },
      });
    } catch (error) {
      console.log(error);
      notification.error({
        message: error,
      });
    }
  };
  const [dropDownItem, setDropDownItem] = useState([]);

  // 版本列表
  const menuProps = {
    items: dropDownItem,
    onClick: handleVersionChange,
  };

  const getAssessmentVersionList = async () => {
    dispatch({
      type: 'smartQualityInspection/getAssessmentVersionList',
      payload: { assessmentId: evaluationFormData.assessmentId },
      callback: res => {
        // 转换数据格式，设置 key 为 versionId，label 为 versionNo
        const formattedItems =
          res.data.length > 0
            ? res.data.map((item, index) => ({
                key: item.versionId,
                label:
                  index === 0
                    ? `${item.versionNo} (${intl.formatMessage({
                        id: 'smart.quality.evaluation.rule.version.current',
                      })})`
                    : item.versionNo,
              }))
            : [];
        // 编辑规则返回后，选中第一个版本
        setSelectedVersionId(formattedItems[0].key);
        setDropDownItem(formattedItems);
      },
    });
  };
  // 根据 key 查找节点
  const findNodeByKey = (key, nodes = treeData) => {
    for (const node of nodes) {
      if (node.key === key) {
        return node;
      }
      if (node.children) {
        const foundNode = findNodeByKey(key, node.children);
        if (foundNode) return foundNode;
      }
    }
    return null;
  };

  // 重命名节点
  const handleRename = (categoryId, newTitle) => {
    if (!checkLatestVersion()) return;

    setLoading(true);
    dispatch({
      type: 'smartQualityInspection/updateCategoryTreeStructure',
      payload: {
        assessmentId: evaluationFormData.assessmentId,
        categoryId: categoryId,
        categoryName: newTitle,
      },
      callback: res => {
        console.log(res);
        if (res && res.code === 200) {
          // 获取最新树数据并保持当前选中状态
          getTreeData(categoryId);
        }
      },
    });
  };
  const getTreeData = async (selectedCategoryId = null) => {
    await dispatch({
      type: 'smartQualityInspection/getCategoryTreeStructureByAssessmentId',
      payload: {
        assessmentId: evaluationFormData.assessmentId,
        versionId: evaluationFormData?.versionId || null,
      },
      callback: res => {
        if (res.code == 200) {
          setTreeData(res.data);
          dispatch({
            type: 'smartQualityInspection/setCategoryList',
            payload: res.data,
          });
          // 如果提供了要选中的节点ID，则设置选中状态
          if (selectedCategoryId) {
            // 查找节点
            const findNodeById = (nodes, id) => {
              for (const node of nodes) {
                if (node.categoryId === id) {
                  return node;
                }
                if (node.children) {
                  const found = findNodeById(node.children, id);
                  if (found) return found;
                }
              }
              return null;
            };

            const node = findNodeById(res.data, selectedCategoryId);
            if (node) {
              // 设置选中状态
              setSelectedKeys([node.categoryId]);

              // 获取子节点并高亮
              const getAllChildrenIds = node => {
                let ids = [];
                if (!node.children) return ids;

                const traverse = nodes => {
                  nodes.forEach(child => {
                    ids.push(child.categoryId);
                    if (child.children) {
                      traverse(child.children);
                    }
                  });
                };

                traverse(node.children);
                return ids;
              };

              const childIds = getAllChildrenIds(node);
              setHighlightedChildKeys(childIds);

              // 确保节点展开
              if (node.parentId) {
                setExpandedKeys(prev => [...prev, node.parentId]);
              }
            }
          }
          setLoading(false);
        }
      },
    });
    setLoading(false);
  };
  // 添加子节点
  const handleAddChild = async parentCategoryId => {
    if (!checkLatestVersion()) {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'smart.quality.evaluation.rule.version.current.tip',
          defaultMessage: '请选择最新版本',
        }),
      });
      return;
    }

    setLoading(true);
    let parentLevel = 1;

    // 查找父节点的级别
    const findNodeLevel = (nodes, id) => {
      for (const node of nodes) {
        if (node.categoryId === id) {
          parentLevel = node.level;
          return true;
        }
        if (node.children && findNodeLevel(node.children, id)) {
          return true;
        }
      }
      return false;
    };

    findNodeLevel(treeData, parentCategoryId);

    if (parentLevel >= 3) {
      notification.warning({
        message: '最多支持三级分类',
      });
      return;
    }

    // 调用接口保存节点数据
    dispatch({
      type: 'smartQualityInspection/addCategoryTreeStructure',
      payload: {
        parentId: parentCategoryId,
        categoryName: intl.formatMessage({
          id: 'smart.quality.evaluation.rule.new.category',
        }),
        level: parentLevel + 1,
        children: [],
        assessmentId: evaluationFormData.assessmentId,
      },
      callback: res => {
        if (res && res.code === 200) {
          console.log(res);

          // 获取最新树数据，不选中任何节点
          getTreeData();

          // 确保父节点展开
          setExpandedKeys(prev => [...prev, parentCategoryId]);
        }
      },
    });
  };

  // 删除节点
  const handleDelete = categoryId => {
    if (!checkLatestVersion()) return;

    setLoading(true);
    // 查找并删除节点前，先找到其父节点
    const findParentId = (nodes, id, parent = null) => {
      for (const node of nodes) {
        if (node.categoryId === id) {
          return parent;
        }
        if (node.children) {
          const result = findParentId(node.children, id, node.categoryId);
          if (result) return result;
        }
      }
      return null;
    };

    const parentCategoryId = findParentId(treeData, categoryId);

    dispatch({
      type: 'smartQualityInspection/deleteCategoryTreeStructure',
      payload: {
        assessmentId: evaluationFormData.assessmentId,
        categoryId: categoryId,
      },
      callback: res => {
        console.log(res);
        if (res && res.code === 200) {
          // 获取最新树数据，如果有父节点则选中父节点
          getTreeData();
          getRuleTableList();
          notification.success({
            message: getIntl().formatMessage({
              id: 'smart.quality.evaluation.table.delete.success',
              defaultMessage: '删除成功',
            }),
          });
        }
      },
    });
  };

  // 处理展开/收缩
  const onExpand = expandedKeysValue => {
    setExpandedKeys(expandedKeysValue);
  };

  // 为 treeData 添加自定义 className
  const addClassNameToTreeData = (data, selectedKey, childrenKeys = []) => {
    console.log(data);
    return data.map((node, index) => {
      // 使用categoryId而不是key来判断选中状态
      const isSelected = node.categoryId === selectedKey;
      const isChildOfSelected = childrenKeys.includes(node.categoryId);

      const newNode = {
        ...node,
        className: isSelected
          ? 'tree-node-selected'
          : isChildOfSelected
          ? 'tree-node-highlighted'
          : '',
        // 检查是否是最后一个子节点，如果是则添加圆角样式
        // style: isChildOfSelected && index === data.length - 1
        //   ? { borderRadius: '0 0 4px 4px' }
        //   : {}
      };

      if (node.children) {
        newNode.children = addClassNameToTreeData(
          node.children,
          selectedKey,
          childrenKeys,
        );
      }

      return newNode;
    });
  };

  // 处理选择节点
  const onSelect = (selectedKeysValue, info) => {
    if (selectedKeysValue.length > 0) {
      const categoryId = selectedKeysValue[0];
      setSelectedKeys(selectedKeysValue);

      // 查找选中节点，获取其所有子节点并高亮
      const findNodeById = (nodes, id) => {
        for (const node of nodes) {
          if (node.categoryId === id) {
            return node;
          }
          if (node.children) {
            const found = findNodeById(node.children, id);
            if (found) return found;
          }
        }
        return null;
      };

      const node = findNodeById(treeData, categoryId);
      if (node) {
        // 设置当前选中的树节点数据
        setCurrentTreeData(node);

        // 获取所有子节点ID
        const getAllChildrenIds = node => {
          let ids = [];
          if (!node.children) return ids;

          const traverse = nodes => {
            nodes.forEach(child => {
              ids.push(child.categoryId);
              if (child.children) {
                traverse(child.children);
              }
            });
          };

          traverse(node.children);
          return ids;
        };

        const childIds = getAllChildrenIds(node);
        setHighlightedChildKeys(childIds);

        // 更新 treeData，添加自定义 className
        const newTreeData = addClassNameToTreeData(
          treeData,
          categoryId,
          childIds,
        );
        setTreeData(newTreeData);

        // 如果节点有子节点且未展开，则自动展开
        if (
          node.children &&
          node.children.length > 0 &&
          !expandedKeys.includes(categoryId)
        ) {
          setExpandedKeys([...expandedKeys, categoryId]);
        }

        // 重置分页状态并获取当前分类下的规则列表
        setCurrent(1);
        getRuleTableList(evaluationFormData?.versionId ?? '', categoryId);
      }
    } else {
      setSelectedKeys([]);
      setHighlightedChildKeys([]);

      // 清除所有自定义 className
      const newTreeData = addClassNameToTreeData(treeData, '', []);
      setTreeData(newTreeData);

      // 重置为获取所有规则
      setCurrent(1);
      getRuleTableList();
    }
  };

  // 自定义节点渲染
  const renderTreeNodes = data => {
    return data.map(item => ({
      ...item,
      key: item.categoryId, // 使用categoryId作为key
      title: (
        <CustomTreeNode
          nodeData={item}
          onRename={handleRename}
          onAddChild={handleAddChild}
          onDelete={handleDelete}
          checkLatestVersion={checkLatestVersion}
          setLoading={setLoading}
        />
      ),
      children: item.children ? renderTreeNodes(item.children) : null,
    }));
  };

  // 添加顶级节点
  const handleAddTopLevel = () => {
    if (!checkLatestVersion()) {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'smart.quality.evaluation.rule.version.current.tip',
          defaultMessage: '请选择最新版本',
        }),
      });
      return;
    }

    setLoading(true);

    // 调用接口保存节点数据
    dispatch({
      type: 'smartQualityInspection/addCategoryTreeStructure',
      payload: {
        parentId: null,
        categoryName: intl.formatMessage({
          id: 'smart.quality.evaluation.rule.new.top.level',
        }),
        level: 1,
        children: [],
        assessmentId: evaluationFormData.assessmentId,
      },
      callback: res => {
        if (res && res.code === 200) {
          console.log(res);
          // 获取最新树数据，不选中任何节点
          getTreeData();
        }
      },
    });
  };

  // 处理搜索
  const handleSearch = e => {
    if (loading) return;
    const value = e.target.value;
    setSearchText(value);

    if (e.key === 'Enter') {
      setCurrent(1);
      setPageSize(10);

      // 如果有选中的分类，按分类ID搜索，否则搜索全部
      if (selectedKeys.length > 0) {
        getRuleTableList(evaluationFormData?.versionId ?? '', selectedKeys[0]);
      } else {
        getRuleTableList();
      }
    }
  };

  // 添加规则
  const handleAddRule = async () => {
    if (!checkLatestVersion()) {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'smart.quality.evaluation.rule.version.current.tip',
          defaultMessage: '请选择最新版本',
        }),
      });
      return;
    }

    // 先判断是否有节点
    if (treeData.length === 0) {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'smart.quality.evaluation.rule.add.node.tip',
          defaultMessage: '请先添加一个节点',
        }),
      });
      return;
    }
    // 再判断是否选中节点
    if (selectedKeys.length === 0) {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'smart.quality.evaluation.rule.select.node.tip',
          defaultMessage: '请先选中一个节点',
        }),
      });
      return;
    }
    console.log('currentTreeData', currentTreeData);
    history.push({
      pathname: '/evaluationRuleAdd',
      state: {
        selectedNode: currentTreeData,
      },
    });
  };

  const handleCancel = () => {
    dispatch({
      type: 'smartQualityInspection/resetAll',
    });
    history.push('/evaluationList');
  };

  const handleEdit = record => {
    if (!checkLatestVersion()) return;

    history.push({
      pathname: '/evaluationRuleAdd',
      state: {
        ruleId: record.ruleId,
        selectedNode: record,
      },
    });
  };

  const handleViewDetail = ruleId => {
    history.push({
      pathname: '/evaluationRuleAdd',
      state: {
        ruleId,
        viewOnly: true,
        selectedVersionId: selectedVersionId,
      },
    });
  };

  const handleDeleteRule = ruleId => {
    setLoading(true);
    if (!checkLatestVersion()) return;

    dispatch({
      type: 'smartQualityInspection/deleteRule',
      payload: {
        ruleId,
      },
      callback: res => {
        if (res && res.code == 200) {
          getRuleTableList();
        }
        setLoading(false);
      },
    });
  };

  // 表格列定义
  const columns = [
    {
      title: (
        <FormattedMessage id="smart.quality.evaluation.rule.table.category" />
      ),
      dataIndex: 'categoryName',
      key: 'categoryName',
      width: 170,
    },
    {
      title: <FormattedMessage id="smart.quality.evaluation.rule.table.name" />,
      dataIndex: 'ruleName',
      key: 'ruleName',
      width: 170,
    },
    {
      title: (
        <FormattedMessage id="smart.quality.evaluation.rule.table.description" />
      ),
      dataIndex: 'description',
      key: 'description',
      width: 200,
    },
    {
      title: (
        <FormattedMessage id="smart.quality.evaluation.rule.table.score" />
      ),
      dataIndex: 'score',
      key: 'score',
      width: 150,
      render: text => (
        <span>
          <b className={styles.scoreSign}>
            {evaluationFormData.scoreMechanism == 'add' ? (
              <FormattedMessage id="smart.quality.evaluation.rule.add.score.add" />
            ) : (
              <FormattedMessage id="smart.quality.evaluation.rule.add.score.subtract" />
            )}
          </b>
          {text}
        </span>
      ),
    },
    {
      title: (
        <FormattedMessage id="smart.quality.evaluation.rule.table.evaluation.method" />
      ),
      dataIndex: 'scoreType',
      key: 'scoreType',
      width: 170,
      render: text => (
        <span style={{ display: 'flex', gap: 4 }}>
          {text === 1 ? AiSvg : ManualSvg}
          <FormattedMessage
            id={`smart.quality.evaluation.rule.table.evaluation.method.${
              text === 1 ? 'ai' : 'manual'
            }`}
          />
        </span>
      ),
    },
    {
      title: (
        <FormattedMessage id="smart.quality.evaluation.rule.table.operation" />
      ),
      key: 'operation',
      fixed: 'right',
      render: (_, record) => (
        <div className={styles.operationColumn}>
          {checkLatestVersion() ? (
            <>
              <Button
                type="link"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
              >
                <FormattedMessage id="smart.quality.evaluation.rule.table.operation.edit" />
              </Button>
              <Popconfirm
                title={
                  <FormattedMessage id="smart.quality.evaluation.rule.table.delete.confirm" />
                }
                onConfirm={() => handleDeleteRule(record.ruleId)}
                okText={
                  <FormattedMessage id="smart.quality.evaluation.rule.table.delete.ok" />
                }
                cancelText={
                  <FormattedMessage id="smart.quality.evaluation.rule.table.delete.cancel" />
                }
              >
                <Button
                  type="link"
                  icon={<img src={NewDeleteIcon} alt="delete" />}
                >
                  <FormattedMessage id="smart.quality.evaluation.rule.table.operation.delete" />
                </Button>
              </Popconfirm>
            </>
          ) : (
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleViewDetail(record.ruleId)}
              style={{ color: '#3463fc' }}
            >
              <FormattedMessage
                id="smart.quality.evaluation.rule.table.operation.detail"
                defaultMessage="详情"
              />
            </Button>
          )}
        </div>
      ),
    },
  ];
  const getRuleTableList = async (
    versionId = evaluationFormData?.versionId ?? '',
    categoryId = '',
  ) => {
    return new Promise((resolve, reject) => {
      setLoading(true);
      dispatch({
        type: 'smartQualityInspection/getRuleTableList',
        payload: {
          categoryId: categoryId,
          pageNum: current,
          pageSize: pageSize,
          ruleName: searchText,
          assessmentId: evaluationFormData.assessmentId,
          versionId: versionId,
        },
        callback: res => {
          if (res && res.code == 200) {
            setRuleData(res.data.records);
            setTotal(res.data.total);
            resolve(res.data.records);
          } else {
            reject(res.msg);
          }
          setLoading(false);
        },
      });
    });
  };
  return (
    <Spin spinning={loading}>
      <div className={styles.evaluationRuleList}>
        <div className={styles.evaluationRuleListHeader}>
          <div className={styles.headerLeft}>
            <div className="blueBorder">
              {isEditMode ? (
                <FormattedMessage id="smart.quality.evaluation.rule.edit.title" />
              ) : (
                <FormattedMessage id="smart.quality.evaluation.rule.title" />
              )}
            </div>
          </div>
          <div className={styles.headerRight}>
            <div className={styles.deployStatus}>
              <span className={styles.statusLabel}>
                <FormattedMessage id="smart.quality.evaluation.rule.deploy.status" />
                ：
              </span>
              {/* 有版本id就是已发布状态，没有就是未发布状态 */}
              <span
                className={`${styles.statusValue} ${
                  evaluationFormData.versionId
                    ? styles.published
                    : styles.unpublished
                }`}
              >
                <FormattedMessage
                  id={
                    evaluationFormData.versionId
                      ? 'smart.quality.evaluation.rule.deploy.status.published'
                      : 'smart.quality.evaluation.rule.deploy.status.unpublished'
                  }
                />
              </span>
            </div>
            <div className={styles.buttonGroup}>
              <Button onClick={handleCancel}>
                <FormattedMessage id="smart.quality.evaluation.rule.button.cancel" />
              </Button>
              {(!isEditMode || dropDownItem.length === 0) && (
                <Button type="primary" onClick={handleSaveAndPublish}>
                  <FormattedMessage id="smart.quality.evaluation.rule.button.save.and.publish" />
                </Button>
              )}
              {isEditMode && dropDownItem.length > 0 && (
                <Dropdown
                  menu={menuProps}
                  className={styles.canvasFlow_header_deploy}
                  overlayClassName={styles.canvasFlow_header_deploy_dropdown}
                >
                  <Button type="primary" onClick={handleSaveAndPublish}>
                    <Space>
                      <FormattedMessage
                        id="smart.quality.evaluation.rule.button.save.and.publish"
                        defaultValue="保存并发布"
                      ></FormattedMessage>
                      <DownOutlined />
                    </Space>
                  </Button>
                </Dropdown>
              )}
            </div>
          </div>
        </div>
        <div className={styles.evaluationRuleListContainer}>
          <div className={styles.ruleListTreeContainer}>
            <h2>
              <FormattedMessage id="smart.quality.evaluation.rule.category" />
            </h2>

            <div className={styles.addTopLevelContainer}>
              <div className={styles.assessmentTitle}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="12"
                  height="12"
                  viewBox="0 0 12 12"
                  fill="none"
                >
                  <path
                    d="M6.79489 3.16425H10.0341C10.1416 3.16425 10.2446 3.12158 10.3205 3.04563C10.3965 2.96967 10.4391 2.86666 10.4391 2.75925C10.4391 2.65184 10.3965 2.54882 10.3205 2.47287C10.2446 2.39692 10.1416 2.35425 10.0341 2.35425H6.79489C6.68748 2.35425 6.58447 2.39692 6.50851 2.47287C6.43256 2.54882 6.38989 2.65184 6.38989 2.75925C6.38989 2.86666 6.43256 2.96967 6.50851 3.04563C6.58447 3.12158 6.68748 3.16425 6.79489 3.16425Z"
                    fill="#333333"
                  />
                  <path
                    d="M10.0343 3.59922H5.8425C5.76753 3.3566 5.70198 3.11116 5.646 2.86347C5.439 2.00547 5.3355 1.57422 4.9215 1.57422H1.93425C1.281 1.57422 0.75 2.19672 0.75 2.96247V9.03672C0.75 9.80247 1.281 10.425 1.93425 10.425H10.0343C10.6875 10.425 11.2192 9.80247 11.2192 9.03597V4.98597C11.2192 4.22172 10.6875 3.59922 10.0343 3.59922ZM10.4692 9.03672C10.4692 9.38247 10.2697 9.67572 10.0343 9.67572H1.93425C1.69875 9.67497 1.5 9.38247 1.5 9.03672V2.96247C1.5 2.61672 1.69875 2.32422 1.93425 2.32422H4.7265C4.7865 2.50047 4.86225 2.81397 4.91625 3.03897C5.12625 3.91122 5.232 4.34922 5.6505 4.34922H10.0335C10.269 4.34922 10.4685 4.64097 10.4685 4.98672V9.03672H10.4692Z"
                    fill="#333333"
                  />
                </svg>
                {evaluationFormData.name || evaluationFormData.assessmentName}
              </div>
              <div
                className={styles.addTopLevelButton}
                onClick={handleAddTopLevel}
              >
                <PlusOutlined />
              </div>
            </div>

            <Tree
              className={styles.customTree}
              showLine={false}
              showIcon={false}
              blockNode={true}
              indentSize={20} // 禁用默认缩进
              onExpand={onExpand}
              expandedKeys={expandedKeys}
              onSelect={onSelect}
              selectedKeys={selectedKeys}
              treeData={renderTreeNodes(treeData)}
            />
          </div>
          <div className={styles.ruleListTableContainer}>
            <div className={styles.tableHeaderContainer}>
              <Input
                className={styles.searchContainer}
                placeholder={intl.formatMessage({
                  id: 'smart.quality.evaluation.rule.search.placeholder',
                })}
                suffix={<SearchOutlined />}
                value={searchText}
                onChange={handleSearch}
                onKeyPress={handleSearch}
              />
              <Button
                type="primary"
                className={styles.addButton}
                icon={<PlusOutlined />}
                onClick={handleAddRule}
              >
                <FormattedMessage id="smart.quality.evaluation.rule.add" />
              </Button>
            </div>
            <Table
              columns={columns}
              dataSource={ruleData}
              scroll={{ x: 'max-content' }}
              pagination={{
                total,
                pageSize,
                current,
                showSizeChanger: true,
                pageSizeOptions: [10, 20, 50, 100],
                onChange: (page, size) => {
                  setLoading(true);
                  setCurrent(page);
                  setPageSize(size);
                  // 直接使用回调中的 page 和 size 参数，不依赖状态
                  dispatch({
                    type: 'smartQualityInspection/getRuleTableList',
                    payload: {
                      categoryId:
                        selectedKeys.length > 0 ? selectedKeys[0] : '',
                      pageNum: page, // 使用参数而非状态
                      pageSize: size, // 使用参数而非状态
                      ruleName: searchText,
                      assessmentId: evaluationFormData.assessmentId,
                      versionId: evaluationFormData?.versionId ?? '',
                    },
                    callback: res => {
                      if (res && res.code == 200) {
                        setRuleData(res.data.records);
                        setTotal(res.data.total);
                      }
                      setLoading(false);
                    },
                  });
                },
                showTotal: total => (
                  <FormattedMessage
                    id="smart.quality.evaluation.rule.page.total.num"
                    defaultMessage={`共 ${total} 条`}
                    values={{ total }}
                  />
                ),
              }}
            />
          </div>
        </div>
      </div>
    </Spin>
  );
};

export default EvaluationRuleList;
