.workContent {
  margin: 20px;
  height: 95%;
  overflow-y: scroll;

  .workContentTop {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    font-size: 18px;
  }

  .pageContent {
    width: 100%;
    // min-height: 600px;
    border-radius: 6px;
    position: relative;
    // margin-top: 16px;
    // overflow: hidden;
    // overflow-y: scroll;
    // scrollbar-width: 0px;
    // -ms-overflow-style: none;

    .pageContentTab1 {
      width: 100%;

      .slaRuleContainer {
        width: 100%;
        margin-top: 20px;

        .ruleItemContainer {
          width: 49%;
          height: 345px;
          border-radius: 4px;
          border: 1px solid #3463fc;
          background: #fff;
          float: left;
          margin-right: 2%;
          padding: 10px 20px;
          margin-bottom: 20px;

          /* 模块阴影 */
          box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.15);

          .ruleHeaderContainer {
            width: 100%;
            height: 25px;
            margin-bottom: 10px;

            .ruleName {
              max-width: 60%;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              color: #333;
              font-size: 14px;
              font-style: normal;
              font-weight: 700;
              line-height: 24px;
              float: left;
              margin-bottom: 0px;
            }

            :global {
              .ant-btn {
                float: right;
                color: #3463fc;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 15px;
                height: 24px;
                padding: 4px 5px;
              }

              .ant-btn-dangerous {
                float: right;
                border-radius: 4px;
                border: 1px solid #f22417;
                color: #f22417;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 15px;
                height: 24px;
                padding: 4px 5px;
                margin-left: 10px;
              }
            }
          }

          .tableTitle {
            color: #333;
            font-size: 12px;
            font-style: normal;
            font-weight: 700;
            line-height: 150%;
            /* 18px */
          }

          .channelTypeContainer {
            width: 100%;
            height: 22px;
            margin-bottom: 10px;

            img {
              width: 16px;
              margin-right: 4px;
            }

            span {
              color: #333;
              font-size: 12px;
              font-style: normal;
              font-weight: 700;
              line-height: 22px;
              float: left;
              margin-right: 3px;
            }

            .detailTypeContainer {
              max-width: 80%;
              height: 22px;
              float: left;
              overflow: hidden;
              overflow-y: scroll;

              .detailText {
                border-radius: 4px;
                border: 1px solid rgba(52, 99, 252, 0.5);
                background: linear-gradient(
                    0deg,
                    rgba(52, 99, 252, 0.1) 0%,
                    rgba(52, 99, 252, 0.1) 100%
                  ),
                  #fff;
                color: #3463fc;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 18px;
                /* 150% */
                padding: 1px 10px;
                float: left;
                margin-right: 2px;
              }
            }
          }
        }

        .ruleItemContainer:nth-child(2n) {
          margin-right: 0;
        }

        .ruleItemContainer:hover {
          background: rgba(52, 99, 252, 0.05);
        }
      }

      :global {
        .ant-select-selector {
          border-radius: 6px;
          font-size: 12px;
        }

        .ant-input {
          border-radius: 6px;
        }

        .ant-col {
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }

    .pageContentTab2 {
      :global {
        .ant-form-item-label > label {
          font-size: 12px !important;
        }

        .ant-form-item-control-input {
          width: 330px;
        }

        .ant-select-selector {
          border-radius: 6px !important;
          text-align: left;
          font-size: 12px;
        }
      }
    }

    .pageContentTab3 {
      .firstForm {
      }

      :global {
        .ant-form-horizontal::-webkit-scrollbar {
          display: none;
        }

        .ant-input {
          border-radius: 6px;
        }

        .ant-form-item-label > label {
          font-size: 12px !important;
        }

        .ant-select-selector {
          border-radius: 6px !important;
          text-align: left;
          font-size: 12px;
        }
      }
    }

    .pageContentTab4 {
      width: 75%;
      margin-top: 40px;
      margin-left: 30px;
      line-height: 22px;

      img {
        width: 16px;
        height: 16px;
        float: left;
        margin-right: 8px;
        margin-top: 3px;
      }

      :global {
        .ant-switch-checked {
          background-color: rgba(52, 99, 252, 1);
        }
      }
    }

    .pageContentTab5 {
      width: 100%;
      height: 100%;
      overflow: hidden;
      overflow-y: scroll;

      :global {
        .ant-pagination-total-text {
          font-size: 12px;
        }

        .ant-select-selection-item {
          font-size: 12px;
        }

        .ant-pagination {
          font-size: 12px;
        }

        .ant-form-item-label {
          padding: 0 !important;
        }

        .ant-input {
          border-radius: 6px;
          border: 1px solid #e6e6e6;
          background: #fff;
        }

        .ant-input:focus,
        .ant-input-focused {
          box-shadow: none !important;
        }

        .ant-select-selector {
          box-shadow: none !important;
          font-size: 12px;
          border-radius: 6px;
          border: 1px solid #e6e6e6;
          background: #fff;
        }

        .ant-btn-text {
          border: 0 !important;
          color: #3463fc;
          text-align: right;
          font-size: 12px;
          padding: 0 !important;
        }
      }

      .operationTable {
        div {
          display: inline-block;
          cursor: pointer;
        }

        img {
          width: 12px;
          float: left;
          margin-right: 3px;
          margin-top: 3px;
        }
      }

      .pageContentTab5_title {
        color: #333;
        font-size: 14px;
        font-style: normal;
        font-weight: 700;
        line-height: 150%;
        /* 21px */
      }

      .pageContentTab5_base {
        display: flex;
        width: 100%;
        padding: 12px;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        gap: 10px;
        border-radius: 10px;
        background: #f5f3fe;
        margin-bottom: 20px;
      }

      .pageContentTab5_each {
        border-radius: 4px;
        background: #f9f9f9;
        width: 100%;
        height: auto;
        margin-bottom: 20px;
        padding: 12px;

        :global {
          .ant-form-item-label {
            font-size: 12px !important;
            color: #333 !important;
            font-weight: 400 !important;
          }

          .ant-picker-range {
            width: 60%;
            border-radius: 6px;
            box-shadow: none;
          }
        }
      }

      .pageContentTab5_holidays {
        padding: 12px;
        border-radius: 4px;
        background: #f9f9f9;
        width: 100%;
        height: auto;

        :global {
          .ant-picker-range {
            width: 100%;
            border-radius: 6px;
            box-shadow: none;
          }
        }
      }
    }

    .titleContent {
      width: 100%;
      height: 50px;

      p {
        font-size: 16px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.88);
        float: left;
      }

      :global {
        .ant-btn-primary {
          float: right;
          font-size: 12px;
        }
      }
    }

    .tableContent {
      width: 100%;
      // height: 500px;
      // overflow: hidden;
      // overflow-y: scroll;
      // scrollbar-width: 0px;
      // -ms-overflow-style: none;

      .userType {
        color: #409eff;
      }

      .connectList {
        width: 150px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .operationArray {
        display: flex;
        flex-direction: row;

        .operationText {
          margin-right: 8px;
        }
      }

      :global {
        .ant-table-thead > tr > th {
          // background-color: #f3f7fe !important;
          background-color: #ffffff !important;
          font-weight: 700;
        }

        .ant-table-tbody {
          background-color: #fcfbff;
        }

        .ant-table-placeholder {
          z-index: 999 !important;
        }
      }
    }

    .tableContent::-webkit-scrollbar {
      display: none;
    }
  }
}
