import React, { Component } from 'react';
import { connect, getIntl, FormattedMessage } from 'umi';
import { Input, Button, Select, Form, Col, Row, Spin } from 'antd';
import styles from './index.less';
import { notification } from '@/utils/utils';
import { PlusOutlined } from '@ant-design/icons';
import { ReactComponent as DeleteHome } from '@/assets/deleteHome.svg';
import { BackIcon, Delete } from '../autoCloseSetting/icon';

import HOCAuth from '@/components/HOCAuth/index';

class TicketType extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      saveLoading: false,
      tab3TypeData: [{}, {}],
      languageList: [],
      language: '',
    };
  }
  componentDidMount() {
    let lang = localStorage.getItem('lang') || 'en-US';
    localStorage.setItem('lang', lang);
    this.setState(
      {
        language: lang,
      },
      () => {
        this.queryWorkOrderType();
        this.queryLanguage();
      },
    );
  }
  /**
   * 查询时区
   */
  queryLanguage = () => {
    this.setState({ loading: true });
    /**语言下拉 */
    this.props.dispatch({
      type: 'personalCenter/listLanguage',
      callback: response => {
        let { code, data, msg } = response;
        if (code === 200) {
          this.setState({
            languageList: data,
          });
        } else {
          notification.error({
            message: msg,
          });
        }
        this.setState({
          loading: false,
        });
      },
    });
  };
  addWorkerType = () => {
    const newObject = {}; // 新的对象
    // 将新对象与现有数组合并
    const newSelectList = [newObject, ...this.state.tab3TypeData];
    this.setState({ tab3TypeData: newSelectList });
  };
  // 更新输入框内容时的回调函数
  handleInputChange = (index, key, value) => {
    const updatedList = [...this.state.tab3TypeData];
    updatedList[index][key] = value;
    this.setState({ tab3TypeData: updatedList });
  };

  /**
   * 工单类型查询
   */
  queryWorkOrderType = () => {
    this.setState({ loading: true });
    this.props.dispatch({
      type: 'workOrderExtension/queryWorkOrderType',
      payload: { languageCode: this.state.language },
      callback: res => {
        if (res.code === 200) {
          const data = res.data;
          data.forEach(item => {
            item.disabled = true;
          });
          this.setState({ tab3TypeData: data });
        } else {
          notification.error({
            message: res.msg,
          });
        }
        this.setState({ loading: false });
      },
    });
  };
  /**
   * 工单类型保存
   */
  saveTab3 = () => {
    this.setState({ saveLoading: true });

    this.props.dispatch({
      type: 'workOrderExtension/updateWorkOrderType',
      payload: {
        data: this.state.tab3TypeData,
        params: { languageCode: this.state.language },
      },
      callback: res => {
        if (res.code === 200) {
          notification.success({
            // description: '保存成功！',
            message: getIntl().formatMessage({
              id: 'customer.ext.info.save.success',
            }),
          });
          this.queryWorkOrderType();
        } else {
          notification.error({
            message: res.msg,
          });
        }
        this.setState({ saveLoading: false });
      },
    });
  };
  /**
   * 删除选项卡内容项
   */
  removeSelectListItem = indexToRemove => {
    const newSelectList = this.state.tab3TypeData.filter(
      (item, index) => index !== indexToRemove,
    );
    console.log(
      newSelectList,
      indexToRemove,
      JSON.stringify(this.state.tab3TypeData[indexToRemove]) === '{}',
    );

    if (!this.state.tab3TypeData[indexToRemove]?.workRecordTypeId) {
      let arr = this.state.tab3TypeData;
      arr.splice(indexToRemove, 1);
      this.setState({
        tab3TypeData: arr,
      });
    } else {
      this.props.dispatch({
        type: 'workOrderExtension/deleteWorkOrderType',
        payload: {
          workRecordTypeId: this.state.tab3TypeData[indexToRemove]
            .workRecordTypeId,
        },
        callback: res => {
          if (res.code === 200) {
            notification.success({
              message: getIntl().formatMessage({
                id: 'user.management.remove.success',
              }),
            });
            this.setState({ tab3TypeData: newSelectList });
            // this.queryWorkOrderType();
          } else {
            notification.error({
              message: res.msg,
            });
          }
        },
      });
    }

    console.log(this.state.tab3TypeData);
  };
  /**
   * 语言改变
   */
  handleLenguageChange = (value, i) => {
    this.setState({ language: value }, () => {
      this.queryWorkOrderType();
    });
  };

  render() {
    const { loading, tab3TypeData, languageList } = this.state;
    return (
      <div className={styles.workContent}>
        <div className={styles.workContentTop}>
          <div style={{ display: 'flex' }}>
            <span
              style={{ marginRight: 10, lineHeight: '35px', cursor: 'pointer' }}
              onClick={() => history.go(-1)}
            >
              {BackIcon()}
            </span>

            <FormattedMessage id="ticketOrCustomerSettings.config.title.1" />
          </div>
        </div>
        <div className={styles.pageContent}>
          <Spin spinning={loading}>
            <div>
              <div className={styles.pageContentTab3}>
                <div
                  style={{
                    borderBottom: '1px #e6e6e6 solid',
                    marginBottom: 10,
                  }}
                  className={styles.firstForm}
                >
                  <Form>
                    <Row style={{ marginTop: 10, lineHeight: '40px' }}>
                      <Col span={8} style={{ textAlign: 'center' }}>
                        <Form.Item
                          label={getIntl().formatMessage({
                            id: 'home.language.select',
                          })}
                        >
                          <Select
                            placeholder={getIntl().formatMessage({
                              id: 'home.set.language.select',
                              defaultValue: '请选择语言',
                            })}
                            options={languageList.map(item => ({
                              label: item.languageName,
                              value: item.languageCode,
                              key: item.languageCode,
                            }))}
                            onChange={value =>
                              this.handleLenguageChange(value, '3')
                            }
                            value={this.state.language}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Form>
                </div>
                <Form>
                  {tab3TypeData.map((item, index) => {
                    return (
                      <Row style={{ marginTop: 10, lineHeight: '40px' }}>
                        <Col
                          span={8}
                          style={{ textAlign: 'center', marginRight: 100 }}
                        >
                          <Form.Item
                            label={getIntl().formatMessage({
                              id: 'channel.config.tab.3.label',
                            })}
                          >
                            <Input
                              placeholder={getIntl().formatMessage({
                                id: 'channel.please.input',
                              })}
                              style={{ marginRight: 8 }}
                              value={item.workRecordTypeName}
                              onChange={e =>
                                this.handleInputChange(
                                  index,
                                  'workRecordTypeName',
                                  e.target.value,
                                )
                              }
                            />
                          </Form.Item>
                        </Col>
                        <Col span={8} style={{ textAlign: 'center' }}>
                          <Form.Item
                            label={getIntl().formatMessage({
                              id: 'channel.config.tab.3.label2',
                            })}
                          >
                            <Input
                              placeholder={getIntl().formatMessage({
                                id: 'channel.please.input',
                              })}
                              disabled={item.disabled}
                              style={{ marginRight: 8 }}
                              value={item.workRecordTypeValue}
                              onChange={e =>
                                this.handleInputChange(
                                  index,
                                  'workRecordTypeValue',
                                  e.target.value,
                                )
                              }
                            />
                          </Form.Item>
                        </Col>
                        <HOCAuth authKey={'delete_ticket_type_definition'}>
                          {authAccess => (
                            <Col span={2} style={{ textAlign: 'center' }}>
                              <div
                                onClick={() => this.removeSelectListItem(index)}
                                style={{ cursor: 'pointer' }}
                                className={authAccess && 'disabled'}
                              >
                                <DeleteHome />
                              </div>
                              {/* <Button
                              type="primary"
                              danger
                              icon={<MinusOutlined />}
                              style={{
                                borderRadius: '50%',
                                backgroundColor: '#ff4d4f',
                                borderColor: '#ff4d4f',
                              }}
                              onClick={() => this.removeSelectListItem(index)}
                            ></Button> */}
                            </Col>
                          )}
                        </HOCAuth>
                      </Row>
                    );
                  })}
                </Form>
                <HOCAuth authKey={'add_ticket_type_definition'}>
                  {authAccess => (
                    <Row style={{ marginTop: 20 }}>
                      <Button
                        type="primary"
                        shape="circle"
                        style={{ padding: '4px 12px' }}
                        icon={<PlusOutlined />}
                        onClick={() => {
                          this.addWorkerType();
                        }}
                        disabled={authAccess}
                      >
                        <span style={{ fontSize: 14 }}>
                          <FormattedMessage id="customer.ext.info.selectList.work" />
                        </span>
                      </Button>
                    </Row>
                  )}
                </HOCAuth>
                <HOCAuth authKey={'modify_ticket_type_definition'}>
                  {authAccess => (
                    <Row justify="center" style={{ marginTop: 80 }}>
                      <Button
                        onClick={() => history.go(-1)}
                        style={{ marginRight: 20 }}
                      >
                        <FormattedMessage
                          id="work.record.button.cancel"
                          defaultValue="取消"
                        />
                      </Button>
                      <Button
                        type="primary"
                        onClick={this.saveTab3}
                        loading={this.state.saveLoading}
                        disabled={authAccess}
                      >
                        <FormattedMessage
                          id="customerInformation.add.basicInformation.button.save"
                          defaultValue="保存"
                        />
                      </Button>
                    </Row>
                  )}
                </HOCAuth>
              </div>
            </div>
          </Spin>
        </div>
      </div>
    );
  }
}

const mapStateToProps = ({
  workOrderExtension,
  customerExt,
  layouts,
  newChannelConfiguration,
  workOrderCenter,
}) => {
  return {
    ...workOrderExtension,
    ...customerExt,
    authAccess: layouts.auth,
    ...newChannelConfiguration,
    ...workOrderCenter,
  };
};
export default connect(mapStateToProps)(TicketType);
