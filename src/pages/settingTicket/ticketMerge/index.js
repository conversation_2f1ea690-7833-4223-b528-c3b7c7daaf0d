import React, { Component } from 'react';
import { connect, getIntl, FormattedMessage } from 'umi';
import { Button, Col, Row, Spin, Switch, notification } from 'antd';
import styles from './index.less';
import { PlusOutlined } from '@ant-design/icons';
import { BackIcon } from '../autoCloseSetting/icon';
import TableEmailIcon from '@/assets/table-email-icon.png';

import WhatsAppIcon from '@/assets/whats-app.svg';
import FacebookIcon from '@/assets/facebook.svg';
import AppVideoOutlinedIcon from '@/assets/AppVideoOutlined.svg';
import AppChatOutlinedIcon from '@/assets/AppChatOutlined.svg';
import WebVideoOutlinedIcon from '@/assets/WebVideoOutlined.svg';
import ChatIcon from '@/assets/chat-icon.jpg';
import HOCAuth from '@/components/HOCAuth/index';
import AwsChannelIcon from '@/assets/aws-channel-icon.svg';
import NewWebOnlineVoiceIcon from '@/assets/web-online-voice-icon.svg';
import NewAppOnlineVoiceIcon from '@/assets/app-online-voice-icon.svg';
import NewInstagramIcon from '@/assets/ins.svg';
import NewLineIcon from '@/assets/new-line-icon.svg';
import NewTwitterIcon from '@/assets/new-twitter-icon.svg';
import NewTelegramIcon from '@/assets/new-telegram-icon.svg';
import NewWeComIcon from '@/assets/new-wecom-icon.svg';
import NewWeChatMiniProgramIcon from '@/assets/new-wechat-mini-program-icon.svg';
import NewWechatOfficialAccountIcon from '@/assets/new-wechat-official-account-icon.svg';
import NewShopifyIcon from '@/assets/new-shopify-icon.svg';
import NewGooglePlayIcon from '@/assets/google-play-icon.svg';

class TicketMerge extends Component {
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      saveLoading: false,
      ticketRecordAutoMergeList: [],
      paramsList: [],
    };
  }
  componentDidMount() {
    this.queryTicketRecordAutoMerge();
  }
  //「是否合并工单」接口
  queryTicketRecordAutoMerge = () => {
    this.setState({ loading: true });
    this.props.dispatch({
      type: 'workOrderExtension/queryTicketRecordAutoMerge',
      callback: response => {
        let { code, data, msg } = response;
        if (200 === code) {
          let paramsList = [];
          let ticketRecordAutoMergeList = data?.map(item => {
            return {
              channelTypeId: item.channelTypeId,
              autoMergeFlag: +item.autoMergeFlag === 1 ? true : false,
              channelTypeName: item.channelTypeName,
            };
          });
          for (let i = 0; i < data.length; i++) {
            let item = {
              channelTypeId: data[i].channelTypeId,
              autoMergeFlag: data[i].autoMergeFlag,
            };
            paramsList.push(item);
          }
          this.setState({
            ticketRecordAutoMergeList: ticketRecordAutoMergeList,
            paramsList: paramsList,
          });
        } else {
          notification.error({
            message: msg,
          });
        }
        this.setState({ loading: false });
      },
    });
  };
  changeCheckbox = (checked, channelTypeId) => {
    console.log(checked);
    let { paramsList, ticketRecordAutoMergeList } = this.state;
    for (let n = 0; n < paramsList.length; n++) {
      if (channelTypeId == paramsList[n].channelTypeId) {
        paramsList[n].autoMergeFlag = checked ? 1 : 0;
      }
    }
    let mergeList = ticketRecordAutoMergeList?.map(item => {
      let i = { ...item };
      if (i.channelTypeId == channelTypeId) {
        i.autoMergeFlag = checked;
        return i;
      }
      return i;
    });
    this.setState({
      paramsList,
      ticketRecordAutoMergeList: mergeList,
    });
  };
  // 保存或者更新 「是否合并工单」接口
  saveTab4 = () => {
    let { paramsList } = this.state;
    this.setState({
      saveLoading: true,
    });
    this.props.dispatch({
      type: 'workOrderExtension/updateTicketRecordAutoMerge',
      payload: paramsList,
      callback: response => {
        let { code, data, msg } = response;
        if (200 === code) {
          notification.success({
            message: msg,
          });
          this.queryTicketRecordAutoMerge();
        } else {
          notification.error({
            message: msg,
          });
        }
        this.setState({ saveLoading: false });
      },
    });
  };

  cancelSaveTab4 = () => {
    this.queryTicketRecordAutoMerge();
  };
  render() {
    const { loading, ticketRecordAutoMergeList } = this.state;

    return (
      <div className={styles.workContent}>
        <div className={styles.workContentTop}>
          <div style={{ display: 'flex' }}>
            <span
              style={{ marginRight: 10, lineHeight: '35px', cursor: 'pointer' }}
              onClick={() => history.go(-1)}
            >
              {BackIcon()}
            </span>

            <FormattedMessage id="automation.config.merge.tickets" />
          </div>
          <HOCAuth authKey={'merge_ticket_settings'}>
            {authAccess => (
              <div style={{ display: 'inline-flex' }}>
                <Button
                  onClick={this.cancelSaveTab4}
                  style={{ marginRight: 20 }}
                >
                  <FormattedMessage
                    id="work.record.button.cancel"
                    defaultValue="取消"
                  />
                </Button>

                <Button
                  type="primary"
                  loading={this.state.saveLoading}
                  onClick={this.saveTab4}
                  disabled={authAccess}
                >
                  <FormattedMessage
                    id="customerInformation.add.basicInformation.button.save"
                    defaultValue="保存"
                  />
                </Button>
              </div>
            )}
          </HOCAuth>
        </div>

        <div className={styles.pageContent}>
          <Spin spinning={loading}>
            <div className={styles.pageContentTab4}>
              <Row style={{ fontSize: 18, marginBottom: '30px' }}>
                <Col span={8} style={{ textAlign: 'left' }}>
                  <FormattedMessage id="merge.work.order.configuration.channel.type" />
                </Col>
                <Col span={5} style={{ textAlign: 'center' }}>
                  <FormattedMessage id="merge.work.order.configuration.open.ticket" />
                </Col>
              </Row>
              {ticketRecordAutoMergeList?.map(item => {
                if (item.channelTypeId == '1') {
                  return (
                    <Row style={{ fontSize: 12, marginTop: '20px' }}>
                      <Col span={8} style={{ textAlign: 'left' }}>
                        <img src={TableEmailIcon} />
                        {item.channelTypeName}
                      </Col>
                      <Col span={5} style={{ textAlign: 'center' }}>
                        <Switch
                          onChange={e =>
                            this.changeCheckbox(e, item.channelTypeId)
                          }
                          checked={item.autoMergeFlag}
                        />
                      </Col>
                    </Row>
                  );
                } else if (item.channelTypeId == '2') {
                  return (
                    <Row style={{ fontSize: 12, marginTop: '20px' }}>
                      <Col span={8} style={{ textAlign: 'left' }}>
                        <img src={TableInfoIcon} />
                        {item.channelTypeName}
                      </Col>
                      <Col span={5} style={{ textAlign: 'center' }}>
                        <Switch
                          onChange={e =>
                            this.changeCheckbox(e, item.channelTypeId)
                          }
                          checked={item.autoMergeFlag}
                        />
                      </Col>
                    </Row>
                  );
                } else if (item.channelTypeId == '3') {
                  return (
                    <Row style={{ fontSize: 12, marginTop: '20px' }}>
                      <Col span={8} style={{ textAlign: 'left' }}>
                        <img src={FacebookIcon} />
                        {item.channelTypeName}
                      </Col>
                      <Col span={5} style={{ textAlign: 'center' }}>
                        <Switch
                          onChange={e =>
                            this.changeCheckbox(e, item.channelTypeId)
                          }
                          checked={item.autoMergeFlag}
                        />
                      </Col>
                    </Row>
                  );
                } else if (item.channelTypeId == '4') {
                  return (
                    <Row style={{ fontSize: 12, marginTop: '20px' }}>
                      <Col span={8} style={{ textAlign: 'left' }}>
                        <img src={WhatsAppIcon} />
                        {item.channelTypeName}
                      </Col>
                      <Col span={5} style={{ textAlign: 'center' }}>
                        <Switch
                          onChange={e =>
                            this.changeCheckbox(e, item.channelTypeId)
                          }
                          checked={item.autoMergeFlag}
                        />
                      </Col>
                    </Row>
                  );
                } else if (item.channelTypeId == '7') {
                  return (
                    <Row style={{ fontSize: 12, marginTop: '20px' }}>
                      <Col span={8} style={{ textAlign: 'left' }}>
                        <img src={TablePhoneIcon} />
                        {item.channelTypeName}
                      </Col>
                      <Col span={5} style={{ textAlign: 'center' }}>
                        <Switch
                          onChange={e =>
                            this.changeCheckbox(e, item.channelTypeId)
                          }
                          checked={item.autoMergeFlag}
                        />
                      </Col>
                    </Row>
                  );
                } else if (item.channelTypeId == '8') {
                  return (
                    <Row style={{ fontSize: 12, marginTop: '20px' }}>
                      <Col span={8} style={{ textAlign: 'left' }}>
                        <img src={ChatIcon} />
                        {item.channelTypeName}
                      </Col>
                      <Col span={5} style={{ textAlign: 'center' }}>
                        <Switch
                          onChange={e =>
                            this.changeCheckbox(e, item.channelTypeId)
                          }
                          checked={item.autoMergeFlag}
                        />
                      </Col>
                    </Row>
                  );
                } else if (item.channelTypeId == '9') {
                  return (
                    <Row style={{ fontSize: 12, marginTop: '20px' }}>
                      <Col span={8} style={{ textAlign: 'left' }}>
                        <img src={AppChatOutlinedIcon} />
                        {item.channelTypeName}
                      </Col>
                      <Col span={5} style={{ textAlign: 'center' }}>
                        <Switch
                          onChange={e =>
                            this.changeCheckbox(e, item.channelTypeId)
                          }
                          checked={item.autoMergeFlag}
                        />
                      </Col>
                    </Row>
                  );
                } else if (item.channelTypeId == '10') {
                  return (
                    <Row style={{ fontSize: 12, marginTop: '20px' }}>
                      <Col span={8} style={{ textAlign: 'left' }}>
                        <img src={WebVideoOutlinedIcon} />
                        {item.channelTypeName}
                      </Col>
                      <Col span={5} style={{ textAlign: 'center' }}>
                        <Switch
                          onChange={e =>
                            this.changeCheckbox(e, item.channelTypeId)
                          }
                          checked={item.autoMergeFlag}
                        />
                      </Col>
                    </Row>
                  );
                } else if (item.channelTypeId == '11') {
                  return (
                    <Row style={{ fontSize: 12, marginTop: '20px' }}>
                      <Col span={8} style={{ textAlign: 'left' }}>
                        <img src={AppVideoOutlinedIcon} />
                        {item.channelTypeName}
                      </Col>
                      <Col span={5} style={{ textAlign: 'center' }}>
                        <Switch
                          onChange={e =>
                            this.changeCheckbox(e, item.channelTypeId)
                          }
                          checked={item.autoMergeFlag}
                        />
                      </Col>
                    </Row>
                  );
                } else if (item.channelTypeId == '12') {
                  return (
                    <Row style={{ fontSize: 12, marginTop: '20px' }}>
                      <Col span={8} style={{ textAlign: 'left' }}>
                        <img src={AwsChannelIcon} />
                        {item.channelTypeName}
                      </Col>
                      <Col span={5} style={{ textAlign: 'center' }}>
                        <Switch
                          onChange={e =>
                            this.changeCheckbox(e, item.channelTypeId)
                          }
                          checked={item.autoMergeFlag}
                        />
                      </Col>
                    </Row>
                  );
                } else if (item.channelTypeId == '13') {
                  return (
                    <Row style={{ fontSize: 12, marginTop: '20px' }}>
                      <Col span={8} style={{ textAlign: 'left' }}>
                        <img src={NewInstagramIcon} />
                        {item.channelTypeName}
                      </Col>
                      <Col span={5} style={{ textAlign: 'center' }}>
                        <Switch
                          onChange={e =>
                            this.changeCheckbox(e, item.channelTypeId)
                          }
                          checked={item.autoMergeFlag}
                        />
                      </Col>
                    </Row>
                  );
                } else if (item.channelTypeId == '14') {
                  return (
                    <Row style={{ fontSize: 12, marginTop: '20px' }}>
                      <Col span={8} style={{ textAlign: 'left' }}>
                        <img src={NewLineIcon} />
                        {item.channelTypeName}
                      </Col>
                      <Col span={5} style={{ textAlign: 'center' }}>
                        <Switch
                          onChange={e =>
                            this.changeCheckbox(e, item.channelTypeId)
                          }
                          checked={item.autoMergeFlag}
                        />
                      </Col>
                    </Row>
                  );
                } else if (item.channelTypeId == '15') {
                  return (
                    <Row style={{ fontSize: 12, marginTop: '20px' }}>
                      <Col span={8} style={{ textAlign: 'left' }}>
                        <img src={NewWeComIcon} />
                        {item.channelTypeName}
                      </Col>
                      <Col span={5} style={{ textAlign: 'center' }}>
                        <Switch
                          onChange={e =>
                            this.changeCheckbox(e, item.channelTypeId)
                          }
                          checked={item.autoMergeFlag}
                        />
                      </Col>
                    </Row>
                  );
                } else if (item.channelTypeId == '16') {
                  return (
                    <Row style={{ fontSize: 12, marginTop: '20px' }}>
                      <Col span={8} style={{ textAlign: 'left' }}>
                        <img src={NewWechatOfficialAccountIcon} />
                        {item.channelTypeName}
                      </Col>
                      <Col span={5} style={{ textAlign: 'center' }}>
                        <Switch
                          onChange={e =>
                            this.changeCheckbox(e, item.channelTypeId)
                          }
                          checked={item.autoMergeFlag}
                        />
                      </Col>
                    </Row>
                  );
                } else if (item.channelTypeId == '17') {
                  return (
                    <Row style={{ fontSize: 12, marginTop: '20px' }}>
                      <Col span={8} style={{ textAlign: 'left' }}>
                        <img src={NewWebOnlineVoiceIcon} />
                        {item.channelTypeName}
                      </Col>
                      <Col span={5} style={{ textAlign: 'center' }}>
                        <Switch
                          onChange={e =>
                            this.changeCheckbox(e, item.channelTypeId)
                          }
                          checked={item.autoMergeFlag}
                        />
                      </Col>
                    </Row>
                  );
                } else if (item.channelTypeId == '18') {
                  return (
                    <Row style={{ fontSize: 12, marginTop: '20px' }}>
                      <Col span={8} style={{ textAlign: 'left' }}>
                        <img src={NewAppOnlineVoiceIcon} />
                        {item.channelTypeName}
                      </Col>
                      <Col span={5} style={{ textAlign: 'center' }}>
                        <Switch
                          onChange={e =>
                            this.changeCheckbox(e, item.channelTypeId)
                          }
                          checked={item.autoMergeFlag}
                        />
                      </Col>
                    </Row>
                  );
                } else if (item.channelTypeId == '19') {
                  return (
                    <Row style={{ fontSize: 12, marginTop: '20px' }}>
                      <Col span={8} style={{ textAlign: 'left' }}>
                        <img src={NewTwitterIcon} />
                        {item.channelTypeName}
                      </Col>
                      <Col span={5} style={{ textAlign: 'center' }}>
                        <Switch
                          onChange={e =>
                            this.changeCheckbox(e, item.channelTypeId)
                          }
                          checked={item.autoMergeFlag}
                        />
                      </Col>
                    </Row>
                  );
                } else if (item.channelTypeId == '20') {
                  return (
                    <Row style={{ fontSize: 12, marginTop: '20px' }}>
                      <Col span={8} style={{ textAlign: 'left' }}>
                        <img src={NewTelegramIcon} />
                        {item.channelTypeName}
                      </Col>
                      <Col span={5} style={{ textAlign: 'center' }}>
                        <Switch
                          onChange={e =>
                            this.changeCheckbox(e, item.channelTypeId)
                          }
                          checked={item.autoMergeFlag}
                        />
                      </Col>
                    </Row>
                  );
                } else if (item.channelTypeId == '21') {
                  return (
                    <Row style={{ fontSize: 12, marginTop: '20px' }}>
                      <Col span={8} style={{ textAlign: 'left' }}>
                        <img src={NewWeChatMiniProgramIcon} />
                        {item.channelTypeName}
                      </Col>
                      <Col span={5} style={{ textAlign: 'center' }}>
                        <Switch
                          onChange={e =>
                            this.changeCheckbox(e, item.channelTypeId)
                          }
                          checked={item.autoMergeFlag}
                        />
                      </Col>
                    </Row>
                  );
                } else if (item.channelTypeId == '22') {
                  return (
                    <Row style={{ fontSize: 12, marginTop: '20px' }}>
                      <Col span={8} style={{ textAlign: 'left' }}>
                        <img src={NewShopifyIcon} />
                        {item.channelTypeName}
                      </Col>
                      <Col span={5} style={{ textAlign: 'center' }}>
                        <Switch
                          onChange={e =>
                            this.changeCheckbox(e, item.channelTypeId)
                          }
                          checked={item.autoMergeFlag}
                        />
                      </Col>
                    </Row>
                  );
                } else if (item.channelTypeId == '23') {
                  return (
                    <Row style={{ fontSize: 12, marginTop: '20px' }}>
                      <Col span={8} style={{ textAlign: 'left' }}>
                        <img src={NewGooglePlayIcon} />
                        {item.channelTypeName}
                      </Col>
                      <Col span={5} style={{ textAlign: 'center' }}>
                        <Switch
                          onChange={e =>
                            this.changeCheckbox(e, item.channelTypeId)
                          }
                          checked={item.autoMergeFlag}
                        />
                      </Col>
                    </Row>
                  );
                }
              })}
            </div>
          </Spin>
        </div>
      </div>
    );
  }
}

const mapStateToProps = ({
  workOrderExtension,
  customerExt,
  layouts,
  newChannelConfiguration,
  workOrderCenter,
}) => {
  return {
    ...workOrderExtension,
    ...customerExt,
    authAccess: layouts.auth,
    ...newChannelConfiguration,
    ...workOrderCenter,
  };
};
export default connect(mapStateToProps)(TicketMerge);
