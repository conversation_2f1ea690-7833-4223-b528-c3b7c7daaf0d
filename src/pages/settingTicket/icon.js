import React from 'react';

export const AISettingIcon = () => (
  <svg
    style={{ marginRight: '10px' }}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <g clip-path="url(#clip0_3052_11691)">
      <path
        d="M5.35762 4.9756C5.67889 4.9756 5.96146 5.13222 6.1379 5.37306C6.26299 5.44296 6.36341 5.5507 6.42403 5.68067L6.44649 5.73634L10.551 17.4317C10.6838 17.8085 10.4468 18.23 10.0227 18.3731C9.61988 18.5089 9.1921 18.3462 9.03633 18.0059L9.01583 17.9502L7.95137 14.9199H2.63594L1.57247 17.9502C1.43961 18.3271 0.989188 18.5168 0.56563 18.3731C0.162085 18.2372 -0.0716924 17.8502 0.0197316 17.4883L0.0363331 17.4307L4.14083 5.73732C4.1816 5.62821 4.24801 5.53031 4.33516 5.45314C4.42233 5.37597 4.52768 5.32209 4.64083 5.29493C4.73118 5.19438 4.84161 5.11356 4.96504 5.0586C5.08841 5.00372 5.22263 4.97543 5.35762 4.9756ZM12.9055 9.95704C13.3319 9.95704 13.6817 10.1701 13.716 10.4404L13.718 10.4834V17.8467C13.718 18.1378 13.3533 18.3731 12.9055 18.3731C12.4793 18.3729 12.1305 18.1606 12.0969 17.8897L12.093 17.8467V10.4834C12.0932 10.1926 12.4573 9.95721 12.9055 9.95704ZM3.19454 13.3262H7.3918L5.29219 7.34767L3.19454 13.3262ZM14.1125 1.21388C14.2883 1.26111 14.4385 1.37655 14.5295 1.53419C14.6203 1.69173 14.6448 1.87904 14.5979 2.0547C14.5507 2.23059 14.4353 2.38161 14.2775 2.47267C14.1512 2.54546 14.0057 2.5756 13.8625 2.56056C13.5805 2.77492 13.1642 3.1036 12.8859 3.38185C12.6826 3.58525 12.452 3.86214 12.259 4.10646C12.2699 4.12332 12.2811 4.14076 12.2912 4.15821C12.449 4.43159 12.4953 4.75551 12.4211 5.06153C12.6114 5.19748 12.8116 5.33146 12.9885 5.4336C13.3968 5.66931 14.0144 5.92465 14.3176 6.04493C14.3303 6.03675 14.3435 6.02909 14.3566 6.02149C14.4685 5.95694 14.5919 5.91437 14.7199 5.89747C14.848 5.88061 14.9789 5.88942 15.1037 5.92286C15.2283 5.95632 15.3451 6.01423 15.4475 6.09278C15.5498 6.17133 15.6359 6.2692 15.7004 6.38087C15.7649 6.49271 15.8076 6.6171 15.8244 6.74513C15.8412 6.87301 15.8324 7.00335 15.799 7.12794C15.7656 7.25255 15.7076 7.36931 15.6291 7.47169C15.5505 7.57412 15.4528 7.66102 15.341 7.7256C15.2291 7.79018 15.1048 7.83178 14.9768 7.84864C14.8488 7.86545 14.7186 7.85664 14.594 7.82325C14.4693 7.78982 14.3517 7.7319 14.2492 7.65333C14.147 7.57478 14.0608 7.47688 13.9963 7.36524C13.9318 7.25354 13.8902 7.12984 13.8732 7.00196C13.8671 6.95519 13.8639 6.90733 13.8645 6.86036C13.6137 6.66156 13.0733 6.24448 12.6584 6.00489C12.4777 5.90056 12.2556 5.79287 12.0383 5.69435C11.9781 5.7472 11.9123 5.79432 11.842 5.83497C11.5602 5.99766 11.2247 6.04212 10.9104 5.95802C10.596 5.87377 10.3271 5.6676 10.1643 5.38575C10.0017 5.10392 9.95803 4.76842 10.0422 4.45411C10.1265 4.1398 10.3326 3.87173 10.6145 3.70899C10.8962 3.54647 11.2309 3.50189 11.5451 3.58595C11.568 3.59207 11.5911 3.59907 11.6135 3.60646C11.8758 3.40198 12.1937 3.14245 12.4201 2.91603C12.7017 2.63444 13.0351 2.21181 13.2492 1.9297C13.2434 1.85313 13.2505 1.77482 13.2707 1.69923C13.3179 1.5234 13.4334 1.37328 13.591 1.28224C13.7488 1.19124 13.9366 1.16674 14.1125 1.21388ZM11.1936 4.10646C10.8275 4.10646 10.5305 4.40351 10.5305 4.76954C10.5308 5.13531 10.8277 5.43165 11.1936 5.43165C11.5594 5.43161 11.8563 5.13528 11.8566 4.76954C11.8566 4.40354 11.5596 4.1065 11.1936 4.10646Z"
        fill="#999999"
      />
      <path
        d="M5.35762 4.9756C5.67889 4.9756 5.96146 5.13222 6.1379 5.37306C6.26299 5.44296 6.36341 5.5507 6.42403 5.68067L6.44649 5.73634L10.551 17.4317C10.6838 17.8085 10.4468 18.23 10.0227 18.3731C9.61988 18.5089 9.1921 18.3462 9.03633 18.0059L9.01583 17.9502L7.95137 14.9199H2.63594L1.57247 17.9502C1.43961 18.3271 0.989188 18.5168 0.56563 18.3731C0.162085 18.2372 -0.0716924 17.8502 0.0197316 17.4883L0.0363331 17.4307L4.14083 5.73732C4.1816 5.62821 4.24801 5.53031 4.33516 5.45314C4.42233 5.37597 4.52768 5.32209 4.64083 5.29493C4.73118 5.19438 4.84161 5.11356 4.96504 5.0586C5.08841 5.00372 5.22263 4.97543 5.35762 4.9756ZM12.9055 9.95704C13.3319 9.95704 13.6817 10.1701 13.716 10.4404L13.718 10.4834V17.8467C13.718 18.1378 13.3533 18.3731 12.9055 18.3731C12.4793 18.3729 12.1305 18.1606 12.0969 17.8897L12.093 17.8467V10.4834C12.0932 10.1926 12.4573 9.95721 12.9055 9.95704ZM3.19454 13.3262H7.3918L5.29219 7.34767L3.19454 13.3262ZM14.1125 1.21388C14.2883 1.26111 14.4385 1.37655 14.5295 1.53419C14.6203 1.69173 14.6448 1.87904 14.5979 2.0547C14.5507 2.23059 14.4353 2.38161 14.2775 2.47267C14.1512 2.54546 14.0057 2.5756 13.8625 2.56056C13.5805 2.77492 13.1642 3.1036 12.8859 3.38185C12.6826 3.58525 12.452 3.86214 12.259 4.10646C12.2699 4.12332 12.2811 4.14076 12.2912 4.15821C12.449 4.43159 12.4953 4.75551 12.4211 5.06153C12.6114 5.19748 12.8116 5.33146 12.9885 5.4336C13.3968 5.66931 14.0144 5.92465 14.3176 6.04493C14.3303 6.03675 14.3435 6.02909 14.3566 6.02149C14.4685 5.95694 14.5919 5.91437 14.7199 5.89747C14.848 5.88061 14.9789 5.88942 15.1037 5.92286C15.2283 5.95632 15.3451 6.01423 15.4475 6.09278C15.5498 6.17133 15.6359 6.2692 15.7004 6.38087C15.7649 6.49271 15.8076 6.6171 15.8244 6.74513C15.8412 6.87301 15.8324 7.00335 15.799 7.12794C15.7656 7.25255 15.7076 7.36931 15.6291 7.47169C15.5505 7.57412 15.4528 7.66102 15.341 7.7256C15.2291 7.79018 15.1048 7.83178 14.9768 7.84864C14.8488 7.86545 14.7186 7.85664 14.594 7.82325C14.4693 7.78982 14.3517 7.7319 14.2492 7.65333C14.147 7.57478 14.0608 7.47688 13.9963 7.36524C13.9318 7.25354 13.8902 7.12984 13.8732 7.00196C13.8671 6.95519 13.8639 6.90733 13.8645 6.86036C13.6137 6.66156 13.0733 6.24448 12.6584 6.00489C12.4777 5.90056 12.2556 5.79287 12.0383 5.69435C11.9781 5.7472 11.9123 5.79432 11.842 5.83497C11.5602 5.99766 11.2247 6.04212 10.9104 5.95802C10.596 5.87377 10.3271 5.6676 10.1643 5.38575C10.0017 5.10392 9.95803 4.76842 10.0422 4.45411C10.1265 4.1398 10.3326 3.87173 10.6145 3.70899C10.8962 3.54647 11.2309 3.50189 11.5451 3.58595C11.568 3.59207 11.5911 3.59907 11.6135 3.60646C11.8758 3.40198 12.1937 3.14245 12.4201 2.91603C12.7017 2.63444 13.0351 2.21181 13.2492 1.9297C13.2434 1.85313 13.2505 1.77482 13.2707 1.69923C13.3179 1.5234 13.4334 1.37328 13.591 1.28224C13.7488 1.19124 13.9366 1.16674 14.1125 1.21388ZM11.1936 4.10646C10.8275 4.10646 10.5305 4.40351 10.5305 4.76954C10.5308 5.13531 10.8277 5.43165 11.1936 5.43165C11.5594 5.43161 11.8563 5.13528 11.8566 4.76954C11.8566 4.40354 11.5596 4.1065 11.1936 4.10646Z"
        fill="url(#paint0_linear_3052_11691)"
      />
      <path
        d="M17.615 1.71432C17.5977 1.69648 17.5763 1.68304 17.5527 1.67511C17.5332 1.66856 17.5126 1.66593 17.492 1.66738C17.4715 1.66883 17.4514 1.67432 17.433 1.68355C17.4145 1.69278 17.3981 1.70555 17.3846 1.72115C17.3711 1.73675 17.3608 1.75485 17.3544 1.77444L17.0013 2.83958L15.9389 3.21335C15.9075 3.22418 15.8804 3.24471 15.8614 3.27198C15.8425 3.29924 15.8326 3.33183 15.8334 3.36505C15.8341 3.39828 15.8453 3.43041 15.8655 3.45681C15.8856 3.48322 15.9136 3.50254 15.9454 3.51199L16.9993 3.83087L17.3302 4.89013C17.3401 4.92184 17.3597 4.9496 17.3864 4.9694C17.413 4.9892 17.4452 5.00003 17.4784 5.00032C17.5115 5.00061 17.5439 4.99035 17.5709 4.97102C17.5979 4.95169 17.618 4.92428 17.6284 4.89275L17.9821 3.82891L19.0654 3.5061C19.0977 3.49693 19.1262 3.47761 19.1467 3.45101C19.1672 3.4244 19.1786 3.39191 19.1792 3.35831C19.1799 3.32472 19.1698 3.2918 19.1503 3.26441C19.1309 3.23702 19.1032 3.2166 19.0713 3.20617L17.9834 2.83696L17.6526 1.7777C17.6453 1.75389 17.6324 1.73216 17.615 1.71432Z"
        fill="url(#paint1_linear_3052_11691)"
      />
    </g>
    <defs>
      <linearGradient
        id="paint0_linear_3052_11691"
        x1="-2.10712e-06"
        y1="10.4066"
        x2="10.6549"
        y2="3.77144"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#187CFC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_3052_11691"
        x1="5.96147"
        y1="9.36072"
        x2="17.0857"
        y2="0.443069"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="#187CFC" />
        <stop offset="1" stop-color="#7700FE" />
      </linearGradient>
      <clipPath id="clip0_3052_11691">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const CloseTicketSettingIcon = () => (
  <svg
    style={{ marginRight: '10px' }}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <g clip-path="url(#clip0_3052_11834)">
      <path
        d="M18.8359 19.7637H11.9062C11.2637 19.7637 10.7422 19.2422 10.7422 18.5996V9.51367C10.7422 8.87109 11.2637 8.34961 11.9062 8.34961H18.8359C19.4785 8.34961 20 8.87109 20 9.51367V18.5996C20 19.2422 19.4785 19.7637 18.8359 19.7637ZM12.8926 18.4746H17.8496C18.332 18.4746 18.7246 18.082 18.7246 17.5996V10.5137C18.7246 10.0312 18.332 9.63867 17.8496 9.63867H12.8926C12.4102 9.63867 12.0176 10.0312 12.0176 10.5137V17.5996C12.0176 18.082 12.4082 18.4746 12.8926 18.4746ZM8.42969 19.7637H1.08008C0.552734 19.7637 0.126953 19.3359 0.126953 18.8105V15.9688C0.126953 15.4414 0.554688 15.0156 1.08008 15.0156H8.42969C8.95703 15.0156 9.38281 15.4434 9.38281 15.9688V18.8086C9.38477 19.3359 8.95703 19.7637 8.42969 19.7637ZM2.08789 18.4746H7.42188C7.80078 18.4746 8.10938 18.166 8.10938 17.7871V16.9922C8.10938 16.6133 7.80078 16.3047 7.42188 16.3047H2.08789C1.70898 16.3047 1.40039 16.6133 1.40039 16.9922V17.7871C1.40039 18.166 1.70898 18.4746 2.08789 18.4746ZM19.0312 0.15625H1.0957C0.560547 0.15625 0.126953 0.589844 0.126953 1.125V6.67969C0.126953 7.21484 0.560547 7.64844 1.0957 7.64844H19.0312C19.5664 7.64844 20 7.21484 20 6.67969V1.125C20 0.589844 19.5664 0.15625 19.0312 0.15625ZM18.7246 5.50977C18.7246 5.97266 18.3496 6.3457 17.8887 6.3457H2.23828C1.77539 6.3457 1.40234 5.9707 1.40234 5.50977V2.28125C1.40234 1.81836 1.77734 1.44531 2.23828 1.44531H17.8887C18.3516 1.44531 18.7246 1.82031 18.7246 2.28125V5.50977ZM8.51758 13.9805H0.992188C0.513672 13.9805 0.126953 13.5918 0.126953 13.1152V9.2168C0.126953 8.73828 0.515625 8.35156 0.992188 8.35156H8.51758C8.99609 8.35156 9.38281 8.74023 9.38281 9.2168V13.1133C9.38477 13.5918 8.99609 13.9805 8.51758 13.9805ZM2.26562 12.7051H7.24414C7.72266 12.7051 8.10938 12.3184 8.10938 11.8398V10.5039C8.10938 10.0254 7.72266 9.63867 7.24414 9.63867H2.26562C1.78711 9.63867 1.40039 10.0254 1.40039 10.5039V11.8398C1.40039 12.3164 1.78906 12.7051 2.26562 12.7051Z"
        fill="#3463FC"
      />
    </g>
    <defs>
      <clipPath id="clip0_3052_11834">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
export const MergeTicketSettingIcon = () => (
  <svg
    style={{ marginRight: '10px' }}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M8.03342 16.7669C7.70008 16.7669 7.33342 16.6335 7.06675 16.3669L1.56675 10.9002C1.03341 10.3669 1.03341 9.5002 1.56675 8.96686L6.43341 4.1002C6.96675 3.56686 7.83342 3.56686 8.36675 4.1002L13.8667 9.60019C14.4001 10.1335 14.4001 11.0002 13.8667 11.5335L9.00008 16.3669C8.73342 16.6335 8.36675 16.7669 8.03342 16.7669ZM7.40008 4.66686C7.30008 4.66686 7.20008 4.7002 7.13341 4.76686L2.26675 9.66686C2.20008 9.73353 2.16675 9.83353 2.16675 9.93353C2.16675 10.0335 2.20008 10.1335 2.26675 10.2002L7.76675 15.6669C7.90008 15.8002 8.13342 15.8002 8.26675 15.6669L13.1334 10.8002C13.2667 10.6669 13.2667 10.4335 13.1334 10.3002L7.66675 4.76686C7.60008 4.7002 7.50008 4.66686 7.40008 4.66686Z"
      fill="#3463FC"
    />
    <path
      d="M12.5999 16.1669C12.2333 16.1669 11.8999 16.0335 11.6333 15.7669L6.73325 10.9002C6.46659 10.6335 6.33325 10.3002 6.33325 9.93353C6.33325 9.56686 6.46659 9.23353 6.73325 8.96686L11.5999 4.1002C11.8666 3.83353 12.1999 3.7002 12.5666 3.7002C12.9333 3.7002 13.2666 3.83353 13.5333 4.1002L18.3999 8.96686C18.6666 9.23353 18.7999 9.56686 18.7999 9.93353C18.7999 10.3002 18.6666 10.6335 18.3999 10.9002L13.5666 15.7669C13.2999 16.0335 12.9666 16.1669 12.5999 16.1669ZM12.5999 4.66686C12.4999 4.66686 12.3999 4.7002 12.3333 4.76686L7.46659 9.66686C7.39992 9.73353 7.36659 9.83353 7.36659 9.93353C7.36659 10.0335 7.39992 10.1335 7.46659 10.2002L12.3333 15.0669C12.4666 15.2002 12.6999 15.2002 12.8333 15.0669L17.6999 10.2002C17.7666 10.1335 17.7999 10.0335 17.7999 9.93353C17.7999 9.83353 17.7666 9.73353 17.6999 9.66686L12.8333 4.76686C12.7666 4.7002 12.6999 4.66686 12.5999 4.66686Z"
      fill="#3463FC"
    />
  </svg>
);
export const QuickReplySettingIcon = () => (
  <svg
    style={{ marginRight: '10px' }}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M12.502 6.875H7.50195C7.15625 6.875 6.87695 6.5957 6.87695 6.25V1.87109C6.87695 1.52539 7.15625 1.24609 7.50195 1.24609H12.502C12.8477 1.24609 13.127 1.52539 13.127 1.87109V6.25C13.127 6.5957 12.8477 6.875 12.502 6.875ZM8.12695 5.625H11.877V2.49609H8.12695V5.625ZM6.88281 18.7402H1.88281C1.53711 18.7402 1.25781 18.4609 1.25781 18.1152V13.7363C1.25781 13.3906 1.53711 13.1113 1.88281 13.1113H6.88281C7.22852 13.1113 7.50781 13.3906 7.50781 13.7363V18.1152C7.50781 18.4609 7.22852 18.7402 6.88281 18.7402ZM2.50781 17.4902H6.25781V14.3613H2.50781V17.4902ZM18.0859 18.7402H13.0859C12.7402 18.7402 12.4609 18.4609 12.4609 18.1152V13.7363C12.4609 13.3906 12.7402 13.1113 13.0859 13.1113H18.0859C18.4316 13.1113 18.7109 13.3906 18.7109 13.7363V18.1152C18.7109 18.4609 18.4297 18.7402 18.0859 18.7402ZM13.709 17.4902H17.459V14.3613H13.709V17.4902Z"
      fill="#3463FC"
    />
    <path
      d="M10 10.6172C9.6543 10.6172 9.375 10.3379 9.375 9.99219V6.875C9.375 6.5293 9.6543 6.25 10 6.25C10.3457 6.25 10.625 6.5293 10.625 6.875V9.99219C10.625 10.3379 10.3457 10.6172 10 10.6172Z"
      fill="#3463FC"
    />
    <path
      d="M15.6289 10.5879H4.38281C4.03711 10.5879 3.75781 10.3086 3.75781 9.96289C3.75781 9.61719 4.03711 9.33789 4.38281 9.33789H15.6289C15.9746 9.33789 16.2539 9.61719 16.2539 9.96289C16.2539 10.3086 15.9727 10.5879 15.6289 10.5879Z"
      fill="#3463FC"
    />
    <path
      d="M4.38281 13.7559C4.03711 13.7559 3.75781 13.4766 3.75781 13.1309V9.99219C3.75781 9.64648 4.03711 9.36719 4.38281 9.36719C4.72852 9.36719 5.00781 9.64648 5.00781 9.99219V13.1309C5.00781 13.4766 4.72852 13.7559 4.38281 13.7559ZM15.6289 13.75C15.2832 13.75 15.0039 13.4707 15.0039 13.125V9.98633C15.0039 9.64062 15.2832 9.36133 15.6289 9.36133C15.9746 9.36133 16.2539 9.64062 16.2539 9.98633V13.125C16.2539 13.4707 15.9727 13.75 15.6289 13.75Z"
      fill="#3463FC"
    />
  </svg>
);
export const AgentQuickReplySettingIcon = () => (
  <svg
    style={{ marginRight: '10px' }}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M4.66667 6.70605H3.77778C2.79594 6.70605 2 7.54882 2 8.58839C2 9.62795 2.79594 10.4707 3.77778 10.4707H4.66667"
      stroke="#3463FC"
      stroke-width="1.6941"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M9.11111 14.2354H7.77778C6.79596 14.2354 6 15.0781 6 16.1177C6 17.1572 6.79596 18 7.77778 18H9.11111"
      stroke="#3463FC"
      stroke-width="1.6941"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M11.3333 2H9.99995C9.01812 2 8.22217 2.84275 8.22217 3.88233C8.22217 4.9219 9.01812 5.76466 9.99995 5.76466H11.3333"
      stroke="#3463FC"
      stroke-width="1.6941"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M10 5.76465H17.1111"
      stroke="#3463FC"
      stroke-width="1.6941"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M4.66663 10.4707H18"
      stroke="#3463FC"
      stroke-width="1.6941"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M9.11108 14.2354H14.4444"
      stroke="#3463FC"
      stroke-width="1.6941"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);
export const InfoSettingIcon = () => (
  <svg
    style={{ marginRight: '10px' }}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M9.99969 2.48047C12.8273 2.48047 15.1196 4.77208 15.1198 7.59961V15.1201H4.88055V7.59961C4.88079 4.77223 7.17231 2.4807 9.99969 2.48047Z"
      stroke="#3463FC"
      stroke-width="0.960007"
    />
    <path
      d="M4.40002 15.6001V7.60004C4.40002 4.50722 6.90724 2 10.0001 2C13.0929 2 15.6001 4.50722 15.6001 7.60004V15.6001M2 15.6001H18.0001"
      stroke="#3463FC"
      stroke-width="0.960007"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M10 18.0001C11.1046 18.0001 12.0001 17.1047 12.0001 16.0001V15.6001H8.00003V16.0001C8.00003 17.1047 8.89548 18.0001 10 18.0001Z"
      stroke="#3463FC"
      stroke-width="0.960007"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);
export const RouteRuleSettingIcon = () => (
  <svg
    style={{ marginRight: '10px' }}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M5.87055 3.03767C6.11609 3.03767 6.31698 2.83677 6.31698 2.59124C6.31698 2.3457 6.11609 2.14481 5.87055 2.14481C3.78127 2.14481 2.08037 3.8457 2.08037 5.93499C2.08037 6.95061 2.44421 7.89258 3.10493 8.58677C3.29689 8.78767 3.50895 8.96401 3.73662 9.11133C2.67412 9.6649 1.82814 10.4328 1.21654 11.406C0.424125 12.6649 0.0223389 14.2743 0.0223389 16.1895C0.0223389 16.435 0.223232 16.6359 0.468767 16.6359C0.714303 16.6359 0.915196 16.435 0.915196 16.1895C0.915196 14.4461 1.27011 12.9975 1.971 11.8814C2.62279 10.8435 3.59377 10.0711 4.85716 9.58454C5.13395 9.64927 5.42189 9.68276 5.71654 9.68276C5.96207 9.68276 6.16296 9.48186 6.16296 9.23633C6.16296 8.99079 5.96207 8.7899 5.71654 8.7899C4.95537 8.7899 4.25671 8.49749 3.75448 7.96847C3.25225 7.43945 2.97546 6.71624 2.97546 5.93276C2.97546 4.33677 4.27457 3.03767 5.87055 3.03767ZM18.7991 11.406C18.1875 10.4328 17.3415 9.66267 16.2768 9.1091C16.5045 8.96177 16.7165 8.78543 16.9085 8.58454C17.5692 7.89035 17.9331 6.94838 17.9331 5.93276C17.9331 3.84347 16.2322 2.14258 14.1429 2.14258C13.8973 2.14258 13.6964 2.34347 13.6964 2.58901C13.6964 2.83454 13.8973 3.03544 14.1429 3.03544C15.7411 3.03544 17.0402 4.33454 17.0402 5.93276C17.0402 6.71847 16.7634 7.44169 16.2612 7.96847C15.7567 8.49749 15.0603 8.7899 14.2991 8.7899C14.0536 8.7899 13.8527 8.99079 13.8527 9.23633C13.8527 9.48186 14.0536 9.68276 14.2991 9.68276C14.5938 9.68276 14.8817 9.64927 15.1585 9.58454C16.4197 10.0711 17.3906 10.8435 18.0447 11.8814C18.7456 12.9953 19.1005 14.4461 19.1005 16.1895C19.1005 16.435 19.3014 16.6359 19.5469 16.6359C19.7924 16.6359 19.9933 16.435 19.9933 16.1895C19.9933 14.2743 19.5915 12.6649 18.7991 11.406Z"
      fill="#3463FC"
    />
    <path
      d="M16.1786 14.2094C15.6429 12.8701 14.8594 11.7384 13.913 10.9415C13.7255 10.7831 13.442 10.8076 13.2835 10.9951C13.125 11.1826 13.1496 11.4661 13.3371 11.6246C14.1719 12.3277 14.8683 13.3366 15.3505 14.542C15.7634 15.5777 16.0045 16.7116 16.0536 17.8478H3.77904C3.85494 16.0286 4.40404 14.2473 5.31253 12.8991C6.06476 11.7853 7.41074 10.4549 9.5536 10.4549L9.91074 10.446H9.9152C12.4442 10.446 14.5 8.38797 14.5 5.86119C14.5 3.33217 12.442 1.27637 9.9152 1.27637C7.38619 1.27637 5.33038 3.3344 5.33038 5.86119C5.33038 7.5844 6.28574 9.08887 7.69645 9.87235C7.34154 9.99735 6.99779 10.1581 6.66744 10.3567C5.8661 10.8389 5.16074 11.5264 4.57369 12.3991C3.49556 13.9973 2.87726 16.1402 2.87726 18.2808C2.87726 18.5331 3.08262 18.7407 3.33708 18.7407H16.4956C16.7478 18.7407 16.9554 18.5353 16.9554 18.2808C16.9554 16.888 16.6853 15.4795 16.1786 14.2094ZM6.22324 5.86119C6.22324 3.82547 7.87949 2.16922 9.9152 2.16922C11.9509 2.16922 13.6072 3.82547 13.6072 5.86119C13.6072 7.8969 11.9509 9.55315 9.9152 9.55315C7.87949 9.55315 6.22324 7.8969 6.22324 5.86119Z"
      fill="#3463FC"
    />
  </svg>
);
export const TicketWarningSettingIcon = () => (
  <svg
    style={{ marginRight: '10px' }}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M9.99998 13.2002C9.49998 13.2002 9.09998 13.6002 9.09998 14.1002C9.09998 14.6002 9.49998 15.0002 9.99998 15.0002C10.5 15.0002 10.9 14.6002 10.9 14.1002C10.9 13.6002 10.5 13.2002 9.99998 13.2002Z"
      fill="#3463FC"
    />
    <path
      d="M18.4 16.4004C19 15.4004 19 14.3004 18.4 13.3004L12.8 3.50039C12.2 2.50039 11.2 1.90039 10.1 1.90039C9 1.90039 8 2.50039 7.4 3.50039L1.7 13.3004C1.1 14.3004 1.1 15.5004 1.7 16.4004C2.3 17.4004 3.3 17.9004 4.4 17.9004H15.7C16.8 18.0004 17.8 17.4004 18.4 16.4004ZM17.1 15.7004C16.8 16.2004 16.2 16.6004 15.6 16.6004H4.3C3.7 16.6004 3.1 16.3004 2.8 15.8004C2.5 15.3004 2.5 14.6004 2.8 14.1004L8.5 4.30039C8.8 3.80039 9.4 3.40039 10 3.40039C10.6 3.40039 11.2 3.70039 11.5 4.30039L17.1 14.1004C17.5 14.5004 17.5 15.2004 17.1 15.7004Z"
      fill="#3463FC"
    />
    <path
      d="M9.79998 6.89965C9.39998 6.99965 9.09998 7.39965 9.09998 7.89965C9.09998 8.19965 9.09998 8.49965 9.19998 8.79965C9.29998 9.89965 9.29998 10.8996 9.39998 11.9996C9.39998 12.3996 9.69998 12.5996 10.1 12.5996C10.5 12.5996 10.8 12.2996 10.8 11.8996V11.1996C10.8 10.4996 10.9 9.79965 10.9 9.09965C10.9 8.59965 11 8.19965 11 7.69965C11 7.49965 11 7.39965 10.9 7.19965C10.7 6.99965 10.2 6.79965 9.79998 6.89965Z"
      fill="#3463FC"
    />
  </svg>
);
export const PermissionSettingsIcon = () => (
  <svg
    style={{ marginRight: '10px' }}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M17.4146 13.4883H16.9048V12.3047C16.9419 11.6406 16.6079 11.0098 16.0376 10.666C15.7579 10.4973 15.4375 10.4082 15.1108 10.4082C14.7842 10.4082 14.4638 10.4973 14.1841 10.666C13.6138 11.0098 13.2798 11.6406 13.3169 12.3047V13.4883H12.8071C12.561 13.4883 12.3638 13.6875 12.3638 13.9316V17.1699C12.3638 17.416 12.563 17.6133 12.8071 17.6133H17.4146C17.6587 17.6133 17.8579 17.4141 17.8579 17.1699V13.9316C17.8579 13.6875 17.6606 13.4883 17.4146 13.4883ZM15.4321 15.7734V16.6836C15.4321 16.7031 15.4243 16.7227 15.4087 16.7383C15.395 16.752 15.3755 16.7617 15.354 16.7617H14.8833C14.8638 16.7617 14.8442 16.7539 14.8286 16.7383C14.8149 16.7246 14.8071 16.7051 14.8071 16.6836V15.7734C14.5122 15.625 14.3579 15.293 14.4341 14.9727C14.5103 14.6523 14.7974 14.4258 15.1274 14.4258C15.4575 14.4258 15.7446 14.6523 15.8208 14.9727C15.897 15.293 15.7427 15.625 15.4478 15.7734H15.4321ZM16.2153 13.4883H14.0239V12.3379C14.0239 11.7324 14.5142 11.2422 15.1196 11.2422C15.7251 11.2422 16.2153 11.7324 16.2153 12.3379V13.4883Z"
      fill="#3463FC"
    />
    <path
      d="M14.2095 5.82031C14.2095 3.71484 12.4966 2 10.3892 2C8.28176 2 6.56887 3.71289 6.56887 5.82031C6.56887 7.92773 8.28176 9.64062 10.3892 9.64062C12.4966 9.64062 14.2095 7.92578 14.2095 5.82031ZM10.3911 8.07617C9.147 8.07617 8.13333 7.06445 8.13333 5.81836C8.13333 4.57227 9.14505 3.56055 10.3911 3.56055C11.6353 3.56055 12.649 4.57227 12.649 5.81836C12.649 7.06445 11.6353 8.07617 10.3911 8.07617ZM4.80716 16.1348C5.09426 15.1914 5.63919 14.3359 6.39114 13.6699C7.3052 12.8594 8.46145 12.3848 9.6763 12.3184C9.84231 12.3086 9.97317 12.1719 9.97317 12.0059V11.0664C9.97317 10.8887 9.82473 10.7461 9.647 10.7539C8.06106 10.8281 6.54739 11.4414 5.35598 12.498C4.09426 13.6191 3.28176 15.1543 3.07083 16.8203L3.00247 17.3516C2.97903 17.5391 3.12551 17.7051 3.31497 17.7031L9.76223 17.6641C9.93411 17.6621 10.0747 17.5215 10.0728 17.3496L10.0669 16.4121C10.065 16.2402 9.92434 16.0996 9.75247 16.1016L4.80716 16.1348Z"
      fill="#3463FC"
    />
  </svg>
);
export const CustomerAttrIcon = () => (
  <svg
    style={{ marginRight: '10px' }}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M13.4531 5.95898C13.4531 3.85352 11.7402 2.13867 9.63281 2.13867C7.52539 2.13867 5.8125 3.85156 5.8125 5.95898C5.8125 8.06641 7.52539 9.7793 9.63281 9.7793C11.7402 9.7793 13.4531 8.06445 13.4531 5.95898ZM9.63476 8.21484C8.39062 8.21484 7.37695 7.20312 7.37695 5.95703C7.37695 4.71094 8.38867 3.69922 9.63476 3.69922C10.8789 3.69922 11.8926 4.71094 11.8926 5.95703C11.8926 7.20312 10.8789 8.21484 9.63476 8.21484ZM4.05078 16.2734C4.33789 15.3301 4.88281 14.4746 5.63476 13.8086C6.54882 12.998 7.70507 12.5234 8.91992 12.457C9.08593 12.4473 9.21679 12.3105 9.21679 12.1445V11.2051C9.21679 11.0273 9.06836 10.8848 8.89062 10.8926C7.30468 10.9668 5.79101 11.5801 4.59961 12.6367C3.33789 13.7578 2.52539 15.293 2.31445 16.959L2.24609 17.4902C2.22265 17.6777 2.36914 17.8438 2.55859 17.8418L9.00586 17.8027C9.17773 17.8008 9.31836 17.6602 9.3164 17.4883L9.31054 16.5508C9.30859 16.3789 9.16796 16.2383 8.99609 16.2402L4.05078 16.2734ZM16.6465 11.7227H12.1719C11.7305 11.7227 11.3711 12.082 11.3711 12.5234V16.998C11.3711 17.4395 11.7305 17.7988 12.1719 17.7988H16.6465C17.0879 17.7988 17.4473 17.4395 17.4473 16.998V12.5234C17.4473 12.082 17.0879 11.7227 16.6465 11.7227ZM15.8848 16.2363H12.9336V13.2852H15.8848V16.2363Z"
      fill="#3463FC"
    />
  </svg>
);
export const TicketAttrIcon = () => (
  <svg
    style={{ marginRight: '10px' }}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M17.5125 6.57458L10.8021 9.0479C10.3721 9.20597 9.90049 9.20877 9.46872 9.05583L2.50206 6.57999C2.25839 6.49749 2.04672 6.34069 1.8968 6.13164C1.74687 5.9226 1.66624 5.67182 1.66624 5.41457C1.66624 5.15732 1.74687 4.90654 1.8968 4.69749C2.04672 4.48845 2.25839 4.33165 2.50206 4.24915L9.46872 1.77333C9.90042 1.62014 10.3721 1.62279 10.8021 1.78083L17.5125 4.25415C17.7525 4.33889 17.9603 4.49598 18.1073 4.70377C18.2543 4.91157 18.3332 5.15983 18.3332 5.41436C18.3332 5.66889 18.2543 5.91716 18.1073 6.12495C17.9603 6.33275 17.7525 6.48984 17.5125 6.57458ZM17.0754 5.38624L10.365 2.9129C10.2124 2.85717 10.0453 2.85614 9.89206 2.90999L2.92536 5.38583C2.92203 5.38615 2.91881 5.38719 2.91592 5.38887C2.91303 5.39054 2.91053 5.39282 2.90859 5.39555C2.90666 5.39827 2.90533 5.40138 2.9047 5.40467C2.90407 5.40795 2.90416 5.41133 2.90495 5.41458C2.90415 5.4178 2.90407 5.42116 2.9047 5.42442C2.90533 5.42768 2.90666 5.43076 2.90861 5.43346C2.91055 5.43615 2.91305 5.43839 2.91594 5.44002C2.91884 5.44165 2.92205 5.44263 2.92536 5.4429L9.89206 7.91874C10.0452 7.97304 10.2125 7.972 10.365 7.91581L17.0754 5.4429C17.0785 5.44254 17.0814 5.44157 17.0841 5.44005C17.0868 5.43853 17.0892 5.43649 17.0911 5.43405C17.093 5.43161 17.0944 5.42881 17.0952 5.42582C17.096 5.42284 17.0962 5.41972 17.0958 5.41665C17.097 5.41329 17.0973 5.40971 17.0969 5.40619C17.0964 5.40266 17.0952 5.39929 17.0932 5.39634C17.0912 5.39339 17.0886 5.39094 17.0855 5.38919C17.0824 5.38744 17.0789 5.38643 17.0754 5.38624ZM2.49997 14.385L9.87165 17.055C10.0416 17.1163 10.2278 17.1153 10.397 17.0521L17.4983 14.3854C17.6512 14.3274 17.821 14.3326 17.9701 14.3998C18.1192 14.4669 18.2356 14.5906 18.2935 14.7435C18.3515 14.8965 18.3463 15.0662 18.2791 15.2153C18.212 15.3644 18.0883 15.4808 17.9354 15.5387L10.8341 18.2054C10.609 18.2895 10.3707 18.3327 10.1304 18.3329C9.89794 18.3331 9.66725 18.2926 9.44874 18.2133L2.07667 15.5433C1.99986 15.5161 1.92921 15.4739 1.86881 15.4192C1.8084 15.3646 1.75943 15.2984 1.72473 15.2247C1.69003 15.151 1.67028 15.0711 1.66663 14.9897C1.66299 14.9083 1.67551 14.827 1.70348 14.7504C1.73145 14.6739 1.77431 14.6037 1.82958 14.5438C1.88486 14.4839 1.95145 14.4356 2.02551 14.4016C2.09958 14.3676 2.17964 14.3487 2.26108 14.3458C2.34251 14.343 2.42371 14.3563 2.49997 14.385Z"
      fill="#3463FC"
    />
    <path
      d="M10.1321 13.7463C9.89966 13.7462 9.669 13.7058 9.45043 13.6267L2.07836 10.9583C2.00037 10.932 1.92842 10.8904 1.86674 10.8359C1.80507 10.7814 1.75491 10.7151 1.71922 10.641C1.68353 10.5668 1.66303 10.4863 1.65891 10.4041C1.6548 10.3219 1.66716 10.2397 1.69527 10.1623C1.72338 10.085 1.76667 10.014 1.82259 9.95362C1.87852 9.89324 1.94595 9.84464 2.02092 9.81069C2.0959 9.77674 2.1769 9.75812 2.25918 9.75593C2.34145 9.75373 2.42334 9.76801 2.50002 9.79791L9.8717 12.4684C10.0417 12.5293 10.2278 12.5282 10.3971 12.465L17.4984 9.79836C17.5741 9.76966 17.6547 9.75616 17.7357 9.75863C17.8166 9.76109 17.8963 9.77948 17.9701 9.81273C18.044 9.84599 18.1105 9.89346 18.166 9.95244C18.2215 10.0114 18.2649 10.0808 18.2936 10.1565C18.3222 10.2322 18.3358 10.3129 18.3333 10.3938C18.3308 10.4747 18.3124 10.5544 18.2792 10.6283C18.2459 10.7021 18.1984 10.7687 18.1395 10.8242C18.0805 10.8797 18.0112 10.923 17.9354 10.9517L10.8342 13.6184C10.6098 13.7028 10.3719 13.7462 10.1321 13.7463L10.1321 13.7463Z"
      fill="#3463FC"
    />
  </svg>
);
export const TicketTypeIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    style={{ marginRight: '10px' }}
  >
    <path
      d="M3.94668 9.24678C3.40105 9.24722 2.86756 9.08579 2.4137 8.78293C1.95985 8.48007 1.60603 8.04939 1.39703 7.54538C1.18802 7.04137 1.13322 6.48669 1.23956 5.95153C1.3459 5.41637 1.6086 4.92477 1.99441 4.53896C2.38023 4.15314 2.87182 3.89044 3.40699 3.7841C3.94215 3.67777 4.49683 3.73257 5.00084 3.94157C5.50485 4.15058 5.93553 4.50439 6.23839 4.95825C6.54125 5.4121 6.70267 5.9456 6.70223 6.49122C6.70106 7.22168 6.41036 7.92188 5.89385 8.4384C5.37734 8.95491 4.67714 9.2456 3.94668 9.24678ZM3.94668 4.75567C3.60332 4.75523 3.26755 4.85664 2.98185 5.04708C2.69615 5.23752 2.47335 5.50842 2.34165 5.82551C2.20995 6.1426 2.17526 6.49164 2.24198 6.82845C2.30869 7.16526 2.47381 7.47471 2.71645 7.71766C2.95908 7.9606 3.26832 8.12612 3.60505 8.19327C3.94177 8.26041 4.29085 8.22617 4.60811 8.09488C4.92537 7.96358 5.19656 7.74113 5.38736 7.45568C5.57817 7.17022 5.68001 6.83458 5.68001 6.49122C5.68001 6.03131 5.49746 5.5902 5.17246 5.26478C4.84747 4.93937 4.40659 4.75626 3.94668 4.75567ZM3.94668 16.2668C3.40115 16.2672 2.86774 16.1059 2.41393 15.8031C1.96013 15.5003 1.60631 15.0698 1.39724 14.5659C1.18817 14.062 1.13324 13.5075 1.2394 12.9723C1.34555 12.4372 1.60803 11.9456 1.99363 11.5597C2.37922 11.1738 2.8706 10.911 3.40562 10.8044C3.94064 10.6978 4.49525 10.7523 4.9993 10.9609C5.50334 11.1696 5.93418 11.5231 6.23731 11.9766C6.54044 12.4302 6.70223 12.9635 6.70223 13.509C6.70106 14.2397 6.41044 14.9401 5.89399 15.457C5.37754 15.9738 4.67734 16.265 3.94668 16.2668ZM3.94668 11.7757C3.60323 11.7752 3.26737 11.8767 2.98162 12.0672C2.69587 12.2578 2.47307 12.5288 2.34144 12.846C2.2098 13.1632 2.17524 13.5124 2.24214 13.8493C2.30904 14.1861 2.47438 14.4956 2.71723 14.7384C2.96009 14.9813 3.26954 15.1466 3.60641 15.2135C3.94328 15.2804 4.29243 15.2459 4.60965 15.1142C4.92687 14.9826 5.19791 14.7598 5.38844 14.4741C5.57898 14.1883 5.68045 13.8525 5.68001 13.509C5.67884 13.0499 5.49577 12.6099 5.1709 12.2854C4.84602 11.961 4.40582 11.7785 3.94668 11.7779V11.7757ZM8.43779 3.44678H18.8111V4.469H8.43779V3.44678ZM8.43779 7.47567H15.5933V8.49789H8.43779V7.47567Z"
      fill="#3463FC"
    />
    <path
      d="M3.43555 8.59541H4.45777V11.4021H3.43555V8.59541ZM3.43555 15.6154H4.45777V18.4221H3.43555V15.6154ZM3.43555 1.57764H4.45777V4.3843H3.43555V1.57764ZM8.43777 15.531H15.5933V16.5532H8.43777V15.531ZM8.43777 11.5021H18.8111V12.5243H8.43777V11.5021Z"
      fill="#3463FC"
    />
  </svg>
);
export const LeftReturnArrow = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="25"
    viewBox="0 0 24 25"
    fill="none"
  >
    <path
      d="M2.22319 11.6863L10.7447 3.5525C10.8159 3.48449 10.8999 3.43117 10.9918 3.3956C11.0836 3.36003 11.1816 3.34291 11.2801 3.34521C11.3785 3.3475 11.4756 3.36918 11.5657 3.409C11.6558 3.44881 11.7372 3.50599 11.8052 3.57725L12.3227 4.1195C12.4489 4.25157 12.5227 4.42518 12.53 4.60774C12.5373 4.7903 12.4777 4.96926 12.3624 5.111L12.2979 5.18L5.80744 11.375H21.3752C21.5741 11.375 21.7649 11.454 21.9055 11.5947C22.0462 11.7353 22.1252 11.9261 22.1252 12.125V12.875C22.1252 13.0739 22.0462 13.2647 21.9055 13.4053C21.7649 13.546 21.5741 13.625 21.3752 13.625H5.80744L12.2979 19.82C12.4301 19.9461 12.5118 20.1161 12.5275 20.2981C12.5432 20.4801 12.492 20.6616 12.3834 20.8085L12.3234 20.8805L11.8052 21.4228C11.7372 21.494 11.6558 21.5512 11.5657 21.591C11.4756 21.6308 11.3785 21.6525 11.2801 21.6548C11.1816 21.6571 11.0836 21.64 10.9918 21.6044C10.8999 21.5688 10.8159 21.5155 10.7447 21.4475L2.22319 13.3138C2.11314 13.2087 2.02555 13.0824 1.9657 12.9426C1.90586 12.8027 1.875 12.6521 1.875 12.5C1.875 12.3479 1.90586 12.1973 1.9657 12.0574C2.02555 11.9176 2.11314 11.7913 2.22319 11.6863Z"
      fill="#3463FC"
    />
  </svg>
);

export const CompanyInfoIcon = () => (
  <svg
    style={{ marginRight: '10px' }}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M13.4531 5.95898C13.4531 3.85352 11.7402 2.13867 9.63282 2.13867C7.52539 2.13867 5.8125 3.85156 5.8125 5.95898C5.8125 8.06641 7.52539 9.7793 9.63282 9.7793C11.7402 9.7793 13.4531 8.06445 13.4531 5.95898ZM9.63477 8.21484C8.39063 8.21484 7.37696 7.20312 7.37696 5.95703C7.37696 4.71094 8.38868 3.69922 9.63477 3.69922C10.8789 3.69922 11.8926 4.71094 11.8926 5.95703C11.8926 7.20312 10.8789 8.21484 9.63477 8.21484ZM4.05078 16.2734C4.33789 15.3301 4.88282 14.4746 5.63477 13.8086C6.54883 12.998 7.70508 12.5234 8.91993 12.457C9.08594 12.4473 9.2168 12.3105 9.2168 12.1445V11.2051C9.2168 11.0273 9.06836 10.8848 8.89063 10.8926C7.30469 10.9668 5.79102 11.5801 4.59961 12.6367C3.33789 13.7578 2.52539 15.293 2.31446 16.959L2.2461 17.4902C2.22266 17.6777 2.36914 17.8438 2.5586 17.8418L9.00586 17.8027C9.17774 17.8008 9.31836 17.6602 9.31641 17.4883L9.31055 16.5508C9.3086 16.3789 9.16797 16.2383 8.9961 16.2402L4.05078 16.2734ZM16.6465 11.7227H12.1719C11.7305 11.7227 11.3711 12.082 11.3711 12.5234V16.998C11.3711 17.4395 11.7305 17.7988 12.1719 17.7988H16.6465C17.0879 17.7988 17.4473 17.4395 17.4473 16.998V12.5234C17.4473 12.082 17.0879 11.7227 16.6465 11.7227ZM15.8848 16.2363H12.9336V13.2852H15.8848V16.2363Z"
      fill="#3463FC"
    />
  </svg>
);
export const PermissionsIcon = () => (
  <svg
    style={{ marginRight: '10px' }}
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M17.4146 13.4883H16.9048V12.3047C16.9419 11.6406 16.6079 11.0098 16.0376 10.666C15.7579 10.4973 15.4375 10.4082 15.1108 10.4082C14.7842 10.4082 14.4638 10.4973 14.1841 10.666C13.6138 11.0098 13.2798 11.6406 13.3169 12.3047V13.4883H12.8071C12.561 13.4883 12.3638 13.6875 12.3638 13.9316V17.1699C12.3638 17.416 12.563 17.6133 12.8071 17.6133H17.4146C17.6587 17.6133 17.8579 17.4141 17.8579 17.1699V13.9316C17.8579 13.6875 17.6606 13.4883 17.4146 13.4883ZM15.4321 15.7734V16.6836C15.4321 16.7031 15.4243 16.7227 15.4087 16.7383C15.395 16.752 15.3755 16.7617 15.354 16.7617H14.8833C14.8638 16.7617 14.8442 16.7539 14.8286 16.7383C14.8149 16.7246 14.8071 16.7051 14.8071 16.6836V15.7734C14.5122 15.625 14.3579 15.293 14.4341 14.9727C14.5103 14.6523 14.7974 14.4258 15.1274 14.4258C15.4575 14.4258 15.7446 14.6523 15.8208 14.9727C15.897 15.293 15.7427 15.625 15.4478 15.7734H15.4321ZM16.2153 13.4883H14.0239V12.3379C14.0239 11.7324 14.5142 11.2422 15.1196 11.2422C15.7251 11.2422 16.2153 11.7324 16.2153 12.3379V13.4883Z"
      fill="#3463FC"
    />
    <path
      d="M14.2095 5.82031C14.2095 3.71484 12.4966 2 10.3892 2C8.28176 2 6.56887 3.71289 6.56887 5.82031C6.56887 7.92773 8.28176 9.64062 10.3892 9.64062C12.4966 9.64062 14.2095 7.92578 14.2095 5.82031ZM10.3911 8.07617C9.147 8.07617 8.13333 7.06445 8.13333 5.81836C8.13333 4.57227 9.14505 3.56055 10.3911 3.56055C11.6353 3.56055 12.649 4.57227 12.649 5.81836C12.649 7.06445 11.6353 8.07617 10.3911 8.07617ZM4.80716 16.1348C5.09426 15.1914 5.63919 14.3359 6.39114 13.6699C7.3052 12.8594 8.46145 12.3848 9.6763 12.3184C9.84231 12.3086 9.97317 12.1719 9.97317 12.0059V11.0664C9.97317 10.8887 9.82473 10.7461 9.647 10.7539C8.06106 10.8281 6.54739 11.4414 5.35598 12.498C4.09426 13.6191 3.28176 15.1543 3.07083 16.8203L3.00247 17.3516C2.97903 17.5391 3.12551 17.7051 3.31497 17.7031L9.76223 17.6641C9.93411 17.6621 10.0747 17.5215 10.0728 17.3496L10.0669 16.4121C10.065 16.2402 9.92434 16.0996 9.75247 16.1016L4.80716 16.1348Z"
      fill="#3463FC"
    />
  </svg>
);
export const TicketSlaIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    style={{ marginRight: '10px' }}
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M2.5 4.16675L17.5 1.66675V4.58341L2.5 7.08341V4.16675Z"
      stroke="#3463FC"
      stroke-width="1.25"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M2.5 10L17.5 7.5V10.4167L2.5 12.9167V10Z"
      stroke="#3463FC"
      stroke-width="1.25"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
    <path
      d="M2.5 15.8333L17.5 13.3333V16.2499L2.5 18.7499V15.8333Z"
      stroke="#3463FC"
      stroke-width="1.25"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);
export const AgentStatusIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    style={{ marginRight: '10px' }}
  >
    <path
      d="M8.4479 10.3612C7.33212 10.3612 6.28071 9.92668 5.49216 9.13634C4.70181 8.346 4.2673 7.29638 4.2673 6.1806C4.2673 5.06482 4.70181 4.01341 5.49216 3.22485C6.28071 2.43451 7.33033 2 8.4479 2C9.56547 2 10.6151 2.43451 11.4036 3.22485C12.194 4.0152 12.6285 5.06482 12.6285 6.1806C12.6285 7.29638 12.194 8.34778 11.4036 9.13634C10.6133 9.92668 9.56368 10.3612 8.4479 10.3612ZM8.4479 2.71524C6.53641 2.71524 4.98255 4.26911 4.98255 6.1806C4.98255 8.09208 6.53641 9.64595 8.4479 9.64595C10.3594 9.64595 11.9133 8.09208 11.9133 6.1806C11.9133 4.26911 10.3576 2.71524 8.4479 2.71524Z"
      fill="#3463FC"
    />
    <path
      d="M2.35762 17.9894C2.16093 17.9894 2 17.8284 2 17.6318C2 15.6648 2.56683 13.6979 3.55744 12.2299C4.09745 11.4306 4.74117 10.8012 5.47429 10.3613C6.27537 9.87852 7.16406 9.63354 8.11175 9.63354C8.30844 9.63354 8.46938 9.79447 8.46938 9.99117C8.46938 10.1879 8.30844 10.3488 8.11175 10.3488C6.10907 10.3488 4.85203 11.5897 4.14931 12.6304C3.23737 13.9822 2.71524 15.8043 2.71524 17.6318C2.71524 17.8284 2.5561 17.9894 2.35762 17.9894Z"
      fill="#3463FC"
    />
    <path
      d="M13.0451 18.0001H2.37013C2.17344 18.0001 2.01251 17.8392 2.01251 17.6425C2.01251 17.4458 2.17344 17.2849 2.37013 17.2849H13.0469C13.2436 17.2849 13.4045 17.4458 13.4045 17.6425C13.4045 17.8392 13.2436 18.0001 13.0451 18.0001ZM13.5923 13.8016C13.46 13.8016 13.333 13.7283 13.2704 13.5996C12.8395 12.7145 12.2923 11.9867 11.6415 11.4396C11.4913 11.3126 11.4716 11.0873 11.5986 10.9353C11.7255 10.7851 11.9508 10.7654 12.1028 10.8924C12.8288 11.5057 13.4385 12.3104 13.9142 13.2867C14 13.4637 13.9267 13.6783 13.7497 13.7641C13.6978 13.7909 13.6442 13.8016 13.5923 13.8016ZM15.7434 17.9983C14.4917 17.9983 13.4743 16.9809 13.4743 15.7292C13.4743 14.4775 14.4935 13.4583 15.7434 13.4583C16.9951 13.4583 18.0143 14.4775 18.0143 15.7292C18.0143 16.9809 16.9951 17.9983 15.7434 17.9983ZM15.7434 14.1736C14.8869 14.1736 14.1895 14.8709 14.1895 15.7274C14.1895 16.5839 14.8869 17.2813 15.7434 17.2813C16.5999 17.2813 17.2973 16.5839 17.2973 15.7274C17.2973 14.8709 16.6017 14.1736 15.7434 14.1736Z"
      fill="#3463FC"
    />
    <path
      d="M15.5163 16.6375C15.4251 16.6375 15.3339 16.6017 15.2642 16.532L14.7063 15.9777C14.5668 15.8382 14.5668 15.6111 14.7063 15.4716C14.8458 15.3322 15.0729 15.3322 15.2123 15.4716L15.5163 15.7756L16.2637 15.0282C16.4032 14.8887 16.6303 14.8887 16.7698 15.0282C16.9092 15.1677 16.9092 15.3948 16.7698 15.5342L15.7684 16.5338C15.6987 16.6035 15.6075 16.6375 15.5163 16.6375Z"
      fill="#3463FC"
    />
  </svg>
);
export const TicketWorkerTimeIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    az
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    style={{ marginRight: '10px' }}
  >
    <path
      d="M10 18C5.58171 18 2 14.4178 2 9.99971C2 5.58159 5.58171 2 10 2C14.4183 2 18 5.58159 18 9.99971C18 14.4178 14.4183 18 10 18ZM10 3.14339C6.21314 3.14339 3.14286 6.21356 3.14286 10.0003C3.14286 13.787 6.21314 16.8572 10 16.8572C13.7869 16.8572 16.8571 13.787 16.8571 10.0003C16.8571 6.21356 13.7869 3.14339 10 3.14339ZM12.5714 10.5717H9.71429C9.55657 10.5717 9.42857 10.4437 9.42857 10.286V9.71458V9.71401V5.14332C9.42857 4.98561 9.55657 4.85761 9.71429 4.85761H10.2857C10.4434 4.85761 10.5714 4.98561 10.5714 5.14332V9.42831H12.5714C12.7291 9.42831 12.8571 9.5563 12.8571 9.71401V10.286C12.8571 10.4437 12.7291 10.5717 12.5714 10.5717Z"
      fill="#3463FC"
    />
  </svg>
);
