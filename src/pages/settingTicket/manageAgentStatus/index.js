import React, { useState, useEffect, useRef } from 'react';
import styles from './index.less';
import HOCAuth from '@/components/HOCAuth/index';
import { FormattedMessage, getIntl, useDispatch } from 'umi';
import { PlusOutlined } from '@ant-design/icons';
import {
  Button,
  Table,
  Switch,
  Popconfirm,
  Modal,
  Form,
  Input,
  Select,
  Spin,
  notification,
} from 'antd';
import NewEditorDisableIcon from '@/assets/new-editor-disable.png';
import NewEditorNormalIcon from '@/assets/new-editor-normal.png';
import NewDeleteIcon from '@/assets/new-delete-list-icon.png';
import NewDeleteNoIcon from '@/assets/external-delete-no.png';
import { BackIcon, Delete } from '../autoCloseSetting/icon';

const { TextArea } = Input;

const ManageAgentStatus = ({}) => {
  const dispatch = useDispatch();
  const formRef = useRef();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingBtn, setLoadingBtn] = useState(false);
  const [loadingSwitch, setLoadingSwitch] = useState(false);
  const [pageSize, setPageSize] = useState(10);
  const [pageNum, setPageNum] = useState(1);
  const [total, setTotal] = useState(0);
  const [agentStatusData, setAgentStatusData] = useState([]); //座席状态列表
  const [editorAgentStatusData, setEditorAgentStatusData] = useState({}); //修改座席状态数据
  const [editorAgentOpenData, setEditorAgentOpenData] = useState({}); //修改是否开启数据
  const [switchStatus, setSwitchStatus] = useState(''); //修改是否开启数据
  const typeList = [
    {
      value: '1',
      label: getIntl().formatMessage({
        id: 'manage.agent.status.type.list.route.able',
        defaultValue: '可路由',
      }),
    },
    {
      value: '0',
      label: getIntl().formatMessage({
        id: 'manage.agent.status.type.list.offline',
        defaultValue: '离线',
      }),
    },
  ];

  useEffect(() => {
    queryAgentStatus();
  }, [pageSize, pageNum]);
  // 切换是否开启开关
  const onChangeSwitch = (checked, record) => {
    setLoadingSwitch(true);
    setSwitchStatus(checked);
    setEditorAgentOpenData(record);
  };
  useEffect(() => {
    if (Object.keys(editorAgentOpenData).length > 0) {
      let params = {
        agentStatusId: editorAgentOpenData.agentStatusId,
        agentStatusName: editorAgentOpenData.agentStatusName,
        description: editorAgentOpenData.description,
        type: editorAgentOpenData.type,
        enableStatus: switchStatus ? 1 : 0,
      };
      addOrUpdateAgentStatus(params);
    }
  }, [editorAgentOpenData]);
  // 显示添加座席状态弹窗
  const showModal = () => {
    setIsModalOpen(true);
  };
  // 取消添加座席状态弹窗
  const handleCancel = () => {
    setIsModalOpen(false);
    setEditorAgentStatusData({});
    formRef.current?.resetFields();
  };
  // 保存添加座席状态
  const onFinish = values => {
    setLoadingBtn(true);
    let params;
    if (Object.keys(editorAgentStatusData).length > 0) {
      params = {
        agentStatusId: editorAgentStatusData.agentStatusId,
        enableStatus: editorAgentStatusData.enableStatus,
        agentStatusName: values.agentStatusName,
        description: values.description,
        type: values.type,
      };
    } else {
      params = {
        agentStatusName: values.agentStatusName,
        description: values.description,
        type: values.type,
      };
    }
    addOrUpdateAgentStatus(params);
  };
  // 保存修改坐席状态接口
  const addOrUpdateAgentStatus = params => {
    dispatch({
      type: 'manageAgentStatus/addOrUpdateAgentStatus',
      payload: params,
      callback: response => {
        setLoadingBtn(false);
        setLoadingSwitch(false);
        let { code, data, msg } = response;
        if (code === 200) {
          setIsModalOpen(false);
          setSwitchStatus('');
          formRef.current?.resetFields();
          if (
            Object.keys(editorAgentOpenData).length > 0 ||
            Object.keys(editorAgentStatusData).length > 0
          ) {
            setEditorAgentStatusData({});
            queryAgentStatus();
          } else {
            if (pageNum === 1) {
              queryAgentStatus();
            } else {
              setPageNum(1);
            }
          }
          notification.success({
            message: data,
          });
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };
  // 显示修改座席状态弹窗
  const showHandleEditor = record => {
    setIsModalOpen(true);
    setEditorAgentStatusData(record);
  };
  useEffect(() => {
    if (Object.keys(editorAgentStatusData).length > 0 && isModalOpen) {
      formRef.current?.setFieldsValue(editorAgentStatusData);
    }
  }, [editorAgentStatusData]);
  // 取消删除座席状态
  const handleCancelDelete = () => {};
  // 删除坐席状态
  const handleConfirmDelete = record => {
    setLoading(true);
    dispatch({
      type: 'manageAgentStatus/agentStatusDel',
      payload: record?.agentStatusId,
      callback: response => {
        setLoading(true);
        let { code, data, msg } = response;
        if (code === 200) {
          if (pageNum === 1) {
            queryAgentStatus();
          } else {
            setPageNum(1);
          }
          notification.success({
            message: data,
          });
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };

  // 查询坐席状态列表
  const queryAgentStatus = () => {
    let params = {
      pageSize: pageSize,
      pageNum: pageNum,
    };
    setLoading(true);
    dispatch({
      type: 'manageAgentStatus/queryAgentStatus',
      payload: params,
      callback: response => {
        setLoading(false);
        let { code, data, msg } = response;
        if (code === 200) {
          setTotal(data.total);
          setAgentStatusData(data.records);
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };

  const columns = [
    {
      title: getIntl().formatMessage({
        id: 'manage.agent.status.table.status.name',
        defaultValue: '状态名称',
      }),
      dataIndex: 'agentStatusName',
      key: 'agentStatusName',
      ellipsis: true,
      width: '20%',
    },
    {
      title: getIntl().formatMessage({
        id: 'manage.agent.status.table.describe',
        defaultValue: '描述',
      }),
      dataIndex: 'description',
      key: 'description',
      width: '30%',
      ellipsis: true,
    },
    {
      title: getIntl().formatMessage({
        id: 'manage.agent.status.table.type',
        defaultValue: '类型',
      }),
      dataIndex: 'type',
      key: 'type',
      ellipsis: true,
      width: '15%',
      render: (text, record) => {
        if (text === '1') {
          return (
            <FormattedMessage
              id="manage.agent.status.type.list.route.able"
              defaultMessage="可路由"
            />
          );
        } else {
          return (
            <FormattedMessage
              id="manage.agent.status.type.list.offline"
              defaultMessage="离线"
            />
          );
        }
      },
    },
    {
      title: getIntl().formatMessage({
        id: 'manage.agent.status.table.enabled',
        defaultValue: '是否开启',
      }),
      dataIndex: 'enableStatus',
      key: 'enableStatus',
      width: '15%',
      render: (text, record) => {
        return (
          <Switch
            disabled={+record.initStatus}
            loading={loadingSwitch}
            checked={+text ? true : false}
            size="small"
            onChange={checked => onChangeSwitch(checked, record)}
          />
        );
      },
    },
    {
      title: getIntl().formatMessage({
        id: 'manage.agent.status.table.operation',
        defaultValue: '操作',
      }),
      dataIndex: 'operation',
      key: 'operation',
      width: '20%',
      render: (text, record) => {
        return (
          <div>
            <div
              style={{
                cursor: +record.initStatus ? 'not-allowed' : 'pointer',
              }}
              className={styles.operationEditorContainer}
              onClick={
                +record.initStatus ? null : () => showHandleEditor(record)
              }
            >
              <img
                title={getIntl().formatMessage({
                  id: 'marketing.activities.editor',
                  defaultValue: '修改',
                })}
                src={
                  +record.initStatus
                    ? NewEditorDisableIcon
                    : NewEditorNormalIcon
                }
              />
              <span style={{ color: +record.initStatus ? '#999' : '#3463FC' }}>
                <FormattedMessage
                  id="marketing.activities.editor"
                  defaultMessage="修改"
                />
              </span>
            </div>
            <Popconfirm
              title={getIntl().formatMessage({
                id: 'manage.agent.status.confirm.delete.tips',
                defaultValue: '确认删除该坐席状态？',
              })}
              getPopupContainer={trigger => trigger.parentElement}
              onConfirm={() => handleConfirmDelete(record)}
              onCancel={handleCancelDelete}
              disabled={+record.enableStatus || +record.initStatus}
              okText={getIntl().formatMessage({
                id: 'work.order.management.table.robot.work.order.yes',
                defaultValue: '是',
              })}
              cancelText={getIntl().formatMessage({
                id: 'work.order.management.table.robot.work.order.no',
                defaultValue: '否',
              })}
            >
              <div
                className={styles.operationDeleteContainer}
                style={{
                  cursor:
                    +record.enableStatus || +record.initStatus
                      ? 'not-allowed'
                      : 'pointer',
                }}
              >
                <img
                  title={getIntl().formatMessage({
                    id: 'user.management.operation.remove',
                    defaultValue: '删除',
                  })}
                  src={
                    +record.enableStatus || +record.initStatus
                      ? NewDeleteNoIcon
                      : NewDeleteIcon
                  }
                />
                <span
                  style={{
                    color:
                      +record.enableStatus || +record.initStatus
                        ? '#999'
                        : '#F22417',
                  }}
                >
                  <FormattedMessage
                    id="user.management.operation.remove"
                    defaultMessage="删除"
                  />
                </span>
              </div>
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  return (
    <div className={styles.manageAgentStatus}>
      <div className={styles.workContentTop}>
        <div style={{ display: 'flex' }}>
          <span
            style={{ marginRight: 10, lineHeight: '35px', cursor: 'pointer' }}
            onClick={() => history.go(-1)}
          >
            {BackIcon()}
          </span>
          <FormattedMessage
            id="agentOperationSettings.config.title.3"
            defaultMessage="座席状态定义"
          />
        </div>

        <Button type={'primary'} icon={<PlusOutlined />} onClick={showModal}>
          <FormattedMessage
            id="manage.agent.status.add.btn"
            defaultMessage="添加座席状态"
          />
        </Button>
      </div>

      <Table
        dataSource={agentStatusData}
        columns={columns}
        pagination={{
          total: total,
          current: pageNum,
          pageSize: pageSize,
          showSizeChanger: true,
          pageSizeOptions: [10, 20, 50, 100],
          showTotal: total => (
            <FormattedMessage
              id="page.total.num"
              defaultMessage={`共 ${total} 条`}
              values={{ total }}
            />
          ),
          onChange: (pageNum, pageSize) => {
            setPageNum(pageNum);
            setPageSize(pageSize);
          },
        }}
        loading={loading}
      />

      <Modal
        title={getIntl().formatMessage({
          id: 'manage.agent.status.add.btn',
          defaultValue: '添加座席状态',
        })}
        open={isModalOpen}
        onCancel={handleCancel}
        className="AddAgentStatusModal"
        footer={null}
        mask={false}
      >
        <Form name="basic" onFinish={onFinish} autoComplete="off" ref={formRef}>
          <Form.Item
            label={getIntl().formatMessage({
              id: 'manage.agent.status.modal.status.name',
              defaultValue: '状态名称：',
            })}
            name="agentStatusName"
            rules={[
              {
                required: true,
                message: getIntl().formatMessage({
                  id: 'manage.agent.status.modal.status.name.tips',
                  defaultValue: '请输入状态名称',
                }),
              },
              {
                max: 80,
                message: getIntl().formatMessage({
                  id: 'manage.agent.status.modal.status.name.required.max',
                  defaultMessage: '状态名称最多输入80个字符',
                }),
              },
            ]}
          >
            <Input
              maxLength={81}
              placeholder={getIntl().formatMessage({
                id: 'manage.agent.status.modal.status.name.tips',
                defaultValue: '请输入状态名称',
              })}
            />
          </Form.Item>
          <Form.Item
            label={getIntl().formatMessage({
              id: 'manage.agent.status.modal.type',
              defaultValue: '类型：',
            })}
            name="type"
            rules={[
              {
                required: true,
                message: getIntl().formatMessage({
                  id: 'manage.agent.status.modal.type.tips',
                  defaultValue: '请选择类型',
                }),
              },
            ]}
          >
            <Select
              placeholder={getIntl().formatMessage({
                id: 'manage.agent.status.modal.type.tips',
                defaultValue: '请选择类型',
              })}
              options={typeList}
            />
          </Form.Item>

          <Form.Item
            label={getIntl().formatMessage({
              id: 'manage.agent.status.modal.describe',
              defaultValue: '描述：',
            })}
            name="description"
            rules={[
              {
                required: true,
                message: getIntl().formatMessage({
                  id: 'manage.agent.status.modal.describe.tips',
                  defaultValue: '请输入描述',
                }),
              },
              {
                max: 2000,
                message: getIntl().formatMessage({
                  id:
                    'manage.agent.status.modal.status.description.required.max',
                  defaultMessage: '描述最多输入2000个字符',
                }),
              },
            ]}
          >
            <TextArea
              placeholder={getIntl().formatMessage({
                id: 'manage.agent.status.modal.describe.tips',
                defaultValue: '请输入描述',
              })}
              maxLength={2001}
              autoSize={{
                minRows: 3,
                maxRows: 3,
              }}
            />
          </Form.Item>

          <Form.Item style={{ textAlign: 'center' }}>
            <Button onClick={handleCancel}>
              <FormattedMessage
                id="awsAccountSetting.cancel.btn"
                defaultMessage="取消"
              />
            </Button>
            <Button loading={loadingBtn} type="primary" htmlType="submit">
              <FormattedMessage
                id="awsAccountSetting.save.btn"
                defaultMessage="保存"
              />
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ManageAgentStatus;
