.manageAgentStatus {
  margin: 20px;
  border-radius: 4px;
  min-height: 88vh;
  box-shadow: 0px 2px 30px 0px rgba(0, 0, 0, 0.02);

  .workContentTop {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    font-size: 18px;
  }

  .operationEditorContainer {
    float: left;
    cursor: pointer;
    margin-right: 8px;

    img {
      width: 12px;
      margin-right: 4px;
    }

    span {
      color: #3463fc;
      text-align: center;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 183.333% */
    }
  }

  .operationDeleteContainer {
    float: left;
    cursor: pointer;

    img {
      width: 12px;
      margin-right: 4px;
      margin-top: -1px;
    }

    span {
      color: #f22417;
      text-align: center;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      /* 183.333% */
    }
  }

  :global {
    .ant-btn .anticon {
      margin-right: 3px;
    }

    .ant-btn.ant-btn-primary {
      margin-bottom: 20px;
    }

    .ant-switch-checked {
      background: #3463fc;
    }

    .ant-table-thead > tr > th {
      // background-color: #f3f7fe !important;
      background-color: #ffffff !important;
      font-weight: 700;
    }

    .ant-table-tbody {
      background-color: #fcfbff;
    }
  }
}
