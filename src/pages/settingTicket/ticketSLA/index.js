import React, { Component } from 'react';
import { connect, getIntl, FormattedMessage } from 'umi';
import {
  Input,
  Button,
  Table,
  Select,
  Form,
  Tabs,
  Col,
  Row,
  Spin,
  Popconfirm,
  Tooltip,
  Checkbox,
  Switch,
  TimePicker,
  DatePicker,
  Modal,
  InputNumber,
} from 'antd';
import styles from './index.less';
import { notification } from '@/utils/utils';
import { PlusOutlined } from '@ant-design/icons';
import { BackIcon, Delete } from '../autoCloseSetting/icon';

import TikTokIcon from '@/assets/new-tiktok-icon.svg';
import DiscordIcon from '@/assets/new-discord-icon.svg';
import PhoneIcon from '@/assets/phone.svg';
import EmailIcon from '@/assets/email.svg';
import WhatsAppIcon from '@/assets/whats-app.svg';
import FacebookIcon from '@/assets/facebook.svg';
import TwitterIcon from '@/assets/twitter.svg';
import AppVideoOutlinedIcon from '@/assets/AppVideoOutlined.svg';
import AppChatOutlinedIcon from '@/assets/AppChatOutlined.svg';
import WebVideoOutlinedIcon from '@/assets/WebVideoOutlined.svg';
import ChatIcon from '@/assets/chat-icon.jpg';
import HOCAuth from '@/components/HOCAuth/index';
import AwsChannelIcon from '@/assets/aws-channel-icon.svg';

import NewWebOnlineVoiceIcon from '@/assets/web-online-voice-icon.svg';
import NewAppOnlineVoiceIcon from '@/assets/app-online-voice-icon.svg';
import NewFaceBookIcon from '@/assets/new-facebook-icon.svg';
import NewInstagramIcon from '@/assets/ins.svg';
import NewLineIcon from '@/assets/new-line-icon.svg';
import NewTwitterIcon from '@/assets/new-twitter-icon.svg';
import NewTelegramIcon from '@/assets/new-telegram-icon.svg';
import NewWeComIcon from '@/assets/new-wecom-icon.svg';
import NewWeChatMiniProgramIcon from '@/assets/new-wechat-mini-program-icon.svg';
import NewWechatOfficialAccountIcon from '@/assets/new-wechat-official-account-icon.svg';
import NewShopifyIcon from '@/assets/new-shopify-icon.svg';
import NewGooglePlayIcon from '@/assets/google-play-icon.svg';
import NewWebOnlineVideoIcon from '@/assets/web-online-video-icon.svg';
import NewAppOnlineVideoIcon from '@/assets/app-online-video-icon.svg';

import NewWebChatIcon from '@/assets/new-web-chat-icon.svg';
import NewAppChatIcon from '@/assets/new-app-chat-icon.svg';
import NewPhoneIcon from '@/assets/new-phone-icon.svg';
import NewWhatsAppIcon from '@/assets/new-whatsapp-icon.svg';
import NewEmailIcon from '@/assets/new-email-icon.svg';
import NewAmazonMessageIcon from '@/assets/new-channel-aws-icon.svg';

import { DeleteIcon, EditorIcon } from './icon';
import LineIcon from '@/assets/line.svg';

const { RangePicker } = DatePicker;
class TicketSLA extends Component {
  childModalRef = React.createRef();
  formRef = React.createRef();
  slaRuleRef = React.createRef();
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      saveLoading: false,
      updateId: '',
      priorityList: [],
      priorityInfo: [],
      isRemoveList: [],
      openModal: false,
      modalTitle: 'add',
      showTab5Add: false,
      tabs5Loading: false,
      timeZoneList: [],

      weekList: [
        {
          title: getIntl().formatMessage({
            id: 'message.week.7',
          }),
          key: '7',
        },
        {
          title: getIntl().formatMessage({
            id: 'message.week.1',
          }),
          key: '1',
        },
        {
          title: getIntl().formatMessage({
            id: 'message.week.2',
          }),
          key: '2',
        },
        {
          title: getIntl().formatMessage({
            id: 'message.week.3',
          }),
          key: '3',
        },
        {
          title: getIntl().formatMessage({
            id: 'message.week.4',
          }),
          key: '4',
        },
        {
          title: getIntl().formatMessage({
            id: 'message.week.5',
          }),
          key: '5',
        },
        {
          title: getIntl().formatMessage({
            id: 'message.week.6',
          }),
          key: '6',
        },
      ],
      isModalAddRuleOpen: false,
      initSlaRule: [
        {
          priorityLevelId: '1005',
          responseTime: '',
          responseTimeUnit: 'M',
          nextResponseTime: '',
          nextResponseTimeUnit: 'M',
          resolveTime: '',
          resolveTimeUnit: 'D',
          priorityLevelName: getIntl().formatMessage({
            id: 'priority-p1',
            defaultValue: 'P1-服务崩溃',
          }),
        },
        {
          priorityLevelId: '1004',
          responseTime: '',
          responseTimeUnit: 'M',
          nextResponseTime: '',
          nextResponseTimeUnit: 'M',
          resolveTime: '',
          resolveTimeUnit: 'D',
          priorityLevelName: getIntl().formatMessage({
            id: 'priority-p2',
            defaultValue: 'P2-紧急处理',
          }),
        },
        {
          priorityLevelId: '1003',
          responseTime: '',
          responseTimeUnit: 'M',
          nextResponseTime: '',
          nextResponseTimeUnit: 'M',
          resolveTime: '',
          resolveTimeUnit: 'D',
          priorityLevelName: getIntl().formatMessage({
            id: 'priority-p3',
            defaultValue: 'P3-严重影响',
          }),
        },
        {
          priorityLevelId: '1002',
          responseTime: '',
          responseTimeUnit: 'M',
          nextResponseTime: '',
          nextResponseTimeUnit: 'M',
          resolveTime: '',
          resolveTimeUnit: 'D',
          priorityLevelName: getIntl().formatMessage({
            id: 'priority-p4',
            defaultValue: 'P4-中级影响',
          }),
        },
        {
          priorityLevelId: '1001',
          responseTime: '',
          responseTimeUnit: 'M',
          nextResponseTime: '',
          nextResponseTimeUnit: 'M',
          resolveTime: '',
          resolveTimeUnit: 'D',
          priorityLevelName: getIntl().formatMessage({
            id: 'priority-p5',
            defaultValue: 'P5-低级影响',
          }),
        },
      ],
      initSlaRuleInit: [
        {
          priorityLevelId: '1005',
          responseTime: '',
          responseTimeUnit: 'M',
          nextResponseTime: '',
          nextResponseTimeUnit: 'M',
          resolveTime: '',
          resolveTimeUnit: 'D',
          priorityLevelName: getIntl().formatMessage({
            id: 'priority-p1',
            defaultValue: 'P1-服务崩溃',
          }),
        },
        {
          priorityLevelId: '1004',
          responseTime: '',
          responseTimeUnit: 'M',
          nextResponseTime: '',
          nextResponseTimeUnit: 'M',
          resolveTime: '',
          resolveTimeUnit: 'D',
          priorityLevelName: getIntl().formatMessage({
            id: 'priority-p2',
            defaultValue: 'P2-紧急处理',
          }),
        },
        {
          priorityLevelId: '1003',
          responseTime: '',
          responseTimeUnit: 'M',
          nextResponseTime: '',
          nextResponseTimeUnit: 'M',
          resolveTime: '',
          resolveTimeUnit: 'D',
          priorityLevelName: getIntl().formatMessage({
            id: 'priority-p3',
            defaultValue: 'P3-严重影响',
          }),
        },
        {
          priorityLevelId: '1002',
          responseTime: '',
          responseTimeUnit: 'M',
          nextResponseTime: '',
          nextResponseTimeUnit: 'M',
          resolveTime: '',
          resolveTimeUnit: 'D',
          priorityLevelName: getIntl().formatMessage({
            id: 'priority-p4',
            defaultValue: 'P4-中级影响',
          }),
        },
        {
          priorityLevelId: '1001',
          responseTime: '',
          responseTimeUnit: 'M',
          nextResponseTime: '',
          nextResponseTimeUnit: 'M',
          resolveTime: '',
          resolveTimeUnit: 'D',
          priorityLevelName: getIntl().formatMessage({
            id: 'priority-p5',
            defaultValue: 'P5-低级影响',
          }),
        },
      ],
      channelTypeList: [],
      loadingModal: false,
      workRecordTypeList: [],
      editorStatus: false,
      editorSlaData: {},
      workRecordSlaId: '',
    };
  }
  componentDidMount() {
    //工单SLA查询
    this.queryWorkOrderSla();
    //工单等级
    this.queryWorkRecordLevel();
  }

  /**
   * 工单SLA查询
   */
  queryWorkOrderSla = () => {
    this.setState({ loading: true });
    this.props.dispatch({
      type: 'workOrderExtension/queryWorkOrderSla',
      callback: res => {
        if (res.code === 200) {
          this.setState({ priorityList: res.data });
          // let sortArr = this.state.priorityList.sort(
          //   (a, b) => a.priorityLevelId - b.priorityLevelId,
          // );
          // this.setState({ priorityList: sortArr });
        } else {
          notification.error({
            message: res.msg,
          });
        }
        this.setState({ loading: false });
      },
    });
  };
  /**
   * 工单等级
   */
  queryWorkRecordLevel = () => {
    this.setState({ loading: true });
    this.props.dispatch({
      type: 'workOrderExtension/queryWorkRecordLevel',
      callback: res => {
        if (res.code === 200) {
          this.setState({ priorityInfo: res.data });
        } else {
          notification.error({
            message: res.msg,
          });
        }
        this.setState({ loading: false });
      },
    });
  };

  // 是否删除SLA规则
  confirm = dataItem => {
    this.setState({ loading: true });
    let workRecordSlaId = dataItem.workRecordSlaId;
    this.props.dispatch({
      type: 'workOrderExtension/workOrderSlaRemove',
      payload: workRecordSlaId,
      callback: response => {
        this.setState({
          saveLoading: false,
        });
        if (response.code === 200) {
          this.setState({
            loading: false,
          });
          this.queryWorkOrderSla();
          notification.success({
            message: response.msg,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 取消删除SLA规则
  cancel = e => {
    console.log(e);
  };
  // 显示新增SLA规则
  handleShowAddSlaRule = () => {
    this.setState(
      {
        isModalAddRuleOpen: true,
      },
      () => {
        this.queryChannelTypeList();
        this.queryWorkRecordType();
        const formData = {
          initSlaRule: [],
        };
        this.state.initSlaRule.forEach(item => {
          formData.initSlaRule[item.priorityLevelId] = {
            responseTime: item.responseTime,
            responseTimeUnit: item.responseTimeUnit,
            nextResponseTime: item.nextResponseTime,
            nextResponseTimeUnit: item.nextResponseTimeUnit,
            resolveTime: item.resolveTime,
            resolveTimeUnit: item.resolveTimeUnit,
          };
        });
        this.slaRuleRef.current?.setFieldsValue(formData);
      },
    );
  };
  // 取消新增SLA规则
  handleCancelAddSlaRule = () => {
    this.setState(
      {
        isModalAddRuleOpen: false,
        editorStatus: false,
        editorSlaData: [],
        workRecordSlaId: '',
        initSlaRule: this.state.initSlaRuleInit,
      },
      () => {
        this.slaRuleRef.current?.resetFields();
      },
    );
  };
  // 修改SLA规则
  handleEditor = itemData => {
    this.queryChannelTypeList();
    this.queryWorkRecordType();
    this.setState(
      {
        editorSlaData: itemData,
        isModalAddRuleOpen: true,
        editorStatus: true,
      },
      () => {
        let channelCode, workRecordTypeValue;
        if (!+itemData.initStatus) {
          channelCode = itemData.channelCodes.map(item => item.code);
          workRecordTypeValue = itemData.workRecordTypeValues.map(
            item => item.workRecordTypeValue,
          );
        }
        const formData = {
          initSlaRule: [],
          workRecordSlaName: itemData.workRecordSlaName,
          channelCodes: channelCode ? channelCode : [],
          workRecordTypeValues: workRecordTypeValue ? workRecordTypeValue : [],
        };
        itemData.slaDefList.forEach(item => {
          formData.initSlaRule[item.priorityLevelId] = {
            responseTime: item.responseTime,
            responseTimeUnit: item.responseTimeUnit,
            nextResponseTime: item.nextResponseTime,
            nextResponseTimeUnit: item.nextResponseTimeUnit,
            resolveTime: item.resolveTime,
            resolveTimeUnit: item.resolveTimeUnit,
          };
        });
        this.setState(
          {
            initSlaRule: itemData.slaDefList,
            workRecordSlaId: itemData.workRecordSlaId,
          },
          () => {
            this.slaRuleRef.current?.setFieldsValue(formData);
          },
        );
      },
    );
  };
  // 保存新增SLA规则
  onSaveAddSlaRule = values => {
    this.setState({
      saveLoading: true,
    });
    const { initSlaRule } = values;

    const slaDefList = Object.keys(initSlaRule).map(priorityLevelId => {
      const rule = initSlaRule[priorityLevelId];
      return {
        priorityLevelId,
        responseTime: rule.responseTime,
        responseTimeUnit: rule.responseTimeUnit,
        nextResponseTime: rule.nextResponseTime,
        nextResponseTimeUnit: rule.nextResponseTimeUnit,
        resolveTime: rule.resolveTime,
        resolveTimeUnit: rule.resolveTimeUnit,
      };
    });
    let params = {
      workRecordSlaId: this.state.workRecordSlaId
        ? this.state.workRecordSlaId
        : '',
      workRecordSlaName: values.workRecordSlaName,
      channelCodes: values.channelCodes,
      workRecordTypeValues: values.workRecordTypeValues,
      slaDefList: slaDefList,
    };
    this.props.dispatch({
      type: 'workOrderExtension/addOrUpdateWorkOrderSla',
      payload: params,
      callback: response => {
        this.setState({
          saveLoading: false,
        });
        if (response.code === 200) {
          this.setState({
            isModalAddRuleOpen: false,
          });
          this.slaRuleRef.current?.resetFields();
          this.queryWorkOrderSla();
          notification.success({
            message: response.msg,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });

    // addOrUpdateWorkOrderSla
  };

  // 查询渠道类型列表
  queryChannelTypeList = () => {
    this.setState({
      loadingModal: true,
    });
    this.props.dispatch({
      type: 'newChannelConfiguration/queryNewChannelTypeList',
      callback: response => {
        this.setState({
          loadingModal: false,
        });
        if (response.code === 200) {
          this.setState({
            channelTypeList: response.data,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询工单类型列表
  queryWorkRecordType = () => {
    this.setState({
      loadingModal: true,
    });
    this.props.dispatch({
      type: 'workOrderCenter/queryWorkRecordType',
      callback: response => {
        this.setState({
          loadingModal: false,
        });
        if (response.code === 200) {
          let workRecordTypeList = response.data;
          this.setState({
            workRecordTypeList: workRecordTypeList,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  render() {
    const {
      loading,
      priorityList,
      priorityInfo,
      isModalAddRuleOpen,
      initSlaRule,
      loadingModal,
      channelTypeList,
      workRecordTypeList,
      editorSlaData,
    } = this.state;
    const result = priorityInfo.reduce((acc, item) => {
      acc[item.priorityLevelId] = item.priorityLevelName;
      return acc;
    }, {});

    // 遍历数据并为每个对象增加 priorityLevelName
    const updatedData = priorityList.map(item => ({
      ...item,
      slaDefList: item.slaDefList.map(sla => ({
        ...sla,
        priorityLevelName: result[sla.priorityLevelId] || '--',
      })),
    }));

    // 渠道数字对应图标-----channelIds 对应的图标映射
    const channelIconMap = {
      1: NewEmailIcon, // 邮件
      3: NewFaceBookIcon, // facebook
      4: NewWhatsAppIcon, // WhatsApp
      7: NewPhoneIcon, // 电话
      8: NewWebChatIcon, // WEB聊天
      9: NewAppChatIcon, // APP聊天
      10: NewWebOnlineVideoIcon, // WEB在线视频
      11: NewAppOnlineVideoIcon, // APP在线视频
      12: NewAmazonMessageIcon, // 亚马逊站内信
      13: NewInstagramIcon, // Instagram
      14: NewLineIcon, // Line
      15: NewWeComIcon, // 微信客服
      16: NewWechatOfficialAccountIcon, // 微信公众号
      17: NewWebOnlineVoiceIcon, // WEB语音
      18: NewAppOnlineVoiceIcon, // APP语音
      19: NewTwitterIcon, // Twitter
      20: NewTelegramIcon, // Telegram
      21: NewWeChatMiniProgramIcon, // 微信小程序
      22: NewShopifyIcon, // Shopify
      23: NewGooglePlayIcon, // GooglePlay
      24: TikTokIcon,
      25: DiscordIcon,
    };
    return (
      <div className={styles.workContent}>
        <div className={styles.workContentTop}>
          <div style={{ display: 'flex' }}>
            <span
              style={{ marginRight: 10, lineHeight: '35px', cursor: 'pointer' }}
              onClick={() => history.go(-1)}
            >
              {BackIcon()}
            </span>

            <FormattedMessage id="agentOperationSettings.config.title.1" />
          </div>
          <Button
            onClick={this.handleShowAddSlaRule}
            icon={<PlusOutlined />}
            type="primary"
          >
            <span>
              <FormattedMessage
                id="channel.config.tab.1.add.rule.btn"
                defaultMessage="新增SLA规则"
              />
            </span>
          </Button>
        </div>

        <div className={styles.pageContent}>
          <Spin spinning={loading}>
            <div>
              <div className={styles.pageContentTab1} style={{ marginTop: 20 }}>
                <div className={styles.slaRuleContainer}>
                  {updatedData?.map(dataItem => {
                    if (dataItem.initStatus === '1') {
                      return (
                        <div className={styles.ruleItemContainer}>
                          <div className={styles.ruleHeaderContainer}>
                            <Tooltip
                              placement="right"
                              title={dataItem.workRecordSlaName}
                            >
                              <p className={styles.ruleName}>
                                {dataItem.workRecordSlaName
                                  ? dataItem.workRecordSlaName
                                  : '--'}
                              </p>
                            </Tooltip>
                            <Button
                              onClick={() => this.handleEditor(dataItem)}
                              icon={EditorIcon()}
                            >
                              <FormattedMessage
                                id="marketing.activities.editor"
                                defaultMessage="修改"
                              />
                            </Button>
                          </div>
                          <div className={styles.tableTitle}>
                            <Row justify="space-around">
                              <Col
                                title={getIntl().formatMessage({
                                  id: 'channel.config.tab.1.col.1',
                                })}
                                span={7}
                                style={{ textAlign: 'left' }}
                              >
                                <FormattedMessage id="channel.config.tab.1.col.1" />
                              </Col>
                              <Col
                                title={getIntl().formatMessage({
                                  id: 'channel.config.tab.1.col.2',
                                })}
                                span={6}
                                style={{ textAlign: 'center' }}
                              >
                                <FormattedMessage id="channel.config.tab.1.col.2" />
                              </Col>
                              <Col
                                title={getIntl().formatMessage({
                                  id: 'channel.config.tab.1.col.4',
                                })}
                                span={6}
                                style={{ textAlign: 'center' }}
                              >
                                <FormattedMessage id="channel.config.tab.1.col.4" />
                              </Col>
                              <Col
                                title={getIntl().formatMessage({
                                  id: 'channel.config.tab.1.col.3',
                                })}
                                span={5}
                                style={{ textAlign: 'center' }}
                              >
                                <FormattedMessage id="channel.config.tab.1.col.3" />
                              </Col>
                            </Row>
                          </div>
                          {dataItem?.slaDefList?.map((item, index) => {
                            return (
                              <Row
                                key={index}
                                justify="space-around"
                                style={{ marginTop: 10, lineHeight: '30px' }}
                              >
                                <Col span={7} style={{ textAlign: 'left' }}>
                                  {item.priorityLevelName}
                                </Col>
                                <Col span={6} style={{ textAlign: 'center' }}>
                                  {item.responseTime}{' '}
                                  {item.responseTimeUnit === 'M'
                                    ? getIntl().formatMessage({
                                        id: 'channel.time.minute',
                                      })
                                    : item.responseTimeUnit === 'H'
                                    ? getIntl().formatMessage({
                                        id: 'channel.time.hour',
                                      })
                                    : getIntl().formatMessage({
                                        id: 'channel.time.day',
                                      })}
                                </Col>
                                <Col span={6} style={{ textAlign: 'center' }}>
                                  {item.nextResponseTime}{' '}
                                  {item.nextResponseTimeUnit === 'M'
                                    ? getIntl().formatMessage({
                                        id: 'channel.time.minute',
                                      })
                                    : item.nextResponseTimeUnit === 'H'
                                    ? getIntl().formatMessage({
                                        id: 'channel.time.hour',
                                      })
                                    : getIntl().formatMessage({
                                        id: 'channel.time.day',
                                      })}
                                </Col>
                                <Col span={5} style={{ textAlign: 'center' }}>
                                  {item.resolveTime}{' '}
                                  {item.resolveTimeUnit === 'M'
                                    ? getIntl().formatMessage({
                                        id: 'channel.time.minute',
                                      })
                                    : item.resolveTimeUnit === 'H'
                                    ? getIntl().formatMessage({
                                        id: 'channel.time.hour',
                                      })
                                    : getIntl().formatMessage({
                                        id: 'channel.time.day',
                                      })}
                                </Col>
                              </Row>
                            );
                          })}
                        </div>
                      );
                    } else {
                      return (
                        <div className={styles.ruleItemContainer}>
                          <div className={styles.ruleHeaderContainer}>
                            <Tooltip
                              placement="right"
                              title={dataItem.workRecordSlaName}
                            >
                              <p className={styles.ruleName}>
                                {dataItem.workRecordSlaName
                                  ? dataItem.workRecordSlaName
                                  : '--'}
                              </p>
                            </Tooltip>
                            <Popconfirm
                              placement="bottom"
                              title={getIntl().formatMessage({
                                id: 'channel.config.tab.1.delete.rule.tips',
                                defaultValue: '您是否要删除这个规则？',
                              })}
                              onConfirm={() => this.confirm(dataItem)}
                              onCancel={this.cancel}
                              okText={getIntl().formatMessage({
                                id:
                                  'work.order.management.table.robot.work.order.yes',
                                defaultValue: '是',
                              })}
                              cancelText={getIntl().formatMessage({
                                id:
                                  'work.order.management.table.robot.work.order.no',
                                defaultValue: '否',
                              })}
                            >
                              <Button danger icon={DeleteIcon()}>
                                <FormattedMessage
                                  id="awsAccountSetting.delete.btn"
                                  defaultMessage="删除"
                                />
                              </Button>
                            </Popconfirm>
                            <Button
                              onClick={() => this.handleEditor(dataItem)}
                              icon={EditorIcon()}
                            >
                              <FormattedMessage
                                id="marketing.activities.editor"
                                defaultMessage="修改"
                              />
                            </Button>
                          </div>
                          <div className={styles.channelTypeContainer}>
                            <span>
                              <FormattedMessage
                                id="statistics.data.details.channel.type"
                                defaultMessage="渠道类型："
                              />
                            </span>
                            {dataItem?.channelCodes?.map(items => {
                              const icon = channelIconMap[items.code];
                              return icon ? (
                                <img key={items.code} src={icon} />
                              ) : null;
                            })}
                          </div>
                          <div className={styles.channelTypeContainer}>
                            <span>
                              <FormattedMessage
                                id="create.work.order.work.order.type"
                                defaultMessage="工单类型："
                              />
                            </span>
                            <div className={styles.detailTypeContainer}>
                              {dataItem?.workRecordTypeValues?.map(items => {
                                return (
                                  <div className={styles.detailText}>
                                    {items.workRecordTypeName
                                      ? items.workRecordTypeName
                                      : '--'}
                                  </div>
                                );
                              })}
                              {dataItem?.workRecordTypeValues.length === 0 && (
                                <span>--</span>
                              )}
                            </div>
                          </div>
                          <div className={styles.tableTitle}>
                            <Row justify="space-around">
                              <Col
                                title={getIntl().formatMessage({
                                  id: 'channel.config.tab.1.col.1',
                                })}
                                span={7}
                                style={{ textAlign: 'left' }}
                              >
                                <FormattedMessage id="channel.config.tab.1.col.1" />
                              </Col>
                              <Col
                                title={getIntl().formatMessage({
                                  id: 'channel.config.tab.1.col.2',
                                })}
                                span={6}
                                style={{ textAlign: 'center' }}
                              >
                                <FormattedMessage id="channel.config.tab.1.col.2" />
                              </Col>
                              <Col
                                title={getIntl().formatMessage({
                                  id: 'channel.config.tab.1.col.4',
                                })}
                                span={6}
                                style={{ textAlign: 'center' }}
                              >
                                <FormattedMessage id="channel.config.tab.1.col.4" />
                              </Col>
                              <Col
                                title={getIntl().formatMessage({
                                  id: 'channel.config.tab.1.col.3',
                                })}
                                span={5}
                                style={{ textAlign: 'center' }}
                              >
                                <FormattedMessage id="channel.config.tab.1.col.3" />
                              </Col>
                            </Row>
                          </div>
                          {dataItem?.slaDefList?.map((item, index) => {
                            return (
                              <Row
                                key={index}
                                justify="space-around"
                                style={{ marginTop: 10, lineHeight: '30px' }}
                              >
                                <Col span={7} title={item.priorityLevelName}>
                                  {item.priorityLevelName}
                                </Col>
                                <Col span={6} style={{ textAlign: 'center' }}>
                                  {item.responseTime}{' '}
                                  {item.responseTimeUnit === 'M'
                                    ? getIntl().formatMessage({
                                        id: 'channel.time.minute',
                                      })
                                    : item.responseTimeUnit === 'H'
                                    ? getIntl().formatMessage({
                                        id: 'channel.time.hour',
                                      })
                                    : getIntl().formatMessage({
                                        id: 'channel.time.day',
                                      })}
                                </Col>
                                <Col span={6} style={{ textAlign: 'center' }}>
                                  {item.nextResponseTime}{' '}
                                  {item.nextResponseTimeUnit === 'M'
                                    ? getIntl().formatMessage({
                                        id: 'channel.time.minute',
                                      })
                                    : item.nextResponseTimeUnit === 'H'
                                    ? getIntl().formatMessage({
                                        id: 'channel.time.hour',
                                      })
                                    : getIntl().formatMessage({
                                        id: 'channel.time.day',
                                      })}
                                </Col>
                                <Col span={5} style={{ textAlign: 'center' }}>
                                  {item.resolveTime}{' '}
                                  {item.resolveTimeUnit === 'M'
                                    ? getIntl().formatMessage({
                                        id: 'channel.time.minute',
                                      })
                                    : item.resolveTimeUnit === 'H'
                                    ? getIntl().formatMessage({
                                        id: 'channel.time.hour',
                                      })
                                    : getIntl().formatMessage({
                                        id: 'channel.time.day',
                                      })}
                                </Col>
                              </Row>
                            );
                          })}
                        </div>
                      );
                    }
                  })}
                </div>
              </div>
            </div>
          </Spin>
        </div>

        {/*新增SLA规则弹窗*/}
        <Modal
          title={getIntl().formatMessage({
            id: 'channel.config.tab.1.add.rule.btn',
            defaultValue: '新增SLA规则',
          })}
          open={isModalAddRuleOpen}
          onCancel={this.handleCancelAddSlaRule}
          className="AddSlaRuleModal"
          footer={null}
          mask={false}
          maskClosable={false}
        >
          <Spin spinning={loadingModal}>
            <Form
              name="slaRule"
              onFinish={this.onSaveAddSlaRule}
              autoComplete="off"
              ref={this.slaRuleRef}
            >
              <Row justify="space-around">
                <Col span={24}>
                  <Form.Item
                    label={getIntl().formatMessage({
                      id: 'channel.config.tab.1.add.rule.name',
                      defaultValue: '规则名称：',
                    })}
                    name="workRecordSlaName"
                    rules={[
                      {
                        required: true,
                        message: getIntl().formatMessage({
                          id: 'channel.config.tab.1.add.rule.name.placeholder',
                          defaultValue: '请输入规则名称',
                        }),
                      },
                      {
                        max: 80,
                        message: getIntl().formatMessage({
                          id: 'channel.config.tab.1.add.rule.name.required.max',
                          defaultMessage: '规则名称最多输入80个字符',
                        }),
                      },
                    ]}
                  >
                    <Input
                      disabled={+editorSlaData.initStatus}
                      maxLength={81}
                      placeholder={getIntl().formatMessage({
                        id: 'channel.config.tab.1.add.rule.name.placeholder',
                        defaultValue: '请输入规则名称',
                      })}
                    />
                  </Form.Item>
                </Col>
              </Row>
              {!+editorSlaData.initStatus && (
                <Row justify="space-around">
                  <Col span={24}>
                    <Form.Item
                      label={getIntl().formatMessage({
                        id: 'statistics.data.details.channel.type',
                        defaultValue: '渠道类型：',
                      })}
                      name="channelCodes"
                      rules={[
                        {
                          required: true,
                          message: getIntl().formatMessage({
                            id: 'work.record.channel.type',
                            defaultValue: '请选择渠道类型',
                          }),
                        },
                      ]}
                    >
                      <Select
                        mode="multiple"
                        popupClassName="selectFilterContent"
                        allowClear
                        placeholder={getIntl().formatMessage({
                          id:
                            'email.channel.configuration.channel.type.placeholder',
                          defaultValue: '请选择渠道类型',
                        })}
                      >
                        {channelTypeList &&
                          channelTypeList.map(items => {
                            if (items.code == '1') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={EmailIcon} />
                                  {items.name}
                                </Option>
                              );
                            } else if (items.code == '3') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={FacebookIcon} />
                                  {items.name}
                                </Option>
                              );
                            } else if (items.code == '4') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={WhatsAppIcon} />
                                  {items.name}
                                </Option>
                              );
                            } else if (items.code == '5') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={TwitterIcon} />
                                  {items.name}
                                </Option>
                              );
                            } else if (items.code == '6') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={LineIcon} /> {items.name}
                                </Option>
                              );
                            } else if (items.code == '7') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={PhoneIcon} /> {items.name}
                                </Option>
                              );
                            } else if (items.code == '8') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={ChatIcon} />
                                  {items.name}
                                </Option>
                              );
                            } else if (items.code == '9') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={AppChatOutlinedIcon} />
                                  {items.name}
                                </Option>
                              );
                            } else if (items.code == '10') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={WebVideoOutlinedIcon} />
                                  {items.name}
                                </Option>
                              );
                            } else if (items.code == '11') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={AppVideoOutlinedIcon} />
                                  {items.name}
                                </Option>
                              );
                            } else if (items.code == '12') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={AwsChannelIcon} />
                                  {items.name}
                                </Option>
                              );
                            } else if (items.code == '13') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={NewInstagramIcon} />
                                  {items.name}
                                </Option>
                              );
                            } else if (items.code == '14') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={NewLineIcon} />
                                  {items.name}
                                </Option>
                              );
                            } else if (items.code == '15') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={NewWeComIcon} />
                                  {items.name}
                                </Option>
                              );
                            } else if (items.code == '16') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={NewWechatOfficialAccountIcon} />
                                  {items.name}
                                </Option>
                              );
                            } else if (items.code == '17') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={NewWebOnlineVoiceIcon} />
                                  {items.name}
                                </Option>
                              );
                            } else if (items.code == '18') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={NewAppOnlineVoiceIcon} />
                                  {items.name}
                                </Option>
                              );
                            } else if (items.code == '19') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={NewTwitterIcon} />
                                  {items.name}
                                </Option>
                              );
                            } else if (items.code == '20') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={NewTelegramIcon} />
                                  {items.name}
                                </Option>
                              );
                            } else if (items.code == '21') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={NewWeChatMiniProgramIcon} />
                                  {items.name}
                                </Option>
                              );
                            } else if (items.code == '22') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={NewShopifyIcon} />
                                  {items.name}
                                </Option>
                              );
                            } else if (items.code == '23') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={NewGooglePlayIcon} />
                                  {items.name}
                                </Option>
                              );
                            } else if (items.code == '23') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={NewGooglePlayIcon} />
                                  {items.name}
                                </Option>
                              );
                            } else if (items.code == '24') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={TikTokIcon} />
                                  {items.name}
                                </Option>
                              );
                            } else if (items.code == '25') {
                              return (
                                <Option value={items.code} key={items.code}>
                                  <img src={DiscordIcon} />
                                  {items.name}
                                </Option>
                              );
                            } else {
                              return (
                                <Option value={items.code} key={items.code}>
                                  {items.name}
                                </Option>
                              );
                            }
                          })}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              )}
              {!+editorSlaData.initStatus && (
                <Row justify="space-around">
                  <Col span={24}>
                    <Form.Item
                      label={getIntl().formatMessage({
                        id: 'statistics.work.report.label.2',
                        defaultValue: '工单类型：',
                      })}
                      name="workRecordTypeValues"
                      rules={[
                        {
                          required: true,
                          message: getIntl().formatMessage({
                            id: 'statistics.work.report.label.2.placeholder',
                            defaultValue: '请选择工单类型',
                          }),
                        },
                      ]}
                    >
                      <Select
                        mode="multiple"
                        allowClear
                        placeholder={getIntl().formatMessage({
                          id: 'create.work.order.work.order.type.required',
                          defaultValue: '请选择工单类型',
                        })}
                        options={workRecordTypeList}
                        fieldNames={{
                          label: 'workRecordTypeName',
                          value: 'workRecordTypeId',
                          key: 'workRecordTypeId',
                        }}
                        filterOption={(inputValue, option) =>
                          option.workRecordTypeName
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) >= 0
                        }
                      />
                    </Form.Item>
                  </Col>
                </Row>
              )}

              <div className="secondTitle">
                <Row justify="space-around">
                  <Col
                    title={getIntl().formatMessage({
                      id: 'channel.config.tab.1.col.1',
                    })}
                    span={5}
                    style={{ textAlign: 'left' }}
                  >
                    <FormattedMessage id="channel.config.tab.1.col.1" />
                  </Col>
                  <Col
                    title={getIntl().formatMessage({
                      id: 'channel.config.tab.1.col.2',
                    })}
                    span={6}
                    style={{ textAlign: 'center' }}
                  >
                    <FormattedMessage id="channel.config.tab.1.col.2" />
                  </Col>
                  <Col
                    title={getIntl().formatMessage({
                      id: 'channel.config.tab.1.col.4',
                    })}
                    span={6}
                    style={{ textAlign: 'center' }}
                  >
                    <FormattedMessage id="channel.config.tab.1.col.4" />
                  </Col>
                  <Col
                    title={getIntl().formatMessage({
                      id: 'channel.config.tab.1.col.3',
                    })}
                    span={6}
                    style={{ textAlign: 'center' }}
                  >
                    <FormattedMessage id="channel.config.tab.1.col.3" />
                  </Col>
                </Row>
              </div>

              {initSlaRule?.map((item, index) => {
                return (
                  <Row
                    key={index}
                    justify="space-around"
                    style={{ marginTop: 10, lineHeight: '30px' }}
                  >
                    <Col
                      title={item.priorityLevelName}
                      span={5}
                      style={{ textAlign: 'left' }}
                    >
                      {item.priorityLevelName}
                    </Col>
                    <Col span={6} style={{ textAlign: 'center' }}>
                      <Row justify="space-around">
                        <Col span={10}>
                          <Form.Item
                            label={null}
                            name={[
                              'initSlaRule',
                              item.priorityLevelId,
                              'responseTime',
                            ]}
                            rules={[
                              {
                                required: true,
                                message: getIntl().formatMessage({
                                  id: 'channel.please.input',
                                }),
                              },
                              {
                                pattern: /^(?:[1-9]\d{0,7}|0)$/,
                                message: (
                                  <FormattedMessage
                                    id="channel.config.tab.1.add.rule.num.required.max"
                                    defaultValue="数字大小不超过100000000"
                                  />
                                ),
                              },
                            ]}
                          >
                            <InputNumber
                              min={0}
                              max={100000001}
                              placeholder={getIntl().formatMessage({
                                id: 'channel.please.input',
                              })}
                              value={item.responseTime}
                            />
                          </Form.Item>
                        </Col>
                        <Col span={10}>
                          <Form.Item
                            label={null}
                            name={[
                              'initSlaRule',
                              item.priorityLevelId,
                              'responseTimeUnit',
                            ]}
                            rules={[
                              {
                                required: true,
                                message: getIntl().formatMessage({
                                  id: 'input.select',
                                  defaultValue: '请选择',
                                }),
                              },
                            ]}
                          >
                            <Select
                              allowClear
                              placeholder={getIntl().formatMessage({
                                id: 'input.select',
                                defaultValue: '请选择',
                              })}
                              value={item.responseTimeUnit}
                              options={[
                                {
                                  value: 'M',
                                  label: getIntl().formatMessage({
                                    id: 'channel.time.minute',
                                  }),
                                },
                                {
                                  value: 'H',
                                  label: getIntl().formatMessage({
                                    id: 'channel.time.hour',
                                  }),
                                },
                                {
                                  value: 'D',
                                  label: getIntl().formatMessage({
                                    id: 'channel.time.day',
                                  }),
                                },
                              ]}
                            />
                          </Form.Item>
                        </Col>
                      </Row>
                    </Col>
                    <Col span={6} style={{ textAlign: 'center' }}>
                      <Row justify="space-around">
                        <Col span={10}>
                          <Form.Item
                            label={null}
                            name={[
                              'initSlaRule',
                              item.priorityLevelId,
                              'nextResponseTime',
                            ]}
                            rules={[
                              {
                                required: true,
                                message: getIntl().formatMessage({
                                  id: 'channel.please.input',
                                }),
                              },
                              {
                                pattern: /^(?:[1-9]\d{0,7}|0)$/,
                                message: (
                                  <FormattedMessage
                                    id="channel.config.tab.1.add.rule.num.required.max"
                                    defaultValue="数字大小不超过100000000"
                                  />
                                ),
                              },
                            ]}
                          >
                            <InputNumber
                              min={0}
                              max={100000001}
                              placeholder={getIntl().formatMessage({
                                id: 'channel.please.input',
                              })}
                              value={item.resolveTime}
                            />
                          </Form.Item>
                        </Col>
                        <Col span={10}>
                          <Form.Item
                            label={null}
                            name={[
                              'initSlaRule',
                              item.priorityLevelId,
                              'nextResponseTimeUnit',
                            ]}
                            rules={[
                              {
                                required: true,
                                message: getIntl().formatMessage({
                                  id: 'input.select',
                                  defaultValue: '请选择',
                                }),
                              },
                            ]}
                          >
                            <Select
                              allowClear
                              placeholder={getIntl().formatMessage({
                                id: 'input.select',
                                defaultValue: '请选择',
                              })}
                              value={item.resolveTimeUnit}
                              options={[
                                {
                                  value: 'M',
                                  label: getIntl().formatMessage({
                                    id: 'channel.time.minute',
                                  }),
                                },
                                {
                                  value: 'H',
                                  label: getIntl().formatMessage({
                                    id: 'channel.time.hour',
                                  }),
                                },
                                {
                                  value: 'D',
                                  label: getIntl().formatMessage({
                                    id: 'channel.time.day',
                                  }),
                                },
                              ]}
                            />
                          </Form.Item>
                        </Col>
                      </Row>
                    </Col>
                    <Col span={6} style={{ textAlign: 'center' }}>
                      <Row justify="space-around">
                        <Col span={10}>
                          <Form.Item
                            label={null}
                            name={[
                              'initSlaRule',
                              item.priorityLevelId,
                              'resolveTime',
                            ]}
                            rules={[
                              {
                                required: true,
                                message: getIntl().formatMessage({
                                  id: 'channel.please.input',
                                }),
                              },
                              {
                                pattern: /^(?:[1-9]\d{0,7}|0)$/,
                                message: (
                                  <FormattedMessage
                                    id="channel.config.tab.1.add.rule.num.required.max"
                                    defaultValue="数字大小不超过100000000"
                                  />
                                ),
                              },
                            ]}
                          >
                            <InputNumber
                              min={0}
                              max={100000001}
                              placeholder={getIntl().formatMessage({
                                id: 'channel.please.input',
                              })}
                              value={item.resolveTime}
                            />
                          </Form.Item>
                        </Col>
                        <Col span={10}>
                          <Form.Item
                            label={null}
                            name={[
                              'initSlaRule',
                              item.priorityLevelId,
                              'resolveTimeUnit',
                            ]}
                            rules={[
                              {
                                required: true,
                                message: getIntl().formatMessage({
                                  id: 'input.select',
                                  defaultValue: '请选择',
                                }),
                              },
                            ]}
                          >
                            <Select
                              allowClear
                              placeholder={getIntl().formatMessage({
                                id: 'input.select',
                                defaultValue: '请选择',
                              })}
                              value={item.resolveTimeUnit}
                              options={[
                                {
                                  value: 'M',
                                  label: getIntl().formatMessage({
                                    id: 'channel.time.minute',
                                  }),
                                },
                                {
                                  value: 'H',
                                  label: getIntl().formatMessage({
                                    id: 'channel.time.hour',
                                  }),
                                },
                                {
                                  value: 'D',
                                  label: getIntl().formatMessage({
                                    id: 'channel.time.day',
                                  }),
                                },
                              ]}
                            />
                          </Form.Item>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                );
              })}

              <Form.Item style={{ textAlign: 'center' }}>
                <HOCAuth authKey={'ticket_sla_settings'}>
                  {authAccess => (
                    <Row justify="center">
                      <Button
                        onClick={this.handleCancelAddSlaRule}
                        style={{ marginRight: 20 }}
                      >
                        <FormattedMessage
                          id="work.record.button.cancel"
                          defaultValue="取消"
                        />
                      </Button>
                      <Button
                        type="primary"
                        loading={this.state.saveLoading}
                        disabled={authAccess}
                        htmlType="submit"
                      >
                        <FormattedMessage
                          id="customerInformation.add.basicInformation.button.save"
                          defaultValue="保存"
                        />
                      </Button>
                    </Row>
                  )}
                </HOCAuth>
              </Form.Item>
            </Form>
          </Spin>
        </Modal>
      </div>
    );
  }
}

const mapStateToProps = ({
  workOrderExtension,
  customerExt,
  layouts,
  newChannelConfiguration,
  workOrderCenter,
}) => {
  return {
    ...workOrderExtension,
    ...customerExt,
    authAccess: layouts.auth,
    ...newChannelConfiguration,
    ...workOrderCenter,
  };
};
export default connect(mapStateToProps)(TicketSLA);
