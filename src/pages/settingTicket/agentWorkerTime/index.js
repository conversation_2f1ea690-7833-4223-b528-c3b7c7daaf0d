import React, { Component } from 'react';
import { connect, getIntl, FormattedMessage } from 'umi';
import {
  Input,
  Button,
  Table,
  Select,
  Form,
  Tabs,
  Col,
  Row,
  Spin,
  Popconfirm,
  TimePicker,
  DatePicker,
} from 'antd';
import { BackIcon } from '../autoCloseSetting/icon';

import moment from 'moment';
import styles from './index.less';
import { notification } from '@/utils/utils';
import { PlusOutlined } from '@ant-design/icons';

import DeleteRed12 from '@/assets/deleteRed12.png';
import Edit12 from '@/assets/edit12.png';

const { RangePicker } = DatePicker;
class AgentWorkerTime extends Component {
  formRef = React.createRef();
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      saveLoading: false,
      pageNum: 1,
      pageSize: 10,
      total: 0,

      showTab5Add: false,
      tabs5Loading: false,
      timeZoneList: [],
      workTimeId: '',
      workTimeList: [],
      workTimeDetail: {
        workTimeName: '',
        workTimeDescription: '',
        timezoneName: '',
        timezoneValue: [],
        workTimeHoursMap: {
          '1': [{}],
          '2': [{}],
          '3': [{}],
          '4': [{}],
          '5': [{}],
          '6': [{}],
          '7': [{}],
        },
        timeHolidaysList: [],
      },
      workTimePage: {
        pageNum: 1,
        pageSize: 10,
        total: 0,
      },
      weekList: [
        {
          title: getIntl().formatMessage({
            id: 'message.week.7',
          }),
          key: '7',
        },
        {
          title: getIntl().formatMessage({
            id: 'message.week.1',
          }),
          key: '1',
        },
        {
          title: getIntl().formatMessage({
            id: 'message.week.2',
          }),
          key: '2',
        },
        {
          title: getIntl().formatMessage({
            id: 'message.week.3',
          }),
          key: '3',
        },
        {
          title: getIntl().formatMessage({
            id: 'message.week.4',
          }),
          key: '4',
        },
        {
          title: getIntl().formatMessage({
            id: 'message.week.5',
          }),
          key: '5',
        },
        {
          title: getIntl().formatMessage({
            id: 'message.week.6',
          }),
          key: '6',
        },
      ],
    };
  }
  componentDidMount() {
    this.queryWorkTimeList();
    this.queryTimeZone();
  }

  //工单配置时间列表
  queryWorkTimeList = () => {
    this.setState({ loading: true });
    this.props.dispatch({
      type: 'workOrderExtension/queryWorkTimeList',
      payload: {
        pageNum: this.state.workTimePage.pageNum,
        pageSize: this.state.workTimePage.pageSize,
      },
      callback: response => {
        let { code, data, msg } = response;
        if (200 === code) {
          let workTimeList = response.data.records;
          this.setState({
            workTimeList: workTimeList,
            workTimePage: {
              ...this.state.workTimePage,
              total: response.data.total,
            },
          });
        } else {
          notification.error({
            message: msg,
          });
        }
        this.setState({ loading: false });
      },
    });
  };
  addTab5 = () => {
    this.formRef.current?.resetFields();

    this.setState({
      workTimeDetail: {
        workTimeName: '',
        workTimeDescription: '',
        timezoneName: '',
        timezoneValue: [],
        workTimeHoursMap: {
          '1': [{}],
          '2': [{}],
          '3': [{}],
          '4': [{}],
          '5': [{}],
          '6': [{}],
          '7': [{}],
        },
        timeHolidaysList: [],
      },
      showTab5Add: true,
    });
  };
  updateWorkTime = value => {
    this.queryTimeDeatal(value);
  };
  queryTimeDeatal = value => {
    this.setState({
      tabs5Loading: true,
      workTimeId: value.workTimeId,
    });
    this.props.dispatch({
      type: 'workOrderExtension/workTimeDetail',
      payload: {
        workTimeId: value.workTimeId,
      },
      callback: response => {
        let { code, data, msg } = response;
        if (200 === code) {
          let workTimeDetail = data;
          workTimeDetail.timeHolidaysList = data.timeHolidaysList?.map(item => {
            return {
              holidayId: item?.holidayId, //假期id
              workTimeId: item?.workTimeId, //工作时间id
              holidayName: item?.holidayName, //假期名称
              date: [moment(item.startDate), moment(item.endDate)],
            };
          });
          workTimeDetail.workTimeHoursMap['1'] = data.workTimeHoursMap[
            '1'
          ]?.map(item => {
            return {
              hourId: item?.hourId, //工作日id
              workTimeId: item?.workTimeId, //工作时间id
              weekType: 1, //周类型 1、周一 2、周二 3、周三...
              workTime:
                item.startTime && item.endTime
                  ? [
                      moment(item.startTime, 'HH:mm:ss'),
                      moment(item.endTime, 'HH:mm:ss'),
                    ]
                  : '',
            };
          });
          workTimeDetail.workTimeHoursMap['2'] = data.workTimeHoursMap[
            '2'
          ]?.map(item => {
            return {
              hourId: item?.hourId, //工作日id
              workTimeId: item?.workTimeId, //工作时间id

              weekType: 2, //周类型 1、周一 2、周二 3、周三...
              workTime:
                item.startTime && item.endTime
                  ? [
                      moment(item.startTime, 'HH:mm:ss'),
                      moment(item.endTime, 'HH:mm:ss'),
                    ]
                  : '',
            };
          });
          workTimeDetail.workTimeHoursMap['3'] = data.workTimeHoursMap[
            '3'
          ]?.map(item => {
            return {
              hourId: item?.hourId, //工作日id
              workTimeId: item?.workTimeId, //工作时间id

              weekType: 3, //周类型 1、周一 2、周二 3、周三...
              workTime:
                item.startTime && item.endTime
                  ? [
                      moment(item.startTime, 'HH:mm:ss'),
                      moment(item.endTime, 'HH:mm:ss'),
                    ]
                  : '',
            };
          });
          workTimeDetail.workTimeHoursMap['4'] = data.workTimeHoursMap[
            '4'
          ]?.map(item => {
            return {
              hourId: item?.hourId, //工作日id
              workTimeId: item?.workTimeId, //工作时间id

              weekType: 4, //周类型 1、周一 2、周二 3、周三...
              workTime:
                item.startTime && item.endTime
                  ? [
                      moment(item.startTime, 'HH:mm:ss'),
                      moment(item.endTime, 'HH:mm:ss'),
                    ]
                  : '',
            };
          });
          workTimeDetail.workTimeHoursMap['5'] = data.workTimeHoursMap[
            '5'
          ]?.map(item => {
            return {
              hourId: item?.hourId, //工作日id
              workTimeId: item?.workTimeId, //工作时间id

              weekType: 5, //周类型 1、周一 2、周二 3、周三...
              workTime:
                item.startTime && item.endTime
                  ? [
                      moment(item.startTime, 'HH:mm:ss'),
                      moment(item.endTime, 'HH:mm:ss'),
                    ]
                  : '',
            };
          });
          workTimeDetail.workTimeHoursMap['6'] = data.workTimeHoursMap[
            '6'
          ]?.map(item => {
            return {
              hourId: item?.hourId, //工作日id
              workTimeId: item?.workTimeId, //工作时间id

              weekType: 6, //周类型 1、周一 2、周二 3、周三...
              workTime:
                item.startTime && item.endTime
                  ? [
                      moment(item.startTime, 'HH:mm:ss'),
                      moment(item.endTime, 'HH:mm:ss'),
                    ]
                  : '',
            };
          });
          workTimeDetail.workTimeHoursMap['7'] = data.workTimeHoursMap[
            '7'
          ]?.map(item => {
            return {
              hourId: item?.hourId, //工作日id
              workTimeId: item?.workTimeId, //工作时间id

              weekType: 7, //周类型 1、周一 2、周二 3、周三...
              workTime:
                item.startTime && item.endTime
                  ? [
                      moment(item.startTime, 'HH:mm:ss'),
                      moment(item.endTime, 'HH:mm:ss'),
                    ]
                  : '',
            };
          });
          this.setState({
            showTab5Add: true,
            workTimeDetail: workTimeDetail,
            workTimeId: data.workTimeId,
          });
        } else {
          notification.error({
            message: msg,
          });
        }
        this.setState({ tabs5Loading: false });
      },
    });
  };
  deleteWorkTime = value => {
    this.props.dispatch({
      type: 'workOrderExtension/workTimeDel',
      payload: {
        workTimeId: value.workTimeId,
      },
      callback: response => {
        let { code, data, msg } = response;
        if (200 === code) {
          this.queryWorkTimeList();
          notification.success({
            message: msg,
          });
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };
  cancelAddTime = () => {
    this.queryWorkTimeList();
    this.setState({
      showTab5Add: false,
      workTimeId: '',
    });
  };

  handleChangeTablePagination = pagination => {
    let pageSize = pagination.pageSize;
    let pageNum = pagination.current;
    this.setState(
      {
        workTimePage: {
          pageNum: pageNum,
          pageSize: pageSize,
        },
      },
      () => {
        this.queryWorkTimeList();
      },
    );
  };
  //星期一到星期日字段变化
  onValuesChangeWeek = (changedValues, allValues) => {
    //校验数据重复或者重叠的逻辑
    //读取key
    let key = Object.keys(changedValues)?.[0];
    //只处理星期一二三四五六七
    if (['1', '2', '3', '4', '5', '6', '7'].includes(key)) {
      //拿到value
      let data = this.formRef.current?.getFieldValue(key);
      //记录value的长度
      let len = data.length;

      if (data) {
        //循环判断去重
        // const uniqueArray = data?.reduce((acc, curr) => {
        //   console.log(acc, curr, 'allValues');
        //   if (!curr) return acc;
        //   const existingWorktime = acc?.find(
        //     item =>
        //       JSON.stringify(item.worktime) === JSON.stringify(curr.worktime),
        //   );
        //   return existingWorktime ? acc : [...acc, curr]; // 如果存在相同的worktime则不添加，否则添加当前对象到累加器中
        // }, []);
        //不需要去重，只要判断下一条数据的开始时间大于上一条数据的结束时间
        const uniqueArray = data?.reduce((acc, curr, index) => {
          // if (!curr) return acc;
          console.log(acc, curr, index, 'allValues');
          if (
            curr !== undefined &&
            index !== 0 &&
            ((moment(curr.workTime[0])?.format('HH:mm:ss') <
              moment(acc[index - 1].workTime[1])?.format('HH:mm:ss') &&
              moment(curr.workTime[0])?.format('HH:mm:ss') >=
                moment(acc[index - 1].workTime[0])?.format('HH:mm:ss')) ||
              (moment(curr.workTime[1])?.format('HH:mm:ss') >
                moment(acc[index - 1].workTime[0])?.format('HH:mm:ss') &&
                moment(curr.workTime[1])?.format('HH:mm:ss') <=
                  moment(acc[index - 1].workTime[1])?.format('HH:mm:ss')))
          ) {
            notification.error({
              message: getIntl().formatMessage({
                id: 'channel.config.tab.5.week.error',
              }),
            });
            return acc;
          } else {
            return [...acc, curr]; // 如果存在相同的worktime则不添加，否则添加当前对象到累加器中
          }
        }, []);
        //赋值
        this.formRef.current?.setFieldValue(key, uniqueArray);
        console.log(
          changedValues,
          allValues,
          key,
          data,
          uniqueArray,
          'changedValues, allValues',
        );
      }
    }
  };
  /**
   * 查询时区
   */
  queryTimeZone = () => {
    this.setState({ loading: true });
    this.props.dispatch({
      type: 'personalCenter/listTimeZone',
      callback: response => {
        let { code, data, msg } = response;
        if (code === 200) {
          this.setState({
            timeZoneList: data,
          });
        } else {
          notification.error({
            message: msg,
          });
        }
        this.setState({ loading: false });
      },
    });
  };
  onFinish = values => {
    let payload = { ...values };
    console.log(values, 'onFinish');
    payload.workTimeId = this.state.workTimeId;
    payload.timezoneName = this.state.timeZoneList?.find(
      time => time.timeZoneId === payload.timezoneValue,
    )?.timeZoneName;
    payload.workTimeHoursList = [];
    values['1']?.forEach(item => {
      if (item?.['workTime']?.[0] && item?.['workTime']?.[1]) {
        payload.workTimeHoursList.push({
          hourId: item?.hourId ? item?.hourId : '', //工作日id
          workTimeId: item?.workTimeId, //工作时间id
          startTime: item?.['workTime']?.[0]
            ? moment(item?.['workTime']?.[0])?.format('HH:mm:ss')
            : '', //开始时间
          endTime: item?.['workTime']?.[1]
            ? moment(item?.['workTime']?.[1])?.format('HH:mm:ss')
            : '', //结束时间
          weekType: 1, //周类型 1、周一 2、周二 3、周三...
        });
      }
    });
    values['2']?.forEach(item => {
      if (item?.['workTime']?.[0] && item?.['workTime']?.[1]) {
        payload.workTimeHoursList.push({
          hourId: item?.hourId ? item?.hourId : '', //工作日id
          workTimeId: item?.workTimeId, //工作时间id
          startTime: item?.['workTime']?.[0]
            ? moment(item?.['workTime']?.[0])?.format('HH:mm:ss')
            : '', //开始时间
          endTime: item?.['workTime']?.[1]
            ? moment(item?.['workTime']?.[1])?.format('HH:mm:ss')
            : '', //结束时间
          weekType: 2, //周类型 1、周一 2、周二 3、周三...
        });
      }
    });
    values['3']?.forEach(item => {
      if (item?.['workTime']?.[0] && item?.['workTime']?.[1]) {
        payload.workTimeHoursList.push({
          hourId: item?.hourId ? item?.hourId : '', //工作日id
          workTimeId: item?.workTimeId, //工作时间id
          startTime: item?.['workTime']?.[0]
            ? moment(item?.['workTime']?.[0])?.format('HH:mm:ss')
            : '', //开始时间
          endTime: item?.['workTime']?.[1]
            ? moment(item?.['workTime']?.[1])?.format('HH:mm:ss')
            : '', //结束时间
          weekType: 3, //周类型 1、周一 2、周二 3、周三...
        });
      }
    });
    values['4']?.forEach(item => {
      if (item?.['workTime']?.[0] && item?.['workTime']?.[1]) {
        payload.workTimeHoursList.push({
          hourId: item?.hourId ? item?.hourId : '', //工作日id
          workTimeId: item?.workTimeId, //工作时间id
          startTime: item?.['workTime']?.[0]
            ? moment(item?.['workTime']?.[0])?.format('HH:mm:ss')
            : '', //开始时间
          endTime: item?.['workTime']?.[1]
            ? moment(item?.['workTime']?.[1])?.format('HH:mm:ss')
            : '', //结束时间
          weekType: 4, //周类型 1、周一 2、周二 3、周三...
        });
      }
    });
    values['5']?.forEach(item => {
      if (item?.['workTime']?.[0] && item?.['workTime']?.[1]) {
        payload.workTimeHoursList.push({
          hourId: item?.hourId ? item?.hourId : '', //工作日id
          workTimeId: item?.workTimeId, //工作时间id
          startTime: item?.['workTime']?.[0]
            ? moment(item?.['workTime']?.[0])?.format('HH:mm:ss')
            : '', //开始时间
          endTime: item?.['workTime']?.[1]
            ? moment(item?.['workTime']?.[1])?.format('HH:mm:ss')
            : '', //结束时间
          weekType: 5, //周类型 1、周一 2、周二 3、周三...
        });
      }
    });
    values['6']?.forEach(item => {
      if (item?.['workTime']?.[0] && item?.['workTime']?.[1]) {
        payload.workTimeHoursList.push({
          hourId: item?.hourId ? item?.hourId : '', //工作日id
          workTimeId: item?.workTimeId, //工作时间id
          startTime: item?.['workTime']?.[0]
            ? moment(item?.['workTime']?.[0])?.format('HH:mm:ss')
            : '', //开始时间
          endTime: item?.['workTime']?.[1]
            ? moment(item?.['workTime']?.[1])?.format('HH:mm:ss')
            : '', //结束时间
          weekType: 6, //周类型 1、周一 2、周二 3、周三...
        });
      }
    });
    values['7']?.forEach(item => {
      if (item?.['workTime']?.[0] && item?.['workTime']?.[1]) {
        payload.workTimeHoursList.push({
          hourId: item?.hourId ? item?.hourId : '', //工作日id
          workTimeId: item?.workTimeId, //工作时间id
          startTime: item?.['workTime']?.[0]
            ? moment(item?.['workTime']?.[0])?.format('HH:mm:ss')
            : '', //开始时间
          endTime: item?.['workTime']?.[1]
            ? moment(item?.['workTime']?.[1])?.format('HH:mm:ss')
            : '', //结束时间
          weekType: 7, //周类型 1、周一 2、周二 3、周三...
        });
      }
    });
    payload.timeHolidaysList = values.timeHolidaysList?.map(item => {
      return {
        holidayId: item?.holidayId ? item?.holidayId : '', //假期id
        workTimeId: this.state.workTimeId, //工作时间id
        holidayName: item.holidayName, //假期名称
        startDate: item?.['date']?.[0]
          ? moment(item?.['date']?.[0])?.format('YYYY-MM-DD')
          : '', //开始日期
        endDate: item?.['date']?.[1]
          ? moment(item?.['date']?.[1])?.format('YYYY-MM-DD')
          : '', //结束日期
      };
    });

    delete payload['1'];
    delete payload['2'];
    delete payload['3'];
    delete payload['4'];
    delete payload['5'];
    delete payload['6'];
    delete payload['7'];
    console.log(payload, 'onFinish1');
    this.setState({
      saveLoading: true,
    });
    this.props.dispatch({
      type: 'workOrderExtension/addOrUpdateWorkTime',
      payload: payload,
      callback: response => {
        let { code, data, msg } = response;
        if (200 === code) {
          notification.success({
            message: getIntl().formatMessage({
              id: 'customer.ext.info.save.success',
            }),
          });
          this.cancelAddTime();
        } else {
          notification.error({
            message: msg,
          });
        }
        this.setState({ saveLoading: false });
      },
    });
  };

  render() {
    const { loading } = this.state;

    const columns = [
      {
        title: getIntl().formatMessage({
          id: 'channel.config.tab.5.base.name',
          defaultValue: '名称',
        }),
        dataIndex: 'workTimeName',
        key: 'workTimeName',
        width: 100,
        ellipsis: true,
      },
      {
        title: getIntl().formatMessage({
          id: 'channel.config.tab.5.base.timezone',
          defaultValue: '时区',
        }),
        dataIndex: 'timezoneValue',
        key: 'timezoneValue',
        width: 120,
        ellipsis: true,
      },
      {
        title: getIntl().formatMessage({
          id: 'channel.config.tab.5.base.des',
          defaultValue: '描述',
        }),
        dataIndex: 'workTimeDescription',
        key: 'workTimeDescription',
        width: 200,
        ellipsis: true,
      },
      {
        title: getIntl().formatMessage({
          id: 'document.knowledge.base.table.creation.time',
          defaultValue: '创建时间',
        }),
        dataIndex: 'createTime',
        key: 'createTime',
        width: 180,
        ellipsis: true,
      },
      {
        title: getIntl().formatMessage({
          id: 'work.order.management.table.modify.time',
          defaultValue: '修改时间',
        }),
        dataIndex: 'modifyTime',
        key: 'modifyTime',
        width: 180,
        ellipsis: true,
      },
      {
        title: getIntl().formatMessage({
          id: 'document.knowledge.base.table.operation',
          defaultValue: '操作',
        }),
        dataIndex: 'operation',
        key: 'operation',
        width: 200,
        ellipsis: true,
        render: (text, record) => {
          return (
            <div className={styles.operationTable}>
              <div
                onClick={() => this.updateWorkTime(record)}
                style={{ marginRight: 8, color: '#3463FC' }}
              >
                <img src={Edit12} />
                <FormattedMessage id="update.channel" defaultMessage="修改" />
              </div>
              <Popconfirm
                title={getIntl().formatMessage({
                  id:
                    'document.knowledge.base.table.operation.delete.Popconfirm',
                })}
                onConfirm={() => this.deleteWorkTime(record)}
                okText={getIntl().formatMessage({
                  id: 'work.order.management.table.robot.work.order.yes',
                  defaultValue: '是',
                })}
                cancelText={getIntl().formatMessage({
                  id: 'work.order.management.table.robot.work.order.no',
                  defaultValue: '否',
                })}
              >
                <div style={{ color: '#F22417' }}>
                  <img src={DeleteRed12} />
                  <FormattedMessage
                    id="document.knowledge.base.table.operation.delete"
                    defaultMessage="删除"
                  />
                </div>
              </Popconfirm>
            </div>
          );
        },
      },
    ];

    return this.state.showTab5Add ? (
      <div className={styles.workContent}>
        <div className={styles.workContentTop}>
          <div style={{ display: 'flex' }}>
            <span
              style={{ marginRight: 10, lineHeight: '35px', cursor: 'pointer' }}
              onClick={() => history.go(-1)}
            >
              {BackIcon()}
            </span>
            <FormattedMessage id="channel.config.tab.5.button" />
          </div>
        </div>

        <div className={styles.pageContent}>
          <Spin spinning={loading}>
            <div>
              <div className={styles.pageContentTab5}>
                <Spin spinning={this.state.tabs5Loading}>
                  <Form
                    layout="vertical"
                    ref={this.formRef}
                    // {...formItemLayoutWithOutLabel}
                    onFinish={this.onFinish}
                    initialValues={this.state.workTimeDetail}
                    onValuesChange={(changedValues, allValues) =>
                      this.onValuesChangeWeek(changedValues, allValues)
                    }
                  >
                    <div className={styles.pageContentTab5_base}>
                      <p className={styles.pageContentTab5_title}>
                        <FormattedMessage
                          id="channel.config.tab.5.base"
                          defaultValue="工作时间基本信息"
                        />
                      </p>
                      <Row style={{ width: '100%' }} gutter={20}>
                        <Col span={9}>
                          <Form.Item
                            label={getIntl().formatMessage({
                              id: 'channel.config.tab.5.base.name',
                              defaultValue: '名称',
                            })}
                            rules={[
                              {
                                required: true,
                              },
                            ]}
                            name={'workTimeName'}
                          >
                            <Input
                              placeholder={getIntl().formatMessage({
                                id: 'channel.config.tab.5.base.name.p',
                                defaultValue: '请输入名称',
                              })}
                              style={{ width: '100%' }}
                            />
                          </Form.Item>
                        </Col>
                        <Col span={15}>
                          <Form.Item
                            label={getIntl().formatMessage({
                              id: 'channel.config.tab.5.base.des',
                              defaultValue: '描述',
                            })}
                            name={'workTimeDescription'}
                          >
                            <Input
                              placeholder={getIntl().formatMessage({
                                id: 'channel.config.tab.5.base.des.p',
                                defaultValue: '请输入描述',
                              })}
                              style={{ width: '100%' }}
                            />
                          </Form.Item>
                        </Col>
                      </Row>
                      <Row style={{ width: '100%' }} gutter={20}>
                        <Col span={9}>
                          <Form.Item
                            label={getIntl().formatMessage({
                              id: 'channel.config.tab.5.base.zone',
                              defaultValue: '选择时区',
                            })}
                            rules={[
                              {
                                required: true,
                              },
                            ]}
                            name={'timezoneValue'}
                          >
                            <Select
                              placeholder={getIntl().formatMessage({
                                id: 'channel.config.tab.5.base.zone',
                                defaultValue: '选择时区',
                              })}
                              showSearch
                              filterOption={(inputValue, option) =>
                                option.label
                                  .toLowerCase()
                                  .indexOf(inputValue.toLowerCase()) >= 0
                              }
                              options={this.state.timeZoneList.map(item => ({
                                label: `${item.timeZoneName}（${item.timeZoneCode}）`,
                                value: item.timeZoneId,
                                key: item.timeZoneId,
                              }))}
                              fieldNames={{
                                label: 'label',
                                value: 'value',
                              }}
                            />
                          </Form.Item>
                        </Col>
                      </Row>
                    </div>

                    <div className={styles.pageContentTab5_each}>
                      {this.state.weekList?.map(week => {
                        return (
                          <div>
                            <p className={styles.pageContentTab5_title}>
                              {week.title}
                            </p>
                            <Form.List
                              name={week.key}
                              initialValue={
                                this.state.workTimeDetail.workTimeHoursMap[
                                  week.key
                                ]
                              }

                              // rules={[
                              //   {
                              //     validator: async (_, names) => {
                              //       if (!names || names.length < 2) {
                              //         return Promise.reject(
                              //           new Error('At least 2 passengers'),
                              //         );
                              //       }
                              //     },
                              //   },
                              // ]}
                            >
                              {(fields, { add, remove }, { errors }) => (
                                <>
                                  {fields.map((field, index) => (
                                    <Form.Item
                                      // {...(index === 0
                                      //   ? formItemLayout
                                      //   : formItemLayoutWithOutLabel)}
                                      label={
                                        index === 0
                                          ? getIntl().formatMessage({
                                              id:
                                                'channel.config.tab.5.base.time.work',
                                            })
                                          : ''
                                      }
                                      required={false}
                                      key={field.key}
                                    >
                                      <Form.Item
                                        {...field}
                                        name={[field.name, 'workTime']}
                                        noStyle
                                      >
                                        <TimePicker.RangePicker />
                                      </Form.Item>
                                      {index >= 1 ? (
                                        <img
                                          src={DeleteRed12}
                                          style={{
                                            marginLeft: 10,
                                            width: 20,
                                            height: 20,
                                            cursor: 'pointer',
                                          }}
                                          onClick={() => remove(field.name)}
                                        />
                                      ) : null}
                                    </Form.Item>
                                  ))}
                                  <Form.Item style={{ marginTop: '-10px' }}>
                                    <Button
                                      type="text"
                                      onClick={() => {
                                        let len = this.formRef.current?.getFieldValue(
                                          week.key,
                                        )?.length;
                                        console.log(
                                          this.formRef.current?.getFieldValue(
                                            week.key,
                                          ),
                                          len,
                                        );
                                        if (
                                          len > 0 &&
                                          (this.formRef.current?.getFieldValue(
                                            week.key,
                                          )[len - 1] === undefined ||
                                            Object.keys(
                                              this.formRef.current?.getFieldValue(
                                                week.key,
                                              )[len - 1],
                                            )?.length === 0)
                                        ) {
                                          notification.error({
                                            message: getIntl().formatMessage({
                                              id:
                                                'channel.config.tab.5.week.error.kong',
                                            }),
                                          });
                                        } else {
                                          add();
                                        }
                                      }}
                                      icon={<PlusOutlined />}
                                    >
                                      <span>
                                        <FormattedMessage id="channel.config.tab.5.time.add" />
                                      </span>
                                    </Button>

                                    <Form.ErrorList errors={errors} />
                                  </Form.Item>
                                </>
                              )}
                            </Form.List>
                          </div>
                        );
                      })}
                    </div>
                    <div className={styles.pageContentTab5_holidays}>
                      <p className={styles.pageContentTab5_title}>
                        <FormattedMessage
                          id="channel.config.tab.5.holiday"
                          defaultValue="节假日"
                        />
                      </p>
                      <Form.List
                        name={'timeHolidaysList'}
                        // initialValue={this.state.timeHolidaysList}
                      >
                        {(fields, { add, remove }, { errors }) => (
                          <>
                            {fields.map((field, index) => (
                              <Row
                                gutter={20}
                                style={{ marginBottom: 10 }}
                                align="middle"
                              >
                                <Col span={10}>
                                  <Form.Item
                                    label={getIntl().formatMessage({
                                      id: 'channel.config.tab.5.base.name',
                                    })}
                                    name={[field.name, 'holidayName']}
                                    rules={[
                                      {
                                        required: true,
                                      },
                                    ]}
                                  >
                                    <Input
                                      placeholder={getIntl().formatMessage({
                                        id: 'channel.config.tab.5.base.name.p',
                                      })}
                                    />
                                  </Form.Item>
                                </Col>
                                <Col span={10}>
                                  <Form.Item
                                    label={getIntl().formatMessage({
                                      id: 'channel.config.tab.5.base.date',
                                    })}
                                    name={[field.name, 'date']}
                                    rules={[
                                      {
                                        required: true,
                                      },
                                    ]}
                                  >
                                    <RangePicker
                                      // showTime
                                      format="YYYY-MM-DD"
                                    />
                                  </Form.Item>
                                </Col>
                                <Col span={2}>
                                  <img
                                    src={DeleteRed12}
                                    style={{
                                      marginLeft: 10,
                                      width: 20,
                                      height: 20,
                                      marginTop: 10,
                                      cursor: 'pointer',
                                    }}
                                    onClick={() => remove(field.name)}
                                  />
                                </Col>
                              </Row>
                            ))}
                            <Form.Item style={{ marginTop: '-10px' }}>
                              <Button
                                type="text"
                                onClick={() => add()}
                                icon={<PlusOutlined />}
                              >
                                <span>
                                  <FormattedMessage id="channel.config.tab.5.time.add" />
                                </span>
                              </Button>

                              <Form.ErrorList errors={errors} />
                            </Form.Item>
                          </>
                        )}
                      </Form.List>
                    </div>
                    <Row justify="center" style={{ marginTop: 30 }}>
                      <Form.Item>
                        <Button
                          onClick={this.cancelAddTime}
                          style={{ marginRight: 20 }}
                        >
                          <FormattedMessage
                            id="work.record.button.cancel"
                            defaultValue="取消"
                          />
                        </Button>
                        <Button
                          type="primary"
                          loading={this.state.saveLoading}
                          htmlType="submit"
                        >
                          <FormattedMessage
                            id="customerInformation.add.basicInformation.button.save"
                            defaultValue="保存"
                          />
                        </Button>
                      </Form.Item>
                    </Row>
                  </Form>
                </Spin>
              </div>
            </div>
          </Spin>
        </div>
      </div>
    ) : (
      <div className={styles.workContent}>
        <div className={styles.workContentTop}>
          <div style={{ display: 'flex' }}>
            <span
              style={{ marginRight: 10, lineHeight: '35px', cursor: 'pointer' }}
              onClick={() => history.go(-1)}
            >
              {BackIcon()}
            </span>

            <FormattedMessage id="agentOperationSettings.config.title.2" />
          </div>
          <Button
            type="primary"
            onClick={this.addTab5}
            icon={<PlusOutlined style={{ fontSize: '12px' }} />}
          >
            <span style={{ fontSize: 12 }}>
              <FormattedMessage
                id="channel.config.tab.5.button"
                defaultValue="工作时间配置"
              />
            </span>
          </Button>
        </div>

        <div className={styles.pageContent}>
          <Spin spinning={loading}>
            <div>
              <div className={styles.pageContentTab5}>
                <div className={styles.tableContent}>
                  <Spin spinning={this.state.tabs5Loading}>
                    <Table
                      dataSource={this.state.workTimeList}
                      columns={columns}
                      onChange={this.handleChangeTablePagination}
                      pagination={{
                        total: this.state.workTimePage.total,
                        pageSize: this.state.workTimePage.pageSize,
                        current: this.state.workTimePage.pageNum,
                        showSizeChanger: true,
                        pageSizeOptions: [10, 20, 50, 100],
                        showTotal: total => (
                          <FormattedMessage
                            id="studentManagement.altogether"
                            defaultMessage={`共 ${this.state.workTimePage.total} 条`}
                            values={{
                              total: this.state.workTimePage.total,
                            }}
                          />
                        ),
                      }}
                    />
                  </Spin>
                </div>
              </div>
            </div>
          </Spin>
        </div>
      </div>
    );
  }
}

const mapStateToProps = ({
  workOrderExtension,
  customerExt,
  layouts,
  newChannelConfiguration,
  workOrderCenter,
}) => {
  return {
    ...workOrderExtension,
    ...customerExt,
    authAccess: layouts.auth,
    ...newChannelConfiguration,
    ...workOrderCenter,
  };
};
export default connect(mapStateToProps)(AgentWorkerTime);
