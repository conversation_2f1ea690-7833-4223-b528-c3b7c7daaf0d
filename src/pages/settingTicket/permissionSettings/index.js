import React, { useState, useRef, useEffect } from 'react';
import {
  Button,
  notification,
  Row,
  Col,
  Form,
  Checkbox,
  Spin,
  Radio,
} from 'antd';
import styles from './index.less';
import { useDispatch, getIntl, FormattedMessage, Link, history } from 'umi';
import { LeftReturnArrow } from '../icon';
import { convertBoolean } from '@/utils/utils';
import HOCAuth from '@/components/HOCAuth/index';

const PermissionSettings = () => {
  const dispatch = useDispatch();
  const formRef = useRef(null);
  const [loadingBtn, setLoadingBtn] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    queryCompanyPermission();
  }, []);

  const queryCompanyPermission = () => {
    setLoading(true);
    dispatch({
      type: 'permissionSettings/queryCompanyPermission',
      callback: response => {
        setLoading(false);
        if (response.code === 200) {
          let newData = {
            downRecordingFile: convertBoolean(response.data?.downRecordingFile),
            phoneHide: convertBoolean(response.data?.phoneHide),
            workbenchPhoneHide: convertBoolean(
              response.data?.workbenchPhoneHide,
            ),
          };
          formRef.current?.setFieldsValue(newData);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  const onFinish = values => {
    setLoadingBtn(true);
    //downRecordingFile允许下载录音文件 0 - 不允许 1 - 允许 phoneHide是否脱敏 0 - 未脱敏 1 - 脱敏
    let params = {
      downRecordingFile: values.downRecordingFile ? 1 : 0,
      phoneHide: values.phoneHide ? 1 : 0,
      workbenchPhoneHide: values.workbenchPhoneHide ? 1 : 0,
    };
    dispatch({
      type: 'permissionSettings/saveOrUpdateCompanyPermission',
      payload: params,
      callback: response => {
        setLoadingBtn(false);
        if (response.code === 200) {
          notification.success({
            message: response.msg,
          });
          history.push('/settingTicket');
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  return (
    <Spin spinning={loading}>
      <div className={styles.permissionSettingsContainer}>
        <div className={styles.titleContainer}>
          <Link to={'/settingTicket'}>
            <span>{LeftReturnArrow()}</span>
          </Link>
          <p>
            <FormattedMessage
              id="ticket.setting.menu.1.permission.setting"
              defaultValue=""
            />
          </p>
        </div>
        <div className={styles.formContainer}>
          <Form
            name="basic"
            onFinish={onFinish}
            autoComplete="off"
            layout="vertical"
            ref={formRef}
          >
            <Row gutter={24}>
              <Col span={8}>
                <Form.Item
                  label={getIntl().formatMessage({
                    id:
                      'permission.setting.sensitive.information.desensitization.worktable',
                    defaultValue: '工作台聊天敏感信息脱敏',
                  })}
                  name="workbenchPhoneHide"
                  valuePropName="checked"
                >
                  <Checkbox>
                    <FormattedMessage
                      id="chat.channel.configuration.work.panels.checkbox"
                      defaultMessage="开启"
                    />
                  </Checkbox>
                </Form.Item>
                <p className={styles.tipsText}>
                  <FormattedMessage
                    id="permission.setting.sensitive.information.desensitization.tips.worktable"
                    defaultMessage="开启之后，用户发送的手机号、邮箱等信息将自动脱敏"
                  />
                </p>
              </Col>
            </Row>
            <Row gutter={24}>
              <Col span={8}>
                <Form.Item
                  label={getIntl().formatMessage({
                    id:
                      'permission.setting.sensitive.information.desensitization',
                    defaultValue: '敏感信息脱敏',
                  })}
                  name="phoneHide"
                  valuePropName="checked"
                >
                  <Checkbox>
                    <FormattedMessage
                      id="chat.channel.configuration.work.panels.checkbox"
                      defaultMessage="开启"
                    />
                  </Checkbox>
                </Form.Item>
                <p className={styles.tipsText}>
                  <FormattedMessage
                    id="permission.setting.sensitive.information.desensitization.tips"
                    defaultMessage="开启之后，用户发送的手机号、订单号等信息将自动脱敏"
                  />
                </p>
              </Col>
            </Row>
            <HOCAuth authKey={'down_recording_file'}>
              {authAccess => (
                <Row gutter={24}>
                  <Col span={8}>
                    <Form.Item
                      label={getIntl().formatMessage({
                        id: 'permission.setting.allow.downloading.audio.files',
                        defaultValue: '允许下载录音文件',
                      })}
                      name="downRecordingFile"
                      valuePropName="checked"
                    >
                      <Checkbox disabled={authAccess}>
                        <FormattedMessage
                          id="chat.channel.configuration.work.panels.checkbox"
                          defaultMessage="开启"
                        />
                      </Checkbox>
                    </Form.Item>
                    <p className={styles.tipsText}>
                      <FormattedMessage
                        id="permission.setting.allow.downloading.audio.files.tips"
                        defaultMessage="开启之后，座席或座席主管可在工单页面下载录音文件"
                      />
                    </p>
                  </Col>
                </Row>
              )}
            </HOCAuth>

            <Form.Item
              style={{ position: 'absolute', top: '0px', right: '0px' }}
            >
              <Link to={'/settingTicket'}>
                <Button style={{ marginRight: '10px' }}>
                  <FormattedMessage
                    id="awsAccountSetting.cancel.btn"
                    defaultMessage="取消"
                  />
                </Button>
              </Link>
              <Button type={'primary'} htmlType="submit" loading={loadingBtn}>
                <FormattedMessage
                  id="work.order.saved.btn"
                  defaultMessage="保存"
                />
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
    </Spin>
  );
};

export default PermissionSettings;
