import React, { Component } from 'react';
import { connect, getIntl, FormattedMessage } from 'umi';
import {
  Button,
  Table,
  Select,
  Form,
  Col,
  Row,
  Spin,
  Popconfirm,
  Tooltip,
} from 'antd';
import styles from './index.less';
import { notification } from '@/utils/utils';
import CustomModal from '@/components/customModal/index';
import {
  PlusOutlined,
  MenuOutlined,
  EditOutlined,
  DeleteOutlined,
} from '@ant-design/icons';
import { BackIcon, Delete } from '../autoCloseSetting/icon';
import { DndContext } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

import HOCAuth from '@/components/HOCAuth/index';

class TicketAttr extends Component {
  childModalRef = React.createRef();
  formRef = React.createRef();
  slaRuleRef = React.createRef();
  constructor(props) {
    super(props);
    this.state = {
      loading: false,
      saveLoading: false,
      dataSource: [],
      pageNum: 1,
      pageSize: 10,
      total: 0,
      updateId: '',
      openModal: false,
      languageList: [],
      language: '',
      modalTitle: 'add',
    };
  }
  componentDidMount() {
    let lang = localStorage.getItem('lang') || 'en-US';
    localStorage.setItem('lang', lang);
    this.setState(
      {
        language: lang,
      },
      () => {
        this.listWorkOrderExt();
        this.queryLanguage();
      },
    );
  }

  /**
   * 查询时区
   */
  queryLanguage = () => {
    this.setState({ loading: true });
    /**语言下拉 */
    this.props.dispatch({
      type: 'personalCenter/listLanguage',
      callback: response => {
        let { code, data, msg } = response;
        if (code === 200) {
          this.setState({
            languageList: data,
          });
        } else {
          notification.error({
            message: msg,
          });
        }
        this.setState({
          loading: false,
        });
      },
    });
  };

  addAttribute = () => {
    this.setState({ openModal: true, modalTitle: 'add' });
  };
  changeSetOpen = val => {
    this.setState({ openModal: val });
  };

  /**
   * 工单属性定义列表
   */
  listWorkOrderExt = () => {
    this.setState({ loading: true });
    this.props.dispatch({
      type: 'workOrderExtension/listWorkOrderExt',
      payload: {
        pageNum: this.state.pageNum,
        pageSize: this.state.pageSize,
        languageCode: this.state.language,
      },
      callback: response => {
        let { code, data, msg } = response;
        if (200 === code) {
          // console.log(data)
          let { rows, total } = data;
          this.setState({ dataSource: rows, total: total });
        } else {
          notification.error({
            message: msg,
          });
        }
        this.setState({ loading: false });
      },
    });
  };
  /**
   * 分页切换
   */
  handleTableChange = pagination => {
    this.setState(
      {
        pageSize: pagination.pageSize,
        pageNum: pagination.current,
      },
      () => {
        this.listWorkOrderExt();
      },
    );
  };
  /*
   *刷新列表
   */
  refreshList = () => {
    this.setState({ openModal: false });
    this.listWorkOrderExt();
  };
  // 修改数据，回显
  updateUserShow = id => {
    this.childModalRef.current.updateModelData(
      'workOrderExtension/queryWorkOrderExtById',
      { workOrderExtDefId: id },
    );
    this.setState({ updateId: id, modalTitle: 'update' });

    setTimeout(() => {
      this.changeSetOpen(true);
    }, 500);
  };
  /**
   * 删除表格数据
   */
  deleteUserShow = id => {
    this.setState({ loading: true });
    this.props.dispatch({
      type: 'workOrderExtension/deleteWorkOrderExt',
      payload: { workRecordExtDefId: id, languageCode: this.state.language },
      callback: response => {
        let { code, data, msg } = response;
        if (200 === code) {
          this.listWorkOrderExt();
        } else {
          notification.error({
            message: msg,
          });
        }
        this.setState({ loading: false });
      },
    });
  };
  /**
   * 语言改变
   */
  handleLenguageChange = (value, i) => {
    this.setState({ language: value }, () => {
      if (i == '2') {
        this.setState(
          {
            pageNum: 1,
            pageSize: 10,
          },
          () => {
            this.listWorkOrderExt();
          },
        );
      } else {
        this.queryWorkOrderType();
      }
    });
  };
  /**
   * 拖拽
   */
  Row = ({ children, ...props }) => {
    const {
      attributes,
      listeners,
      setNodeRef,
      setActivatorNodeRef,
      transform,
      transition,
      isDragging,
    } = useSortable({
      id: props['data-row-key'],
    });
    const style = {
      ...props.style,
      transform: CSS.Transform.toString(
        transform && {
          ...transform,
          scaleY: 1,
        },
      ),
      transition,
      ...(isDragging
        ? {
            position: 'relative',
            zIndex: 9999,
          }
        : {}),
    };
    return (
      <tr {...props} ref={setNodeRef} style={style} {...attributes}>
        {React.Children.map(children, child => {
          if (child.key === 'sort') {
            return React.cloneElement(child, {
              children: (
                <Tooltip
                  placement="top"
                  title={<FormattedMessage id="user.management.remove" />}
                >
                  <MenuOutlined
                    ref={setActivatorNodeRef}
                    style={{
                      touchAction: 'none',
                      cursor: 'move',
                    }}
                    {...listeners}
                  />
                </Tooltip>
              ),
            });
          }
          return child;
        })}
      </tr>
    );
  };
  onDragEnd = ({ active, over }) => {
    if (active.id !== over?.id) {
      let newArray = () => {
        const activeIndex = this.state.dataSource.findIndex(
          i => i.workRecordExtDefId === active.id,
        );
        const overIndex = this.state.dataSource.findIndex(
          i => i.workRecordExtDefId === over?.id,
        );
        const data = arrayMove(this.state.dataSource, activeIndex, overIndex);
        const payLoadData = data.map(item => {
          return {
            workRecordExtDefId: item.workRecordExtDefId,
            workRecordExtDefOrder: item.workRecordExtDefOrder,
            languageCode: this.state.language,
          };
        });
        this.props.dispatch({
          type: 'workOrderExtension/updateWorkOrderExtSort',
          payload: payLoadData,
          callback: response => {
            let { code, data, msg } = response;
            if (200 === code) {
              this.listWorkOrderExt();
            } else {
              notification.error({
                message: msg,
              });
            }
          },
        });
        return arrayMove(this.state.dataSource, activeIndex, overIndex);
      };
      this.setState({
        dataSource: newArray(),
      });
      console.log(this.state.dataSource);
    }
  };

  columns = () => {
    return [
      {
        key: 'sort',
      },
      // 属性名称
      {
        align: 'center',
        title: getIntl().formatMessage({ id: 'customer.ext.info.name' }),
        dataIndex: 'workRecordExtDefName',
        key: 'workRecordExtDefName',
      },
      // 属性编码
      {
        align: 'center',
        title: getIntl().formatMessage({ id: 'customer.ext.info.code' }),
        dataIndex: 'workRecordExtDefCode',
        key: 'workRecordExtDefCode',
      },
      // 属性类型
      {
        align: 'center',
        title: getIntl().formatMessage({
          id: 'customer.ext.info.type',
        }),
        dataIndex: 'propertyTypeName',
        key: 'propertyTypeName',
        // render: value => (
        //   <div>
        //     <span>
        //       {this.props.propertyTypeList.map(item => {
        //         if (item.value === value) {
        //           return item.label;
        //         }
        //       })}
        //     </span>
        //   </div>
        // ),
      },
      // 是否必填
      {
        align: 'center',
        title: getIntl().formatMessage({ id: 'customer.ext.info.require' }),
        dataIndex: 'isRequired',
        key: 'isRequired',
        render: value => {
          return (
            <span>
              {value === 1 ? (
                <FormattedMessage id="user.management.operation.btn.yes" />
              ) : (
                <FormattedMessage id="user.management.operation.btn.no" />
              )}
            </span>
          );
        },
      },
      // 是否多选
      {
        align: 'center',
        title: getIntl().formatMessage({
          id: 'customer.ext.info.multiple',
        }),
        dataIndex: 'dataStatus',
        key: 'dataStatus',
        render: value => {
          let status =
            value === 0
              ? getIntl().formatMessage({
                  id: 'user.management.operation.forbidden',
                })
              : value === 1
              ? getIntl().formatMessage({
                  id: 'user.management.operation.normal',
                })
              : value === 2
              ? getIntl().formatMessage({
                  id: 'user.management.operation.noApply',
                })
              : '';
          return (
            <div>
              <span>{status}</span>
            </div>
          );
        },
      },
      // 提示
      {
        align: 'center',
        title: getIntl().formatMessage({
          id: 'customer.ext.info.tips',
        }),
        dataIndex: 'prompt',
        key: 'prompt',
      },
      // 操作
      {
        align: 'center',
        title: getIntl().formatMessage({ id: 'customer.ext.info.operate' }),
        dataIndex: 'address',
        key: 'address',
        minWidth: 120,
        render: (value, data) => (
          <div style={{ textAlign: 'center' }}>
            {data.isSystemDefault === 1 ? (
              <div>--</div>
            ) : (
              <div style={{ display: 'inline-flex', alignItems: 'center' }}>
                {/* 修改 */}
                <HOCAuth authKey={'modify_ticket_expansion_fields'}>
                  {authAccess => (
                    <span
                      onClick={() =>
                        this.updateUserShow(data.workRecordExtDefId)
                      }
                      className={authAccess && 'disabled'}
                      style={{
                        marginRight: '8px',
                        display: 'flex',
                        alignItems: 'center',
                        color: '#3463FC',
                        marginBottom: 0,
                        cursor: 'pointer',
                      }}
                    >
                      <EditOutlined
                        style={{
                          color: '#3463FC',
                          marginRight: '4px',
                        }}
                      />
                      <FormattedMessage id="user.management.operation.update" />
                    </span>
                  )}
                </HOCAuth>
                {/* 删除 */}
                <HOCAuth authKey={'delete_ticket_expansion_fields'}>
                  {authAccess => (
                    <Popconfirm
                      // title="返回将清空表单，确定返回么？"
                      title={getIntl().formatMessage({
                        id: 'customer.ext.popconfirm.delete',
                        defaultValue: '确认删除?',
                      })}
                      onConfirm={() =>
                        this.deleteUserShow(data.workRecordExtDefId)
                      }
                      style={{ marginRight: '20px' }}
                      disabled={authAccess}
                    >
                      <span
                        className={authAccess && 'disabled'}
                        style={{
                          marginRight: '8px',
                          display: 'flex',
                          alignItems: 'center',
                          color: '#F22417',
                          marginBottom: 0,
                          cursor: 'pointer',
                        }}
                      >
                        <span
                          style={{ marginRight: '4px', marginTop: '4px' }}
                          className={authAccess && 'disabled'}
                        >
                          {Delete()}
                        </span>
                        <FormattedMessage id="delete.channel" />
                      </span>
                    </Popconfirm>
                  )}
                </HOCAuth>
              </div>
            )}
          </div>
        ),
      },
    ];
  };

  render() {
    const {
      loading,
      dataSource,
      total,
      pageSize,
      pageNum,
      languageList,
      language,
    } = this.state;
    return (
      <div className={styles.workContent}>
        <div className={styles.workContentTop}>
          <div style={{ display: 'flex' }}>
            <span
              style={{ marginRight: 10, lineHeight: '35px', cursor: 'pointer' }}
              onClick={() => history.go(-1)}
            >
              {BackIcon()}
            </span>

            <FormattedMessage id="ticketOrCustomerSettings.config.title.2" />
          </div>

          <HOCAuth authKey={'add_ticket_expansion_fields'}>
            {authAccess => (
              <Col>
                <Button
                  type="primary"
                  onClick={() => {
                    this.addAttribute();
                  }}
                  style={{ fontSize: 14 }}
                  disabled={authAccess}
                >
                  <PlusOutlined />
                  <FormattedMessage id="customer.ext.info.add" />
                </Button>
              </Col>
            )}
          </HOCAuth>
        </div>
        <div className={styles.pageContent}>
          <Spin spinning={loading}>
            <div>
              <div className={styles.pageContentTab2}>
                <div>
                  <Form>
                    <Row
                      style={{
                        marginTop: 20,
                        marginBottom: 10,
                        lineHeight: '40px',
                      }}
                      justify={'space-between'}
                    >
                      <Col span={8}>
                        <Form.Item
                          label={getIntl().formatMessage({
                            id: 'home.language.select',
                          })}
                        >
                          <Select
                            placeholder={getIntl().formatMessage({
                              id: 'home.set.language.select',
                              defaultValue: '请选择语言',
                            })}
                            options={languageList?.map(item => ({
                              label: item.languageName,
                              value: item.languageCode,
                              key: item.languageCode,
                            }))}
                            onChange={value =>
                              this.handleLenguageChange(value, '2')
                            }
                            value={this.state.language}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Form>
                </div>
                <div className={styles.tableContent}>
                  <DndContext
                    modifiers={[restrictToVerticalAxis]}
                    onDragEnd={this.onDragEnd}
                  >
                    <SortableContext
                      // rowKey array
                      items={dataSource.map(i => i.key)}
                      strategy={verticalListSortingStrategy}
                    >
                      <Table
                        components={{
                          body: {
                            row: this.Row,
                          },
                        }}
                        dataSource={dataSource}
                        columns={this.columns()}
                        rowKey={row => row.workRecordExtDefId}
                        onChange={this.handleTableChange}
                        pagination={{
                          total: total,
                          pageSize: pageSize,
                          current: pageNum,
                          showSizeChanger: true,
                          pageSizeOptions: [10, 20, 50, 100],
                          showTotal: total => (
                            <FormattedMessage
                              id="page.total.num"
                              defaultMessage={`共 ${total} 条`}
                              values={{ total }}
                            />
                          ),
                        }}
                      />
                    </SortableContext>
                  </DndContext>
                </div>
              </div>
            </div>
          </Spin>
        </div>
        <CustomModal
          title={
            this.state.modalTitle === 'update'
              ? getIntl().formatMessage({
                  id: 'customer.worker.info.modal.title',
                })
              : getIntl().formatMessage({
                  id: 'customer.worker.info.modal.title.add',
                })
          }
          refreshList={this.refreshList}
          formName={{
            name: 'workRecordExtDefName',
            code: 'workRecordExtDefCode',
            id: 'workRecordExtDefId',
          }}
          interface="workOrderExtension/addWorkOrderExt"
          ref={this.childModalRef}
          updateId={this.state.updateId}
          updateInterface="workOrderExtension/updateWorkOrderExt"
          optionList="workOrderExtOptionDefList"
          open={this.state.openModal}
          changeSetOpen={this.changeSetOpen}
          language={language}
        />
      </div>
    );
  }
}

const mapStateToProps = ({
  workOrderExtension,
  customerExt,
  layouts,
  newChannelConfiguration,
  workOrderCenter,
}) => {
  return {
    ...workOrderExtension,
    ...customerExt,
    authAccess: layouts.auth,
    ...newChannelConfiguration,
    ...workOrderCenter,
  };
};
export default connect(mapStateToProps)(TicketAttr);
