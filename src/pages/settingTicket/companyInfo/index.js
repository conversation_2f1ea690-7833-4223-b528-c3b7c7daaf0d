import React, { useState, useRef, useEffect } from 'react';
import { Button, notification, Form, Spin, Upload, Input } from 'antd';
import styles from './index.less';
import { useDispatch, getIntl, FormattedMessage, Link, history } from 'umi';
import { LeftReturnArrow } from '../icon';
import { PlusOutlined } from '@ant-design/icons';

const CompanyInfo = () => {
  const dispatch = useDispatch();
  const formRef = useRef(null);
  const [loadingBtn, setLoadingBtn] = useState(false);
  const [loading, setLoading] = useState(false);
  const [fileList, setFileList] = useState([]);
  const [previewImage, setPreviewImage] = useState('');
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewTitle, setPreviewTitle] = useState('');
  const [companyLogo, setCompanyLogo] = useState('');
  const userInfo = JSON.parse(sessionStorage.getItem('user'));
  const [companyInfo, setCompanyInfo] = useState({});
  useEffect(() => {
    dispatch({
      type: 'companyInfo/getCompanyInfo',
      payload: { companyId: userInfo.companyId },
      callback: response => {
        if (response.code === 200) {
          setCompanyInfo(response.data);
          setFileList([
            {
              uid: '-1',
              name: 'image.png',
              status: 'done',
              url: response.data?.companyLogo,
            },
          ]);
          formRef.current.setFieldsValue({
            companyName: response.data.companyName,
            companyAddress: response.data.companyAddress,
            companyLogo: response.data.companyLogo,
          });
        }
      },
    });
  }, []);
  const onFinish = values => {
    console.log(values);
    setLoadingBtn(true);
    values.companyId = companyInfo.companyId;
    dispatch({
      type: 'companyInfo/updateCompanyConfig',
      payload: values,
      callback: response => {
        setLoadingBtn(false);
        if (response.code === 200) {
          notification.success({
            message: response.msg,
          });
          history.push('/settingTicket');
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
    setLoadingBtn(false);
  };
  const getBase64 = file =>
    new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = error => reject(error);
    });

  const handleCancel = () => {
    history.push('/settingTicket');
  };

  const uploadProps = {
    maxCount: 1,
    accept: '.jpg,.png,.jpeg',
    name: 'file',
    multiple: false,
    beforeUpload: file => {
      console.log(file);
      let { size } = file;
      let fileSize = size / 1024; // Convert to KB
      if (fileSize > 500) {
        notification.error({
          message: getIntl().formatMessage(
            {
              id: 'ticket.setting.upload.size',
            },
            {
              fileSize: 500, // Convert to MB for display
            },
          ),
        });
        return Upload.LIST_IGNORE;
      }
      return true;
    },
    // 自定义的上传接口
    customRequest: ({ file, onSuccess }) => {
      const fmData = new FormData();
      fmData.append('file', file);
      dispatch({
        type: 'worktable/queryUploadPicture',
        payload: fmData,
        callback: response => {
          if (200 === response.code) {
            setCompanyLogo(response.data.url);
            // 设置表单的companyLogo字段值
            formRef.current.setFieldsValue({
              companyLogo: response.data.url,
            });
            onSuccess(response.data, file);
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    },
    // 文件上传中、上传成功、上传失败 都会回调这个函数
    onChange: ({ fileList: newFileList }) => {
      setFileList(newFileList);
    },
    onPreview: async file => {
      if (!file.url && !file.preview) {
        file.preview = await getBase64(file.originFileObj);
      }
      setPreviewImage(file.url || file.preview);
      setPreviewOpen(true);
      setPreviewTitle(
        file.name || file.url.substring(file.url.lastIndexOf('/') + 1),
      );
    },
    onDrop(e) {
      console.log('Dropped files', e.dataTransfer.files);
    },
  };

  return (
    <Spin spinning={loading}>
      <div className={styles.companyInfoContainer}>
        <div className={styles.titleContainer}>
          <Link to={'/settingTicket'}>
            <span>{LeftReturnArrow()}</span>
          </Link>
          <p>
            <FormattedMessage
              id="ticket.setting.menu.5.1"
              defaultValue="公司基本信息"
            />
          </p>
        </div>
        <div className={styles.formContainer}>
          <Form
            name="basic"
            onFinish={onFinish}
            autoComplete="off"
            layout="vertical"
            ref={formRef}
          >
            <Form.Item
              name="companyName"
              label={
                <FormattedMessage
                  id="ticket.setting.menu.company.name"
                  defaultValue="公司名称："
                />
              }
              required
              disabled
              rules={[
                {
                  required: true,
                  message: getIntl().formatMessage({
                    id: 'ticket.setting.menu.company.name.placeholder',
                    defaultValue: '请输入公司名称',
                  }),
                },
              ]}
            >
              <Input
                placeholder={getIntl().formatMessage({
                  id: 'ticket.setting.menu.company.name.placeholder',
                  defaultValue: '请输入公司名称',
                })}
              />
            </Form.Item>
            <Form.Item
              name="companyAddress"
              label={
                <FormattedMessage
                  id="ticket.setting.menu.company.address"
                  defaultValue="公司地址："
                />
              }
              rules={[
                {
                  required: true,
                  message: getIntl().formatMessage({
                    id: 'ticket.setting.menu.company.address.placeholder',
                    defaultValue: '请输入公司地址',
                  }),
                },
              ]}
            >
              <Input
                placeholder={getIntl().formatMessage({
                  id: 'ticket.setting.menu.company.address.placeholder',
                  defaultValue: '请输入公司地址',
                })}
              />
            </Form.Item>
            <Form.Item
              label={getIntl().formatMessage({
                id: 'chat.channel.configuration.chat2.logo',
                defaultValue: '公司Logo',
              })}
              name="companyLogo"
              required
              rules={[
                {
                  required: true,
                  message: '请上传公司Logo',
                },
              ]}
            >
              <p className={`${styles.fs12} ${styles.color999}`}>
                <FormattedMessage
                  id="chat.channel.configuration.chat2.logo.message1"
                  defaultMessage="只能上传jpg/png文件，且不超过500kb"
                />
              </p>

              <Upload
                name="avatar"
                listType="picture-card"
                className="avatar-uploader"
                fileList={fileList}
                {...uploadProps}
              >
                {
                  <div>
                    <PlusOutlined />
                    <div
                      style={{
                        marginTop: 8,
                      }}
                    >
                      Upload
                    </div>
                  </div>
                }
              </Upload>
            </Form.Item>

            <Form.Item
              style={{ position: 'absolute', top: '0px', right: '0px' }}
            >
              <Link to={'/settingTicket'}>
                <Button onClick={handleCancel} style={{ marginRight: '10px' }}>
                  <FormattedMessage
                    id="awsAccountSetting.cancel.btn"
                    defaultMessage="取消"
                  />
                </Button>
              </Link>
              <Button type={'primary'} htmlType="submit" loading={loadingBtn}>
                <FormattedMessage
                  id="work.order.saved.btn"
                  defaultMessage="保存"
                />
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
    </Spin>
  );
};

export default CompanyInfo;
