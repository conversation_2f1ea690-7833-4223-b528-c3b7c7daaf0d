.companyInfoContainer {
  margin: 24px;
  position: relative;

  .titleContainer {
    width: 100%;
    height: 40px;
    margin-bottom: 10px;

    span {
      cursor: pointer;
      margin-right: 10px;
      float: left;
      width: 25px;
    }

    p {
      color: #333;
      font-size: 18px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%;
      /* 27px */
      float: left;
      margin-bottom: 0px;
    }
  }

  .formContainer {
    width: 70%;
  }

  :global {
    .ant-input {
      border-radius: 6px;
      border: 1px solid #e6e6e6;
      height: 32px;
    }

    .ant-upload-list-picture-card-container {
      background-color: #f0f0f0;
    }

    .ant-upload.ant-upload-select-picture-card {
      background-color: #f0f0f0;
    }
  }

  .fs12 {
    font-size: 12px;
  }

  .color999 {
    color: #999;
  }
}
