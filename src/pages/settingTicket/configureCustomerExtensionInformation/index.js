import React, { Component } from 'react';
import { connect, getIntl, FormattedMessage } from 'umi';
import styles from './index.less';
import TableComponents from '@/components/table/index';
import CustomModal from '@/components/customModal/index';
import { PlusOutlined } from '@ant-design/icons';
import { EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { Popconfirm } from 'antd';
import HOCAuth from '@/components/HOCAuth/index';
import { Delete } from '../autoCloseSetting/icon';

class configureCustomerExtensionInformation extends Component {
  childListRef = React.createRef();
  childModalRef = React.createRef();
  constructor(props) {
    super(props);
    this.state = {
      openModal: false,
      updateId: '',
      languageData: {
        title: 'ticketOrCustomerSettings.config.title.3',
      },
      propsData: {
        btnShow: true,
        key: 'customerExtDefId',
        isBack: true,
        isBackBtnShow: true,
        btnList: [
          {
            type: 'primary',
            onClick: () => {
              this.setState({ openModal: true });
            },
            icons: <PlusOutlined />,
            buttonLang: 'customer.ext.info.add',
            authKey: 'add_customer_expansion_fields',
          },
        ],
        rowSelection: false,
        loading: false,
        language: '',
        interface: 'customerExt/listCustomerExt',
        sortInterfaceName: 'customerExt/updateCustomerExtSort',
        columns: [
          {
            key: 'sort',
          },
          // 属性名称
          {
            align: 'center',
            title: getIntl().formatMessage({ id: 'customer.ext.info.name' }),
            dataIndex: 'customerExtDefName',
            key: 'customerExtDefName',
          },
          // 属性编码
          {
            align: 'center',
            title: getIntl().formatMessage({ id: 'customer.ext.info.code' }),
            dataIndex: 'customerExtDefCode',
            key: 'customerExtDefCode',
          },
          // 属性类型
          {
            align: 'center',
            title: getIntl().formatMessage({
              id: 'customer.ext.info.type',
            }),
            dataIndex: 'propertyTypeName',
            key: 'propertyTypeName',
            // render: value => (
            //   <div>
            //     <span>
            //       {props.propertyTypeList.map(item => {
            //         if (item.value === value) {
            //           return item.label;
            //         }
            //       })}
            //     </span>
            //   </div>
            // ),
          },
          // 是否必填
          {
            align: 'center',
            title: getIntl().formatMessage({ id: 'customer.ext.info.require' }),
            dataIndex: 'isRequired',
            key: 'isRequired',
            render: value => {
              return (
                <span>
                  {value === 1 ? (
                    <FormattedMessage id="user.management.operation.btn.yes" />
                  ) : (
                    <FormattedMessage id="user.management.operation.btn.no" />
                  )}
                </span>
              );
            },
          },
          // 是否多选
          {
            align: 'center',
            title: getIntl().formatMessage({
              id: 'customer.ext.info.multiple',
            }),
            dataIndex: 'status',
            key: 'status',
            render: value => {
              let status =
                value === 0
                  ? getIntl().formatMessage({
                      id: 'user.management.operation.forbidden',
                    })
                  : value === 1
                  ? getIntl().formatMessage({
                      id: 'user.management.operation.normal',
                    })
                  : value === 2
                  ? getIntl().formatMessage({
                      id: 'user.management.operation.noApply',
                    })
                  : '';
              return (
                <div>
                  <span>{status}</span>
                </div>
              );
            },
          },
          // 提示
          {
            align: 'center',
            title: getIntl().formatMessage({
              id: 'customer.ext.info.tips',
            }),
            dataIndex: 'prompt',
            key: 'prompt',
          },
          // 操作
          {
            align: 'center',
            title: getIntl().formatMessage({ id: 'customer.ext.info.operate' }),
            dataIndex: 'address',
            key: 'address',
            minWidth: 120,
            render: (value, data) => (
              <div>
                {data.isSystemDefault === 1 ? (
                  <div>--</div>
                ) : (
                  <div style={{ display: 'inline-flex', alignItems: 'center' }}>
                    {/* 修改 */}
                    <HOCAuth authKey={'modify_customer_expansion_fields'}>
                      {authAccess => (
                        <span
                          onClick={() =>
                            this.updateUserShow(data.customerExtDefId)
                          }
                          style={{
                            marginRight: '8px',
                            display: 'flex',
                            alignItems: 'center',
                            color: '#3463FC',
                            marginBottom: 0,
                            cursor: 'pointer',
                          }}
                          className={authAccess && 'disabled'}
                        >
                          <EditOutlined
                            style={{
                              color: '#3463FC',
                              marginRight: '4px',
                            }}
                          />
                          <FormattedMessage id="user.management.operation.update" />
                        </span>
                      )}
                    </HOCAuth>
                    {/* 删除 */}
                    <HOCAuth authKey={'delete_customer_expansion_fields'}>
                      {authAccess => (
                        <Popconfirm
                          // title="返回将清空表单，确定返回么？"
                          title={getIntl().formatMessage({
                            id: 'customer.ext.popconfirm.delete',
                            defaultValue: '确认删除?',
                          })}
                          onConfirm={() =>
                            this.deleteUserShow(data.customerExtDefId)
                          }
                          style={{ marginRight: '20px' }}
                          disabled={authAccess}
                        >
                          <span
                            className={authAccess && 'disabled'}
                            style={{
                              marginRight: '8px',
                              display: 'flex',
                              alignItems: 'center',
                              color: '#F22417',
                              marginBottom: 0,
                              cursor: 'pointer',
                            }}
                          >
                            <span
                              style={{ marginRight: '4px', marginTop: '4px' }}
                              className={authAccess && 'disabled'}
                            >
                              {Delete()}
                            </span>
                            <FormattedMessage id="delete.channel" />
                          </span>
                        </Popconfirm>
                      )}
                    </HOCAuth>
                  </div>
                )}
              </div>
            ),
          },
        ],
      },
    };
  }
  componentDidMount() {
    this.setState({
      language: localStorage.getItem('lang'),
    });
  }
  //调用子组件刷新列表
  refreshList = () => {
    this.childListRef.current.queryListRef();
  };
  // 修改数据，回显
  updateUserShow = id => {
    this.childModalRef.current.updateModelData(
      'customerExt/queryCustomerExtById',
      { customerExtDefId: id },
    );
    this.setState({ updateId: id });
    setTimeout(() => {
      this.changeSetOpen(true);
    }, 500);
  };
  /**
   * 删除表格数据
   */
  deleteUserShow = id => {
    this.childListRef.current.deleteList('customerExt/deleteCustomerExt', {
      customerExtDefId: id,
      languageCode: this.state.language,
    });
  };
  /**
   * 获取语言
   */
  getLanguage = val => {
    this.setState({ language: val });
  };
  /**
   * 控制弹窗
   */
  changeSetOpen = val => {
    this.setState({ openModal: val });
  };
  render() {
    const { languageData, propsData, openModal } = this.state;
    return (
      <div>
        <CustomModal
          title={getIntl().formatMessage({
            id: 'customer.ext.info.modal.title',
          })}
          open={openModal}
          formName={{
            name: 'customerExtDefName',
            code: 'customerExtDefCode',
            id: 'customerExtDefId',
          }}
          optionList="optionDefList"
          refreshList={this.refreshList}
          changeSetOpen={this.changeSetOpen}
          interface="customerExt/addCustomerExt"
          ref={this.childModalRef}
          updateId={this.state.updateId}
          updateInterface="customerExt/updateCustomerExt"
          language={this.state.language}
        />
        <TableComponents
          languageData={languageData}
          props={propsData}
          ref={this.childListRef}
          getLanguage={this.getLanguage}
          AgentContenClass={styles.agentContenClass}
          HeadContentClass={styles.headContentClass}
          AgentManagementContentClass={styles.agentManagementContentClass}
        />
      </div>
    );
  }
}

const mapStateToProps = ({ customerExt }) => {
  return {
    ...customerExt,
  };
};

const mapDispatchToProps = {};

export default connect(
  mapStateToProps,
  mapDispatchToProps,
)(configureCustomerExtensionInformation);
