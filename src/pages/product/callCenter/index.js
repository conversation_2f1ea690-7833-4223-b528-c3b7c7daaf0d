import Banner from '../components/banner';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import TocCardBg from '@/components/TocCardBg';
import styles from '../index.less';
import currentStyle from './index.less';
import { FormattedMessage } from 'umi';
import MotionFade from '@/components/MotionFade';
import { motion } from 'framer-motion';

const CallCenter = () => {
  const renderCard4 = () => (
    <div className={currentStyle.callCenterCard4}>
      <MotionFade
        as={motion.img}
        type="fadeRight"
        src={require('@/assets/product/callCenter4.png')}
        translateX="25%"
        scale={0.5}
        alt=""
      />
      <MotionFade
        as={motion.img}
        type="fadeLeft"
        delay={0.1}
        src={require('@/assets/product/callCenter41.png')}
        alt=""
      />
      <MotionFade
        as={motion.img}
        type="fadeLeft"
        delay={0.15}
        src={require('@/assets/product/callCenter42.png')}
        alt=""
      />
      <MotionFade
        as={motion.img}
        type="fadeLeft"
        delay={0.2}
        src={require('@/assets/product/callCenter43.png')}
        alt=""
      />
    </div>
  );

  return (
    <div>
      <Header />
      <Banner
        titleFirstId="product.callCenter.title.first"
        titleSecondId="product.callCenter.title.second"
        subtitleId="product.callCenter.subtitle"
        image={require('@/assets/product/callCenter.png')}
        bgColor="#FFF5EC"
        videoSrc={`https://connectnowai.com/video/AI-Call-Center.mp4`}
      />
      <div className={styles.productSubpageContent}>
        <MotionFade
          as={motion.div}
          type="fadeUp"
          className={styles.productSubpageContentTitle}
        >
          <FormattedMessage id="product.callCenter.content.title" />
        </MotionFade>
        <MotionFade
          as={motion.div}
          type="fadeUp"
          delay={0.1}
          className={styles.productSubpageContentDesc}
        >
          <FormattedMessage id="product.callCenter.content.desc" />
        </MotionFade>
      </div>
      <TocCardBg
        title={<FormattedMessage id="product.callCenter.card1.title" />}
        desc={<FormattedMessage id="product.callCenter.card1.desc" />}
        children={
          <MotionFade
            as={motion.img}
            type="fadeRight"
            delay={0.2}
            style={{ width: '100%' }}
            src={require('@/assets/product/callCenter1.png')}
            alt=""
          />
        }
      />
      <TocCardBg
        title={<FormattedMessage id="product.callCenter.card2.title" />}
        desc={<FormattedMessage id="product.callCenter.card2.desc" />}
        children={
          <MotionFade
            as={motion.img}
            type="fadeLeft"
            delay={0.2}
            style={{ width: '100%' }}
            src={require('@/assets/product/callCenter2.png')}
            alt=""
          />
        }
        bgColor="#FFF5EC"
      />
      <TocCardBg
        title={<FormattedMessage id="product.callCenter.card3.title" />}
        desc={<FormattedMessage id="product.callCenter.card3.desc" />}
        children={
          <MotionFade
            as={motion.img}
            type="fadeRight"
            delay={0.2}
            style={{ width: '100%' }}
            src={require('@/assets/product/callCenter3.png')}
            alt=""
          />
        }
      />
      <TocCardBg
        title={<FormattedMessage id="product.callCenter.card4.title" />}
        desc={<FormattedMessage id="product.callCenter.card4.desc" />}
        bgColor="#FFF5EC"
        children={renderCard4()}
      />
      <Footer />
    </div>
  );
};

export default CallCenter;
