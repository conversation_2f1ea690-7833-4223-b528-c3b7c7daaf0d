import Banner from '../components/banner';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import TocCardBg from '@/components/TocCardBg';
import { FormattedMessage } from 'umi';
import styles from '../index.less';
import MotionFade from '@/components/MotionFade';
import { motion } from 'framer-motion';

const AiAgent = () => {
  return (
    <div>
      <Header />
      <Banner
        titleFirstId="product.aiAgent.banner.title.first"
        titleSecondId="product.aiAgent.banner.title.second"
        subtitleId="product.aiAgent.banner.subtitle"
        image={require('@/assets/product/agent.png')}
        bgColor="#f9eefd"
      />
      <div className={styles.productSubpageContent}>
        <MotionFade
          as={motion.div}
          type="fadeUp"
          className={styles.productSubpageContentTitle}
        >
          <FormattedMessage id="product.aiAgent.content.title" />
        </MotionFade>
        <MotionFade
          as={motion.div}
          type="fadeUp"
          delay={0.1}
          className={styles.productSubpageContentDesc}
        >
          <FormattedMessage id="product.aiAgent.content.desc" />
        </MotionFade>
      </div>
      <TocCardBg
        titleId="product.aiAgent.card1.title"
        descId="product.aiAgent.card1.desc"
        children={
          <MotionFade
            as={motion.img}
            type="fadeRight"
            style={{ width: '100%' }}
            src={require('@/assets/product/agent1.png')}
            alt=""
          />
        }
      />
      <TocCardBg
        titleId="product.aiAgent.card2.title"
        descId="product.aiAgent.card2.desc"
        children={
          <MotionFade
            as={motion.img}
            type="fadeLeft"
            delay={0.2}
            style={{ width: '100%' }}
            src={require('@/assets/product/agent2.png')}
            alt=""
          />
        }
        bgColor="#f9eefd"
      />
      <TocCardBg
        titleId="product.aiAgent.card3.title"
        descId="product.aiAgent.card3.desc"
        children={
          <MotionFade
            as={motion.img}
            type="fadeRight"
            delay={0.2}
            style={{ width: '100%' }}
            src={require('@/assets/product/agent3.png')}
            alt=""
          />
        }
      />
      <Footer />
    </div>
  );
};

export default AiAgent;
