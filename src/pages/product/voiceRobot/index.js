import Banner from '../components/banner';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import TocCardBg from '@/components/TocCardBg';
import styles from '../index.less';
import currentStyle from './index.less';
import { FormattedMessage } from 'umi';
import MotionFade from '@/components/MotionFade';
import { motion } from 'framer-motion';

const VoiceRobot = () => {
  const renderCard2 = () => (
    <div className={currentStyle.voiceRobotCard2}>
      <img src={require('@/assets/product/aiyuyin2.png')} alt="" />
      <MotionFade
        as={motion.img}
        type="fadeRight"
        delay={0.2}
        src={require('@/assets/product/aiyuyin21.png')}
        alt=""
      />
    </div>
  );
  const renderCard4 = () => (
    <div className={currentStyle.voiceRobotCard4}>
      <img src={require('@/assets/product/aiyuyin4.png')} alt="" />
      <MotionFade
        as={motion.img}
        type="fadeRight"
        delay={0.15}
        src={require('@/assets/product/aiyuyin41.png')}
        alt=""
      />
      <MotionFade
        as={motion.img}
        type="fadeRight"
        delay={0.2}
        src={require('@/assets/product/aiyuyin42.png')}
        alt=""
      />
    </div>
  );
  return (
    <div>
      <Header />
      <Banner
        titleFirstId="product.voiceRobot.banner.title.first"
        titleSecondId="product.voiceRobot.banner.title.second"
        subtitleId="product.voiceRobot.banner.subtitle"
        image={require('@/assets/product/aiyuyin.png')}
        bgColor="#efffec"
        videoSrc={`https://connectnowai.com/video/AI-Phone-Bot-Horizontal-Version.mp4`}
      />
      <div className={styles.productSubpageContent}>
        <MotionFade
          as={motion.div}
          type="fadeUp"
          className={styles.productSubpageContentTitle}
        >
          <FormattedMessage id="product.voiceRobot.content.title" />
        </MotionFade>
        <MotionFade
          as={motion.div}
          type="fadeUp"
          delay={0.1}
          className={styles.productSubpageContentDesc}
        >
          <FormattedMessage id="product.voiceRobot.content.desc" />
        </MotionFade>
      </div>
      <TocCardBg
        titleId="product.voiceRobot.card1.title"
        descId="product.voiceRobot.card1.desc"
        children={
          <MotionFade
            as={motion.img}
            type="fadeRight"
            delay={0.2}
            style={{ width: '100%' }}
            src={require('@/assets/product/aiyuyin1.png')}
            alt=""
          />
        }
      />
      <TocCardBg
        titleId="product.voiceRobot.card2.title"
        descId="product.voiceRobot.card2.desc"
        bgColor="#efffec"
        children={renderCard2()}
      />
      <TocCardBg
        titleId="product.voiceRobot.card3.title"
        descId="product.voiceRobot.card3.desc"
        children={
          <MotionFade
            as={motion.img}
            type="fadeLeft"
            delay={0.2}
            style={{ width: '100%' }}
            src={require('@/assets/product/aiyuyin3.png')}
            alt=""
          />
        }
      />
      <TocCardBg
        titleId="product.voiceRobot.card4.title"
        descId="product.voiceRobot.card4.desc"
        bgColor="#efffec"
        children={renderCard4()}
      />
      <Footer />
    </div>
  );
};

export default VoiceRobot;
