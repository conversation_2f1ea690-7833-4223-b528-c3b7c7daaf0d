import React, {
  useState,
  useMemo,
  useCallback,
  useEffect,
  useRef,
} from 'react';
import styles from './index.less';
import {
  Input,
  Button,
  Table,
  Tooltip,
  Modal,
  notification,
  Select,
  Skeleton,
  Popconfirm,
  Spin,
  Tag,
  Form,
  Upload,
  Space,
  Popover,
  Checkbox,
} from 'antd';
import {
  UserDeleteOutlined,
  PlusOutlined,
  DeleteOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DownOutlined,
  RightOutlined,
} from '@ant-design/icons';
import { ReactComponent as Search } from '../../assets/Search.svg';
import DeleteTableImg from '../../assets/delete-table-knowledge.png';
import HOCAuth from '@/components/HOCAuth/index';

import agent from '../../assets/agent.svg';
import user from '../../assets/AIGCAva.svg';
import EditorIcon from '../../assets/edit12.png';
import NewReviewIcon from '../../assets/new-review-icon.svg';
import ReactMarkdown from 'react-markdown';
import { read, utils } from 'xlsx';

import {
  connect,
  useDispatch,
  useSelector,
  getIntl,
  FormattedMessage,
  history,
} from 'umi';

import {
  ImportIcon,
  AiBotIcon,
  ContractIcon,
  ExpandIcon,
  MoreIcon,
  DeleteIcon,
  DisableEditorIcon,
} from './icon';
import UploadIcon from '../../assets/upload.svg';
import FileIcon from '../../assets/file-text.svg';
import * as XLSX from 'xlsx';
import moment from 'moment/moment';
import { FormattedHTMLMessage } from '../../.umi/plugin-locale/localeExports';

const { Option, OptGroup } = Select;

export default () => {
  const dispatch = useDispatch();
  const formRef = useRef();
  const { user, languageListR } = useSelector(({ layouts, knowledgeQA }) => ({
    user: layouts.user,
    languageListR: knowledgeQA.languageList,
  }));
  let [loading, setLoading] = useState(false);
  let [modelLoading, setModelLoading] = useState(false);
  let [rows, setRows] = useState([]);
  let [userData, setUserData] = useState([]);
  let [total, setTotal] = useState(0);
  let [question, setQuestion] = useState('');
  let [isModalOpen, setIsModalOpen] = useState(false);
  let [pageNum, setPageNum] = useState(1);
  let [pageSize, setPageSize] = useState(10);
  let [languageList, setLanguageList] = useState([]);
  let [language, setLanguage] = useState([]);
  let [selectedRowKeysModal, setSelectedRowKeysModal] = useState([]);
  let [selectedRowKeys, setSelectedRowKeys] = useState([]);
  let [selectedRowData, setSelectedRowData] = useState([]);
  let [viewQNA, setViewQNA] = useState({});
  let [opFlag, setOpFlag] = useState(true);
  // 显示批量导入弹窗
  let [batchImportModal, setBatchImportModal] = useState(false);
  let [batchImportLoading, setBatchImportLoading] = useState(false);
  const [standardTagList, setStandardTasList] = useState([]); // 客户标签输入框渲染
  const [isModalTranslateOpen, setIsModalTranslateOpen] = useState(false); //智能翻译弹窗
  const [loadingBtn, setLoadingBtn] = useState(false); //保存智能翻译loading
  const [tagValue, setTagValue] = useState([]); //选中知识标签值查询
  const [tagValueModal, setTagValueModal] = useState([]); //批量导入选中知识标签值
  const [fileList, setFileList] = useState([]); //批量上传列表
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [allExpanded, setAllExpanded] = useState(false);
  const [allKeysLength, setAllKeysLength] = useState(0);
  const [isOpenBatchDelete, setIsOpenBatchDelete] = useState(false);
  const [localLanguage, setLocalLanguage] = useState('');
  const [checkedValues, setCheckedValues] = useState([]); //知识有效性值
  const [translationStatus, setTranslationStatus] = useState(''); //翻译状态
  const translationStatusList = [
    {
      key: 0,
      value: 0,
      label: getIntl().formatMessage({
        id: 'knowledge.Q&A.translation.status.1',
        defaultValue: '未翻译',
      }),
    },
    {
      key: 1,
      value: 1,
      label: getIntl().formatMessage({
        id: 'knowledge.Q&A.translation.status.2',
        defaultValue: '翻译中',
      }),
    },
    {
      key: 2,
      value: 2,
      label: getIntl().formatMessage({
        id: 'knowledge.Q&A.translation.status.3',
        defaultValue: '已翻译',
      }),
    },
    {
      key: 3,
      value: 3,
      label: getIntl().formatMessage({
        id: 'knowledge.Q&A.translation.status.4',
        defaultValue: '翻译失败',
      }),
    },
  ];

  const googleLanguage = JSON.parse(localStorage.getItem('languageLocal'));
  useEffect(() => {
    getLanguageList();
    queryAllStandardTag();
    let languageNew = localStorage.getItem('lang');
    if (languageNew) {
      setLocalLanguage(languageNew);
    } else {
      setLocalLanguage('en-US');
    }
  }, []);
  useEffect(() => {
    if (user.roleList && ['1003'].includes(user.roleList[0]?.roleId)) {
      setOpFlag(false);
    }
    queryAnswerKnowledge();
  }, [
    question,
    pageNum,
    pageSize,
    language,
    tagValue,
    checkedValues,
    translationStatus,
  ]);

  /**
   * 获取列表
   */
  const queryAnswerKnowledge = useCallback(
    values => {
      setLoading(true);
      setRows([]);
      setTotal(0);
      setTimeout(() => {
        dispatch({
          type: 'knowledgeQA/queryAnswerKnowledge',
          payload: {
            languageList: language,
            tagIdList: tagValue,
            questions: question,
            pageNum,
            pageSize,
            isCurrentTimeInRange:
              checkedValues.length > 0 ? checkedValues.join(',') : '',
            translationStatus: translationStatus,
          },
          callback: response => {
            let { code, data, msg } = response;
            if (200 === code) {
              if (data) {
                let { records, total } = data;
                const newData = records.map(item => ({
                  ...item,
                  key: item.groupQid, // 添加 key 字段，并使用 qid 作为唯一值
                  // ...(item.translateLanguageList?.length > 0 && { children: item.translateLanguageList })
                  // ...((item.translateLanguageList?.length === 0 || !item.translateLanguageList) && { translateLanguageList: [] })
                }));
                const allKeys = newData
                  .filter(item => item.translateLanguageList?.length > 0)
                  .map(item => item.key);
                setAllKeysLength(allKeys?.length);
                setRows(newData);
                setTotal(total);
              } else {
                setRows([]);
                setTotal(0);
              }
              setLoading(false);
            } else {
              notification.error({
                message: response.msg,
              });
            }
            setLoading(false);
          },
        });
      }, 1000);
    },
    [
      question,
      pageNum,
      pageSize,
      language,
      tagValue,
      checkedValues,
      translationStatus,
    ],
  );

  /**
   * 语言改变
   */
  const handleLanguageChange = value => {
    setLanguage(value);
    setPageNum(1);
    setRows([]);
    setTotal(0);
  };
  /**语言下拉 */
  const getLanguageList = () => {
    dispatch({
      type: 'personalCenter/listLanguage',
      callback: response => {
        let { code, data, msg } = response;
        if (code === 200) {
          setLanguageList(data);
          let l = localStorage.getItem('lang')?.split('-')?.[0];
          // setLanguage(l);
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };

  // 选择翻译状态、
  const handleTranslationStatusChange = value => {
    setTranslationStatus(value);
    setPageNum(1);
    setRows([]);
    setTotal(0);
  };

  /**
   * 打开弹窗
   * */
  const showModal = useCallback(
    (id, lang) => {
      setIsModalOpen(true);
      setModelLoading(true);
      dispatch({
        type: 'knowledgeQA/answerPreview',
        payload: {
          language: lang,
          qid: id,
        },
        callback: response => {
          let { code, data, msg } = response;
          if (200 === code) {
            setViewQNA({
              ...data,
              userName: getIntl().formatMessage({
                id: 'knowledge.QA.notification.user',
              }),
              agnetName: getIntl().formatMessage({
                id: 'knowledge.QA.notification.system',
              }),
            });
          }
          setModelLoading(false);
        },
      });
    },
    [isModalOpen],
  );
  /**
   * 关闭弹窗
   * */
  const handleCancel = useCallback(() => {
    setIsModalOpen(false);
  }, [isModalOpen, selectedRowKeysModal]);
  /**
   * 跳转知识库
   */
  const historyAddQa = () => {
    history.push('/qnaKnowledgeBaseAdd');
  };
  /**
   * 修改
   */
  const updateTableItem = (id, lang, data) => {
    const state = {
      groupQid: id,
      language: lang,
      translateStatus: false,
      translateBtn: data.translateLanguageList?.length > 0 ? true : false,
    };
    history.push({ pathname: '/qnaKnowledgeBaseAdd', state });
  };
  /**
   * 删除
   */
  const deleteTableItem = id => {
    dispatch({
      type: 'knowledgeQA/deleteAnswerKnowledge',
      payload: {
        groupQidList: id,
      },
      callback: response => {
        let { code, data, msg } = response;
        if (200 === code) {
          // queryAnswerKnowledge();
          setPageNum(1);
          setLanguage([]);
          setTagValue([]);
          setQuestion('');
          setTranslationStatus('');
          setCheckedValues([]);
          setIsOpenBatchDelete(false);
          setBatchImportLoading(false);
          setSelectedRowKeys([]);
          setRows([]);
          notification.success({
            message: getIntl().formatMessage({
              id: 'user.management.remove.success',
            }),
          });
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };

  // 查询客户标签
  const queryAllStandardTag = () => {
    dispatch({
      type: 'knowledgeQA/queryKnowStandardTag',
      callback: response => {
        if (response.code === 200) {
          setStandardTasList(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 显示智能翻译弹窗
  const showTranslateModal = () => {
    console.log('--------selectedRowKeys----------', selectedRowKeys);
    if (selectedRowKeys.length > 0) {
      setIsModalTranslateOpen(true);
    } else {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'work.order.batch.marking.tips',
          defaultValue: '至少选择一行数据！',
        }),
      });
    }
  };
  // 取消智能翻译弹窗
  const handleTranslateCancel = () => {
    setIsModalTranslateOpen(false);
    formRef.current?.resetFields();
  };
  // 保存智能翻译
  const onFinish = values => {
    setLoadingBtn(true);
    let params = {
      groupQid: selectedRowKeys,
      groupData: selectedRowData,
      translateTypeList: values.language,
    };

    dispatch({
      type: 'knowledgeQA/translateLanguage',
      payload: params,
      callback: response => {
        setLoadingBtn(false);
        if (response.code === 200) {
          setIsModalTranslateOpen(false);
          setIsModalTranslateOpen(false);
          formRef.current?.resetFields();
          queryAnswerKnowledge();
          // setPageNum(1);
          // setLanguage([]);
          // setTagValue([]);
          // setQuestion('');
          setSelectedRowKeys([]);
          setSelectedRowData([]);
          setRows([]);
          notification.success({
            message: response.msg,
          });
        } else if (response.code === 1001) {
          setIsModalTranslateOpen(false);
          setIsModalTranslateOpen(false);
          formRef.current?.resetFields();
          queryAnswerKnowledge();
          // setPageNum(1);
          // setLanguage([]);
          // setTagValue([]);
          // setQuestion('');
          setSelectedRowKeys([]);
          setSelectedRowData([]);
          setRows([]);
          notification.warning({
            message: response.msg,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });

    // translateLanguage
  };

  // 切换知识标签
  const handleTagChange = value => {
    setPageNum(1);
    setTagValue(value);
    setTotal(0);
    setRows([]);
  };

  // 批量上传
  const newProps = {
    name: 'file', //取值必须和接口参数中的文件参数名相同： MultipartFile file
    multiple: false,
    showUploadList: true,
    fileList,
    accept: '.xls,.xlsx',
    onRemove: file => {
      const index = fileList.indexOf(file);
      const newFileList = fileList.slice();
      newFileList.splice(index, 1);
      setFileList(newFileList);
    },
    //上传前的校验
    beforeUpload: (file, fileList) => {
      let { size } = file;
      let fileSize = size / 1024 / 1024;
      if (fileSize > 10) {
        notification.error({
          message: getIntl().formatMessage(
            {
              id: 'message.upload.size',
            },
            {
              fileSize: 10,
            },
          ),
        });
        return false;
      }
      const requiredFields = [
        'Language (Required)',
        'Question (Required)',
        'Answer1 (Required)',
      ];

      const fileReader = new FileReader();
      fileReader.readAsArrayBuffer(file);

      // fileReader.onload = async (e) => {
      //   const arrayBuffer = e.target.result;
      //   const workbook = read(arrayBuffer, { type: 'array' });
      //   const sheetName = workbook.SheetNames[0];
      //   const worksheet = workbook.Sheets[sheetName];
      //   const jsonData = utils.sheet_to_json(worksheet);
      //
      //   const hasEmptyFields = jsonData.some(row => {
      //     // 判断行中是否至少有一个字段有内容
      //     const isNonEmptyRow = Object.values(row).some(value => value && value.toString().trim() !== '');
      //
      //     if (isNonEmptyRow) {
      //       // 检查必填字段是否为空
      //       return requiredFields.some(field =>
      //         !row[field] || row[field].toString().trim() === ''
      //       );
      //     }
      //     return false;
      //   });
      //
      //   if (hasEmptyFields) {
      //     notification.error({
      //       message: getIntl().formatMessage({
      //           id: 'knowledge.QA.import.tips',defaultValue:'文件中存在必填字段为空，请检查后重试。'
      //         },),
      //     });
      //     return false; // 阻止文件上传
      //   } else {
      //     setFileList([file]); // 设置文件列表
      //   }
      // };
      setFileList([file]);

      return false;
    },
  };
  // 批量上传选择知识标签
  const handleTagChangeModal = value => {
    setTagValueModal(value);
  };
  // 显示批量导入弹窗
  const showBatchImportModal = () => {
    setBatchImportModal(true);
  };
  // 取消批量导入
  const handleCancelBatchImportModal = () => {
    setBatchImportModal(false);
    setTagValueModal([]);
    setFileList([]);
  };
  // 保存批量导入
  const handleSaveBatchImportModal = () => {
    setBatchImportLoading(true);
    const fmData = new FormData();
    if (fileList.length > 0) {
      fmData.append('file', fileList[0]);
      const json = JSON.stringify(tagValueModal);
      fmData.append('knowledgeTagList', json);
      dispatch({
        type: 'knowledgeQA/batchImportAnswerKnowledge',
        payload: fmData,
        callback: response => {
          setBatchImportLoading(false);
          if (response.code === 200) {
            notification.success({
              message: response.msg,
            });
            setBatchImportModal(false);
            setFileList([]);
            setTagValueModal([]);
            setPageNum(1);
            setLanguage([]);
            setTagValue([]);
            setQuestion('');
            setTranslationStatus('');
            setCheckedValues([]);
            setRows([]);
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    } else {
      setBatchImportLoading(false);
      notification.warning({
        message: getIntl().formatMessage({
          id: 'knowledge.QA.import.file.tips',
          defaultValue: '请上传需要导入的文件！',
        }),
      });
    }
  };

  // 点击翻译的问题跳转详情
  const handleQuestionsClick = record => {
    const state = {
      groupQid: record.groupQid,
      language: record.language,
      translateStatus: true,
      translateBtn: false,
    };
    history.push({ pathname: '/qnaKnowledgeBaseAdd', state });
  };

  // 点击批量删除按钮
  const handleShowBatchDeleteModal = () => {
    if (selectedRowKeys.length > 0) {
      setIsOpenBatchDelete(true);
    } else {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'work.order.batch.marking.tips',
          defaultValue: '至少选择一行数据！',
        }),
      });
    }
  };
  // 取消批量删除
  const handleCancelBatchDeleteModal = () => {
    setIsOpenBatchDelete(false);
    setSelectedRowKeys([]);
    setSelectedRowData([]);
  };
  // 确认批量删除
  const handleSaveBatchDeleteModal = () => {
    setBatchImportLoading(true);
    deleteTableItem(selectedRowKeys.join(','));
  };

  // 选择生效周期性
  const handleChangeEffectivePeriod = checkedValues => {
    console.log('-------checkedValues------------', checkedValues);
    setCheckedValues(checkedValues);
  };

  // 全部展开收缩
  const toggleExpandAll = () => {
    if (expandedKeys.length > 0) {
      setExpandedKeys([]);
    } else {
      setExpandedKeys(
        rows.filter(item => item.translateLanguageList).map(item => item.key),
      );
    }
    setAllExpanded(!allExpanded);
  };

  // 单行数据展开收缩
  const handleExpand = (expanded, record) => {
    const newExpandedKeys = expanded
      ? [...expandedKeys, record.key]
      : expandedKeys.filter(key => key !== record.key);

    setExpandedKeys(newExpandedKeys);
    if (newExpandedKeys.length > 0) {
      setAllExpanded(true);
    } else {
      setAllExpanded(false);
    }

    // 判断当前展开的 keys 是否与所有的可展开 rows 的 keys 一致
    // const allKeys = rows.filter(item => item.translateLanguageList?.length>0).map(item => item.key);
    // setAllExpanded(newExpandedKeys.length === allKeys.length);
  };

  /**
   * 列表表头信息
   */
  const columns = useMemo(
    () => [
      Table.SELECTION_COLUMN,
      Table.EXPAND_COLUMN,
      {
        title: (
          <Space>
            {getIntl().formatMessage({ id: 'knowledge.QA.table.1' })}
            <span
              style={{ cursor: allKeysLength > 0 ? 'pointer' : 'not-allowed' }}
              onClick={allKeysLength > 0 ? toggleExpandAll : null}
            >
              {allExpanded ? <ExpandIcon /> : <ContractIcon />}
            </span>
          </Space>
        ),
        dataIndex: 'questions',
        key: 'questions',
        ellipsis: true,
        width: '20%',
        // fixed: 'left',
      },
      {
        title: getIntl().formatMessage({ id: 'knowledge.QA.table.3' }),
        dataIndex: 'language',
        key: 'language',
        width: '20%',
        // fixed: 'left',
        render: value => {
          let showValue = value
            ? googleLanguage
                ?.map(item => {
                  if (item.value === value) {
                    return item.label;
                  }
                })
                .join('')
            : '';
          return <span>{showValue}</span>;
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'knowledge.Q&A.table.translation.status',
        }),
        dataIndex: 'translateStatus',
        key: 'translateStatus',
        width: '15%',
        render: (text, record) => {
          if (+text === 0) {
            return (
              <span>
                <FormattedMessage id={'knowledge.Q&A.translation.status.1'} />
              </span>
            );
          } else if (+text === 1) {
            return (
              <span className={styles.translateStatus2}>
                <FormattedMessage id={'knowledge.Q&A.translation.status.2'} />
              </span>
            );
          } else if (+text === 3) {
            return (
              <span className={styles.translateStatus3}>
                <FormattedMessage id={'knowledge.Q&A.translation.status.4'} />
              </span>
            );
          } else {
            return (
              <span className={styles.translateStatus4}>
                <FormattedMessage id={'knowledge.Q&A.translation.status.3'} />
              </span>
            );
          }
        },
      },
      {
        title: getIntl().formatMessage({ id: 'knowledge.QA.table.6' }),
        dataIndex: 'knowledgeTagList',
        key: 'knowledgeTagList',
        width: '30%',
        render: (text, record) => {
          if (text?.length > 0) {
            if (text?.length > 1) {
              return (
                <div className={styles.tagContainer}>
                  <Tag
                    className={text[0].tagColorCode || 'colorType1'}
                    style={{
                      marginRight: 3,
                    }}
                  >
                    <span className="tagText">
                      {text[0].tagCategoryName ? text[0].tagCategoryName : '--'}{' '}
                      /{' '}
                    </span>
                    {text[0].tagName ? text[0].tagName : '--'}
                  </Tag>
                  <Popover
                    overlayClassName="knowledgeQAPopover"
                    content={
                      <div>
                        {text?.map(items => {
                          return (
                            <Tag
                              className={items.tagColorCode || 'colorType1'}
                              style={{
                                marginRight: 3,
                              }}
                            >
                              <span className="tagText">
                                {text[0].tagCategoryName
                                  ? text[0].tagCategoryName
                                  : '--'}{' '}
                                /{' '}
                              </span>
                              {items.tagName ? items.tagName : '--'}
                            </Tag>
                          );
                        })}
                      </div>
                    }
                    title={null}
                  >
                    <span className={styles.moreIcon}>{MoreIcon()}</span>
                  </Popover>
                </div>
              );
            } else {
              return (
                <div className={styles.tagContainer}>
                  {text?.map(items => {
                    return (
                      <Tag className={items.tagColorCode || 'colorType1'}>
                        <span className="tagText">
                          {items.tagCategoryName ? items.tagCategoryName : '--'}{' '}
                          /{' '}
                        </span>
                        {items.tagName ? items.tagName : '--'}
                      </Tag>
                    );
                  })}
                </div>
              );
            }
          } else {
            return '--';
          }
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'web.scraping.table.6',
          defaultValue: '是否有效',
        }),
        dataIndex: 'isCurrentTimeInRange',
        key: 'isCurrentTimeInRange',
        width: '10%',
        ellipsis: true,
        render: (text, record) => {
          if (text) {
            return (
              <span className={styles.validText}>
                <FormattedMessage
                  id="web.scraping.table.5.valid"
                  defaultMessage="有效"
                />
              </span>
            );
          } else {
            return (
              <span className={styles.notText}>
                <FormattedMessage
                  id="web.scraping.table.5.not"
                  defaultMessage="无效"
                />
              </span>
            );
          }
        },
      },
      {
        title: getIntl().formatMessage({ id: 'knowledge.QA.table.4' }),
        dataIndex: 'creator',
        key: 'creator',
        width: '10%',
        ellipsis: true,
      },
      {
        title: getIntl().formatMessage({ id: 'knowledge.QA.table.5' }),
        dataIndex: 'createTime',
        key: 'createTime',
        width: '15%',
        ellipsis: true,
      },
      {
        title: getIntl().formatMessage({
          id: 'intelligent.form.filling.table.modify.time',
        }),
        dataIndex: 'updateTime',
        key: 'updateTime',
        width: '15%',
        ellipsis: true,
      },
      {
        title: getIntl().formatMessage({ id: 'knowledge.QA.table.operation' }),
        dataIndex: 'address',
        key: 'address',
        // minWidth: opFlag ? '18%' : '13%',
        width: opFlag ? '22%' : '17%',
        fixed: 'right',
        render: (value, data) => (
          <div className={styles.operationArray}>
            {/* 预览 */}
            <HOCAuth authKey={'preview_knowledge_base'}>
              {authAccess => (
                <div
                  onClick={() => showModal(data.qid, data.language)}
                  className={`${styles.operationText} ${
                    authAccess ? 'disabled' : ''
                  }`}
                >
                  <img src={NewReviewIcon} />
                  <a>
                    <FormattedMessage id="knowledge.QA.table.operation.btn.1" />
                  </a>
                </div>
              )}
            </HOCAuth>
            {/* 修改 */}
            {opFlag ? (
              <HOCAuth authKey={'edit_knowledge_base'}>
                {authAccess => (
                  <div
                    onClick={
                      +data.translateStatus === 1
                        ? null
                        : () =>
                            updateTableItem(data.groupQid, data.language, data)
                    }
                    className={`${styles.operationText} ${
                      authAccess ? 'disabled' : ''
                    }`}
                    style={{
                      cursor:
                        +data.translateStatus === 1 ? 'not-allowed' : 'pointer',
                    }}
                  >
                    {/*<EditOutlined*/}
                    {/*  style={{ color: '#1890ff', marginRight: 4 }}*/}
                    {/*/>*/}
                    {+data.translateStatus === 1 ? (
                      <span>{DisableEditorIcon()}</span>
                    ) : (
                      <img src={EditorIcon} />
                    )}
                    <a
                      style={{
                        color: +data.translateStatus === 1 ? '#999' : '#3463fc',
                        cursor:
                          +data.translateStatus === 1
                            ? 'not-allowed'
                            : 'pointer',
                      }}
                    >
                      <FormattedMessage id="knowledge.QA.table.operation.btn.2" />
                    </a>
                  </div>
                )}
              </HOCAuth>
            ) : (
              ''
            )}
            {/* 删除 */}
            {opFlag ? (
              <HOCAuth authKey={'delete_knowledge_base'}>
                {authAccess => (
                  <div className={styles.operationText}>
                    <Popconfirm
                      title={getIntl().formatMessage({
                        id: 'customer.ext.popconfirm.delete',
                      })}
                      okText={getIntl().formatMessage({
                        id: 'user.management.operation.btn.yes',
                      })}
                      cancelText={getIntl().formatMessage({
                        id: 'user.management.operation.btn.no',
                      })}
                      onConfirm={() => deleteTableItem(data.groupQid)}
                      disabled={authAccess}
                    >
                      <img
                        src={DeleteTableImg}
                        style={{
                          color: '#1890ff',
                          marginRight: 4,
                          width: 12,
                          marginBottom: 4,
                        }}
                        className={authAccess && 'disabled'}
                      />

                      <a className={authAccess && 'disabled'}>
                        <FormattedMessage id="knowledge.QA.table.operation.btn.3" />
                      </a>
                    </Popconfirm>
                  </div>
                )}
              </HOCAuth>
            ) : (
              ''
            )}
          </div>
        ),
      },
    ],
    [loading, pageNum, pageSize, toggleExpandAll, allExpanded],
  );

  const rowSelection = {
    selectedRowKeys,
    getCheckboxProps: record => ({
      style: record.translateLanguageList ? {} : { display: 'none' }, // 隐藏子级的 Checkbox
      disabled: !record.translateLanguageList, // 禁止子数据选中
    }),
    onChange: (selectedRowKeys, selectedRows) => {
      setSelectedRowKeys(selectedRowKeys);
      setSelectedRowData(selectedRows);
    },
  };

  // 选中后tag标签颜色
  const tagRender = props => {
    const { label, value, closable, onClose } = props;
    let resultObj = findTagById(value);
    const onPreventMouseDown = event => {
      event.preventDefault();
      event.stopPropagation();
    };
    if (resultObj) {
      return (
        <Tag
          className={resultObj.tagColorCode || 'colorType1'}
          onMouseDown={onPreventMouseDown}
          closable={closable}
          onClose={onClose}
          style={{
            marginRight: 3,
          }}
        >
          <span className="tagText">{resultObj.categoryContent} / </span>
          {resultObj.tagContent}{' '}
        </Tag>
      );
    }
  };
  const findTagById = tagId => {
    for (let category of standardTagList) {
      for (let tag of category.tagList) {
        if (tag.tagId == tagId) {
          return tag;
        }
      }
    }
    return null; // 如果没有找到匹配的标签，返回 null
  };

  return (
    <div className={styles.contentBox} id="contentBox">
      <div className={styles.contentBoxDiv}>
        <div className={styles.contentBoxDivTitle}>
          <p className="blueBorder">
            <FormattedMessage id="knowledge.QA.title" />
          </p>
          <div className={styles.headContent}>
            <div className={styles.selectContainer}>
              <span>
                <Tooltip
                  title={getIntl().formatMessage({
                    id: 'knowledge.QA.select',
                  })}
                >
                  <label>
                    <FormattedMessage id="knowledge.QA.select" />：
                  </label>
                </Tooltip>
              </span>
              <Select
                placeholder={getIntl().formatMessage({
                  id: 'home.set.language.select',
                  defaultValue: '请选择语言',
                })}
                mode="multiple"
                options={googleLanguage}
                onChange={value => handleLanguageChange(value)}
                value={language}
                showSearch
                allowClear
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) >= 0
                }
              />
              <label>
                <FormattedMessage
                  id="knowledge.QA.select.knowledge.tag"
                  defaultMessage="知识标签："
                />
              </label>
              <Select
                tagRender={tagRender}
                optionLabelProp="label"
                optionFilterProp="children"
                showArrow={false}
                showSearch
                mode="multiple"
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) >= 0
                }
                allowClear
                placeholder={getIntl().formatMessage({
                  id: 'knowledge.QA.select.knowledge.tag.tips',
                  defaultMessage: '请选择知识标签',
                })}
                onChange={value => handleTagChange(value)}
              >
                {standardTagList.map(group => (
                  <OptGroup
                    key={group.categoryId}
                    label={
                      group.categoryContent !== 'private_tag_category_code'
                        ? group.categoryContent
                        : getIntl().formatMessage({
                            id: 'tag.management.tab.private',
                            defaultValue: '私有标签',
                          })
                    }
                  >
                    {group.tagList.map(option => (
                      <Option
                        key={option.tagId}
                        value={option.tagId}
                        label={option.tagContent}
                      >
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                          }}
                        >
                          <div
                            style={{
                              width: '12px',
                              height: '12px',
                              backgroundColor: option.tagColor,
                              marginRight: '4px',
                            }}
                          ></div>
                          <span>{option.tagContent}</span>
                        </div>
                      </Option>
                    ))}
                  </OptGroup>
                ))}
              </Select>
              <Input
                placeholder={getIntl().formatMessage({
                  id: 'knowledge.QA.select.tips',
                })}
                prefix={<Search />}
                onPressEnter={e => {
                  setQuestion(e.target.value);
                  setPageNum(1);
                  // setRows([]);
                  // setTotal(0);
                }}
                onBlur={e => {
                  setQuestion(e.target.value);
                  setPageNum(1);
                  // setRows([]);
                  // setTotal(0);
                }}
                style={{ width: '25%', height: 32 }}
              />
            </div>
            <div className={styles.selectContainer}>
              <span>
                <Tooltip
                  title={getIntl().formatMessage({
                    id: 'knowledge.Q&A.table.translation.status',
                  })}
                >
                  <label>
                    <FormattedMessage id="knowledge.Q&A.table.translation.status" />
                    ：
                  </label>
                </Tooltip>
              </span>
              <Select
                placeholder={getIntl().formatMessage({
                  id: 'knowledge.Q&A.table.translation.status.placeholder',
                  defaultValue: '请选择翻译状态',
                })}
                options={translationStatusList}
                onChange={value => handleTranslationStatusChange(value)}
                value={translationStatus}
                showSearch
                allowClear
                filterOption={(inputValue, option) =>
                  option.label
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) >= 0
                }
              />
              <label style={{ float: 'left' }}>
                <FormattedMessage
                  id="web.scraping.select.effective.period"
                  defaultMessage="知识有效性："
                />
              </label>
              <Checkbox.Group
                onChange={handleChangeEffectivePeriod}
                style={{ float: 'left' }}
              >
                <Checkbox value="1">
                  <FormattedMessage id="web.scraping.table.5.valid" />
                </Checkbox>
                <Checkbox value="0">
                  <FormattedMessage id="web.scraping.table.5.not" />
                </Checkbox>
              </Checkbox.Group>
            </div>
            <div className={styles.btnContainer}>
              {opFlag ? (
                <HOCAuth authKey={'add_knowledge_base'}>
                  {authAccess => (
                    <div>
                      <Button
                        type={'primary'}
                        onClick={historyAddQa}
                        icon={<PlusOutlined />}
                        disabled={authAccess}
                      >
                        <span style={{ marginLeft: 6 }}>
                          <FormattedMessage id="knowledge.QA.button" />
                        </span>
                      </Button>
                    </div>
                  )}
                </HOCAuth>
              ) : (
                ''
              )}
              {opFlag ? (
                <HOCAuth authKey={'translate_language'}>
                  {authAccess => (
                    <div>
                      <Button
                        disabled={authAccess}
                        className={styles.intelligentTranslationBtn}
                        icon={AiBotIcon()}
                        onClick={showTranslateModal}
                      >
                        <FormattedMessage
                          id="AIGC.tabs.4"
                          defaultMessage="智能翻译"
                        />
                      </Button>
                    </div>
                  )}
                </HOCAuth>
              ) : (
                ''
              )}
              <div className={styles.deleteBtn}>
                {opFlag ? (
                  <HOCAuth authKey={'delete_knowledge_base'}>
                    {authAccess => (
                      <Button
                        danger
                        icon={DeleteIcon()}
                        className={authAccess && 'disabled'}
                        onClick={handleShowBatchDeleteModal}
                        disabled={authAccess}
                      >
                        <FormattedMessage
                          id="document.knowledge.base.batch.deletion.btn"
                          defaultMessage="批量删除"
                        />
                      </Button>
                    )}
                  </HOCAuth>
                ) : (
                  ''
                )}
              </div>
              {opFlag ? (
                <HOCAuth authKey={'add_knowledge_base'}>
                  {authAccess => (
                    <div>
                      <Button
                        disabled={authAccess}
                        className={styles.importBtn}
                        icon={ImportIcon()}
                        onClick={showBatchImportModal}
                      >
                        <FormattedMessage
                          id="customerInformation.batchImport"
                          defaultMessage="批量导入"
                        />
                      </Button>
                    </div>
                  )}
                </HOCAuth>
              ) : (
                ''
              )}
            </div>
          </div>
        </div>
        <div className={styles.tableContent}>
          <Table
            loading={loading}
            dataSource={rows}
            columns={columns}
            scroll={{
              x: '2000px',
              // y: 'calc(100vh - 400px)',
            }}
            pagination={{
              total: total,
              showSizeChanger: true,
              current: pageNum,
              pageSize: pageSize,
              pageSizeOptions: [10, 20, 50, 100],
              showTotal: total => (
                <FormattedMessage
                  id="page.total.num"
                  defaultMessage={`共 ${total} 条`}
                  values={{ total }}
                />
              ),
              onChange: (pageNum, pageSize) => {
                setPageNum(pageNum);
                setPageSize(pageSize);
              },
            }}
            rowSelection={rowSelection}
            expandable={{
              rowExpandable: record => record.translateLanguageList?.length > 0,
              expandedRowKeys: expandedKeys,
              onExpand: handleExpand,
              expandedRowRender: record =>
                record.translateLanguageList?.map(child => {
                  let showValue = child.language
                    ? googleLanguage
                        ?.map(item => {
                          if (item.value === child.language) {
                            return item.label;
                          }
                        })
                        .join('')
                    : '';
                  return (
                    <div
                      key={child.key}
                      // style={{
                      //   // padding: '8px 16px',
                      //   paddingLeft:'80px'
                      // }}
                      className={styles.translateContainer}
                    >
                      <div
                        onClick={
                          user.roleList[0]?.roleId === '1003'
                            ? null
                            : () => handleQuestionsClick(child)
                        }
                        className={styles.translateQuestion}
                        style={{
                          color:
                            user.roleList[0]?.roleId === '1003'
                              ? '#333'
                              : '#3463fc',
                          cursor:
                            user.roleList[0]?.roleId === '1003'
                              ? 'initial'
                              : 'pointer',
                        }}
                        title={child.questions || '--'}
                      >
                        {child.questions || '--'}
                      </div>
                      <div className={styles.translateLanguage}>
                        {showValue || '--'}
                      </div>
                    </div>
                  );
                }),
            }}
          />
        </div>
      </div>

      {/*批量删除弹窗*/}
      <Modal
        title={getIntl().formatMessage({
          id: 'document.knowledge.base.batch.deletion.btn',
          defaultValue: '批量删除',
        })}
        open={isOpenBatchDelete}
        onOk={handleSaveBatchDeleteModal}
        onCancel={handleCancelBatchDeleteModal}
        confirmLoading={batchImportLoading}
        className="batchDeleteQAModal"
        maskClosable={false}
        mask={false}
      >
        <p>
          <FormattedMessage
            id="knowledge.Q&A.batch.delete.tips"
            defaultMessage="确认删除所有选中数据？"
          />
        </p>
      </Modal>

      {/*批量导入弹窗*/}
      <Modal
        title={getIntl().formatMessage({
          id: 'customerInformation.modal.batch.import',
          defaultValue: '批量导入',
        })}
        open={batchImportModal}
        onOk={handleSaveBatchImportModal}
        onCancel={handleCancelBatchImportModal}
        confirmLoading={batchImportLoading}
        className="batchImportQAModal"
        maskClosable={false}
        mask={false}
        cancelText={getIntl().formatMessage({
          id: 'awsAccountSetting.cancel.btn',
        })}
        okText={getIntl().formatMessage({
          id: 'work.order.management.btn.save',
        })}
      >
        <label>
          <FormattedMessage
            id="knowledge.QA.modal.select.knowledge.tag"
            defaultMessage="选择知识标签："
          />
        </label>
        <Select
          tagRender={tagRender}
          optionLabelProp="label"
          optionFilterProp="children"
          showArrow={false}
          showSearch
          mode="multiple"
          filterOption={(inputValue, option) =>
            option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >= 0
          }
          allowClear
          placeholder={getIntl().formatMessage({
            id: 'knowledge.QA.select.knowledge.tag.tips',
            defaultMessage: '请选择知识标签',
          })}
          value={tagValueModal ? tagValueModal : null}
          onChange={value => handleTagChangeModal(value)}
        >
          {standardTagList.map(group => (
            <OptGroup
              key={group.categoryId}
              label={
                group.categoryContent !== 'private_tag_category_code'
                  ? group.categoryContent
                  : getIntl().formatMessage({
                      id: 'tag.management.tab.private',
                      defaultValue: '私有标签',
                    })
              }
            >
              {group.tagList.map(option => (
                <Option
                  key={option.tagId}
                  value={option.tagId}
                  label={option.tagContent}
                >
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                    }}
                  >
                    <div
                      style={{
                        width: '12px',
                        height: '12px',
                        backgroundColor: option.tagColor,
                        marginRight: '4px',
                      }}
                    ></div>
                    <span>{option.tagContent}</span>
                  </div>
                </Option>
              ))}
            </OptGroup>
          ))}
        </Select>
        <div className="importContent">
          <Upload {...newProps} maxCount={1}>
            <div className="uploadDiv">
              <img src={UploadIcon} />
              <div className="detailDownload">
                <p>
                  <FormattedHTMLMessage
                    id="customerInformation.upload.btn"
                    defaultMessage="将文件拖拽到此处或点击上传。"
                    values={{
                      CH: '点击上传',
                      US: ' click upload',
                      DE: 'Zum Hochladen klicken',
                      JA: 'アップロードをクリック',
                    }}
                  />
                </p>
                <p className="fileDescription">
                  <FormattedMessage
                    id="customerInformation.upload.file.1"
                    defaultMessage="文件说明："
                  />
                </p>
                <p>
                  <FormattedMessage
                    id="knowledge.QA.upload.file.request"
                    defaultMessage="1. Excel格式支持*.xls，*.xlsx两种格式"
                  />
                </p>
              </div>
            </div>
          </Upload>
          <p className="uploadTips">
            <a
              href={`https://${process.env.DOMAIN_NAME_OVER}/static-icon/template_${localLanguage}/ConnectNow+FAQ+Import+Template.xlsx`}
            >
              <FormattedHTMLMessage
                id="customerInformation.upload.file"
                defaultMessage="仅支持一个文件上传，点击「下载模版」"
                values={{
                  CH: '「下载模版」',
                  US: '"Download Template"',
                  DE: '"Vorlage herunterladen"',
                  JA: '「ダウンロードテンプレート」',
                }}
              />
            </a>
          </p>
        </div>
      </Modal>

      {/*智能翻译弹窗*/}
      <Modal
        title={getIntl().formatMessage({
          id: 'AIGC.tabs.4',
          defaultValue: '智能翻译',
        })}
        open={isModalTranslateOpen}
        onCancel={handleCancel}
        className="IntelligentTranslationModal"
        footer={null}
        mask={false}
        maskClosable={false}
      >
        <Form name="basic" onFinish={onFinish} autoComplete="off" ref={formRef}>
          <Form.Item
            label={null}
            name="language"
            rules={[
              {
                required: true,
                message: getIntl().formatMessage({
                  id: 'home.set.language.select',
                  defaultValue: '请选择语言',
                }),
              },
            ]}
          >
            <Select
              placeholder={getIntl().formatMessage({
                id: 'home.set.language.select',
                defaultValue: '请选择语言',
              })}
              mode="multiple"
              options={googleLanguage}
              showSearch
              filterOption={(inputValue, option) =>
                option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >=
                0
              }
            />
          </Form.Item>

          <Form.Item style={{ textAlign: 'center' }}>
            <Button onClick={handleTranslateCancel}>
              <FormattedMessage
                id="awsAccountSetting.cancel.btn"
                defaultMessage="取消"
              />
            </Button>
            <Button loading={loadingBtn} type="primary" htmlType="submit">
              <FormattedMessage
                id="awsAccountSetting.save.btn"
                defaultMessage="保存"
              />
            </Button>
          </Form.Item>
        </Form>
      </Modal>

      {/*预览问答弹窗*/}
      <Modal
        title={getIntl().formatMessage({
          id: 'knowledge.QA.table.modal.title',
          defaultValue: '预览回答',
        })}
        open={isModalOpen}
        footer={null}
        getContainer={() => document.getElementById('contentBox')}
        onCancel={handleCancel}
      >
        <Spin spinning={modelLoading}>
          <div className="centerDetail">
            <User name={viewQNA.userName} content={viewQNA.question} />
            <Agent name={viewQNA.agnetName} content={viewQNA.answers} />
          </div>
          <div className="footerDetail">
            <Button onClick={handleCancel} style={{ marginRight: 20 }}>
              <FormattedMessage id="work.record.return" defaultMessage="返回" />
            </Button>
            <Button type="primary" onClick={handleCancel}>
              <FormattedMessage
                id="work.order.management.btn.sure"
                defaultMessage="确定"
              />
            </Button>
          </div>
        </Spin>
      </Modal>
    </div>
  );
};
const Agent = props => {
  return (
    <div className={styles.main}>
      <div className={styles.avator}>
        <img src={agent} />
      </div>
      <div className={styles.box}>
        <div className={styles.box_role}>{props.name}</div>
        {props.content?.map((item, index) => {
          return item.atype == '1' ? (
            <div key={index}>
              <div className={styles.box_content}>
                <span>
                  {item.a ? (
                    <ReactMarkdown>{item.a}</ReactMarkdown>
                  ) : (
                    <Skeleton active />
                  )}
                </span>
              </div>
              {item.buttons && item.buttons.length > 0 ? (
                <div className={styles.box_list}>
                  {item.buttons?.map((itemSon, indexSon) => {
                    return (
                      <div key={indexSon} className={styles.box_list_it}>
                        {itemSon.name}
                      </div>
                    );
                  })}
                </div>
              ) : (
                ''
              )}
            </div>
          ) : item.atype == '2' ? (
            <div className={styles.box_content_img} key={index}>
              <img src={item.a} />
              {item.buttons && item.buttons.length > 0 ? (
                <div className={styles.box_list}>
                  {item.buttons?.map((itemSon, indexSon) => {
                    return (
                      <div key={indexSon} className={styles.box_list_it}>
                        {itemSon.name}
                      </div>
                    );
                  })}
                </div>
              ) : (
                ''
              )}
            </div>
          ) : (
            <div className={styles.box_content_img}>
              <video controls>
                <source src={item.a} type="video/mp4" />
              </video>
              {item.buttons && item.buttons.length > 0 ? (
                <div className={styles.box_list}>
                  {item.buttons?.map((itemSon, indexSon) => {
                    return (
                      <div key={indexSon} className={styles.box_list_it}>
                        {itemSon.name}
                      </div>
                    );
                  })}
                </div>
              ) : (
                ''
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};
const User = props => {
  return (
    <div className={styles.userMain}>
      <div className={styles.box}>
        <div className={styles.box_role}>{props.name}</div>

        <div className={styles.box_content}>
          {props.content ? props.content : <Skeleton active />}
        </div>
      </div>
      <div className={styles.avator}>
        <img src={user} />
      </div>
    </div>
  );
};
