import styles from './index.less';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { Link, Element, Events } from 'react-scroll';
import React, { useEffect } from 'react';
import { FormattedMessage, Link as LinkUrl, useIntl, getLocale } from 'umi';
import { isCNDomain } from '@/utils/utils';
import { FormattedHTMLMessage } from '../../../.umi/plugin-locale/localeExports';
const DiscordHelpDocument = () => {
  useEffect(() => {
    Events.scrollEvent.register('begin', (...args) => {
      console.log('begin', args);
    });

    Events.scrollEvent.register('end', (...args) => {
      console.log('end', args);
    });

    return () => {
      Events.scrollEvent.remove('begin');
      Events.scrollEvent.remove('end');
    };
  }, []);
  const intl = useIntl();
  useEffect(() => {}, [intl]);
  const getCampanyName = () => {
    if (isCNDomain()) {
      return '深圳星途启航科技有限公司';
    } else {
      return 'Connectnow AI Technology Co.,Limited ';
    }
  };
  return (
    <div>
      <Header />
      {/* 占位符 */}
      <div className={styles.placeholder}></div>
      <div className={styles.privacyPolicy}>
        <div className={styles.contentWrapper}>
          <div className={styles.nav}>
            <div className={styles.navTitle}>
              <FormattedMessage id="channel.allocation.discord.document.title" />
            </div>
            <ul>
              <li>
                <Link
                  activeClass={styles.active}
                  to="one"
                  spy={true}
                  smooth={true}
                >
                  <FormattedMessage id="guide.section.one.prefix" />
                  <span>
                    <FormattedMessage id="channel.allocation.discord.document.step.1" />
                  </span>
                </Link>
              </li>
              <li>
                <Link
                  activeClass={styles.active}
                  to="two"
                  spy={true}
                  smooth={true}
                >
                  <FormattedMessage id="guide.section.two.prefix" />
                  <span>
                    <FormattedMessage id="channel.allocation.discord.document.step.2" />
                  </span>
                </Link>
              </li>
              <li>
                <Link
                  activeClass={styles.active}
                  to="three"
                  spy={true}
                  smooth={true}
                >
                  <FormattedMessage id="guide.section.three.prefix" />
                  <span>
                    <FormattedMessage id="channel.allocation.discord.document.step.3" />
                  </span>
                </Link>
              </li>
              <li>
                <Link
                  activeClass={styles.active}
                  to="four"
                  spy={true}
                  smooth={true}
                >
                  <FormattedMessage id="guide.section.four.prefix" />
                  <span>
                    <FormattedMessage id="channel.allocation.discord.document.step.4" />
                  </span>
                </Link>
              </li>
            </ul>
          </div>
          <div className={styles.content}>
            <div className={styles.privacyPolicyTitle}>
              <FormattedMessage id="channel.allocation.discord.document.title" />
            </div>
            <h1>
              <FormattedMessage id="channel.allocation.discord.document.h1" />
            </h1>
            <Element name="one" className={styles.element}>
              <h2>
                <FormattedMessage id="guide.section.one.prefix" />
                <FormattedMessage id="channel.allocation.discord.document.step.1" />
              </h2>
              <p>
                <FormattedMessage
                  id="channel.allocation.discord.document.step.1.text"
                  values={{
                    a: chunks => (
                      <a
                        href="https://discord.com/"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {chunks}
                      </a>
                    ),
                  }}
                />
                <br />
                <FormattedMessage id="channel.allocation.discord.document.step.1.text.1" />
              </p>
            </Element>

            <Element name="two" className={styles.element}>
              <h2>
                <FormattedMessage id="guide.section.two.prefix" />
                <FormattedMessage id="channel.allocation.discord.document.step.2" />
              </h2>
              <p>
                <FormattedMessage id="channel.allocation.discord.document.step.2.text" />
                <br />
                <FormattedMessage id="channel.allocation.discord.document.step.2.text.1" />
                <br />
                <img src="https://www.connectnowai.com/doc/discord/92e00344-c89c-4b67-b9f4-3647b43ae73e.jpg" />
                <br />
                <FormattedMessage id="channel.allocation.discord.document.step.2.text.2" />
                <br />
                <img src="https://www.connectnowai.com/doc/discord/61136bfb-597f-460a-8c9a-9c08ca662f75.jpg" />
                <br />
                <FormattedMessage id="channel.allocation.discord.document.step.2.text.3" />
                <br />
                <img src="https://www.connectnowai.com/doc/discord/503ad096-85f1-4c35-9276-a131feec30f2.jpg" />
                <br />
                <FormattedMessage id="channel.allocation.discord.document.step.2.text.4" />
                <br />
                <img src="https://www.connectnowai.com/doc/discord/7b47570c-8384-461f-bf6a-59c01f0edd01.jpg" />
                <br />
              </p>
            </Element>

            <Element name="three" className={styles.element}>
              <h2>
                <FormattedMessage id="guide.section.three.prefix" />
                <FormattedMessage id="channel.allocation.discord.document.step.3" />
              </h2>
              <p>
                <FormattedMessage
                  id="channel.allocation.discord.document.step.3.text"
                  values={{
                    a: chunks => (
                      <a
                        href="https://discord.com/developers/applications"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {chunks}
                      </a>
                    ),
                  }}
                />
                <br />
                <img src="https://www.connectnowai.com/doc/discord/25b10c33-adf7-4585-aa73-9af0f9d706e3.jpg" />
                <br />
                <FormattedMessage id="channel.allocation.discord.document.step.3.text.1" />
                <br />
                <img src="https://www.connectnowai.com/doc/discord/4b1ea28c-1483-4e67-94d3-814f25c40f49.jpg" />
                <br />
                <FormattedMessage id="channel.allocation.discord.document.step.3.text.2" />
                <br />
                <img src="https://www.connectnowai.com/doc/discord/f33d31af-e939-4f97-8530-88e195c11ed3.jpg" />
                <br />
                <FormattedMessage id="channel.allocation.discord.document.step.3.text.3" />
                <br />
                <img src="https://www.connectnowai.com/doc/discord/36fc2242-cb9e-409a-8426-2d5c24cf5613.jpg" />
                <br />
                <FormattedMessage id="channel.allocation.discord.document.step.3.text.4" />
                <br />
                <img src="https://www.connectnowai.com/doc/discord/6cb3ffb4-bc4b-44e0-a9c6-6dfb9778e7bf.jpg" />
                <br />
                <img src="https://www.connectnowai.com/doc/discord/1f54cf4f-845b-4af3-8bec-dd3aef1aff26.jpg" />
                <br />
                <FormattedMessage id="channel.allocation.discord.document.step.3.text.5" />
                <br />
                <img src="https://www.connectnowai.com/doc/discord/00e3f747-c6fd-400b-853e-407f7d78bb4f.jpg" />
                <br />
                <img src="https://www.connectnowai.com/doc/discord/4e80293e-8480-4900-905a-c55b08576534.jpg" />
                <br />
                <FormattedMessage id="channel.allocation.discord.document.step.3.text.6" />
                <br />
                <img src="https://www.connectnowai.com/doc/discord/50244d19-8ca9-48f7-a2ba-546a9d3ea742.jpg" />
                <br />
                <FormattedMessage id="channel.allocation.discord.document.step.3.text.7" />
                <br />
                <img src="https://www.connectnowai.com/doc/discord/332e3b8c-4350-428b-92c7-8e88a3b12169.jpg" />
                <br />
                <img src="https://www.connectnowai.com/doc/discord/21d06339-6b0e-4877-aaa9-1a6c50f71282.jpg" />
                <br />
                <FormattedMessage id="channel.allocation.discord.document.step.3.text.8" />
                <br />
                <img src="https://www.connectnowai.com/doc/discord/ec1e3ea3-d02e-4547-89c0-f4b961c8bd89.jpg" />
                <br />
              </p>
            </Element>

            <Element name="four" className={styles.element}>
              <h2>
                <FormattedMessage id="guide.section.four.prefix" />
                <FormattedMessage id="channel.allocation.discord.document.step.4" />
              </h2>
              <p>
                <FormattedMessage
                  id="channel.allocation.discord.document.step.4.text"
                  values={{
                    a: chunks => (
                      <a
                        href="https://discord.com/developers/applications"
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        {chunks}
                      </a>
                    ),
                  }}
                />
                <br />
                <img src="https://www.connectnowai.com/doc/discord/5ba405f2-2666-44a5-ab87-3c0e64d5b205.jpg" />
                <br />
                <FormattedMessage id="channel.allocation.discord.document.step.4.text.1" />
                <br />
                <img src="https://www.connectnowai.com/doc/discord/62038c4e-fb76-4e41-8bcf-a9a9f28757f3.jpg" />
                <br />
                <FormattedMessage id="channel.allocation.discord.document.step.4.text.6" />
                <br />
                <img src="https://www.connectnowai.com/doc/discord/862b19d6-b06e-4f72-9e30-8a1726af336c.jpg" />
                <br />
                <FormattedMessage id="channel.allocation.discord.document.step.4.text.2" />
                <br />
                <img src="https://www.connectnowai.com/doc/discord/95d4a128-362a-42cd-a51a-c28d43b017c1.jpg" />
                <br />
                <FormattedMessage id="channel.allocation.discord.document.step.4.text.3" />
                <br />
                <img src="https://www.connectnowai.com/doc/discord/6e265b0d-b925-479a-97eb-e00e62a8325e.jpg" />
                <br />
                <FormattedMessage id="channel.allocation.discord.document.step.4.text.4" />
                <br />
                <img src="https://www.connectnowai.com/doc/discord/b9fed758-71ee-498d-92eb-8b183d90bd4d.jpg" />
                <br />
              </p>
              <h2>
                <FormattedMessage id="channel.allocation.discord.document.step.4.text.5" />
              </h2>
            </Element>
            {/* 日期 */}
            {/*<div className={styles.date}>*/}
            {/*  <div className={styles.updateDate}>*/}
            {/*    <FormattedMessage id="guide.updateDate" />*/}
            {/*  </div>*/}
            {/*  <div className={styles.effectiveDate}>*/}
            {/*    <FormattedMessage id="guide.effectiveDate" />*/}
            {/*  </div>*/}
            {/*</div>*/}
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default DiscordHelpDocument;
