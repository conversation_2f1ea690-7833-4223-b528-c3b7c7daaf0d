.placeholder {
  height: calc(64px + 1.625rem);
}

.privacyPolicy {
  max-width: 90%;
  margin: 0 auto;
  padding-bottom: 5rem;

  .privacyPolicyTitle {
    font-family: 'MicrosoftYaHei';
    font-size: 2.5rem;
    font-style: normal;
    font-weight: 700;
    padding-bottom: 15px;
    border-bottom: 0.0625rem solid #e6e6e6;
    color: #333;
    padding: 0 1.25rem;
    margin-bottom: 30px;
  }

  .contentWrapper {
    display: flex;
    gap: 2.5rem;
  }

  .nav {
    width: 19rem;
    position: sticky;
    top: 5rem;
    height: fit-content;

    .navTitle {
      font-size: 1.125rem;
      font-weight: bold;
      margin-bottom: 2.25rem;
      padding: 0.625rem 0.9375rem;
      color: #333;
      border-bottom: 0.0625rem solid #e6e6e6;
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;
      border-left: 0.0625rem solid #e6e6e6;

      li {
        line-height: 2rem;
        padding-left: 1rem;
        display: flex;
        align-items: flex-start;

        a {
          color: #333;
          text-decoration: none;
          font-weight: 400;
          transition: color 0.3s;
          overflow: hidden;
          text-overflow: ellipsis;
          font-family: 'MicrosoftYaHei Regular';
          font-size: 1.125rem;
          font-style: normal;
          display: flex;
          span {
            display: block;
          }
          &:hover {
            color: #1890ff;
          }
        }
      }
    }
  }

  table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;

    th,
    td {
      border: 1px solid #e6e6e6;
      padding: 0.75rem;
      text-align: left;
      font-family: 'MicrosoftYaHei Regular';
      font-size: 1rem;
      line-height: 1.5;
      white-space: pre-wrap;
    }

    th {
      background-color: #f5f5f5;
      font-weight: bold;
    }
  }

  .content {
    flex: 1;
    padding: 0.9375rem 2.5rem 3.125rem 2.5rem;
    border-left: 0.0625rem solid #e6e6e6;

    p {
      color: #333;
      font-family: 'MicrosoftYaHei Regular';
      font-size: 1rem;
      font-style: normal;
      font-weight: 400;
      line-height: 2rem;
      margin-bottom: 10px;
    }

    .section {
      margin-bottom: 2.5rem;

      h2 {
        color: #333;
        font-family: 'MicrosoftYaHei';
        font-size: 1.6875rem;
        font-style: normal;
        font-weight: 700;
        margin-bottom: 10px;
      }

      h3 {
        color: #333;
        font-family: 'MicrosoftYaHei';
        font-size: 1.3875rem;
        font-style: normal;
        font-weight: 700;
        margin-bottom: 10px;
      }

      h4 {
        color: #333;
        font-family: 'MicrosoftYaHei';
        font-size: 1.1875rem;
        font-style: normal;
        font-weight: 700;
        margin-bottom: 10px;
      }

      p {
        color: #333;
        font-family: 'MicrosoftYaHei Regular';
        font-size: 1rem;
        font-style: normal;
        font-weight: 400;
        line-height: 2rem;
        margin-bottom: 10px;
      }
    }

    img {
      max-width: 80%;
      margin-top: 10px;
      margin-bottom: 10px;
    }
  }

  .active {
    color: #3463fc !important;
    font-weight: bold;
  }

  .date {
    display: flex;
    padding: 0.9375rem 0;
    gap: 3.125rem;
    border-top: 1px solid #e6e6e6;
    color: #999;
    font-family: 'MicrosoftYaHei Regular';
    font-size: 1rem;
    font-style: normal;
    font-weight: 400;
  }
}

.element {
  margin-bottom: 20px;
}
.navTwo {
  .navTitle {
    font-size: 1rem;
    font-weight: bold;
    margin-bottom: 20px;
  }

  .mainNav {
    list-style: none;
    padding: 0;
    margin: 0;

    > li {
      margin-bottom: 0.625rem;

      .index {
        width: 24px;
        flex-shrink: 0;
        font-size: 1.125rem;
      }

      .text {
        flex: 1;
        font-size: 1.125rem;
      }
    }

    .subNav {
      list-style: none;
      padding-left: 0;
      margin: 0;

      li {
        margin-top: 8px;

        a {
          display: block;
          text-decoration: none;
          color: #666;
          padding: 4px 0;

          &:hover {
            color: #1890ff;
          }

          .text {
            display: block;
          }
        }
      }
    }
  }

  .active {
    color: #1890ff !important;
    font-weight: 500;
  }
}
.img {
  display: block;
  margin: 0 auto;
  // 阴影
  box-shadow: 0 0 1.5rem 0.2rem rgba(0, 0, 0, 0.2);
  margin-bottom: 1.25rem;
}
.imgText {
  text-align: center;
}
