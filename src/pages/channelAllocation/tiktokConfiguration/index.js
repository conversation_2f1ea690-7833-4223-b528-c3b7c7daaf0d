import React, { Component, useState } from 'react';

import { connect, FormattedMessage, getIntl, history, getLocale } from 'umi';
import {
  Input,
  Button,
  Checkbox,
  Steps,
  Form,
  Col,
  Row,
  Select,
  Radio,
  notification,
  Spin,
  Popover,
  Tag,
  Tooltip,
  Modal,
} from 'antd';

import { convertBoolean, transformObject } from '@/utils/utils';
import styles from './index.less';
import HOCAuth from '@/components/HOCAuth/index';
import TikTokConfirm from '../../../assets/amazon_content2_Confirm.png';
import NewTikTokIcon from '../../../assets/new-tiktok-icon.svg';
import { copy } from '@/utils/utils';
import { ReactComponent as IrelandIcon } from '@/assets/IrelandIcon.svg';
import { ReactComponent as JapanIcon } from '@/assets/JapanIcon.svg';
import { ReactComponent as AustraliaIcon } from '@/assets/AustraliaIcon.svg';
import { ReactComponent as SingaporeIcon } from '@/assets/SingaporeIcon.svg';

import { ReactComponent as UnitedKingdomIcon } from '@/assets/UnitedKingdomIcon.svg';
import { ReactComponent as PolandIcon } from '@/assets/PolandIcon.svg';
import { ReactComponent as TurkeyIcon } from '@/assets/TurkeyIcon.svg';
import { ReactComponent as GermanyIcon } from '@/assets/GermanyIcon.svg';
import { ReactComponent as NetherlandsIcon } from '@/assets/NetherlandsIcon.svg';

import { ReactComponent as UnitedArabEmiratesIcon } from '@/assets/UnitedArabEmiratesIcon.svg';
import { ReactComponent as FranceIcon } from '@/assets/FranceIcon.svg';
import { ReactComponent as SwedenIcon } from '@/assets/SwedenIcon.svg';
import { ReactComponent as SaudiArabiaIcon } from '@/assets/SaudiArabiaIcon.svg';
import { ReactComponent as ItalyIcon } from '@/assets/ItalyIcon.svg';

import { ReactComponent as BelgiumIcon } from '@/assets/BelgiumIcon.svg';
import { ReactComponent as EgyptIcon } from '@/assets/EgyptIcon.svg';
import { ReactComponent as SpainIcon } from '@/assets/SpainIcon.svg';
import { ReactComponent as IndiaIcon } from '@/assets/IndiaIcon.svg';
import { ReactComponent as SouthAfricaIcon } from '@/assets/SouthAfricaIcon.svg';
import { ReactComponent as UnitedStatesIcon } from '@/assets/UnitedStatesIcon.svg';
import { ReactComponent as CanadaIcon } from '@/assets/CanadaIcon.svg';
import { ReactComponent as MexicoIcon } from '@/assets/MexicoIcon.svg';
import { ReactComponent as BrazilIcon } from '@/assets/BrazilIcon.svg';

import { ReactComponent as MalaysiaIcon } from '@/assets/MalaysiaIcon.svg';
import { ReactComponent as IndonesiaIcon } from '@/assets/IndonesiaIcon.svg';
import { ReactComponent as PhilippinesIcon } from '@/assets/PhilippinesIcon.svg';
import { ReactComponent as ThailandIcon } from '@/assets/ThailandIcon.svg';
import { ReactComponent as VietnamIcon } from '@/assets/VietnamIcon.svg';
const { StepItem } = Steps;

class tiktokConfiguration extends Component {
  formAddChannelRef = React.createRef();
  constructor(props) {
    super(props);
    this.state = {
      detailLoading: false,
      tiktokConnectId: '',
      current: 0,
      completedSteps: 0,
      stepStatus: 'wait',
      content1Loading: false,
      content3Loading: false,
      finishData: {},
      agentInitName: '',
      doc_bot: null,
      knowledgeBaseList: [],
      btnLoading: false,
      menuToAgentTxt: '',
      termsTags: [
        'transfer to an agent',
        'agent',
        'to an agent',
        'need agent',
        'agent help',
        'need human',
        'human',
        '座席',
        '人工',
        '转座席',
        '转人工',
        '人工服务',
        '不要机器人',
      ],
      shop_region: '',
      callingLines: '',
      openSelfChat: null,
      tiktokRegionList: [],
      regionNow: {},
      isModalOpen: false,
      workTimeList: [],
      tiktok_shop_Info: {
        shop_id: '', //Shop Id
        shop_name: '', //Shop 名字
        shop_region: '', //Shop 区域
        shop_seller_type: '', //Shop 销售商类型
        shop_cipher: '', //Shop 密码
        shop_code: '', //Shop 代码
      },
      tiktok_auth_code: '',
      tikTokShopList: [],
    };
  }

  componentDidMount() {
    console.log(this.props, history, 'dsksidjudjdhdh');
    //拿到openSelfChat，用来判断是否需要选择实例
    let a = null;
    if (this.props.user.openSelfChat) {
      a = this.props.user.openSelfChat;
    } else {
      a = JSON.parse(sessionStorage.getItem('user'))?.openSelfChat;
    }
    this.setState({
      openSelfChat: a,
    });

    //保存connectid
    let { tiktokConnectId } = this.props || {};
    if (tiktokConnectId) {
      this.setState({
        tiktokConnectId,
      });
    } else {
      let tiktokConnectId1 = history.location.query.id;
      this.setState({
        tiktokConnectId: tiktokConnectId1,
      });
    }
    console.log(history.location.query, 'history.location.query');
    //授权成功后回调直接跳第二部
    if (history.location.query.code) {
      this.setState(
        {
          current: 1,
          completedSteps: 0,
          tiktok_auth_code: history.location.query.code,
          regionNow:
            history.location.query.state &&
            history.location.query.state !== 'undefined'
              ? JSON.parse(history.location.query.state)
                  .tiktokRegionConfiguration
              : {},
        },
        () => {
          this.getTiktokShopInfo();
        },
      );
    }
    //查询文档知识库下拉
    this.queryDocumentKnowledge();
    //工作时间下拉
    this.channelWorkTimeList();

    //调用回显接口
    if (this.props.tiktokChannelId || history.location.query.channelId) {
      this.channelDetails(
        this.props.tiktokChannelId || history.location.query.channelId,
      );
    } else {
      //获取国家列表
      this.queryTikTokMarketRegion();
    }
  }
  componentWillUnmount() {}
  componentDidUpdate(prevProps, prevState) {}
  onChangeSteps = value => {
    let { current, completedSteps } = this.state;
    console.log('++++++', current);
    console.log('++++++', completedSteps);
    console.log('++++++', value);
    console.log('++++', this.formAddChannelRef.current.getFieldsError());
    //修改渠道不允许修改步骤1，2
    if (
      [0, 1].includes(value) &&
      (this.props.tiktokChannelId || history.location.query.channelId)
    ) {
      return;
    }

    //跳步骤时校验是否填了
    let error = false;
    this.formAddChannelRef.current.getFieldsError().forEach(item => {
      if (item.errors.length > 0) {
        error = true;
      }
    });
    //跳步骤时做好保存
    if (completedSteps >= value && !error) {
      let a = this.onFinish(
        this.formAddChannelRef.current.getFieldsValue(),
        'step',
      );
      if (a) {
        this.setState({
          current: value,
        });
      }
    }
  };
  //获取店铺信息
  getTiktokShopInfo = () => {
    this.setState({
      detailLoading: true,
    });
    this.props.dispatch({
      type: 'tiktokRegionConfiguration/queryTiktokShopInfo',
      payload: {
        countryCode: history.location.query.shop_region
          ? history.location.query.shop_region
          : this.state.regionNow.countryCode,
        authCode: history.location.query.code
          ? history.location.query.code
          : this.state.tiktok_auth_code,
      },
      callback: response => {
        this.setState({
          detailLoading: false,
        });
        if (response.code === 200) {
          let shopList = response.data;
          this.setState({
            tikTokShopList: shopList,
          });
          //这里根据shop_region回显
          this.state.tiktokRegionList?.forEach(item => {
            if (item.countryCode === shopList[0]?.shop_region) {
              this.setState({
                regionNow: item,
              });
            }
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  /**
   * 回显
   */
  channelDetails = id => {
    this.setState({
      content1Loading: false,
      detailLoading: true,
    });
    this.props.dispatch({
      type: 'amazonRegionConfiguration/channelDetails',
      payload: { id },
      callback: async response => {
        this.setState({
          detailLoading: false,
        });
        let { code, data, msg } = response;
        if (code === 200) {
          console.log(response);

          let channelName = data.name;
          let callingLines = data.connectId;

          let connectList =
            this.props.connectList?.length > 0
              ? this.props.connectList
              : JSON.parse(localStorage.getItem('connectList'));

          let selectedConnectFindValue = connectList?.find(
            item => JSON.parse(item.value).connectId === callingLines,
          )?.value;

          let config = {};
          data.config.forEach(item => {
            config[item.code] = item.name;
          });
          console.log(config);
          this.setState({
            current: 2,
            completedSteps: 4,
            agentInitName: config.bot_name,
            callingLines: callingLines,
            doc_bot: config.document_knowledge_base_id,
            termsTags: config.menu_to_agent_txt
              ? config.menu_to_agent_txt.split(',')
              : [],
            shop_region: config.shop_region,
            tiktok_auth_code: config.tiktok_auth_code,
            tiktok_shop_Info: {
              shop_id: config.shop_id, //Shop Id
              shop_name: config.shop_name, //Shop 名字
              shop_region: config.shop_region, //Shop 区域
              shop_seller_type: config.shop_seller_type, //Shop 销售商类型
              shop_cipher: config.shop_cipher, //Shop 密码
              shop_code: config.shop_code, //Shop 代码
            },
          });
          //给regionNow赋值
          await this.queryTikTokMarketRegion(config.shop_region);
          console.log(this.state.regionNow);
          let channelDetailList = {
            name: channelName,
            callingLines: selectedConnectFindValue,
            chat_open_channel_type: config.chat_open_channel_type,
            bot_name: config.bot_name,
            menu_to_agent_txt: config.menu_to_agent_txt,
            // auto_language_flag: convertBoolean(config.auto_language_flag),
            doc_bot_flag: convertBoolean(config.doc_bot_flag),
            document_knowledge_base_id: config.document_knowledge_base_id,
            mood_board_flag: config.mood_board_flag,
            knowledge_unknown_reply: config.knowledge_unknown_reply,
            agent_worktime_id: config.agent_worktime_id,
            agent_not_worktime_reply: config.agent_not_worktime_reply,
            is_open_aiagent: convertBoolean(config.is_open_aiagent),
          };
          this.formAddChannelRef.current?.setFieldsValue(channelDetailList);

          this.setState(
            {
              finishData: channelDetailList,
            },
            () => {
              console.log(this.state.finishData);
              // this.queryWhatsAppChannelTypeContact(
              //   this.state.finishData.wabaId,
              // );
            },
          );
        } else {
          notification.warning({
            message: getIntl().formatMessage({
              id: msg,
            }),
          });
        }
      },
    });
  };
  /**
   * 呼叫类型选项
   */
  handleChangeSelectCallingLine = (value, name) => {
    this.setState({
      [name]: JSON.parse(value).connectId,
    });
  };
  /**
   * return 每个地区的图标
   */
  getRegionIcon = e => {
    switch (e) {
      case 'JapanIcon':
        return <JapanIcon />;
      case 'AustraliaIcon':
        return <AustraliaIcon />;
      case 'SingaporeIcon':
        return <SingaporeIcon />;
      case 'IrelandIcon':
        return <IrelandIcon />;
      case 'UnitedKingdomIcon':
        return <UnitedKingdomIcon />;
      case 'PolandIcon':
        return <PolandIcon />;
      case 'TurkeyIcon':
        return <TurkeyIcon />;
      case 'GermanyIcon':
        return <GermanyIcon />;
      case 'NetherlandsIcon':
        return <NetherlandsIcon />;

      case 'UnitedArabEmiratesIcon':
        return <UnitedArabEmiratesIcon />;
      case 'FranceIcon':
        return <FranceIcon />;
      case 'SwedenIcon':
        return <SwedenIcon />;
      case 'SaudiArabiaIcon':
        return <SaudiArabiaIcon />;
      case 'ItalyIcon':
        return <ItalyIcon />;

      case 'BelgiumIcon':
        return <BelgiumIcon />;
      case 'EgyptIcon':
        return <EgyptIcon />;
      case 'SpainIcon':
        return <SpainIcon />;
      case 'IndiaIcon':
        return <IndiaIcon />;
      case 'SouthAfricaIcon':
        return <SouthAfricaIcon />;

      case 'UnitedStatesIcon':
        return <UnitedStatesIcon />;
      case 'CanadaIcon':
        return <CanadaIcon />;
      case 'MexicoIcon':
        return <MexicoIcon />;
      case 'BrazilIcon':
        return <BrazilIcon />;

      case 'MalaysiaIcon':
        return <MalaysiaIcon />;
      case 'IndonesiaIcon':
        return <IndonesiaIcon />;
      case 'PhilippinesIcon':
        return <PhilippinesIcon />;
      case 'ThailandIcon':
        return <ThailandIcon />;
      case 'VietnamIcon':
        return <VietnamIcon />;
    }
  };
  selectRegion = item => {
    this.setState({
      shop_region: item.countryCode,
      regionNow: item,
    });
  };
  /**
   * 处理form中所有字段变化的事件
   */
  handleFormValuesChange = (changedValues, allValues) => {
    if (this.state.current === 2) {
      //不能删，目的是content3组件更新
      this.setState({ agentInitName: allValues.bot_name });
      // }
    }
    console.log('All values:', allValues);
  };
  /**
   * 查询文档知识库下拉
   */
  queryDocumentKnowledge = () => {
    this.props.dispatch({
      type: 'documentKnowledgeBase/queryDocumentKnowledge',
      payload: { knowledgeType: 2 },
      callback: response => {
        if (response.code == 200) {
          this.setState({
            knowledgeBaseList: response.data,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  /**
   * 查询国家地区
   */
  queryTikTokMarketRegion = async code => {
    this.setState({
      content1Loading: true,
    });
    this.props.dispatch({
      type: 'tiktokRegionConfiguration/queryTiktokMarketRegion',
      callback: response => {
        this.setState({
          content1Loading: false,
        });
        if (response.code == 200) {
          this.setState({
            tiktokRegionList: response.data,
          });
          //处理回显时，获取已选择的国家item
          if (code || this.state.tiktokRegionList) {
            let list = [];
            response.data?.forEach(item => {
              list.push(item);
            });
            let newList = list.flat(Infinity);
            console.log(newList);
            newList?.forEach(item => {
              if (
                item.countryCode === code ||
                item.countryCode === this.state.tiktokRegionList[0].shop_region
              ) {
                this.setState(
                  {
                    regionNow: item,
                  },
                  () => {
                    console.log(this.state.regionNow);
                  },
                );
              }
            });
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  /**
   * content2 去配置
   */
  goConfigation = () => {
    window.open(
      `https://sellercentral.${this.state.regionNow.domain}/notifications/preferences/ref=xx_notifpref_dnav_xx`,
    );
  };
  /**
   * 选择tiktok店铺
   */
  selectTiktokShop = item => {
    this.props.dispatch({
      type: 'tiktokRegionConfiguration/checkShopInfoExist',
      payload: {
        shopId: item.shop_id,
      },
      callback: response => {
        if (response.code === 200) {
          this.setState({
            tiktok_shop_Info: item,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  /**
   * 复制
   */
  copyEmail = link => {
    copy(link);
    notification.success({
      message: getIntl().formatMessage({
        id: 'work.record.detail.copy.success',
      }),
    });
  };
  cancelAdd = () => {
    this.setState({
      isModalOpen: false,
    });
  };
  /**
   * content4文档知识库选择
   */
  docChange = e => {
    this.setState({ doc_bot: e });
  };
  /**
   * content4转人工关键词
   */
  handleInputChange = e => {
    this.setState({
      menuToAgentTxt: e.target.value,
    });
  };
  /**
   * content4转人工关键词回车
   */
  handleEnterPress = e => {
    // 中文输入状态下按下回车键判断
    if (e.key === 'Enter') {
      if (!e.nativeEvent.isComposing) {
        // 直接按下回车键
        this.handleInputConfirm(e);
      }
    }
  };
  handleInputConfirm = e => {
    let { menuToAgentTxt, termsTags } = this.state;

    if (termsTags && termsTags.length < 20) {
      if (menuToAgentTxt?.trim() && termsTags?.indexOf(menuToAgentTxt) === -1) {
        this.setState({
          termsTags: [...termsTags, menuToAgentTxt],
        });
      }
    } else {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'definition.synonyms.add.synonym.rules.number.tips.20',
          defaultValue: '最多添加20个标签！',
        }),
      });
    }
    this.setState({
      menuToAgentTxt: '',
    });
    e.preventDefault();
  };
  /**
   * content4删除标签
   */
  handleClose = removedTag => {
    let { termsTags } = this.state;
    const newTags = termsTags.filter(tag => tag !== removedTag);
    this.setState({
      termsTags: newTags,
    });
  };
  // 下一步事件
  onFinish = (values, flag) => {
    let { current, completedSteps, shop_region } = this.state;
    console.log(values, current);
    //分步骤保存
    if (current == 0) {
      if (!shop_region) {
        notification.error({
          message: getIntl().formatMessage({
            id: 'tiktokRegion.channel.configuration.title.1',
            defaultValue: '请选择你的 TikTok Shop 卖家地区',
          }),
        });
        return;
      }
    } else if (current == 1 && !this.state.tiktok_shop_Info?.shop_id) {
      notification.error({
        message: getIntl().formatMessage({
          id: 'tiktokRegion.channel.configuration.title.2.shop',
          defaultValue: '请授权您的账号并选择要绑定的 TikTok 店铺。',
        }),
      });
      return;
    } else if (current == 2) {
      //不选择文档知识库校验
      if (values.doc_bot_flag && !this.state.doc_bot) {
        notification.error({
          message: getIntl().formatMessage({
            id: 'whatsApp.channel.configuration.chat4.document.placeholder',
            defaultValue: '请选择文档知识库',
          }),
        });
        return false;
      }
      //必须输入转人工关键字
      console.log(this.state.termsTags);
      if (
        values.chat_open_channel_type !== '3' &&
        values.chat_open_channel_type !== '2' &&
        (!this.state.termsTags || this.state.termsTags.length <= 0)
      ) {
        notification.error({
          message: getIntl().formatMessage({
            id:
              'whatsApp.channel.configuration.chat4.document.placeholder.keyword',
            defaultValue: '请至少输入一条关键词',
          }),
        });
        return false;
      }
      //处理不能被form抓取的字段
      let data = {
        document_knowledge_base_id:
          values.doc_bot_flag && values.chat_open_channel_type !== '3'
            ? this.state.doc_bot
            : '',
        // auto_language_flag:
        //   values.chat_open_channel_type !== '3'
        //     ? values.auto_language_flag
        //     : '',
        auto_language_flag: false,
        doc_bot_flag:
          values.chat_open_channel_type !== '3' ? values.doc_bot_flag : '',

        knowledge_unknown_reply:
          values.chat_open_channel_type !== '3'
            ? values.knowledge_unknown_reply
            : '',
        menu_to_agent_txt:
          values.chat_open_channel_type !== '3' &&
          values.chat_open_channel_type !== '2'
            ? this.state.termsTags?.join(',')
            : '',
        agent_not_worktime_reply: values.agent_not_worktime_reply,
        ...this.state.tiktok_shop_Info,
        tiktok_auth_code: this.state.tiktok_auth_code,
        country_code: this.state.regionNow.countryCode,
        //写死的数据
        bot_name: 'CoCo',
      };
      this.setState(prevState => {
        return {
          finishData: Object.assign({}, prevState.finishData, values, data),
        };
      });
    } else if (current == 3) {
      //处理不能被form抓取的字段
      this.setState(
        prevState => {
          return {
            finishData: Object.assign({}, prevState.finishData, values),
          };
        },
        () => {
          //只有点击完成按钮才走接口
          if (flag !== 'step') {
            // 处理true转‘1’，false转‘0’
            let finishData = transformObject(this.state.finishData);
            let data = {
              name: this.state.finishData.name,
              channelType: 24, //固定传
              channelSubtype: 1, //固定传
              connectId:
                this.state.finishData.chat_open_channel_type === '2'
                  ? ''
                  : this.state.callingLines,
              config: [],
              // isOpenConnect: '1',
            };
            let config = Object.entries(finishData);
            data.config = config.map(item => {
              return {
                code: item[0],
                name: item[1],
              };
            });
            console.log(data);
            if (this.props.chatChannelId || history.location.query.channelId) {
              data.channelId =
                this.props.chatChannelId || history.location.query.channelId;
              data.name = this.formAddChannelRef.current?.getFieldValue('name');
              // 循环data.config，过滤掉tiktok_auth_code
              data.config = data.config.filter(
                item => item.code !== 'tiktok_auth_code',
              );

              this.setState({ btnLoading: true });

              this.props.dispatch({
                type: 'amazonRegionConfiguration/updateChannel',
                payload: {
                  data,
                  id: data.channelId,
                },
                callback: response => {
                  let { code, data, msg } = response;
                  if (code === 200) {
                    notification.success({
                      message: getIntl().formatMessage({
                        id: 'customer.ext.info.save.success',
                      }),
                    });
                    this.handleCancel();
                  } else {
                    this.setState({ btnLoading: false });
                    notification.error({
                      message: response.msg,
                    });
                  }
                },
              });
            } else {
              this.setState({ btnLoading: true });

              this.props.dispatch({
                type: 'amazonRegionConfiguration/createChannel',
                payload: data,
                callback: response => {
                  let { code, data, msg } = response;
                  if (code === 200) {
                    notification.success({
                      message: getIntl().formatMessage({
                        id: 'customer.ext.info.save.success',
                      }),
                    });
                    this.handleCancel();
                  } else {
                    this.setState({ btnLoading: false });

                    notification.error({
                      message: response.msg,
                    });
                  }
                },
              });
            }
          }
        },
      );
    }
    console.log(this.state.finishData);
    //跳步骤编辑情况下只保存数据不自动进入下一步
    if (flag !== 'step') {
      if (current < 3) {
        if (completedSteps <= current) {
          this.setState({
            current: current + 1,
            completedSteps: completedSteps + 1,
          });
        } else {
          this.setState({
            current: current + 1,
          });
        }
      }
    } else {
      return true;
    }
  };
  // 取消
  handleCancel = () => {
    this.formAddChannelRef.current?.resetFields();
    history.push({
      pathname: '/channelConfigurationDetailList',
      state: {
        channelType: 24,
      },
    });
    localStorage.setItem('newChannelType', 24);
  };
  /**
   * 工作时间下拉
   */
  channelWorkTimeList = () => {
    this.props.dispatch({
      type: 'workOrderExtension/channelWorkTimeList',
      callback: res => {
        if (res.code === 200) {
          this.setState({ workTimeList: res.data });
        } else {
          notification.error({
            message: res.msg,
          });
        }
      },
    });
  };
  //   /**
  //    * 绑定亚马逊
  //    */
  bindTiktok = () => {
    let state = JSON.stringify({
      tiktokRegionConfiguration: this.state.regionNow,
    });
    let url = `${this.state.regionNow.authUrl}&state=${state}`;
    window.open(url);
  };
  render() {
    const {
      tiktokConnectId,
      current,
      completedSteps,
      stepStatus,
      detailLoading,
    } = this.state;
    return (
      <Spin spinning={detailLoading}>
        <div className={styles.liveChatChannelConfigurationContent}>
          <div>
            <div className={styles.titleContent}>
              <img src={NewTikTokIcon} />
              <p className={styles.titleContent1}>
                {this.props.chatChannelId ||
                history.location.query.channelId ? (
                  <FormattedMessage
                    id="add.tiktok.channel.configuration.title.update"
                    defaultMessage="修改 TikTok Shop 渠道"
                  />
                ) : (
                  <FormattedMessage
                    id="add.tiktok.channel.configuration.title"
                    defaultMessage="添加 TikTok Shop 渠道"
                  />
                )}
              </p>
              {this.props.chatChannelId || history.location.query.channelId ? (
                <p className={styles.titleContentp}>
                  <FormattedMessage
                    id="add.tiktok.channel.configuration.tips.update"
                    defaultMessage="请修改 TikTok Shop 渠道"
                  />
                </p>
              ) : (
                <p className={styles.titleContentp}>
                  <FormattedMessage
                    id="add.tiktok.channel.configuration.tips"
                    defaultMessage="请添加 TikTok Shop 渠道"
                  />
                </p>
              )}
            </div>

            <div className={styles.detailContent}>
              <Form
                name="basic"
                autoComplete="off"
                layout="vertical"
                ref={this.formAddChannelRef}
                onFinish={this.onFinish}
                onValuesChange={this.handleFormValuesChange}
                initialValues={{
                  chat_open_channel_type: '3',
                  auto_language_flag: false,
                  doc_bot_flag: true,
                  mood_board_flag: '0',
                }}
              >
                <Steps
                  current={current}
                  onChange={this.onChangeSteps}
                  direction="vertical"
                >
                  <StepItem
                    title={getIntl().formatMessage({
                      id: 'tiktokRegion.channel.configuration.title.1',
                      defaultValue: '选择你的 TikTok Shop 卖家地区',
                    })}
                    subTitle={getIntl().formatMessage({
                      id: 'tiktokRegion.channel.configuration.title.tips.1',
                      defaultValue:
                        '如果你有不止一个 TikTok 地区，那也没关系。只需选择你想要首先设置的那个地区，我们稍后可以添加其他地区。',
                    })}
                    description={
                      <Content1
                        current={current}
                        content1Loading={this.state.content1Loading}
                        formAddChannelRef={this.formAddChannelRef}
                        step1Finish={this.state.step1Finish}
                        tiktokRegionList={this.state.tiktokRegionList}
                        getRegionIcon={this.getRegionIcon}
                        selectRegion={this.selectRegion}
                        chatRegion={this.state.shop_region}
                        regionNow={this.state.regionNow}
                      />
                    }
                    status={
                      current === 0
                        ? 'process'
                        : current > 0 || completedSteps >= 0
                        ? 'finish'
                        : ''
                    }
                  ></StepItem>
                  <StepItem
                    title={
                      this.state.tikTokShopList.length > 0 ||
                      this.props.chatChannelId ||
                      history.location.query.channelId
                        ? getIntl().formatMessage({
                            id:
                              'tiktokRegion.channel.configuration.title.2.shop',
                            defaultValue: '让我们进行授权，获取Token。',
                          })
                        : getIntl().formatMessage({
                            id: 'tiktokRegion.channel.configuration.title.2',
                            defaultValue: '让我们进行授权，获取Token。',
                          })
                    }
                    subTitle={
                      this.state.tikTokShopList.length > 0 ||
                      this.props.chatChannelId ||
                      history.location.query.channelId
                        ? getIntl().formatMessage({
                            id:
                              'tiktokRegion.channel.configuration.title.tips.2.shop',
                            defaultValue:
                              '为了帮助您接入 TikTok 消息与订单系统，请授权 ConnectNow 访问您的 TikTok 店铺信息，如果您有多个店铺请选择其中的一个。',
                          })
                        : getIntl().formatMessage({
                            id:
                              'tiktokRegion.channel.configuration.title.tips.2',
                            defaultValue:
                              '为了让我们能获取到您的 TikTok 的消息，你需要授予 ConnectNow 访问权限。',
                          })
                    }
                    description={
                      <Content2
                        current={current}
                        formAddChannelRef={this.formAddChannelRef}
                        completedSteps={completedSteps}
                        tikTokShopList={this.state.tikTokShopList}
                        bindTiktok={this.bindTiktok}
                        tiktok_shop_Info={this.state.tiktok_shop_Info}
                        selectTiktokShop={this.selectTiktokShop}
                      />
                    }
                    status={
                      current === 1
                        ? 'process'
                        : current > 1 || (current < 1 && completedSteps > 1)
                        ? 'finish'
                        : current < 1 && completedSteps < 1
                        ? 'wait'
                        : ''
                    }
                  ></StepItem>

                  <StepItem
                    title={getIntl().formatMessage({
                      id: 'amazonRegion.channel.configuration.title.4',
                      defaultValue: '智能客服设置',
                    })}
                    subTitle={getIntl().formatMessage({
                      id: 'amazonRegion.channel.configuration.title.tips.4',
                      defaultValue: '在这里您可以配置智能客服的相关信息',
                    })}
                    description={
                      <Content4
                        current={current}
                        completedSteps={completedSteps}
                        formAddChannelRef={this.formAddChannelRef}
                        docChange={this.docChange}
                        agentInitName={this.state.agentInitName}
                        doc_bot={this.state.doc_bot}
                        knowledgeBaseList={this.state.knowledgeBaseList
                          ?.map(item => {
                            if (item.status == 2) {
                              return {
                                label: item.knowledgeName,
                                value: item.knowledgeId,
                                key: item.knowledgeId,
                              };
                            }
                          })
                          ?.filter(item => item !== undefined)}
                        menuToAgentTxt={this.state.menuToAgentTxt}
                        handleInputChange={this.handleInputChange}
                        handleEnterPress={this.handleEnterPress}
                        handleClose={this.handleClose}
                        termsTags={this.state.termsTags}
                        workTimeList={this.state.workTimeList}
                        closeBetaPermission={
                          this.props.user.closeBetaPermission
                        }
                        chatChannelId={
                          this.props.chatChannelId ||
                          history.location.query.channelId
                        }
                      />
                    }
                    status={
                      current === 2
                        ? 'process'
                        : current > 2 || (current < 2 && completedSteps > 2)
                        ? 'finish'
                        : current < 2 && completedSteps < 2
                        ? 'wait'
                        : ''
                    }
                  ></StepItem>
                  <StepItem
                    title={getIntl().formatMessage({
                      id: 'amazonRegion.channel.configuration.title.5',
                      defaultValue: '填写渠道基本信息',
                    })}
                    subTitle={getIntl().formatMessage({
                      id: 'amazonRegion.channel.configuration.title.tips.5',
                      defaultValue: '填写基本的渠道信息',
                    })}
                    description={
                      <Content5
                        current={current}
                        completedSteps={completedSteps}
                        connectList={this.props.connectList}
                        formAddChannelRef={this.formAddChannelRef}
                        handleChangeSelectCallingLine={
                          this.handleChangeSelectCallingLine
                        }
                        openSelfChat={this.state.openSelfChat}
                      />
                    }
                  ></StepItem>
                </Steps>

                <Row
                  gutter={24}
                  style={{ textAlign: 'center', marginTop: '20px' }}
                >
                  <Col span={24}>
                    <Form.Item>
                      <Button
                        onClick={this.handleCancel}
                        style={{ marginRight: '12px' }}
                      >
                        <FormattedMessage
                          id="whatsApp.channel.configuration.cancel.btn"
                          defaultMessage="取消"
                        />
                      </Button>
                      <Button
                        type="primary"
                        htmlType="submit"
                        loading={this.state.btnLoading}
                      >
                        {current !== 3
                          ? getIntl().formatMessage({
                              id: 'whatsApp.channel.configuration.next.btn',
                              defaultValue: '下一步',
                            })
                          : getIntl().formatMessage({
                              id: 'whatsApp.channel.configuration.complete.btn',
                              defaultValue: '保存',
                            })}
                      </Button>
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
            </div>
          </div>
          <Modal
            title={null}
            closable={false}
            open={this.state.isModalOpen}
            footer={null}
          >
            <div className={styles.content3ModalBox}>
              <img src={TikTokConfirm} />
              <p>
                <FormattedMessage id="amazonRegion.channel.configuration.title.3.modal.title" />
              </p>
              <p>
                <FormattedMessage id="tiktokRegion.channel.configuration.title.3.modal.title.tips" />
              </p>
              <div className={styles.footerContent}>
                <Button
                  className={styles.cancel}
                  // onClick={this.showAddChannelAllocationFunction}
                  onClick={this.cancelAdd}
                >
                  {getIntl().formatMessage({ id: 'channel.cancel' })}
                </Button>
                <Button
                  type="primary"
                  onClick={() =>
                    this.onFinish(
                      this.formAddChannelRef.current.getFieldsValue(),
                      '',
                      true,
                    )
                  }
                  className={styles.saveBtn}
                >
                  {getIntl().formatMessage({
                    id: 'connect.beginner.guide.next.btn',
                  })}
                </Button>
              </div>
            </div>
          </Modal>
        </div>
      </Spin>
    );
  }
}

const Content1 = props => {
  let {
    current,
    tiktokRegionList,
    getRegionIcon,
    selectRegion,
    chatRegion,
    regionNow,
    content1Loading,
  } = props;

  if (current == 0) {
    return (
      <div>
        <Spin spinning={content1Loading}>
          <Row
            style={{
              marginTop: '-5px',
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            <div className={styles.amazonRegionList}>
              {tiktokRegionList?.map(item => {
                return (
                  // <div className={styles.amazonRegionBox}>
                  // <p className={styles.amazonRegionTitle}>{item.groupName}</p>
                  // {item.regionDefList?.map(itemSon => {
                  <div
                    className={styles.amazonRegionSingle}
                    onClick={() => selectRegion(item)}
                    style={{
                      boxShadow:
                        item.countryCode === chatRegion
                          ? '0px 3px 4px 0px rgba(52, 99, 252, 0.20);'
                          : '',
                      background:
                        item.countryCode === chatRegion ? '#3463FC' : '',
                    }}
                  >
                    <div style={{ height: 35 }}>
                      {getRegionIcon(item.countryNameIcon)}
                    </div>
                    <div className={styles.amazonRegionFont}>
                      <span
                        className={styles.amazonRegionName}
                        style={{
                          color: item.countryCode === chatRegion ? '#fff' : '',
                        }}
                      >
                        {item.countryName}
                      </span>
                      {/* <span
                              style={{
                                color: itemSon.id === chatRegion ? '#fff' : '',
                              }}
                              className={styles.amazonRegionDomain}
                            >
                              {itemSon.domain}
                            </span> */}
                    </div>

                    {/* })} */}
                    {/* </div> */}
                  </div>
                );
              })}
            </div>
          </Row>
        </Spin>
      </div>
    );
  } else {
    return (
      <div className={styles.amazonRegionBoxFinish}>
        <span className={styles.completedText}>
          <FormattedMessage id="amazonRegion.channel.configuration.1.finish" />
        </span>
        <div className={styles.amazonRegionSingle}>
          <div style={{ height: 35 }}>
            {getRegionIcon(regionNow.countryNameIcon)}
          </div>
          <div className={styles.amazonRegionFont}>
            <span className={styles.amazonRegionName}>
              {regionNow.countryName}
            </span>
            {/* <span className={styles.amazonRegionDomain}>
              {regionNow.domain}
            </span> */}
          </div>
        </div>
      </div>
    );
  }
};
const Content2 = props => {
  let { current, completedSteps, tikTokShopList, tiktok_shop_Info } = props;
  if (current == 1 && tikTokShopList.length <= 0) {
    return (
      <div className={styles.content2BoxFinish}>
        <Button type="primary" onClick={() => props.bindTiktok()}>
          <FormattedMessage id="amazonRegion.channel.configuration.title.2.btn" />
        </Button>
      </div>
    );
  } else if (current == 1 && tikTokShopList.length > 0) {
    return (
      <div className={styles.content2BoxFinishGoList}>
        {tikTokShopList?.map(item => {
          return (
            <div
              className={styles.content2BoxFinishGo}
              style={{
                border:
                  item.shop_id === tiktok_shop_Info.shop_id
                    ? '1px solid #3463FC'
                    : '',
              }}
              onClick={() => props.selectTiktokShop(item)}
            >
              <div className={styles.content2BoxFinishGoText}>
                {getIntl().formatMessage({
                  id: 'channel.allocation.detail.select.account.tiktok.shopId',
                  defaultValue: '店铺ID：',
                })}
                {item.shop_id}
              </div>
              <div className={styles.content2BoxFinishGoText}>
                {getIntl().formatMessage({
                  id:
                    'channel.allocation.detail.select.account.tiktok.shopName',
                  defaultValue: '店铺名称：',
                })}
                {item.shop_name}
              </div>
              <div className={styles.content2BoxFinishGoText}>
                {getIntl().formatMessage({
                  id:
                    'channel.allocation.detail.select.account.tiktok.shopCode',
                  defaultValue: '店铺编码：',
                })}
                {item.shop_code}
              </div>
            </div>
          );
        })}
      </div>
    );
  } else if (completedSteps >= 1 && tiktok_shop_Info) {
    return (
      <div className={styles.content2BoxFinishGoList}>
        <div className={styles.content2BoxFinishGo}>
          <div className={styles.content2BoxFinishGoText}>
            {getIntl().formatMessage({
              id: 'channel.allocation.detail.select.account.tiktok.shopId',
              defaultValue: '店铺ID：',
            })}
            {tiktok_shop_Info?.shop_id}
          </div>
          <div className={styles.content2BoxFinishGoText}>
            {getIntl().formatMessage({
              id: 'channel.allocation.detail.select.account.tiktok.shopName',
              defaultValue: '店铺名称：',
            })}
            {tiktok_shop_Info?.shop_name}
          </div>
          <div className={styles.content2BoxFinishGoText}>
            {getIntl().formatMessage({
              id: 'channel.allocation.detail.select.account.tiktok.shopCode',
              defaultValue: '店铺编码：',
            })}
            {tiktok_shop_Info?.shop_code}
          </div>
        </div>
      </div>
    );
  } else {
    return null;
  }
};

const Content4 = props => {
  let { current, completedSteps } = props;
  console.log(
    props.formAddChannelRef.current?.getFieldValue('chat_open_channel_type'),
  );
  if (current == 2) {
    return (
      <div>
        {props.closeBetaPermission === 1 ? (
          <Row gutter={24}>
            <Col span={10}>
              <Form.Item
                label={getIntl().formatMessage({
                  id: 'chat.channel.configuration.chat1.ai.aiagent',
                  defaultValue: '开启AI Agent',
                })}
                name="is_open_aiagent"
                valuePropName="checked"
              >
                <Checkbox
                  disabled={
                    props.chatChannelId || localStorage.getItem('chatChannelId')
                  }
                >
                  <FormattedMessage
                    id="chat.channel.configuration.work.panels.checkbox"
                    defaultMessage="开启"
                  />
                </Checkbox>
              </Form.Item>
              <p
                className={`${styles.fs12} ${styles.color999} ${styles.mt_10}`}
              >
                <FormattedMessage
                  id="chat.channel.configuration.chat1.ai.aiagent.tips"
                  defaultMessage="开启AI Agent之后，欢迎用语以及AIGC知识库的选择需要到AI Agent页面进行对应的配置。"
                />
              </p>
            </Col>
          </Row>
        ) : (
          ''
        )}
        <Row gutter={24}>
          <Col span={24}>
            <Form.Item
              label={getIntl().formatMessage({
                id: 'whatsApp.channel.configuration.chat4.mode',
                defaultValue: '客服模式',
              })}
              name="chat_open_channel_type"
            >
              <Radio.Group>
                <HOCAuth authKey={'automatically_create_tickets'}>
                  {authAccess => (
                    <Radio value={'1'}>
                      <FormattedMessage
                        id="whatsApp.channel.configuration.chat4.mode.1"
                        defaultMessage="智能客服"
                      />
                    </Radio>
                  )}
                </HOCAuth>
                <Radio value={'3'}>
                  <FormattedMessage
                    id="whatsApp.channel.configuration.chat4.mode.2"
                    defaultMessage="仅人工"
                  />
                </Radio>
                <HOCAuth authKey={'automatically_create_tickets'}>
                  {authAccess => (
                    <Radio value={'2'}>
                      <FormattedMessage
                        id="whatsApp.channel.configuration.chat4.mode.3"
                        defaultMessage="仅机器人"
                      />
                    </Radio>
                  )}
                </HOCAuth>
              </Radio.Group>
            </Form.Item>

            <div className={`${styles.mt_10_10}`}>
              <p
                className={`${styles.fs12} ${styles.color999} ${styles.mb_0}`}
                style={{ paddingBottom: 10 }}
              >
                <FormattedMessage
                  id="whatsApp.channel.configuration.chat4.mode.message"
                  defaultMessage="智能客服会先有机器人应答，机器人回答不了的问题，用户可以随时转人工 / 仅人工应答只有人工客服应答问题 / 仅机器人应答只有机器人赢答问题"
                />
                <br />
                <FormattedMessage
                  id="whatsApp.channel.configuration.chat4.mode.message.1"
                  defaultMessage=""
                />
                <br />
                <FormattedMessage
                  id="whatsApp.channel.configuration.chat4.mode.message.2"
                  defaultMessage=""
                />
              </p>
            </div>
          </Col>
        </Row>
        {props.formAddChannelRef.current?.getFieldValue(
          'chat_open_channel_type',
        ) !== '3' ? (
          <div>
            {/*********************** 文档知识库 **********************/}
            {props.formAddChannelRef.current?.getFieldValue(
              'is_open_aiagent',
            ) !== true ? (
              <Row gutter={24}>
                <Col span={24}>
                  <Form.Item
                    label={getIntl().formatMessage({
                      id: 'whatsApp.channel.configuration.chat4.document',
                      defaultValue: '文档知识库',
                    })}
                    name="doc_bot_flag"
                    valuePropName="checked"
                  >
                    <Checkbox>
                      <FormattedMessage
                        id="whatsApp.channel.configuration.work.panels.checkbox"
                        defaultMessage="开启"
                      />
                    </Checkbox>
                  </Form.Item>
                  {props.formAddChannelRef.current?.getFieldValue(
                    'doc_bot_flag',
                  ) ? (
                    <Select
                      style={{ marginTop: '-10px', width: 300 }}
                      placeholder={getIntl().formatMessage({
                        id:
                          'whatsApp.channel.configuration.chat4.document.placeholder',
                        defaultValue: '请选择文档知识库',
                      })}
                      value={props.doc_bot}
                      onChange={e => props.docChange(e)}
                      options={props.knowledgeBaseList}
                    />
                  ) : (
                    ''
                  )}
                  <p
                    style={{
                      marginTop: props.formAddChannelRef.current?.getFieldValue(
                        'doc_bot_flag',
                      )
                        ? ''
                        : '-10px',
                    }}
                  >
                    <span className={`${styles.fs12} ${styles.color999}`}>
                      <FormattedMessage
                        id="whatsApp.channel.configuration.chat4.document.message.1"
                        defaultMessage="此处的文档知识库仅展示外部知识库，请提前到"
                      />
                    </span>
                    <a
                      href={`https://${process.env.DOMAIN_NAME_OVER}/#/documentKnowledgeBase`}
                      style={{ fontSize: 12, color: '#3463FC' }}
                      target="_blank"
                    >
                      <FormattedMessage
                        id="whatsApp.channel.configuration.chat4.document.message"
                        defaultMessage="文档知识库"
                      />
                    </a>
                    &nbsp;
                    <span className={`${styles.fs12} ${styles.color999}`}>
                      <FormattedMessage
                        id="whatsApp.channel.configuration.chat4.document.message.2"
                        defaultMessage="页面进行知识库的配置"
                      />
                    </span>
                  </p>
                </Col>
              </Row>
            ) : (
              ''
            )}
            {/*********************** 转人工关键词 **********************/}
            {props.formAddChannelRef.current?.getFieldValue(
              'chat_open_channel_type',
            ) !== '2' ? (
              <div>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      label={
                        <div>
                          {getIntl().formatMessage({
                            id:
                              'whatsApp.channel.configuration.chat4.workers.keyword',
                            defaultValue: '转人工关键词',
                          })}

                          {/* <Popover
                            content={() => (
                              <div>
                                <p>
                                  <FormattedMessage
                                    id="chat.channel.configuration.chat4.workers.position"
                                    defaultMessage="所在位置"
                                  />
                                </p>
                                <div
                                  style={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'baseline',
                                  }}
                                >
                                  <div
                                    className={styles.box_content}
                                    style={{
                                      background: `url(${whatsApp_bg})`,
                                      color: '#fff',
                                      fontSize: 12,
                                      height: 54,
                                      width: 209,
                                      display: 'flex',
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                      paddingLeft: 20,
                                    }}
                                  >
                                    <span>
                                      <FormattedMessage
                                        id="chat.channel.configuration.chat4.workers.content"
                                        defaultMessage="很抱歉，这个问题我无法回答您，请转人工咨询。"
                                      />
                                    </span>
                                  </div>
                                  <div
                                    style={{
                                      backgroundColor: '#222C32',
                                      borderRadius: 10,
                                      color: '#70BBE6',
                                      fontSize: '12px',
                                      height: 24,
                                      width: 209,
                                      marginTop: 5,
                                      display: 'flex',
                                      justifyContent: 'center',
                                      alignItems: 'center',
                                    }}
                                  >
                                    <span>
                                      <FormattedMessage
                                        id="whatsApp.channel.configuration.chat4.workers.zhuan"
                                        defaultMessage="转人工"
                                      />
                                    </span>
                                  </div>
                                </div>
                              </div>
                            )}
                            trigger="click"
                            placement="right"
                          >
                            <img
                              src={blueBg}
                              style={{ marginLeft: 10, marginBottom: 5 }}
                            />
                          </Popover> */}
                        </div>
                      }
                      name="menu_to_agent_txt"
                    >
                      <Input
                        type="text"
                        value={props.menuToAgentTxt}
                        maxLength={40}
                        onChange={props.handleInputChange}
                        onKeyDown={props.handleEnterPress}
                        placeholder={getIntl().formatMessage({
                          id: 'definition.synonyms.input.placeholder',
                          defaultValue: '输入回车生成标签，最长20字符',
                        })}
                      />
                      {props.termsTags?.map((tag, index) => {
                        const isLongTag = tag.length > 20;
                        const tagElem = (
                          <Tag
                            className="edit-tag"
                            key={tag}
                            // closable={index !== 0}
                            closable
                            onClose={() => props.handleClose(tag)}
                          >
                            <span>
                              {isLongTag ? `${tag.slice(0, 20)}...` : tag}
                            </span>
                          </Tag>
                        );
                        return isLongTag ? (
                          <Tooltip title={tag} key={tag}>
                            {tagElem}
                          </Tooltip>
                        ) : (
                          tagElem
                        );
                      })}
                    </Form.Item>
                    <p
                      className={`${styles.fs12} ${styles.color999}  ${styles.mt_10}`}
                    >
                      <FormattedMessage
                        id="whatsApp.channel.configuration.chat4.workers.keyword.message"
                        defaultMessage="客户输入此关键词后会转人工客服"
                      />
                    </p>
                  </Col>
                </Row>
              </div>
            ) : (
              ''
            )}
            {/*********************** 问答风格 **********************/}

            {props.formAddChannelRef.current?.getFieldValue(
              'is_open_aiagent',
            ) !== true ? (
              <Row gutter={24}>
                <Col span={24}>
                  <Form.Item
                    label={getIntl().formatMessage({
                      id: 'facebook.channel.configuration.title.3.qingxu',
                      defaultValue: '问答风格',
                    })}
                    name="mood_board_flag"
                  >
                    <Radio.Group>
                      <Radio value={'0'}>
                        <FormattedMessage
                          id="facebook.channel.configuration.title.3.qingxu.radio.1"
                          defaultMessage="简洁专业"
                        />
                      </Radio>
                      <Radio value={'1'}>
                        <FormattedMessage
                          id="facebook.channel.configuration.title.3.qingxu.radio.2"
                          defaultMessage="温柔陪伴"
                        />
                      </Radio>
                    </Radio.Group>
                  </Form.Item>

                  <div className={`${styles.mt_10_10}`}>
                    <p
                      className={`${styles.fs12} ${styles.color999} ${styles.mb_0}`}
                      style={{ paddingBottom: 10 }}
                    >
                      <FormattedMessage
                        id="facebook.channel.configuration.title.3.qingxu.radio.tips"
                        defaultMessage="开启温柔陪伴，AIGC Chatbot将会为您的客户提供更温和的回答。"
                      />
                    </p>
                  </div>
                </Col>
              </Row>
            ) : (
              ''
            )}
            {/*********************** 机器人未知问题回复 **********************/}
            {props.formAddChannelRef.current?.getFieldValue(
              'is_open_aiagent',
            ) !== true ? (
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    label={getIntl().formatMessage({
                      id: 'whatsApp.channel.configuration.chat4.unknown',
                      defaultValue: '机器人未知问题回复',
                    })}
                    name="knowledge_unknown_reply"
                    rules={[
                      {
                        required: true,
                        message: getIntl().formatMessage({
                          id:
                            'whatsApp.channel.configuration.chat4.unknown.placeholder',
                          defaultValue: '请输入机器人未知问题回复',
                        }),
                      },
                      {
                        min: 1,
                        max: 200,
                      },
                    ]}
                  >
                    <Input
                      defaultValue={props.defaultValue}
                      placeholder={getIntl().formatMessage({
                        id:
                          'whatsApp.channel.configuration.chat4.unknown.placeholder',
                        defaultValue: '请输入机器人未知问题回复',
                      })}
                    />
                  </Form.Item>
                  <p
                    className={`${styles.fs12} ${styles.color999} ${styles.mt_10}`}
                  >
                    <FormattedMessage
                      id="whatsApp.channel.configuration.chat4.unknown.message"
                      defaultMessage="此处设置是机器人在遇到未知问题时的回复"
                    />
                  </p>
                </Col>
              </Row>
            ) : (
              ''
            )}
          </div>
        ) : (
          ''
        )}
        <Row gutter={24}>
          <Col span={8}>
            <Form.Item
              label={getIntl().formatMessage({
                id: 'chat.channel.configuration.chat4.agent.workTime',
                defaultValue: '座席工作时间',
              })}
              name="agent_worktime_id"
              rules={[
                {
                  required: true,
                  message: getIntl().formatMessage({
                    id: 'chat.channel.configuration.chat4.agent.workTime.p',
                    defaultValue: '请选择座席工作时间',
                  }),
                },
              ]}
            >
              <Select
                style={{ marginTop: 0 }}
                placeholder={getIntl().formatMessage({
                  id: 'chat.channel.configuration.chat4.agent.workTime.p',
                  defaultValue: '请选择座席工作时间',
                })}
                options={props.workTimeList}
                fieldNames={{
                  label: 'workTimeName',
                  value: 'workTimeId',
                }}
                showSearch
                filterOption={(inputValue, option) =>
                  option.workTimeName
                    .toLowerCase()
                    .indexOf(inputValue.toLowerCase()) >= 0
                }
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              label={getIntl().formatMessage({
                id: 'chat.channel.configuration.chat4.agent.workTime.no',
                defaultValue: '不在工作时间机器人回复话术',
              })}
              name="agent_not_worktime_reply"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Input maxLength={200} />
            </Form.Item>
            <p className={`${styles.fs12} ${styles.color999} ${styles.mt_10}`}>
              <FormattedMessage
                id="chat.channel.configuration.chat4.agent.workTime.no.tips"
                defaultMessage="当不在座席工作时间，客户转人工咨询时机器人回复的话术"
              />
            </p>
          </Col>
        </Row>
      </div>
    );
  } else if (completedSteps >= 2) {
    return (
      <p className={styles.completedText12}>
        <FormattedMessage
          id="whatsApp.channel.configuration.chat4.information.configuration.completed"
          defaultMessage="您已完成智能客服的设置，这些将会在您保存后实现。"
        />
      </p>
    );
  } else {
    return null;
  }
};
const Content5 = props => {
  let { current, completedSteps } = props;

  if (current == 3) {
    return (
      <div>
        <Row gutter={24}>
          <Col span={10}>
            <Form.Item
              label={getIntl().formatMessage({
                id: 'chat.channel.configuration.channel.name',
                defaultValue: '渠道名称',
              })}
              name="name"
              rules={[
                {
                  required: true,
                  message: getIntl().formatMessage({
                    id: 'chat.channel.configuration.channel.name.placeholder',
                    defaultValue: '请输入渠道名称',
                  }),
                },
                {
                  min: 1,
                  max: 80,
                  pattern: '^[\u4e00-\u9fa5A-Za-z0-9-_]*$',
                  message: getIntl().formatMessage({
                    id:
                      'chat.channel.configuration.channel.name.placeholder.error',
                    defaultValue: '只能输入中文、大小写字母、数字、”-”、“_”',
                  }),
                },
              ]}
            >
              <Input
                placeholder={getIntl().formatMessage({
                  id: 'chat.channel.configuration.channel.name.placeholder',
                  defaultValue: '请输入渠道名称',
                })}
              />
            </Form.Item>
          </Col>
        </Row>
        {props.formAddChannelRef.current?.getFieldValue(
          'chat_open_channel_type',
        ) !== '2' ? (
          props.openSelfChat === 1 ? (
            ''
          ) : (
            <Row gutter={24}>
              <Col span={10}>
                <Form.Item
                  label={getIntl().formatMessage({
                    id: 'email.channel.configuration.calling.lines',
                    defaultValue: '联络线路',
                  })}
                  name="callingLines"
                  rules={[
                    {
                      required: true,
                      message: getIntl().formatMessage({
                        id:
                          'email.channel.configuration.calling.lines.placeholder',
                        defaultValue: '请选择联络线路',
                      }),
                    },
                  ]}
                >
                  <Select
                    style={{ marginTop: 0 }}
                    placeholder={getIntl().formatMessage({
                      id:
                        'email.channel.configuration.calling.lines.placeholder',
                      defaultValue: '请选择联络线路',
                    })}
                    options={props.connectList}
                    showSearch
                    filterOption={(inputValue, option) =>
                      option.label
                        .toLowerCase()
                        .indexOf(inputValue.toLowerCase()) >= 0
                    }
                    onChange={value =>
                      props.handleChangeSelectCallingLine(value, 'callingLines')
                    }
                  />
                </Form.Item>
              </Col>
            </Row>
          )
        ) : (
          ''
        )}
        {/* <Row gutter={24}>
          <Col span={24}>
            <Form.Item
              label={getIntl().formatMessage({
                id: 'whatsApp.channel.configuration.chat3.form',
                defaultValue: '工作集成面板',
              })}
              name="isOpenConnect"
              valuePropName="checked"
            >
              <Checkbox>
                <FormattedMessage
                  id="whatsApp.channel.configuration.work.panels.checkbox"
                  defaultMessage="开启"
                />
              </Checkbox>
            </Form.Item>
          </Col>
        </Row> */}
      </div>
    );
  } else if (completedSteps == 4) {
    return (
      <p className={styles.completedText12}>
        <p>
          <FormattedMessage
            id="chat.channel.configuration.channel.name"
            defaultMessage="渠道名称"
          />
          ：{props.formAddChannelRef.current?.getFieldValue('name')}
        </p>
        {props.formAddChannelRef.current?.getFieldValue(
          'chat_open_channel_type',
        ) !== '2' ? (
          props.openSelfChat === 1 ? (
            ''
          ) : (
            <p>
              <FormattedMessage
                id="email.channel.configuration.calling.lines"
                defaultMessage="联络线路"
              />
              ：
              {
                props.connectList.filter(
                  item =>
                    item.value ===
                    props.formAddChannelRef.current?.getFieldValue(
                      'callingLines',
                    ),
                )[0]?.label
              }
            </p>
          )
        ) : (
          ''
        )}
      </p>
    );
  } else {
    return null;
  }
};

const mapStateToProps = ({ whatsAppChannelConfiguration, layouts }) => {
  return {
    ...whatsAppChannelConfiguration,
    connectList: layouts.connectList,
    user: layouts.user,
  };
};
export default connect(mapStateToProps)(tiktokConfiguration);
