.liveChatChannelConfigurationContent {
  margin: 24px;
  padding: 20px;
  height: 95%;
  background: #fff;
  overflow: hidden;
  overflow-y: scroll;
  // display: flex;
  position: relative;
  /* 隐藏滚动条 */
  scrollbar-width: none;
  /* firefox */
  -ms-overflow-style: none;

  /* IE 10+ */
  .noMb {
    :global {
      .ant-form-item {
        margin-bottom: 5px !important;
      }
    }
  }

  .titleContent {
    width: 100%;
    height: 55px;

    img {
      width: 40px;
      float: left;
      margin-right: 12px;
      margin-bottom: 15px;
    }

    .titleContent1 {
      width: 80%;
      color: #333;
      font-size: 20px;
      font-style: normal;
      font-weight: 400;
      margin-bottom: 0px;
      line-height: 40px;
      margin-top: 0 !important;
    }

    .titleContentp {
      color: #999;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      float: left;
    }
  }

  .detailContent {
    width: 100%;
    margin-top: 40px;

    // .amazonRegionBox {
    //   margin-bottom: 10px;

    .amazonRegionTitle {
      color: #333;
      font-family: 'Poppins', sans-serif;
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
      line-height: normal;
      margin-bottom: 10px;
    }

    .amazonRegionList {
      display: grid;
      /* 使用 Grid 布局 */
      grid-template-columns: repeat(5, 1fr);
      /* 每行最多 5 列 */
      column-gap: 40px;
      row-gap: 10px;
    }

    .amazonRegionSingle {
      display: flex;
      flex-direction: row;
      cursor: pointer;
      border-radius: 10px;
      padding: 10px 10px 5px 10px;
      align-items: center;

      .amazonRegionFont {
        display: flex;
        flex-direction: column;
        margin-left: 6px;

        .amazonRegionName {
          color: #333;
          font-family: 'Poppins', sans-serif;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          margin-bottom: 1px;
          margin-top: -1px;
        }

        .amazonRegionDomain {
          color: #999;
          font-family: 'Poppins', sans-serif;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }
      }

      &:hover {
        background: rgba(52, 99, 252, 0.1);
        /* 阴影 */
        box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
      }
    }

    // .amazonRegionSingle&:active {
    //   background: #3463FC;

    //   /* 蓝色阴影 */
    //   box-shadow: 0px 3px 4px 0px rgba(52, 99, 252, 0.20);
    // }
    // }

    .amazonRegionBoxFinish {
      margin-bottom: 10px;

      .amazonRegionSingle {
        display: flex;
        flex-direction: row;
        cursor: pointer;
        border-radius: 10px;
        padding: 10px 10px 0 10px;
        width: fit-content;

        .amazonRegionFont {
          display: flex;
          flex-direction: column;
          margin-left: 6px;

          .amazonRegionName {
            color: #333;
            font-family: 'Poppins', sans-serif;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            margin-bottom: 1px;
            margin-top: -1px;
          }

          .amazonRegionDomain {
            color: #999;
            font-family: 'Poppins', sans-serif;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
          }
        }
      }
    }

    .content2BoxFinishGoList {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      max-height: 30vh;
      overflow-y: auto;
      gap: 20px;

      .content2BoxFinishGo {
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        padding: 10px !important;
        height: auto;
        display: flex;
        flex-direction: column;
        color: #333;
        border-radius: 4px;
        box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
        cursor: pointer;

        &:hover {
          background: rgba(52, 99, 252, 0.1);
          /* 阴影 */
          box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
        }
      }

      .content2BoxFinishGoText {
        font-size: 12px;
        color: #333;
        margin-bottom: 10px;
      }
    }

    .content2BoxFinish {
      margin: 20px 0;

      :global {
        .ant-btn {
          padding-left: 50px !important;
          padding-right: 50px !important;
          padding-top: 4px !important;
          padding-bottom: 4px !important;
          font-size: 14px !important;
          background: #3463fc !important;
          border: 1px solid #3463fc !important;
          height: auto;
        }
      }
    }

    .content3BoxFinish {
      :global {
        .ant-btn {
          padding-left: 50px !important;
          padding-right: 50px !important;
          font-size: 14px !important;
          background: #13c825 !important;
          border: 1px solid #13c825 !important;
        }
      }
    }

    .content3Box {
      color: #333;
      font-family: 'Poppins', sans-serif;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;

      :global {
        .ant-btn {
          padding-left: 50px !important;
          padding-right: 50px !important;
          font-size: 14px !important;
        }
      }

      .emailBox {
        border-radius: 6px;
        border: 1px solid #e6e6e6;
        background: #fff;
        box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.05);
        padding: 4px 10px;
        width: fit-content;
      }

      .emailBoxCopy {
        color: #3463fc;
        cursor: pointer;
        text-align: right;
        font-family: 'Poppins', sans-serif;
        font-size: 12px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        margin-left: 20px;
      }
    }

    .completedText {
      color: #333;
      font-family: 'Poppins', sans-serif;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }

    .completedText12 {
      color: #333;
      font-family: 'Poppins', sans-serif;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }

    :global {
      .ant-tag {
        font-size: 12px !important;
      }

      .ant-select:not(.ant-select-customize-input) .ant-select-selector,
      .ant-input {
        border-radius: 6px;
        //border: 1px solid #e6e6e6;
        background: #fff;
        width: 100%;
        font-size: 12px;
        box-shadow: none !important;
      }

      .ant-steps-item-wait
        > .ant-steps-item-container
        > .ant-steps-item-content
        > .ant-steps-item-title,
      .ant-steps-item-finish
        > .ant-steps-item-container
        > .ant-steps-item-content
        > .ant-steps-item-title,
      .ant-steps-item-process
        > .ant-steps-item-container
        > .ant-steps-item-content
        > .ant-steps-item-title {
        color: #333 !important;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
      }

      .ant-steps-item-subtitle {
        color: #999 !important;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
      }

      .ant-upload-list-picture-card-container {
        background-color: #f0f0f0;
      }

      .ant-upload.ant-upload-select-picture-card {
        background-color: #f0f0f0;
      }

      .ant-form-item {
        margin-bottom: 15px;
      }

      .ant-steps-item-process
        > .ant-steps-item-container
        > .ant-steps-item-icon {
        background: #3463fc;
      }

      .ant-steps-item-process .ant-steps-item-icon {
        border-color: #3463fc;
      }

      .ant-steps-item-title {
        display: flex;
        flex-direction: column;
        line-height: 26px !important;

        .ant-steps-item-subtitle {
          margin-left: 0;
        }
      }

      .ant-steps-item-icon {
        width: 40px;
        height: 40px;
        font-size: 20px;
        font-weight: 700;
      }

      .ant-steps-item-icon .ant-steps-icon {
        top: 2.5px;
      }

      .ant-steps-vertical
        > .ant-steps-item
        > .ant-steps-item-container
        > .ant-steps-item-tail {
        left: 20px;
        padding-top: 48px;
      }

      .ant-steps-item-wait .ant-steps-item-icon {
        background: #e6e6e6 !important;
        border-color: #e6e6e6 !important;
        box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.1);
      }

      .ant-steps-item-wait .ant-steps-item-icon > .ant-steps-icon {
        color: #333 !important;
        font-size: 20px;
        font-style: normal;
        font-weight: 400;
      }

      .ant-steps-item-finish .ant-steps-item-icon {
        background-color: #13c825 !important;
        border-color: #13c825 !important;
      }

      .ant-steps-item-finish .ant-steps-item-icon > .ant-steps-icon {
        color: #fff !important;
      }

      .ant-steps-item-finish
        > .ant-steps-item-container
        > .ant-steps-item-tail::after {
        background-color: #e6e6e6 !important;
      }

      .ant-steps-vertical > .ant-steps-item .ant-steps-item-title {
        line-height: 42px;
      }

      .ant-steps-item-description {
        margin-top: 15px;
      }

      .ant-form-item-label {
        padding: 0 !important;
      }

      .ant-form-item-label
        > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
        display: none;
      }

      .ant-form label {
        font-size: 12px !important;
      }

      .ant-select {
        margin-top: 10px;
      }

      .ant-input-number {
        margin: 0 10px;
        border-radius: 6px;
        width: 60px;
      }
    }
  }

  p {
    margin-top: 6px;
  }

  .preCode {
    background-color: #f0f0f0;
    font-size: 12px;
    // width: max-content;
    padding: 10px;
    border-radius: 4px;
    position: relative;

    .copyContent {
      background-color: #3463fc;
      display: inline-block;
      border-radius: 0 4px 0 4px;
      color: #fff;
      padding: 2px 6px;
    }
  }

  .LiveChatTemplete {
    position: fixed;
    background-color: #eee;
    padding: 20px;
    width: 600px;
    height: 546px;
    right: 50px;
    top: 80px;

    .LiveChatForm {
      width: 270px;
      height: 420px;
      border-radius: 5px 5px 15px 5px;
      display: flex;
      flex-direction: column;

      .header {
        height: 32px;
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0 8px;
        justify-content: space-between;
        width: 100%;
        top: 0;
        border-radius: 10px 10px 0 0;

        .headerLeft {
          display: flex;
          flex-direction: row;
          align-items: center;
        }

        .headerRight {
          display: flex;
          flex-direction: row;
          align-items: center;
        }
      }

      .mainContent {
        height: 350px;
        display: flex;
        flex-direction: column;
        background-color: #f9f9f9;
        flex: 10;
        border-radius: 0 0 50px 10px;
        justify-content: space-between;

        .agentmain {
          display: flex;
          flex-direction: row;
          align-items: baseline;
          padding: 10px;

          .avator {
          }

          .box {
            display: flex;
            flex-direction: column;
            margin-left: 5px;
            width: 70%;

            .box_role {
              font-size: 11px;
            }

            .box_content {
              margin-top: 0.5rem;
              padding: 0.5rem;
              color: #000;
              font-size: 0.75rem;
              border-radius: 0.2rem 0.5rem 0.5rem 0.5rem;
              box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.15);
              display: flex;
              flex-direction: column;
              overflow-wrap: break-word;
            }
          }
        }

        .customermain {
          display: flex;
          flex-direction: row;
          align-items: baseline;
          margin-bottom: 0.75rem;
          justify-content: flex-end;
          padding: 0 10px;

          .avator {
          }

          .box {
            display: flex;
            flex-direction: column;
            margin-right: 5px;
            width: 70%;

            .box_role {
              font-size: 11px;
              text-align: end;
            }

            .box_content {
              margin-top: 0.5rem;
              padding: 0.5rem;
              color: #fff;
              font-size: 0.75rem;
              border-radius: 0.5rem 0.2rem 0.5rem 0.5rem;
              box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.15);
              overflow-wrap: break-word;
            }
          }
        }
      }

      .main {
        height: 350px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f9f9f9;
        flex: 10;
        padding-bottom: 40px;
        border-radius: 0 0 50px 10px;

        .loginForm {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;

          :global {
            .ant-form {
              width: 232px;
            }

            .ant-form-item {
              margin-bottom: 40px;
            }

            .ant-form-item:nth-child(1) {
              margin-top: 8px;
              margin-bottom: 8px;
            }

            .ant-form-item-required {
              color: #333;
              font-size: 0.8rem;
            }

            .ant-form-item-label > label {
              font-size: 10px !important;
            }

            .ant-input-affix-wrapper {
              border-radius: 8px;
              border-color: 1px solid #e6e6e6;
            }

            .ant-input-prefix {
              height: 20px;
            }

            .ant-input {
              font-size: 9px !important;
              height: 20px;
            }

            .ant-form-item-label {
              padding: 5px !important;
            }

            .ant-btn {
              border-radius: 4px;
              height: 2.25rem;
              width: 90px;
              font-size: 1rem;
              font-weight: 500;
              color: #fff;
            }

            .ant-btn:hover {
              /* 取消按钮的默认 hover 样式 */
              background-color: #ad30e5;
              color: #fff;
            }
          }
        }
      }
    }
  }

  .LiveChatPhoneTemplete {
    position: fixed;
    background-color: #eee;
    padding: 20px;
    width: 600px;
    height: 546px;
    right: 50px;
    top: 80px;

    .LiveChatForm {
      width: 250px;
      height: 420px;
      border-radius: 5px 5px 15px 5px;
      display: flex;
      flex-direction: column;

      .header {
        height: 32px;
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0 8px;
        justify-content: space-between;
        width: 100%;
        top: 0;

        .headerLeft {
          display: flex;
          flex-direction: row;
          align-items: center;
        }

        .headerRight {
          display: flex;
          flex-direction: row;
          align-items: center;
        }
      }

      .mainContent {
        height: 350px;
        display: flex;
        flex-direction: column;
        background-color: #f9f9f9;
        flex: 10;
        justify-content: space-between;

        .agentmain {
          display: flex;
          flex-direction: row;
          align-items: baseline;
          padding: 10px;

          .avator {
          }

          .box {
            display: flex;
            flex-direction: column;
            margin-left: 5px;
            width: 70%;

            .box_role {
              font-size: 11px;
            }

            .box_content {
              margin-top: 0.5rem;
              padding: 0.5rem;
              color: #000;
              font-size: 0.75rem;
              border-radius: 0.2rem 0.5rem 0.5rem 0.5rem;
              box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.15);
              display: flex;
              flex-direction: column;
              overflow-wrap: break-word;
            }
          }
        }

        .customermain {
          display: flex;
          flex-direction: row;
          align-items: baseline;
          margin-bottom: 0.75rem;
          justify-content: flex-end;
          padding: 0 10px;

          .avator {
          }

          .box {
            display: flex;
            flex-direction: column;
            margin-right: 5px;
            width: 70%;

            .box_role {
              font-size: 11px;
              text-align: end;
            }

            .box_content {
              margin-top: 0.5rem;
              padding: 0.5rem;
              color: #fff;
              font-size: 0.75rem;
              border-radius: 0.5rem 0.2rem 0.5rem 0.5rem;
              box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.15);
              overflow-wrap: break-word;
            }
          }
        }
      }

      .main {
        height: 350px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #f9f9f9;
        flex: 10;
        padding-bottom: 40px;

        .loginForm {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;

          :global {
            .ant-form {
              width: 192px;
            }

            .ant-form-item {
              margin-bottom: 40px;
            }

            .ant-form-item:nth-child(1) {
              margin-top: 8px;
              margin-bottom: 8px;
            }

            .ant-form-item-required {
              color: #333;
              font-size: 0.8rem;
            }

            .ant-input-affix-wrapper {
              border-radius: 8px;
              border-color: 1px solid #e6e6e6;
            }

            .ant-input-prefix {
              height: 20px;
            }

            .ant-input {
              font-size: 9px !important;
              height: 20px;
            }

            .ant-form-item-label {
              padding: 5px !important;
            }

            .ant-form-item-label > label {
              font-size: 10px !important;
            }

            .ant-btn {
              border-radius: 4px;
              height: 2.25rem;
              width: 90px;
              font-size: 1rem;
              font-weight: 500;
              color: #fff;
            }

            .ant-btn:hover {
              /* 取消按钮的默认 hover 样式 */
              background-color: #ad30e5;
              color: #fff;
            }
          }
        }
      }
    }
  }

  :global {
    .ant-upload-list-item-actions {
      .ant-btn {
        border: 0;
      }

      a {
        display: none;
      }
    }
  }
}

.liveChatChannelConfigurationContent::-webkit-scrollbar {
  display: none;
  /* Chrome Safari */
}

.fs12 {
  font-size: 12px;
}

.color999 {
  color: #999;
}

.mt_10 {
  margin-top: -10px !important;
  margin-bottom: 20px;
}

.mt_10_10 {
  margin-top: -10px !important;
}

.mb_20 {
  margin-bottom: 20px;
}

.mb_0 {
  margin-bottom: 0;
}

.service {
  :global {
    .sketch-picker {
      position: absolute;
      z-index: 99;
      top: 180px;
      left: 200px;
    }
  }
}

.serviceApp {
  position: absolute;
  z-index: 99;
  top: -40px;
  left: 250px;
}

.theme {
  :global {
    .sketch-picker {
      position: absolute;
      z-index: 99;
      top: 80px;
      left: 200px;
    }
  }
}

.themeApp {
  position: absolute;
  z-index: 99;
  left: 250px;
  top: -150px;
}

.message {
  :global {
    .sketch-picker {
      position: absolute;
      z-index: 99;
      top: 280px;
      left: 200px;
    }
  }
}

.messageApp {
  position: absolute;
  z-index: 99;
  top: 70px;
  left: 250px;
}

.content3ModalBox {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;

  img {
    width: 145px;
    margin-bottom: 20px;
  }

  .footerContent {
    text-align: center;

    .cancel {
      margin-right: 20px;
    }
  }

  p:nth-of-type(1) {
    color: #333;
    text-align: center;
    font-family: 'Poppins', sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
  }

  p:nth-of-type(2) {
    color: #999;
    text-align: center;
    font-family: 'Poppins', sans-serif;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
    margin-bottom: 20px;
  }
}
