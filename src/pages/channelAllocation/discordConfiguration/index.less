.discordChannelConfiguration {
  margin: 20px;
  background: #fff;
  height: 88vh;
  //min-height: 88vh;
  padding: 20px;
  overflow: hidden;
  overflow-y: scroll;
  /* 隐藏滚动条 */
  scrollbar-width: none;
  /* firefox */
  -ms-overflow-style: none;
  .discordChannelTitle {
    width: 100%;
    float: left;
    margin-bottom: 45px;
    .discordChannelIcon {
      width: 40px;
      float: left;
      margin-right: 12px;
      margin-bottom: 20px;
    }
    p {
      font-size: 20px;
      font-style: normal;
      font-weight: 400;
      line-height: 40px;
      margin-bottom: 0px;
    }
    span {
      color: #999;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
    }
  }
  .discordChannelContent {
    width: 100%;

    .customerServiceModeTips {
      color: #999;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      margin-bottom: 20px;
      margin-top: -10px;
    }
    .lineWebhookConfiguration {
      border-radius: 4px;
      background: #f0f0f0;
      padding: 10px;
      color: #000;
      font-size: 12px;
      .copyContent {
        background-color: #3463fc;
        display: inline-block;
        border-radius: 0 4px 0 4px;
        color: #fff;
        padding: 2px 6px;
        float: right;
        margin-right: -10px;
        margin-top: -10px;
        cursor: pointer;
      }
    }
    .channelNameText {
      color: #333;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      margin-top: -10px;
      margin-bottom: 20px;
    }

    .fs12 {
      font-size: 12px;
    }

    .color999 {
      color: #999;
    }

    .mt_10 {
      margin-top: -10px !important;
      margin-bottom: 20px;
    }

    .mt_10_10 {
      margin-top: -10px !important;
    }

    .mb_20 {
      margin-bottom: 20px;
    }

    .mb_00 {
      margin-bottom: 0px;
    }

    :global {
      .ant-input {
        border-radius: 6px;
        //border: 1px solid #e6e6e6;
        background: #fff;
        width: 100%;
        font-size: 12px;
        box-shadow: none !important;
      }
      .ant-select {
        //border: 1px solid #e6e6e6;
        background: #fff;
        width: 100%;
        font-size: 12px;
        box-shadow: none !important;
      }
      .ant-select:not(.ant-select-customize-input) .ant-select-selector {
        border-radius: 6px;
      }
      .ant-input-affix-wrapper {
        padding: 0px 11px;
        border-radius: 6px;
        //border: 1px solid #e6e6e6;
        background: #fff;
        width: 100%;
        font-size: 12px;
        box-shadow: none !important;
        .ant-input {
          border: none;
          background: #fff;
          width: 100%;
          font-size: 12px;
          box-shadow: none !important;
        }
      }
      .ant-steps-item-wait
        > .ant-steps-item-container
        > .ant-steps-item-content
        > .ant-steps-item-title,
      .ant-steps-item-finish
        > .ant-steps-item-container
        > .ant-steps-item-content
        > .ant-steps-item-title,
      .ant-steps-item-process
        > .ant-steps-item-container
        > .ant-steps-item-content
        > .ant-steps-item-title {
        color: #333 !important;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
      }
      .ant-steps-item-subtitle {
        color: #999 !important;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        margin-left: 0px;
      }
      .ant-form-item {
        margin-bottom: 15px;
      }
      .ant-steps-item-process
        > .ant-steps-item-container
        > .ant-steps-item-icon {
        background: #3463fc;
      }
      .ant-steps-item-process .ant-steps-item-icon {
        border-color: #3463fc;
      }
      .ant-steps-item-icon {
        width: 40px;
        height: 40px;
        font-size: 20px;
        font-weight: 700;
      }
      .ant-steps-item-icon .ant-steps-icon {
        top: 2.5px;
      }
      .ant-steps-vertical
        > .ant-steps-item
        > .ant-steps-item-container
        > .ant-steps-item-tail {
        left: 20px;
        padding-top: 48px;
      }
      .ant-steps-item-wait .ant-steps-item-icon {
        background: #e6e6e6 !important;
        border-color: #e6e6e6 !important;
        box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.1);
      }
      .ant-steps-item-wait .ant-steps-item-icon > .ant-steps-icon {
        color: #333 !important;
        font-size: 20px;
        font-style: normal;
        font-weight: 400;
      }
      .ant-steps-item-finish .ant-steps-item-icon {
        background-color: #13c825 !important;
        border-color: #13c825 !important;
      }
      .ant-steps-item-finish .ant-steps-item-icon > .ant-steps-icon {
        color: #fff !important;
      }
      .ant-steps-item-finish
        > .ant-steps-item-container
        > .ant-steps-item-tail::after {
        background-color: #e6e6e6 !important;
      }
      .ant-steps-vertical > .ant-steps-item .ant-steps-item-title {
        //line-height: 42px;
        display: flex;
        flex-direction: column;
        line-height: 26px !important;
      }
      .ant-steps-item-description {
        margin-top: 15px;
      }
      .ant-form-item-label
        > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
        display: none;
      }
      .ant-radio-group {
        font-size: 12px;
        color: #333;
      }
      .ant-form label {
        font-size: 12px;
      }
      .ant-radio-checked .ant-radio-inner {
        border-color: #3463fc;
      }
      .ant-radio-inner::after {
        background-color: #3463fc;
      }
      .ant-tag {
        font-size: 12px;
        margin-top: 5px;
      }
    }
  }
}
.discordChannelConfiguration::-webkit-scrollbar {
  display: none;
  /* Chrome Safari */
}
