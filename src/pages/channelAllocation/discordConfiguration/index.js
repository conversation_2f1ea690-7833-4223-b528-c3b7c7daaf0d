import React, { useState, useRef, useEffect } from 'react';
import {
  Spin,
  Steps,
  Form,
  Col,
  Input,
  Row,
  Radio,
  Tag,
  Tooltip,
  notification,
  Checkbox,
  Select,
  message,
  Button,
} from 'antd';
import styles from './index.less';
import {
  useDispatch,
  getIntl,
  useSelector,
  Link,
  FormattedMessage,
  history,
} from 'umi';
import { convertBoolean, transformObject } from '@/utils/utils';
import HOCAuth from '@/components/HOCAuth/index';
import { CopyToClipboard } from 'react-copy-to-clipboard';
import NewDiscordChannelIcon from '../../../assets/new-discord-icon.svg';

const { StepItem } = Steps;
const { TextArea } = Input;

const DiscordConfiguration = () => {
  const dispatch = useDispatch();
  let formAddChannelRef = useRef(null);
  const [spinning, setSpinning] = useState(false);
  const [btnLoading, setBtnLoading] = useState(false);
  // 进度条值
  const [current, setCurrent] = useState(0);
  // 进度条完成值
  const [completedSteps, setCompletedSteps] = useState(0);
  // 客服模式值
  const [customerServiceModeValue, setCustomerServiceModeValue] = useState('1');
  // 人工关键词input值
  const [menuToAgentTxt, setMenuToAgentTxt] = useState('');
  // 文档知识库开关值
  const [knowledgeCheckValue, setKnowledgeCheckValue] = useState(true);
  // 人工关键词tag
  const [termsTags, setTermsTags] = useState([
    'transfer to an agent',
    'agent',
    'to an agent',
    'need agent',
    'agent help',
    'need human',
    'human',
    '座席',
    '人工',
    '转座席',
    '转人工',
    '人工服务',
    '不要机器人',
  ]);
  // 知识库ID
  const [knowledgeId, setKnowledgeId] = useState('');
  // 知识库列表
  const [knowledgeBaseList, setKnowledgeBaseList] = useState([]);
  // 问答风格值
  const [emotionalVersionValue, setEmotionalVersionValue] = useState('0');
  // 机器人回复不知道
  const [knowledgeUnknownReply, setKnowledgeUnknownReply] = useState('');
  // 工作时间下拉列表
  const [workTimeList, setWorkTimeList] = useState([]);
  // 选择的工作时间
  const [agentWorkTimeId, setAgentWorkTimeId] = useState('');
  // 座席不在线时的回复
  const [agentNotWorkTimeReply, setAgentNotWorkTimeReply] = useState('');
  // 是否开启AiAgent
  const [isOpenAiagent, setIsOpenAiagent] = useState('');
  const [channelDetailList, setChannelDetailList] = useState({});
  const [applicationId, setApplicationId] = useState('');
  const [channelBotToken, setChannelBotToken] = useState('');
  const [channelBotName, setChannelBotName] = useState('');
  // 渠道名称
  const [channelName, setChannelName] = useState('');
  // 是否推送座席加入消息
  const [isAgentJoinFlag, setIsAgentJoinFlag] = useState('');
  const { closeBetaPermission } = useSelector(
    ({ whatsAppChannelConfiguration, layouts, chatChannelConfiguration }) => ({
      closeBetaPermission: layouts.user.closeBetaPermission,
    }),
  );

  useEffect(() => {
    //调用回显接口
    if (history.location.query.channelId) {
      channelDetails(history.location.query.channelId);
    }

    queryDocumentKnowledge();
    channelWorkTimeList();
  }, []);

  // 切换是否推送坐席加入消息
  const changeAgentJoinFlag = e => {
    setIsAgentJoinFlag(e.target.checked);
  };

  // 工作时间下拉
  const channelWorkTimeList = () => {
    dispatch({
      type: 'workOrderExtension/channelWorkTimeList',
      callback: res => {
        if (res.code === 200) {
          setWorkTimeList(res.data);
        } else {
          notification.error({
            message: res.msg,
          });
        }
      },
    });
  };
  // 查询知识库列表
  const queryDocumentKnowledge = () => {
    setSpinning(true);

    dispatch({
      type: 'documentKnowledgeBase/queryDocumentKnowledge',
      payload: { knowledgeType: 2 },
      callback: response => {
        setSpinning(false);

        if (response.code == 200) {
          setKnowledgeBaseList(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 查询详情回显
  const channelDetails = id => {
    setSpinning(true);

    dispatch({
      type: 'amazonRegionConfiguration/channelDetails',
      payload: { id },
      callback: async response => {
        let { code, data, msg } = response;
        setSpinning(false);

        if (code === 200) {
          if (data) {
            let config = {};
            data.config.forEach(item => {
              config[item.code] = item.name;
            });
            // 渠道名称
            let channelNameDetail = data.name;
            // 应用ID
            let applicationId = config.applicationId;
            // 机器人 Name
            let botName = config.botName;
            // bot Token
            let botToken = config.botToken;
            // 客服模式
            let customerModeValue = config.chat_open_channel_type;
            // 人工关键词
            let keyWords = config.menu_to_agent_txt;
            // 问答风格
            let moonVersion = config.mood_board_flag;
            // 是否开启知识库
            let knowledgeCheckValue = config.doc_bot_flag;
            // 知识库
            let knowledgeIdDetail = config.document_knowledge_base_id;
            // 机器人不知道
            let botUnaware = config.knowledge_unknown_reply;
            let webhookUrl = config.line_webhook_url;
            let channelDetailListNew = {
              channelName: channelNameDetail,
              applicationId: applicationId,
              botName: botName,
              botToken: botToken,
              customerServiceMode: customerModeValue,
              knowledgeCheck: convertBoolean(knowledgeCheckValue),
              emotionalVersion: moonVersion,
              knowledgeId: knowledgeIdDetail,
              knowledgeUnknownReply: botUnaware,
              agent_not_worktime_reply: config.agent_not_worktime_reply,
              agent_worktime_id: config.agent_worktime_id,
              is_open_aiagent: convertBoolean(config.is_open_aiagent),
              push_system_message_flag: convertBoolean(
                config.push_system_message_flag,
              ),
            };
            formAddChannelRef.current?.setFieldsValue(channelDetailListNew);
            setTermsTags(keyWords ? keyWords.split(',') : []);
            setChannelName(data.name);
            setApplicationId(applicationId);
            setChannelBotName(botName);
            setChannelBotToken(botToken);
            setCustomerServiceModeValue(customerModeValue);
            setKnowledgeCheckValue(convertBoolean(knowledgeCheckValue));
            setKnowledgeId(knowledgeIdDetail);
            setEmotionalVersionValue(moonVersion);
            setKnowledgeUnknownReply(botUnaware);
            setCompletedSteps(3);
            setAgentNotWorkTimeReply(config.agent_not_worktime_reply);
            setAgentWorkTimeId(config.agent_worktime_id);
            // setCurrent(3);
            setChannelDetailList(channelDetailListNew);
            setIsOpenAiagent(convertBoolean(config.is_open_aiagent));
            setIsAgentJoinFlag(convertBoolean(config.push_system_message_flag));
          }
        } else {
          notification.warning({
            message: getIntl().formatMessage({
              id: msg,
            }),
          });
        }
      },
    });
  };

  // 切换步骤
  const onChangeSteps = value => {
    if (history.location.query.channelId) {
      formAddChannelRef.current?.setFieldsValue(channelDetailList);
    }
    //跳步骤时校验是否填了
    let error = false;
    formAddChannelRef.current.getFieldsError().forEach(item => {
      if (item.errors.length > 0) {
        error = true;
      }
    });
    //跳步骤时做好保存
    if (completedSteps >= value && !error) {
      setCurrent(value);
    }
  };
  // 下一步事件
  const onFinish = values => {
    if (current == 0) {
      setCurrent(current + 1);
      setCompletedSteps(completedSteps + 1);
      setApplicationId(values.applicationId);
      setChannelBotName(values.botName);
      setChannelBotToken(values.botToken);
    } else if (current == 1) {
      setCurrent(current + 1);
      setCompletedSteps(completedSteps + 1);
      setCustomerServiceModeValue(values.customerServiceMode);
      setKnowledgeCheckValue(values.knowledgeCheck);
      setEmotionalVersionValue(values.emotionalVersion);
      setKnowledgeId(values.knowledgeId);
      setKnowledgeUnknownReply(values.knowledgeUnknownReply);
      setAgentNotWorkTimeReply(values.agent_not_worktime_reply);
      setAgentWorkTimeId(values.agent_worktime_id);
    } else {
      let params = {
        name: channelName,
        channelType: 25,
        channelSubtype: 1,
        connectId: '',
        isOpenConnect: 0,
        config: [
          {
            code: 'chat_open_channel_type',
            name: customerServiceModeValue,
          },
          {
            code: 'doc_bot_flag',
            name:
              customerServiceModeValue !== '3'
                ? knowledgeCheckValue
                  ? 1
                  : 0
                : '',
          },
          {
            code: 'bot_name',
            name: 'CoCo',
          },
          {
            code: 'document_knowledge_base_id',
            name: knowledgeCheckValue ? knowledgeId : '',
          },
          {
            code: 'menu_to_agent_txt',
            name:
              customerServiceModeValue !== '3' &&
              customerServiceModeValue !== '2'
                ? termsTags?.join(',')
                : '',
          },
          {
            code: 'knowledge_unknown_reply',
            name: customerServiceModeValue !== '3' ? knowledgeUnknownReply : '',
          },
          {
            code: 'mood_board_flag',
            name: emotionalVersionValue,
          },
          {
            code: 'agent_not_worktime_reply',
            name: agentNotWorkTimeReply,
          },
          {
            code: 'agent_worktime_id',
            name: agentWorkTimeId,
          },
          {
            code: 'is_open_aiagent',
            name: isOpenAiagent ? 1 : 0,
          },
          {
            code: 'push_system_message_flag',
            name: isAgentJoinFlag ? 1 : 0,
          },
          {
            code: 'applicationId',
            name: applicationId,
          },
          {
            code: 'botName',
            name: channelBotName,
          },
          {
            code: 'botToken',
            name: channelBotToken,
          },
        ],
      };

      console.log('-----------params------------', params);

      // return;
      setSpinning(true);
      setBtnLoading(true);
      if (history.location.query.channelId) {
        dispatch({
          type: 'amazonRegionConfiguration/updateChannel',
          payload: {
            data: params,
            id: history.location.query.channelId,
          },
          callback: response => {
            setSpinning(false);
            let { code, data, msg } = response;
            if (code === 200) {
              notification.success({
                message: getIntl().formatMessage({
                  id: 'customer.ext.info.save.success',
                }),
              });
              setBtnLoading(false);
              history.push({
                pathname: '/channelConfigurationDetailList',
                state: {
                  channelType: 25,
                },
              });
              localStorage.setItem('newChannelType', 25);
            } else {
              setBtnLoading(false);

              notification.error({
                message: response.msg,
              });
            }
          },
        });
      } else {
        dispatch({
          type: 'amazonRegionConfiguration/createChannel',
          payload: params,
          callback: response => {
            setSpinning(false);
            let { code, data, msg } = response;
            if (code === 200) {
              notification.success({
                message: getIntl().formatMessage({
                  id: 'customer.ext.info.save.success',
                }),
              });
              setBtnLoading(false);
              history.push({
                pathname: '/channelConfigurationDetailList',
                state: {
                  channelType: 25,
                },
              });
              localStorage.setItem('newChannelType', 25);
            } else {
              setBtnLoading(false);

              notification.error({
                message: response.msg,
              });
            }
          },
        });
      }
    }
  };

  const handleInputApplicationIdChange = e => {
    setApplicationId(e.target.value);
  };
  // 获取BotToken
  const handleInputChange = e => {
    setChannelBotToken(e.target.value);
  };
  // 获取机器人名称
  const handleInputBotNameChange = e => {
    setChannelBotName(e.target.value);
  };
  // 获取渠道名称
  const handleInputNameChange = e => {
    setChannelName(e.target.value);
  };

  // 获取是否开启AiAgent
  const handleChangeAiAgent = e => {
    setIsOpenAiagent(e.target.checked);
    setCustomerServiceModeValue('1');
    formAddChannelRef.current?.setFieldsValue({ customerServiceMode: '1' });
  };
  // 切换客服模式
  const onChangeCustomerServiceMode = e => {
    setCustomerServiceModeValue(e.target.value);
  };
  const handleInputChangeWorkers = e => {
    setMenuToAgentTxt(e.target.value);
  };
  const handleEnterPress = e => {
    // 中文输入状态下按下回车键判断
    if (e.key === 'Enter') {
      if (!e.nativeEvent.isComposing) {
        // 直接按下回车键
        handleInputConfirm(e);
      }
    }
  };
  const handleInputConfirm = e => {
    if (termsTags && termsTags.length < 20) {
      if (menuToAgentTxt?.trim() && termsTags?.indexOf(menuToAgentTxt) === -1) {
        setTermsTags([...termsTags, menuToAgentTxt]);
      }
    } else {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'definition.synonyms.add.synonym.rules.number.tips.20',
          defaultValue: '最多添加20个标签！',
        }),
      });
    }
    setMenuToAgentTxt('');
    e.preventDefault();
  };
  /**
   * 删除标签
   */
  const handleClose = removedTag => {
    const newTags = termsTags.filter(tag => tag !== removedTag);
    setTermsTags(newTags);
  };
  // 开启关闭知识库
  const onChangeKnowledgeCheck = e => {
    setKnowledgeCheckValue(e.target.checked);
  };
  // 切换知识库
  const onChangeKnowledge = value => {
    setKnowledgeId(value);
  };
  // 切换问答风格
  const onChangeEmotionalVersion = e => {
    setEmotionalVersionValue(e.target.value);
  };
  // 取消创建渠道
  const handleCancel = () => {
    history.push({
      pathname: '/channelConfigurationDetailList',
      state: {
        channelType: 25,
      },
    });
    localStorage.setItem('newChannelType', 25);
  };

  return (
    <div className={styles.discordChannelConfiguration}>
      <Spin spinning={spinning}>
        <div className={styles.discordChannelTitle}>
          <img
            className={styles.discordChannelIcon}
            src={NewDiscordChannelIcon}
          />
          <p>
            {history.location.query.channelId ? (
              <FormattedMessage
                id="editor.discord.channel.configuration.title"
                defaultMessage="修改 Discord 渠道"
              />
            ) : (
              <FormattedMessage
                id="add.discord.channel.configuration.title"
                defaultMessage="添加 Discord 渠道"
              />
            )}
          </p>
          <span>
            <FormattedMessage
              id="add.discord.channel.configuration.tips"
              defaultMessage="请添加 Discord 渠道"
            />
          </span>
        </div>
        <div className={styles.discordChannelContent}>
          <Form
            name="basic"
            layout="vertical"
            onFinish={onFinish}
            autoComplete="off"
            ref={formAddChannelRef}
            initialValues={{
              knowledgeCheck: knowledgeCheckValue,
              customerServiceMode: customerServiceModeValue,
              emotionalVersion: emotionalVersionValue,
            }}
          >
            <Steps
              current={current}
              onChange={onChangeSteps}
              direction="vertical"
            >
              <StepItem
                title={getIntl().formatMessage({
                  id: 'discord.channel.configuration.title.1',
                  defaultValue: '配置 Discord Bot Token',
                })}
                subTitle={
                  <>
                    {getIntl().formatMessage({
                      id: 'discord.channel.configuration.title.tips.1',
                      defaultValue:
                        '将 Discord 开发者后台 Bot Token 信息填入下方对应的输入框，',
                    })}
                    {/*<span style={{ color: '#3463FC' }}>*/}
                    {/*  <FormattedMessage*/}
                    {/*    id="discord.channel.configuration.title.tips.2"*/}
                    {/*    defaultMessage="查看配置文档"*/}
                    {/*  />*/}
                    {/*</span>*/}
                    <Link to={'/discordHelpDocument'} target={'_blank'}>
                      <FormattedMessage
                        id="discord.channel.configuration.title.tips.2"
                        defaultMessage="查看配置文档"
                      />
                    </Link>
                  </>
                }
                description={
                  <Content
                    current={current}
                    completedSteps={completedSteps}
                    handleInputChange={handleInputChange}
                    handleInputBotNameChange={handleInputBotNameChange}
                    handleInputApplicationIdChange={
                      handleInputApplicationIdChange
                    }
                    chatChannelId={history.location.query.channelId}
                  />
                }
                status={
                  current === 0
                    ? 'process'
                    : current > 0 || completedSteps >= 0
                    ? 'finish'
                    : ''
                }
              ></StepItem>
              <StepItem
                title={getIntl().formatMessage({
                  id: 'line.channel.configuration.title.3',
                  defaultValue: '智能客服设置',
                })}
                subTitle={getIntl().formatMessage({
                  id: 'line.channel.configuration.title.tips.3',
                  defaultValue: '在这里您可以配置智能客服的相关信息',
                })}
                description={
                  <Content1
                    handleChangeAiAgent={handleChangeAiAgent}
                    closeBetaPermission={closeBetaPermission}
                    current={current}
                    completedSteps={completedSteps}
                    termsTags={termsTags}
                    workTimeList={workTimeList}
                    customerServiceModeValue={customerServiceModeValue}
                    menuToAgentTxt={menuToAgentTxt}
                    knowledgeCheckValue={knowledgeCheckValue}
                    knowledgeBaseList={knowledgeBaseList
                      ?.map(item => {
                        if (item.status == 2) {
                          return {
                            label: item.knowledgeName,
                            value: item.knowledgeId,
                            key: item.knowledgeId,
                          };
                        }
                      })
                      ?.filter(item => item !== undefined)}
                    knowledgeId={knowledgeId}
                    knowledgeUnknownReply={knowledgeUnknownReply}
                    onChangeCustomerServiceMode={onChangeCustomerServiceMode}
                    handleInputChangeWorkers={handleInputChangeWorkers}
                    handleEnterPress={handleEnterPress}
                    handleClose={handleClose}
                    onChangeKnowledgeCheck={onChangeKnowledgeCheck}
                    onChangeKnowledge={onChangeKnowledge}
                    onChangeEmotionalVersion={onChangeEmotionalVersion}
                    isOpenAiagent={isOpenAiagent}
                    changeAgentJoinFlag={changeAgentJoinFlag}
                    chatChannelId={history.location.query.channelId}
                  />
                }
                status={
                  current === 1
                    ? 'process'
                    : current > 1 || (current < 1 && completedSteps > 1)
                    ? 'finish'
                    : current < 1 && completedSteps < 1
                    ? 'wait'
                    : ''
                }
              ></StepItem>
              <StepItem
                title={getIntl().formatMessage({
                  id: 'chat.channel.configuration.title.1',
                  defaultValue: '填写渠道基本信息',
                })}
                subTitle={getIntl().formatMessage({
                  id: 'amazonRegion.channel.configuration.title.tips.5',
                  defaultValue: '填写基本的渠道信息',
                })}
                description={
                  <Content2
                    current={current}
                    completedSteps={completedSteps}
                    handleInputNameChange={handleInputNameChange}
                  />
                }
                status={
                  current === 2
                    ? 'process'
                    : current > 2 || (current < 2 && completedSteps > 2)
                    ? 'finish'
                    : current < 2 && completedSteps < 2
                    ? 'wait'
                    : ''
                }
              ></StepItem>
            </Steps>
            <Row gutter={24} style={{ textAlign: 'center', marginTop: '20px' }}>
              <Col span={24}>
                <Form.Item>
                  <Button
                    onClick={handleCancel}
                    style={{ marginRight: '12px' }}
                  >
                    <FormattedMessage
                      id="email.channel.configuration.cancel.btn"
                      defaultMessage="取消"
                    />
                  </Button>
                  <Button loading={btnLoading} type="primary" htmlType="submit">
                    {current !== 2
                      ? getIntl().formatMessage({
                          id: 'email.channel.configuration.next.btn',
                          defaultValue: '下一步',
                        })
                      : getIntl().formatMessage({
                          id: 'email.channel.configuration.complete.btn',
                          defaultValue: '完成',
                        })}
                  </Button>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
      </Spin>
    </div>
  );
};

// 步骤一
const Content = props => {
  if (props.current == 0) {
    return (
      <div>
        <Row gutter={24}>
          <Col span={10}>
            <Form.Item
              label={getIntl().formatMessage({
                id: 'discord.channel.configuration.channel.application.id',
                defaultValue: '应用ID：',
              })}
              name="applicationId"
              rules={[
                {
                  required: true,
                  message: getIntl().formatMessage({
                    id:
                      'discord.channel.configuration.channel.application.id.placeholder',
                    defaultValue: '请输入应用ID',
                  }),
                },
              ]}
            >
              <Input
                name="applicationId"
                placeholder={getIntl().formatMessage({
                  id:
                    'discord.channel.configuration.channel.application.id.placeholder',
                  defaultValue: '请输入应用ID',
                })}
                onChange={props.handleInputApplicationIdChange}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={10}>
            <Form.Item
              label={getIntl().formatMessage({
                id: 'discord.channel.configuration.channel.bot.name',
                defaultValue: '机器人名称',
              })}
              name="botName"
              rules={[
                {
                  required: true,
                  message: getIntl().formatMessage({
                    id:
                      'discord.channel.configuration.channel.bot.name.placeholder',
                    defaultValue: '请输入机器人名称',
                  }),
                },
              ]}
            >
              <Input
                name="channelName"
                placeholder={getIntl().formatMessage({
                  id:
                    'discord.channel.configuration.channel.bot.name.placeholder',
                  defaultValue: '请输入机器人名称',
                })}
                onChange={props.handleInputBotNameChange}
              />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={10}>
            <Form.Item
              label={getIntl().formatMessage({
                id: 'discord.channel.configuration.channel.bot.token',
                defaultValue: 'Bot Token',
              })}
              name="botToken"
              rules={[
                {
                  required: true,
                  message: getIntl().formatMessage({
                    id:
                      'discord.channel.configuration.channel.bot.token.placeholder',
                    defaultValue: '请输入Bot Token',
                  }),
                },
              ]}
            >
              <Input.Password
                name="channelName"
                placeholder={getIntl().formatMessage({
                  id:
                    'discord.channel.configuration.channel.bot.token.placeholder',
                  defaultValue: '请输入Bot Token',
                })}
                onChange={props.handleInputChange}
              />
            </Form.Item>
          </Col>
        </Row>
      </div>
    );
  } else if (props.completedSteps > 0) {
    return (
      <div className={styles.channelNameText}>
        <Row gutter={24}>
          <Col span={10}>
            <FormattedMessage
              id="discord.channel.allocation.complete.1"
              defaultMessage="您已经成功输入 Bot Token，请继续完成智能客服配置。"
            />
          </Col>
        </Row>
      </div>
    );
  } else {
    return null;
  }
};
// 步骤二
const Content1 = props => {
  if (props.current == 1) {
    return (
      <div>
        {props.closeBetaPermission === 1 ? (
          <Row gutter={24}>
            <Col span={10}>
              <Form.Item
                label={getIntl().formatMessage({
                  id: 'chat.channel.configuration.chat1.ai.aiagent',
                  defaultValue: '开启AI Agent',
                })}
                name="is_open_aiagent"
                valuePropName="checked"
              >
                <Checkbox
                  disabled={props.chatChannelId}
                  onChange={props.handleChangeAiAgent}
                >
                  <FormattedMessage
                    id="chat.channel.configuration.work.panels.checkbox"
                    defaultMessage="开启"
                  />
                </Checkbox>
              </Form.Item>
              <p
                className={`${styles.fs12} ${styles.color999} ${styles.mt_10}`}
              >
                <FormattedMessage
                  id="chat.channel.configuration.chat1.ai.aiagent.tips"
                  defaultMessage="开启AI Agent之后，欢迎用语以及AIGC知识库的选择需要到AI Agent页面进行对应的配置。"
                />
              </p>
            </Col>
          </Row>
        ) : (
          ''
        )}
        <Row gutter={24}>
          <Col span={10}>
            <Form.Item
              label={getIntl().formatMessage({
                id: 'line.channel.configuration.customer.service.mode',
                defaultValue: '客服模式',
              })}
              name="customerServiceMode"
            >
              <Radio.Group
                defaultValue
                onChange={props.onChangeCustomerServiceMode}
              >
                <HOCAuth authKey={'automatically_create_tickets'}>
                  {authAccess => (
                    <Radio value={'1'} disabled={authAccess}>
                      <FormattedMessage
                        id="chat.channel.configuration.chat4.mode.1"
                        defaultMessage="智能客服"
                      />
                    </Radio>
                  )}
                </HOCAuth>
                {!props.isOpenAiagent && (
                  <Radio value={'3'}>
                    <FormattedMessage
                      id="chat.channel.configuration.chat4.mode.2"
                      defaultMessage="仅人工"
                    />
                  </Radio>
                )}
                {!props.isOpenAiagent && (
                  <HOCAuth authKey={'automatically_create_tickets'}>
                    {authAccess => (
                      <Radio value={'2'} disabled={authAccess}>
                        <FormattedMessage
                          id="chat.channel.configuration.chat4.mode.3"
                          defaultMessage="仅机器人"
                        />
                      </Radio>
                    )}
                  </HOCAuth>
                )}
              </Radio.Group>
            </Form.Item>
            <div className={styles.customerServiceModeTips}>
              <FormattedMessage
                id="line.channel.configuration.customer.service.mode.tips"
                defaultMessage="智能客服会先有机器人应答，机器人回答不了的问题，用户可以随时转人工"
              />
            </div>
          </Col>
        </Row>
        {props.customerServiceModeValue == '1' ? (
          <Row gutter={24}>
            <Col span={10}>
              <Form.Item
                label={getIntl().formatMessage({
                  id: 'chat.channel.configuration.chat4.workers.keyword',
                  defaultValue: '转人工关键词',
                })}
                name="workersKeyword"
              >
                <Input
                  style={{ marginBottom: '5px' }}
                  type="text"
                  value={props.menuToAgentTxt}
                  maxLength={40}
                  onChange={props.handleInputChangeWorkers}
                  onKeyDown={props.handleEnterPress}
                  placeholder={getIntl().formatMessage({
                    id: 'definition.synonyms.input.placeholder',
                    defaultValue: '输入回车生成标签，最长20字符',
                  })}
                />
                {props.termsTags?.map((tag, index) => {
                  const isLongTag = tag.length > 20;
                  const tagElem = (
                    <Tag
                      className="edit-tag"
                      key={tag}
                      // closable={index !== 0}
                      closable
                      onClose={() => props.handleClose(tag)}
                    >
                      <span>{isLongTag ? `${tag.slice(0, 20)}...` : tag}</span>
                    </Tag>
                  );
                  return isLongTag ? (
                    <Tooltip title={tag} key={tag}>
                      {tagElem}
                    </Tooltip>
                  ) : (
                    tagElem
                  );
                })}
              </Form.Item>
              <div className={styles.customerServiceModeTips}>
                <FormattedMessage
                  id="chat.channel.configuration.chat4.workers.keyword.message"
                  defaultMessage="客户输入此关键词后会转人工客服"
                />
              </div>
            </Col>
          </Row>
        ) : (
          ''
        )}

        {props.customerServiceModeValue != '3' && !props.isOpenAiagent ? (
          <Row gutter={24}>
            <Col span={10}>
              <Form.Item
                label={getIntl().formatMessage({
                  id: 'whatsApp.channel.configuration.chat4.document',
                  defaultValue: '文档知识库',
                })}
                name="knowledgeCheck"
                valuePropName="checked"
              >
                <Checkbox onChange={props.onChangeKnowledgeCheck}>
                  <FormattedMessage
                    id="whatsApp.channel.configuration.work.panels.checkbox"
                    defaultMessage="开启"
                  />
                </Checkbox>
              </Form.Item>
            </Col>
          </Row>
        ) : (
          ''
        )}
        {props.knowledgeCheckValue &&
        props.customerServiceModeValue != '3' &&
        !props.isOpenAiagent ? (
          <Row gutter={24}>
            <Col span={10}>
              <Form.Item
                label={null}
                name="knowledgeId"
                rules={[
                  {
                    required: true,
                    message: getIntl().formatMessage({
                      id:
                        'whatsApp.channel.configuration.chat4.document.placeholder',
                      defaultValue: '请选择文档知识库',
                    }),
                  },
                ]}
              >
                <Select
                  style={{ marginTop: '-10px' }}
                  placeholder={getIntl().formatMessage({
                    id:
                      'whatsApp.channel.configuration.chat4.document.placeholder',
                    defaultValue: '请选择文档知识库',
                  })}
                  value={props.knowledgeId}
                  onChange={props.onChangeKnowledge}
                  options={props.knowledgeBaseList}
                  // filterOption={(inputValue, option) =>
                  //   option.knowledgeName
                  //     .toLowerCase()
                  //     .indexOf(inputValue.toLowerCase()) >= 0
                  // }
                  // fieldNames={{
                  //   label: 'knowledgeName',
                  //   value: 'knowledgeId',
                  //   key: 'knowledgeId',
                  // }}
                />
              </Form.Item>
              <div className={styles.customerServiceModeTips}>
                <span>
                  <FormattedMessage
                    id="whatsApp.channel.configuration.chat4.document.message.1"
                    defaultMessage="此处的文档知识库仅展示外部知识库，请提前到"
                  />
                </span>
                <a
                  href={`https://${process.env.DOMAIN_NAME_OVER}/#/documentKnowledgeBase`}
                  style={{ fontSize: 12, color: '#3463FC' }}
                  target="_blank"
                >
                  <FormattedMessage
                    id="whatsApp.channel.configuration.chat4.document.message"
                    defaultMessage="文档知识库"
                  />
                </a>
                &nbsp;
                <span>
                  <FormattedMessage
                    id="whatsApp.channel.configuration.chat4.document.message.2"
                    defaultMessage="页面进行知识库的配置"
                  />
                </span>
              </div>
            </Col>
          </Row>
        ) : (
          ''
        )}
        {props.customerServiceModeValue != '3' && !props.isOpenAiagent ? (
          <Row gutter={24}>
            <Col span={10}>
              <Form.Item
                label={getIntl().formatMessage({
                  id: 'line.channel.configuration.emotional.version',
                  defaultValue: '问答风格',
                })}
                name="emotionalVersion"
              >
                <Radio.Group
                  onChange={props.onChangeEmotionalVersion}
                  defaultValue
                >
                  <Radio value={'0'}>
                    <FormattedMessage
                      id="line.channel.configuration.emotional.version.concise.clear"
                      defaultMessage="简洁专业"
                    />
                  </Radio>
                  <Radio value={'1'}>
                    <FormattedMessage
                      id="line.channel.configuration.emotional.version.gentle.companionship"
                      defaultMessage="温柔陪伴"
                    />
                  </Radio>
                </Radio.Group>
              </Form.Item>
              <div className={styles.customerServiceModeTips}>
                <span>
                  <FormattedMessage
                    id="line.channel.configuration.emotional.version.tips"
                    defaultMessage="开启温柔陪伴，AIGC Chatbot将会为您的客户提供更温和的回答。"
                  />
                </span>
              </div>
            </Col>
          </Row>
        ) : (
          ''
        )}
        {props.customerServiceModeValue != '3' && !props.isOpenAiagent ? (
          <Row gutter={24}>
            <Col span={10}>
              <Form.Item
                label={getIntl().formatMessage({
                  id: 'whatsApp.channel.configuration.chat4.unknown',
                  defaultValue: '机器人未知问题回复',
                })}
                name="knowledgeUnknownReply"
                rules={[
                  {
                    required: true,
                    message: getIntl().formatMessage({
                      id:
                        'whatsApp.channel.configuration.chat4.unknown.placeholder',
                      defaultValue: '请输入机器人未知问题回复',
                    }),
                  },
                  {
                    min: 1,
                    max: 200,
                  },
                ]}
              >
                <Input
                  defaultValue={props.knowledgeUnknownReply}
                  placeholder={getIntl().formatMessage({
                    id:
                      'whatsApp.channel.configuration.chat4.unknown.placeholder',
                    defaultValue: '请输入机器人未知问题回复',
                  })}
                />
              </Form.Item>
              <div className={styles.customerServiceModeTips}>
                <FormattedMessage
                  id="whatsApp.channel.configuration.chat4.unknown.message"
                  defaultMessage="此处设置是机器人在遇到未知问题时的回复"
                />
              </div>
            </Col>
          </Row>
        ) : (
          ''
        )}
        {props.customerServiceModeValue == '3' ? (
          <Row gutter={24}>
            <Col span={10}>
              <Form.Item
                label={getIntl().formatMessage({
                  id: 'chat.channel.configuration.chat1.push.agents.join.chat',
                  defaultValue: '推送座席加入聊天',
                })}
                name="push_system_message_flag"
                valuePropName="checked"
              >
                <Checkbox onChange={props.changeAgentJoinFlag}>
                  <FormattedMessage
                    id="chat.channel.configuration.work.panels.checkbox"
                    defaultMessage="开启"
                  />
                </Checkbox>
              </Form.Item>
            </Col>
          </Row>
        ) : (
          ''
        )}
        {props.customerServiceModeValue != '2' ? (
          <Row gutter={24}>
            <Col span={10}>
              <Form.Item
                label={getIntl().formatMessage({
                  id: 'chat.channel.configuration.chat4.agent.workTime',
                  defaultValue: '座席工作时间',
                })}
                name="agent_worktime_id"
                rules={[
                  {
                    required: true,
                    message: getIntl().formatMessage({
                      id: 'chat.channel.configuration.chat4.agent.workTime.p',
                      defaultValue: '请选择座席工作时间',
                    }),
                  },
                ]}
              >
                <Select
                  style={{ marginTop: 0 }}
                  placeholder={getIntl().formatMessage({
                    id: 'chat.channel.configuration.chat4.agent.workTime.p',
                    defaultValue: '请选择座席工作时间',
                  })}
                  options={props.workTimeList}
                  fieldNames={{
                    label: 'workTimeName',
                    value: 'workTimeId',
                  }}
                  showSearch
                  filterOption={(inputValue, option) =>
                    option.workTimeName
                      .toLowerCase()
                      .indexOf(inputValue.toLowerCase()) >= 0
                  }
                />
              </Form.Item>
            </Col>
          </Row>
        ) : (
          ''
        )}
        {props.customerServiceModeValue != '2' ? (
          <Row gutter={24}>
            <Col span={10}>
              <Form.Item
                label={getIntl().formatMessage({
                  id: 'chat.channel.configuration.chat4.agent.workTime.no',
                  defaultValue: '不在工作时间机器人回复话术',
                })}
                name="agent_not_worktime_reply"
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <Input maxLength={200} />
              </Form.Item>
              <p
                className={`${styles.fs12} ${styles.color999} ${styles.mt_10}`}
              >
                <FormattedMessage
                  id="chat.channel.configuration.chat4.agent.workTime.no.tips"
                  defaultMessage="当不在座席工作时间，客户转人工咨询时机器人回复的话术"
                />
              </p>
            </Col>
          </Row>
        ) : (
          ''
        )}
      </div>
    );
  } else if (props.completedSteps > 1) {
    return (
      <p className={styles.channelNameText}>
        <FormattedMessage
          id="whatsApp.channel.configuration.chat4.information.configuration.completed"
          defaultMessage="您已完成智能客服的设置，这些将会在您保存后实现。"
        />
      </p>
    );
  } else {
    return null;
  }
};
// 步骤三
const Content2 = props => {
  if (props.current == 2) {
    return (
      <div>
        <Row gutter={24}>
          <Col span={10}>
            <Form.Item
              label={getIntl().formatMessage({
                id: 'email.channel.configuration.channel.name',
                defaultValue: '渠道名称',
              })}
              name="channelName"
              rules={[
                {
                  required: true,
                  message: getIntl().formatMessage({
                    id: 'email.channel.configuration.channel.name.placeholder',
                    defaultValue: '请输入渠道名称',
                  }),
                },
              ]}
            >
              <Input
                name="channelName"
                placeholder={getIntl().formatMessage({
                  id: 'email.channel.configuration.channel.name.placeholder',
                  defaultValue: '请输入渠道名称',
                })}
                onChange={props.handleInputNameChange}
              />
            </Form.Item>
          </Col>
        </Row>
      </div>
    );
  } else {
    return null;
  }
};

export default DiscordConfiguration;
