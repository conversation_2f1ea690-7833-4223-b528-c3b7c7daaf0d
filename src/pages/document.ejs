<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="description" content="包括AIGC,智能工单,智能客服等功能，全球部署，畅通稳定的全球化、一体化、智能化的客户联络中心。">
  <meta name="keywords" content="ConnectNow,云联络中心,智能办公,AWS">
  <meta name="theme-color" content="#8500BB">
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <link rel="manifest" href="/manifest.json">
  <link rel="apple-touch-icon" href="/icons/192.png">
  <title><%= context.config.title %></title>
  <script>
    // // 检查浏览器是否支持Service Worker
    // if ('serviceWorker' in navigator) {
    //   window.addEventListener('load', function() {
    //     navigator.serviceWorker.register('/service-worker.js')
    //       .then(function(registration) {
    //         // 注册成功
    //         console.log('PWA Service Worker 注册成功:', registration.scope);
    //       })
    //       .catch(function(error) {
    //         // 注册失败
    //         console.log('PWA Service Worker 注册失败:', error);
    //       });
    //   });
    // }

    // 添加到主屏幕相关逻辑
    let deferredPrompt;
    window.addEventListener('beforeinstallprompt', (e) => {
      // 阻止Chrome 67及更早版本自动显示安装提示
      e.preventDefault();
      // 存储事件以便稍后触发
      deferredPrompt = e;

      // 可以在这里添加自定义的"添加到主屏幕"按钮逻辑
    });

    // 检测PWA是否已安装
    window.addEventListener('appinstalled', (evt) => {
      console.log('PWA已成功安装');
    });
  </script>
</head>
<body>
  <div id="root"></div>
</body>
</html>
