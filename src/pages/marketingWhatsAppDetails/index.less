.marketingDetailsContent {
  margin: 20px 20px 0px 20px;
  height: 88vh;
  overflow: hidden;
  overflow-y: scroll;
  /* 隐藏滚动条 */
  scrollbar-width: none;
  /* firefox */
  -ms-overflow-style: none;

  .topSelectContent {
    width: 100%;
    height: 172px;
    padding: 20px;
    background: #ffffff;
    box-shadow: 0px 2px 30px 0px rgba(0, 0, 0, 0.02);
    border-radius: 4px 4px 4px 4px;

    label {
      font-size: 12px;
      color: #333333;
      float: left;
      margin-right: 4px;
      width: 90px;
      line-height: 30px;
      text-align: right;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    :global {
      .ant-input {
        width: 23%;
        float: left;
        background: #ffffff;
        border-radius: 6px 6px 6px 6px;
        //border: 1px solid #e6e6e6;
        margin-right: 20px;
        box-shadow: none;
        margin-bottom: 20px;
      }
      .ant-select {
        width: 23%;
        float: left;
        background: #ffffff;
        margin-right: 20px;
        font-size: 12px;
        margin-bottom: 20px;
      }
      .ant-select:not(.ant-select-customize-input) .ant-select-selector {
        background: #ffffff;
        border-radius: 6px 6px 6px 6px;
        //border: 1px solid #e6e6e6;
        box-shadow: none;
      }
      .ant-btn {
        float: right;
        margin-right: 0.6%;
      }
      .ant-btn .anticon {
        margin-right: 4px;
      }
      .ant-select-selection-item img {
        width: 16px;
        height: 16px;
        margin-right: 3px;
        margin-top: -2px;
      }
    }
  }
  .tableDetailContent {
    width: 100%;
    min-height: 61vh;
    margin-top: 20px;
    padding: 20px;
    background: #ffffff;
    box-shadow: 0px 2px 30px 0px rgba(0, 0, 0, 0.02);
    border-radius: 4px 4px 4px 4px;

    .numberIcon {
      min-width: 23px;
      height: 23px;
      float: left;
      color: #3463fc;
      margin-right: 4px;
      border-radius: 2px;
      margin-left: 15px;
      padding: 0px 4px;
      background: rgba(52, 99, 252, 0.2);
    }
    .failNumberIcon {
      min-width: 23px;
      height: 23px;
      float: left;
      color: #333;
      margin-right: 4px;
      border-radius: 2px;
      margin-left: 15px;
      padding: 0px 4px;
      background: rgba(51, 51, 51, 0.2);
    }
    .terminatedNumberIcon {
      min-width: 23px;
      height: 23px;
      float: left;
      color: #f22417;
      margin-right: 4px;
      border-radius: 2px;
      margin-left: 15px;
      padding: 0px 4px;
      background: rgba(242, 36, 23, 0.2);
    }
    .readNumberIcon {
      min-width: 23px;
      height: 23px;
      float: left;
      color: #8500bb;
      margin-right: 4px;
      border-radius: 2px;
      margin-left: 15px;
      padding: 0px 4px;
      background: rgba(133, 0, 187, 0.2);
    }
    .clickNumberIcon {
      min-width: 23px;
      height: 23px;
      float: left;
      color: #64b9ff;
      margin-right: 4px;
      border-radius: 2px;
      margin-left: 15px;
      padding: 0px 4px;
      background: rgba(100, 185, 255, 0.2);
    }
    .subscribeNumberIcon {
      min-width: 23px;
      height: 23px;
      float: left;
      color: #13c825;
      margin-right: 4px;
      border-radius: 2px;
      margin-left: 15px;
      padding: 0px 4px;
      background: rgba(19, 200, 37, 0.2);
    }
    .unsubscribeNumberIcon {
      min-width: 23px;
      height: 23px;
      float: left;
      color: #fcb830;
      margin-right: 4px;
      border-radius: 2px;
      margin-left: 15px;
      padding: 0px 4px;
      background: rgba(252, 184, 48, 0.2);
    }
    .sentNumberIcon {
      min-width: 23px;
      height: 23px;
      float: left;
      color: #1bb39a;
      margin-right: 4px;
      border-radius: 2px;
      margin-left: 15px;
      padding: 0px 4px;
      background: #9fddd3;
    }
    .renderingFailureNumberIcon {
      min-width: 23px;
      height: 23px;
      float: left;
      color: #800080;
      margin-right: 4px;
      border-radius: 2px;
      margin-left: 15px;
      padding: 0px 4px;
      background: #e6cce6;
    }
    .rejectNumIcon {
      min-width: 23px;
      height: 23px;
      float: left;
      color: #333333;
      margin-right: 4px;
      border-radius: 2px;
      margin-left: 15px;
      padding: 0px 4px;
      background: #d6d6d6;
    }
    .deliveryDelayNumIcon {
      min-width: 23px;
      height: 23px;
      float: left;
      color: #07d1d1;
      margin-right: 4px;
      border-radius: 2px;
      margin-left: 15px;
      padding: 0px 4px;
      background: #cdf6f6;
    }
    .bounceNumIcon {
      min-width: 23px;
      height: 23px;
      float: left;
      color: #d3d30d;
      margin-right: 4px;
      border-radius: 2px;
      margin-left: 15px;
      padding: 0px 4px;
      background: #ffffcc;
    }

    .tabTitle {
      float: left;
    }

    .exportCustomerBtn {
      float: right;
      margin-bottom: 15px;
    }

    .marketingChannelTypeContent {
      img {
        width: 16px;
        float: left;
        margin-top: 1px;
        margin-right: 3px;
      }
      span {
        font-size: 12px;
        color: #333333;
      }
    }
    .operationContent {
      cursor: pointer;
      span {
        font-size: 12px;
        color: #3463fc;
      }
    }
    .statusContent1 {
      .circle {
        width: 6px;
        height: 6px;
        background: #3463fc;
        border-radius: 45px;
        float: left;
        margin-right: 5px;
        margin-top: 6px;
      }
      span {
        font-size: 12px;
        color: #3463fc;
      }
    }
    .statusContent2 {
      .circle {
        width: 6px;
        height: 6px;
        background: #8500bb;
        border-radius: 45px;
        float: left;
        margin-right: 5px;
        margin-top: 6px;
      }
      span {
        font-size: 12px;
        color: #8500bb;
      }
    }
    .statusContent3 {
      .circle {
        width: 6px;
        height: 6px;
        background: #79b7f9;
        border-radius: 45px;
        float: left;
        margin-right: 5px;
        margin-top: 6px;
      }
      span {
        font-size: 12px;
        color: #79b7f9;
      }
    }
    .statusContent4 {
      .circle {
        width: 6px;
        height: 6px;
        background: #13c825;
        border-radius: 45px;
        float: left;
        margin-right: 5px;
        margin-top: 6px;
      }
      span {
        font-size: 12px;
        color: #13c825;
      }
    }
    .statusContent5 {
      .circle {
        width: 6px;
        height: 6px;
        background: #fcb830;
        border-radius: 45px;
        float: left;
        margin-right: 5px;
        margin-top: 6px;
      }
      span {
        font-size: 12px;
        color: #fcb830;
      }
    }
    .statusContent6 {
      .circle {
        width: 6px;
        height: 6px;
        background: #999999;
        border-radius: 45px;
        float: left;
        margin-right: 5px;
        margin-top: 6px;
      }
      span {
        font-size: 12px;
        color: #999999;
      }
    }
    .statusContent7 {
      .circle {
        width: 6px;
        height: 6px;
        background: #f22417;
        border-radius: 45px;
        float: left;
        margin-right: 5px;
        margin-top: 6px;
      }
      span {
        font-size: 12px;
        color: #f22417;
      }
    }
    .statusContent8 {
      .circle {
        width: 6px;
        height: 6px;
        background: #1bb39a;
        border-radius: 45px;
        float: left;
        margin-right: 5px;
        margin-top: 6px;
      }
      span {
        font-size: 12px;
        color: #1bb39a;
      }
    }

    :global {
      .ant-tabs {
        color: #999;
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
      }
      .ant-tabs-tab {
        min-width: 140px;
        padding: 6px 0;
        text-align: center;
      }
      //.ant-tabs-tab:nth-child(6) {
      //  min-width: 140px;
      //  padding: 6px 0;
      //  text-align: center;
      //}
      //.ant-tabs-tab:nth-child(7) {
      //  min-width: 140px;
      //  padding: 6px 0;
      //  text-align: center;
      //}
      //.ant-tabs-tab:nth-child(9) {
      //  min-width: 125px;
      //  padding: 6px 0;
      //  text-align: center;
      //}
      .ant-tabs-tab-btn {
        width: 100%;
        text-align: center;
        //padding:0px 20px;
      }
      .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
        color: #333;
        text-align: center;
        width: 100%;
      }
      .ant-tabs-tab:hover {
        color: #333;
      }
      .ant-tabs-ink-bar {
        background-color: #3463fc;
      }
      .ant-tabs-top > .ant-tabs-nav .ant-tabs-ink-bar,
      .ant-tabs-bottom > .ant-tabs-nav .ant-tabs-ink-bar,
      .ant-tabs-top > div > .ant-tabs-nav .ant-tabs-ink-bar,
      .ant-tabs-bottom > div > .ant-tabs-nav .ant-tabs-ink-bar {
        height: 1px;
      }
      .ant-table-thead > tr > th {
        color: #000000;
      }
      .ant-table-tbody > tr > td {
        color: #333333;
      }
    }
  }
}
.marketingDetailsContent::-webkit-scrollbar {
  display: none;
  /* Chrome Safari */
}
