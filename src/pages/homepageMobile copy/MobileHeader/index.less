.mobileHeader {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 100;

  .navBar {
    height: 3.667rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 1.25rem;

    .logoContainer {
      .logo {
        height: 2rem;
        width: auto;
      }
    }

    .actions {
      display: flex;
      gap: 0.41rem;

      .iconWrapper {
        width: 2.5rem;
        height: 2.5rem;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        .icon {
          width: 1.5rem;
          height: 1.5rem;
        }
      }
    }
  }
}
