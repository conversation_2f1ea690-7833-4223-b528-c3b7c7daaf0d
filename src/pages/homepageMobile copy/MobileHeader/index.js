import React, { useState } from 'react';
import { FormattedMessage } from 'umi';
import styles from './index.less';
import menuIcon from '@/assets/menuIcon.svg';
import searchIcon from '@/assets/searchIcon.svg';

import headerLogo from '@/assets/logo.png';
import { Picker } from 'antd-mobile';

const MobileHeader = () => {
  const basicColumns = [
    {
      value: 'china',
      label: <FormattedMessage id="home.page.region.china" />,
      url: 'https://www.connectnow.cn',
    },
    {
      value: 'asia',
      label: <FormattedMessage id="home.page.region.asia" />,
      url: 'https://www.connectnowai.com',
    },
    {
      value: 'europe',
      label: <FormattedMessage id="home.page.region.europe" />,
      url: 'https://eu.connectnowai.com',
    },
    {
      value: 'usa',
      label: <FormattedMessage id="home.page.region.usa" />,
      url: 'https://us.connectnowai.com',
    },
  ];
  const [visible, setVisible] = useState(false);
  const [value, setValue] = useState([]);
  return (
    <div className={styles.mobileHeader}>
      <div className={styles.navBar}>
        <div className={styles.logoContainer}>
          <img src={headerLogo} alt="Logo" className={styles.logo} />
        </div>
        <div className={styles.actions}>
          <Picker
            data={basicColumns}
            visible={visible}
            onDismiss={() => {
              setVisible(false);
            }}
            cols={1}
            value={['usa']}
            onOk={v => {
              console.log(v, 'onConfirm');
              setValue(v);
            }}
          >
            <div
              className={styles.iconWrapper}
              onClick={() => setVisible(true)}
            >
              <img src={menuIcon} alt="菜单" className={styles.icon} />
            </div>
          </Picker>
          <div
            className={styles.iconWrapper}
            onClick={() => console.log('搜索')}
          >
            <img src={searchIcon} alt="搜索" className={styles.icon} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileHeader;
