.homeContainer {
  width: 100%;
  height: 100vh;
  background-image: url('../../assets/homepage_mobileBG.png');
  background-repeat: round;
  // background-size: cover;
  // background-position: center;
  // background-repeat: no-repeat;
  /* 状态栏 + 导航栏高度 */
  /* 隐藏滚动条 */
  overflow-x: hidden;
  overflow-y: scroll;

  &::-webkit-scrollbar {
    display: none;
  }

  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */

  .hero {
    padding: 6.5rem 2.917rem 1.5rem;
    text-align: center;
    position: relative;
    overflow: hidden;

    .heroBackground {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;

      .bgCircle {
        position: absolute;
        border-radius: 50%;
        opacity: 0.2;

        &.bgCircle1 {
          width: 14.75rem;
          height: 14.75rem;
          background-color: #aeed3b;
          top: 0;
          left: 5.417rem;
          filter: blur(7.667rem);
        }

        &.bgCircle2 {
          width: 11.667rem;
          height: 11.667rem;
          background-color: #ad30e5;
          top: 1.917rem;
          right: 2.5rem;
          filter: blur(7.667rem);
        }

        &.bgCircle3 {
          width: 27.75rem;
          height: 27.75rem;
          background-color: #aeed3b;
          top: 33.5rem;
          left: 0;
          filter: blur(7.667rem);
        }
      }
    }

    .heroTitle {
      font-size: 3.333rem;
      font-weight: 700;
      margin-bottom: 0.583rem;
      background: linear-gradient(
        -45deg,
        #eb3349,
        #f45c43,
        #ff8008,
        #ffc837,
        #4cb8c4,
        #3cd3ad,
        #24c6dc,
        #514a9d,
        #ff512f,
        #dd2476
      );
      color: transparent;
      background-size: 200% 200%;
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      line-height: 1.31982421875em;
      animation: gradientMove 8s ease infinite;

      @keyframes gradientMove {
        0% {
          background-position: 0% 50%;
        }

        25% {
          background-position: 50% 0%;
        }

        50% {
          background-position: 100% 50%;
        }

        75% {
          background-position: 50% 100%;
        }

        100% {
          background-position: 0% 50%;
        }
      }
    }

    .heroSubtitle {
      font-size: 2.5rem;
      font-weight: 700;
      margin-bottom: 0.833rem;
      background: #333;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      line-height: 1.31982421875em;
    }

    .heroText {
      font-size: 1rem;
      line-height: 2;
      color: #333333;
      margin-bottom: 1.667rem;
    }
  }

  .slideIndicator {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.833rem;
    margin: 1.667rem 0 2.667rem;

    .dot {
      width: 0.667rem;
      height: 0.667rem;
      border-radius: 50%;
      background-color: #e6e6e6;

      &.active {
        width: 1.667rem;
        height: 0.667rem;
        border-radius: 0.833rem;
        background-color: #3463fc;
        box-shadow: 0 0.25rem 0.333rem rgba(52, 99, 252, 0.2);
      }
    }
  }

  .solutionsSection {
    padding: 0 1.25rem;
    overflow: hidden;

    .solutionCard {
      width: 20.83rem !important;
      height: 32.25rem;
      position: relative;
      margin: 0 1rem;
      touch-action: pan-y;

      .cardBg {
        position: absolute;
        top: 0;
        left: 0;
        width: 20.83rem !important;
        height: 32.25rem;
      }

      .cardIcon {
        position: absolute;
        left: 0;
        top: 11.083rem;
        width: 20.833rem;
        height: 20.833rem;
        opacity: 0.5;
      }

      .cardImage {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 11.167rem;
        height: 16.667rem;
        object-fit: cover;
        border-radius: 0.833rem;
      }

      .cardContent {
        position: relative;
        z-index: 1;
        padding: 1.25rem;

        .cardHeader {
          display: flex;
          align-items: center;
          gap: 0.333rem;
          margin-bottom: 0.833rem;

          .cardTitle {
            font-size: 1.333rem;
            font-weight: 700;
            color: #ffffff;
            margin: 0;
          }

          .cardTag {
            display: flex;
            align-items: center;
            gap: 0.333rem;
            padding: 0.25rem 0.667rem;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 0.417rem;

            .tagIcon {
              width: 1.222rem;
              height: 1.097rem;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .tagText {
              font-size: 1rem;
              font-weight: 700;
              color: #7838ec;
            }
          }
        }

        .cardDescription {
          font-size: 1.167rem;
          line-height: 1.5;
          color: #ffffff;
          margin-bottom: 0.833rem;
        }

        .cardAction {
          display: inline-flex;
          align-items: center;
          gap: 0.25rem;
          padding: 0.667rem 1.333rem;
          background-color: rgba(255, 255, 255);
          border: none;
          border-radius: 8.333rem;
          cursor: pointer;

          .actionText {
            font-size: 1rem;
            font-weight: 700;
            color: #7838ec;
            font-family: 'Microsoft YaHei';
          }

          .actionIcon {
            width: 1.167rem;
            height: 0.667rem;
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }

  // .solutionsSection {
  //   padding: 1.25rem;

  //   .solutionCardList {
  //     display: flex;
  //     flex-direction: column;
  //     gap: 1.25rem;
  //     padding-bottom: 1.25rem;

  //   }
  // }

  /* 全局样式调整 */
  :global {
    html {
      font-size: 12px;
    }

    body {
      margin: 0;
      padding: 0;
      font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont,
        'Segoe UI', Roboto, sans-serif;
    }

    * {
      box-sizing: border-box;
    }

    .slick-slide {
      width: fit-content !important;
    }

    .slick-track {
      // transform: translate3d(-19.16rem, 0px, 0px) !important;
    }
  }
}
