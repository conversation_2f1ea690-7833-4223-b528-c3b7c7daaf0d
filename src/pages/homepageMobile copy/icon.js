export const FinanceIcon = () => (
  <svg
    width="250"
    height="250"
    viewBox="0 0 250 250"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M92.5003 196.429C101.845 196.429 109.464 204.048 109.464 213.393C109.464 222.738 101.845 230.417 92.5003 230.417C83.1551 230.417 75.5354 222.797 75.5354 213.452C75.5356 204.107 83.1551 196.429 92.5003 196.429ZM216.131 196.429C225.476 196.429 233.095 204.048 233.095 213.393C233.095 222.738 225.476 230.417 216.131 230.417C211.624 230.417 207.302 228.626 204.115 225.439C200.928 222.253 199.137 217.93 199.137 213.423C199.137 208.916 200.928 204.593 204.115 201.406C207.302 198.219 211.624 196.429 216.131 196.429ZM92.5003 202.381C86.4289 202.381 81.4877 207.321 81.4876 213.393C81.4876 219.464 86.4289 224.404 92.5003 224.404C98.5716 224.404 103.512 219.464 103.512 213.393C103.512 207.321 98.5715 202.381 92.5003 202.381ZM216.131 202.381C210.06 202.381 205.12 207.321 205.119 213.393C205.119 219.464 210.06 224.404 216.131 224.404C222.202 224.404 227.143 219.464 227.143 213.393C227.143 207.321 222.202 202.381 216.131 202.381ZM51.1907 56.25C52.6191 56.2501 53.8686 57.262 54.1067 58.6904L75.5354 176.786H233.333C235 176.786 236.31 178.095 236.31 179.762C236.31 181.428 235 182.738 233.333 182.738H73.0354C71.6071 182.738 70.3576 181.726 70.1194 180.298L48.6907 62.2021H20.2376L22.7376 75.3574H37.5003C38.9287 75.3576 40.1782 76.3694 40.4163 77.7979L62.5599 196.429H70.0599C71.7262 196.429 73.0352 197.738 73.0354 199.404C73.0354 201.071 71.6668 202.381 70.0599 202.381H60.1194C58.6909 202.381 57.4405 201.369 57.2024 199.94L35.0003 81.3096H20.2972C18.8688 81.3094 17.6193 80.2975 17.3812 78.8691L13.7503 59.7617C13.5718 58.9284 13.8094 57.976 14.4046 57.3213C14.9402 56.6666 15.7736 56.2501 16.6663 56.25H51.1907ZM193.929 196.429C195.595 196.429 196.904 197.738 196.905 199.404C196.905 201.071 195.595 202.381 193.929 202.381H114.047C112.381 202.381 111.072 201.071 111.072 199.404C111.072 197.738 112.381 196.429 114.047 196.429H193.929ZM207.797 43.3926C209.464 43.3926 210.774 44.7025 210.774 46.3691V102.5H233.333C234.167 102.5 235 102.857 235.595 103.512C236.19 104.166 236.429 105 236.31 105.833L229.047 160.893C228.809 162.44 227.56 163.512 226.072 163.512H90.9524C89.2859 163.512 87.9761 162.203 87.9759 160.536C87.9759 158.869 89.2858 157.56 90.9524 157.56H223.452L229.941 108.452H172.679C172.487 108.491 172.288 108.512 172.083 108.512C171.875 108.512 171.672 108.491 171.477 108.452H101.261C101.066 108.491 100.863 108.512 100.655 108.512C100.446 108.512 100.243 108.491 100.048 108.452H83.6311C81.9646 108.452 80.6548 107.143 80.6546 105.477C80.6546 103.81 81.9645 102.5 83.6311 102.5H97.679V76.1904C97.679 74.5238 98.988 73.2139 100.655 73.2139H126.656C126.851 73.1748 127.054 73.1553 127.262 73.1553H133.393V46.3691C133.393 44.7025 134.703 43.3926 136.369 43.3926H207.797ZM189.702 109.227C202.798 109.227 213.512 119.941 213.512 133.036C213.512 146.131 202.798 156.846 189.702 156.846H118.274C105.179 156.846 94.4644 146.131 94.4642 133.036C94.4642 119.941 105.179 109.227 118.274 109.227H189.702ZM103.631 102.5H124.285V79.167H103.631V102.5ZM130.238 102.5H142.143V79.167H130.238V102.5ZM148.095 102.5H169.107V79.167H148.095V102.5ZM183.81 76.1309C183.81 77.7975 182.44 79.1074 180.833 79.1074H175.06V102.5H204.822V49.3457H183.81V76.1309ZM139.345 73.1553H145.119C145.328 73.1553 145.531 73.1748 145.726 73.2139H160V49.3457H139.345V73.1553ZM165.952 73.1553H177.857V49.3457H165.952V73.1553Z"
      fill="url(#paint0_linear_2860_11435)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_2860_11435"
        x1="125.016"
        y1="43.3926"
        x2="125.016"
        y2="230.417"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="white" stop-opacity="0.5" />
        <stop offset="1" stop-color="white" stop-opacity="0" />
      </linearGradient>
    </defs>
  </svg>
);
export const JinrongIcon = () => (
  <svg
    width="251"
    height="250"
    viewBox="0 0 251 250"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g opacity="0.5" clip-path="url(#clip0_2855_808)">
      <path
        d="M125.457 250.002C100.732 250.005 76.5619 242.676 56.003 228.94C35.4441 215.205 19.4204 195.681 9.95858 172.839C0.496749 149.996 -1.97813 124.86 2.84698 100.61C7.67209 76.3607 19.5804 54.0868 37.0658 36.6058C54.5511 19.1248 76.828 7.222 101.079 2.40293C125.33 -2.41615 150.465 0.0649815 173.305 9.53249C196.146 19 215.666 35.0286 229.396 55.5909C243.126 76.1532 250.449 100.326 250.44 125.05C250.395 158.181 237.211 189.942 213.782 213.366C190.352 236.79 158.588 249.965 125.457 250.002ZM125.457 10.9947C102.911 10.9978 80.872 17.6857 62.1264 30.213C43.3807 42.7403 28.7703 60.5444 20.1422 81.3744C11.5141 102.204 9.25586 125.125 13.6529 147.238C18.05 169.352 28.9049 189.665 44.8453 205.61C60.7857 221.554 81.0959 232.415 103.208 236.818C125.32 241.221 148.241 238.969 169.074 230.346C189.906 221.724 207.714 207.118 220.246 188.376C232.779 169.634 239.473 147.597 239.482 125.05C239.466 94.81 227.449 65.8121 206.07 44.4245C184.691 23.0369 155.698 11.008 125.457 10.9791V10.9947Z"
        fill="url(#paint0_linear_2855_808)"
      />
      <path
        d="M119.238 112.614V77.4863C103.586 82.2736 97.8971 100.459 119.238 112.614ZM223.004 117.277C222.996 117.339 222.996 117.401 223.004 117.463C223.004 118.054 223.346 120.137 222.849 120.572C222.605 121.1 222.188 121.528 221.667 121.784C217.471 123.696 217.859 117.463 217.579 114.728C217.471 113.748 216.616 110.22 219.926 110.22C222.926 110.345 223.004 114.324 223.004 117.277ZM219.849 99.169C219.849 99.34 220.952 103.319 220.952 103.537V103.77V104.034C220.957 104.08 220.957 104.127 220.952 104.174C220.959 104.22 220.959 104.267 220.952 104.314C220.95 104.345 220.95 104.376 220.952 104.407C221.59 107.516 220.346 108.681 218.683 108.681C215.994 108.681 216.165 106.148 215.093 101.842C214.717 100.922 214.568 99.9255 214.657 98.9358C214.701 98.2568 215.009 97.6219 215.516 97.1677C216.023 96.7135 216.688 96.4762 217.367 96.5067C218.047 96.5373 218.688 96.8332 219.152 97.331C219.616 97.8288 219.866 98.4887 219.849 99.169ZM217.471 91.7083C217.465 91.7443 217.465 91.7811 217.471 91.8171V92.4077C217.618 92.8295 217.653 93.2825 217.571 93.7219C217.49 94.1612 217.295 94.5716 217.006 94.9124C216.717 95.2532 216.344 95.5126 215.924 95.6649C215.504 95.8172 215.051 95.857 214.611 95.7806C212.543 95.4852 212.823 93.962 210.259 88.009C210.12 87.601 210.087 87.1647 210.162 86.7405C210.237 86.3162 210.417 85.9176 210.687 85.5815C210.956 85.2454 211.306 84.9826 211.704 84.8174C212.102 84.6522 212.535 84.5898 212.963 84.6361C214.657 84.6361 215.372 85.5532 217.471 91.7083ZM208.984 74.0512C212.093 79.1338 214.051 82.0404 211.253 83.5015C210.699 83.7878 210.06 83.8637 209.454 83.7151C208.848 83.5665 208.317 83.2035 207.958 82.6932C207.523 82.1181 203.637 75.4656 204.275 74.7662C204.398 74.2719 204.664 73.825 205.041 73.482C205.418 73.139 205.887 72.9152 206.391 72.8387C206.895 72.7623 207.41 72.8366 207.871 73.0524C208.333 73.2681 208.72 73.6157 208.984 74.0512ZM199.767 62.0053C200.277 62.1626 200.727 62.4717 201.057 62.8912C201.213 63.0777 203.824 66.3263 203.964 66.5905V66.7459L204.057 66.8703L204.212 67.0723C204.212 67.0723 204.958 68.0671 204.958 68.1137C205.349 68.6652 205.505 69.3484 205.395 70.0148C205.284 70.6813 204.915 71.2772 204.368 71.6731C201.71 73.6005 200.513 70.4141 196.798 65.9066C196.49 65.459 196.331 64.9255 196.344 64.3822C196.357 63.839 196.541 63.3137 196.87 62.8812C197.199 62.4488 197.657 62.1313 198.177 61.974C198.697 61.8167 199.253 61.8276 199.767 62.0053ZM193.27 54.4668C193.27 54.4668 193.425 54.6223 193.472 54.6223C193.519 54.6223 193.472 54.6223 193.472 54.7C193.472 54.7777 193.69 54.8709 193.69 54.8709L193.798 54.9642C195.959 56.6118 196.736 58.0728 196.736 59.1764C196.708 59.8361 196.425 60.4592 195.946 60.9143C195.468 61.3695 194.832 61.6213 194.171 61.6167C192.524 61.6167 192.726 60.9172 187.674 56.2387C187.416 55.9893 187.211 55.6898 187.072 55.3584C186.933 55.027 186.863 54.6707 186.866 54.3114C187.317 52.6794 189.54 50.6432 193.27 54.4668ZM185.001 47.3325L185.11 47.4102C185.657 47.7387 186.119 48.1911 186.46 48.7311C186.8 49.2711 187.008 49.8835 187.068 50.5189C187.017 50.9798 186.842 51.4184 186.563 51.7892C186.285 52.1599 185.912 52.4492 185.483 52.6269C185.055 52.8046 184.587 52.8643 184.127 52.7997C183.668 52.7351 183.234 52.5487 182.872 52.2597C181.187 50.9004 179.444 49.6143 177.649 48.405C175.193 46.602 177.789 40.136 185.001 47.3325ZM169.66 37.6957H170.142L170.375 37.8201C170.375 37.8201 171.447 38.3796 171.494 38.4263L171.836 38.5972H172.566L172.706 38.6905H172.862C172.862 38.6905 173.996 39.3433 174.028 39.3744L174.214 39.4987H174.323C174.407 39.5712 174.48 39.6549 174.54 39.7474C174.773 39.9932 174.953 40.2842 175.069 40.6023C175.12 40.754 175.156 40.9101 175.178 41.0686C175.238 41.4349 175.219 41.81 175.121 42.168C175.022 42.526 174.847 42.8584 174.608 43.1422C174.368 43.4259 174.07 43.6543 173.734 43.8116C173.397 43.9688 173.031 44.0512 172.66 44.0529C171.37 44.0529 171.37 43.4312 165.634 40.5712C163.722 40.4624 165.867 35.4575 169.66 37.6957ZM155.158 32.038C155.78 31.9137 156.029 31.8826 160.707 33.7011H160.909H161.795C162.961 34.4161 164.204 34.183 164.204 36.9497C164.233 37.5612 164.04 38.1627 163.66 38.6427C163.28 39.1227 162.739 39.4489 162.137 39.5609C160.956 39.7164 160.723 39.0946 154.785 37.1206C154.187 36.9801 153.659 36.6309 153.296 36.1359C152.933 35.6409 152.758 35.0326 152.803 34.4201C152.847 33.8077 153.109 33.2314 153.541 32.7948C153.973 32.3581 154.546 32.0898 155.158 32.038ZM149.811 30.235C150.405 30.3994 150.92 30.7722 151.262 31.2852C151.604 31.7983 151.749 32.4172 151.672 33.0288C151.594 33.6403 151.299 34.2035 150.841 34.6153C150.382 35.027 149.79 35.2598 149.174 35.271C149.174 35.271 144.138 34.1674 142.195 33.8255C141.704 33.7265 141.25 33.4922 140.884 33.1488C140.519 32.8055 140.257 32.367 140.128 31.8826C138.962 27.1264 145.661 29.1625 149.811 30.235ZM118.958 27.3751H119.331C119.487 27.3751 123.745 27.2507 123.994 27.2507H124.476C125.611 27.2507 126.543 27.2507 127.367 28.2144C127.749 27.7962 128.256 27.5127 128.812 27.4062C129.232 27.1264 136.802 27.6393 137.734 28.5253C138.133 28.8934 138.405 29.3784 138.51 29.9107C138.616 30.4429 138.55 30.9949 138.322 31.4873C138.093 31.9797 137.715 32.387 137.241 32.6508C136.767 32.9145 136.221 33.0211 135.683 32.9551C128.952 32.209 128.424 32.8618 127.305 31.5251C126.155 32.8152 125.626 32.1468 118.896 32.5976C115.414 32.9084 115.243 27.4683 118.585 27.4683C124.414 27.3906 112.632 27.3751 118.958 27.3751ZM105 29.2713H107.285C107.32 29.2595 107.359 29.2595 107.394 29.2713H108.995H110.425H110.829C110.896 29.2627 110.964 29.2627 111.031 29.2713C111.404 29.1323 111.804 29.0794 112.201 29.1168C112.598 29.1541 112.981 29.2807 113.322 29.487C113.662 29.6933 113.952 29.974 114.169 30.3081C114.386 30.6422 114.525 31.021 114.575 31.4163C114.046 33.3281 112.383 33.1416 108.466 33.8255C106.772 34.1519 103.648 35.271 103.026 32.2712C102.901 31.6127 103.037 30.9314 103.406 30.3715C103.774 29.8116 104.346 29.417 105 29.2713ZM90.2498 34.727C90.3706 34.3562 90.5729 34.0171 90.8419 33.7347C91.1108 33.4522 91.4397 33.2337 91.8042 33.095C91.8042 33.095 94.804 32.0225 94.9128 32.0225C95.0944 31.9506 95.2815 31.8934 95.4723 31.8515H96.9956C102.218 30.1107 102.513 35.613 99.5913 36.5145C93.9802 38.0688 93.545 38.675 92.5347 38.675C92.0659 38.6928 91.6011 38.5825 91.1903 38.356C90.7795 38.1294 90.4383 37.7951 90.2033 37.3891C89.9683 36.983 89.8485 36.5206 89.8567 36.0516C89.8649 35.5825 90.0008 35.1246 90.2498 34.727ZM70.9297 43.7265H71.1784L71.2872 43.6332H71.3804C76.2299 40.5246 77.1625 43.8975 77.0071 44.9855C77.5355 46.6797 69.6551 51.0318 68.9246 51.0318C68.5658 51.0335 68.2107 50.9599 67.8821 50.8157C67.5536 50.6716 67.2589 50.4601 67.0172 50.1949C66.7755 49.9298 66.5921 49.6169 66.4789 49.2764C66.3657 48.936 66.3252 48.5756 66.36 48.2185C66.6553 46.5709 67.8677 45.7782 70.9297 43.7265ZM57.5936 54.5135H57.7491L57.8268 54.4357V54.358C57.8268 54.358 57.8268 54.2803 57.9045 54.2803C57.9822 54.2803 57.9045 54.187 57.9822 54.187L58.6195 53.5498C58.693 53.468 58.7708 53.3902 58.8526 53.3166C59.2723 52.897 60.9354 51.4514 60.9354 51.4514H61.0131L61.0908 51.3737C61.16 51.3077 61.2326 51.2454 61.3085 51.1872C65.1165 48.0786 67.5257 52.6327 65.2253 54.6067C60.5624 58.5858 60.407 59.2697 59.5676 59.5339C59.238 59.6635 58.886 59.7269 58.5319 59.7205C58.1777 59.7141 57.8283 59.638 57.5035 59.4966C57.1788 59.3551 56.8851 59.1511 56.6392 58.8962C56.3933 58.6412 56.2 58.3403 56.0704 58.0107C55.9408 57.681 55.8774 57.3291 55.8838 56.9749C55.8902 56.6208 55.9663 56.2713 56.1077 55.9466C56.2492 55.6218 56.4532 55.3281 56.7081 55.0822C56.9631 54.8363 57.264 54.6431 57.5936 54.5135ZM48.3455 64.7253C48.3455 64.7253 48.4232 64.7253 48.4232 64.6321C48.6252 64.3212 51.0189 61.368 51.2831 61.1659C54.7648 56.8294 57.5004 60.4354 56.661 62.5648C56.459 63.3264 55.5264 63.8704 51.9981 68.409C50.506 70.3209 46.0451 69.0929 48.3455 64.7253ZM40.8847 75.7454L40.9935 75.5744L42.0194 73.8802C42.0223 73.8544 42.0223 73.8283 42.0194 73.8025V73.7093V73.6315V73.4606C42.0131 73.4298 42.0131 73.3981 42.0194 73.3673V73.2896L43.0763 72.2016V71.9995V71.9218C43.284 71.568 43.5698 71.2664 43.9119 71.0401C44.2541 70.8138 44.6435 70.6688 45.0503 70.6162C45.5342 70.5693 46.0214 70.66 46.456 70.8779C46.8905 71.0959 47.2546 71.4321 47.5064 71.8479C47.7582 72.2638 47.8874 72.7423 47.8791 73.2283C47.8708 73.7144 47.7253 74.1882 47.4595 74.5952C46.252 76.4023 45.1114 78.2532 44.04 80.1441C43.7851 80.5806 43.4077 80.9326 42.9545 81.1564C42.5013 81.3802 41.9924 81.466 41.4909 81.4031C41.2578 80.2063 39.2061 79.0872 40.8847 75.7454ZM34.7918 88.1178V87.9934V87.7914C36.9057 82.6932 37.4341 81.341 39.346 81.341C39.783 81.3398 40.2133 81.4498 40.5962 81.6604C40.9792 81.8711 41.3024 82.1756 41.5354 82.5454C41.7685 82.9152 41.9038 83.3381 41.9286 83.7745C41.9534 84.2109 41.867 84.6464 41.6774 85.0402C39.0973 90.356 38.833 93.6045 35.7089 92.4232C34.5587 91.6461 33.8903 90.3249 34.7918 88.1178ZM30.3931 101.734C30.3964 101.697 30.3964 101.661 30.3931 101.625C30.3903 101.594 30.3903 101.563 30.3931 101.532C30.4026 101.501 30.4026 101.469 30.3931 101.438V100.988C30.3947 100.962 30.3947 100.936 30.3931 100.91C30.3953 100.879 30.3953 100.848 30.3931 100.817C30.4027 100.792 30.4027 100.764 30.3931 100.739C30.3955 100.713 30.3955 100.687 30.3931 100.661C31.5277 97.2261 31.3879 95.2366 33.6416 94.7858C34.0058 94.7282 34.378 94.7489 34.7335 94.8464C35.089 94.9439 35.4197 95.1161 35.7035 95.3514C35.9873 95.5866 36.2178 95.8797 36.3795 96.211C36.5412 96.5423 36.6305 96.9042 36.6414 97.2727C36.6414 97.5525 35.3514 101.936 34.7607 104.376C34.1079 107.034 29.7714 107.485 29.7714 103.132C29.909 103.088 30.0357 103.016 30.1433 102.919C30.2509 102.823 30.3368 102.704 30.3955 102.572C30.4542 102.44 30.4843 102.297 30.4839 102.153C30.4835 102.008 30.4526 101.865 30.3931 101.734Z"
        fill="url(#paint1_linear_2855_808)"
      />
      <path
        d="M28.1549 114.526V114.401V114.168C28.1549 114.168 28.5901 110.764 28.6212 110.485C28.6263 110.438 28.6263 110.391 28.6212 110.345C28.6268 110.288 28.6268 110.231 28.6212 110.174C28.6873 109.354 28.8869 108.551 29.2118 107.796C29.5456 107.322 30.0289 106.975 30.5839 106.81C31.1389 106.645 31.7333 106.671 32.2715 106.885C32.8098 107.098 33.2606 107.486 33.5514 107.987C33.8422 108.488 33.9561 109.072 33.8748 109.645C33.0821 114.308 33.4396 119.888 29.5072 118.225C27.9684 117.401 28.0461 115.94 28.1549 114.526ZM27.5332 125.826V125.717V123.028C27.5332 121.163 28.3259 119.779 30.2066 119.779C33.1754 119.873 32.6624 123.028 32.6624 125.017V129.276C32.3671 132.261 27.5176 132.944 27.5176 128.126C27.6575 128.157 27.5332 125.826 27.5332 125.826ZM28.6989 139.799V139.675C28.7067 139.571 28.7067 139.467 28.6989 139.364V139.037C28.7056 138.996 28.7056 138.954 28.6989 138.913V138.773V138.571C28.7021 138.54 28.7021 138.509 28.6989 138.478V138.369V138.12C28.6957 138.084 28.6957 138.048 28.6989 138.012C28.707 137.976 28.707 137.939 28.6989 137.903V137.483C28.6989 136.333 27.9373 133.162 31.077 133.162C34.2167 133.162 33.5173 135.96 34.5742 141.866C34.668 142.35 34.6205 142.85 34.4373 143.308C34.2542 143.765 33.9432 144.16 33.5415 144.445C33.1398 144.731 32.6645 144.894 32.1723 144.916C31.6801 144.939 31.1919 144.819 30.7662 144.571C28.8854 143.592 28.8077 140.359 28.6989 139.799ZM30.6418 149.467C30.587 149.143 30.587 148.812 30.6418 148.488C30.7057 147.983 30.9189 147.51 31.254 147.127C31.5892 146.745 32.0309 146.472 32.5225 146.343C32.8481 146.259 33.1871 146.24 33.5199 146.288C33.8527 146.336 34.1728 146.449 34.4618 146.621C34.7507 146.792 35.0028 147.02 35.2035 147.29C35.4042 147.559 35.5495 147.866 35.6312 148.192C36.0508 149.747 40.2941 160.269 34.0769 157.611C32.2894 156.912 30.6418 150.026 30.6418 149.467ZM38.4134 169.393C38.4134 169.393 38.3046 169.284 38.2891 169.284C37.6854 168.293 37.1655 167.253 36.7347 166.176V165.896C36.7403 165.865 36.7403 165.834 36.7347 165.803L36.6415 165.616L35.5068 162.974C35.3041 162.481 35.258 161.938 35.3748 161.418C35.4916 160.898 35.7656 160.426 36.1596 160.067C39.4237 157.083 41.1956 163.362 42.9831 166.891C43.3114 167.496 43.3865 168.208 43.1917 168.868C42.997 169.529 42.5484 170.086 41.9441 170.417C41.3398 170.748 40.6291 170.826 39.9674 170.634C39.3056 170.442 38.7469 169.996 38.4134 169.393Z"
        fill="url(#paint2_linear_2855_808)"
      />
      <path
        d="M41.1646 174.787C41.0806 174.639 41.0128 174.483 40.9625 174.32C40.7989 173.872 40.7647 173.386 40.8636 172.919C40.9625 172.452 41.1907 172.022 41.5221 171.678C41.5221 171.678 41.5221 171.585 41.6154 171.585C45.1126 167.637 48.0969 177.802 49.2781 177.802C49.5422 178.314 49.6296 178.898 49.5268 179.465C49.4581 179.828 49.3113 180.172 49.0966 180.472C48.8819 180.773 48.6044 181.023 48.2834 181.206C45.7498 182.745 44.4908 180.351 41.1646 174.787ZM53.4437 191.262H53.366C53.3621 191.241 53.3529 191.222 53.3393 191.206C53.3257 191.189 53.3081 191.177 53.2883 191.169C53.2883 191.169 53.1795 191.169 53.1639 191.06C53.1198 191.04 53.0821 191.008 53.0551 190.967L52.9463 190.843V190.734H52.8686C52.8686 190.734 52.8686 190.641 52.7909 190.641L52.6665 190.501L52.5733 190.392C51.019 188.604 48.2057 186.195 49.0139 184.33C49.008 184.299 49.008 184.268 49.0139 184.237C49.0267 184.16 49.053 184.087 49.0916 184.019C49.1966 183.813 49.3329 183.624 49.4957 183.46C51.8894 180.802 54.6094 185.48 57.516 188.651C59.661 190.33 55.5109 193.594 53.4437 191.262ZM65.8472 201.117C64.0908 201.692 62.9872 200.215 61.5572 199.034H61.464V198.941L61.3707 198.847H61.1687C61.1221 198.8 61.0699 198.758 61.0132 198.723C61.0182 198.692 61.0182 198.661 61.0132 198.63C56.8477 195.894 57.9046 193.345 59.4589 192.552C61.7127 191.387 62.381 193.159 67.3548 197.215C67.5739 197.559 67.7091 197.949 67.7496 198.354C67.7901 198.759 67.7347 199.168 67.5879 199.547C67.4412 199.927 67.2071 200.267 66.9047 200.54C66.6023 200.812 66.2401 201.01 65.8472 201.117ZM75.9036 208.982C75.419 208.976 74.9454 208.836 74.5358 208.577H74.3337C73.5255 208.096 71.5515 206.79 69.4843 205.329C69.0322 205.012 68.6945 204.557 68.5215 204.033C68.3484 203.508 68.3493 202.942 68.5239 202.418C68.6986 201.894 69.0376 201.441 69.4907 201.125C69.9438 200.809 70.4866 200.648 71.0386 200.666C72.453 200.666 72.3442 201.272 77.9708 204.707C78.3037 205.088 78.5171 205.559 78.5846 206.06C78.652 206.562 78.5705 207.072 78.3502 207.528C78.1299 207.983 77.7805 208.364 77.3455 208.622C76.9104 208.881 76.409 209.006 75.9036 208.982ZM78.1263 41.877C77.9038 41.2929 77.901 40.6479 78.1182 40.0618C78.3355 39.4758 78.7581 38.9885 79.3075 38.6906H79.5873L82.2763 37.3073H82.7581C85.0741 36.2037 85.2917 36.0793 85.96 36.0793C86.4503 36.0786 86.9306 36.2176 87.3448 36.4799C87.7589 36.7423 88.0897 37.1173 88.2985 37.5609C88.5072 38.0045 88.5853 38.4984 88.5235 38.9847C88.4617 39.4711 88.2626 39.9298 87.9495 40.3071C87.5454 40.8666 79.8671 44.97 78.6236 43.8043C78.4172 43.532 78.267 43.2214 78.1816 42.8905C78.0962 42.5597 78.0774 42.2151 78.1263 41.877ZM87.7786 215.199C86.7216 215.199 81.3748 212.448 80.5665 212.09C80.0638 211.818 79.6657 211.387 79.4344 210.865C79.2032 210.342 79.1521 209.757 79.289 209.202C79.4259 208.648 79.7432 208.154 80.191 207.799C80.6389 207.444 81.1919 207.248 81.7634 207.241C82.9446 207.241 83.1156 207.878 88.82 210.349C89.3139 210.614 89.7085 211.031 89.9441 211.539C90.1797 212.048 90.2435 212.619 90.1259 213.166C90.0083 213.714 89.7156 214.209 89.2922 214.575C88.8687 214.942 88.3374 215.161 87.7786 215.199ZM102.964 217.219C102.871 217.746 102.618 218.231 102.24 218.609C101.862 218.987 101.377 219.24 100.85 219.333C100.098 219.346 99.3498 219.219 98.6432 218.96H98.2236C98.2236 218.96 95.7988 218.214 95.7367 218.199H95.2237C94.058 217.981 91.9286 216.769 91.9286 215.09C91.9621 214.69 92.0847 214.303 92.2875 213.956C92.4903 213.61 92.7681 213.313 93.1005 213.088C93.4329 212.863 93.8115 212.715 94.2085 212.655C94.6055 212.596 95.0108 212.626 95.3947 212.743C100.058 214.391 103.524 214.204 103.524 217.219C102.98 216.536 103.057 216.644 102.964 217.219ZM111.793 221.898H111.529L108.715 221.416C108.634 221.396 108.548 221.396 108.467 221.416H107.596C105.171 221.323 104.192 219.489 104.394 218.447C104.394 214.764 109.29 216.582 113.876 217.157C116.922 217.375 117.357 222.862 111.793 221.898ZM126.652 222.862H124.243C122.953 222.862 120.715 222.753 120.715 222.753C119.564 222.908 117.015 222.147 117.606 219.644C117.732 219.078 118.047 218.571 118.499 218.207C118.951 217.843 119.513 217.643 120.093 217.639C126.108 217.639 129.325 217.297 129.419 220.328C129.409 220.678 129.329 221.022 129.183 221.34C129.038 221.659 128.83 221.945 128.572 222.181C128.314 222.417 128.011 222.599 127.681 222.716C127.351 222.833 127.001 222.883 126.652 222.862Z"
        fill="url(#paint3_linear_2855_808)"
      />
      <path
        d="M125.455 17.8004C104.244 17.8004 83.5083 24.0904 65.8715 35.8749C48.2346 47.6595 34.4884 64.4093 26.3711 84.0063C18.2537 103.603 16.1299 125.167 20.2681 145.971C24.4062 166.775 34.6206 185.885 49.6195 200.884C64.6184 215.883 83.7281 226.097 104.532 230.235C125.336 234.374 146.9 232.25 166.497 224.132C186.094 216.015 202.844 202.269 214.629 184.632C226.413 166.995 232.703 146.26 232.703 125.048C232.703 96.6044 221.404 69.3255 201.291 49.2126C181.178 29.0997 153.899 17.8004 125.455 17.8004ZM159.806 169.766C157.556 173.793 154.354 177.209 150.48 179.714C146.334 182.384 141.736 184.275 136.911 185.294C135.232 185.651 133.507 185.931 131.75 186.211V201.023C131.75 209.323 119.222 209.323 119.222 201.023V186.957C117.668 186.957 115.803 186.755 113.673 186.522C110.907 186.211 108.047 185.775 105.156 185.185C102.374 184.623 99.6288 183.896 96.9335 183.009C95.1268 182.399 93.556 181.24 92.4411 179.693C91.3263 178.146 90.7231 176.289 90.7162 174.382V172.3C90.7178 171.494 90.924 170.702 91.3155 169.998C91.707 169.294 92.2709 168.7 92.9544 168.274C93.6391 167.852 94.4212 167.615 95.2246 167.585C96.028 167.555 96.8255 167.734 97.5397 168.103C100.22 169.449 103.006 170.572 105.871 171.46C108.752 172.408 111.704 173.125 114.699 173.605C116.254 173.885 117.808 174.071 119.222 174.211V128.561C103.555 120.51 90.8406 112.614 90.8406 95.1121C90.6896 90.1376 91.9102 85.2173 94.3689 80.8901C96.708 76.8852 99.9359 73.4712 103.804 70.9114C107.872 68.2065 112.379 66.229 117.124 65.0672C117.792 64.8807 118.461 64.7252 119.129 64.6009V51.1094C119.129 49.4481 119.789 47.8549 120.964 46.6802C122.138 45.5055 123.732 44.8455 125.393 44.8455C127.054 44.8455 128.648 45.5055 129.822 46.6802C130.997 47.8549 131.657 49.4481 131.657 51.1094V63.1398H132.403C139.401 62.9375 146.394 63.7011 153.184 65.4091C154.64 65.8284 155.919 66.7107 156.828 67.9224C157.736 69.1342 158.225 70.6092 158.22 72.1238C158.218 73.213 157.955 74.286 157.455 75.2537C156.955 76.2214 156.232 77.0559 155.345 77.6882C154.459 78.3304 153.43 78.7479 152.347 78.9049C151.264 79.062 150.159 78.9538 149.127 78.5898C143.496 76.733 137.602 75.7988 131.672 75.8231V119.002C147.853 127.022 163.225 136.706 163.225 154.751C163.393 159.947 162.215 165.097 159.806 169.704V169.766Z"
        fill="url(#paint4_linear_2855_808)"
      />
      <path
        d="M138.014 222.053H137.563C136.926 222.053 129.403 223.996 131.113 218.945C131.968 216.784 133.802 217.484 139.739 216.535C142.988 216.069 145.055 222.053 138.014 222.053ZM154.77 218.183C152.174 219.233 149.475 220.009 146.718 220.499C146.091 220.483 145.49 220.239 145.028 219.814C144.566 219.389 144.274 218.811 144.205 218.186C144.137 217.562 144.297 216.934 144.657 216.419C145.016 215.904 145.549 215.537 146.159 215.385C150.915 214.297 155.749 211.95 155.749 216.193C155.746 216.577 155.657 216.956 155.487 217.3C155.317 217.645 155.072 217.946 154.77 218.183ZM167.624 213.038C166.909 213.846 166.225 213.862 164.609 214.701L164.267 214.857C164.236 214.867 164.203 214.867 164.173 214.857H163.52H163.427C162.277 215.23 161.08 216.162 159.588 215.292C159.219 215.06 158.914 214.737 158.704 214.355C158.493 213.974 158.382 213.544 158.382 213.108C158.382 212.672 158.493 212.243 158.704 211.861C158.914 211.479 159.219 211.157 159.588 210.924C162.038 209.785 164.55 208.784 167.111 207.924C167.474 207.922 167.834 207.997 168.166 208.144C168.498 208.291 168.795 208.506 169.037 208.777C169.28 209.047 169.462 209.366 169.572 209.712C169.682 210.058 169.717 210.424 169.676 210.784C168.261 211.717 168.09 212.525 167.624 213.038ZM177.09 208.127C177.054 208.134 177.017 208.134 176.981 208.127H176.872C176.872 208.127 174.82 209.308 174.82 209.323H174.727C174.206 209.79 173.53 210.048 172.831 210.048C172.131 210.048 171.456 209.79 170.935 209.323C167.935 206.215 172.909 204.536 177.059 201.847C177.96 201.443 182.856 203.806 177.09 208.127Z"
        fill="url(#paint5_linear_2855_808)"
      />
      <path
        d="M184.519 203.029C180.789 204.894 179.064 200.728 181.411 198.941C185.374 195.941 187.877 192.568 190.084 195.117C192.291 197.666 189.027 199.547 184.519 203.029ZM197.949 190.703V190.796C197.893 190.844 197.841 190.896 197.793 190.952L197.638 191.092C197.599 191.15 197.552 191.202 197.498 191.247C197.498 191.247 197.498 191.34 197.405 191.356C197.362 191.406 197.315 191.453 197.265 191.496C197.22 191.515 197.181 191.547 197.156 191.589C197.108 191.645 197.056 191.697 197 191.744C196.997 191.781 196.997 191.817 197 191.853C196.925 191.906 196.857 191.969 196.798 192.04C195.399 193.408 193.938 193.936 192.602 192.599C190.239 190.252 192.757 189.118 197.032 184.222C197.274 183.935 197.577 183.706 197.918 183.55C198.259 183.394 198.63 183.316 199.005 183.32C199.518 184.843 201.415 187.221 197.949 190.703ZM206.901 179.217L206.808 179.357C206.771 179.425 206.724 179.488 206.668 179.543V179.636L206.575 179.776C206.572 179.812 206.572 179.849 206.575 179.885V179.978C204.896 183.305 203.218 184.377 201.912 184.377C201.316 184.35 200.748 184.114 200.307 183.71C199.867 183.307 199.582 182.762 199.503 182.17C199.176 180.522 204.368 173.932 204.943 173.932C205.938 173.637 209.746 175.036 206.901 179.217ZM212.901 168.958C212.908 168.973 212.912 168.989 212.912 169.005C212.912 169.021 212.908 169.037 212.901 169.051V169.238C212.898 169.274 212.898 169.311 212.901 169.347C212.435 170.342 211.44 172.455 210.321 172.455H209.388C208.871 172.57 208.331 172.524 207.84 172.323C207.35 172.122 206.933 171.776 206.645 171.331C206.357 170.885 206.213 170.363 206.231 169.833C206.249 169.304 206.429 168.792 206.746 168.368C207.508 167.015 211.3 157.052 214.145 162.756C214.335 163.099 214.437 163.484 214.44 163.876C214.337 165.668 213.809 167.41 212.901 168.958ZM219.134 153.353C219.134 153.462 218.683 154.783 218.683 154.783C218.692 154.813 218.692 154.846 218.683 154.876C218.68 154.912 218.68 154.949 218.683 154.985V155.187C218.687 155.259 218.687 155.332 218.683 155.405C218.683 155.529 217.766 158.062 217.657 158.218C217.501 158.669 217.223 159.067 216.853 159.369C216.483 159.67 216.036 159.862 215.563 159.924C215.09 159.985 214.609 159.913 214.174 159.716C213.74 159.519 213.369 159.205 213.103 158.808C211.844 157.052 216.554 142.504 219.771 149.995C219.961 150.254 220.096 150.548 220.167 150.861C220.239 151.174 220.244 151.498 220.185 151.813C220.125 152.128 220.001 152.428 219.82 152.693C219.639 152.957 219.406 153.182 219.134 153.353Z"
        fill="url(#paint6_linear_2855_808)"
      />
      <path
        d="M222.025 140.887V141.26C222.034 141.301 222.034 141.344 222.025 141.385V142.69C221.528 144.105 221.792 147.882 219.367 148.488C218.938 148.59 218.49 148.581 218.066 148.461C217.642 148.341 217.255 148.114 216.944 147.803C216.632 147.491 216.405 147.104 216.285 146.68C216.165 146.256 216.156 145.808 216.258 145.379C216.756 143.063 216.958 141.96 217.051 141.385C218.232 133.022 223.377 138.089 222.025 140.887Z"
        fill="url(#paint7_linear_2855_808)"
      />
      <path
        d="M223.191 129.478V129.618C223.198 129.69 223.198 129.763 223.191 129.835C223.191 138.384 217.611 135.633 217.859 132.726C218.062 130.286 217.393 120.929 222.025 124.038C223.781 125.033 223.408 128.328 223.222 128.965L223.191 129.478ZM131.673 135.042V173.636C150.837 170.294 158.22 149.062 131.673 135.042Z"
        fill="url(#paint8_linear_2855_808)"
      />
    </g>
    <defs>
      <linearGradient
        id="paint0_linear_2855_808"
        x1="125.442"
        y1="0.00561523"
        x2="125.442"
        y2="250.002"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="white" stop-opacity="0.5" />
        <stop offset="1" stop-color="white" stop-opacity="0" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_2855_808"
        x1="126.445"
        y1="27.2507"
        x2="126.445"
        y2="122.143"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="white" stop-opacity="0.5" />
        <stop offset="1" stop-color="white" stop-opacity="0" />
      </linearGradient>
      <linearGradient
        id="paint2_linear_2855_808"
        x1="35.4078"
        y1="106.703"
        x2="35.4078"
        y2="170.737"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="white" stop-opacity="0.5" />
        <stop offset="1" stop-color="white" stop-opacity="0" />
      </linearGradient>
      <linearGradient
        id="paint3_linear_2855_808"
        x1="85.1135"
        y1="36.0793"
        x2="85.1135"
        y2="222.866"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="white" stop-opacity="0.5" />
        <stop offset="1" stop-color="white" stop-opacity="0" />
      </linearGradient>
      <linearGradient
        id="paint4_linear_2855_808"
        x1="125.455"
        y1="17.8004"
        x2="125.455"
        y2="232.296"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="white" stop-opacity="0.5" />
        <stop offset="1" stop-color="white" stop-opacity="0" />
      </linearGradient>
      <linearGradient
        id="paint5_linear_2855_808"
        x1="155.358"
        y1="201.802"
        x2="155.358"
        y2="222.42"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="white" stop-opacity="0.5" />
        <stop offset="1" stop-color="white" stop-opacity="0" />
      </linearGradient>
      <linearGradient
        id="paint6_linear_2855_808"
        x1="200.283"
        y1="147.866"
        x2="200.283"
        y2="203.499"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="white" stop-opacity="0.5" />
        <stop offset="1" stop-color="white" stop-opacity="0" />
      </linearGradient>
      <linearGradient
        id="paint7_linear_2855_808"
        x1="219.219"
        y1="136.789"
        x2="219.219"
        y2="148.558"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="white" stop-opacity="0.5" />
        <stop offset="1" stop-color="white" stop-opacity="0" />
      </linearGradient>
      <linearGradient
        id="paint8_linear_2855_808"
        x1="177.53"
        y1="123.412"
        x2="177.53"
        y2="173.636"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="white" stop-opacity="0.5" />
        <stop offset="1" stop-color="white" stop-opacity="0" />
      </linearGradient>
      <clipPath id="clip0_2855_808">
        <rect
          width="250"
          height="250"
          fill="white"
          transform="translate(0.437866)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const ZhizaoIcon = () => (
  <svg
    width="251"
    height="250"
    viewBox="0 0 251 250"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g opacity="0.5" clip-path="url(#clip0_2877_4545)">
      <path
        d="M250.062 100.5V148.587L225.499 154.075C223.395 161.174 220.533 168.026 216.962 174.513L228.774 194.188L195.149 227.838L175.174 215.812C168.839 219.249 162.155 222.002 155.237 224.025L149.637 250H101.387L95.7244 223.8C89.6046 221.89 83.675 219.417 78.0119 216.413L115.512 178.913C127.292 181.195 139.493 179.637 150.321 174.466C161.149 169.295 170.031 160.785 175.66 150.189C181.29 139.592 183.369 127.469 181.593 115.602C179.816 103.735 174.278 92.7518 165.792 84.2684C157.306 75.785 146.321 70.2494 134.454 68.4762C122.587 66.703 110.464 68.7859 99.8686 74.4183C89.2736 80.0508 80.7664 88.9353 75.5987 99.7645C70.431 110.594 68.8758 122.796 71.1619 134.575L33.6619 172.062C30.6543 166.398 28.1731 160.469 26.2494 154.35L0.0618896 148.65V100.438L26.0119 94.8375C28.0376 87.9203 30.7905 81.2372 34.2244 74.9L22.2369 54.9L55.8619 21.25L75.5494 33.1C82.0157 29.5056 88.862 26.6422 95.9619 24.5625L101.487 0H149.524L154.999 24.2875C162.199 26.4125 169.162 29.2875 175.749 32.8875L195.149 21.25L228.774 54.9L217.149 74.2875C220.774 80.875 223.649 87.8375 225.724 95.05L250.062 100.5ZM79.2744 146.375C76.205 138.569 75.4801 130.037 77.1884 121.825C78.8967 113.613 82.964 106.078 88.8917 100.144C94.8194 94.2097 102.35 90.1339 110.56 88.4164C118.77 86.699 127.303 87.4144 135.112 90.475L131.612 94.0125L113.324 112.312V136.75H137.737L156.037 118.45L159.562 114.925C162.632 122.729 163.357 131.26 161.646 139.469C159.935 147.679 155.864 155.211 149.932 161.139C144 167.067 136.466 171.133 128.255 172.838C120.044 174.543 111.514 173.813 103.712 170.738L42.0119 232.438C40.4106 234.04 38.5092 235.312 36.4164 236.18C34.3236 237.049 32.0802 237.496 29.8145 237.497C27.5487 237.498 25.3049 237.053 23.2112 236.187C21.1175 235.321 19.2148 234.051 17.6119 232.45C16.0089 230.849 14.7371 228.947 13.869 226.855C13.0008 224.762 12.5534 222.518 12.5522 220.253C12.5511 217.987 12.9962 215.743 13.8622 213.649C14.7282 211.556 15.9981 209.653 17.5994 208.05L79.2744 146.375Z"
        fill="url(#paint0_linear_2877_4545)"
      />
    </g>
    <defs>
      <linearGradient
        id="paint0_linear_2877_4545"
        x1="125.062"
        y1="0"
        x2="125.062"
        y2="250"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="white" stop-opacity="0.5" />
        <stop offset="1" stop-color="white" stop-opacity="0" />
      </linearGradient>
      <clipPath id="clip0_2877_4545">
        <rect
          width="250"
          height="250"
          fill="white"
          transform="translate(0.0618896)"
        />
      </clipPath>
    </defs>
  </svg>
);
export const YouxiIcon = () => (
  <svg
    width="251"
    height="250"
    viewBox="0 0 251 250"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      opacity="0.5"
      d="M81.6399 57.5382H168.597C169.539 57.4442 170.491 57.5536 171.387 57.8589C172.283 58.1643 173.103 58.6585 173.792 59.3081C174.48 59.9578 175.021 60.7478 175.378 61.6246C175.735 62.5014 175.9 63.4447 175.861 64.3906V131.75C175.894 132.694 175.726 133.635 175.367 134.509C175.008 135.383 174.467 136.171 173.779 136.82C173.092 137.468 172.274 137.962 171.381 138.27C170.487 138.577 169.538 138.691 168.597 138.602H81.6399C80.699 138.691 79.7501 138.577 78.8565 138.27C77.9629 137.962 77.1452 137.468 76.4578 136.82C75.7704 136.171 75.2292 135.383 74.8703 134.509C74.5113 133.635 74.3429 132.694 74.3763 131.75V64.2878C74.3578 63.352 74.5371 62.4227 74.9026 61.5609C75.2681 60.6992 75.8115 59.9243 76.4972 59.2871C77.1829 58.6499 77.9955 58.1647 78.8817 57.8634C79.768 57.562 80.7079 57.4512 81.6399 57.5382ZM175.861 44.0389H74.3763C70.6698 43.9275 67.069 45.2826 64.3555 47.81C61.6421 50.3375 60.0351 53.8331 59.8834 57.5382V138.5C60.0439 142.199 61.6547 145.686 64.3672 148.206C67.0796 150.726 70.6755 152.076 74.3763 151.965H175.861C179.562 152.076 183.157 150.726 185.87 148.206C188.582 145.686 190.193 142.199 190.354 138.5V57.5382C190.202 53.8331 188.595 50.3375 185.882 47.81C183.168 45.2826 179.567 43.9275 175.861 44.0389ZM67.147 165.498C66.1566 165.31 65.137 165.344 64.1608 165.595C63.1847 165.847 62.276 166.31 61.4997 166.953C60.7234 167.596 60.0985 168.403 59.6695 169.315C59.2406 170.227 59.0182 171.223 59.0182 172.231C59.0182 173.239 59.2406 174.234 59.6695 175.147C60.0985 176.059 60.7234 176.865 61.4997 177.508C62.276 178.151 63.1847 178.615 64.1608 178.866C65.137 179.118 66.1566 179.151 67.147 178.963C68.1374 179.151 69.157 179.118 70.1331 178.866C71.1093 178.615 72.0179 178.151 72.7942 177.508C73.5706 176.865 74.1955 176.059 74.6244 175.147C75.0534 174.234 75.2758 173.239 75.2758 172.231C75.2758 171.223 75.0534 170.227 74.6244 169.315C74.1955 168.403 73.5706 167.596 72.7942 166.953C72.0179 166.31 71.1093 165.847 70.1331 165.595C69.157 165.344 68.1374 165.31 67.147 165.498ZM103.362 165.498C102.372 165.31 101.352 165.344 100.376 165.595C99.3998 165.847 98.4911 166.31 97.7148 166.953C96.9385 167.596 96.3136 168.403 95.8846 169.315C95.4557 170.227 95.2333 171.223 95.2333 172.231C95.2333 173.239 95.4557 174.234 95.8846 175.147C96.3136 176.059 96.9385 176.865 97.7148 177.508C98.4911 178.151 99.3998 178.615 100.376 178.866C101.352 179.118 102.372 179.151 103.362 178.963C104.352 179.151 105.372 179.118 106.348 178.866C107.324 178.615 108.233 178.151 109.009 177.508C109.786 176.865 110.411 176.059 110.84 175.147C111.268 174.234 111.491 173.239 111.491 172.231C111.491 171.223 111.268 170.227 110.84 169.315C110.411 168.403 109.786 167.596 109.009 166.953C108.233 166.31 107.324 165.847 106.348 165.595C105.372 165.344 104.352 165.31 103.362 165.498ZM74.3763 192.463C73.3913 192.29 72.3805 192.335 71.4148 192.595C70.4491 192.854 69.552 193.322 68.7863 193.965C68.0206 194.609 67.405 195.412 66.9827 196.318C66.5603 197.224 66.3414 198.212 66.3414 199.212C66.3414 200.212 66.5603 201.2 66.9827 202.106C67.405 203.013 68.0206 203.816 68.7863 204.459C69.552 205.102 70.4491 205.57 71.4148 205.83C72.3805 206.089 73.3913 206.134 74.3763 205.962C75.3613 206.134 76.3721 206.089 77.3378 205.83C78.3034 205.57 79.2006 205.102 79.9663 204.459C80.7319 203.816 81.3476 203.013 81.7699 202.106C82.1923 201.2 82.4111 200.212 82.4111 199.212C82.4111 198.212 82.1923 197.224 81.7699 196.318C81.3476 195.412 80.7319 194.609 79.9663 193.965C79.2006 193.322 78.3034 192.854 77.3378 192.595C76.3721 192.335 75.3613 192.29 74.3763 192.463ZM110.626 192.463C109.641 192.29 108.63 192.335 107.664 192.595C106.698 192.854 105.801 193.322 105.036 193.965C104.27 194.609 103.654 195.412 103.232 196.318C102.81 197.224 102.591 198.212 102.591 199.212C102.591 200.212 102.81 201.2 103.232 202.106C103.654 203.013 104.27 203.816 105.036 204.459C105.801 205.102 106.698 205.57 107.664 205.83C108.63 206.089 109.641 206.134 110.626 205.962C111.611 206.134 112.621 206.089 113.587 205.83C114.553 205.57 115.45 205.102 116.216 204.459C116.981 203.816 117.597 203.013 118.019 202.106C118.442 201.2 118.661 200.212 118.661 199.212C118.661 198.212 118.442 197.224 118.019 196.318C117.597 195.412 116.981 194.609 116.216 193.965C115.45 193.322 114.553 192.854 113.587 192.595C112.621 192.335 111.611 192.29 110.626 192.463ZM190.354 17.0746C206.286 17.0746 219.339 29.2034 219.339 44.0389V205.962C219.339 220.797 206.286 232.926 190.354 232.926H59.8834C43.9515 232.926 30.8976 220.797 30.8976 205.962V44.0389C30.8976 29.2034 43.9515 17.0746 59.8834 17.0746H190.354Z"
      fill="url(#paint0_linear_2877_4548)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_2877_4548"
        x1="125.119"
        y1="17.0746"
        x2="125.119"
        y2="232.926"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="white" stop-opacity="0.5" />
        <stop offset="1" stop-color="white" stop-opacity="0" />
      </linearGradient>
    </defs>
  </svg>
);
export const XinnengyuanIcon = () => (
  <svg
    width="251"
    height="250"
    viewBox="0 0 251 250"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      opacity="0.5"
      d="M86.9359 24.1251C79.1859 23.6251 70.1859 26.1251 63.5609 35.2501L63.0609 36.2501L42.8109 69.8751L87.5609 95.5001L114.436 50.0001L103.436 31.7501C101.311 28.3751 94.6859 24.6251 86.9359 24.1251ZM161.686 24.1251L104.811 24.5001C106.686 25.0001 110.936 25.7501 114.061 30.8751L143.061 80.3751L125.436 88.7501H180.561L202.561 46.0001L185.686 56.5001L171.686 29.1251C170.061 25.8751 167.686 23.5001 161.686 24.1251ZM213.686 85.1251L168.936 110.375L194.436 153.5H216.436C224.561 153.375 242.311 138.125 232.936 117.75L232.561 116.75L213.686 85.1251ZM15.1859 99.5001L32.0609 108.625L16.9359 134.25C15.0609 137.25 14.3109 140.125 17.6859 145L44.9359 192.5C44.4359 190.375 43.0609 186.25 45.9359 181L75.1859 132.125L92.4359 142.625L70.3109 99.5001H15.1859ZM158.561 146.375L130.311 190.125L158.561 235.875V218L187.561 217.625C191.061 218 194.436 217.25 196.811 211.875L226.061 159.375C224.686 160.75 221.561 163.875 215.436 164.125H158.561V146.375ZM67.9359 164.25L56.9359 185.75C53.0609 192.875 57.5609 215.5 80.3109 217.75L81.3109 218.125H114.436V164.25H67.9359Z"
      fill="url(#paint0_linear_2877_4554)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_2877_4554"
        x1="125.393"
        y1="24.0249"
        x2="125.393"
        y2="235.875"
        gradientUnits="userSpaceOnUse"
      >
        <stop stop-color="white" stop-opacity="0.5" />
        <stop offset="1" stop-color="white" stop-opacity="0" />
      </linearGradient>
    </defs>
  </svg>
);
export const searchIcon = () => (
  <svg
    width="30"
    height="30"
    viewBox="0 0 30 30"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M14.7317 20.5424C14.7656 20.5794 14.8069 20.6089 14.853 20.6289C14.8991 20.6489 14.9489 20.6589 14.9991 20.6584C15.0493 20.6578 15.0989 20.6467 15.1445 20.6257C15.1902 20.6047 15.2308 20.5743 15.2639 20.5365C16.1732 19.5099 19.1752 16.0667 20.2099 14.2263C20.6525 13.4366 20.8333 12.7366 20.8333 11.8106C20.8333 8.74517 18.2163 6.24996 15 6.24996C11.7836 6.24996 9.16665 8.72767 9.16665 11.7749C9.16665 12.5828 9.36279 13.4111 9.73394 14.1738C10.6046 15.9778 13.7837 19.5091 14.7317 20.5424ZM15 14.2708C14.4198 14.2708 13.8634 14.0403 13.4532 13.6301C13.043 13.2199 12.8125 12.6635 12.8125 12.0833C12.8125 11.5031 13.043 10.9467 13.4532 10.5365C13.8634 10.1263 14.4198 9.8958 15 9.8958C15.5802 9.8958 16.1365 10.1263 16.5468 10.5365C16.957 10.9467 17.1875 11.5031 17.1875 12.0833C17.1875 12.6635 16.957 13.2199 16.5468 13.6301C16.1365 14.0403 15.5802 14.2708 15 14.2708Z"
      fill="#7700FE"
    />
    <path
      d="M20.9303 17.5535C20.8827 17.6366 20.8519 17.7282 20.8396 17.8232C20.8274 17.9182 20.834 18.0146 20.8591 18.107C20.8841 18.1995 20.9271 18.286 20.9856 18.3618C21.0442 18.4376 21.117 18.5012 21.2001 18.5488C22.0532 19.0395 22.2917 19.4924 22.2917 19.7804C22.2917 19.9656 22.202 20.2135 21.887 20.5146C21.5698 20.8173 21.0725 21.1235 20.3988 21.3955C19.0534 21.9387 17.1452 22.2916 15 22.2916C12.8548 22.2916 10.9466 21.9387 9.60124 21.3955C8.92676 21.1235 8.4302 20.8173 8.11374 20.5146C7.79728 20.2135 7.70832 19.9656 7.70832 19.7804C7.70832 19.4924 7.94603 19.0395 8.80061 18.5488C8.96469 18.4507 9.08368 18.2921 9.13195 18.1071C9.18022 17.9221 9.15391 17.7256 9.05869 17.5598C8.96347 17.394 8.80696 17.2723 8.62285 17.2208C8.43875 17.1693 8.24178 17.1921 8.07436 17.2844C7.09144 17.8481 6.24998 18.6808 6.24998 19.7804C6.24998 20.4899 6.60874 21.0943 7.10603 21.569C7.6004 22.0415 8.27853 22.4338 9.05509 22.7474C10.6133 23.3781 12.7155 23.75 15 23.75C17.2845 23.75 19.3867 23.3774 20.9449 22.7474C21.7215 22.4338 22.3996 22.0415 22.894 21.569C23.3913 21.0951 23.75 20.4899 23.75 19.7804C23.75 18.6808 22.9085 17.8481 21.9256 17.2844C21.8426 17.2368 21.7509 17.206 21.6559 17.1938C21.561 17.1815 21.4645 17.1881 21.3721 17.2132C21.2797 17.2382 21.1931 17.2813 21.1173 17.3398C21.0415 17.3983 20.978 17.4712 20.9303 17.5542V17.5535Z"
      fill="#7700FE"
    />
  </svg>
);
export const menuIcon = () => (
  <svg
    width="30"
    height="30"
    viewBox="0 0 30 30"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M24.2572 13.97C24.826 13.97 25.2871 14.4312 25.2871 15C25.2871 15.5631 24.8352 16.0207 24.2742 16.0298L24.2572 16.03H5.74286C5.17401 16.03 4.71289 15.5688 4.71289 15C4.71289 14.4369 5.16484 13.9793 5.72581 13.9702L5.74286 13.97H24.2572ZM24.2572 7.73872C24.826 7.73872 25.2871 8.19987 25.2871 8.76869C25.2871 9.33183 24.8352 9.78942 24.2742 9.79853L24.2572 9.79866H5.74286C5.17401 9.79866 4.71289 9.33752 4.71289 8.76869C4.71289 8.20556 5.16484 7.74797 5.72581 7.73885L5.74286 7.73872H24.2572ZM24.2572 20.2013C24.826 20.2013 25.2871 20.6625 25.2871 21.2313C25.2871 21.7944 24.8352 22.252 24.2742 22.2611L24.2572 22.2613H5.74286C5.17401 22.2613 4.71289 21.8001 4.71289 21.2313C4.71289 20.6682 5.16484 20.2106 5.72581 20.2015L5.74286 20.2013H24.2572Z"
      fill="#7938EB"
    />
  </svg>
);
