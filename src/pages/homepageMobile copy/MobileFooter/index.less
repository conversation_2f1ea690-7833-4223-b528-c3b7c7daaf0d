.mobileFooter {
  width: 100%;
  background-color: #fbfcff;
  padding-top: 2.5rem;

  .footerContent {
    display: flex;
    flex-direction: row;
    gap: 4.8rem;
    padding: 0 2.5rem;

    .logoSection {
      display: flex;
      flex-direction: column;
      gap: 2rem;

      .logo {
        width: 8.333rem;
        height: auto;
      }

      .productLinks {
        display: flex;
        flex-direction: column;
        gap: 0.833rem;
      }
    }

    .linksSection {
      display: flex;
      flex-direction: column;
      gap: 1.25rem;

      .solutionLinks,
      .companyLinks {
        display: flex;
        flex-direction: column;
        gap: 0.833rem;
      }
    }

    .columnTitle {
      font-size: 1.333rem;
      font-weight: 700;
      color: #666666;
      margin-bottom: 0.417rem;
    }

    .linkList {
      display: flex;
      flex-direction: column;
      gap: 0.833rem;

      .footerLink {
        font-size: 1.167rem;
        color: #666666;
        text-decoration: none;
        line-height: 1.4;

        &:hover {
          color: #3463fc;
        }
      }
    }
  }

  .copyright {
    height: 3.333rem;
    background-color: #3463fc;
    margin-top: 2.5rem;
    display: flex;
    justify-content: center;
    align-items: center;

    .copyrightText {
      font-size: 0.833rem;
      color: #ffffff;
      text-align: center;
    }
  }
}
