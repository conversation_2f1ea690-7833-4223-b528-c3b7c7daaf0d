import React, {
  useState,
  useRef,
  useEffect,
  forwardRef,
  useImperativeHandle,
} from 'react';
import {
  useDispatch,
  getIntl,
  FormattedMessage,
  history,
  useSelector,
  getLocale,
} from 'umi';
import { generateRandomNumber } from '@/utils/utils';
import {
  Button,
  notification,
  Spin,
  Form,
  Row,
  Col,
  Input,
  Select,
  Radio,
  Collapse,
  Checkbox,
  InputNumber,
  Popconfirm,
  Modal,
  message,
  Table,
  Typography,
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import styles from './index.less';
import HighlightTextarea from '../VariableComponents';
// import NewComponents from '../NewComponents';
import data from '@emoji-mart/data';
import Picker from '@emoji-mart/react';

import {
  AiBtnIcon,
  UpArrowIcon,
  DownArrowIcon,
  IntentionClassificationManagementIcon,
  EmojiIcon,
} from './icon';
import DeleteIcon from '../../../assets/new-delete-list-icon.png';
import IntentionSuccessIcon from '../../../assets/intention-success-icon.png';
import DeleteTipsIcon from '../../../assets/delete-tips-icon.png';
import IntentionClassificationAddIcon from '../../../assets/intention-classification-add.png';
import IntentionClassificationDeleteIcon from '../../../assets/intention-classification-delete.png';
import { ReactComponent as Search } from '../../../assets/Search.svg';
import NoDataImg from '../../../assets/no-data-img.jpg';
import VariablePromptIcon from '../../../assets/variable-prompt-icon.svg';
import DeleteReplyIcon from '@/assets/email-template-delete.png';
import EditorReplyIcon from '@/assets/email-template-editor.png';
import SaveReplyIcon from '@/assets/save-editor-content.png';
import CancelSaveReplyIcon from '@/assets/cancel-editor-content.png';
const { TextArea } = Input;
const NewIntent = () => {
  const dispatch = useDispatch();
  const { highlightHtml } = useSelector(({ intentionManagement }) => ({
    highlightHtml: intentionManagement.highlightHtml,
  }));
  const formRef = useRef(null); // 创建一个 ref 来保存 EditorJS 实例
  const dynamicLlmRef = useRef(); // 创建一个 ref 来保存 DynamicLlmExtractAttribute 实例
  const [loading, setLoading] = useState(false);
  const [loadingAddBtn, setLoadingAddBtn] = useState(false);
  const [loadingModal, setLoadingModal] = useState(false);
  const [loadingBtn, setLoadingBtn] = useState(false);
  // 语言下拉列表
  const [translationCodeList, setTranslationCodeList] = useState([]);
  // 创建意图成功弹窗
  const [intentionSuccess, setIntentionSuccess] = useState(false);
  // 意图话术类型 静态话术---1  动态话术---2
  const [scriptTypeValue, setScriptTypeValue] = useState(1);
  // 静态/动态话术list
  const [staticLanguageTechnique, setStaticLanguageTechnique] = useState([]);
  const [dynamicLanguageTechnique, setDynamicLanguageTechnique] = useState([]);
  // 话术添加数量
  const [techniqueNum, setTechniqueNum] = useState(1);
  // 话术变量数量
  const [verbalVariablesNum, setVerbalVariablesNum] = useState(0);
  const [
    verbalVariablesRenderingNum,
    setVerbalVariablesRenderingNum,
  ] = useState(0);
  // 话术变量标题数量
  const [verbalVariablesText, setVerbalVariablesText] = useState(1);
  // 话术产生的变量值
  const [variables, setVariables] = useState([]);
  // 页面所有变量列表及值
  const [variablesRendering, setVariablesRendering] = useState([]);
  // 动态话术变量是否展开
  const [contractStatus, setContractStatus] = useState(0);
  // 变量高亮组件默认格式
  const [highlightKey, setHighlightKey] = useState(['{}']);
  // 意图分类下拉列表
  const [optionsList, setOptionsList] = useState([]);
  // 新增意图分类输入框值
  const [inputValue, setInputValue] = useState('');
  // 搜索意图分类输入框值
  const [searchText, setSearchText] = useState('');
  // 意图分类选择值
  const [optionsSelect, setOptionsSelect] = useState([]);
  // 意图分类管理弹窗
  const [managementModal, setManagementModal] = useState(false);
  // 校验正则下拉列表
  const [regexDefList, setRegexDefList] = useState([]);
  // 属性格式正则校验值
  const [regexDefValue, setRegexDefValue] = useState('');
  // 意图ID
  const [intentId, setIntentId] = useState('');
  // 意图名称
  const [intentName, setIntentName] = useState('');
  // 意图请求理由
  const [requestReason, setRequestReason] = useState('');
  // 页面状态
  const [status, setStatus] = useState('add');
  // 修改意图详情
  const [editorDetail, setEditorDetail] = useState({});
  // 动态话术变量修改回显值
  const [variablesValue, setVariablesValue] = useState([]);
  // 展示表情包
  const [shownEmoji, setShownEmoji] = useState({});
  useEffect(() => {
    // 翻译的语言列表
    const googleLanguage = JSON.parse(localStorage.getItem('languageLocal'));
    setTranslationCodeList(googleLanguage);
    queryIntentionClassification();
    getAllRegexDefList();
    groupVariables();
  }, []);
  useEffect(() => {
    if (history.location.state) {
      let selectIntentItem = history.location.state.selectIntentItem;
      if (selectIntentItem) {
        let status = history.location.state.status;
        let intentId = selectIntentItem.intentId;
        setIntentId(intentId);
        setStatus(status);
        setEditorDetail(selectIntentItem);
      } else {
        setStatus('add');
      }
    } else {
      let selectIntentItem = JSON.parse(
        localStorage.getItem('selectIntentItem'),
      );
      if (selectIntentItem) {
        let intentId = selectIntentItem.intentId;
        setIntentId(intentId);
        setStatus('editor');
        setEditorDetail(selectIntentItem);
      } else {
        setStatus('add');
      }
    }
  }, [history.location.state]);
  useEffect(() => {
    // if (highlightHtml) {
    //   // 正则表达式匹配 {} 中的英文变量
    //   const regex = /\{([a-zA-Z]+)\}/g;
    //   let variables = [];
    //   let newData = [];
    //   // 遍历每个对象，提取其中的英文变量
    //   highlightHtml?.forEach(item => {
    //     const matches = [];
    //     const itemData = [];
    //     let match;
    //     // 使用正则的 exec 方法来遍历所有匹配项
    //     while ((match = regex.exec(item.highlightHtmlText)) !== null) {
    //       matches.push(match[1]); // match[1] 是捕获到的英文变量
    //       let items = {
    //         attributeTitle: match[1],
    //         attributeName: match[1],
    //         isRequired: 0,
    //       };
    //       itemData.push(items);
    //     }
    //     // 将每个对象中的变量加入到 variables 数组中
    //     if (matches.length > 0) {
    //       // 合并两个数组并去重
    //       const mergedArray1 = [...variables, ...matches].reduce(
    //         (acc, curr) => {
    //           // 检查是否已存在相同的 attributeTitle
    //           if (!acc.some(item => item === curr)) {
    //             acc.push(curr);
    //           }
    //           return acc;
    //         },
    //         [],
    //       );
    //       // variables = [...variables, ...matches];
    //       variables = [...mergedArray1];
    //       // 合并两个数组并去重
    //       const mergedArray = [...newData, ...itemData].reduce((acc, curr) => {
    //         // 检查是否已存在相同的 attributeTitle
    //         if (
    //           !acc.some(item => item.attributeTitle === curr.attributeTitle)
    //         ) {
    //           acc.push(curr);
    //         }
    //         return acc;
    //       }, []);
    //       newData = [...mergedArray];
    //       // newData = [...newData, ...itemData];
    //     }
    //   });
    //   // 统计变量的个数
    //   const variableCount = variables.length;
    //   const mergedArray1 = [...variablesRendering, ...newData].reduce(
    //     (acc, curr) => {
    //       // 检查是否已存在相同的 attributeTitle
    //       if (!acc.some(item => item.attributeTitle === curr.attributeTitle)) {
    //         acc.push(curr);
    //       }
    //       return acc;
    //     },
    //     [],
    //   );
    //   formRef.current?.setFieldsValue({ variablesRendering: mergedArray1 });
    //   // let newScript = [];
    //   // highlightHtml.map(item => {
    //   //   console.log(item, 'item highlightHtml');
    //   //   let newItem = {
    //   //     scriptId: item.name,
    //   //     scriptName: item.highlightHtmlText,
    //   //     scriptLanguage: '中文',
    //   //   };
    //   //   newScript.push(newItem);
    //   // });
    //   // setStaticLanguageTechnique(newScript);
    //   setVariables(variables); // 保存匹配到的英文变量
    //   setVariablesRendering(mergedArray1); // 保存重新组装后的匹配到的英文变量
    //   setVerbalVariablesNum(variableCount); // 保存匹配到的变量数量
    //   setVerbalVariablesRenderingNum(variableCount); // 保存匹配到的变量数量
    // }
  }, [highlightHtml]);
  useEffect(() => {
    if (intentId && status === 'editor') {
      queryIntentDetail();
    }
  }, [intentId, status]);

  // 查询意图详情
  const queryIntentDetail = () => {
    setLoading(true);
    dispatch({
      type: 'intentionManagement/queryIntentDetail',
      payload: intentId,
      callback: response => {
        setLoading(false);
        if (response.code === 200) {
          let detailData = response.data;
          let scriptType = detailData.scriptType;
          setIntentName(detailData.intentName);
          setRequestReason(detailData.requestReason);
          let detailList = {
            intentionName: detailData.intentName,
            intentionClassification: detailData.categoryId?.split(','),
            intentionReason: detailData.requestReason,
            scriptType: detailData.scriptType,
          };
          let scriptList = detailData.scriptList;
          setTechniqueNum(scriptList.length);
          const newData = [];
          console.log(detailData, 'detailData queryIntentDetail');

          scriptList?.forEach((item, index) => {
            let itemData;
            itemData = {
              scriptId: 'languageTechnique' + index,
              scriptName: item.scriptName,
              scriptLanguage: item.scriptLanguage,
            };
            detailList['languageTechnique' + index] = item.scriptName;
            newData.push(itemData);
            // if (index === 0) {
            //   itemData = {
            //     scriptId: 'defaultLanguageTechnique',
            //     scriptName: item.scriptName,
            //     scriptLanguage: item.scriptLanguage,
            //   };
            //   detailList.defaultLanguageTechnique = item.scriptName;
            //   newData.push(itemData);
            // } else {
            //   itemData = {
            //     scriptId: 'languageTechnique' + index,
            //     scriptName: item.scriptName,
            //     scriptLanguage: item.scriptLanguage,
            //   };
            //   detailList['languageTechnique' + index] = item.scriptName;
            //   newData.push(itemData);
            // }
          });
          if (scriptType === 1) {
            setStaticLanguageTechnique(newData);
          } else {
            setDynamicLanguageTechnique(newData);
            const attributesList = detailData.attributesList;
            setTimeout(() => {
              if (dynamicLlmRef.current) {
                const llmData = attributesList.map((item, index) => ({
                  key: String(Date.now() + index),
                  attributeName: item.attributeName,
                  attributeFormat: item.attributeFormat,
                  attributeValues: item.attributeValues,
                  isRequired: item.isRequired,
                }));
                dynamicLlmRef.current.setDynamicLlmData(llmData);
              }
            }, 0);
          }

          formRef.current?.setFieldsValue(detailList);
          setScriptTypeValue(detailData.scriptType);
        } else {
          notification.error({
            message: response.msg,
          });
        }
        // if (detailData.scriptType === 2) {
        // const dataList = [];
        // scriptList?.forEach((item, index) => {
        //   let itemData;
        //   if (index === 0) {
        //     itemData = {
        //       name: 'defaultLanguageTechnique',
        //       highlightHtmlText: item.scriptName,
        //     };
        //     dataList.push(itemData);
        //   } else {
        //     itemData = {
        //       name: 'languageTechnique' + index,
        //       highlightHtmlText: item.scriptName,
        //     };
        //     dataList.push(itemData);
        //   }
        // });
        // dispatch({
        //   type: 'intentionManagement/saveHighlightHtml',
        //   payload: dataList,
        // });
        // const attributesList = detailData.attributesList;
        // 使用 map 方法来遍历数组并新增 attributeTitle
        // const updatedData = attributesList.map(item => ({
        //   ...item,
        //   attributeTitle: item.attributeName, // 新增 attributeTitle 属性，值为 attributeName
        // }));

        // setVariablesRendering(updatedData);
        // setVerbalVariablesRenderingNum(attributesList.length);
        // detailList.variablesRendering = updatedData;

        // 使用 setTimeout 确保组件已经挂载
        //   setTimeout(() => {
        //     if (dynamicLlmRef.current) {
        //       const llmData = attributesList.map((item, index) => ({
        //         key: String(Date.now() + index),
        //         attributeName: item.attributeName,
        //         attributeFormat: item.attributeFormat,
        //         attributeValues: item.attributeValues,
        //         isRequired: item.isRequired,
        //       }));
        //       dynamicLlmRef.current.setDynamicLlmData(llmData);
        //     }
        //   }, 0);
        // }
        // formRef.current?.setFieldsValue(detailList);
        // setScriptTypeValue(detailData.scriptType);
        // } else {
        //   notification.error({
        //     message: response.msg,
        //   });
        // }
      },
    });
  };
  // 获取存储到智能体的变量
  const groupVariables = () => {
    setLoading(true);
    dispatch({
      type: 'intentionManagement/groupVariables',
      callback: response => {
        setLoading(false);
        if (response.code === 200) {
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 获取正则下拉列表
  const getAllRegexDefList = () => {
    setLoading(true);
    dispatch({
      type: 'intentionManagement/getAllRegexDefList',
      payload: '',
      callback: response => {
        setLoading(false);
        if (response.code === 200) {
          setRegexDefList(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 查询意图分类列表
  const queryIntentionClassification = () => {
    setLoadingModal(true);
    dispatch({
      type: 'intentionManagement/queryIntentionClassification',
      payload: '',
      callback: response => {
        setLoadingModal(false);
        if (response.code === 200) {
          setOptionsList(response.data);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 显示意图分类管理弹窗
  const handleShowIntentionClassification = () => {
    setManagementModal(true);
  };
  // 关闭意图分类管理弹窗
  const handleCancelIntentionClassification = () => {
    setManagementModal(false);
    setInputValue('');
    setSearchText('');
  };
  // 处理搜索框的输入
  const handleSearch = value => {
    setSearchText(value);
  };
  // 新增意图分类
  const handleSelect = value => {
    if (value) {
      setLoadingModal(true);
      let params = {
        categoryName: value,
      };
      dispatch({
        type: 'intentionManagement/saveCategory',
        payload: params,
        callback: response => {
          setLoadingModal(false);
          if (response.code === 200) {
            setInputValue('');
            setSearchText('');
            queryIntentionClassification();
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    } else {
      notification.warning({
        message: getIntl().formatMessage({
          id: 'new.intent.intention.classification.tips',
          defaultValue: '意图分类不能为空！',
        }),
      });
    }
  };
  // 删除选项
  const handleDelete = option => {
    setLoadingModal(true);
    let categoryId = option.categoryId;
    dispatch({
      type: 'intentionManagement/deleteCategory',
      payload: categoryId,
      callback: response => {
        setLoadingModal(false);
        if (response.code === 200) {
          setInputValue('');
          setSearchText('');
          queryIntentionClassification();
          // 当删除意图分类后筛查删除选择框选中的选项  optionsSelect
          const newValue = optionsSelect.filter(item => item !== categoryId);
          setOptionsSelect(newValue);
          formRef.current?.setFieldsValue({
            intentionClassification: newValue,
          });

          notification.success({
            message: response.msg,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 选择意图分类
  const handleChangeIntentionClassification = value => {
    setOptionsSelect(value);
  };
  // 添加一个state来存储LLM表格的临时数据
  const [tempDynamicLlmData, setTempDynamicLlmData] = useState([]);

  // 切换意图话术类型--静态话术/动态话术
  const onChange = e => {
    const newType = e.target.value;
    // 如果当前是动态话术,保存LLM表格数据
    if (scriptTypeValue === 2 && dynamicLlmRef.current) {
      setTempDynamicLlmData(dynamicLlmRef.current.getDynamicLlmData());
    }
    setScriptTypeValue(newType);
    // 如果切换到动态话术,恢复之前保存的LLM数据
    if (newType === 2) {
      setTimeout(() => {
        if (dynamicLlmRef.current) {
          dynamicLlmRef.current.setDynamicLlmData(tempDynamicLlmData);
        }
      }, 0);
    }
    // setStaticLanguageTechnique([]);
    // setTechniqueNum(1);
    // formRef.current?.setFieldsValue({});
    // setVerbalVariablesNum(0);
    // setVerbalVariablesRenderingNum(0);
    // setVerbalVariablesText(0);
    // setVariables([]);
    // setVariablesRendering([]);
    // if (dynamicLlmRef.current) {
    //   dynamicLlmRef.current.resetData();
    // }
    // dispatch({
    //   type: 'intentionManagement/saveHighlightHtml',
    //   payload: [
    //     {
    //       name: 'defaultLanguageTechnique',
    //       highlightHtmlText: '',
    //     },
    //   ],
    // });
  };
  // AI生成意图话术select
  const aiSelectLanguageChange = (value, item, scriptType) => {
    // 修改staticLanguageTechnique中的语言

    if (scriptType === 1) {
      const updatedList = staticLanguageTechnique.map(
        items =>
          items.scriptId === item.scriptId
            ? {
                ...items,
                scriptLanguage: value,
              } // 返回更新后的对象
            : items, // 保持不变的对象
      );
      setStaticLanguageTechnique(updatedList);
    } else {
      const updatedList = dynamicLanguageTechnique.map(
        items =>
          items.scriptId === item.scriptId
            ? {
                ...items,
                scriptLanguage: value,
              } // 返回更新后的对象
            : items, // 保持不变的对象
      );
      setDynamicLanguageTechnique(updatedList);
    }
  };
  // 静态话术输入
  const handleChangeLanguageTechnique = (e, itemData, scriptType) => {
    if (scriptType === 1) {
      const updatedList = staticLanguageTechnique.map(
        item =>
          item.scriptId === itemData.scriptId
            ? {
                ...item,
                scriptName: e.target.value,
              } // 返回更新后的对象
            : item, // 保持不变的对象
      );
      setStaticLanguageTechnique(updatedList);
    } else {
      const updatedList = dynamicLanguageTechnique.map(
        item =>
          item.scriptId === itemData.scriptId
            ? {
                ...item,
                scriptName: e.target.value,
              } // 返回更新后的对象
            : item, // 保持不变的对象
      );
      setDynamicLanguageTechnique(updatedList);
    }
  };
  // 新增静态/动态话术
  const handleAddStaticLanguageTechnique = async (
    scriptName,
    scriptType,
    language,
  ) => {
    let currentLanguage = language || getLocale();
    return new Promise((resolve, reject) => {
      // 生成6为随机ID
      const num = generateRandomNumber(6);
      if (techniqueNum < 100) {
        setTechniqueNum(techniqueNum + 1);
        if (!language) {
          // 判断是否是AI生成

          if (scriptType === 1) {
            currentLanguage =
              staticLanguageTechnique.length > 0
                ? staticLanguageTechnique[staticLanguageTechnique.length - 1]
                    .scriptLanguage
                : currentLanguage;
          } else {
            currentLanguage =
              dynamicLanguageTechnique.length > 0
                ? dynamicLanguageTechnique[dynamicLanguageTechnique.length - 1]
                    .scriptLanguage
                : currentLanguage;
          }
        }
        const newData = {
          scriptId: 'languageTechnique' + num,
          scriptName: scriptName,
          scriptLanguage: currentLanguage,
        };
        if (scriptType === 1) {
          setStaticLanguageTechnique(prev => {
            const updated = [...prev, newData];
            // 如果有scriptName,更新form表单
            if (scriptName) {
              const detailList = {};
              detailList['languageTechnique' + num] = scriptName;
              formRef.current?.setFieldsValue(detailList);
            }
            return updated;
          });
        } else {
          setDynamicLanguageTechnique(prev => {
            const updated = [...prev, newData];
            // 如果有scriptName,更新form表单
            if (scriptName) {
              const detailList = {};
              detailList['languageTechnique' + num] = scriptName;
              formRef.current?.setFieldsValue(detailList);
            }
            return updated;
          });
        }
        if (scriptName) {
          const detailList = {};
          detailList['languageTechnique' + num] = scriptName;
          formRef.current?.setFieldsValue(detailList);
        }
        setTimeout(() => {
          resolve();
        }, 0);
      } else {
        notification.warning({
          message: getIntl().formatMessage({
            id: 'new.intent.add.static.language.technique.tips',
            defaultValue: '最多创建100个意图话术',
          }),
        });
        resolve();
      }
    });
  };
  // 获取意图名称
  const handleChangeIntentionName = e => {
    setIntentName(e.target.value);
  };
  // 获取意图请求原因
  const handleChangeRequestReason = e => {
    setRequestReason(e.target.value);
  };
  // AI生成意图话术
  const handleAiGeneratedIntentSpeech = scriptType => {
    setLoading(true);
    if (intentName && requestReason) {
      if (techniqueNum < 100) {
        // 使用 filter 过滤空字符串，map 提取 scriptName
        const scriptNames = staticLanguageTechnique
          .filter(item => item.scriptName)
          .map(item => item.scriptName);
        // 获取选择的语言数组，并转换为逗号分隔的字符串
        const selectedLanguages = formRef.current.getFieldValue(
          'intentionLanguage',
        );
        const languageString = Array.isArray(selectedLanguages)
          ? selectedLanguages.join(',')
          : selectedLanguages || 'zh'; // 如果未选择，默认使用中文
        let params = {
          intentName: intentName,
          requestReason: requestReason,
          intentInfoList: scriptNames,
          language: languageString,
        };
        formRef.current?.setFieldsValue({
          intentionLanguage: [],
        }); // 清空ai生成意图语言选择
        dispatch({
          type: 'intentionManagement/aiScriptCreate',
          payload: params,
          callback: async response => {
            setLoading(false);
            if (response.code === 200) {
              // 返回的数据为JSON 循环对象 调用handleAddStaticLanguageTechnique
              const languageScripts = JSON.parse(response.data);
              for (const [language, scripts] of Object.entries(
                languageScripts,
              )) {
                if (Array.isArray(scripts)) {
                  for (const script of scripts) {
                    await handleAddStaticLanguageTechnique(
                      script,
                      scriptType,
                      language,
                    );
                  }
                }
              }
            } else {
              notification.error({
                message: response.msg,
              });
            }
          },
        });
      } else {
        setLoading(false);
        notification.warning({
          message: getIntl().formatMessage({
            id: 'new.intent.add.static.language.technique.tips',
            defaultValue: '最多创建100个意图话术',
          }),
        });
      }
    } else {
      setLoading(false);
      notification.warning({
        message: getIntl().formatMessage({
          id: 'new.intent.add.ai.script.create',
          defaultValue: '意图名称和意图请求原因不能为空！',
        }),
      });
    }
  };
  // 删除静态/动态话术
  const handleDeleteStaticLanguageTechnique = (itemData, scriptType) => {
    setTechniqueNum(techniqueNum - 1);
    if (scriptType === 1) {
      setStaticLanguageTechnique(prevState => {
        const newData = prevState.filter(
          item => item.scriptId !== itemData.scriptId,
        );
        console.log(newData);
        return newData;
      });
    } else {
      setDynamicLanguageTechnique(prevState => {
        const newData = prevState.filter(
          item => item.scriptId !== itemData.scriptId,
        );
        console.log(newData);
        return newData;
      });
    }
  };

  // 判断重复并提示
  const checkDuplicates = data => {
    const scriptNames = data.map(item => item.scriptName);
    const duplicates = scriptNames.reduce((acc, current) => {
      // 如果当前 scriptName 已经出现过，就添加到 duplicates 中
      if (acc[current]) {
        acc[current] = acc[current] + 1;
      } else {
        acc[current] = 1;
      }
      return acc;
    }, {});

    // 从 duplicates 中筛选出出现次数超过 1 次的重复项
    const duplicateEntries = Object.keys(duplicates)
      .filter(key => duplicates[key] > 1)
      .map(key => key);

    return duplicateEntries;
  };
  // 新增意图
  const onFinish = values => {
    setLoadingBtn(true);
    let params;
    if (scriptTypeValue === 1) {
      // 必须有一条话术
      if (staticLanguageTechnique.length === 0) {
        notification.warning({
          message: getIntl().formatMessage({
            id: 'new.intent.add.required.one.speech',
            defaultValue: '必须有一条话术',
          }),
        });
        setLoadingBtn(false);
        return;
      }
      // 处理数据，构建新的数组---静态话术
      const newScriptList = staticLanguageTechnique.map(item => {
        // 从 values 中获取对应的 scriptName
        // const scriptName = values[item.scriptId] || ''; // 如果没有对应的值，默认使用空字符串
        return {
          scriptName: item.scriptName,
          intentId: intentId,
          scriptLanguage: item.scriptLanguage,
        };
      });
      const duplicateEntries = checkDuplicates(newScriptList);
      if (duplicateEntries.length > 0) {
        notification.warning({
          message: getIntl().formatMessage(
            { id: 'new.intent.add.variables.only.tips.3' },
            { script: duplicateEntries.join(', ') },
          ),
        });
        setLoadingBtn(false);
        return;
      }
      params = {
        intentId: intentId,
        intentName: values.intentionName,
        categoryId:
          values.intentionClassification &&
          values.intentionClassification.join(','),
        scriptType: scriptTypeValue,
        requestReason: values.intentionReason,
        scriptList: newScriptList,
        attributesList: [],
      };
    } else {
      // 检查是否有正在编辑的动态话术
      if (dynamicLlmRef.current && dynamicLlmRef.current.isEditing()) {
        notification.warning({
          message: getIntl().formatMessage({
            id: 'new.intent.add.dynamic.speech.editing.tips',
            defaultValue: '请先保存正在编辑的动态话术意图属性变量',
          }),
        });
        setLoadingBtn(false);
        return;
      }
      // 必须有一条话术
      if (dynamicLanguageTechnique.length === 0) {
        notification.warning({
          message: getIntl().formatMessage({
            id: 'new.intent.add.required.one.speech',
            defaultValue: '必须有一条话术',
          }),
        });
        setLoadingBtn(false);
        return;
      }
      // 处理数据，构建新的数组---动态话术
      const dynamicLlmData = dynamicLlmRef.current.getDynamicLlmData();
      const newScriptList = dynamicLanguageTechnique.map(item => {
        // 从 values 中获取对应的 scriptName
        const scriptName = values[item.scriptId] || ''; // 如果没有对应的值，默认使用空字符串
        return {
          scriptName: item.scriptName,
          intentId: intentId,
          scriptLanguage: item.scriptLanguage,
        };
      });
      const duplicateEntries = checkDuplicates(newScriptList);
      if (duplicateEntries.length > 0) {
        notification.warning({
          message: getIntl().formatMessage(
            { id: 'new.intent.add.variables.only.tips.3' },
            { script: duplicateEntries.join(', ') },
          ),
        });
        setLoadingBtn(false);
        return;
      }
      console.log(newScriptList);
      params = {
        intentId: intentId,
        intentName: values.intentionName,
        categoryId:
          values.intentionClassification &&
          values.intentionClassification.join(','),
        scriptType: scriptTypeValue,
        requestReason: values.intentionReason,
        scriptList: newScriptList,
        attributesList: dynamicLlmData,
      };
    }
    console.log(params);
    dispatch({
      type: 'intentionManagement/saveIntention',
      payload: params,
      callback: response => {
        setLoadingBtn(false);
        if (response.code === 200) {
          localStorage.removeItem('selectIntentItem');
          notification.success({
            message: response.msg,
          });
          if (status === 'editor') {
            history.push('/intentionManagement');
          } else {
            setIntentId(response.data.intentId);
            setIntentionSuccess(true);
            localStorage.setItem(
              'selectIntentItem',
              JSON.stringify(response.data),
            );
          }
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 取消新增意图
  const backHistory = () => {
    localStorage.removeItem('selectIntentItem');
    setVerbalVariablesNum(0);
    setVerbalVariablesRenderingNum(0);
    setVerbalVariablesText(0);
    setVariables([]);
    setVariablesRendering([]);
    // dispatch({
    //   type: 'intentionManagement/saveHighlightHtml',
    //   payload: [
    //     {
    //       name: 'defaultLanguageTechnique',
    //       highlightHtmlText: '',
    //     },
    //   ],
    // });
    history.push('/intentionManagement');
  };
  // 意图创建成功--返回
  const returnIntentionList = () => {
    localStorage.removeItem('selectIntentItem');
    setVerbalVariablesNum(0);
    setVerbalVariablesRenderingNum(0);
    setVerbalVariablesText(0);
    setVariables([]);
    setVariablesRendering([]);
    // dispatch({
    //   type: 'intentionManagement/saveHighlightHtml',
    //   payload: [
    //     {
    //       name: 'defaultLanguageTechnique',
    //       highlightHtmlText: '',
    //     },
    //   ],
    // });
    history.push('/intentionManagement');
  };
  // 创建意图成功--跳转新建智能体
  const handleCreateIntelligentAgent = () => {
    localStorage.removeItem('selectIntentItem');
    setVerbalVariablesNum(0);
    setVerbalVariablesRenderingNum(0);
    setVerbalVariablesText(0);
    setVariables([]);
    setVariablesRendering([]);
    // dispatch({
    //   type: 'intentionManagement/saveHighlightHtml',
    //   payload: [
    //     {
    //       name: 'defaultLanguageTechnique',
    //       highlightHtmlText: '',
    //     },
    //   ],
    // });
    // history.push(
    //   '/externalIntelligentAgentList/createExternalIntelligentAgent',
    // );
    history.push({
      pathname: '/externalIntelligentAgentList/createExternalIntelligentAgent',
      state: {
        newIntentId: intentId,
      },
    });
  };

  const [aiSelectLanguage, setAiSelectLanguage] = useState(false);

  return (
    <div className={styles.newIntentContainer}>
      <Spin spinning={loading}>
        <p className="blueBorder">
          <FormattedMessage id="new.intent.title.add" defaultValue="添加意图" />
        </p>
        <div className={styles.addContainer}>
          <div className={styles.addDetailContainer}>
            <div className={styles.line}></div>
            <div className={styles.formContainer}>
              <Form
                name="basic"
                ref={formRef}
                onFinish={onFinish}
                autoComplete="off"
                layout="vertical"
                initialValues={{
                  scriptType: scriptTypeValue,
                }}
              >
                <Row gutter={24}>
                  <Col span={10}>
                    <div className={styles.secondTitle}>
                      <div className={styles.circleContainer}></div>
                      <FormattedMessage
                        id="new.intent.intention.basic.information"
                        defaultValue="意图基本信息"
                      ></FormattedMessage>
                    </div>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={6}>
                    <Form.Item
                      label={getIntl().formatMessage({
                        id: 'new.intent.intention.name',
                        defaultValue: '意图名称',
                      })}
                      name="intentionName"
                      rules={[
                        {
                          required: true,
                          message: getIntl().formatMessage({
                            id: 'intention.management.intent.name.placeholder',
                            defaultValue: '请输入意图名称',
                          }),
                        },
                        {
                          max: 40,
                          message: (
                            <FormattedMessage
                              id="new.intent.intention.name.maxlength"
                              defaultValue="长度不能超过80个字符"
                            />
                          ),
                        },
                      ]}
                    >
                      <Input
                        onChange={handleChangeIntentionName}
                        // disabled={status === 'editor' ? true : false}
                        maxLength={80}
                        placeholder={getIntl().formatMessage({
                          id: 'intention.management.intent.name.placeholder',
                          defaultValue: '请输入意图名称',
                        })}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label={getIntl().formatMessage({
                        id: 'new.intent.intention.classification',
                        defaultValue: '意图分类',
                      })}
                      name="intentionClassification"
                      rules={[
                        {
                          required: false,
                          message: getIntl().formatMessage({
                            id:
                              'intention.management.intent.classification.placeholder',
                            defaultValue: '请选择意图分类',
                          }),
                        },
                      ]}
                    >
                      <Select
                        value={optionsSelect}
                        onChange={handleChangeIntentionClassification}
                        mode="multiple"
                        placeholder={getIntl().formatMessage({
                          id:
                            'intention.management.intent.classification.placeholder',
                          defaultValue: '请选择意图分类',
                        })}
                        popupClassName="intentionClassificationContainer"
                        allowClear={true}
                        showSearch
                        options={optionsList.map(item => ({
                          label: item.categoryName,
                          value: item.categoryId,
                          key: item.categoryId,
                        }))}
                        filterOption={(inputValue, option) =>
                          option.label
                            .toLowerCase()
                            .indexOf(inputValue.toLowerCase()) >= 0
                        }
                      />
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    <div
                      onClick={handleShowIntentionClassification}
                      className={styles.intentionClassificationManagementIcon}
                    >
                      {IntentionClassificationManagementIcon()}
                      <span>
                        <FormattedMessage
                          id="new.intent.intention.classification.management"
                          defaultMessage="意图分类管理"
                        />
                      </span>
                    </div>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      label={getIntl().formatMessage({
                        id: 'new.intent.intention.request.reason',
                        defaultValue: '意图请求原因',
                      })}
                      name="intentionReason"
                      rules={[
                        {
                          required: true,
                          message: getIntl().formatMessage({
                            id:
                              'new.intent.intention.request.reason.placeholder',
                            defaultValue:
                              '请输入客户触发这个意图的原因，例如客户请求查询订单状态',
                          }),
                        },
                        {
                          max: 2000,
                          message: (
                            <FormattedMessage
                              id="new.intent.intention.reason.maxlength"
                              defaultValue="长度不能超过2000个字符"
                            />
                          ),
                        },
                      ]}
                    >
                      <TextArea
                        onChange={handleChangeRequestReason}
                        placeholder={getIntl().formatMessage({
                          id: 'new.intent.intention.request.reason.placeholder',
                          defaultValue:
                            '请输入客户触发这个意图的原因，例如客户请求查询订单状态',
                        })}
                        autoSize={{
                          minRows: 3,
                          maxRows: 3,
                        }}
                        maxLength={2001}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={12}>
                    <div
                      style={{ marginBottom: '20px' }}
                      className={styles.basicInformationTips}
                    >
                      <FormattedMessage
                        id="new.intent.intention.basic.information.tips"
                        defaultValue="意图名称和意图请求原因是AIGC识别客户意图的重要依据。"
                      ></FormattedMessage>
                    </div>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={12}>
                    <div className={styles.secondTitle}>
                      <div className={styles.circleContainer}></div>
                      <FormattedMessage
                        id="new.intent.intention. language.information"
                        defaultValue="意图话术信息"
                      ></FormattedMessage>
                    </div>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={6}>
                    <Form.Item label={null} name="scriptType">
                      <Radio.Group onChange={onChange}>
                        <Radio
                          value={1}
                          disabled={
                            status === 'editor' && scriptTypeValue === 2
                          }
                        >
                          <FormattedMessage
                            id="new.intent.static.language.technique"
                            defaultValue="静态话术"
                          />
                        </Radio>
                        <Radio
                          value={2}
                          disabled={
                            status === 'editor' && scriptTypeValue === 1
                          }
                        >
                          <FormattedMessage
                            id="new.intent.dynamic.speech.technique"
                            defaultValue="动态话术"
                          />
                        </Radio>
                      </Radio.Group>
                    </Form.Item>
                  </Col>
                </Row>
                {/*静态话术*/}
                <div
                  style={{
                    maxHeight: '320px',
                    overflowY: 'scroll',
                    overflowX: 'hidden',
                  }}
                >
                  {scriptTypeValue === 1 &&
                    staticLanguageTechnique?.map(item => {
                      return (
                        <Row gutter={24}>
                          <Col span={10}>
                            <Form.Item
                              label={null}
                              name={item.scriptId}
                              rules={[
                                {
                                  required: true,
                                  message: getIntl().formatMessage({
                                    id:
                                      'new.intent.static.language.technique.placeholder',
                                    defaultValue: '请输入静态意图话术',
                                  }),
                                },
                                {
                                  max: 2000,
                                  message: (
                                    <FormattedMessage
                                      id="new.intent.intention.reason.maxlength"
                                      defaultValue="长度不能超过2000个字符"
                                    />
                                  ),
                                },
                              ]}
                            >
                              <div className={styles.inputAndButton}>
                                <TextArea
                                  defaultValue={item.scriptName}
                                  value={item.scriptName}
                                  onChange={e =>
                                    handleChangeLanguageTechnique(e, item, 1)
                                  }
                                  placeholder={getIntl().formatMessage({
                                    id:
                                      'new.intent.static.language.technique.placeholder',
                                    defaultValue: '请输入静态意图话术',
                                  })}
                                  autoSize={{
                                    minRows: 1,
                                    maxRows: 3,
                                  }}
                                  maxLength={2001}
                                />
                                <Select
                                  defaultValue={item.scriptLanguage}
                                  value={item.scriptLanguage}
                                  onSelect={value =>
                                    aiSelectLanguageChange(value, item, 1)
                                  }
                                  showSearch
                                  options={translationCodeList.map(item => ({
                                    label: item.label,
                                    value: item.value,
                                    key: item.value,
                                  }))}
                                  filterOption={(input, option) =>
                                    option.label
                                      .toLowerCase()
                                      .indexOf(input.toLowerCase()) >= 0
                                  }
                                />
                              </div>
                            </Form.Item>
                          </Col>
                          <Col
                            span={1}
                            style={{
                              display:
                                staticLanguageTechnique?.length > 1
                                  ? 'block'
                                  : 'none',
                            }}
                          >
                            <img
                              onClick={() =>
                                handleDeleteStaticLanguageTechnique(item, 1)
                              }
                              className={styles.deleteIcon}
                              src={DeleteIcon}
                            />
                          </Col>
                        </Row>
                      );
                    })}
                </div>
                {scriptTypeValue === 1 && (
                  <Row gutter={24}>
                    <Col span={4}>
                      <Button
                        onClick={() => handleAddStaticLanguageTechnique('', 1)}
                        className={styles.addStaticLanguageTechnique}
                        icon={<PlusOutlined />}
                      >
                        <span>
                          <FormattedMessage
                            id="new.intent.add.static.language.technique"
                            defaultValue="添加意图话术"
                          />
                        </span>
                      </Button>
                    </Col>
                    <Col span={4}>
                      <div style={{ position: 'relative' }}>
                        <Button
                          className={styles.intelligenceSummary}
                          icon={<AiBtnIcon />}
                          onClick={() => setAiSelectLanguage(!aiSelectLanguage)}
                        >
                          <span>
                            <FormattedMessage
                              id="new.intent.ai.static.language.technique"
                              defaultValue="AI生成意图话术"
                            />
                          </span>
                        </Button>
                        {aiSelectLanguage && (
                          <div className={styles.aiSelectLanguage}>
                            <Form.Item
                              label={getIntl().formatMessage({
                                id: 'new.ai.generate.intent.intention.language',
                                defaultValue: '选择意图话术语言',
                              })}
                              name="intentionLanguage"
                            >
                              <Select
                                mode="multiple"
                                placeholder={getIntl().formatMessage({
                                  id:
                                    'new.ai.generate.intent.intention.language',
                                  defaultValue: '请选择意图话术语言',
                                })}
                                popupClassName="intentionClassificationContainer"
                                allowClear={true}
                                showSearch
                                options={translationCodeList.map(item => ({
                                  label: item.label,
                                  value: item.value,
                                  key: item.value,
                                }))}
                                filterOption={(input, option) =>
                                  option.label
                                    .toLowerCase()
                                    .indexOf(input.toLowerCase()) >= 0
                                }
                              />
                            </Form.Item>
                            <Button onClick={() => setAiSelectLanguage(false)}>
                              <FormattedMessage
                                id="external.intelligent.agent.share.cancel"
                                defaultMessage="取消"
                              />
                            </Button>
                            <Button
                              type="primary"
                              style={{ marginLeft: '16px' }}
                              onClick={() => {
                                handleAiGeneratedIntentSpeech(1);
                                setAiSelectLanguage(false);
                              }}
                            >
                              <FormattedMessage
                                id="external.intelligent.agent.share.confirm"
                                defaultMessage="确认"
                              />
                            </Button>
                          </div>
                        )}
                      </div>
                    </Col>
                  </Row>
                )}
                {/*动态话术*/}
                <div
                  style={{
                    maxHeight: '320px',
                    overflowY: 'scroll',
                    overflowX: 'hidden',
                  }}
                >
                  {scriptTypeValue === 2 &&
                    dynamicLanguageTechnique?.map(item => {
                      return (
                        <Row gutter={24}>
                          <Col span={10}>
                            <Form.Item
                              label={null}
                              name={item.scriptId}
                              rules={[
                                {
                                  required: true,
                                  message: getIntl().formatMessage({
                                    id:
                                      'new.intent.dynamic.speech.technique.placeholder',
                                    defaultValue: '请输入动态话术',
                                  }),
                                },
                                {
                                  max: 2000,
                                  message: (
                                    <FormattedMessage
                                      id="new.intent.intention.reason.maxlength"
                                      defaultValue="长度不能超过2000个字符"
                                    />
                                  ),
                                },
                              ]}
                            >
                              <div className={styles.inputAndButton}>
                                <TextArea
                                  defaultValue={item.scriptName}
                                  value={item.scriptName}
                                  onChange={e =>
                                    handleChangeLanguageTechnique(e, item, 2)
                                  }
                                  placeholder={getIntl().formatMessage({
                                    id:
                                      'new.intent.dynamic.speech.technique.placeholder2',
                                    defaultValue: '请输入动态意图话术',
                                  })}
                                  autoSize={{
                                    minRows: 1,
                                    maxRows: 3,
                                  }}
                                  maxLength={2001}
                                />
                                <Select
                                  defaultValue={item.scriptLanguage}
                                  value={item.scriptLanguage}
                                  onSelect={value =>
                                    aiSelectLanguageChange(value, item, 2)
                                  }
                                  options={translationCodeList.map(item => ({
                                    label: item.label,
                                    value: item.value,
                                    key: item.value,
                                  }))}
                                  filterOption={(input, option) =>
                                    option.label
                                      .toLowerCase()
                                      .indexOf(input.toLowerCase()) >= 0
                                  }
                                />
                              </div>
                            </Form.Item>
                          </Col>
                          <Col
                            span={1}
                            style={{
                              display:
                                dynamicLanguageTechnique?.length > 1
                                  ? 'block'
                                  : 'none',
                            }}
                          >
                            <img
                              onClick={() =>
                                handleDeleteStaticLanguageTechnique(item, 2)
                              }
                              className={styles.deleteIcon}
                              src={DeleteIcon}
                            />
                          </Col>
                        </Row>
                      );
                    })}
                </div>
                {scriptTypeValue === 2 && (
                  <Row gutter={24}>
                    <Col span={4}>
                      <Button
                        onClick={() => handleAddStaticLanguageTechnique('', 2)}
                        className={styles.addStaticLanguageTechnique}
                      >
                        <PlusOutlined />
                        <span>
                          <FormattedMessage
                            id="new.intent.add.static.language.technique"
                            defaultValue="添加意图话术"
                          />
                        </span>
                      </Button>
                    </Col>
                    <Col span={4}>
                      <div style={{ position: 'relative' }}>
                        <Button
                          className={styles.intelligenceSummary}
                          icon={<AiBtnIcon />}
                          onClick={e => {
                            e.preventDefault();
                            setAiSelectLanguage(true);
                          }}
                        >
                          <span>
                            <FormattedMessage
                              id="new.intent.ai.static.language.technique"
                              defaultValue="AI生成意图话术"
                            />
                          </span>
                        </Button>
                        {aiSelectLanguage && (
                          <div className={styles.aiSelectLanguage}>
                            <Form.Item
                              label={getIntl().formatMessage({
                                id: 'new.ai.generate.intent.intention.language',
                                defaultValue: '选择意图话术语言',
                              })}
                              name="intentionLanguage"
                            >
                              <Select
                                mode="multiple"
                                placeholder={getIntl().formatMessage({
                                  id:
                                    'new.ai.generate.intent.intention.language',
                                  defaultValue: '请选择意图话术语言',
                                })}
                                popupClassName="intentionClassificationContainer"
                                allowClear={true}
                                showSearch
                                options={translationCodeList.map(item => ({
                                  label: item.label,
                                  value: item.value,
                                  key: item.value,
                                }))}
                                filterOption={(input, option) =>
                                  option.label
                                    .toLowerCase()
                                    .indexOf(input.toLowerCase()) >= 0
                                }
                              />
                            </Form.Item>
                            <Button
                              onClick={e => {
                                setAiSelectLanguage(false);
                              }}
                            >
                              取消
                            </Button>
                            <Button
                              type="primary"
                              style={{ marginLeft: '16px' }}
                              onClick={e => {
                                handleAiGeneratedIntentSpeech(2);
                                setAiSelectLanguage(false);
                              }}
                            >
                              确定
                            </Button>
                          </div>
                        )}
                      </div>
                    </Col>
                  </Row>
                )}

                {scriptTypeValue === 2 && (
                  <DynamicLlmExtractAttribute ref={dynamicLlmRef} />
                )}
                <Row gutter={24}>
                  <Col span={24}>
                    <Form.Item
                      style={{ textAlign: 'center', marginTop: '20px' }}
                    >
                      <Popconfirm
                        title={getIntl().formatMessage({
                          id: 'new.intent.add.cancel.confirm',
                          defaultValue: '取消将清空表单，确定取消么？',
                        })}
                        onConfirm={backHistory}
                      >
                        <Button style={{ marginRight: '15px' }}>
                          <FormattedMessage
                            id="awsAccountSetting.cancel.btn"
                            defaultMessage="取消"
                          />
                        </Button>
                      </Popconfirm>
                      <Button
                        loading={loadingBtn}
                        type="primary"
                        htmlType="submit"
                      >
                        <FormattedMessage
                          id="work.order.management.btn.save"
                          defaultMessage="保存"
                        />
                      </Button>
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
            </div>
          </div>
        </div>
      </Spin>

      {/*创建意图成功弹窗*/}
      <Modal
        title={null}
        open={intentionSuccess}
        onCancel={returnIntentionList}
        onOk={handleCreateIntelligentAgent}
        className="intentionSuccessModal"
        confirmLoading={loadingAddBtn}
        mask={false}
        maskClosable={false}
        cancelText={getIntl().formatMessage({
          id: 'work.record.return',
          defaultValue: '返回',
        })}
        okText={getIntl().formatMessage({
          id: 'external.intelligent.agent.create.add',
          defaultValue: '创建智能体',
        })}
      >
        <img src={IntentionSuccessIcon} />
        <p>
          <FormattedMessage
            id="new.intent.add.success.tips"
            defaultMessage="恭喜您已经成功创建了一个意图，接下来您可以为当前意图创建智能体"
          />
        </p>
      </Modal>

      {/*意图分类管理弹窗*/}
      <Modal
        title={getIntl().formatMessage({
          id: 'new.intent.intention.classification.management',
          defaultValue: '意图分类管理',
        })}
        open={managementModal}
        onCancel={handleCancelIntentionClassification}
        footer={null}
        mask={false}
        maskClosable={false}
        className="intentionClassificationModal"
      >
        <Spin spinning={loadingModal}>
          <div className="operationContainer">
            <Input
              placeholder={getIntl().formatMessage({
                id:
                  'new.intent.intention.classification.management.placeholder',
                defaultValue: '请输入意图分类名称，回车搜索或点击按钮添加',
              })}
              value={inputValue}
              prefix={<Search />}
              onChange={e => setInputValue(e.target.value)}
              onPressEnter={e => handleSearch(e.target.value)}
            />
            <img
              onClick={() => handleSelect(inputValue)}
              src={IntentionClassificationAddIcon}
            />
          </div>
          <div className="intentionClassificationList">
            {optionsList.length === 0 && (
              <div className="noDataContent1">
                <img src={NoDataImg} />
                <p>
                  <FormattedMessage
                    id="work.order.reply.no.data"
                    defaultMessage="暂无数据"
                  />
                </p>
              </div>
            )}
            {optionsList
              .filter(option =>
                option.categoryName
                  .toLowerCase()
                  .includes(searchText.toLowerCase()),
              )
              .map(option => {
                return (
                  <div
                    className="intentionClassificationItem"
                    key={option.categoryId}
                  >
                    <p>{option.categoryName}</p>
                    <img
                      src={IntentionClassificationDeleteIcon}
                      onClick={e => {
                        e.stopPropagation(); // 阻止点击事件传播到父容器
                        handleDelete(option);
                      }}
                    />
                  </div>
                );
              })}
          </div>
        </Spin>
      </Modal>
    </div>
  );
};

const DynamicLlmExtractAttribute = forwardRef((props, ref) => {
  const [form] = Form.useForm();
  const [editingKey, setEditingKey] = useState('');
  const [dynamicLlmData, setDynamicLlmData] = useState([]);

  const columns = [
    {
      title: getIntl().formatMessage({
        id: 'new.intent.intention.language.attribute.name',
        defaultValue: '属性名称',
      }),
      dataIndex: 'attributeName',
      width: '25%',
      editable: true,
      inputType: 'text',
    },
    {
      title: getIntl().formatMessage({
        id: 'new.intent.intention.language.attribute.code',
        defaultValue: '属性编码',
      }),
      dataIndex: 'attributeValues',
      width: '25%',
      editable: true,
      inputType: 'text',
      render: (_, record) => (
        <div className={styles.dynamicLlmAttributeCode}>
          {'{'}
          {record.attributeValues}
          {'}'}
        </div>
      ),
    },
    {
      title: getIntl().formatMessage({
        id: 'new.intent.intention.language.attribute.format.requirement',
        defaultValue: '属性格式要求',
      }),
      dataIndex: 'attributeFormat',
      width: '25%',
      editable: true,
      inputType: 'text',
    },
    {
      title: getIntl().formatMessage({
        id: 'new.intent.intention.language.attribute.is.required',
        defaultValue: '是否必填',
      }),
      dataIndex: 'isRequired',
      width: '15%',
      editable: true,
      inputType: 'switch',
      render: (_, record) => (record.isRequired === 1 ? '必填' : '非必填'),
    },
    {
      title: getIntl().formatMessage({
        id: 'new.intent.intention.language.attribute.operation',
        defaultValue: '操作',
      }),
      dataIndex: 'operation',
      render: (_, record) => {
        const editable = isEditing(record);
        return editable ? (
          <span className={styles.dynamicLlmOperation}>
            <Typography.Link onClick={() => save(record.key)}>
              <img src={SaveReplyIcon} />
            </Typography.Link>
            <Popconfirm
              title={getIntl().formatMessage({
                id: 'new.intent.add.cancel.confirm',
                defaultValue: '确定取消?',
              })}
              onConfirm={cancel}
            >
              <img src={CancelSaveReplyIcon} />
            </Popconfirm>
          </span>
        ) : (
          <span className={styles.dynamicLlmOperation}>
            <Typography.Link
              disabled={editingKey !== ''}
              onClick={() => edit(record)}
            >
              <img src={EditorReplyIcon} />
            </Typography.Link>
            <Popconfirm
              title={getIntl().formatMessage({
                id: 'document.knowledge.base.table.operation.delete.Popconfirm',
                defaultValue: '确定删除该条数据吗？',
              })}
              onConfirm={() => deleteScript(record)}
            >
              <img src={DeleteReplyIcon} />
            </Popconfirm>
          </span>
        );
      },
    },
  ];

  const mergedColumns = columns.map(col => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: record => ({
        record,
        inputType: col.inputType,
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
      }),
    };
  });

  // 暴露动态话术数据和方法
  useImperativeHandle(ref, () => ({
    getDynamicLlmData: () => dynamicLlmData,
    setDynamicLlmData: data => {
      setDynamicLlmData(data);
    },
    resetData: () => {
      setDynamicLlmData([]);
      setEditingKey('');
      form.resetFields();
    },
    isEditing: () => editingKey !== '', // 添加检查是否有正在编辑的行
  }));
  const deleteScript = record => {
    const newData = dynamicLlmData.filter(item => item.key !== record.key);
    setDynamicLlmData(newData);
  };
  const isEditing = record => record.key === editingKey;
  const edit = record => {
    form.setFieldsValue({
      attributeName: record.attributeName,
      attributeValues: record.attributeValues,
      attributeFormat: record.attributeFormat,
      isRequired: record.isRequired,
    });
    setEditingKey(record.key);
  };
  const save = async key => {
    const row = await form.validateFields();
    const isDuplicate = dynamicLlmData.some(
      item =>
        item.key !== editingKey && item.attributeValues === row.attributeValues,
    );
    if (isDuplicate) {
      notification.warning({
        message: (
          <FormattedMessage
            id="new.intent.add.variables.only.tips"
            defaultMessage="属性编码不能重复！"
          ></FormattedMessage>
        ),
      });
      return;
    }
    // 更新数据
    const newData = [...dynamicLlmData];
    const index = newData.findIndex(item => key === item.key);
    if (index > -1) {
      // 构建新的记录
      const item = newData[index];
      const updatedItem = {
        ...item,
        ...row,
        isRequired: Number(row.isRequired),
        key: item.key,
      };
      // 更新数据源
      newData.splice(index, 1, updatedItem);
      setDynamicLlmData(newData);
      // 重置编辑状态
      setEditingKey('');
      form.resetFields();
    }
    setDynamicLlmData(newData);
  };
  const cancel = () => {
    const record = dynamicLlmData.find(item => item.key === editingKey);
    // 如果是新增的空记录,则删除
    if (record && !record.attributeName && !record.attributeValues) {
      setDynamicLlmData(prev => prev.filter(item => item.key !== editingKey));
    }
    setEditingKey('');
    // 重置表单
    form.resetFields();
  };

  const dynamicLlmEditableCell = ({
    editing,
    dataIndex,
    title,
    inputType,
    record,
    index,
    children,
    ...restProps
  }) => {
    let inputNode;
    let rules = [
      {
        required: true,
        message: `${title}`,
      },
    ];
    if (dataIndex === 'attributeValues') {
      rules = [
        {
          required: true,
          message: getIntl().formatMessage({
            id:
              'new.intent.intention.language.attribute.code.only.support.english',
            defaultValue: '仅支持英文字母',
          }),
        },
        {
          pattern: /^[A-Za-z]+$/,
          message: getIntl().formatMessage({
            id:
              'new.intent.intention.language.attribute.code.only.support.english',
            defaultValue: '属性编码只能包含英文字母',
          }),
        },
      ];
    }
    if (dataIndex === 'isRequired') {
      inputNode = (
        <Select
          options={[
            {
              label: getIntl().formatMessage({
                id: 'new.intent.intention.language.attribute.is.required.yes',
                defaultValue: '必填',
              }),
              value: 1,
            },
            {
              label: getIntl().formatMessage({
                id: 'new.intent.intention.language.attribute.is.required.no',
                defaultValue: '非必填',
              }),
              value: 0,
            },
          ]}
        />
      );
    } else {
      // 为属性编码添加placeholder提示
      const placeholder =
        dataIndex === 'attributeValues'
          ? getIntl().formatMessage({
              id:
                'new.intent.intention.language.attribute.code.only.support.english.message',
              defaultValue: '仅支持英文字母',
            })
          : `${title}`;
      inputNode = <Input placeholder={placeholder} maxLength={80} />;
    }
    return (
      <td {...restProps}>
        {editing ? (
          <Form.Item name={dataIndex} style={{ margin: 0 }} rules={rules}>
            {inputNode}
          </Form.Item>
        ) : (
          children
        )}
      </td>
    );
  };

  const handleAddLlmVariables = () => {
    const newKey = String(Date.now());
    const newRow = {
      key: newKey,
      attributeName: '',
      attributeValues: '',
      attributeFormat: '',
      isRequired: 1,
    };
    form.resetFields();
    const newData = [newRow, ...dynamicLlmData];
    setDynamicLlmData(newData);
    setEditingKey(newKey);
  };
  return (
    <>
      <Row gutter={24}>
        <Col span={10}>
          <div className={styles.secondTitle}>
            <div className={styles.circleContainer}></div>
            <FormattedMessage
              id="new.intent.intention.llm.extract.attribute"
              defaultValue="LLM 提取意图属性"
            ></FormattedMessage>
          </div>
        </Col>
      </Row>
      <Row gutter={24}>
        <Col span={10}>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddLlmVariables}
          >
            <FormattedMessage id="new.intent.llm.extract.attribute.add.variables" />
          </Button>
        </Col>
      </Row>
      <div className={styles.dynamicLlmTable}>
        <Form form={form} component={false}>
          <Table
            components={{
              body: {
                cell: dynamicLlmEditableCell,
              },
            }}
            columns={mergedColumns}
            dataSource={dynamicLlmData}
            pagination={false}
            rowClassName="editable-row"
          />
        </Form>
      </div>
    </>
  );
});

export default NewIntent;
