.partner {
  background-image: url('../../assets/homePageNew/hzhbbg.jpg');
  background-size: 100% 100%;
  // background-repeat: no-repeat;

  .partnerBanner {
    display: flex;
    gap: 6.25rem;
    margin: 0 auto;
    padding: 15.25rem 10.625rem;

    .partnerLeft {
      flex: 1;

      img {
        width: 44.375rem;
      }
    }

    .partnerRight {
      flex: 1;
      display: flex;
      flex-direction: column;
      padding: 3.125rem 0;

      .partnerBannerTitle {
        font-size: 4.375rem;
        color: #333333;
        font-weight: 700;
        word-break: break-all;
        font-family: 'MicrosoftYaHei';
      }

      .partnerBannerSubtitle {
        font-size: 3.625rem;
        color: #333333;
        // background: linear-gradient(to right, #ffc7a2, #7838ec, #264db1);
        background: linear-gradient(89deg, #00ebf9, #0a82fa, #7800a7, #8000b3);
        background-clip: text;
        -webkit-background-clip: text;
        color: transparent;
        font-weight: 700;
        word-break: break-all;
        font-family: 'MicrosoftYaHei';
      }

      .desc {
        font-size: 1.5rem;
        line-height: 2.5rem;
        color: #333333;
        margin: 2.5rem 0;
        font-family: 'MicrosoftYaHei Regular';
      }

      .partnerBannerBtn {
        a {
          color: #fff;
        }

        width: max-content;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.75rem 2.25rem;
        border: unset;
        border-radius: 2.5rem;
        margin-top: 1.5rem;
        z-index: 1;
        background: #3463fc;
        position: relative;
        font-weight: 700;
        font-size: 1.75rem;
        font-family: 'MicrosoftYaHei Regular';
        box-shadow: 0.25rem 0.625rem 0.625rem -0.1875rem rgba(0, 0, 0, 0.15);
        transition: all 250ms;
        overflow: hidden;

        &:before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          height: 100%;
          width: 0;
          border-radius: 0.9375rem;
          background-color: #3463fc;
          z-index: -1;
          box-shadow: 0.25rem 0.625rem 0.625rem -0.1875rem rgba(0, 0, 0, 0.15);
          transition: all 250ms;
        }

        &:hover {
          a {
            color: #3463fc;
          }

          border: 1px solid #3463fc;

          &:before {
            width: 100%;
            background-color: #fff;
          }
        }
      }
    }
  }

  .partnerContent {
    padding: 2.5rem 10rem 10rem 10rem;

    .partnerContentTitle {
      font-size: 4rem;
      color: #333333;
      font-weight: 700;
      text-align: center;
      font-family: 'MicrosoftYaHei';
    }

    .partnerContentDesc {
      max-width: 1200px;
      text-align: center;
      font-size: 1.5rem;
      font-family: 'MicrosoftYaHei Regular';
      color: #333333;
      margin: 2.5rem auto 5.375rem;
    }

    .partnerContentList {
      display: flex;
      gap: 3.125rem;
      justify-content: space-between;

      .partnerContentItem {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;

        img {
          width: 100%;
        }

        .partnerContentItemTitle {
          font-size: 1.875rem;
          font-family: 'MicrosoftYaHei Regular';
          color: #333333;
          font-weight: 700;
          margin: 1.25rem 0 0.625rem 0;
        }

        .partnerContentItemDesc {
          font-size: 1.125rem;
          font-family: 'MicrosoftYaHei Regular';
          line-height: 2rem;
          color: #333333;
        }
      }
    }
  }

  .partnerCard {
    padding: 0 8.75rem;

    .partnerCardContent {
      // width: 1640px;
      height: 23.75rem;
      margin: 0 auto;
      display: flex;
      gap: 10rem;
      justify-content: space-between;
      align-items: center;
      background-image: url('../../assets/start-banner.png');
      background-size: cover;
      background-position: center;
      padding: 0 7rem;
      border-radius: 1.875rem;
      overflow: hidden;

      .partnerCardLeft {
        color: #fff;

        .partnerCardTitle {
          font-size: 2.5rem;
          font-weight: 700;
          margin-bottom: 1.25rem;
          font-family: 'MicrosoftYaHei';
        }

        .partnerCardDesc {
          font-size: 1.5rem;
          line-height: 2.25rem;
          display: -webkit-box;
          -webkit-line-clamp: 5;
          line-clamp: 5;
          -webkit-box-orient: vertical;
          overflow: hidden;
          font-family: 'MicrosoftYaHei Regular';
        }
      }

      .partnerCardRight {
        display: flex;
        align-items: center;
        height: 100%;

        .partnerCardBtn {
          border-radius: 10px;
          border: 1px solid #fff;
          background-color: #fff;
          color: #3463fc;
          font-size: 1.625rem;
          font-style: normal;
          font-family: 'MicrosoftYaHei Regular';
          font-weight: 700;
          height: 3.125rem;
          min-width: 12.5rem;
          transition: all 0.1s;

          &:hover {
            background-color: #2a4fca;
            color: #fff;
          }
        }
      }
    }
  }
}
