import React from 'react';
import { Link } from 'umi';
import styles from './index.less';
import headerLogo from '@/assets/logo.png';
import { FormattedMessage } from 'umi';
const MobileFooter = () => {
  return (
    <div className={styles.mobileFooter}>
      <div className={styles.footerContent}>
        <div className={styles.logoSection}>
          <div className={styles.productLinks}>
            <div className={styles.columnTitle}>
              <FormattedMessage id="footer.product" />
            </div>
            <div className={styles.linkList}>
              <Link to="/homePage" className={styles.footerLink}>
                <FormattedMessage id="footer.product.1" />
              </Link>
              <Link to="/homePage" className={styles.footerLink}>
                <FormattedMessage id="footer.product.2" />
              </Link>
              <Link to="/homePage" className={styles.footerLink}>
                <FormattedMessage id="footer.product.3" />
              </Link>
              <Link to="/homePage" className={styles.footerLink}>
                <FormattedMessage id="footer.product.4" />
              </Link>
              <Link to="/homePage" className={styles.footerLink}>
                <FormattedMessage id="footer.product.5" />
              </Link>
              <Link to="/homePage" className={styles.footerLink}>
                <FormattedMessage id="footer.product.6" />
              </Link>
              <Link to="/homePage" className={styles.footerLink}>
                <FormattedMessage id="footer.product.7" />
              </Link>
              <Link to="/homePage" className={styles.footerLink}>
                <FormattedMessage id="footer.product.8" />
              </Link>
              <Link to="/homePage" className={styles.footerLink}>
                <FormattedMessage id="footer.product.9" />
              </Link>
            </div>
          </div>
          <img src={headerLogo} alt="ConnectNow Logo" className={styles.logo} />
        </div>

        <div className={styles.linksSection}>
          <div className={styles.solutionLinks}>
            <div className={styles.columnTitle}>
              <FormattedMessage id="footer.solution" />
            </div>
            <div className={styles.linkList}>
              <Link to="/homePage" className={styles.footerLink}>
                <FormattedMessage id="footer.solution.1" />
              </Link>
              <Link to="/homePage" className={styles.footerLink}>
                <FormattedMessage id="footer.solution.2" />
              </Link>
              <Link to="/homePage" className={styles.footerLink}>
                <FormattedMessage id="footer.solution.3" />
              </Link>
              <Link to="/homePage" className={styles.footerLink}>
                <FormattedMessage id="footer.solution.6" />
              </Link>
            </div>
          </div>

          <div className={styles.companyLinks}>
            <div className={styles.columnTitle}>
              <FormattedMessage id="footer.company" />
            </div>
            <div className={styles.linkList}>
              <Link to="/homePage" className={styles.footerLink}>
                <FormattedMessage id="footer.company.1" />
              </Link>
              <Link to="/userTerms" className={styles.footerLink}>
                <FormattedMessage id="footer.company.2" />
              </Link>
              <Link to="/privacyPolicy" className={styles.footerLink}>
                <FormattedMessage id="footer.company.3" />
              </Link>
              <Link to="/cookiePolicy" className={styles.footerLink}>
                <FormattedMessage id="footer.company.4" />
              </Link>
              <Link to="/complianceGuide" className={styles.footerLink}>
                <FormattedMessage id="footer.company.5" />
              </Link>
              <Link to="/euAiActCompliance" className={styles.footerLink}>
                <FormattedMessage id="footer.company.6" />
              </Link>
              <Link to="/gdprCompliance" className={styles.footerLink}>
                <FormattedMessage id="footer.company.7" />
              </Link>
            </div>
          </div>
        </div>
      </div>

      <div className={styles.copyright}>
        <div className={styles.copyrightText}>北京云势数据科技有限责任公司</div>
      </div>
    </div>
  );
};

export default MobileFooter;
