.mobileFooter {
  width: 100%;
  background-color: #fbfcff;
  padding-top: 2.143rem;

  .footerContent {
    display: flex;
    flex-direction: row;
    gap: 4.114rem;
    padding: 0 2.143rem;

    .logoSection {
      display: flex;
      flex-direction: column;
      gap: 1.714rem;

      .logo {
        width: 7.142rem;
        height: auto;
      }

      .productLinks {
        display: flex;
        flex-direction: column;
        gap: 0.714rem;
      }
    }

    .linksSection {
      display: flex;
      flex-direction: column;
      gap: 1.071rem;

      .solutionLinks,
      .companyLinks {
        display: flex;
        flex-direction: column;
        gap: 0.714rem;
      }
    }

    .columnTitle {
      font-size: 1.143rem;
      font-weight: 700;
      color: #666666;
      margin-bottom: 0.357rem;
    }

    .linkList {
      display: flex;
      flex-direction: column;
      gap: 0.714rem;

      .footerLink {
        font-size: 1rem;
        color: #666666;
        text-decoration: none;
        line-height: 1.4;

        &:hover {
          color: #3463fc;
        }
      }
    }
  }

  .copyright {
    height: 2.857rem;
    background-color: #3463fc;
    margin-top: 2.143rem;
    display: flex;
    justify-content: center;
    align-items: center;

    .copyrightText {
      font-size: 0.714rem;
      color: #ffffff;
      text-align: center;
    }
  }
}
