import React, { useEffect, useState, useRef } from 'react';
import styles from './index.less';
import MobileHeader from './MobileHeader';
import MobileFooter from './MobileFooter';
import cardBg from '@/assets/homepage_mobileCardBG.png';
import { FormattedMessage, setLocale, getLocale, getIntl, history } from 'umi';
import {
  FinanceIcon,
  JinrongIcon,
  ZhizaoIcon,
  YouxiIcon,
  XinnengyuanIcon,
} from './icon';
import pexels1 from '@/assets/pexels-yankrukov-1.png';
import pexels2 from '@/assets/pexels-yankrukov-2.png';
import pexels3 from '@/assets/pexels-yankrukov-3.png';
import pexels4 from '@/assets/pexels-yankrukov-4.png';
import pexels5 from '@/assets/pexels-yankrukov-5.png';
import mobileChat from '@/assets/mobileChat.gif';

// 模拟图片数据，实际项目中可能从后端获取
const solutionCards = [
  {
    id: 1,
    title: <FormattedMessage id="aiAgentLibrary.tab.list.5" />,
    icon: (
      <div className={styles.cardIcon}>
        <FinanceIcon />
      </div>
    ),
    description: [
      getIntl().formatMessage({
        id: 'aiAgentLibrary.agent.list.retail.1.title',
      }),
      getIntl().formatMessage({
        id: 'aiAgentLibrary.agent.list.retail.2.title',
      }),
      getIntl().formatMessage({
        id: 'aiAgentLibrary.agent.list.retail.3.title',
      }),
    ],
    image: <img src={pexels1} className={styles.cardImage} />,
  },
  {
    id: 2,
    title: <FormattedMessage id="aiAgentLibrary.tab.list.2" />,
    icon: (
      <div className={styles.cardIcon}>
        <JinrongIcon />
      </div>
    ),
    description: [
      getIntl().formatMessage({
        id: 'aiAgentLibrary.agent.list.1.title',
      }),
      getIntl().formatMessage({
        id: 'aiAgentLibrary.agent.list.3.title',
      }),
      getIntl().formatMessage({
        id: 'aiAgentLibrary.agent.list.2.title',
      }),
      getIntl().formatMessage({
        id: 'aiAgentLibrary.agent.list.5.title',
      }),
      getIntl().formatMessage({
        id: 'aiAgentLibrary.agent.list.4.title',
      }),
    ],
    image: <img src={pexels2} className={styles.cardImage} />,
  },
  {
    id: 3,
    title: <FormattedMessage id="aiAgentLibrary.tab.list.6" />,
    icon: (
      <div className={styles.cardIcon}>
        <ZhizaoIcon />
      </div>
    ),
    description: [
      getIntl().formatMessage({
        id: 'aiAgentLibrary.agent.list.manufacturing.1.title',
      }),
      getIntl().formatMessage({
        id: 'aiAgentLibrary.agent.list.manufacturing.2.title',
      }),
    ],
    image: <img src={pexels3} className={styles.cardImage} />,
  },
  {
    id: 4,
    title: <FormattedMessage id="aiAgentLibrary.tab.list.4" />,
    icon: (
      <div className={styles.cardIcon}>
        <YouxiIcon />
      </div>
    ),
    description: [
      getIntl().formatMessage({
        id: 'aiAgentLibrary.agent.list.game.1.title',
      }),
      getIntl().formatMessage({
        id: 'aiAgentLibrary.agent.list.game.3.title',
      }),
      getIntl().formatMessage({
        id: 'aiAgentLibrary.agent.list.game.2.title',
      }),
    ],
    image: <img src={pexels4} className={styles.cardImage} />,
  },
  {
    id: 5,
    title: <FormattedMessage id="aiAgentLibrary.tab.list.3" />,
    icon: (
      <div className={styles.cardIcon}>
        <XinnengyuanIcon />
      </div>
    ),
    description: [
      getIntl().formatMessage({
        id: 'aiAgentLibrary.agent.list.newEnergy.1.title',
      }),
      getIntl().formatMessage({
        id: 'aiAgentLibrary.agent.list.newEnergy.3.title',
      }),
      getIntl().formatMessage({
        id: 'aiAgentLibrary.agent.list.newEnergy.2.title',
      }),
    ],
    image: <img src={pexels5} className={styles.cardImage} />,
  },
];

// 直接使用原始卡片数组
const carouselCards = solutionCards;

const HomepageMobile = () => {
  // 初始化检查滚动位置
  useEffect(() => {
    // 获取滚动容器
    const container = document.querySelector(`.${styles.homeContainer}`);
    if (container) {
      // 检查初始滚动位置
      setIsScrolled(container.scrollTop > 0);

      // 监听滚动事件
      const handleScroll = () => {
        setIsScrolled(container.scrollTop > 0);
      };

      // 添加滚动事件监听
      container.addEventListener('scroll', handleScroll, { passive: true });

      // 清理函数
      return () => {
        container.removeEventListener('scroll', handleScroll);
      };
    }
  }, []);

  // 设置基准字体大小
  useEffect(() => {
    document.documentElement.style.fontSize = '14px';
    // 初始化区域选择，先获取浏览器语言，如果没有则获取系统语言
    const getBrowserLanguage = () => {
      // 尝试获取浏览器语言
      const browserLang = navigator.language || navigator.userLanguage;
      // 如果浏览器语言不可用，尝试获取系统语言
      if (!browserLang) {
        // 尝试通过其他方式获取系统语言
        // 注意：大多数情况下，navigator.language 应该已经包含了系统语言信息
        const systemLang = navigator.languages?.[0] || 'zh-CN'; // 默认为中文
        return systemLang;
      }
      return browserLang.includes('en') ? 'en-US' : browserLang;
    };
    // 读取浏览器的语言，设置本地化
    const browserLang = getBrowserLanguage();
    // const local = browserLang.toLowerCase().includes('zh') ? 'zh-CN' : browserLang;
    setLocale(browserLang, false);

    // 清理函数
    return () => {
      document.documentElement.style.fontSize = '';
    };
  }, []);
  // 轮播图状态管理
  const [currentIndex, setCurrentIndex] = useState(0); // 从第一张开始
  const [isTouching, setIsTouching] = useState(false);
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);
  const [transitioning, setTransitioning] = useState(false);
  const [visible, setVisible] = useState(false);
  const carouselRef = useRef(null);
  const autoPlayTimerRef = useRef(null);
  // 监听页面滚动，记录scrollTop是否大于0
  const [isScrolled, setIsScrolled] = useState(false);

  // 处理容器滚动事件
  const boxTouchMove = e => {
    // 获取滚动容器
    const container = e.currentTarget;
    if (container) {
      // 获取滚动位置
      const scrollTop = container.scrollTop;
      console.log('容器滚动位置:', scrollTop);

      // 当滚动位置大于0时，设置isScrolled为true，否则为false
      setIsScrolled(scrollTop > 0);
    }
  };

  // 处理触摸开始事件
  const handleTouchStart = e => {
    console.log(e, 'handleTouchStart');
    setIsTouching(true);
    setTouchStart(e.touches[0].clientX);
    setTouchEnd(e.touches[0].clientX);
    // 暂停自动轮播
    clearInterval(autoPlayTimerRef.current);
  };

  // 处理触摸移动事件
  const handleTouchMove = e => {
    if (isTouching) {
      setTouchEnd(e.touches[0].clientX);
      // 计算偏移量
      const offset = touchEnd - touchStart;

      // 边界情况处理：第一张向左划或最后一张向右划时减少位移效果
      const isFirstCard = currentIndex === 0 && offset > 0;
      const isLastCard =
        currentIndex === carouselCards.length - 1 && offset < 0;

      // 如果是边界情况，添加阻尼效果，移动距离减少到正常的30%
      const dampingFactor = isFirstCard || isLastCard ? 0.3 : 1;

      if (carouselRef.current) {
        // 根据手指移动距离设置轮播容器的位移
        // 使用固定的卡片宽度
        const cardWidth = 19.56; // rem

        const basePosition =
          currentIndex === 0 ? 0 : -((currentIndex - 1) * cardWidth + 17);

        // 在基础位置上添加手指移动的偏移量（可能带有阻尼效果）
        const translateX = `${basePosition + (offset / 14) * dampingFactor}rem`; // 转换px到rem
        console.log(translateX, 'translateX');
        carouselRef.current.style.transform = `translateX(${translateX})`;
        carouselRef.current.style.transition = 'none';
      }
    }
  };

  // 处理触摸结束事件
  const handleTouchEnd = e => {
    setIsTouching(false);
    setTransitioning(true);

    if (carouselRef.current) {
      const slideWidth = 19.56 * 14; // 转为px单位
      const minSwipeDistance = slideWidth * 0.2; // 至少滑动20%才触发切换
      const offset = touchEnd - touchStart;

      let newIndex = currentIndex;

      if (offset < -minSwipeDistance) {
        // 向左滑，下一张，但不超过最后一张
        newIndex = Math.min(currentIndex + 1, carouselCards.length - 1);
      } else if (offset > minSwipeDistance) {
        // 向右滑，上一张，但不低于第一张
        newIndex = Math.max(currentIndex - 1, 0);
      }

      setCurrentIndex(newIndex);
      // 恢复到正确位置
      resetPosition();
    }
  };
  const loadInit = url => {
    const existing = document.getElementById('goclouds-script');
    const livechatDiv = document.getElementById('ConnectNowChat');
    if (existing) {
      existing.remove();
      if (livechatDiv) {
        livechatDiv.remove();
      }
    }
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = process.env.CHAT_PATH;
      script.async = true;
      script.id = 'goclouds-script';
      script.onload = () => {
        if (!process.env.CHAT_PATH) {
          const pollInterval = setInterval(() => {
            const element = document.querySelector('.sc-Qotzb');
            // console.log('element', element);
            if (element) {
              element.click();
              clearInterval(pollInterval); // 找到元素后清除轮询
            }
          }, 500); // 每100ms检查一次
        }
      };
      script.onerror = () => {
        console.error('Failed to load goclouds script');
        reject();
      };
      document.head.appendChild(script);
    });
  };
  // 重置位置到当前索引
  const resetPosition = () => {
    if (carouselRef.current) {
      const cardWidth = 19.56; // rem
      carouselRef.current.style.transform =
        currentIndex === 0
          ? `translateX(0)`
          : `translateX(-${(currentIndex - 1) * cardWidth + 17}rem)`;
      carouselRef.current.style.transition = 'transform 0.25s ease-out';
    }
  };

  // 监听currentIndex变化，重置位置
  useEffect(() => {
    if (transitioning) {
      const handle = setTimeout(() => {
        setTransitioning(false);
        resetPosition();
      }, 280);

      return () => clearTimeout(handle);
    }
  }, [currentIndex, transitioning]);

  // 自动轮播
  useEffect(() => {
    clearInterval(autoPlayTimerRef.current);

    if (!isTouching) {
      autoPlayTimerRef.current = setInterval(() => {
        setTransitioning(true);

        // 如果到达最后一张，跳回第一张
        if (currentIndex >= carouselCards.length - 1) {
          setCurrentIndex(0);
        } else {
          setCurrentIndex(prev => prev + 1);
        }
      }, 8000);
    }

    return () => clearInterval(autoPlayTimerRef.current);
  }, [currentIndex, isTouching, carouselCards.length]);

  // 轮播容器样式
  const carouselStyle = {
    transition: transitioning ? 'transform 0.25s ease-out' : 'none',
    display: 'flex',
    width: `${carouselCards.length * 19.56}rem`,
    transform:
      currentIndex === 0
        ? `translateX(0)`
        : `translateX(-${(currentIndex - 1) * 19.56 + 17}rem)`,
    willChange: 'transform',
  };
  useEffect(() => {
    (function(w, d, x, id) {
      let s = d.createElement('script');
      s.src = process.CHAT_PATH;
      s.async = 1;
      s.id = 'goclouds-script';
      d.getElementsByTagName('head')[0].appendChild(s);
      w[x] =
        w[x] ||
        function() {
          (w[x].ac = w[x].ac || []).push(arguments);
        };
    })(window, document, 'goclouds', '');
    const industry = localStorage.getItem('libraryIndustry') || 'Finance';
    const aiagentName =
      localStorage.getItem('libraryAiagentName') || 'FinanceKB';
    goclouds({
      i: null,
      v: process.env.CHAT_PARAMS_V,
      channelType: '8',
      singleChat: true,
      ticketAttr: {},
    });
    window['goclouds'].ac[0][0] = {
      i: null,
      v: process.env.CHAT_PARAMS_V,
      channelType: '8',
      singleChat: true,
    };
    loadInit(process.env.CHAT_PATH);
    // 检查本地存储中是否已经同意
    const hasConsented = localStorage.getItem('cookieConsent');
    if (!hasConsented) {
      setVisible(true);
    }
  }, []);

  return (
    <div
      className={styles.homeContainer}
      onTouchMove={boxTouchMove}
      onScroll={boxTouchMove}
    >
      <MobileHeader isScrolled={isScrolled} />

      <main>
        {/* 自定义轮播区域 */}
        <section className={styles.solutionsSection}>
          <div
            className={styles.solutionCarousel}
            ref={carouselRef}
            onTouchStart={handleTouchStart}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
            style={carouselStyle}
          >
            {carouselCards.map((card, idx) => (
              <div key={idx} className={styles.solutionCard}>
                <img className={styles.cardBg} src={cardBg}></img>
                {card.icon}
                {card.image}
                <div className={styles.cardContent}>
                  <div className={styles.cardHeader}>
                    <h3 className={styles.cardTitle}>{card.title}</h3>
                    <div className={styles.cardTag}>
                      <div className={styles.tagIcon}>
                        {/* Agent 标签中的图标在实际项目中应该用SVG或者图片来表示 */}
                        <svg
                          width="15"
                          height="14"
                          viewBox="0 0 15 14"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M4.34331 3.30792C4.58892 3.30792 4.80518 3.42838 4.93999 3.61261C5.03554 3.66601 5.11242 3.7477 5.15874 3.84698L5.17534 3.88995L8.31206 12.8294C8.41331 13.1174 8.23184 13.4389 7.90777 13.5482C7.60015 13.6518 7.2739 13.5278 7.15484 13.2679L7.13824 13.2249L6.32574 10.9095H2.26324L1.45171 13.2249C1.35022 13.5129 1.00578 13.6579 0.68218 13.5482C0.373839 13.4443 0.195387 13.149 0.265188 12.8724L0.277883 12.8284L3.41363 3.89093C3.44478 3.80751 3.49545 3.73216 3.56206 3.67316C3.62864 3.61419 3.70904 3.57283 3.79546 3.55206C3.86451 3.47521 3.94919 3.41341 4.04351 3.3714C4.13767 3.3295 4.24028 3.30783 4.34331 3.30792ZM10.1119 7.11554C10.4374 7.11564 10.7036 7.27829 10.73 7.48468L10.732 7.51788V13.1458C10.732 13.3682 10.4539 13.548 10.1119 13.5482C9.78607 13.5482 9.51844 13.3862 9.49273 13.179L9.49077 13.1458V7.51788C9.49117 7.29561 9.76935 7.11554 10.1119 7.11554ZM2.68999 9.69073H5.898L4.29351 5.1214L2.68999 9.69073ZM11.0328 0.432922C11.1672 0.468964 11.2825 0.557449 11.3521 0.678039C11.4216 0.798582 11.4399 0.942062 11.4039 1.07648C11.3678 1.2108 11.2801 1.32522 11.1597 1.39484C11.0631 1.4506 10.9519 1.47372 10.8423 1.46222C10.6269 1.62599 10.3089 1.87753 10.0962 2.09015C9.94082 2.24562 9.76424 2.45807 9.61675 2.64484C9.62505 2.6577 9.63347 2.67058 9.64116 2.6839C9.76177 2.89288 9.79746 3.14038 9.74077 3.37433C9.88622 3.47825 10.0392 3.58141 10.1744 3.65948C10.4864 3.83959 10.9584 4.03438 11.19 4.12628C11.1998 4.11996 11.2101 4.1136 11.2203 4.10773C11.3057 4.05844 11.3998 4.02588 11.4976 4.013C11.5955 4.00011 11.6952 4.00697 11.7906 4.03253C11.8858 4.05809 11.975 4.10236 12.0533 4.16241C12.1316 4.22253 12.1973 4.2976 12.2466 4.38312C12.2959 4.46854 12.3284 4.56268 12.3414 4.66046C12.3542 4.75833 12.3474 4.85808 12.3218 4.95343C12.2963 5.0488 12.252 5.13876 12.1919 5.2171C12.1319 5.29527 12.0566 5.36117 11.9712 5.41046C11.8859 5.45968 11.7916 5.49134 11.6939 5.50421C11.596 5.5171 11.4963 5.51121 11.4009 5.48566C11.3057 5.46011 11.2165 5.41578 11.1382 5.35577C11.06 5.2957 10.9942 5.22051 10.9449 5.13507C10.8955 5.04957 10.863 4.95464 10.8501 4.85675C10.8455 4.82109 10.8438 4.78513 10.8443 4.74933C10.6528 4.59745 10.2396 4.27823 9.92241 4.09503C9.78436 4.01531 9.61479 3.93302 9.44878 3.85773C9.40271 3.89826 9.35231 3.93497 9.29839 3.96613C9.08309 4.09045 8.82663 4.12411 8.58648 4.05988C8.34619 3.99547 8.14056 3.83786 8.01616 3.62238C7.89186 3.40699 7.85816 3.1507 7.92241 2.91046C7.9868 2.67011 8.14448 2.46457 8.35991 2.34015C8.57532 2.21582 8.8316 2.18201 9.07183 2.2464C9.08928 2.25108 9.10653 2.25638 9.12359 2.26202C9.32394 2.1058 9.56678 1.90769 9.7398 1.73468C9.95494 1.51948 10.21 1.1964 10.3736 0.980773C10.3691 0.922319 10.3748 0.862704 10.3902 0.804992C10.4262 0.670558 10.5139 0.555292 10.6343 0.485656C10.7548 0.416082 10.8984 0.396987 11.0328 0.432922ZM8.80327 2.64386C8.52359 2.64386 8.29646 2.87095 8.29644 3.1507C8.29647 3.43043 8.5236 3.65753 8.80327 3.65753C9.0828 3.65735 9.3091 3.43032 9.30913 3.1507C9.30912 2.87106 9.08281 2.64404 8.80327 2.64386ZM13.6626 0.785461C13.6806 0.791525 13.6972 0.802113 13.7105 0.815734C13.7237 0.829335 13.7333 0.846446 13.7388 0.864562L13.9918 1.67413L14.8228 1.95636C14.8471 1.96433 14.8685 1.97944 14.8834 2.0003C14.8982 2.02124 14.9063 2.04689 14.9058 2.07257C14.9053 2.09809 14.896 2.12264 14.8804 2.14288C14.8648 2.16322 14.8426 2.17786 14.8179 2.18488L13.9908 2.43195L13.7203 3.24542C13.7123 3.26941 13.6969 3.29027 13.6763 3.30499C13.6558 3.31968 13.6312 3.3276 13.606 3.32745C13.5808 3.32723 13.556 3.31897 13.5357 3.30402C13.5155 3.28896 13.5003 3.26754 13.4927 3.24347L13.2398 2.4339L12.4341 2.18976C12.41 2.18256 12.3889 2.16776 12.3736 2.14777C12.3582 2.12758 12.3488 2.10284 12.3482 2.07745C12.3476 2.0521 12.3552 2.02698 12.3697 2.00616C12.3842 1.98532 12.4053 1.96952 12.4293 1.96124L13.2408 1.67609L13.5113 0.861633C13.5162 0.846745 13.5235 0.832482 13.5337 0.820617C13.544 0.808763 13.5568 0.799327 13.5709 0.792297C13.5849 0.785284 13.6002 0.780737 13.6158 0.779602C13.6315 0.778495 13.6477 0.780453 13.6626 0.785461Z"
                            fill="#6E39FF"
                          />
                          <path
                            d="M4.34331 3.30792C4.58892 3.30792 4.80518 3.42838 4.93999 3.61261C5.03554 3.66601 5.11242 3.7477 5.15874 3.84698L5.17534 3.88995L8.31206 12.8294C8.41331 13.1174 8.23184 13.4389 7.90777 13.5482C7.60015 13.6518 7.2739 13.5278 7.15484 13.2679L7.13824 13.2249L6.32574 10.9095H2.26324L1.45171 13.2249C1.35022 13.5129 1.00578 13.6579 0.68218 13.5482C0.373839 13.4443 0.195387 13.149 0.265188 12.8724L0.277883 12.8284L3.41363 3.89093C3.44478 3.80751 3.49545 3.73216 3.56206 3.67316C3.62864 3.61419 3.70904 3.57283 3.79546 3.55206C3.86451 3.47521 3.94919 3.41341 4.04351 3.3714C4.13767 3.3295 4.24028 3.30783 4.34331 3.30792ZM10.1119 7.11554C10.4374 7.11564 10.7036 7.27829 10.73 7.48468L10.732 7.51788V13.1458C10.732 13.3682 10.4539 13.548 10.1119 13.5482C9.78607 13.5482 9.51844 13.3862 9.49273 13.179L9.49077 13.1458V7.51788C9.49117 7.29561 9.76935 7.11554 10.1119 7.11554ZM2.68999 9.69073H5.898L4.29351 5.1214L2.68999 9.69073ZM11.0328 0.432922C11.1672 0.468964 11.2825 0.557449 11.3521 0.678039C11.4216 0.798582 11.4399 0.942062 11.4039 1.07648C11.3678 1.2108 11.2801 1.32522 11.1597 1.39484C11.0631 1.4506 10.9519 1.47372 10.8423 1.46222C10.6269 1.62599 10.3089 1.87753 10.0962 2.09015C9.94082 2.24562 9.76424 2.45807 9.61675 2.64484C9.62505 2.6577 9.63347 2.67058 9.64116 2.6839C9.76177 2.89288 9.79746 3.14038 9.74077 3.37433C9.88622 3.47825 10.0392 3.58141 10.1744 3.65948C10.4864 3.83959 10.9584 4.03438 11.19 4.12628C11.1998 4.11996 11.2101 4.1136 11.2203 4.10773C11.3057 4.05844 11.3998 4.02588 11.4976 4.013C11.5955 4.00011 11.6952 4.00697 11.7906 4.03253C11.8858 4.05809 11.975 4.10236 12.0533 4.16241C12.1316 4.22253 12.1973 4.2976 12.2466 4.38312C12.2959 4.46854 12.3284 4.56268 12.3414 4.66046C12.3542 4.75833 12.3474 4.85808 12.3218 4.95343C12.2963 5.0488 12.252 5.13876 12.1919 5.2171C12.1319 5.29527 12.0566 5.36117 11.9712 5.41046C11.8859 5.45968 11.7916 5.49134 11.6939 5.50421C11.596 5.5171 11.4963 5.51121 11.4009 5.48566C11.3057 5.46011 11.2165 5.41578 11.1382 5.35577C11.06 5.2957 10.9942 5.22051 10.9449 5.13507C10.8955 5.04957 10.863 4.95464 10.8501 4.85675C10.8455 4.82109 10.8438 4.78513 10.8443 4.74933C10.6528 4.59745 10.2396 4.27823 9.92241 4.09503C9.78436 4.01531 9.61479 3.93302 9.44878 3.85773C9.40271 3.89826 9.35231 3.93497 9.29839 3.96613C9.08309 4.09045 8.82663 4.12411 8.58648 4.05988C8.34619 3.99547 8.14056 3.83786 8.01616 3.62238C7.89186 3.40699 7.85816 3.1507 7.92241 2.91046C7.9868 2.67011 8.14448 2.46457 8.35991 2.34015C8.57532 2.21582 8.8316 2.18201 9.07183 2.2464C9.08928 2.25108 9.10653 2.25638 9.12359 2.26202C9.32394 2.1058 9.56678 1.90769 9.7398 1.73468C9.95494 1.51948 10.21 1.1964 10.3736 0.980773C10.3691 0.922319 10.3748 0.862704 10.3902 0.804992C10.4262 0.670558 10.5139 0.555292 10.6343 0.485656C10.7548 0.416082 10.8984 0.396987 11.0328 0.432922ZM8.80327 2.64386C8.52359 2.64386 8.29646 2.87095 8.29644 3.1507C8.29647 3.43043 8.5236 3.65753 8.80327 3.65753C9.0828 3.65735 9.3091 3.43032 9.30913 3.1507C9.30912 2.87106 9.08281 2.64404 8.80327 2.64386ZM13.6626 0.785461C13.6806 0.791525 13.6972 0.802113 13.7105 0.815734C13.7237 0.829335 13.7333 0.846446 13.7388 0.864562L13.9918 1.67413L14.8228 1.95636C14.8471 1.96433 14.8685 1.97944 14.8834 2.0003C14.8982 2.02124 14.9063 2.04689 14.9058 2.07257C14.9053 2.09809 14.896 2.12264 14.8804 2.14288C14.8648 2.16322 14.8426 2.17786 14.8179 2.18488L13.9908 2.43195L13.7203 3.24542C13.7123 3.26941 13.6969 3.29027 13.6763 3.30499C13.6558 3.31968 13.6312 3.3276 13.606 3.32745C13.5808 3.32723 13.556 3.31897 13.5357 3.30402C13.5155 3.28896 13.5003 3.26754 13.4927 3.24347L13.2398 2.4339L12.4341 2.18976C12.41 2.18256 12.3889 2.16776 12.3736 2.14777C12.3582 2.12758 12.3488 2.10284 12.3482 2.07745C12.3476 2.0521 12.3552 2.02698 12.3697 2.00616C12.3842 1.98532 12.4053 1.96952 12.4293 1.96124L13.2408 1.67609L13.5113 0.861633C13.5162 0.846745 13.5235 0.832482 13.5337 0.820617C13.544 0.808763 13.5568 0.799327 13.5709 0.792297C13.5849 0.785284 13.6002 0.780737 13.6158 0.779602C13.6315 0.778495 13.6477 0.780453 13.6626 0.785461Z"
                            fill="url(#paint0_linear_2858_11057)"
                          />
                          <defs>
                            <linearGradient
                              id="paint0_linear_2858_11057"
                              x1="0.25012"
                              y1="7.45919"
                              x2="8.97502"
                              y2="0.878839"
                              gradientUnits="userSpaceOnUse"
                            >
                              <stop stop-color="#187CFC" />
                              <stop offset="1" stop-color="#7700FE" />
                            </linearGradient>
                          </defs>
                        </svg>
                      </div>
                      <span className={styles.tagText}>Agent</span>
                    </div>
                  </div>

                  <div className={styles.cardDescription}>
                    {card.description?.map(item => {
                      return (
                        <span
                          className={`${styles.gridItem} ${
                            item.length > 7 ? styles.longText : ''
                          }`}
                        >
                          <svg
                            width="7"
                            height="7"
                            viewBox="0 0 7 7"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <circle
                              cx="3.43787"
                              cy="3.5"
                              r="2.5"
                              stroke="white"
                            />
                          </svg>
                          {item}
                        </span>
                      );
                    })}
                  </div>
                  <button
                    className={styles.cardAction}
                    onClick={() =>
                      history.push(`/homepage/detail?id=${card.id}`)
                    }
                  >
                    <span className={styles.actionText}>GO</span>
                    <span className={styles.actionIcon}>
                      <svg
                        width="14"
                        height="8"
                        viewBox="0 0 14 8"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M13.3536 4.35355C13.5488 4.15829 13.5488 3.84171 13.3536 3.64645L10.1716 0.464466C9.97631 0.269204 9.65973 0.269204 9.46447 0.464466C9.2692 0.659728 9.2692 0.976311 9.46447 1.17157L12.2929 4L9.46447 6.82843C9.2692 7.02369 9.2692 7.34027 9.46447 7.53553C9.65973 7.7308 9.97631 7.7308 10.1716 7.53553L13.3536 4.35355ZM0 4.5H13V3.5H0V4.5Z"
                          fill="#7838EC"
                        />
                      </svg>
                    </span>
                  </button>
                </div>
              </div>
            ))}
          </div>
        </section>
        {/* 轮播指示器 */}
        <div className={styles.slideIndicator}>
          {carouselCards.map((_, idx) => (
            <div
              key={idx}
              className={`${styles.dot} ${
                currentIndex === idx ? styles.active : ''
              }`}
              onClick={() => setCurrentIndex(idx)}
            />
          ))}
        </div>
        {/* <img
          src={mobileChat}
          style={{
            position: 'fixed',
            height: 50,
            width: 50,
            zIndex: 999,
            bottom: 100,
            right: 20,
          }}
        /> */}
      </main>
      {visible ? <CookieDiv setVisible={setVisible} /> : null}
      <MobileFooter />
    </div>
  );
};
const CookieDiv = ({ setVisible }) => {
  const handleOk = () => {
    // 保存用户同意到本地存储
    localStorage.setItem('cookieConsent', 'true');
    setVisible(false);
  };

  const getCookiePolicyLink = () => {
    const locale = getLocale();
    if (locale === 'zh-CN') {
      return (
        <a
          href={`${window.location.origin}/#/cookiePolicy`}
          target="_blank"
          rel="noopener noreferrer"
        >
          《Cookie 政策》
        </a>
      );
    } else {
      return (
        <a
          href={`${window.location.origin}/#/cookiePolicy`}
          target="_blank"
          rel="noopener noreferrer"
        >
          《Cookie Policy》
        </a>
      );
    }
  };
  return (
    <div className={styles.cookieConsent}>
      <div className={styles.cookieConsentContent}>
        <FormattedMessage
          id="cookie.consent.content"
          values={{
            link: getCookiePolicyLink(),
          }}
        />
      </div>
      <div className={styles.cookieConsentButton}>
        {/* 接受cookie 只接受必要cookie */}
        <div className={styles.accept} onClick={handleOk}>
          <FormattedMessage
            id="cookie.consent.accept"
            defaultValue="接受所有Cookie"
          />
        </div>
        {/* 拒绝cookie 拒绝所有cookie */}
        <div className={styles.necessary} onClick={handleOk}>
          <FormattedMessage
            id="cookie.consent.reject"
            defaultValue="接受必要Cookie"
          />
          {/* <FormattedMessage
            id="event.notification.status.reject"
            defaultValue="拒绝"
          /> */}
        </div>
      </div>
    </div>
  );
};
export default HomepageMobile;
