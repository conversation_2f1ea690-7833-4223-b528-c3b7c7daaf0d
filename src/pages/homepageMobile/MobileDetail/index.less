.homeContainer {
  width: 100%;
  height: 100vh;
  background-image: url('../../../assets/homepage_mobileBG.png');
  background-repeat: round;
  // background-size: cover;
  // background-position: center;
  // background-repeat: no-repeat;
  /* 状态栏 + 导航栏高度 */
  /* 隐藏滚动条 */
  overflow-x: hidden;
  overflow-y: scroll;

  &::-webkit-scrollbar {
    display: none;
  }

  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */

  .agentListColumn {
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .agentListItemContent {
      margin-bottom: 0.857rem;
    }

    .aiAgentLibraryAgentListItem {
      background-color: #fff;
      border-radius: 0.514rem;
      padding: 0.943rem 0.771rem;
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      .aiAgentLibraryAgentListItemTitle {
        font-family: Microsoft YaHei;
        font-weight: 700;
        font-size: 1.114rem;
        line-height: 150%;
        letter-spacing: 0%;
        color: #333;
        margin-bottom: 0.857rem;
      }

      .agentListItems {
        margin-bottom: 0.857rem;

        b {
          display: contents;
        }

        .agentListItemTitle {
          font-family: Microsoft YaHei;
          font-weight: 700;
          font-size: 1.003rem;
          line-height: 150%;
          letter-spacing: 0%;
          color: #333;
          display: flex;
          margin-bottom: 0.429rem;
          align-items: center;
          gap: 0.283rem;

          svg {
            width: 0.857rem;
            height: 0.857rem;
          }
        }

        .agentListItemDescriptionItem {
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 1.003rem;
          line-height: 150%;
          letter-spacing: 0%;
          color: #333;
          margin-bottom: 0.343rem;
          display: flex;
          gap: 0.283rem;
          align-items: center;

          svg {
            width: 0.429rem;
            height: 0.429rem;
            flex-shrink: 0;
          }
        }
      }

      .agentListItemButton {
        background: #7700fe;
        // width: 7.285rem;
        height: 2.425rem;
        padding-top: 0.574rem;
        padding-right: 1.286rem;
        padding-bottom: 0.574rem;
        padding-left: 1.286rem;
        gap: 0.283rem;
        border-radius: 0.36rem;
        font-family: Microsoft YaHei;
        font-weight: 700;
        font-size: 0.857rem;
        line-height: 150%;
        letter-spacing: 0px;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  /* 全局样式调整 */
  :global {
    html {
      font-size: 14px;
    }

    body {
      margin: 0;
      padding: 0;
      font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont,
        'Segoe UI', Roboto, sans-serif;
    }
  }
}
