import React, { useEffect, useState, useRef } from 'react';
import styles from './index.less';
import MobileHeader from '../MobileHeader';
import MobileFooter from '../MobileFooter';
import { FormattedMessage, setLocale, getLocale, useIntl, history } from 'umi';
import mobileChat from '@/assets/mobileChat.gif';
import { motion } from 'framer-motion';
import MotionFade from '@/components/MotionFade';
import { circleSvg } from '@/pages/AIAgentLibrary/icons';
import {
  agentFinanceList,
  agentGameList,
  agentNewEnergyList,
  agentRetailList,
  agentManufacturingList,
} from '@/pages/AIAgentLibrary/libraryData';
const MobileDetail = () => {
  const { id } = history.location.query;
  const [detailCard, setDetailCard] = useState([]);
  // 监听页面滚动，记录scrollTop是否大于0
  const [isScrolled, setIsScrolled] = useState(false);

  // 处理容器滚动事件
  const boxTouchMove = e => {
    // 获取滚动容器
    const container = e.currentTarget;
    if (container) {
      // 获取滚动位置
      const scrollTop = container.scrollTop;
      console.log('容器滚动位置:', scrollTop);

      // 当滚动位置大于0时，设置isScrolled为true，否则为false
      setIsScrolled(scrollTop > 0);
    }
  };
  // 初始化检查滚动位置
  useEffect(() => {
    // 获取滚动容器
    const container = document.querySelector(`.${styles.homeContainer}`);
    if (container) {
      // 检查初始滚动位置
      setIsScrolled(container.scrollTop > 0);

      // 监听滚动事件
      const handleScroll = () => {
        setIsScrolled(container.scrollTop > 0);
      };

      // 添加滚动事件监听
      container.addEventListener('scroll', handleScroll, { passive: true });

      // 清理函数
      return () => {
        container.removeEventListener('scroll', handleScroll);
      };
    }
  }, []);
  // 设置基准字体大小
  useEffect(() => {
    document.documentElement.style.fontSize = '14px';
    // 初始化区域选择，先获取浏览器语言，如果没有则获取系统语言
    const getBrowserLanguage = () => {
      // 尝试获取浏览器语言
      const browserLang = navigator.language || navigator.userLanguage;
      // 如果浏览器语言不可用，尝试获取系统语言
      if (!browserLang) {
        // 尝试通过其他方式获取系统语言
        // 注意：大多数情况下，navigator.language 应该已经包含了系统语言信息
        const systemLang = navigator.languages?.[0] || 'zh-CN'; // 默认为中文
        return systemLang;
      }
      return browserLang.includes('en') ? 'en-US' : browserLang;
    };
    // 读取浏览器的语言，设置本地化
    const browserLang = getBrowserLanguage();
    // const local = browserLang.toLowerCase().includes('zh') ? 'zh-CN' : browserLang;
    setLocale(browserLang, false);

    // 清理函数
    return () => {
      document.documentElement.style.fontSize = '';
    };
  }, []);
  useEffect(() => {
    (function(w, d, x, id) {
      let s = d.createElement('script');
      s.src = 'https://chat.connectnowai.com/ai-agent/umi.js';
      s.async = 1;
      s.id = 'goclouds-script';
      d.getElementsByTagName('head')[0].appendChild(s);
      w[x] =
        w[x] ||
        function() {
          (w[x].ac = w[x].ac || []).push(arguments);
        };
    })(window, document, 'goclouds', '1683b9a8-a205-4606-b040-c2b2ed6aab54');
    const industry = localStorage.getItem('libraryIndustry') || 'Finance';
    const aiagentName =
      localStorage.getItem('libraryAiagentName') || 'FinanceKB';
    goclouds({
      i: null,
      v: 'd780c7e37bf335b1f3b28ed9ea4a64a8',
      channelType: '8',
      singleChat: true,
      ticketAttr: {
        aiagentName: { value: aiagentName },
        industry: { value: industry },
      },
    });
    return () => {
      console.log('组件卸载');
      window['goclouds'].ac[0][0] = {
        i: null,
        v: process.env.CHAT_PARAMS_V,
        channelType: '8',
      };
      loadInit(process.env.CHAT_PATH);
    };
  }, []);
  useEffect(() => {
    if (id == 2) {
      setDetailCard(agentFinanceList);
    } else if (id == 3) {
      setDetailCard(agentManufacturingList);
    } else if (id == 1) {
      setDetailCard(agentRetailList);
    } else if (id == 4) {
      setDetailCard(agentGameList);
    } else if (id == 5) {
      setDetailCard(agentNewEnergyList);
    }
  }, [id]);
  const loadInit = url => {
    const existing = document.getElementById('goclouds-script');
    const livechatDiv = document.getElementById('ConnectNowChat');
    if (existing) {
      existing.remove();
      if (livechatDiv) {
        livechatDiv.remove();
      }
    }
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = url || 'https://chat.connectnowai.com/ai-agent/umi.js';
      script.async = true;
      script.id = 'goclouds-script';
      script.onload = () => {
        if (!url) {
          const pollInterval = setInterval(() => {
            // 查找src包含chat_open.gif的img元素
            const imgElement = document.querySelector(
              'img[src*="chat_open.gif"]',
            );
            console.log('找到chat_open.gif图片元素:', imgElement);
            if (imgElement) {
              // 找到img元素的祖父元素（爷爷div）
              const grandparentElement =
                imgElement.parentElement?.parentElement;
              console.log('找到祖父元素:', grandparentElement);
              if (grandparentElement) {
                grandparentElement.click();
                clearInterval(pollInterval); // 找到元素并点击后清除轮询
              }
            }
          }, 500); // 每100ms检查一次
        }
      };
      script.onerror = () => {
        console.error('Failed to load goclouds script');
        reject();
      };
      document.head.appendChild(script);
    });
  };
  const handleClick = (aiagentName, industry) => {
    console.log('handleClick', aiagentName, industry);
    localStorage.removeItem('inProgress');
    localStorage.removeItem('historySession');
    // console.log('goclouds', goclouds);
    window.goclouds.ac[0][0] = {
      i: null,
      v: 'd780c7e37bf335b1f3b28ed9ea4a64a8',
      channelType: '8',
      singleChat: true,
      ticketAttr: {
        aiagentName: { value: aiagentName },
        industry: { value: industry },
      },
    };
    // console.log('window.goclouds', window['goclouds'].ac[0][0]);
    loadInit();
  };
  return (
    <div
      className={styles.homeContainer}
      onTouchMove={boxTouchMove}
      onScroll={boxTouchMove}
    >
      <MobileHeader isScrolled={isScrolled} />

      <main style={{ margin: '0 0.857rem' }}>
        <div className={styles.agentListColumn}>
          {detailCard.map((item, index) => (
            <MotionFade
              as={motion.div}
              type="fadeUp"
              className={styles.agentListItemContent}
              triggerOnce={true}
              threshold={0}
            >
              <img
                src={item.img}
                alt=""
                className={styles.aiAgentLibraryAgentListItemImg}
              />
              <div className={styles.aiAgentLibraryAgentListItem}>
                <div className={styles.aiAgentLibraryAgentListItemTitle}>
                  {item.title}
                </div>
                {/* 行业 */}
                {item.content.map((content, index) => (
                  <div className={styles.agentListItems}>
                    <div className={styles.agentListItemTitle}>
                      {content.icon}
                      {content.title}
                    </div>
                    {/* 描述列表 */}
                    {content.list.map(item => (
                      <div className={styles.agentListItemDescriptionItem}>
                        {index != 1 && circleSvg}
                        {item}
                      </div>
                    ))}
                  </div>
                ))}
                <div
                  className={styles.agentListItemButton}
                  onClick={() =>
                    handleClick(item.aiagentName, item.industryValue)
                  }
                >
                  <FormattedMessage id="aiAgentLibrary.agent.list.button" />
                  <div className={styles.arrowWrapper}>
                    <svg
                      width="14"
                      height="8"
                      viewBox="0 0 14 8"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M13.3536 4.35355C13.5488 4.15829 13.5488 3.84171 13.3536 3.64645L10.1716 0.464466C9.97631 0.269204 9.65973 0.269204 9.46447 0.464466C9.2692 0.659728 9.2692 0.976311 9.46447 1.17157L12.2929 4L9.46447 6.82843C9.2692 7.02369 9.2692 7.34027 9.46447 7.53553C9.65973 7.7308 9.97631 7.7308 10.1716 7.53553L13.3536 4.35355ZM0 4.5H13V3.5H0V4.5Z"
                        fill="#fff"
                      />
                    </svg>
                  </div>
                </div>
              </div>
            </MotionFade>
          ))}
        </div>
        {/* <img
          src={mobileChat}
          style={{
            position: 'fixed',
            height: 50,
            width: 50,
            zIndex: 999,
            bottom: 100,
            right: 20,
          }}
        /> */}
      </main>

      <MobileFooter />
    </div>
  );
};

export default MobileDetail;
