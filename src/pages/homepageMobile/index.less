.homeContainer {
  width: 100%;
  height: 100vh;
  background-image: url('../../assets/homepage_mobileBG.png');
  background-repeat: round;

  // background-size: cover;
  // background-position: center;
  // background-repeat: no-repeat;
  /* 状态栏 + 导航栏高度 */
  /* 隐藏滚动条 */
  overflow-x: hidden;
  overflow-y: scroll;

  &::-webkit-scrollbar {
    display: none;
  }

  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */

  .slideIndicator {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.714rem;
    margin: 1.429rem 0 2.286rem;

    .dot {
      width: 0.571rem;
      height: 0.571rem;
      border-radius: 50%;
      background-color: #e6e6e6;

      &.active {
        width: 1.429rem;
        height: 0.571rem;
        border-radius: 0.714rem;
        background-color: #3463fc;
        box-shadow: 0 0.214rem 0.286rem rgba(52, 99, 252, 0.2);
      }
    }
  }

  .solutionsSection {
    padding: 0 1.071rem;
    overflow: hidden;

    .solutionCard {
      width: 17.851rem !important;
      height: 27.639rem;
      position: relative;
      margin: 0 0.857rem;
      touch-action: pan-y;
      // box-shadow: 13px 60px 20px 0px #00000010;

      .cardBg {
        position: absolute;
        top: 0;
        left: 0;
        width: 17.851rem !important;
        height: 27.639rem;
        // box-shadow: 5px 5px 20px 0px #00000026;

        // backdrop-filter: blur(100px);
      }

      .cardIcon {
        position: absolute;
        left: 0;
        top: 9.498rem;
        width: 17.854rem;
        height: 17.854rem;
        opacity: 0.5;
      }

      .cardImage {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 9.57rem;
        height: 14.284rem;
        object-fit: cover;
        border-radius: 0.714rem;
      }

      .cardContent {
        position: relative;
        z-index: 1;
        padding: 1.071rem;
        padding-right: 0;

        .cardHeader {
          display: flex;
          align-items: center;
          gap: 0.286rem;
          margin-bottom: 0.714rem;

          .cardTitle {
            font-size: 1.143rem;
            font-weight: 700;
            color: #ffffff;
            margin: 0;
          }

          .cardTag {
            display: flex;
            align-items: center;
            gap: 0.286rem;
            padding: 0.214rem 0.571rem;
            background-color: rgba(255, 255, 255, 0.8);
            border-radius: 0.357rem;

            .tagIcon {
              width: 1.047rem;
              height: 0.94rem;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .tagText {
              font-size: 0.857rem;
              font-weight: 700;
              color: #7838ec;
            }
          }
        }

        .cardDescription {
          font-size: 0.943rem;
          line-height: 1.5;
          color: #ffffff;
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 0.542rem;
          margin-bottom: 0.857rem;

          .longText {
            grid-column: 1 / span 2 !important;
          }

          .gridItem {
            // display: inline-flex;
            // align-items: center;
            // margin-bottom: 0.5rem;
            // margin-right: 1rem;
            display: flex;
            align-items: center;
            overflow: hidden;
            text-overflow: ellipsis;
            // white-space: nowrap;
            grid-column: auto / span 1;

            svg {
              width: 0.429rem;
              height: 0.429rem;
              margin-right: 0.257rem;
            }
          }
        }

        // .cardDescriptionFlex {
        //   font-size: 0.943rem;
        //   line-height: 1.5;
        //   color: #ffffff;
        //   display: flex;
        //   flex-direction: column;
        //   gap: 0.542rem;
        //   margin-bottom: 0.857rem;

        //   .gridItem {
        //     display: flex;
        //     align-items: center;
        //     gap: 0.429rem;

        //     svg {
        //       width: 0.429rem;
        //       height: 0.429rem;
        //       flex-shrink: 0;
        //     }
        //   }
        // }

        .cardAction {
          display: inline-flex;
          align-items: center;
          gap: 0.214rem;
          padding: 0.571rem 1.143rem;
          background-color: rgba(255, 255, 255);
          border: none;
          border-radius: 7.141rem;
          cursor: pointer;

          .actionText {
            font-size: 0.857rem;
            font-weight: 700;
            color: #7838ec;
            font-family: 'Microsoft YaHei';
          }

          .actionIcon {
            width: 1rem;
            height: 0.571rem;
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }

  /* 全局样式调整 */
  :global {
    html {
      font-size: 14px;
    }

    body {
      margin: 0;
      padding: 0;
      font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont,
        'Segoe UI', Roboto, sans-serif;
    }

    * {
      box-sizing: border-box;
    }

    .slick-slide {
      width: fit-content !important;
    }

    .slick-track {
      // transform: translate3d(-16.423rem, 0px, 0px) !important;
    }
  }

  .cookieConsent {
    max-width: 85%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    position: fixed;
    bottom: 0.857rem;
    left: 0.857rem;
    right: 0;
    z-index: 999;
    border-radius: 0.857rem;
    border: 0.161rem solid #fff;
    background: rgba(255, 255, 255, 0.7);
    box-shadow: 0 0.214rem 0.536rem 0 rgba(0, 0, 0, 0.1);
    -webkit-backdrop-filter: blur(0.804rem);
    backdrop-filter: blur(0.804rem);
    padding: 0.857rem;

    .cookieConsentContent {
      color: #333;
      font-family: 'Microsoft YaHei';
      font-size: 0.711rem;
      font-style: normal;
      font-weight: 400;
      line-height: 200%;
      margin-bottom: 0.857rem;
    }

    .cookieConsentButton {
      display: flex;
      width: 100%;
      align-items: center;
      gap: 1.179rem;

      .accept {
        padding: 0.711rem 0.566rem;
        color: #fff;
        font-family: 'Microsoft YaHei';
        font-size: 0.857rem;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 0.357rem;
        background: #644cf6;
        cursor: pointer;
        flex-grow: 1;
      }

      .necessary {
        color: #644cf6;
        font-family: 'Microsoft YaHei';
        font-size: 0.857rem;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        cursor: pointer;
        border-radius: 0.357rem;
        padding: 0.711rem 0.566rem;
        border: 1px solid #644cf6;
        flex-grow: 1;
        text-align: center;
      }
    }
  }
}
