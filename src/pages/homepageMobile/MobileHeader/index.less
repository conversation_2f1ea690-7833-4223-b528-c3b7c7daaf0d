.mobileHeader {
  width: 100%;

  .hero {
    padding: 5.571rem 2.5rem 1.286rem;
    text-align: center;
    position: relative;
    overflow: hidden;

    .heroBackground {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;

      .bgCircle {
        position: absolute;
        border-radius: 50%;
        opacity: 0.2;

        &.bgCircle1 {
          width: 12.641rem;
          height: 12.641rem;
          background-color: #aeed3b;
          top: 0;
          left: 4.642rem;
          filter: blur(6.571rem);
        }

        &.bgCircle2 {
          width: 10rem;
          height: 10rem;
          background-color: #ad30e5;
          top: 1.643rem;
          right: 2.143rem;
          filter: blur(6.571rem);
        }

        &.bgCircle3 {
          width: 23.782rem;
          height: 23.782rem;
          background-color: #aeed3b;
          top: 28.71rem;
          left: 0;
          filter: blur(6.571rem);
        }
      }
    }

    .heroTitle {
      font-size: 2.857rem;
      font-weight: 700;
      margin-bottom: 0.5rem;
      background: linear-gradient(
        -45deg,
        #eb3349,
        #f45c43,
        #ff8008,
        #ffc837,
        #4cb8c4,
        #3cd3ad,
        #24c6dc,
        #514a9d,
        #ff512f,
        #dd2476
      );
      color: transparent;
      background-size: 200% 200%;
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      line-height: 1.31982421875em;
      animation: gradientMove 8s ease infinite;

      @keyframes gradientMove {
        0% {
          background-position: 0% 50%;
        }

        25% {
          background-position: 50% 0%;
        }

        50% {
          background-position: 100% 50%;
        }

        75% {
          background-position: 50% 100%;
        }

        100% {
          background-position: 0% 50%;
        }
      }
    }

    .heroSubtitle {
      font-size: 2.143rem;
      font-weight: 700;
      margin-bottom: 0.714rem;
      background: #333;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      line-height: 1.31982421875em;
    }

    .heroText {
      font-size: 1.003rem;
      line-height: 2;
      color: #333333;
      margin-bottom: 1.429rem;
    }
  }

  .navBar {
    width: 100%;
    height: 3.143rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 1.071rem;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;

    .logoContainer {
      .logo {
        height: 1.714rem;
        width: auto;
      }
    }

    .actions {
      display: flex;
      gap: 0.351rem;

      .iconWrapper {
        width: 2.143rem;
        height: 2.143rem;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;

        .icon {
          width: 1.286rem;
          height: 1.286rem;
        }
      }
    }
  }
}
