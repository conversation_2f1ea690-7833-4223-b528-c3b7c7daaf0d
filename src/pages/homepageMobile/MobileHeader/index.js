import React, { useState, useEffect } from 'react';
import { FormattedMessage } from 'umi';
import styles from './index.less';
import menuIcon from '@/assets/menuIcon.svg';
import searchIcon from '@/assets/searchIcon.svg';

import headerLogo from '@/assets/logo.png';
import { Picker } from 'antd-mobile';

const MobileHeader = ({ TitleShow = true, isScrolled }) => {
  const basicColumns = [
    {
      value: 'china',
      label: <FormattedMessage id="home.page.region.china" />,
      url: 'https://www.connectnow.cn',
    },
    {
      value: 'asia',
      label: <FormattedMessage id="home.page.region.asia" />,
      url: 'https://www.connectnowai.com',
    },
    {
      value: 'europe',
      label: <FormattedMessage id="home.page.region.europe" />,
      url: 'https://eu.connectnowai.com',
    },
    {
      value: 'usa',
      label: <FormattedMessage id="home.page.region.usa" />,
      url: 'https://us.connectnowai.com',
    },
  ];
  const [visible, setVisible] = useState(false);
  const [value, setValue] = useState([]);

  useEffect(() => {
    // 获取当前网站域名并设置相应的区域
    const currentDomain = window.location.hostname;

    // 根据域名设置默认区域值
    if (currentDomain.includes('us.connectnowai.com')) {
      setValue(['usa']);
    } else if (currentDomain.includes('eu.connectnowai.com')) {
      setValue(['europe']);
    } else if (currentDomain.includes('www.connectnowai.com')) {
      setValue(['asia']);
    } else if (currentDomain.includes('www.connectnow.cn')) {
      setValue(['china']);
    } else {
      // 默认设置为中国区域
      setValue(['china']);
    }
  }, []);

  return (
    <div className={styles.mobileHeader}>
      <div
        className={styles.navBar}
        style={{
          boxShadow: isScrolled ? '0px 2px 4px 0px #0000001A' : '',
          backgroundColor: isScrolled ? '#fff' : '',
        }}
      >
        <div className={styles.logoContainer}>
          <img src={headerLogo} alt="Logo" className={styles.logo} />
        </div>
        <div className={styles.actions}>
          <Picker
            data={basicColumns}
            visible={visible}
            onDismiss={() => {
              setVisible(false);
            }}
            cols={1}
            value={value}
            onOk={v => {
              // 根据选中的值找到对应的选项
              const option = basicColumns.find(item => item.value === v[0]);
              // 如果找到了对应的选项并且有URL，则进行跳转
              if (option && option.url) {
                // 跳转到新的URL
                window.location.href = option.url;
              }
              setValue(v);
            }}
            okText={<FormattedMessage id="work.record.button.ok" />}
            dismissText={
              <FormattedMessage id="ai.agent.enable.confirm.cancel" />
            }
          >
            <div
              className={styles.iconWrapper}
              onClick={() => setVisible(true)}
            >
              <img src={menuIcon} alt="菜单" className={styles.icon} />
            </div>
          </Picker>
          <div
            className={styles.iconWrapper}
            onClick={() => console.log('搜索')}
          >
            <img src={searchIcon} alt="搜索" className={styles.icon} />
          </div>
        </div>
      </div>
      {/* Hero Section */}
      {TitleShow ? (
        <section className={styles.hero}>
          <div className={styles.heroBackground}>
            <div className={`${styles.bgCircle} ${styles.bgCircle1}`}></div>
            <div className={`${styles.bgCircle} ${styles.bgCircle2}`}></div>
            <div className={`${styles.bgCircle} ${styles.bgCircle3}`}></div>
          </div>

          <div className={styles.heroTitle}>
            <FormattedMessage id="aiAgentLibrary.banner.subtitle" />
          </div>
          <h2 className={styles.heroSubtitle}>
            <FormattedMessage id="aiAgentLibrary.banner.title" />
          </h2>
          <p className={styles.heroText}>
            <FormattedMessage id="home.page.new.banner.desc" />
          </p>
        </section>
      ) : null}
    </div>
  );
};

export default MobileHeader;
