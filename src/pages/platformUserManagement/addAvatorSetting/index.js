import React, {
  Component,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  connect,
  useDispatch,
  useSelector,
  getIntl,
  FormattedMessage,
  history,
} from 'umi';
import { PlusOutlined } from '@ant-design/icons';
import { ReactComponent as DeleteHome } from '@/assets/deleteHome.svg';
import {
  Input,
  Button,
  Select,
  Steps,
  Spin,
  Popconfirm,
  message,
  Upload,
  notification,
} from 'antd';
import ChannelMultipleSelect from '@/components/channelMutipleSelect';
import styles from './index.less';
const { Option, OptGroup } = Select;

// 添加防抖函数
const useDebounce = (fn, delay) => {
  const timerRef = useRef(null);

  return (...args) => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    timerRef.current = setTimeout(() => {
      fn(...args);
    }, delay);
  };
};

export default () => {
  const dispatch = useDispatch();
  const { connectList, deptList, user, authAccess } = useSelector(
    ({ layouts }) => ({
      connectList: layouts.connectList,
      deptList: layouts.deptList,
      user: layouts.user,
      authAccess: layouts.auth,
    }),
  );
  const [loading, setLoading] = useState(false);
  const [loadingBtn, setLoadingBtn] = useState(false);
  const [channelOptions, setChannelOptions] = useState([]);
  const [avatarSettings, setAvatarSettings] = useState([]);
  const [userId, setUserId] = useState('');
  const [inputValues, setInputValues] = useState({});

  useEffect(() => {
    const fetchData = async () => {
      // 从URL获取userId参数
      const { userId } = history.location.query;
      setUserId(userId);
      console.log('当前用户ID:', userId);
      // 如果没有userId，显示提示并返回上一页
      if (!userId) {
        notification.error({
          message: '错误',
          description: '未找到用户ID，请返回重试',
        });
        setTimeout(() => {
          history.goBack();
        }, 1500);
      }
      await newGetChannels();
      await fetchAvatarSettings(userId);
    };
    fetchData();
  }, []);

  const fetchAvatarSettings = async userId => {
    setLoading(true);

    dispatch({
      type: 'userManagement/getUserProfilePicture',
      payload: {
        userId: userId,
      },
      callback: response => {
        setLoading(false);
        if (response.code === 200) {
          if (response.data.length > 0) {
            let result = response.data;
            // 打平channelList数组
            let channelList = result?.map(item => {
              let newList = { ...item };
              let list1 = item.channelList?.map(ov => {
                let list2 = ov.child?.map(ovChild => {
                  return ovChild.channelId;
                });
                console.log(list2, 'list2');
                return list2;
              });
              list1?.flat(Infinity);
              newList.channelList = [...list1]?.flat(Infinity);
              return newList;
            });
            setAvatarSettings(channelList);
            // 初始化 inputValues
            const initialInputValues = {};
            response.data.forEach((setting, index) => {
              initialInputValues[index] = setting.nickName || '';
            });
            setInputValues(initialInputValues);
          } else {
            setAvatarSettings([
              {
                pictureId: '', //配置id
                userId: userId, //用户id
                nickName: '', //用户昵称
                profilePicturePath: '', //用户头像路径
                channelList: [], //渠道信息
              },
            ]);
            // 初始化一个空的输入值
            setInputValues({ 0: '' });
          }
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };
  // 查询渠道
  const newGetChannels = async () => {
    setLoading(true);
    return new Promise((resolve, reject) => {
      dispatch({
        type: 'userManagement/queryChannelTreeForPicture',
        callback: res => {
          if (res.code === 200) {
            let result = [...res.data];
            setChannelOptions(result);
            resolve(); // 成功时调用 resolve
          } else {
            setLoading(false);
            reject(res.msg); // 失败时调用 reject
          }
        },
      });
    });
  };

  const handleAddOrUpdateAvatarSetting = () => {
    console.log(avatarSettings, 'avatarSettings');
    let nickNameFlag = false;
    let channelListFlag = false;
    let pictureFlag = false;

    avatarSettings.forEach(item => {
      if (item.channelList.length === 0) {
        channelListFlag = true;
      }
      if (!item.nickName) {
        nickNameFlag = true;
      }
      if (!item.profilePicturePath) {
        pictureFlag = true;
      }
    });
    if (channelListFlag) {
      notification.error({
        message: getIntl().formatMessage({
          id: 'customerInformation.contactCustomer.channel.placeholder',
          defaultValue: '请选择渠道',
        }),
      });
      return;
    }
    if (nickNameFlag) {
      notification.error({
        message: getIntl().formatMessage({
          id: 'user.management.operation.add.avator.nickname.placeholder',
          defaultValue: '请输入您的昵称',
        }),
      });
      return;
    }
    if (pictureFlag) {
      notification.error({
        message: getIntl().formatMessage({
          id: 'user.management.operation.add.avator.avatar.placeholder',
          defaultValue: '请上传您的头像',
        }),
      });
      return;
    }
    setLoadingBtn(true);
    dispatch({
      type: 'userManagement/saveOrUpdateUserProfilePicture',
      payload: avatarSettings,
      callback: response => {
        setLoadingBtn(false);
        if (response.code === 200) {
          notification.success({
            message: getIntl().formatMessage({
              id: 'customer.ext.info.save.success',
            }),
          });
          fetchAvatarSettings(userId);
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  const handleDeleteSetting = i => {
    //有id走删除接口
    if (avatarSettings[i].pictureId) {
      dispatch({
        type: 'userManagement/queryChannelPictureRemove',
        payload: { pictureId: avatarSettings[i].pictureId },
        callback: response => {
          if (response.code === 200) {
            let temp = [...avatarSettings];
            temp.splice(i, 1);
            setInputValues(prev => {
              const newInputValues = { ...prev };
              delete newInputValues[i];
              return newInputValues;
            });
            setAvatarSettings(temp);
          } else {
            notification.error({
              message: response.msg,
            });
          }
        },
      });
    } else {
      let temp = [...avatarSettings];
      temp.splice(i, 1);
      setInputValues(prev => {
        const newInputValues = { ...prev };
        delete newInputValues[i];
        return newInputValues;
      });
      setAvatarSettings(temp);
    }
  };

  const handleCancel = () => {
    history.goBack();
  };
  // 检查是否有重复的渠道选择
  const isChannelDuplicate = (channelId, currentIndex) => {
    return avatarSettings.some((setting, idx) => {
      // 跳过当前正在编辑的项
      if (idx === currentIndex) {
        return false;
      }
      // 检查其他设置项中是否已包含该渠道
      return setting.channelList && setting.channelList.includes(channelId);
    });
  };

  // 过滤掉重复的渠道
  const handleChannelSelect = (value, index) => {
    const filteredValues = value.filter(channelId => {
      if (isChannelDuplicate(channelId, index)) {
        // 如果发现重复渠道，显示提示信息
        notification.warning({
          message: getIntl().formatMessage({
            id: 'user.management.operation.add.avator.setting.duplicate.tips',
            defaultValue: '该渠道已被其他头像设置规则选中！',
          }),
        });
        return false;
      }
      return true;
    });
    console.log(filteredValues, 'filteredValues');

    let temp = [...avatarSettings];
    temp[index].channelList = filteredValues;
    setAvatarSettings(temp);
  };

  const renderAvatarSettingItem = (setting, index) => {
    return (
      <div className={styles.settingItem} key={setting.id}>
        <div className={styles.settingContainer}>
          <div className={styles.settingHeader}>
            <h3 className={styles.settingTitle}>
              {getIntl().formatMessage({
                id: 'user.management.operation.add.avator.setting',
                defaultValue: '个性化设置',
              })}
              {index + 1}
            </h3>
            {index > 0 && (
              <Popconfirm
                title={getIntl().formatMessage({
                  id:
                    'user.management.operation.add.avator.setting.delete.confirm',
                  defaultValue: '确定要删除这个个性化头像设置吗？',
                })}
                onConfirm={() => handleDeleteSetting(index)}
              >
                <Button
                  type="primary"
                  danger
                  icon={<DeleteHome />}
                  className={styles.deleteBtn}
                >
                  <FormattedMessage id="document.knowledge.base.table.operation.delete" />
                </Button>
              </Popconfirm>
            )}
          </div>

          <div className={styles.settingContent}>
            <div className={styles.channelSection}>
              <div className={styles.channelLabel}>
                {getIntl().formatMessage({
                  id: 'customerInformation.contactCustomer.channel.placeholder',
                  defaultValue: '请选择渠道',
                })}
              </div>

              <ChannelMultipleSelect
                value={setting.channelList}
                onChange={value => handleChannelSelect(value, index)}
                channelOptions={channelOptions}
                placeholder={getIntl().formatMessage({
                  id: 'feedbackPerformance.card.5.select.placeholder',
                  defaultValue: '请选择渠道',
                })}
              />
            </div>

            <div className={styles.nicknameSection}>
              <div className={styles.nicknameLabel}>
                {getIntl().formatMessage({
                  id: 'user.management.operation.add.avator.nickname',
                  defaultValue: '您的昵称',
                })}
              </div>
              <Input
                placeholder={getIntl().formatMessage({
                  id:
                    'user.management.operation.add.avator.nickname.placeholder',
                  defaultValue: '请输入您的昵称',
                })}
                maxLength={80}
                value={
                  inputValues[index] !== undefined
                    ? inputValues[index]
                    : setting.nickName
                }
                onChange={e => handleNicknameChange(e, index)}
              />
            </div>

            <div className={styles.avatarSection}>
              <div className={styles.avatarLabel}>
                {getIntl().formatMessage({
                  id: 'user.management.operation.add.avator.avatar',
                  defaultValue: '您的头像',
                })}
              </div>
              <div className={styles.avatarPreview}>
                {setting.profilePicturePath && (
                  <img
                    src={setting.profilePicturePath}
                    alt="头像"
                    className={styles.avatarImage}
                  />
                )}
                <div className={styles.avatarPlaceholder}>
                  <Upload
                    name="avatar"
                    listType="picture-card"
                    className="avatar-uploader"
                    showUploadList={false}
                    {...uploadProperties}
                    customRequest={value => customRequest(value, index)}
                  >
                    {
                      <span className={styles.placeholderIcon}>
                        <PlusOutlined />
                      </span>
                    }
                  </Upload>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };
  // 上传
  const uploadProperties = {
    name: 'file',
    multiple: false,
    maxCount: 1,
    accept: '.jpg,.png,.jpeg',

    // // 上传前的校验
    beforeUpload: file => {
      console.log(file);
      let { size } = file;
      let fileSize = size / 1024 / 1024;
      if (fileSize > 10) {
        notification.error({
          message: getIntl().formatMessage(
            {
              id: 'message.upload.size',
            },
            {
              fileSize: 10,
            },
          ),
        });
        return Upload.LIST_IGNORE;
      }
      return true;
    },

    onDrop(e) {
      console.log('Dropped files', e.dataTransfer.files);
    },
  };
  // 自定义的上传接口
  const customRequest = ({ file, onSuccess }, index) => {
    const fmData = new FormData();
    fmData.append('file', file);
    setLoadingBtn(true);
    dispatch({
      type: 'worktable/queryUploadPicture',
      payload: fmData,
      callback: response => {
        setLoadingBtn(false);
        if (200 === response.code) {
          let temp = [...avatarSettings];
          temp[index].profilePicturePath = response.data.url;
          setAvatarSettings(temp);
          onSuccess(response.data, file);
        }
      },
    });
  };
  // 添加设置
  const handleAddSetting = () => {
    const newIndex = avatarSettings.length;
    setAvatarSettings([
      ...avatarSettings,
      {
        pictureId: '', //配置id
        userId: userId, //用户id
        nickName: '', //用户昵称
        profilePicturePath: '', //用户头像路径
        channelList: [], //渠道信息
      },
    ]);

    // 确保新添加的设置有初始化的输入值
    setInputValues(prev => ({
      ...prev,
      [newIndex]: '',
    }));
  };

  // 滚动到底部
  const scrollToBottom = () => {
    setTimeout(() => {
      const container = document.getElementById('avatarSettingContainer');
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    }, 100);
  };

  // 添加设置后滚动到底部
  useEffect(() => {
    if (avatarSettings.length > 0) {
      scrollToBottom();
    }
  }, [avatarSettings.length]);

  // 处理昵称输入变化
  const handleNicknameChange = (e, index) => {
    const value = e.target.value;

    // 更新本地输入状态
    setInputValues(prev => ({
      ...prev,
      [index]: value,
    }));

    // 使用防抖更新真正的状态
    updateNicknameDebounced(value, index);
  };

  // 防抖函数，延迟更新 avatarSettings 状态
  const updateNicknameDebounced = useDebounce((value, index) => {
    let temp = [...avatarSettings];
    temp[index].nickName = value;
    setAvatarSettings(temp);
  }, 300);

  // 组件主体的返回部分
  return (
    <div className={styles.avatarSettingContainer}>
      <div className={styles.pageHeader}>
        <div className={styles.titleBar}>
          <div className={styles.titleIndicator}></div>
          <h1 className={styles.pageTitle}>
            <FormattedMessage
              id="user.management.operation.add.avator.setting.title"
              defaultMessage="添加个性化头像"
            />
          </h1>
        </div>
      </div>

      <Spin spinning={loading}>
        <div className={styles.content}>
          <Steps
            progressDot
            current={0}
            direction="vertical"
            items={[
              {
                title: '',
                description: (
                  <div
                    // key={uuidv4()}
                    id="avatarSettingContainer"
                    style={{
                      marginLeft: 20,
                      marginTop: 10,
                      maxHeight: 'calc(100vh - 320px)',
                      overflowY: 'scroll',
                    }}
                  >
                    {avatarSettings?.map((setting, index) =>
                      renderAvatarSettingItem(setting, index),
                    )}
                  </div>
                ),
              },
              // 为实现ui设计需要留下
              {
                title: null,
                description: (
                  <Button
                    icon={
                      <PlusOutlined
                        style={{ fontSize: '12px', marginRight: 10 }}
                      />
                    }
                    ghost
                    onClick={() => handleAddSetting()}
                  >
                    <FormattedMessage
                      id="user.management.operation.add.avator.setting.btn"
                      defaultMessage="个性化头像设置"
                    />
                  </Button>
                ),
              },
            ]}
          />

          <div className={styles.actionButtons}>
            <Button onClick={handleCancel}>
              <FormattedMessage
                id="awsAccountSetting.cancel.btn"
                defaultMessage="取消"
              />
            </Button>
            <Button
              type="primary"
              onClick={() => handleAddOrUpdateAvatarSetting()}
              loading={loadingBtn}
            >
              <FormattedMessage
                id="knowledge.QA.tabs.3.list.btn.tips4"
                defaultMessage="保存"
              />
            </Button>
          </div>
        </div>
      </Spin>
    </div>
  );
};
