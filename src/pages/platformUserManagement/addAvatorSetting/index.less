/* 新增样式 - 个性化头像设置页面 */
.avatarSettingContainer {
  padding: 20px;
  background: #fff;
  height: 100%;
  margin: 20px;

  .pageHeader {
    margin-bottom: 20px;

    .titleBar {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }

    .titleIndicator {
      width: 4px;
      height: 28px;
      background-color: #3463fc;
      margin-right: 8px;
    }

    .pageTitle {
      margin: 0;
      font-size: 18px;
      font-weight: 400;
      color: #333333;
    }
  }

  .content {
    display: flex;
    flex-direction: column;
    gap: 20px;

    :global {
      .ant-btn.ant-btn-background-ghost {
        border: 1px solid #3463fc;
        color: #3463fc;
        margin-top: 20px;
        margin-left: 20px;
      }

      .ant-steps-item-title {
        color: rgba(0, 0, 0, 0.88) !important;
        font-family: 'Poppins', sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 700;
        line-height: 14px;
        /* 100% */
      }

      .ant-steps-item-container > .ant-steps-item-tail::after {
        background-color: #ad30e5;
      }

      .ant-steps-item-wait
        .ant-steps-item-icon
        > .ant-steps-icon
        .ant-steps-icon-dot {
        display: none;
      }

      .ant-steps-item-container > .ant-steps-item-tail::after {
        background: linear-gradient(180deg, #ad30e5, #fff) !important;
      }

      .ant-steps-icon-dot {
        background-color: rgb(24, 144, 255) !important;
      }

      .ant-select:not(.ant-select-disabled):hover .ant-select-selector {
        border: 1px solid #3463fc !important;
        box-shadow: none !important;
      }

      .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input)
        .ant-select-selector {
        border: 1px solid #3463fc !important;
        box-shadow: none !important;
      }

      .ant-select-selection-placeholder {
        font-size: 12px !important;
      }

      .ant-tag {
        margin-right: 3px;
        font-size: 12px;
        border: 1px solid #3463fc;
        background: #3463fc1a;
        border-radius: 4px;
        color: #3463fc;

        svg path {
          fill: #3463fc;
        }
      }

      .ant-input {
        font-size: 12px !important;
        border-radius: 6px !important;
        border: 1px solid #e6e6e6 !important;
      }

      .ant-input:focus,
      .ant-input-focused {
        box-shadow: none;
        border: 1px solid #3463fc !important;
      }

      .ant-select-single {
        .ant-select-selection-item img {
          width: 16px;
          height: 16px;
          margin-right: 3px;
          margin-top: -2px;
        }

        .ant-select-selector {
          font-size: 12px !important;
          border-radius: 6px !important;
          border: 1px solid #e6e6e6 !important;
        }
      }

      .ant-select-multiple {
        .ant-select-selection-item {
          border: 1px solid #3463fc;
          background: #3463fc1a;
          border-radius: 4px;
          color: #3463fc;
        }

        .ant-select-selection-item-remove {
          svg path {
            fill: #3463fc;
          }
        }

        .ant-select-selector {
          font-size: 12px !important;
          border-radius: 6px !important;
          border: 1px solid #e6e6e6 !important;
        }
      }

      .ant-btn-dangerous.ant-btn-primary {
        color: #fff;
        border-color: #ff4d4f !important;
        background: #ff4d4f !important;

        svg {
          width: 16px;
          height: 16px;
        }

        svg path {
          fill: #fff;
        }
      }

      .ant-upload.ant-upload-select-picture-card {
        width: 100%;
        height: 100%;
        background: none;
        border: none;
        margin: 0;
      }
    }
  }

  .settingItem {
    width: 100%;
    margin-bottom: 20px;
  }

  .settingContainer {
    width: 70%;
    background-color: #f9f9f9;
    border-radius: 6px;
    padding: 20px;
    box-shadow: 0px 2px 30px 0px rgba(0, 0, 0, 0.02);
  }

  .settingHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
  }

  .settingTitle {
    font-size: 12px;
    font-weight: 700;
    color: #333333;
    margin: 0;
  }

  .deleteBtn {
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 6px;
    height: auto;
  }

  .settingContent {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .avatarSection,
  .nicknameSection,
  .channelSection {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .avatarLabel,
  .nicknameLabel,
  .channelLabel {
    font-size: 12px;
    color: #333333;
  }

  .avatarPreview {
    width: 100%;
    height: 60px;
    display: flex;
  }

  .avatarImage {
    width: 60px;
    height: 60px;
    object-fit: cover;
    margin-right: 20px;
    border-radius: 50%;
  }

  .avatarPlaceholder {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    border: 1px solid #e6e6e6;

    overflow: hidden;
    background-color: #fff;
  }

  .placeholderIcon {
    color: #e6e6e6;
    font-size: 34px;
    cursor: pointer;
  }

  .nicknameValue,
  .channelValue {
    width: 100%;
    height: 32px;
    border-radius: 6px;
    border: 1px solid #e6e6e6;
    padding: 6px 12px;
    font-size: 12px;
    color: #333333;
    background-color: #fff;
    line-height: 20px;
  }

  .formCard {
    width: 100%;
    background-color: #f9f9f9;
    border-radius: 6px;
    padding: 20px;
    box-shadow: 0px 2px 30px 0px rgba(0, 0, 0, 0.02);
  }

  .formHeader {
    margin-bottom: 15px;
  }

  .formTitle {
    font-size: 12px;
    font-weight: 700;
    color: #333333;
    margin: 0;
  }

  .formContent {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .avatarUpload {
    width: 60px;
    height: 60px;
    margin-bottom: 10px;

    :global {
      .ant-upload {
        width: 60px !important;
        height: 60px !important;
        border-radius: 4px !important;
      }
    }
  }

  .uploadButton {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
  }

  .channelSelector {
    width: 100%;
  }

  .selectedTags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 8px;
  }

  .channelTag {
    background: rgba(52, 99, 252, 0.1);
    border: 1px solid rgba(52, 99, 252, 0.5);
    color: #3463fc;
    margin-right: 0;
  }

  .actionButtons {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 20px;

    :global {
      .ant-btn-primary {
        background-color: #3463fc;
      }
    }
  }
}
