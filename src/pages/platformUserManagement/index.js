import React, {
  Component,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  connect,
  useDispatch,
  useSelector,
  getIntl,
  FormattedMessage,
  getLocale,
  history,
} from 'umi';
import {
  Input,
  Button,
  Select,
  Table,
  Modal,
  Form,
  Radio,
  Popconfirm,
  TreeSelect,
  Tooltip,
  Tree,
  Tabs,
  Popover,
  Dropdown,
  Menu,
  Row,
  Col,
  Switch,
  Spin,
  Tag,
} from 'antd';
import {
  MoreOutlined,
  EditOutlined,
  MinusSquareOutlined,
  CheckOutlined,
  CloseOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { ReactComponent as DeleteHome } from '@/assets/deleteHome.svg';
import HOCAuth from '@/components/HOCAuth/index';
import useAuthAccess from '@/utils/authAccess';

import styles from './index.less';
import { notification, convertToChineseNumber } from '@/utils/utils';

export default () => {
  const dispatch = useDispatch();
  const { connectList, deptList, user, authAccess } = useSelector(
    ({ layouts }) => ({
      connectList: layouts.connectList,
      deptList: layouts.deptList,
      user: layouts.user,
      authAccess: layouts.auth,
    }),
  );
  const { TextArea } = Input;
  const formRef = useRef(null);
  const formRefDept = useRef(null);
  const formRefDeptUpdate = useRef(null);
  const formRefInvite = useRef(null);

  let [isModalOpen, setIsModalOpen] = useState(false);
  let [openSelfChat, setOpenSelfChat] = useState(null);
  let [isAddDeptOpen, setIsAddDeptOpen] = useState(false);
  let [isUpdateDeptOpen, setIsUpdateDeptOpen] = useState(false);
  let [radioValue, setRadioValue] = useState(1);
  let [deptPrinciple, setDeptPrinciple] = useState(0);
  let [userName, setUserName] = useState('');
  let [userType, setUserType] = useState(1);
  let [rows, setRows] = useState([]);
  let [total, setTotal] = useState(0);
  let [pageNum, setPageNum] = useState(1);
  let [pageSize, setPageSize] = useState(10);
  let [isSave, setIsSave] = useState(true);
  let [isSaveDept, setIsSaveDept] = useState(true);
  let [treeData, setTreeData] = useState([]);
  let [expandedKeys, setExpandedKeys] = useState([]);
  let [userNotInDeptArr, setUserNotInDeptArr] = useState([]);
  let [updateUserId, setUpdateUserId] = useState('');
  let [updateDeptId, setUpdateDeptId] = useState('');
  let [externalParam, setExternalParam] = useState('');
  let [visible, setVisible] = useState(true);
  let [status, setStatus] = useState(null);
  let [inviteModal, setInviteModal] = useState(false);
  let [selectedRowKeys, setSelectedRowKeys] = useState([]);
  let [selectedRows, setSelectedRows] = useState([]);
  let [deptId, setDeptId] = useState('');
  let [deptLevel, setDeptLevel] = useState('');
  let [inputValue, setInputValue] = useState('');
  let [tagList, setTagList] = useState([]);
  let [treeNode, setTreeNode] = useState(null);
  let [userLoading, setUserLoading] = useState(false);
  let [hasDept, setHasDept] = useState(false);

  let [scrollTop, setScrollTop] = useState(0);
  const [contactLinesList, setContactLinesList] = useState([
    {
      id: Date.now(),
      connectId: '',
      userCreateType: 1,
      connectUserId: '',
      connectUserName: '',
      newConnectUserList: [],
    },
  ]);
  const [visibleMap, setVisibleMap] = useState({});
  // 添加按钮  加载
  const [loadings, setLoadings] = useState([]);

  // table的loading状态
  const [loading, setLoading] = useState(false);
  const [switchValue, setSwitchValue] = useState(true);

  // 构造connect list  构造成 {value: id, label: value}
  let newConnectList = useMemo(
    () =>
      connectList.map(connect => ({
        value: JSON.parse(connect.value).connectId,
        label: connect.label,
        identityManagementType: JSON.parse(connect.value)
          .identityManagementType,
      })),
    [connectList],
  );

  useEffect(() => {
    let a = null;
    if (user.openSelfChat) {
      console.log(user.openSelfChat);
      a = user.openSelfChat;
    } else {
      console.log(JSON.parse(sessionStorage.getItem('user')).openSelfChat);
      a = JSON.parse(sessionStorage.getItem('user')).openSelfChat;
    }
    setOpenSelfChat(a);
    queryUserList();
    queryDeptList();
  }, []);
  useEffect(() => {
    console.log(contactLinesList, 'contactLinesList已经更新了===');
    // 在这里执行其他操作
  }, [contactLinesList]);
  useEffect(() => {
    let treeNode1 = renderTreeNodes(treeData);
    console.log(treeNode1);
    setTreeNode(treeNode1);
  }, [treeData]);
  // 页面输入框、下拉选项、page信息改变 进行查询
  useEffect(() => {
    queryUserList();
  }, [
    deptId,
    deptLevel,
    status,
    userName,
    userType,
    pageNum,
    pageSize,
    isModalOpen,
  ]);

  // 页面输入框、下拉选项、page信息改变 进行查询
  useEffect(() => {
    userNotInDept();
  }, [isAddDeptOpen]);

  // 创建用户 展示弹窗
  const showModal = () => {
    // setIsModalOpen(true);
    // setIsSave(true);

    // // 清空页面信息（初始化）
    // formRef.current?.setFieldsValue({
    //   email: '',
    //   userName: '',
    //   lastName: '',
    //   phonenumber: '',
    //   deptId: [],
    //   deptPrinciple: 0,
    //   password: '',
    //   workNumber: '',
    //   userType: 1,
    //   connectList: [],
    // });

    // setRadioValue(1);
    // setContactLinesList([]);

    history.push(`/platformUserManagement/createUser?isEdit=${false}`);
  };
  //添加组织
  const showModalAdd = () => {
    // setIsAddDeptOpen(true);
    // setIsSaveDept(true);
    // // 清空页面信息（初始化）
    // formRefDept.current?.setFieldsValue({
    //   deptName: '',
    //   code: '',
    //   deptLevel: '',
    //   parentId: '',
    //   userName: '',
    // });
    history.push(
      `/platformUserManagement/createOrganization?isEdit=${false}&hasDept=${hasDept}`,
    );
  };
  const handleCancel = useCallback(() => {
    setIsModalOpen(false);
  }, [isModalOpen]);
  const handleCancelAdd = useCallback(() => {
    setIsAddDeptOpen(false);
  }, [isAddDeptOpen]);
  const handleCancelUpdate = useCallback(() => {
    setIsUpdateDeptOpen(false);
  }, [isUpdateDeptOpen]);
  const handleInviteModal = useCallback(() => {
    setInviteModal(false);
  }, [inviteModal]);

  const onChange = useCallback(
    e => {
      setRadioValue(e.target.value);
      if (e.target.value === 3) {
        setContactLinesList([
          {
            id: Date.now(),
            connectId: [],
            userCreateType: 1,
            connectUserId: '',
            connectUserName: '',
            newConnectUserList: [],
          },
        ]);
      } else {
        setContactLinesList([]);
      }
    },
    [radioValue],
  );
  const onChangeCheck = useCallback(
    e => {
      setDeptPrinciple(e.target.value);
    },
    [deptPrinciple],
  );
  const onChangeCreateType = useCallback(
    (e, index) => {
      const data = [...contactLinesList];
      data[index].userCreateType = e.target.value;
      if (e.target.value === 1) {
        data[index].connectUserId = '';
        data[index].connectUserName = '';
      } else {
        data[index].connectUserId = [];
      }
      setContactLinesList(data);
    },
    [contactLinesList],
  );
  const onChangeConnectUser = useCallback(
    (e, option, index) => {
      console.log(e, option);
      const data = [...contactLinesList];
      data[index].connectUserId = option.value;
      data[index].connectUserName = option.label;
      setContactLinesList(data);
    },
    [contactLinesList],
  );
  /**
   * 返回connectid对应的绑定用户列表
   */
  const getConnectUserInfoUpdate = useCallback(
    (contactlinesList, useId) => {
      setUserLoading(true);
      console.log(contactlinesList);
      contactlinesList?.forEach(item => {
        dispatch({
          type: 'userManagement/getConnectUserInfo',
          payload: { connectId: item.connectId, userId: useId },
          callback: response => {
            let { code, data, msg } = response;
            if (200 === code) {
              const a = data?.map(connect => ({
                value: connect.connectUserId,
                label: connect.userName,
              }));
              item.newConnectUserList = a;
            }
            setUserLoading(false);
          },
        });
      });
      console.log(contactlinesList);

      setContactLinesList([...contactlinesList]);
    },
    [contactLinesList],
  );
  // 修改信息 展示弹窗
  const updateUserShow = userId => {
    // let openSelfChat1 = JSON.parse(sessionStorage.getItem('user')).openSelfChat;
    // setIsSave(false);
    // setIsModalOpen(true);
    // setUpdateUserId(userId);
    // dispatch({
    //   type: 'userManagement/queryUserById',
    //   payload: userId,
    //   callback: response => {
    //     let { code, data, msg } = response;
    //     if (200 === code) {
    //       // console.log(data)
    //       // 回显数据
    //       let userType = data.roleList?.map(role => role.roleId)?.[0];
    //       console.log(userType);
    //       let deptPrinciple = 0;
    //       if (userType === '1001') {
    //         userType = 1;
    //       } else if (userType === '1002') {
    //         userType = 2;
    //       } else if (userType === '1003') {
    //         userType = 3;
    //       } else if (userType === '1004') {
    //         userType = 2;
    //         deptPrinciple = 1;
    //       } else if (userType === '1005') {
    //         userType = 3;
    //         deptPrinciple = 1;
    //       }
    //       console.log(data);
    //       // 展示 取消 connect list
    //       setRadioValue(userType);
    //       setDeptPrinciple(deptPrinciple);
    //       let contactlinesList = data?.connectList?.map((item, index) => {
    //         return {
    //           id: Date.now(),
    //           connectId: item.connectId,
    //           userCreateType: item.connectUserId ? 2 : 1,
    //           connectUserId: item.connectUserId,
    //           connectUserName: item.connectUserName,
    //           newConnectUserList: [],
    //           identityManagementType: item.identityManagementType,
    //         };
    //       });
    //       if (userType === 3 && openSelfChat1 !== 1) {
    //         getConnectUserInfoUpdate(contactlinesList, userId);
    //       }

    //       // 回显数据
    //       formRef.current.setFieldsValue({
    //         ...data,
    //         userType,
    //         deptPrinciple,
    //         connectList: data.connectList?.map(connect => connect.connectId),
    //       });
    //     }
    //   },
    // });

    // history.push({
    //   pathname: '/platformUserManagement/createUser',
    //   query: {
    //     isEdit: 1,
    //   },
    // });
    history.push(
      `/platformUserManagement/createUser?isEdit=${true}&id=${userId}`,
    );
  };
  // 编辑组织 展示弹窗
  const updateHandle = nowValue => {
    // console.log(nowValue);
    // setIsSaveDept(false);
    // setIsAddDeptOpen(true);
    // setExternalParam(nowValue.level);
    // dispatch({
    //   type: 'userManagement/getDeptInfo',
    //   payload: nowValue.value,
    //   callback: response => {
    //     let { code, data, msg } = response;
    //     if (200 === code) {
    //       // 回显数据
    //       formRefDept.current.setFieldsValue({
    //         ...data,
    //       });
    //       setUpdateDeptId(data.deptId);
    //     }
    //   },
    // });

    history.push(
      `/platformUserManagement/createOrganization?isEdit=${true}&id=${
        nowValue.value
      }`,
    );
  };
  //添加个性化头像
  const addAvatorSetting = userId => {
    history.push(`/platformUserManagement/addAvatorSetting?userId=${userId}`);
  };
  /**
   * 添加/更新 用户信息
   * @type {(function(*=): void)|*}
   */
  const onFinish = useCallback(
    values => {
      let payload = values;
      //判空，判重
      let flag = true;
      let connectIdList = [];
      contactLinesList?.forEach(item => {
        if (
          (!item.connectId || item.connectId?.length == 0) &&
          openSelfChat !== 1
        ) {
          notification.error({
            message: getIntl().formatMessage({
              id: 'user.management.contact.selsect.please',
            }),
          });
          flag = false;
        } else if (
          item.userCreateType === 2 &&
          (item.connectUserId?.length == 0 || !item.connectUserId)
        ) {
          notification.error({
            message: getIntl().formatMessage({
              id: 'user.management.contact.selsect.user.please',
            }),
          });
          flag = false;
        } else if (
          connectIdList.length > 0 &&
          connectIdList.includes(item.connectId)
        ) {
          notification.error({
            message: getIntl().formatMessage({
              id: 'user.management.contact.selsect.connectLines.please',
            }),
          });
          flag = false;
        }
        connectIdList.push(item.connectId);
      });
      //传入contactLinesList
      if (flag == true) {
        payload.connectUserInfoVos = contactLinesList?.map(item => {
          if (item.connectId?.length == 0 || item.connectUserId?.length == 0) {
            return {
              ...item,
              connectId: item.connectId?.length == 0 ? '' : item.connectId,
              connectUserId:
                item.connectUserId?.length == 0 ? '' : item.connectUserId,
            };
          } else {
            return item;
          }
        });
      } else {
        return;
      }

      // 按钮加载状态
      enterLoading(0);
      if (isSave) {
        dispatch({
          type: 'userManagement/createUser',
          payload: payload,
          callback: response => {
            let { code, msg } = response;
            if (200 === code) {
              notification.success({
                message: getIntl().formatMessage({
                  id: 'user.management.create.success',
                }),
              });
              // 关闭弹窗
              setIsModalOpen(false);
            } else {
              notification.error({
                message: msg,
              });
            }

            // 取消加载
            existLoading(0);
          },
        });
      } else {
        values.userId = updateUserId;
        dispatch({
          type: 'userManagement/updateUser',
          payload: payload,
          callback: response => {
            let { code, msg } = response;
            if (200 === code) {
              notification.success({
                message: getIntl().formatMessage({
                  id: 'user.management.update.success',
                }),
              });
              setUpdateUserId('');
              // 关闭弹窗
              setIsModalOpen(false);
            } else {
              notification.error({
                message: msg,
              });
            }

            // 取消加载
            existLoading(0);
          },
        });
      }
    },
    [isSave, updateUserId, contactLinesList],
  );
  /**
   * 批量更改组织
   */
  const onFinishDeptUpdate = useCallback(value => {
    const formData = {
      userIdList: selectedRowKeys,
      deptId: value.deptId, // 表单数据
    };
    if (formData.userIdList.length === 0) {
      notification.error({
        message: getIntl().formatMessage({
          id: 'user.management.batch.confirmation.ids.not.null',
        }),
      });
      return;
    }
    dispatch({
      type: 'userManagement/batchUpdateDept',
      payload: formData,
      callback: response => {
        let { code, msg } = response;
        if (200 === code) {
          queryUserList();
          notification.success({
            message: getIntl().formatMessage({
              id: 'user.management.operation.success',
            }),
          });
          // 关闭弹窗
          setIsUpdateDeptOpen(false);
        } else {
          notification.error({
            message: msg,
          });
        }

        // 取消加载
        existLoading(0);
      },
    });
  });
  /**
   * 邀请注册
   */
  const formRefInviteUpdate = () => {
    console.log(tagList, deptId, switchValue, deptLevel);
    const data = {
      emailList: tagList, // 邮箱list
      deptId: deptId, // 部门id
      approval: switchValue, // 是否开启审核
    };
    if (!deptId || (deptId && deptLevel !== 3)) {
      notification.error({
        message: getIntl().formatMessage({
          id: 'user.management.invite.fail.msg',
        }),
      });
      return;
    }
    enterLoading(1);
    dispatch({
      type: 'userManagement/sendInvitation',
      payload: data,
      callback: response => {
        let { code, msg } = response;
        if (200 === code) {
          notification.success({
            message: getIntl().formatMessage({
              id: 'user.management.invite.success',
            }),
          });
        } else {
          notification.error({
            message: msg,
          });
        }
        handleInviteModal();
        existLoading(1);
      },
    });
  };
  /**
   * 设为组长
   */
  const handleSetLeader = useCallback(values => {
    const formData = {
      userId: values.userId, // userId
      // roleId: values.roleList[0].roleId, // roleId
      deptId: values.deptId, // 部门id
    };
    dispatch({
      type: 'userManagement/setDeptPrinciple',
      payload: formData,
      callback: response => {
        let { code, msg } = response;
        if (200 === code) {
          queryUserList();
          notification.success({
            message: getIntl().formatMessage({
              id: 'user.management.operation.success',
            }),
          });
        } else {
          notification.error({
            message: msg,
          });
        }

        // 取消加载
        existLoading(0);
      },
    });
  });
  /**
   * 批量确认
   */
  const batchConfirmation = useCallback(() => {
    /*const userInfoList = selectedRows.map(item => {
      return {
        userId: item.userId,
        email: item.email,
      };
    });
    console.log(selectedRows, userInfoList);
    const formData = {
      userInfoList: userInfoList,
    };*/
    const formData = selectedRows.map(({ userId }) => userId);
    if (formData.length === 0) {
      notification.error({
        message: getIntl().formatMessage({
          id: 'user.management.batch.confirmation.ids.not.null',
        }),
      });
      return;
    }

    dispatch({
      type: 'userManagement/batchConfirm',
      payload: formData,
      callback: response => {
        let { code, msg } = response;
        if (200 === code) {
          queryUserList();
          notification.success({
            message: getIntl().formatMessage({
              id: 'user.management.operation.success',
            }),
          });
        } else {
          notification.error({
            message: msg,
          });
        }

        // 取消加载
        existLoading(0);
      },
    });
  });
  /**
   * 添加/更新 用户信息
   * @type {(function(*=): void)|*}
   */
  const onFinishDept = useCallback(
    values => {
      // 按钮加载状态
      enterLoading(0);
      console.log(externalParam);
      if (isSaveDept) {
        const formData = {
          ...values, // 表单数据
          deptLevel: externalParam, // 表单外的参数
        };
        dispatch({
          type: 'userManagement/createDept',
          payload: formData,
          callback: response => {
            let { code, msg } = response;
            if (200 === code) {
              queryDeptList();
              notification.success({
                message: getIntl().formatMessage({
                  id: 'user.management.create.success',
                }),
              });
              // 关闭弹窗
              setIsAddDeptOpen(false);
            } else {
              notification.error({
                message: msg,
              });
            }

            // 取消加载
            existLoading(0);
          },
        });
      } else {
        values.deptId = updateDeptId;
        const formData = {
          ...values, // 表单数据
          deptLevel: externalParam, // 表单外的参数
        };
        dispatch({
          type: 'userManagement/updateDept',
          payload: formData,
          callback: response => {
            let { code, msg } = response;
            if (200 === code) {
              queryDeptList();
              notification.success({
                message: getIntl().formatMessage({
                  id: 'user.management.update.success',
                }),
              });
              setUpdateUserId('');
              // 关闭弹窗
              setIsAddDeptOpen(false);
            } else {
              notification.error({
                message: msg,
              });
            }

            // 取消加载
            existLoading(0);
          },
        });
      }
    },
    [isSaveDept, updateDeptId, externalParam, deptId, deptLevel],
  );

  /**
   * 按钮加载状态
   * @type {(function(*): void)|*}
   */
  const enterLoading = useCallback(
    index => {
      setLoadings(prevLoadings => {
        const newLoadings = [...prevLoadings];
        newLoadings[index] = true;
        return newLoadings;
      });
    },
    [loadings],
  );

  /**
   * 按钮取消加载状态
   * @type {(function(*): void)|*}
   */
  const existLoading = useCallback(
    index => {
      setLoadings(prevLoadings => {
        const newLoadings = [...prevLoadings];
        newLoadings[index] = false;
        return newLoadings;
      });
    },
    [loadings],
  );

  // 禁用
  const forbiddenUser = useCallback(
    userId => {
      // 自己不能禁用自己
      if (userId === user.userId) {
        notification.error({
          message: getIntl().formatMessage({
            id: 'user.management.forbidden.self.error',
          }),
        });
        return;
      }
      dispatch({
        type: 'userManagement/forbiddenUser',
        payload: {
          userId,
          status: 0,
        },
        callback: response => {
          let { code, msg } = response;
          if (200 === code) {
            queryUserList();
            notification.success({
              message: getIntl().formatMessage({
                id: 'user.management.forbidden.success',
              }),
            });
          } else {
            notification.error({
              message: msg,
            });
          }
        },
      });
    },
    [userName, pageNum, pageSize, deptId, deptLevel, status],
  );

  // 启用用户
  const enableUser = useCallback(
    userId => {
      console.log(userName, status, pageNum, pageSize, deptId, deptLevel);
      dispatch({
        type: 'userManagement/enableUser',
        payload: {
          userId,
          status: 1,
        },
        callback: response => {
          let { code, msg } = response;
          if (200 === code) {
            queryUserList();
            notification.success({
              message: getIntl().formatMessage({
                id: 'user.management.enable.success',
              }),
            });
          } else {
            notification.error({
              message: msg,
            });
          }
        },
      });
    },
    [userName, pageNum, pageSize, deptId, deptLevel, status],
  );

  // 审核通过
  const applyPass = useCallback(
    values => {
      dispatch({
        type: 'userManagement/applyPass',
        payload: {
          userId: values.userId,
          status: 1,
          email: values.email,
        },
        callback: response => {
          let { code, msg } = response;
          if (200 === code) {
            queryUserList();
            notification.success({
              message: getIntl().formatMessage({
                id: 'user.management.operation.success',
              }),
            });
          } else {
            notification.error({
              message: msg,
            });
          }
        },
      });
    },
    [status],
  );

  // 审核拒绝
  const applyRefuse = useCallback(values => {
    dispatch({
      type: 'userManagement/applyRefuse',
      payload: {
        userId: values.userId,
        status: 3,
        email: values.email,
      },
      callback: response => {
        let { code, msg } = response;
        if (200 === code) {
          queryUserList();
          notification.success({
            message: getIntl().formatMessage({
              id: 'user.management.operation.success',
            }),
          });
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  }, []);

  // 删除用户
  const removeUser = useCallback(
    userId => {
      // 自己不能删除自己
      console.log('--------deptLevel------------', deptLevel);
      console.log('--------deptId------------', deptId);
      if (userId === user.userId) {
        notification.error({
          message: getIntl().formatMessage({
            id: 'user.management.remove.self.error',
          }),
        });
        return;
      }
      dispatch({
        type: 'userManagement/removeUser',
        payload: userId,
        callback: response => {
          let { code, msg } = response;
          if (200 === code) {
            queryUserList();
            notification.success({
              message: getIntl().formatMessage({
                id: 'user.management.remove.success',
              }),
            });
          } else {
            notification.error({
              message: msg,
            });
          }
        },
      });
    },
    [userName, status, deptId, deptLevel],
  );

  const treeHandle = key => {
    setVisibleMap(prevVisibleMap => ({
      ...prevVisibleMap,
      [key]: true,
    }));
    console.log(visibleMap);
  };

  const handleClosePopover = key => {
    setVisibleMap(prevVisibleMap => ({
      ...prevVisibleMap,
      [key]: false,
    }));
  };

  /**
   * 获得组织对应level
   */
  const onChangeGetLevel = useCallback(
    (value, label, extra) => {
      // userNotInDept(value);
      setExternalParam(extra.triggerNode.props.level + 1);
      console.log(externalParam, value);
    },
    [externalParam],
  );
  /**
   * 邀请中的输入框
   */
  const handleInputChange = e => {
    setInputValue(e.target.value);
  };
  /**
   * 在邀请框中输入后按下回车生成标签
   */
  const handleInputKeyPress = e => {
    if (e.key === 'Enter' && inputValue.trim() !== '') {
      setTagList([...tagList, inputValue.trim()]);
      setInputValue('');
    }
  };
  /**
   * 生成tags标签
   */
  const renderTags = () => {
    return tagList.map((tag, index) => (
      // onClose={() => handleCloseTag(index)}
      <Tag closable color="processing" key={index}>
        {tag}
      </Tag>
    ));
  };
  /**
   * 删除标签
   */
  const handleCloseTag = index => {
    const newTagList = [...tagList];
    newTagList.splice(index, 1);
    setTagList(newTagList);
  };
  /**
   * 获取选择项
   */
  // const rowSelection = {
  //   onChange: (selectedRowKeys, selectedRows) => {
  //     setSelectedRows(selectedRows);
  //   },
  //   // 设置选中行的 key 值数组，可以通过该数组更新选中状态
  //   selectedRowKeys: selectedRows.map(row => row.key),
  // };
  const onSelectChange = (newSelectedRowKeys, selectedRows) => {
    console.log(selectedRows);
    setSelectedRows(selectedRows);
    setSelectedRowKeys(newSelectedRowKeys);
  };
  /**
   * 查询列表信息
   */
  const queryUserList = useCallback(
    values => {
      setLoading(true);
      dispatch({
        type: 'userManagement/queryUserList',
        payload: {
          userName,
          status,
          pageNum,
          pageSize,
          deptId,
          deptLevel,
        },
        callback: response => {
          let { code, data, msg } = response;
          if (200 === code) {
            // console.log(data)
            let { rows, total } = data;
            setRows(rows);
            setTotal(total);
          }
          setLoading(false);
        },
      });
    },
    [userName, status, pageNum, pageSize, deptId, deptLevel],
  );
  /**
   * 查询负责人
   */
  const userNotInDept = value => {
    console.log(isSaveDept, value);
    dispatch({
      type: 'userManagement/userNotInDept',
      // payload: isSaveDept ? null : value,
      callback: response => {
        let { code, data, msg } = response;
        if (200 === code) {
          // console.log(data)
          const a = data?.map(connect => ({
            value: connect.userId,
            label: connect.userName,
          }));
          setUserNotInDeptArr(a);
        }
      },
    });
  };
  /**
   * 添加联络线路
   */
  const addContactLines = () => {
    setTimeout(() => {
      setContactLinesList(prevData => {
        const data = [...prevData];
        data.push({
          id: Date.now(),
          connectId: [],
          userCreateType: 1,
          connectUserId: [],
          connectUserName: '',
          newConnectUserList: [],
        });
        return data;
      });
    }, 0);
  };
  /**
   * 删除联络线路
   */
  const deleteContactLines = index => {
    setTimeout(() => {
      console.log('删=====');
      setContactLinesList(prevData => {
        const newData = [...prevData]; // 进行浅拷贝
        newData.splice(index, 1); // 删除指定位置的元素
        return newData; // 返回新的数组
      });
    }, 0);
  };
  const getConnectUserInfoOld = (e, index) => {
    getConnectUserInfo(e, index);
  };
  /**
   * 根据渠道id查询用户列表
   */
  const getConnectUserInfo = (e, index, op) => {
    console.log(op);
    setUserLoading(true);
    dispatch({
      type: 'userManagement/getConnectUserInfo',
      payload: { connectId: e },
      callback: response => {
        let { code, data, msg } = response;
        if (200 === code) {
          // console.log(data)

          const a = data?.map(connect => ({
            value: connect.connectUserId,
            label: connect.userName,
          }));
          console.log('查=====');
          const data1 = [...contactLinesList];
          if (op.identityManagementType !== 'SAML') {
            data1[index].userCreateType = null;
            data1[index].connectUserId = '';
          } else {
            if (isSave) {
              data1[index].userCreateType = 1;
              data1[index].connectUserId = '';
              data1[index].connectUserName = '';
            } else {
              data1[index].userCreateType = 2;
              data1[index].connectUserId = [];
              data1[index].connectUserName = '';
            }
          }
          data1[index].connectId = e;
          data1[index].newConnectUserList = a;
          data1[index].identityManagementType = op.identityManagementType;
          setContactLinesList(data1);
        }
        setUserLoading(false);
      },
    });
  };

  /**
   * 查询部门信息
   */
  const queryDeptList = () => {
    setLoading(true);
    dispatch({
      type: 'userManagement/queryDeptList',
      payload: null,
      callback: response => {
        let { code, data, msg } = response;
        if (200 === code) {
          setTreeData(data);
          let hasDept = hasChildrenForLevel2(data);
          setHasDept(hasDept);
        }
        setLoading(false);
      },
    });
  };
  /**
   * 判断该账号下有无团队
   */
  const hasChildrenForLevel2 = data => {
    function traverse(nodes) {
      let hasChildren = false;
      for (let node of nodes) {
        if (node.level === 2) {
          if (node.children && node.children.length > 0) {
            hasChildren = true;
          }
        } else if (node.children && node.children.length > 0) {
          hasChildren = hasChildren || traverse(node.children);
        }
      }
      return hasChildren;
    }
    return traverse(data);
  };
  /**
   * 删除分组
   */
  const deteleHandle = deptId => {
    console.log(deptId);
    dispatch({
      type: 'userManagement/deleteDept',
      payload: deptId,
      callback: response => {
        let { code, msg } = response;
        if (200 === code) {
          queryDeptList();
          notification.success({
            message: getIntl().formatMessage({
              id: 'user.management.remove.success',
            }),
          });
        } else {
          notification.error({
            message: msg,
          });
        }
      },
    });
  };
  /**
   * 邀请
   */
  const inviteHandle = () => {
    setInviteModal(true);
  };
  /**
   *  是否开启审核
   */
  const onChangeSwitch = e => {
    console.log(e);
    setSwitchValue(e);
  };
  /**
   * 展示更多下的操作
   */
  const getDropdownMore = item => {
    return (
      <Menu>
        {item.status === 1 && (
          // <HOCAuth authKey={'disable_platform_users'}>
          //   {authAccess => (
          <Menu.Item key={0}>
            <div className={styles.operationTextDrop}>
              <Popconfirm
                title={getIntl().formatMessage({
                  id: 'user.management.notice.forbidden.user',
                })}
                okText={getIntl().formatMessage({
                  id: 'user.management.operation.btn.yes',
                })}
                cancelText={getIntl().formatMessage({
                  id: 'user.management.operation.btn.no',
                })}
                onConfirm={() => forbiddenUser(item.userId)}
                disabled={
                  !useAuthAccess().results({
                    auth: authAccess,
                    authKey: 'disable_platform_users',
                  })
                }
              >
                <a
                  className={
                    !useAuthAccess().results({
                      auth: authAccess,
                      authKey: 'disable_platform_users',
                    }) && 'disabledFont'
                  }
                >
                  <FormattedMessage id="user.management.operation.forbidden" />
                </a>
              </Popconfirm>
            </div>
          </Menu.Item>
          //   )}
          // </HOCAuth>
        )}
        {item.status === 0 && (
          <Menu.Item
            key={1}
            disabled={
              !useAuthAccess().results({
                auth: authAccess,
                authKey: 'disable_platform_users',
              })
            }
          >
            <div className={styles.operationTextDrop}>
              <Popconfirm
                title={getIntl().formatMessage({
                  id: 'user.management.notice.enable.user',
                })}
                okText={getIntl().formatMessage({
                  id: 'user.management.operation.btn.yes',
                })}
                cancelText={getIntl().formatMessage({
                  id: 'user.management.operation.btn.no',
                })}
                onConfirm={() => enableUser(item.userId)}
                disabled={
                  !useAuthAccess().results({
                    auth: authAccess,
                    authKey: 'disable_platform_users',
                  })
                }
              >
                <a
                  className={
                    !useAuthAccess().results({
                      auth: authAccess,
                      authKey: 'disable_platform_users',
                    }) && 'disabledFont'
                  }
                >
                  <FormattedMessage id="user.management.operation.enable" />
                </a>
              </Popconfirm>
            </div>
          </Menu.Item>
        )}
        {item.status === 2 && (
          <Menu.Item
            key={2}
            disabled={
              !useAuthAccess().results({
                auth: authAccess,
                authKey: 'review_platform_users',
              })
            }
          >
            <div
              onClick={() => applyPass(item)}
              className={
                !useAuthAccess().results({
                  auth: authAccess,
                  authKey: 'review_platform_users',
                }) && 'disabledFont'
              }
            >
              <FormattedMessage id="user.management.operation.applyY" />
            </div>
          </Menu.Item>
        )}
        {item.status === 2 && (
          <Menu.Item
            key={3}
            disabled={
              !useAuthAccess().results({
                auth: authAccess,
                authKey: 'review_platform_users',
              })
            }
          >
            <div
              onClick={() => applyRefuse(item)}
              className={
                !useAuthAccess().results({
                  auth: authAccess,
                  authKey: 'review_platform_users',
                }) && 'disabledFont'
              }
            >
              <FormattedMessage id="user.management.operation.applyN" />
            </div>
          </Menu.Item>
        )}
        <Menu.Item
          key={4}
          disabled={
            !useAuthAccess().results({
              auth: authAccess,
              authKey: 'delete_platform_users',
            })
          }
        >
          <div className={styles.operationTextDrop}>
            <Popconfirm
              title={getIntl().formatMessage({
                id: 'user.management.notice.remove.user',
              })}
              okText={getIntl().formatMessage({
                id: 'user.management.operation.btn.yes',
              })}
              cancelText={getIntl().formatMessage({
                id: 'user.management.operation.btn.no',
              })}
              onConfirm={() => removeUser(item.userId)}
              disabled={
                !useAuthAccess().results({
                  auth: authAccess,
                  authKey: 'delete_platform_users',
                })
              }
            >
              <a
                className={
                  !useAuthAccess().results({
                    auth: authAccess,
                    authKey: 'delete_platform_users',
                  }) && 'disabledFont'
                }
              >
                <FormattedMessage id="user.management.operation.remove" />
              </a>
            </Popconfirm>
          </div>
        </Menu.Item>
        {item.status === 1 &&
          item.roleList &&
          !['1004', '1005', '1001'].includes(item.roleList[0].roleId) && (
            <Menu.Item
              key={5}
              disabled={
                !useAuthAccess().results({
                  auth: authAccess,
                  authKey: 'set_platform_user_as_team_leader',
                })
              }
            >
              <div
                onClick={() => handleSetLeader(item)}
                className={
                  !useAuthAccess().results({
                    auth: authAccess,
                    authKey: 'set_platform_user_as_team_leader',
                  }) && 'disabledFont'
                }
              >
                <FormattedMessage id="user.management.operation.setLeader" />
              </div>
            </Menu.Item>
          )}
      </Menu>
    );
  };

  // 列表表头信息
  const columns = useMemo(
    () => [
      {
        title: getIntl().formatMessage({ id: 'user.management.user.name' }),
        dataIndex: 'userName',
        key: 'userName',
        fixed: 'left',
      },
      {
        title: getIntl().formatMessage({ id: 'user.management.user.type' }),
        dataIndex: 'roleNameConcat',
        key: 'roleNameConcat',
        // render: value => (
        //   <div>
        //     <span className={styles.userType}>
        //       {value
        //         ?.map(item => {
        //           if (item.roleId === '1001') {
        //             return getIntl().formatMessage({
        //               id: 'user.management.radio.user.1',
        //             });
        //           } else if (item.roleId === '1002') {
        //             return getIntl().formatMessage({
        //               id: 'user.management.radio.user.2',
        //             });
        //           } else if (item.roleId === '1003') {
        //             return getIntl().formatMessage({
        //               id: 'user.management.radio.user.3',
        //             });
        //           } else if (item.roleId === '1004') {
        //             return getIntl().formatMessage({
        //               id: 'user.management.radio.user.4',
        //             });
        //           } else if (item.roleId === '1005') {
        //             return getIntl().formatMessage({
        //               id: 'user.management.radio.user.5',
        //             });
        //           }
        //         })
        //         .join(',')}
        //     </span>
        //   </div>
        // ),
      },
      {
        title: getIntl().formatMessage({ id: 'user.management.user.deptName' }),
        dataIndex: 'deptName',
        key: 'deptName',
        width: 180,
      },
      {
        title: getIntl().formatMessage({ id: 'user.management.connect' }),
        dataIndex: 'connectList',
        key: 'connectList',
        width: 180,
        render: value => {
          let showValue = value
            ? value?.map(item => item.connectAlias).join(',')
            : '-';
          return (
            <div title={showValue} className={styles.connectList}>
              <span>{showValue}</span>
            </div>
          );
        },
      },
      {
        title: getIntl().formatMessage({
          id: 'user.management.user.validity.period',
        }),
        dataIndex: 'canUser',
        key: 'canUser',
        render: value => {
          return (
            <div style={{ color: value ? '#3463fc' : '#999' }}>
              <span>
                {value
                  ? getIntl().formatMessage({
                      id: 'create.work.order.radio.yes',
                    })
                  : getIntl().formatMessage({
                      id: 'create.work.order.radio.no',
                    })}
              </span>
            </div>
          );
        },
      },
      {
        title: getIntl().formatMessage({ id: 'user.management.user.status' }),
        dataIndex: 'status',
        key: 'status',
        render: value => {
          let status =
            value === 0
              ? getIntl().formatMessage({
                  id: 'user.management.operation.forbidden',
                })
              : value === 1
              ? getIntl().formatMessage({
                  id: 'user.management.operation.normal',
                })
              : value === 2
              ? getIntl().formatMessage({
                  id: 'user.management.operation.noApply',
                })
              : value === 3
              ? getIntl().formatMessage({
                  id: 'user.management.operation.applyN',
                })
              : '';
          return (
            <div>
              <span>{status}</span>
            </div>
          );
        },
      },
      {
        title: getIntl().formatMessage({ id: 'user.management.operation' }),
        dataIndex: 'address',
        key: 'address',
        fixed: 'right',
        width: 260,
        render: (value, data) => (
          <div className={styles.operationArray}>
            {/* 修改 */}
            <HOCAuth authKey={'modify_platform_users'}>
              {authAccess => (
                <div
                  onClick={() => updateUserShow(data.userId)}
                  className={`${styles.operationText} ${
                    authAccess ? 'disabled' : ''
                  }`}
                >
                  <EditOutlined style={{ color: '#1890ff', marginRight: 5 }} />
                  <a>
                    <FormattedMessage id="user.management.operation.update" />
                  </a>
                </div>
              )}
            </HOCAuth>
            {/* 添加个性化头像 */}
            <HOCAuth authKey={'add_personalized_avatar'}>
              {authAccess => (
                <div
                  onClick={() => addAvatorSetting(data.userId)}
                  className={`${styles.operationText} ${
                    authAccess ? 'disabled' : ''
                  }`}
                >
                  <PlusOutlined style={{ color: '#1890ff', marginRight: 5 }} />
                  <a>
                    <FormattedMessage id="user.management.operation.add.avator" />
                  </a>
                </div>
              )}
            </HOCAuth>
            {/* 更多 */}
            <Dropdown
              overlay={() => getDropdownMore(data)}
              placement="bottom"
              overlayClassName="settingDownMenuUserManagement"
            >
              <div className={styles.operationText}>
                <MinusSquareOutlined
                  style={{ color: '#1890ff', marginRight: 5 }}
                />
                <a>
                  <FormattedMessage id="user.management.btn.more" />
                </a>
              </div>
            </Dropdown>
          </div>
        ),
      },
    ],
    [userName, userType, status, deptId, deptLevel, deptPrinciple],
  );

  const tabsItems = [
    {
      key: null,
      label: getIntl().formatMessage({ id: 'user.management.tabs.user.1' }),
    },
    {
      key: '2',
      label: getIntl().formatMessage({ id: 'user.management.tabs.user.2' }),
    },
    {
      key: '1',
      label: getIntl().formatMessage({ id: 'user.management.tabs.user.3' }),
    },
    {
      key: '0',
      label: getIntl().formatMessage({ id: 'user.management.tabs.user.4' }),
    },
    {
      key: '3',
      label: getIntl().formatMessage({ id: 'user.management.tabs.user.5' }),
    },
  ];

  const popoverContent = nowValue => {
    return (
      <div style={{ display: 'flex', flexDirection: 'column' }}>
        <HOCAuth authKey={'modify_organization'}>
          {authAccess => (
            <div
              onClick={() => updateHandle(nowValue)}
              style={{ paddingBottom: 10, cursor: 'pointer' }}
              className={authAccess && 'disabledFont'}
            >
              <FormattedMessage id="user.option.edit" />
            </div>
          )}
        </HOCAuth>
        <HOCAuth authKey={'delete_organization'}>
          {authAccess => (
            <div
              onClick={() => deteleHandle(nowValue.value)}
              style={{ cursor: 'pointer' }}
              className={authAccess && 'disabledFont'}
            >
              <FormattedMessage id="user.option.delete" />
            </div>
          )}
        </HOCAuth>
      </div>
    );
  };

  const handleScroll = event => {
    const { scrollTop } = event.target;
    console.log(scrollTop);
    setScrollTop(scrollTop);
  };

  const renderTreeNodes = data =>
    data.map(item => {
      const key = item.value;
      // setDefaultExpandedKeys(prevExpandedKeys => [...prevExpandedKeys, key]);
      // console.log(defaultExpandedKeys);
      const visible = visibleMap[key] || false;
      if (item.children) {
        return (
          <Tree.TreeNode
            style={
              item.level === 1
                ? {
                    position: '-webkit-sticky',
                    position: 'sticky',
                    top: 0,
                    background: '#fff',
                    zIndex: 99,
                  }
                : {}
            }
            key={key}
            title={
              <div
                onClick={() => {
                  setDeptId(item.value);
                  setDeptLevel(item.level);
                  setPageNum(1);
                }}
              >
                <span>{item.title}</span>

                {item.level === 3 ? (
                  <HOCAuth
                    authKey={['modify_organization', 'delete_organization']}
                  >
                    {authAccess => (
                      <Popover
                        content={() => popoverContent(item)}
                        trigger="hover"
                        visible={visible}
                        onVisibleChange={newVisible =>
                          setVisibleMap({ ...visibleMap, [key]: newVisible })
                        }
                        placement="right"
                        onClose={() => handleClosePopover(key)}
                      >
                        <MoreOutlined
                          style={{ float: 'right', marginTop: 5 }}
                          onMouseEnter={() => treeHandle(key)}
                        />
                      </Popover>
                    )}
                  </HOCAuth>
                ) : (
                  ''
                )}
              </div>
            }
          >
            {renderTreeNodes(item.children)}
          </Tree.TreeNode>
        );
      }
      return (
        <Tree.TreeNode
          key={key}
          title={
            <div
              onClick={() => {
                setDeptId(item.value);
                setDeptLevel(item.level);
                setPageNum(1);
              }}
            >
              <span>{item.title}</span>
              <MoreOutlined
                style={{ float: 'right', marginTop: 5 }}
                onMouseEnter={treeHandle}
              />
            </div>
          }
        />
      );
    });
  return (
    <div className={styles.platformUserManagement}>
      <div className={styles.platformUserManagementLeft}>
        <div className={styles.platformUserManagementLeftTop}>
          <div className={styles.platformUserManagementLeftTopTitle}>
            <FormattedMessage id="user.management.tree.title" />
          </div>
          <div className={styles.platformUserManagementLeftTopBtn}>
            <HOCAuth authKey={'add_organization'}>
              {authAccess => (
                <Button
                  type="primary"
                  ghost
                  onClick={showModalAdd}
                  disabled={authAccess}
                >
                  + <FormattedMessage id="user.management.btn.add" />
                </Button>
              )}
            </HOCAuth>
          </div>
        </div>
        <div
          className={styles.platformUserManagementLeftBottom}
          onScroll={handleScroll}
        >
          {treeNode && treeNode.length > 0 && (
            <Tree defaultExpandAll>{renderTreeNodes(treeData)}</Tree>
          )}
          {/* <Tree treeData={treeData1} /> */}
        </div>
      </div>
      <div className={styles.platformUserManagementRight}>
        <div className={styles.selectContent}>
          <Input
            placeholder={getIntl().formatMessage({
              id: 'user.management.user.Info.placeholder',
            })}
            // onChange={e => setUserName(e.target.value)}
            onPressEnter={e => setUserName(e.target.value)}
            style={{ width: 264, height: 32 }}
          />

          <div style={{ float: 'right' }}>
            <HOCAuth authKey={'platform_user_invitation'}>
              {authAccess => (
                <Button
                  type="primary"
                  ghost
                  className={styles.ghostBtn}
                  onClick={() => inviteHandle()}
                  disabled={authAccess}
                >
                  <FormattedMessage id="user.management.invite" />
                </Button>
              )}
            </HOCAuth>
            <HOCAuth authKey={'change_organization_for_platform_users'}>
              {authAccess => (
                <Button
                  type="primary"
                  ghost
                  className={styles.ghostBtn}
                  onClick={() => setIsUpdateDeptOpen(true)}
                  disabled={authAccess}
                >
                  <FormattedMessage id="user.management.update.dept" />
                </Button>
              )}
            </HOCAuth>
            <HOCAuth authKey={'batch_confirm_platform_users'}>
              {authAccess => (
                <Button
                  type="primary"
                  ghost
                  className={styles.ghostBtn}
                  onClick={() => batchConfirmation()}
                  disabled={authAccess}
                >
                  <FormattedMessage id="user.management.batch.confirmation" />
                </Button>
              )}
            </HOCAuth>
            <HOCAuth authKey={'add_platform_users'}>
              {authAccess => (
                <Button
                  type={'primary'}
                  onClick={showModal}
                  disabled={authAccess}
                >
                  <FormattedMessage id="user.management.create.user" />
                </Button>
              )}
            </HOCAuth>
          </div>
        </div>
        <div className={styles.platformUserManagementContent}>
          <div className={styles.titleContent}>
            {/* <p>
              <FormattedMessage id="user.management.management" />
            </p> */}
            <Tabs items={tabsItems} onChange={key => setStatus(key)} />
          </div>
          <div className={styles.tableContent}>
            <Table
              loading={loading}
              dataSource={rows}
              columns={columns}
              rowSelection={{
                selectedRowKeys,
                onChange: onSelectChange,
              }}
              scroll={{
                x: 1200,
                // y: 'calc(100vh - 400px)',
              }}
              rowKey={row => row.userId}
              pagination={{
                total: total,
                showSizeChanger: true,
                current: pageNum,
                pageSize: pageSize,
                pageSizeOptions: [10, 20, 50, 100],
                showTotal: total => (
                  <FormattedMessage
                    id="page.total.num"
                    defaultMessage={`共 ${total} 条`}
                    values={{ total }}
                  />
                ),
                onChange: (pageNum, pageSize) => {
                  setPageNum(pageNum);
                  setPageSize(pageSize);
                },
              }}
            />
          </div>
        </div>

        <Modal
          title={
            isSave
              ? getIntl().formatMessage({
                  id: 'user.management.create.user',
                })
              : getIntl().formatMessage({
                  id: 'user.management.update.user',
                })
          }
          footer={null}
          open={isModalOpen}
          onCancel={handleCancel}
          className="addAdminModal"
        >
          <Spin spinning={userLoading}>
            <div>
              <Form
                name="basic"
                labelAlign="left"
                // labelCol={{
                //   span: 6,
                // }}
                // wrapperCol={{
                //   span: 14,
                // }}
                style={{
                  maxWidth: 900,
                }}
                initialValues={{
                  userType: 1,
                  deptId: [],
                }}
                ref={formRef}
                autoComplete="off"
                onFinish={onFinish}
              >
                <Row gutter={20}>
                  <Col span={10}>
                    {/* 用户名 */}
                    <Form.Item
                      label={getIntl().formatMessage({
                        id: 'register.user.name',
                      })}
                      name="userName"
                      rules={[
                        {
                          required: true,
                          message: (
                            <FormattedMessage id="register.user.name.input" />
                          ),
                        },
                      ]}
                    >
                      <Input
                        placeholder={getIntl().formatMessage({
                          id: 'register.user.name.input',
                        })}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={10}>
                    {/* 用户名 */}
                    <Form.Item
                      label={getIntl().formatMessage({
                        id: 'register.last.name',
                      })}
                      name="lastName"
                      rules={[
                        {
                          required: true,
                          message: (
                            <FormattedMessage id="register.last.name.input" />
                          ),
                        },
                      ]}
                    >
                      <Input
                        placeholder={getIntl().formatMessage({
                          id: 'register.last.name.input',
                        })}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={20}>
                  <Col span={10}>
                    {/* 邮箱 */}
                    <Form.Item
                      label={getIntl().formatMessage({
                        id: 'register.email',
                      })}
                      name="email"
                      rules={[
                        {
                          required: true,
                          message: (
                            <FormattedMessage id="login.user.email.input" />
                          ),
                        },
                        {
                          min: 1,
                          max: 40,
                          type: 'email',
                          message: (
                            <FormattedMessage id="login.user.email.pattern" />
                          ),
                        },
                      ]}
                    >
                      <Input
                        disabled={!isSave ? true : false}
                        placeholder={getIntl().formatMessage({
                          id: 'login.user.email.input',
                        })}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={10}>
                    {/* 手机号 */}
                    <Form.Item
                      label={getIntl().formatMessage({
                        id: 'register.phone',
                      })}
                      name="phonenumber"
                      rules={[
                        {
                          required: true,
                          message: (
                            <FormattedMessage id="register.user.phone.input" />
                          ),
                        },
                        {
                          max: 30,
                          pattern: '^(\\+\\d{1,3})?[1-9]\\d{9,14}$',
                          message: (
                            <FormattedMessage id="register.user.phone.length.input" />
                          ),
                        },
                      ]}
                    >
                      <Input
                        placeholder={getIntl().formatMessage({
                          id: 'register.user.phone.input',
                        })}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                {isSave ? (
                  <Row gutter={20}>
                    <Col span={10}>
                      {/* 初始密码 */}
                      <Form.Item
                        label={getIntl().formatMessage({
                          id: 'register.user.password',
                        })}
                        name="password"
                        rules={[
                          {
                            required: true,
                            message: (
                              <FormattedMessage id="register.user.name.input.initialPassword" />
                            ),
                          },
                        ]}
                      >
                        <Input
                          type={'password'}
                          placeholder={getIntl().formatMessage({
                            id: 'register.user.name.input.initialPassword',
                          })}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                ) : (
                  ''
                )}
                <Row gutter={20}>
                  <Col span={20}>
                    {/* 部门 */}
                    <Form.Item
                      label={getIntl().formatMessage({
                        id: 'register.deptName',
                      })}
                      name="deptId"
                      rules={[
                        {
                          required: true,
                          message: (
                            <FormattedMessage id="user.management.user.deptName.placeholder" />
                          ),
                        },
                      ]}
                    >
                      <TreeSelect
                        showSearch
                        style={{
                          width: '100%',
                        }}
                        // value={value}
                        dropdownStyle={{
                          maxHeight: 400,
                          overflow: 'auto',
                        }}
                        placeholder={getIntl().formatMessage({
                          id: 'user.management.user.deptName.placeholder',
                        })}
                        allowClear
                        treeDefaultExpandAll
                        // onChange={onChange}
                        treeData={treeData}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={20}>
                  <Col span={20}>
                    {/* 角色 */}
                    <Form.Item
                      label={getIntl().formatMessage({
                        id: 'user.management.role.select',
                      })}
                      name="userType"
                      rules={[
                        {
                          required: true,
                          message: (
                            <FormattedMessage id="user.management.role.select" />
                          ),
                        },
                      ]}
                    >
                      <Radio.Group
                        disabled={!isSave ? true : false}
                        onChange={onChange}
                        // defaultValue={radioValue}
                      >
                        <Radio value={1}>
                          <FormattedMessage id="user.management.radio.user.1" />
                        </Radio>
                        <Radio value={2}>
                          <FormattedMessage id="user.management.radio.user.2" />
                        </Radio>
                        <Radio value={3}>
                          <FormattedMessage id="user.management.radio.user.3" />
                        </Radio>
                      </Radio.Group>
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={20}>
                  <Col span={20}>
                    {/* 设为组织负责人 */}
                    {radioValue !== 1 && (
                      <Form.Item
                        label={getIntl().formatMessage({
                          id: 'user.management.set.leader',
                        })}
                        name="deptPrinciple"
                      >
                        <Radio.Group
                          onChange={onChangeCheck}
                          defaultValue={deptPrinciple}
                        >
                          <Radio value={1}>
                            <FormattedMessage id="user.management.operation.btn.yes" />
                          </Radio>
                          <Radio value={0}>
                            <FormattedMessage id="user.management.operation.btn.no" />
                          </Radio>
                        </Radio.Group>
                      </Form.Item>
                    )}
                  </Col>
                </Row>
                <Row gutter={20}>
                  <Col span={20}>
                    {/* 工号 */}
                    {radioValue !== 1 && (
                      <Form.Item
                        label={getIntl().formatMessage({
                          id: 'register.user.workNumber',
                        })}
                        name="workNumber"
                      >
                        <Input
                          placeholder={getIntl().formatMessage({
                            id: 'register.user.name.input.workNumber',
                          })}
                        />
                      </Form.Item>
                    )}
                  </Col>
                </Row>
                <Row gutter={20}>
                  <Col span={20}>
                    <Form.Item
                      style={{
                        display:
                          radioValue === 3 && openSelfChat !== 1
                            ? 'block'
                            : 'none',
                      }}
                      label={getIntl().formatMessage({
                        id: 'user.management.connect.add',
                      })}
                      // name="connectList"
                      rules={[
                        {
                          required: true,
                          // validator: (_, value) => {
                          //   if (radioValue === 3 && !value) {
                          //     return Promise.reject(
                          //       getIntl().formatMessage({
                          //         id: 'user.management.connect.select',
                          //       }),
                          //     );
                          //   }
                          //   return Promise.resolve();
                          // },
                        },
                      ]}
                    >
                      <Button
                        type={'primary'}
                        onClick={() => addContactLines()}
                        style={{
                          fontSize: 12,
                          height: 24,
                          width: 24,
                          padding: 0,
                        }}
                      >
                        <PlusOutlined />
                      </Button>
                    </Form.Item>
                    <Row
                      style={{
                        display:
                          radioValue === 3 && openSelfChat !== 1
                            ? 'block'
                            : 'none',
                        marginLeft: 40,
                        marginTop: '-10px',
                      }}
                    >
                      <Col span={24}>
                        {contactLinesList?.map((item, index) => {
                          return (
                            <div className={styles.contactLinesList}>
                              <p style={{ fontWeight: 'bold' }}>
                                <span>
                                  {getIntl().formatMessage({
                                    id: 'user.management.connect',
                                  })}
                                  {index + 1}
                                </span>

                                <span
                                  style={{
                                    float: 'right',
                                    display: index !== 0 ? 'block' : 'none',
                                  }}
                                >
                                  <Button
                                    type="primary"
                                    danger
                                    onClick={() => deleteContactLines(index)}
                                    icon={<DeleteHome />}
                                  >
                                    <span style={{ marginLeft: 6 }}>
                                      <FormattedMessage id="user.option.delete" />
                                    </span>
                                  </Button>
                                </span>
                              </p>
                              <div
                                style={{
                                  marginTop: 30,
                                  marginBottom: 10,
                                  clear: 'both',
                                  display: 'flex',
                                  alignItems: 'baseline',
                                }}
                              >
                                {getIntl().formatMessage({
                                  id: 'user.management.contact.selsect',
                                })}

                                <Select
                                  allowClear={true}
                                  options={newConnectList}
                                  value={item.connectId}
                                  // defaultValue={defaultConnectList}
                                  onChange={(e, option) =>
                                    getConnectUserInfo(e, index, option)
                                  }
                                  placeholder={getIntl().formatMessage({
                                    id:
                                      'user.management.contact.selsect.please',
                                  })}
                                  showSearch
                                  filterOption={(inputValue, option) =>
                                    option.label
                                      .toLowerCase()
                                      .indexOf(inputValue.toLowerCase()) >= 0
                                  }
                                />
                              </div>
                              {item.identityManagementType === 'SAML' ? (
                                <div>
                                  <div
                                    style={{
                                      marginLeft:
                                        getLocale() === 'zh-CN' ? 84 : 140,
                                      marginTop: 10,
                                      marginBottom: 10,
                                    }}
                                  >
                                    <Radio.Group
                                      onChange={e =>
                                        onChangeCreateType(e, index)
                                      }
                                      value={item.userCreateType}
                                    >
                                      <Radio value={1} disabled={!isSave}>
                                        <FormattedMessage id="user.management.connect.add.user" />
                                      </Radio>
                                      <Radio value={2}>
                                        <FormattedMessage id="user.management.connect.add.user.bind" />
                                      </Radio>
                                      {/* <Tooltip
                                    placement="top"
                                    title={getIntl().formatMessage({
                                      id:
                                        'user.management.contact.Tooltip.radio3',
                                    })}
                                  >
                                    <Radio value={3}>
                                      <FormattedMessage id="user.management.connect.add.user.bind.no" />
                                    </Radio>
                                  </Tooltip> */}
                                    </Radio.Group>
                                  </div>
                                  {item.userCreateType == 2 ? (
                                    <div
                                      style={{
                                        marginBottom: 10,
                                        display: 'flex',
                                        alignItems: 'baseline',
                                      }}
                                    >
                                      {getIntl().formatMessage({
                                        id:
                                          'user.management.contact.selsect.user',
                                      })}

                                      <Select
                                        allowClear={true}
                                        options={item.newConnectUserList}
                                        // defaultValue={defaultConnectList}
                                        onChange={(e, option) =>
                                          onChangeConnectUser(e, option, index)
                                        }
                                        value={item.connectUserId}
                                        placeholder={getIntl().formatMessage({
                                          id:
                                            'user.management.contact.selsect.user.please',
                                        })}
                                        showSearch
                                        filterOption={(inputValue, option) =>
                                          option.label
                                            .toLowerCase()
                                            .indexOf(
                                              inputValue.toLowerCase(),
                                            ) >= 0
                                        }
                                      />
                                    </div>
                                  ) : (
                                    ''
                                  )}
                                </div>
                              ) : (
                                ''
                              )}
                            </div>
                          );
                        })}
                      </Col>
                    </Row>
                  </Col>
                </Row>
                <Form.Item
                  wrapperCol={{
                    offset: 9,
                    span: 16,
                  }}
                  style={{ marginBottom: '0px' }}
                >
                  <Button onClick={handleCancel}>
                    <FormattedMessage id="user.management.btn.cancel" />
                  </Button>
                  <Button
                    loading={loadings[0]}
                    type="primary"
                    htmlType="submit"
                  >
                    {isSave
                      ? getIntl().formatMessage({
                          id: 'user.management.btn.add',
                        })
                      : getIntl().formatMessage({
                          id: 'user.management.btn.update',
                        })}
                  </Button>
                </Form.Item>
              </Form>
            </div>
          </Spin>
        </Modal>
        <Modal
          title={
            isSaveDept
              ? getIntl().formatMessage({
                  id: 'user.management.add.dept',
                })
              : getIntl().formatMessage({
                  id: 'user.management.update.deptModel',
                })
          }
          width={500}
          footer={null}
          open={isAddDeptOpen}
          onCancel={handleCancelAdd}
          className="addDeptModal"
        >
          <div>
            <Form
              name="basic"
              labelCol={{
                span: 6,
              }}
              wrapperCol={{
                span: 18,
              }}
              style={{
                maxWidth: 400,
              }}
              initialValues={{
                remember: true,
              }}
              ref={formRefDept}
              autoComplete="off"
              onFinish={onFinishDept}
            >
              <Form.Item
                label={getIntl().formatMessage({
                  id: 'register.deptName',
                })}
                name="deptName"
                rules={[
                  {
                    required: true,
                    message: (
                      <FormattedMessage id="user.management.user.default.placeholder" />
                    ),
                  },
                ]}
              >
                <Input
                  placeholder={getIntl().formatMessage({
                    id: 'user.management.user.default.placeholder',
                  })}
                />
              </Form.Item>
              <Form.Item
                label={getIntl().formatMessage({
                  id: 'register.deptId',
                })}
                name="code"
                rules={[
                  {
                    required: true,
                    message: (
                      <FormattedMessage id="user.management.user.default.placeholder" />
                    ),
                  },
                ]}
              >
                <Input
                  placeholder={getIntl().formatMessage({
                    id: 'user.management.user.default.placeholder',
                  })}
                />
              </Form.Item>

              <Form.Item
                label={getIntl().formatMessage({
                  id: 'register.parentDept',
                })}
                name="parentId"
                rules={[
                  {
                    required: true,
                    message: (
                      <FormattedMessage id="user.management.user.parent.placeholder" />
                    ),
                  },
                ]}
              >
                <TreeSelect
                  showSearch
                  style={{
                    width: '100%',
                  }}
                  // value={value}
                  dropdownStyle={{
                    maxHeight: 400,
                    overflow: 'auto',
                  }}
                  placeholder={getIntl().formatMessage({
                    id: 'user.management.user.parent.placeholder',
                  })}
                  allowClear
                  treeDefaultExpandAll
                  onChange={onChangeGetLevel}
                  treeData={treeData}
                />
              </Form.Item>
              <Form.Item
                label={getIntl().formatMessage({
                  id: 'register.deptPerson',
                })}
                name="userName"
              >
                <Select
                  allowClear={true}
                  disabled={isSaveDept ? false : true}
                  options={userNotInDeptArr}
                  // defaultValue={defaultConnectList}
                  // onChange={onChangeChannel}
                  placeholder={getIntl().formatMessage({
                    id: 'user.management.user.person.placeholder',
                  })}
                />
              </Form.Item>

              <Form.Item
                wrapperCol={{
                  offset: 10,
                  span: 14,
                }}
                style={{ marginBottom: '0px' }}
              >
                <Button onClick={handleCancelAdd} style={{ marginRight: 20 }}>
                  <FormattedMessage id="user.management.btn.cancel" />
                </Button>
                <Button loading={loadings[0]} type="primary" htmlType="submit">
                  {getIntl().formatMessage({
                    id: 'channel.save',
                  })}
                </Button>
              </Form.Item>
            </Form>
          </div>
        </Modal>

        <Modal
          title={getIntl().formatMessage({
            id: 'user.management.update.dept',
          })}
          width={500}
          footer={null}
          open={isUpdateDeptOpen}
          onCancel={handleCancelUpdate}
        >
          <div>
            <Form
              name="basic"
              labelCol={{
                span: 6,
              }}
              wrapperCol={{
                span: 18,
              }}
              style={{
                maxWidth: 400,
              }}
              initialValues={{
                remember: true,
              }}
              ref={formRefDeptUpdate}
              autoComplete="off"
              onFinish={onFinishDeptUpdate}
            >
              <Form.Item
                label={getIntl().formatMessage({
                  id: 'register.deptName',
                })}
                name="deptId"
                rules={[
                  {
                    required: true,
                    message: (
                      <FormattedMessage id="user.management.user.deptName.placeholder" />
                    ),
                  },
                ]}
              >
                <TreeSelect
                  showSearch
                  style={{
                    width: '100%',
                  }}
                  // value={value}
                  dropdownStyle={{
                    maxHeight: 400,
                    overflow: 'auto',
                  }}
                  placeholder={getIntl().formatMessage({
                    id: 'user.management.user.deptName.placeholder',
                  })}
                  allowClear
                  treeDefaultExpandAll
                  treeData={treeData}
                />
              </Form.Item>

              <Form.Item
                wrapperCol={{
                  offset: 10,
                  span: 14,
                }}
                style={{ marginBottom: '0px' }}
              >
                <Button
                  onClick={handleCancelUpdate}
                  style={{ marginRight: 20 }}
                >
                  <FormattedMessage id="user.management.btn.cancel" />
                </Button>
                <Button loading={loadings[0]} type="primary" htmlType="submit">
                  {getIntl().formatMessage({
                    id: 'channel.save',
                  })}
                </Button>
              </Form.Item>
            </Form>
          </div>
        </Modal>
      </div>
      <Modal
        title={getIntl().formatMessage({
          id: 'user.management.invite.title',
        })}
        width={700}
        footer={null}
        open={inviteModal}
        onCancel={handleInviteModal}
        className={styles.modal}
      >
        <div>
          <Form
            name="basic"
            labelCol={{
              span: 4,
            }}
            wrapperCol={{
              span: 20,
            }}
            style={{
              maxWidth: 600,
            }}
            initialValues={{
              remember: true,
            }}
            ref={formRefInvite}
            autoComplete="off"
            onFinish={formRefInviteUpdate}
          >
            <Form.Item
              label={getIntl().formatMessage({
                id: 'register.email',
              })}
              name="email"
            >
              <div className={styles.inviteModal}>
                <div>{renderTags()}</div>
                <TextArea
                  placeholder={getIntl().formatMessage({
                    id: 'user.management.email.tags.placeholder',
                  })}
                  style={{ border: 'none' }}
                  rows={4}
                  value={inputValue}
                  onChange={handleInputChange}
                  onPressEnter={handleInputKeyPress}
                />
              </div>
            </Form.Item>
            <Form.Item
              label={getIntl().formatMessage({
                id: 'user.management.invite.apply',
              })}
              name="email"
            >
              <Switch
                checkedChildren={<CheckOutlined />}
                unCheckedChildren={<CloseOutlined />}
                defaultChecked
                onChange={onChangeSwitch}
              />
            </Form.Item>
            <Form.Item
              wrapperCol={{
                offset: 10,
                span: 14,
              }}
              style={{ marginBottom: '0px' }}
            >
              <Button onClick={handleInviteModal} style={{ marginRight: 20 }}>
                <FormattedMessage id="user.management.btn.cancel" />
              </Button>
              <Button loading={loadings[0]} type="primary" htmlType="submit">
                {getIntl().formatMessage({
                  id: 'user.management.btn.confirm',
                })}
              </Button>
            </Form.Item>
          </Form>
        </div>
      </Modal>
    </div>
  );
};
