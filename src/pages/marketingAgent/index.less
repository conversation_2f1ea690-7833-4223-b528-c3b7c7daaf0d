.tabItem {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  padding: 3px 0;
}

.createContainer {
  width: 32%;
  margin-right: 2%;
  margin-bottom: 20px;
  float: left;
  position: relative;
  height: 210px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  background: #dfe2f0;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
  cursor: pointer;

  .bg {
    position: absolute;
    top: 2%;
    left: 1.5%;
    width: 97%;
    height: 96%;
    z-index: 2;
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(24px);
    border-radius: 10px;
    overflow: hidden;

    .detailCreateContainer {
      width: 100%;
      height: 100%;
      text-align: center;

      img {
        width: 80px;
        margin-top: 35px;
      }

      p {
        color: #333;
        text-align: center;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 150%;
        margin-bottom: 0px;
      }
    }
  }

  .blob {
    position: absolute;
    z-index: 1;
    top: 50%;
    left: 50%;
    width: 220px;
    height: 160px;
    border-radius: 50%;
    background-color: #3463fc;
    opacity: 1;
    filter: blur(12px);
    animation: blob-bounce 5s infinite ease;
  }

  @keyframes blob-bounce {
    0% {
      transform: translate(-100%, -100%) translate3d(0, 0, 0);
    }

    25% {
      transform: translate(-100%, -100%) translate3d(100%, 0, 0);
    }

    50% {
      transform: translate(-100%, -100%) translate3d(100%, 100%, 0);
    }

    75% {
      transform: translate(-100%, -100%) translate3d(0, 100%, 0);
    }

    100% {
      transform: translate(-100%, -100%) translate3d(0, 0, 0);
    }
  }
}

.externalItemContainer {
  width: 32%;
  margin-right: 2%;
  margin-bottom: 20px;
  float: left;
  height: 210px;
  border: 1px solid #fff;
  border-radius: 10px;
  background: #fff;
  position: relative;
  box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.15);
  padding: 10px;

  .numberUsesContainerDefault {
    position: absolute;
    right: -5px;
    top: -4px;
  }

  .topItem {
    width: 100%;
    float: left;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    flex-direction: row;
    justify-content: flex-start;

    p {
      margin-bottom: 0px;
    }

    .intentionName {
      color: #333;
      font-size: 14px;
      font-style: normal;
      font-weight: 700;
      line-height: 150%;
      max-width: calc(100% - 70px);
      margin-bottom: 0px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .numberUsesContainerSwitch {
      position: absolute;
      right: 10px;
      top: 10px;
    }

    .numberUsesContainer {
      min-width: 10%;
      margin-top: -2px;
      margin-left: 5px;
      overflow: hidden;

      img {
        width: 12px;
      }

      span {
        color: #3463fc;
        text-align: center;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        margin-left: 4px;
        line-height: 150%;
      }
    }

    .labelText {
      color: #333;
      font-size: 12px;
      font-style: normal;
      font-weight: 700;
      line-height: 150%;
      float: left;
      max-width: 29%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin-right: 1%;
    }

    .intentClassificationText {
      color: #999;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%;
      max-width: 70%;
      float: left;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

      img {
        width: 12px;
        float: left;
        margin-top: 2px;
        margin-right: 4px;
      }
    }

    .intentClassificationImg {
      width: 70%;
      overflow: hidden;

      img {
        width: 12px;
        height: 12px;
        float: left;
        margin-right: 4px;
        margin-top: 3px;
      }
    }

    .deployStatusText {
      color: #333;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%;
      max-width: 70%;
      float: left;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

      img {
        width: 14px;
        height: 14px;
        float: left;
        margin-right: 4px;
        margin-top: 2px;
      }
    }

    .intentReasonText {
      color: #333;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%;
      max-width: 70%;
      float: left;
      word-break: break-all;
      text-overflow: ellipsis;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .createInformationText {
      color: #333;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 150%;
      max-width: 70%;
      float: left;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .topItemRule {
    color: #939393;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
  }

  .footerItemContainer {
    width: 100%;
    float: left;
    position: absolute;
    bottom: 8px;

    .intelligentAgentContainer {
      cursor: pointer;
      text-align: center;

      img {
        width: 12px;
        float: left;
        margin-right: 3px;
        margin-top: 5px;
      }

      span {
        color: #3463fc;
        text-align: center;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        float: left;
        margin-right: 3px;
      }

      .intelligentAgentNum {
        width: 23px;
        height: 20px;
        float: left;
        border-radius: 4px;
        background: #e7fae9;
        color: #13c825;
        text-align: center;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
      }
    }

    .intelligentAgentContainerNo {
      cursor: not-allowed;
      text-align: center;

      img {
        width: 12px;
        float: left;
        margin-right: 3px;
        margin-top: 5px;
      }

      span {
        color: #999;
        text-align: center;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        float: left;
        margin-right: 3px;
      }
    }

    .intentionEditorContainer {
      cursor: pointer;

      img {
        width: 12px;
        float: left;
        margin-right: 3px;
        margin-top: 5px;
      }

      span {
        color: #3463fc;
        text-align: center;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        float: left;
        margin-right: 3px;
      }
    }

    .intentionDeleteContainer {
      cursor: pointer;

      img {
        width: 12px;
        float: left;
        margin-right: 3px;
        margin-top: 5px;
      }

      span {
        color: #f22417;
        text-align: center;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        float: left;
        margin-right: 3px;
      }
    }

    :global {
      .ant-col-7,
      .ant-col-9 {
        display: flex;
        justify-content: center;
      }
    }
  }
}

// 选择第三个class为externalItemContainer的div
.externalItemContainer:nth-child(3n),
.externalItemContainer:nth-child(3n + 3) {
  margin-right: 0px;
}

.externalItemContainer:hover {
  border-radius: 10px;
  border: 1px solid #3463fc;
  background: #ebefff;
  box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.15);
}

:global {
  .ant-switch-checked {
    background-color: #3463fc;
  }
}

.channelName {
  border-radius: 4px;
  border: 1px solid rgba(52, 99, 252, 0.5);
  background: #ebefff;
  color: #3463fc;
  font-variant-numeric: lining-nums tabular-nums;
  font-family: 'Noto Sans SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 18px;
  padding: 0 10px;
}

.statusContainer {
  display: flex;
  width: 100%;
  padding: 5px 0;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 4px;
  font-family: 'Microsoft YaHei';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  cursor: pointer;
}

.startStatus {
  background-color: #3463fc;
  color: #fff;
}
.pauseStatus {
  background-color: #fcb830;
  color: #fff;
}
.endStatus {
  background-color: #e6e6e6;
  color: #666;
}

.marketingDetailCreateContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  img {
    margin-top: 0 !important;
  }
}
