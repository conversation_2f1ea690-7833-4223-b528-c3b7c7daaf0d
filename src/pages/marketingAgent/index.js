import React, { useState, useRef, useEffect } from 'react';
import styles from '../externalIntelligentAgent/listPage/index.less';
import currentStyles from './index.less';
import { useDispatch, getIntl, FormattedMessage, history } from 'umi';
import { SearchOutlined } from '@ant-design/icons';
import {
  Button,
  Col,
  Input,
  notification,
  Row,
  Select,
  Spin,
  Tabs,
  Modal,
  Form,
  Pagination,
} from 'antd';
// import AddMarketingAgentModel from './components/addMarketingAgentModel'; // 已移除，改为跳转页面
import CreateIntentionIcon from '@/assets/create-intention-icon.svg';
import NumberUsesIcon from '@/assets/number-uses-icon.svg';
import NewEditorNormalIcon from '@/assets/external-editor.png';
import NewDeleteIcon from '@/assets/new-delete-list-icon.png';
import NewCopyIcon from '@/assets/external-copy.png';
import DraftIcon from '@/assets/external-draft.png';
import DeployIcon from '@/assets/external-deploy.png';
import NewWebChatIcon from '@/assets/new-web-chat-icon.svg';
import NewAppChatIcon from '@/assets/new-app-chat-icon.svg';
import NewPhoneIcon from '@/assets/new-phone-icon.svg';
import NewWebOnlineVoiceIcon from '@/assets/web-online-voice-icon.svg';
import NewAppOnlineVoiceIcon from '@/assets/app-online-voice-icon.svg';
import NewWhatsAppIcon from '@/assets/new-whatsapp-icon.svg';
import NewFaceBookIcon from '@/assets/new-channel-facebook-icon.svg';
import NewInstagramIcon from '@/assets/new-channel-instagram-icon.svg';
import NewLineIcon from '@/assets/new-line-icon.svg';
import NewWeComIcon from '@/assets/new-wecom-icon.svg';
import NewWeChatMiniProgramIcon from '@/assets/new-wechat-mini-program-icon.svg';
import NewWechatOfficialAccountIcon from '@/assets/new-wechat-official-account-icon.svg';
import NewEmailIcon from '@/assets/new-email-icon.svg';
import NewAmazonMessageIcon from '@/assets/new-channel-aws-icon.svg';
import NewShopifyIcon from '@/assets/new-shopify-icon.svg';
import DeleteTipsIcon from '@/assets/delete-tips-icon.png';
import ShareCodeModal from '../externalIntelligentAgent/shareComponents';
import { WhatsappIcon, ShareCodeIcon } from './icon';

const MarketingAgentList = () => {
  const dispatch = useDispatch();
  const formDeleteIntentionRef = useRef(null); // 创建一个 ref 来保存 EditorJS 实例
  const [loading, setLoading] = useState(false);
  const [loadingAddBtn, setLoadingAddBtn] = useState(false);
  const [deleteIntention, setDeleteModal] = useState(false);
  // tab值 1----chat  2----email  3-----phone
  const [tabKey, setTabKey] = useState(1);
  const [total, setTotal] = useState(0);
  const [pageSize, setPageSize] = useState(8);
  const [pageNum, setPageNum] = useState(1);
  // 聊天类型智能体数量
  const [chatNum, setChatNum] = useState(0);
  // 邮件类型智能体数量
  const [emailNum, setEmailNum] = useState(0);
  // 电话类型智能体数量
  const [phoneNum, setPhoneNum] = useState(0);

  // const [addModelOpen, setAddModelOpen] = useState(false); // 已移除弹窗

  // 部署状态下拉列表
  const [deployStatusList, setDeployStatusList] = useState([
    {
      label: getIntl().formatMessage({
        id: 'external.intelligent.agent.deploy.status.1',
        defaultValue: '已部署',
      }),
      value: 1,
      key: 1,
    },
    {
      label: getIntl().formatMessage({
        id: 'external.intelligent.agent.deploy.status.2',
        defaultValue: '草稿',
      }),
      value: 0,
      key: 0,
    },
  ]);
  // 智能体列表
  const [intelligentAgentList, setIntelligentAgentList] = useState([]);
  // 智能体名称
  const [aiAgentName, setAiAgentName] = useState('');
  // 部署状态
  const [deployStatus, setDeployStatus] = useState('');
  // 活动ID----
  const [marketingId, setmarketingId] = useState('');
  // 活动名称下拉列表
  const [intentNameList, setIntentNameList] = useState([]);
  //智能体名称搜索值----智能体名称
  const [aiAgentNameSearch, setAiAgentNameSearch] = useState('');
  // 智能体ID
  const [aiAgentId, setAiAgentId] = useState('');
  // 分享码弹窗
  const [shareCodeModalVisible, setShareCodeModalVisible] = useState(false);
  // 分享码
  const [shareCode, setShareCode] = useState('');
  // 分享码有效期
  const [shareCodeExpiryTime, setShareCodeExpiryTime] = useState('');
  // 分享码用户名
  const [userName, setUserName] = useState('');
  // 渠道数字对应图标-----channelIds 对应的图标映射
  const channelIconMap = {
    1: NewEmailIcon, // 邮件
    3: NewFaceBookIcon, // facebook
    4: NewWhatsAppIcon, // WhatsApp
    7: NewPhoneIcon, // 电话
    8: NewWebChatIcon, // WEB聊天
    9: NewAppChatIcon, // APP聊天
    12: NewAmazonMessageIcon, // 亚马逊站内信
    13: NewInstagramIcon, // Instagram
    14: NewLineIcon, // Line
    15: NewWeComIcon, // 微信客服
    16: NewWechatOfficialAccountIcon, // 微信公众号
    17: NewWebOnlineVoiceIcon, // WEB语音
    18: NewAppOnlineVoiceIcon, // APP语音
    21: NewWeChatMiniProgramIcon, // 微信小程序
    22: NewShopifyIcon, // Shopify
  };
  // 意图ID
  const [intentId, setIntentId] = useState('');
  useEffect(() => {
    queryIntentNameList();
  }, []);

  useEffect(() => {
    if (history.location.state) {
      let newIntentId = history.location.state.newIntentId;
      if (newIntentId) {
        setIntentId(newIntentId);
      }
    } else {
      let selectIntentItem = JSON.parse(
        localStorage.getItem('selectIntentItem'),
      );
      if (selectIntentItem) {
        let intentId = selectIntentItem.intentId;
        setIntentId(intentId);
      }
    }
  }, [history.location.state]);

  // 构建查询参数
  const buildQueryParams = (overrides = {}) => {
    const baseParams = {
      pageSize: pageSize,
      pageNum: pageNum,
      aiAgentName: aiAgentNameSearch,
      deployStatus: deployStatus,
      marketingId: marketingId,
      channelTypeId: tabKey,
    };

    const baseParamsNum = {
      aiAgentName: aiAgentNameSearch,
      deployStatus: deployStatus,
      marketingId: marketingId,
    };

    return {
      params: { ...baseParams, ...overrides },
      paramsNum: { ...baseParamsNum, ...overrides },
    };
  };

  // 统一获取所有数据
  const fetchAllData = (overrides = {}) => {
    const { params, paramsNum } = buildQueryParams(overrides);
    setLoading(true);
    paramsNum.isMarketing = 1;
    const promises = [
      new Promise(resolve => {
        dispatch({
          type: 'intentionManagement/queryIntelligentAgentPages',
          payload: params,
          callback: resolve,
        });
      }),
      new Promise(resolve => {
        dispatch({
          type: 'intentionManagement/queryIntelligentAgentNum',
          payload: paramsNum,
          callback: resolve,
        });
      }),
    ];

    Promise.all(promises)
      .then(([pagesResponse, numResponse]) => {
        setLoading(false);
        // 处理页面数据
        if (pagesResponse && pagesResponse.code === 200) {
          setIntelligentAgentList(pagesResponse.data.records);
          setTotal(pagesResponse.data.total);
        } else if (pagesResponse) {
          notification.error({
            message: pagesResponse.msg,
          });
        }
        // 处理数量数据
        if (numResponse && numResponse.code === 200) {
          let newData = numResponse.data;
          newData.map(item => {
            if (item.channelTypeId === '1') {
              setChatNum(item.count);
            }
            if (item.channelTypeId === '2') {
              setEmailNum(item.count);
            }
            if (item.channelTypeId === '3') {
              setPhoneNum(item.count);
            }
          });
        } else if (numResponse) {
          notification.error({
            message: numResponse.msg,
          });
        }
      })
      .catch(() => {
        console.log('error----------');
        setLoading(false);
      });
  };

  // 获取意图名称下拉列表
  const queryIntentNameList = () => {
    setLoading(true);
    dispatch({
      type: 'marketingActivities/queryMarketingAllList',
      callback: response => {
        setLoading(false);
        if (response.code === 200) {
          setIntentNameList(response.data);
          const intentId = history.location.state?.intentId;
          if (intentId) {
            setmarketingId(intentId);
          }
          fetchAllData({
            marketingId: intentId ? intentId : marketingId,
          });
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 选择部署状态
  const handleSelectStatus = value => {
    setDeployStatus(value);
    setPageNum(1);
    setPageSize(8);
    fetchAllData({
      pageSize: 8,
      pageNum: 1,
      deployStatus: value,
    });
  };
  // 选择意图名称
  const handleSelectIntentName = value => {
    setmarketingId(value);
    setPageNum(1);
    setPageSize(8);
    fetchAllData({
      pageSize: 8,
      pageNum: 1,
      marketingId: value,
    });
  };
  // 输入智能体名称
  const handleChangeInput = e => {
    setAiAgentName(e.target.value);
  };
  // 筛选事件
  const handleSearch = () => {
    setAiAgentNameSearch(aiAgentName);
    setPageNum(1);
    setPageSize(8);
    fetchAllData({
      pageSize: 8,
      pageNum: 1,
      aiAgentName: aiAgentName,
    });
  };
  // 切换智能体类型----Tab
  const onChangeTabs = e => {
    setTabKey(e);
    setPageNum(1);
    setPageSize(8);
    let params = {
      pageSize: pageSize,
      pageNum: pageNum,
      aiAgentName: aiAgentNameSearch,
      deployStatus: deployStatus,
      marketingId: marketingId,
      channelTypeId: e,
    };
    queryIntelligentAgentPages(params);
  };

  // 查询智能体列表（单独使用）
  const queryIntelligentAgentPages = params => {
    setLoading(true);
    dispatch({
      type: 'intentionManagement/queryIntelligentAgentPages',
      payload: params,
      callback: response => {
        if (response.code === 200) {
          setIntelligentAgentList(response.data.records);
          setTotal(response.data.total);
          setLoading(false);
        } else {
          setLoading(false);
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  // 部署智能体
  const deployIntelligentAgent = intelligentAgentItem => {
    setLoading(true);
    dispatch({
      type: 'intentionManagement/deployIntelligentAgent',
      payload: intelligentAgentItem.aiAgentId,
      callback: response => {
        setLoading(false);
        if (response.code === 200) {
          fetchAllData();
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 新建智能体
  const handleCreateIntention = () => {
    history.push('/marketingAgent/create');
  };
  // 显示删除智能体弹窗
  const handleShowDeleteModal = intelligentAgentItem => {
    setAiAgentId(intelligentAgentItem.aiAgentId);
    setDeleteModal(true);
  };
  // 删除智能体
  const deleteIntentionModal = () => {
    setLoadingAddBtn(true);
    setLoading(true);
    dispatch({
      type: 'intentionManagement/deleteIntelligentAgent',
      payload: aiAgentId,
      callback: response => {
        if (response.code === 200) {
          formDeleteIntentionRef.current?.resetFields();
          setDeleteModal(false);
          setLoading(false);
          setLoadingAddBtn(false);
          setAiAgentId('');
          setAiAgentName('');
          setAiAgentNameSearch('');
          setmarketingId('');
          setDeployStatus('');
          setPageNum(1);
          setPageSize(8);
          fetchAllData({
            pageSize: 8,
            pageNum: 1,
            aiAgentName: '',
            deployStatus: '',
            marketingId: '',
            channelTypeId: 1,
          });
        } else {
          setLoading(false);
          setLoadingAddBtn(false);
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };
  // 取消删除智能体事件
  const closeDeleteModal = () => {
    setDeleteModal(false);
    setAiAgentId('');
    formDeleteIntentionRef.current?.resetFields();
  };
  // 分页操作
  const handleChangePage = (page, pageSize) => {
    setPageNum(page);
    setPageSize(pageSize);
    let params = {
      pageSize: pageSize,
      pageNum: page,
      aiAgentName: aiAgentNameSearch,
      deployStatus: deployStatus,
      marketingId: marketingId,
      channelTypeId: tabKey,
    };
    queryIntelligentAgentPages(params);
  };
  // 总数显示
  const showTotal = total => (
    <FormattedMessage
      id="studentManagement.altogether"
      defaultMessage={`共 ${total} 条`}
      values={{ total }}
    />
  );
  // 修改智能体
  const handleEditorIntention = intelligentAgentItem => {
    history.push({
      pathname: '/marketingAgent/create',
      state: {
        itemData: intelligentAgentItem,
        status: 'editor',
      },
    });

    localStorage.setItem('aiAgentId', intelligentAgentItem.aiAgentId);
    localStorage.setItem('aiAgentType', intelligentAgentItem.aiAgentType);
    localStorage.setItem('aiAgentStatus', 'editor');
  };
  // 分享码
  const shareCodeHandle = intelligentAgentItem => {
    setLoading(true);
    dispatch({
      type: 'aiagent/getShareCode',
      payload: { aiAgentId: intelligentAgentItem.aiAgentId },
      callback: response => {
        if (response.code === 200) {
          setShareCode(response.data?.shareCode);
          setUserName(response.data?.userName);
          setShareCodeModalVisible(true);
          setShareCodeExpiryTime(response.data?.expiryTime);
        } else {
          notification.error({
            message: response.msg,
          });
        }
        setLoading(false);
      },
    });
  };
  // 复制智能体
  const queryCopyAiAgent = intelligentAgentItem => {
    setLoading(true);
    dispatch({
      type: 'intentionManagement/queryCopyAiAgent',
      payload: intelligentAgentItem.aiAgentId,
      callback: response => {
        setLoading(false);
        if (response.code === 200) {
          history.push({
            pathname: '/agentFlow',
            state: {
              aiAgentData: response.data,
              status: 'copy',
            },
          });
          localStorage.setItem('aiAgentData', JSON.stringify(response.data));
        } else {
          notification.error({
            message: response.msg,
          });
        }
      },
    });
  };

  const getStatusClassName = status => {
    const statusClassMap = new Map([
      [0, `${currentStyles.startStatus}`],
      [1, `${currentStyles.pauseStatus}`],
      [2, `${currentStyles.endStatus}`],
    ]);
    return statusClassMap.get(status) || '';
  };
  const getStatusText = status => {
    const statusTextMap = new Map([
      [
        0,
        getIntl().formatMessage({
          id: 'marketing.agent.status.start',
          defaultValue: '立即开始',
        }),
      ],
      [
        1,
        getIntl().formatMessage({
          id: 'marketing.agent.status.pause',
          defaultValue: '立即暂停',
        }),
      ],
      [
        2,
        getIntl().formatMessage({
          id: 'marketing.agent.status.end',
          defaultValue: '活动已结束',
        }),
      ],
    ]);
    return statusTextMap.get(status);
  };

  return (
    <Spin spinning={loading}>
      <div className={styles.externalIntelligentAgentList}>
        <div className={styles.selectContainer}>
          <div className={styles.selectItem}>
            <p
              title={getIntl().formatMessage({
                id: 'external.intelligent.agent.deployment.status',
                defaultValue: '部署状态：',
              })}
            >
              <FormattedMessage
                id="external.intelligent.agent.deployment.status"
                defaultValue="部署状态："
              ></FormattedMessage>
            </p>
            <Select
              value={deployStatus !== '' ? deployStatus : null}
              allowClear
              placeholder={getIntl().formatMessage({
                id: 'external.intelligent.agent.deployment.status.placeholder',
                defaultValue: '请选择部署状态',
              })}
              options={deployStatusList}
              onChange={handleSelectStatus}
            />
          </div>
          <div className={styles.selectItem}>
            <p
              title={getIntl().formatMessage({
                id: 'external.intelligent.agent.name',
                defaultValue: '智能体名称：',
              })}
            >
              <FormattedMessage
                id="external.intelligent.agent.name"
                defaultValue="智能体名称："
              ></FormattedMessage>
            </p>
            <Input
              value={aiAgentName}
              onChange={handleChangeInput}
              placeholder={getIntl().formatMessage({
                id: 'external.intelligent.agent.name.placeholder',
                defaultValue: '请输入智能体名称',
              })}
            />
          </div>
          <div className={styles.selectItem}>
            <p
              title={getIntl().formatMessage({
                id: 'marketing.agent.activity.name',
                defaultValue: '活动名称：',
              })}
            >
              <FormattedMessage
                id="marketing.agent.activity.name"
                defaultValue="活动名称："
              ></FormattedMessage>
            </p>
            <Select
              value={marketingId ? marketingId : null}
              onChange={handleSelectIntentName}
              placeholder={getIntl().formatMessage({
                id: 'marketing.agent.activity.select.name',
                defaultValue: '请选择活动名称',
              })}
              allowClear={true}
              showSearch
              options={intentNameList.map(item => ({
                label: item.activityName,
                value: item.activityId,
                key: item.activityId,
              }))}
              filterOption={(inputValue, option) =>
                option.label.toLowerCase().indexOf(inputValue.toLowerCase()) >=
                0
              }
            />
          </div>
          <div className={styles.buttonContainer}>
            <Button
              onClick={handleSearch}
              type="primary"
              icon={<SearchOutlined />}
            >
              <FormattedMessage
                id="marketing.agent.search.btn"
                defaultMessage="筛选"
              />
            </Button>
          </div>
        </div>
        <Tabs
          onChange={e => onChangeTabs(e)}
          activeKey={tabKey}
          items={[
            {
              label: (
                <span className={currentStyles.tabItem}>
                  {tabKey === 1 ? WhatsappIcon() : WhatsappIcon()}
                  <FormattedMessage
                    id="marketing.agent.tab.whatsapp"
                    defaultMessage="WhatsApp"
                  ></FormattedMessage>
                  <span
                    className={
                      tabKey === 1 ? 'numActiveContainer' : 'numContainer'
                    }
                  >
                    {chatNum}
                  </span>
                </span>
              ),
              key: 1,
            },
          ]}
        />
        <div className={styles.listContainer}>
          <div
            style={{ height: '254px' }}
            className={styles.createContainer}
            onClick={handleCreateIntention}
          >
            <div className={styles.bg}>
              <div
                className={`${styles.detailCreateContainer} ${currentStyles.marketingDetailCreateContainer}`}
              >
                <img src={CreateIntentionIcon} />
                <p>
                  <FormattedMessage
                    id="external.intelligent.agent.create.text"
                    defaultMessage="新建智能体"
                  ></FormattedMessage>
                </p>
              </div>
            </div>
            <div className={styles.blob}></div>
          </div>
          {intelligentAgentList?.map((intelligentAgentItem, index) => {
            // 新增：抽取可编辑判断条件
            const canEdit =
              intelligentAgentItem.aiAgentStatus === 0 &&
              intelligentAgentItem.activityStatus === 1;
            return (
              <div
                style={{ height: '254px' }}
                className={styles.externalItemContainer}
                key={intelligentAgentItem.aiAgentId}
              >
                <div className={styles.topItem}>
                  <p
                    title={intelligentAgentItem.aiAgentName}
                    className={styles.intentionName}
                  >
                    {intelligentAgentItem.aiAgentName}
                  </p>
                  <div className={styles.numberUsesContainer}>
                    <img src={NumberUsesIcon} />
                    <span>
                      <FormattedMessage
                        id="intention.management.user.number"
                        values={{
                          num: intelligentAgentItem.agentUsageCount,
                        }}
                      ></FormattedMessage>
                    </span>
                  </div>
                </div>
                <div className={styles.topItem}>
                  <p className={styles.labelText}>
                    <FormattedMessage
                      id="marketing.agent.activity.name"
                      defaultMessage="活动名称："
                    ></FormattedMessage>
                  </p>
                  <p
                    title={
                      intelligentAgentItem?.marketingActivityInfo?.activityName
                    }
                    className={styles.intentClassificationText}
                    style={{ color: '#333' }}
                  >
                    {intelligentAgentItem?.marketingActivityInfo?.activityName
                      ? intelligentAgentItem?.marketingActivityInfo
                          ?.activityName
                      : '--'}
                  </p>
                </div>
                <div className={styles.topItem}>
                  <p className={styles.labelText}>
                    <FormattedMessage
                      id="statistics.data.details.channel.type"
                      defaultMessage="渠道类型："
                    ></FormattedMessage>
                  </p>
                  <div className={styles.intentClassificationImg}>
                    {intelligentAgentItem.channelIds?.split(',').map(id => {
                      const icon = channelIconMap[id];
                      return icon ? <img key={id} src={icon} /> : null;
                    })}
                  </div>
                </div>
                <div className={styles.topItem}>
                  <p className={styles.labelText}>
                    <FormattedMessage
                      id="marketing.agent.channel.name"
                      defaultMessage="渠道名称："
                    ></FormattedMessage>
                  </p>
                  <p className={currentStyles.channelName}>{'WhatsApp'}</p>
                </div>
                <div className={styles.topItem}>
                  <p
                    className={styles.labelText}
                    title={getIntl().formatMessage({
                      id: 'external.intelligent.agent.deployment.status',
                      defaultValue: '部署状态：',
                    })}
                  >
                    <FormattedMessage
                      id="external.intelligent.agent.deployment.status"
                      defaultMessage="部署状态："
                    ></FormattedMessage>
                  </p>
                  {+intelligentAgentItem.deployStatus ? (
                    <p
                      title={
                        intelligentAgentItem.crmAiAgentDeployVo?.deployName
                      }
                      className={styles.deployStatusText}
                    >
                      <img src={DeployIcon} />
                      {intelligentAgentItem.crmAiAgentDeployVo?.deployName
                        ? getIntl().formatMessage(
                            { id: 'intelligent.agent.deployed.text' },
                            {
                              deployName:
                                intelligentAgentItem.crmAiAgentDeployVo
                                  ?.deployName,
                            },
                          )
                        : '--'}
                    </p>
                  ) : (
                    <p className={styles.intentClassificationText}>
                      <img src={DraftIcon} />
                      <FormattedMessage
                        id="emailMarketing.form.status.op.1"
                        defaultMessage="草稿"
                      />
                    </p>
                  )}
                </div>
                <div className={styles.topItem}>
                  <p
                    className={styles.labelText}
                    title={getIntl().formatMessage({
                      id: 'external.intelligent.agent.deployment.time',
                      defaultValue: '部署时间：',
                    })}
                  >
                    <FormattedMessage
                      id="external.intelligent.agent.deployment.time"
                      defaultMessage="部署时间："
                    ></FormattedMessage>
                  </p>
                  <p
                    title={intelligentAgentItem.crmAiAgentDeployVo?.createTime}
                    className={styles.createInformationText}
                  >
                    {intelligentAgentItem.crmAiAgentDeployVo?.createTime
                      ? intelligentAgentItem.crmAiAgentDeployVo?.createTime
                      : '--'}
                  </p>
                </div>
                <div
                  className={`${
                    currentStyles.statusContainer
                  } ${getStatusClassName(intelligentAgentItem.aiAgentStatus)}`}
                  onClick={() => deployIntelligentAgent(intelligentAgentItem)}
                >
                  {getStatusText(intelligentAgentItem.aiAgentStatus)}
                </div>
                <div className={styles.footerItemContainer}>
                  <Row gutter={24}>
                    <Col span={6}>
                      <div
                        className={`${styles.intentionEditorContainer} ${
                          !canEdit ? styles.disabled : ''
                        }`}
                        {...(canEdit
                          ? {
                              onClick: () =>
                                handleEditorIntention(intelligentAgentItem),
                            }
                          : {})}
                        style={
                          !canEdit
                            ? { cursor: 'not-allowed', opacity: 0.5 }
                            : {}
                        }
                      >
                        <img src={NewEditorNormalIcon} />
                        <span>
                          <FormattedMessage
                            id="marketing.activities.editor"
                            defaultValue="修改"
                          ></FormattedMessage>
                        </span>
                      </div>
                    </Col>
                    <Col span={6}>
                      <div
                        onClick={() => queryCopyAiAgent(intelligentAgentItem)}
                        className={styles.intelligentAgentContainer}
                      >
                        <img src={NewCopyIcon} />
                        <span>
                          <FormattedMessage
                            id="knowledge.QA.tabs.3.list.btn.tips1"
                            defaultMessage="复制"
                          ></FormattedMessage>
                        </span>
                      </div>
                    </Col>
                    <Col span={6}>
                      <div
                        className={styles.shareCodeContainer}
                        onClick={() => {
                          shareCodeHandle(intelligentAgentItem);
                        }}
                      >
                        <ShareCodeIcon />
                        <span>
                          <FormattedMessage
                            id="external.intelligent.agent.share"
                            defaultMessage="分享"
                          ></FormattedMessage>
                        </span>
                      </div>
                    </Col>
                    <Col span={6}>
                      <div
                        className={styles.intentionDeleteContainer}
                        onClick={() =>
                          handleShowDeleteModal(intelligentAgentItem)
                        }
                      >
                        <img src={NewDeleteIcon} />
                        <span>
                          <FormattedMessage
                            id="user.management.operation.remove"
                            defaultValue="删除"
                          ></FormattedMessage>
                        </span>
                      </div>
                    </Col>
                  </Row>
                </div>
              </div>
            );
          })}
        </div>
        <div className={styles.pagination}>
          <Pagination
            onChange={handleChangePage}
            current={pageNum}
            pageSize={pageSize}
            showTotal={showTotal}
            total={total}
            showSizeChanger
            pageSizeOptions={[8, 16, 24, 32]}
          />
        </div>

        {/*删除智能体*/}
        <Modal
          title={getIntl().formatMessage({
            id: 'external.intelligent.agent.delete.synonym.rules',
            defaultValue: '删除提示',
          })}
          open={deleteIntention}
          onCancel={closeDeleteModal}
          className="deleteExternalModal"
          confirmLoading={loadingAddBtn}
          okButtonProps={{
            htmlType: 'submit',
            form: 'basic2',
          }}
        >
          <p>
            <img src={DeleteTipsIcon} />
            <FormattedMessage
              id="external.intelligent.agent.delete.tips"
              defaultMessage="您确定要删除这个智能体么？"
            />
          </p>
          <Form
            name="basic2"
            autoComplete="off"
            labelAlign="right"
            ref={formDeleteIntentionRef}
            onFinish={deleteIntentionModal}
          >
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  label={null}
                  name="deleteTips"
                  rules={[
                    {
                      required: true,
                      message: (
                        <FormattedMessage
                          id="document.knowledge.base.click.delete.required"
                          defaultValue='请输入"Delete"并点击确定'
                        />
                      ),
                    },
                    {
                      pattern: 'Delete',
                      message: getIntl().formatMessage({
                        id: 'document.knowledge.base.click.delete.required',
                        defaultValue: '请输入"Delete"并点击确定',
                      }),
                    },
                  ]}
                >
                  <Input
                    name="deleteTips"
                    placeholder={getIntl().formatMessage({
                      id: 'document.knowledge.base.click.delete.required',
                      defaultValue: '请输入"Delete"并点击确定',
                    })}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Modal>
      </div>
      <ShareCodeModal
        visible={shareCodeModalVisible}
        onClose={() => {
          setShareCodeModalVisible(false);
        }}
        value={shareCode}
        expiryTime={shareCodeExpiryTime}
        note={getIntl().formatMessage(
          { id: 'external.intelligent.agent.share.tips' },
          { expiryTime: shareCodeExpiryTime },
        )}
        title={getIntl().formatMessage({
          id: 'shareComponents.modal.subTitle',
        })}
        onCopy={() => {
          if (shareCode) {
            const shareContent = getIntl().formatMessage(
              { id: 'external.intelligent.agent.share.content' },
              {
                username: userName,
                shareCode: shareCode,
                expiryTime: shareCodeExpiryTime,
              },
            );
            navigator.clipboard.writeText(shareContent).then(() => {
              notification.success({
                message: getIntl().formatMessage({
                  id: 'external.intelligent.agent.share.success',
                  defaultValue: '复制成功',
                }),
              });
            });
          }
        }}
      />
    </Spin>
  );
};

export default MarketingAgentList;
