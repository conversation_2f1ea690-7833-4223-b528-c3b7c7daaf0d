import React, { useState, useEffect, useCallback } from 'react';
import { Steps, Button, Form, notification, Popconfirm, Spin } from 'antd';
import { FormattedMessage, getIntl, history } from 'umi';
import BasicInfo from './components/BasicInfo';
import TargetCustomer from './components/TargetCustomer';
import styles from './index.less';
import { useAsyncDispatchWithLoading } from '@/hooks/useAsyncDispatchWithLoading';
import AddCustomerGroup from '@/pages/customerDataGroupManagement/addCustomerGroup/index';

const CreateMarketingAgent = () => {
  const [current, setCurrent] = useState(0);
  const [basicForm] = Form.useForm();
  const [targetCustomerForm] = Form.useForm();
  const [basicInfoData, setBasicInfoData] = useState(null);
  const [targetCustomerData, setTargetCustomerData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [intentId, setIntentId] = useState('');
  const [showAddCustomerGroup, setShowAddCustomerGroup] = useState(false);

  // 编辑状态管理
  const [isEdit, setIsEdit] = useState(false);
  const [aiAgentId, setAiAgentId] = useState('');
  const [editItemData, setEditItemData] = useState(null);

  // 数据状态
  const [whatsappChannels, setWhatsappChannels] = useState([]);
  const [marketingActivities, setMarketingActivities] = useState([]);
  const [customerSegmentOptions, setCustomerSegmentOptions] = useState([]);

  const {
    loading: dataLoading,
    error,
    dispatchAllWithLoading,
  } = useAsyncDispatchWithLoading({
    onError: error => {
      notification.error({
        message: error.message,
      });
    },
  });

  // 数据转换工具函数
  const momentToTimeString = useCallback(momentObj => {
    return momentObj ? momentObj.format('HH:mm:ss') : '';
  }, []);

  // 转换为后端格式
  const convertToBackendFormat = useCallback(
    formData => {
      return {
        customerSegmentId: formData.customerSegmentId,
        frequencyType: formData.frequencyType,
        dailyTimes: formData.dailyTimes?.map(momentToTimeString) || [],
        weeklyDays: formData.weeklyDays || [],
        weeklyTimes: formData.weeklyTimes?.map(momentToTimeString) || [],
        monthlyDay: formData.monthlyDay,
        monthlyTime: momentToTimeString(formData.monthlyTime),
        customTimes:
          formData.customTimes?.map(moment =>
            moment.format('YYYY-MM-DD HH:mm:ss'),
          ) || [],
        useRecipientTimezone: formData.useRecipientTimezone,
      };
    },
    [momentToTimeString],
  );

  // 检查是否为编辑状态
  useEffect(() => {
    if (history.location.state) {
      const { itemData, status, newIntentId } = history.location.state;

      if (status === 'editor' && itemData) {
        setIsEdit(true);
        setAiAgentId(itemData.aiAgentId);
        setEditItemData(itemData);
      }

      if (newIntentId) {
        setIntentId(newIntentId);
      }
    } else {
      let selectIntentItem = JSON.parse(
        localStorage.getItem('selectIntentItem'),
      );
      if (selectIntentItem) {
        let intentId = selectIntentItem.intentId;
        setIntentId(intentId);
      }
    }
  }, [history.location.state]);

  // 获取客户细分数据的函数
  const fetchCustomerSegmentOptions = useCallback(async () => {
    try {
      const [customerSubdivisionResponse] = await dispatchAllWithLoading([
        {
          type: 'marketingActivities/queryCustomerSubdivision',
          payload: { subdivisionName: '' },
        },
      ]);
      if (
        customerSubdivisionResponse &&
        customerSubdivisionResponse.code === 200
      ) {
        setCustomerSegmentOptions(customerSubdivisionResponse.data);
      } else {
        notification.error({
          message: customerSubdivisionResponse?.msg || '获取客户细分数据失败',
        });
      }
    } catch (error) {
      console.error('客户细分数据加载失败:', error);
    }
  }, [dispatchAllWithLoading]);

  // 处理编辑数据回显
  const handleEditDataInit = useCallback(() => {
    if (isEdit && editItemData && marketingActivities.length > 0) {
      // 构造BasicInfo需要的数据格式
      const basicInfoEditData = {
        marketingId: editItemData.marketingActivityInfo?.activityId,
        aiAgentName: editItemData.aiAgentName,
        channelTypeId: '1', // 固定为WhatsApp
        channelConfigId: editItemData.channelConfigId,
        interactionType: 1, // 固定为一次性营销
      };

      setBasicInfoData(basicInfoEditData);

      // 如果有目标客户数据，也进行回显
      if (editItemData.toTaskRpcRequest) {
        const targetCustomerEditData = {
          customerSegmentId: editItemData.toTaskRpcRequest.customerSegmentId,
          frequencyType: editItemData.toTaskRpcRequest.frequencyType,
          dailyTimes: editItemData.toTaskRpcRequest.dailyTimes,
          weeklyDays: editItemData.toTaskRpcRequest.weeklyDays,
          weeklyTimes: editItemData.toTaskRpcRequest.weeklyTimes,
          monthlyDay: editItemData.toTaskRpcRequest.monthlyDay,
          monthlyTime: editItemData.toTaskRpcRequest.monthlyTime,
          customTimes: editItemData.toTaskRpcRequest.customTimes,
          useRecipientTimezone:
            editItemData.toTaskRpcRequest.useRecipientTimezone,
        };
        setTargetCustomerData(targetCustomerEditData);
      }
    }
  }, [isEdit, editItemData, marketingActivities]);

  // 获取基本信息需要的数据
  useEffect(() => {
    const fetchBasicInfoData = async () => {
      try {
        const [
          channelResponse,
          marketingResponse,
          customerSubdivisionResponse,
        ] = await dispatchAllWithLoading([
          {
            type: 'channel/queryChannelTypeContact',
            payload: { channelType: '4' },
          },
          {
            type: 'marketingActivities/queryMarketingList',
            payload: {},
          },
          {
            type: 'marketingActivities/queryCustomerSubdivision',
            payload: { subdivisionName: '' },
          },
        ]);
        setWhatsappChannels(channelResponse.data);
        setMarketingActivities(marketingResponse.data);
        setCustomerSegmentOptions(customerSubdivisionResponse.data);
      } catch (error) {
        console.error('基本信息数据加载失败:', error);
      }
    };

    fetchBasicInfoData();
  }, []);

  // 在获取到营销活动数据后处理编辑数据回显
  useEffect(() => {
    handleEditDataInit();
  }, [handleEditDataInit]);

  // 返回列表页
  const back = () => {
    history.replace('/marketingAgent');
  };

  // 下一步
  const handleNext = async () => {
    if (current === 0) {
      try {
        const values = await basicForm.validateFields();
        console.log(values, 'values');
        values.channelIds = '4';
        setBasicInfoData(values);
        setCurrent(1);
      } catch (errorInfo) {
        console.log('Validation failed:', errorInfo);
      }
    }
  };

  // 点击步骤
  const handleStepClick = step => {
    if (step === 0) {
      // 可以回到第一步
      setCurrent(0);
      // 保存当前第二步的数据
      if (current === 1) {
        try {
          const values = targetCustomerForm.getFieldsValue();
          setTargetCustomerData(values);
        } catch (error) {
          console.log('保存目标客户数据失败:', error);
        }
      }
    } else if (step === 1 && basicInfoData) {
      // 只有第一步完成后才能点击第二步
      setCurrent(1);
    }
  };

  // 保存目标客户数据
  const handleTargetCustomerSave = data => {
    setTargetCustomerData(data);
  };

  // 处理添加客户细分
  const handleAddCustomerSegment = () => {
    // 清除URL中的query参数 因为组件内部 useEffect 监听query参数会直接跳转页面
    history.replace(history.location.pathname);
    setShowAddCustomerGroup(true);
  };

  // 新建客户细分callback
  const backCustomer = value => {
    // 回到营销智能体创建页面
    setShowAddCustomerGroup(false);

    // 重新获取客户细分数据
    fetchCustomerSegmentOptions();

    if (value) {
      // 默认选择新创建的客户细分
      targetCustomerForm.setFieldValue('customerSegmentId', value);
    }
  };

  // 完成创建/修改
  const handleFinish = async () => {
    setLoading(true);
    try {
      const values = await targetCustomerForm.validateFields();
      const targetCustomerData = convertToBackendFormat(values);

      // 通过活动id获取活动开始结束时间
      const taskStartTime = marketingActivities.find(
        item => item.activityId === basicInfoData.marketingId,
      ).activityStartTime;
      const taskEndTime = marketingActivities.find(
        item => item.activityId === basicInfoData.marketingId,
      ).activityEndTime;
      const createData = {
        ...basicInfoData,
        isMarketing: 1, // 是否是营销智能体
        toTaskRpcRequest: {
          ...targetCustomerData,
          taskStartTime,
          taskEndTime,
        },
      };

      // 如果是编辑状态，添加aiAgentId
      if (isEdit && aiAgentId) {
        createData.aiAgentId = aiAgentId;
      }

      const [response] = await dispatchAllWithLoading([
        {
          type: 'intentionManagement/saveAgentInfo',
          payload: createData,
        },
      ]);

      setLoading(false);
      history.push({
        pathname: '/agentFlow',
        state: {
          channelId: basicInfoData.channelConfigId,
          aiAgentStatus: 'create',
        },
      });
      localStorage.setItem('aiAgentId', response.data);
      localStorage.setItem('aiAgentStatus', 'create');
    } catch (error) {
      setLoading(false);
    }
  };
  // 如果显示添加客户细分页面，则覆盖整个组件
  if (showAddCustomerGroup) {
    return (
      <div>
        <AddCustomerGroup
          emailMarketing={true}
          backCustomer={value => backCustomer(value)}
        />
      </div>
    );
  }

  return (
    <Spin spinning={dataLoading}>
      <div className={styles.createMarketingAgent}>
        {/* 页面头部 */}
        <div className={styles.header}>
          <div className={styles.headerLeft}>
            <div className="blueBorder">
              <FormattedMessage
                id={
                  isEdit
                    ? 'marketing.agent.edit.title'
                    : 'marketing.agent.create.title'
                }
                defaultValue={isEdit ? '修改营销智能体' : '添加营销智能体'}
              />
            </div>
          </div>
        </div>

        {/* 步骤内容 */}
        <div className={styles.stepContent}>
          <Steps
            current={current}
            onChange={handleStepClick}
            direction="vertical"
            className={styles.steps}
            items={[
              {
                key: 'basic-info',
                title: getIntl().formatMessage({
                  id: 'marketing.agent.create.step.basic.info',
                  defaultValue: '营销基本信息',
                }),
                description: (
                  <BasicInfo
                    form={basicForm}
                    isEditing={current === 0}
                    data={basicInfoData}
                    onDataChange={setBasicInfoData}
                    intentId={intentId}
                    whatsappChannels={whatsappChannels}
                    marketingActivities={marketingActivities}
                  />
                ),
                status:
                  current === 0
                    ? 'process'
                    : current > 0 || basicInfoData
                    ? 'finish'
                    : 'wait',
              },
              {
                key: 'target-customer',
                title: getIntl().formatMessage({
                  id: 'marketing.agent.create.step.target.customer',
                  defaultValue: '目标客户和营销时间',
                }),
                description: current >= 1 && (
                  <TargetCustomer
                    form={targetCustomerForm}
                    isEditing={current === 1}
                    data={targetCustomerData}
                    onDataChange={handleTargetCustomerSave}
                    customerSegmentOptions={customerSegmentOptions}
                    openCustomerGroup={handleAddCustomerSegment}
                  />
                ),
                status:
                  current === 1
                    ? 'process'
                    : current > 1 ||
                      (current < 1 && basicInfoData && targetCustomerData)
                    ? 'finish'
                    : current < 1 && !basicInfoData
                    ? 'wait'
                    : 'wait',
              },
            ]}
          />
        </div>

        {/* 底部按钮 */}
        <div className={styles.footer}>
          <div className={styles.buttonGroup}>
            <Popconfirm
              title={getIntl().formatMessage({
                id: 'emailMarketingEvent.back.Popconfirm',
              })}
              okText={getIntl().formatMessage({
                id: 'user.management.operation.btn.yes',
              })}
              cancelText={getIntl().formatMessage({
                id: 'user.management.operation.btn.no',
              })}
              onConfirm={() => back()}
            >
              <Button>
                <FormattedMessage
                  id="marketing.agent.create.cancel"
                  defaultValue="取消"
                />
              </Button>
            </Popconfirm>
            {current < 1 && (
              <Button type="primary" onClick={handleNext}>
                <FormattedMessage
                  id="marketing.agent.create.next"
                  defaultValue="下一步"
                />
              </Button>
            )}
            {current === 1 && (
              <Button type="primary" onClick={handleFinish} loading={loading}>
                <FormattedMessage
                  id="marketing.agent.create.finish"
                  defaultValue="保存"
                />
              </Button>
            )}
          </div>
        </div>
      </div>
    </Spin>
  );
};

export default CreateMarketingAgent;
