import React, { useEffect, useState } from 'react';
import { Form, Input, Select, Radio } from 'antd';
import { FormattedMessage, getIntl } from 'umi';
import styles from './BasicInfo.less';

const WhatsAppIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="40"
    height="40"
    viewBox="0 0 40 40"
    fill="none"
  >
    <g clipPath="url(#clip0_4455_16834)">
      <g filter="url(#filter0_d_4455_16834)">
        <path
          d="M24.8443 21.7414C25.2243 21.7414 28.8614 23.6386 29.0186 23.9071C29.0629 24.0186 29.0629 24.1528 29.0629 24.2414C29.0629 24.8 28.8843 25.4243 28.6829 25.9386C28.17 27.1886 26.0929 27.9914 24.8214 27.9914C23.75 27.9914 21.54 27.0543 20.58 26.6071C17.3886 25.1557 15.4014 22.6786 13.4814 19.8443C12.6329 18.5943 11.8743 17.0543 11.8971 15.5143V15.3357C11.9414 13.8628 12.4771 12.8128 13.5486 11.8086C13.8829 11.4957 14.24 11.3171 14.7086 11.3171C14.9757 11.3171 15.2443 11.3843 15.5343 11.3843C16.1371 11.3843 16.2486 11.5628 16.4714 12.1428C16.6271 12.5228 17.7657 15.5586 17.7657 15.7814C17.7657 16.63 16.2257 17.59 16.2257 18.1028C16.2257 18.2143 16.27 18.3257 16.3371 18.4371C16.8286 19.4857 17.7657 20.6914 18.6143 21.4957C19.6414 22.4786 20.7343 23.1257 21.9843 23.75C22.1311 23.8451 22.3009 23.8989 22.4757 23.9057C23.1457 23.9057 24.2614 21.74 24.8414 21.74L24.8443 21.7414ZM20.3129 33.5714C28.0357 33.5714 34.33 27.2771 34.33 19.5543C34.33 11.8314 28.0357 5.53712 20.3129 5.53712C12.59 5.53712 6.29571 11.8314 6.29571 19.5543C6.29571 22.5014 7.23286 25.38 8.97429 27.7686L7.21143 32.97L12.6129 31.2514C14.9003 32.7562 17.5763 33.5628 20.3143 33.5728L20.3129 33.5714ZM20.3129 2.72284C29.5986 2.72284 37.1429 10.2671 37.1429 19.5528C37.1429 28.8386 29.5986 36.3828 20.3129 36.3828C17.4786 36.3828 14.6657 35.6686 12.1657 34.2843L2.85714 37.2757L5.89286 28.2357C4.31166 25.6158 3.47818 22.6129 3.48286 19.5528C3.48286 10.2671 11.0271 2.72284 20.3129 2.72284Z"
          fill="#37C837"
        />
      </g>
    </g>
    <defs>
      <filter
        id="filter0_d_4455_16834"
        x="2.85714"
        y="2.72284"
        width="38.2857"
        height="38.5529"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dx="2" dy="2" />
        <feGaussianBlur stdDeviation="1" />
        <feComposite in2="hardAlpha" operator="out" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0.2 0 0 0 0 0.2 0 0 0 0 0.2 0 0 0 0.2 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_4455_16834"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_4455_16834"
          result="shape"
        />
      </filter>
      <clipPath id="clip0_4455_16834">
        <rect width="40" height="40" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

const BasicInfo = ({
  form,
  isEditing,
  data,
  whatsappChannels,
  marketingActivities,
}) => {
  // 数据回显 - 当data变化时设置表单值
  useEffect(() => {
    if (data && isEditing && marketingActivities.length > 0) {
      form.setFieldsValue({
        marketingId: data.marketingId,
        aiAgentName: data.aiAgentName,
        channelTypeId: data.channelTypeId || '1',
        channelConfigId: data.channelConfigId,
        interactionType: data.interactionType || 1,
      });
    }
  }, [data, form, isEditing, marketingActivities]);

  // 获取营销活动详情
  const getActivityDetails = activityValue => {
    return marketingActivities.find(item => item.activityId === activityValue);
  };

  // 获取渠道名称
  const getChannelName = channelValue => {
    const channel = whatsappChannels.find(
      item => item.channelId === channelValue,
    );
    return channel ? channel.channelName : channelValue;
  };

  // 获取交互方式名称
  const getInteractionTypeName = type => {
    const types = {
      1: getIntl().formatMessage({
        id: 'marketing.basic.info.interaction.one.time',
      }),
    };
    return (
      types[type] ||
      getIntl().formatMessage({
        id: 'marketing.basic.info.interaction.one.time',
      })
    );
  };

  // 监听营销活动选择变化，获取活动详情
  const selectedActivity = Form.useWatch('marketingId', form);
  const selectedActivityData = selectedActivity
    ? marketingActivities.find(item => item.activityId === selectedActivity)
    : null;

  const renderActivityDetails = data => {
    return (
      <div className={styles.activityDetails}>
        <div className={styles.detailItem}>
          <span className={styles.detailLabel}>
            <FormattedMessage id="marketing.basic.info.activity.time" />
          </span>
          <span className={styles.detailValue}>{data.activityStartTime}</span>-
          <span className={styles.detailValue}>{data.activityEndTime}</span>
        </div>
        <div className={styles.detailItem}>
          <span className={styles.detailLabel}>
            <FormattedMessage id="marketing.basic.info.activity.target" />
          </span>
          <span className={styles.detailValue}>{data.activityTarget}</span>
        </div>
      </div>
    );
  };
  if (isEditing) {
    // 编辑状态 - 显示表单
    return (
      <Form form={form} layout="vertical" className={styles.form}>
        {/* 选择营销活动 */}
        <div className={styles.formSection}>
          <div className={styles.sectionTitle}>
            <FormattedMessage
              id="marketing.agent.select.activity"
              defaultMessage="选择营销活动"
            />
          </div>
          <Form.Item
            name="marketingId"
            rules={[
              {
                required: true,
                message: getIntl().formatMessage({
                  id: 'marketing.basic.info.activity.required',
                }),
              },
            ]}
          >
            <Select
              placeholder={getIntl().formatMessage({
                id: 'marketing.basic.info.activity.placeholder',
              })}
              showSearch
              allowClear
              className={styles.selectWrapper}
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {marketingActivities.map(activity => (
                <Select.Option
                  key={activity.activityId}
                  value={activity.activityId}
                >
                  {activity.activityName}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          {/* 活动详情显示 */}
          {selectedActivityData && renderActivityDetails(selectedActivityData)}
        </div>

        {/* 营销智能体名称 */}
        <div className={styles.formSection}>
          <div className={styles.sectionTitle}>
            <FormattedMessage
              id="marketing.agent.name"
              defaultMessage="营销智能体名称"
            />
          </div>
          <Form.Item
            name="aiAgentName"
            rules={[
              {
                required: true,
                message: getIntl().formatMessage({
                  id: 'marketing.basic.info.agent.name.required',
                }),
              },
              {
                max: 80,
                message: getIntl().formatMessage({
                  id: 'marketing.basic.info.agent.name.max.length',
                }),
              },
            ]}
          >
            <Input
              className={styles.inputWrapper}
              placeholder={getIntl().formatMessage({
                id: 'marketing.basic.info.agent.name.placeholder',
              })}
            />
          </Form.Item>
        </div>

        {/* 选择营销渠道 */}
        <div className={styles.formSection}>
          <div className={styles.sectionTitle}>
            <FormattedMessage
              id="marketing.agent.select.channel"
              defaultMessage="选择营销渠道"
            />
          </div>

          {/* WhatsApp渠道卡片 - 直接显示，不需要选择 */}
          <div className={styles.whatsappCard}>
            <div className={styles.whatsappIcon}>
              <WhatsAppIcon />
            </div>
            <div className={styles.whatsappText}>WhatsApp</div>
          </div>

          <Form.Item
            name="channelTypeId"
            initialValue="1"
            style={{ display: 'none' }}
          >
            <Input />
          </Form.Item>
        </div>

        {/* 选择WhatsApp渠道 */}
        <div className={styles.formSection}>
          <div className={styles.sectionTitle}>
            <FormattedMessage
              id="marketing.agent.select.whatsapp.channel"
              defaultMessage="选择WhatsApp渠道"
            />
          </div>
          <Form.Item
            name="channelConfigId"
            rules={[
              {
                required: true,
                message: getIntl().formatMessage({
                  id: 'marketing.basic.info.channel.required',
                }),
              },
            ]}
          >
            <Select
              placeholder={getIntl().formatMessage({
                id: 'marketing.basic.info.channel.placeholder',
              })}
              showSearch
              allowClear
              className={styles.selectWrapper}
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {whatsappChannels.map(channel => (
                <Select.Option
                  key={channel.channelId}
                  value={channel.channelId}
                >
                  {channel.channelName}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </div>

        {/* 交互方式 */}
        <div className={styles.formSection}>
          <div className={styles.sectionTitle}>
            <FormattedMessage
              id="marketing.agent.interaction.type"
              defaultMessage="交互方式"
            />
          </div>
          <Form.Item
            name="interactionType"
            initialValue={1}
            rules={[
              {
                required: true,
                message: getIntl().formatMessage({
                  id: 'marketing.basic.info.interaction.required',
                }),
              },
            ]}
          >
            <Radio.Group className={styles.interactionOptions}>
              <div className={styles.interactionOption}>
                <Radio value={1}>
                  <FormattedMessage
                    id="marketing.agent.interaction.type.one.time"
                    defaultMessage="一次性营销"
                  />
                </Radio>
              </div>
            </Radio.Group>
          </Form.Item>
        </div>
      </Form>
    );
  } else {
    // 展示状态 - 显示文字内容
    if (!data) return null;

    const selectedActivityData = getActivityDetails(data.marketingId);

    return (
      <div className={styles.basicInfoSection}>
        <div className={styles.sectionContent}>
          <div className={styles.displayContent}>
            <div className={styles.displayItem}>
              <span className={styles.displayLabel}>
                <FormattedMessage id="marketing.basic.info.display.activity" />
              </span>
              <span className={styles.displayValue}>
                {selectedActivityData.activityName}
              </span>
            </div>
            {renderActivityDetails(selectedActivityData)}
            <div className={styles.displayItem}>
              <span className={styles.displayLabel}>
                <FormattedMessage id="marketing.basic.info.display.agent.name" />
              </span>
              <span className={styles.displayValue}>{data.aiAgentName}</span>
            </div>
            <div className={styles.displayItem}>
              <span className={styles.displayLabel}>
                <FormattedMessage id="marketing.basic.info.display.channel.type" />
              </span>
              <span className={styles.displayValue}>WhatsApp</span>
            </div>
            <div className={styles.displayItem}>
              <span className={styles.displayLabel}>
                <FormattedMessage id="marketing.basic.info.display.whatsapp.channel" />
              </span>
              <span className={styles.displayValue}>
                {getChannelName(data.channelConfigId)}
              </span>
            </div>
            <div className={styles.displayItem}>
              <span className={styles.displayLabel}>
                <FormattedMessage id="marketing.basic.info.display.interaction.type" />
              </span>
              <span className={styles.displayValue}>
                {getInteractionTypeName(data.interactionType)}
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  }
};

export default BasicInfo;
