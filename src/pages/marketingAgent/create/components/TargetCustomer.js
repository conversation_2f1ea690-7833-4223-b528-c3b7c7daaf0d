import React, { useEffect, useCallback } from 'react';
import {
  Select,
  Checkbox,
  TimePicker,
  DatePicker,
  InputNumber,
  Form,
} from 'antd';
import { CloseOutlined, PlusOutlined } from '@ant-design/icons';
import DeleteTipsIcon from '@/assets/delete-tips-icon.png';
import { FormattedMessage, getIntl } from 'umi';
import moment from 'moment';
import styles from './TargetCustomer.less';

const { Option } = Select;
const deleteSvg = (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M16.4062 5.625H13.4375V4.53125C13.4375 3.75 12.8125 3.125 12.0312 3.125H7.96875C7.1875 3.125 6.5625 3.75 6.5625 4.53125V5.625H3.59375C3.34375 5.625 3.125 5.84375 3.125 6.09375C3.125 6.34375 3.34375 6.5625 3.59375 6.5625H16.4062C16.6562 6.5625 16.875 6.34375 16.875 6.09375C16.875 5.84375 16.6562 5.625 16.4062 5.625ZM7.5 5.625V4.53125C7.5 4.28125 7.71875 4.0625 7.96875 4.0625H12.0312C12.2812 4.0625 12.5 4.28125 12.5 4.53125V5.625H7.5ZM14.8125 7.5C14.5625 7.5 14.3438 7.71875 14.3438 7.96875V15.0625C14.3438 15.5312 13.9688 15.9375 13.4688 15.9375H6.5C6.03125 15.9375 5.625 15.5625 5.625 15.0625V7.96875C5.625 7.71875 5.40625 7.5 5.15625 7.5C4.90625 7.5 4.6875 7.71875 4.6875 7.96875V15.0625C4.6875 16.0625 5.5 16.875 6.5 16.875H13.5C14.5 16.875 15.3125 16.0625 15.3125 15.0625V7.96875C15.2812 7.71875 15.0625 7.5 14.8125 7.5Z"
      fill="#F22417"
    />
    <path
      d="M8.6875 14.5312V7.96875C8.6875 7.71875 8.46875 7.5 8.21875 7.5C7.96875 7.5 7.75 7.71875 7.75 7.96875V14.5312C7.75 14.7812 7.96875 15 8.21875 15C8.46875 15 8.6875 14.7812 8.6875 14.5312ZM12.25 14.5312V7.96875C12.25 7.71875 12.0313 7.5 11.7813 7.5C11.5313 7.5 11.3125 7.71875 11.3125 7.96875V14.5312C11.3125 14.7812 11.5313 15 11.7813 15C12.0313 15 12.25 14.7812 12.25 14.5312Z"
      fill="#F22417"
    />
  </svg>
);
const TargetCustomer = ({
  form,
  data,
  customerSegmentOptions,
  openCustomerGroup,
}) => {
  const timeStringToMoment = useCallback(timeString => {
    return timeString ? moment(timeString, 'HH:mm:ss') : null;
  }, []);

  const dateTimeStringToMoment = useCallback(dateTimeString => {
    return dateTimeString
      ? moment(dateTimeString, 'YYYY-MM-DD HH:mm:ss')
      : null;
  }, []);

  const handleAddCustomerSegment = () => {
    openCustomerGroup();
  };

  // 发送频率选项
  const sendFrequencyOptions = [
    {
      value: 0,
      label: getIntl().formatMessage({
        id: 'marketing.target.customer.frequency.immediate',
      }),
    },
    {
      value: 1,
      label: getIntl().formatMessage({
        id: 'marketing.target.customer.frequency.daily',
      }),
    },
    {
      value: 2,
      label: getIntl().formatMessage({
        id: 'marketing.target.customer.frequency.weekly',
      }),
    },
    {
      value: 3,
      label: getIntl().formatMessage({
        id: 'marketing.target.customer.frequency.monthly',
      }),
    },
    {
      value: 4,
      label: getIntl().formatMessage({
        id: 'marketing.target.customer.frequency.custom',
      }),
    },
  ];

  // 周几选项
  const weekDayOptions = [
    {
      value: 0,
      label: getIntl().formatMessage({
        id: 'marketing.target.customer.week.sunday',
      }),
    },
    {
      value: 1,
      label: getIntl().formatMessage({
        id: 'marketing.target.customer.week.monday',
      }),
    },
    {
      value: 2,
      label: getIntl().formatMessage({
        id: 'marketing.target.customer.week.tuesday',
      }),
    },
    {
      value: 3,
      label: getIntl().formatMessage({
        id: 'marketing.target.customer.week.wednesday',
      }),
    },
    {
      value: 4,
      label: getIntl().formatMessage({
        id: 'marketing.target.customer.week.thursday',
      }),
    },
    {
      value: 5,
      label: getIntl().formatMessage({
        id: 'marketing.target.customer.week.friday',
      }),
    },
    {
      value: 6,
      label: getIntl().formatMessage({
        id: 'marketing.target.customer.week.saturday',
      }),
    },
  ];

  // 初始化表单数据
  useEffect(() => {
    if (data) {
      const formData = {
        customerSegmentId: data.customerSegmentId,
        frequencyType: data.frequencyType || 0,
        dailyTimes: data.dailyTimes?.map(time => timeStringToMoment(time)) || [
          moment('12:00', 'HH:mm'),
          moment('18:00', 'HH:mm'),
        ],
        weeklyDays: data.weeklyDays || [1, 2, 3, 4, 5],
        weeklyTimes: data.weeklyTimes?.map(time =>
          timeStringToMoment(time),
        ) || [moment('12:00', 'HH:mm')],
        monthlyDay: data.monthlyDay || 16,
        monthlyTime:
          timeStringToMoment(data.monthlyTime) || moment('19:00', 'HH:mm'),
        customTimes: data.customTimes?.map(dateTime =>
          dateTimeStringToMoment(dateTime),
        ) || [moment('2023-12-16 19:00', 'YYYY-MM-DD HH:mm')],
        useRecipientTimezone: data.useRecipientTimezone !== false,
      };
      form.setFieldsValue(formData);
    } else {
      // 设置默认值
      const defaultData = {
        customerSegmentId: null,
        frequencyType: 0,
        dailyTimes: [moment('12:00', 'HH:mm'), moment('18:00', 'HH:mm')],
        weeklyDays: [1, 2, 3, 4, 5],
        weeklyTimes: [moment('12:00', 'HH:mm')],
        monthlyDay: 16,
        monthlyTime: moment('19:00', 'HH:mm'),
        customTimes: [moment('2023-12-16 19:00', 'YYYY-MM-DD HH:mm')],
        useRecipientTimezone: true,
      };
      form.setFieldsValue(defaultData);
    }
  }, [data, form, timeStringToMoment, dateTimeStringToMoment]);

  // 监听发送频率变化
  const frequencyType = Form.useWatch('frequencyType', form);

  // 渲染时间配置UI
  const renderTimeConfiguration = () => {
    switch (frequencyType) {
      case 1: // 每日发送
        return (
          <div className={styles.formGroup}>
            <span className={styles.label}>
              <FormattedMessage id="marketing.target.customer.time.select" />
            </span>
            <Form.List name="dailyTimes">
              {(fields, { add, remove }) => (
                <div className={styles.timeInputGroup}>
                  <div className={styles.timeInputGroupMaxHeight}>
                    {fields.map(({ key, name, ...restField }) => (
                      <div key={key} className={styles.timeInputRow}>
                        <Form.Item
                          {...restField}
                          name={name}
                          rules={[
                            {
                              required: true,
                              message: getIntl().formatMessage({
                                id: 'marketing.target.customer.time.required',
                              }),
                            },
                          ]}
                        >
                          <TimePicker
                            className={styles.timeInput}
                            format="HH:mm"
                            placeholder={getIntl().formatMessage({
                              id: 'marketing.target.customer.time.placeholder',
                            })}
                            needConfirm={true}
                          />
                        </Form.Item>
                        {fields.length > 1 && (
                          <div
                            onClick={() => remove(name)}
                            className={styles.removeTimeBtn}
                          >
                            {deleteSvg}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                  <div
                    className={styles.addTimeBtn}
                    onClick={() => add(moment('12:00', 'HH:mm'))}
                  >
                    <PlusOutlined />
                    <span>
                      <FormattedMessage id="marketing.target.customer.time.add" />
                    </span>
                  </div>
                </div>
              )}
            </Form.List>
          </div>
        );

      case 2: // 每周发送
        return (
          <div className={styles.formGroup}>
            <span className={styles.label}>
              <FormattedMessage id="marketing.target.customer.time.select" />
            </span>
            <div className={styles.weeklyConfig}>
              <Form.Item
                name="weeklyDays"
                rules={[
                  {
                    required: true,
                    message: getIntl().formatMessage({
                      id: 'marketing.target.customer.date.required',
                    }),
                  },
                ]}
              >
                <Checkbox.Group
                  className={styles.weekDaysGroup}
                  options={weekDayOptions}
                />
              </Form.Item>
              <Form.List name="weeklyTimes">
                {(fields, { add, remove }) => (
                  <div className={styles.timeInputGroup}>
                    {fields.map(({ key, name, ...restField }) => (
                      <div key={key} className={styles.timeInputRow}>
                        <Form.Item
                          {...restField}
                          name={name}
                          rules={[
                            {
                              required: true,
                              message: getIntl().formatMessage({
                                id: 'marketing.target.customer.time.required',
                              }),
                            },
                          ]}
                        >
                          <TimePicker
                            className={styles.timeInput}
                            format="HH:mm"
                            placeholder={getIntl().formatMessage({
                              id: 'marketing.target.customer.time.placeholder',
                            })}
                          />
                        </Form.Item>
                        {fields.length > 1 && (
                          <CloseOutlined
                            className={styles.removeTimeBtn}
                            onClick={() => remove(name)}
                          />
                        )}
                      </div>
                    ))}
                    <div
                      className={styles.addTimeBtn}
                      onClick={() => add(moment('12:00', 'HH:mm'))}
                    >
                      <PlusOutlined />
                      <span>
                        <FormattedMessage id="marketing.target.customer.time.add" />
                      </span>
                    </div>
                  </div>
                )}
              </Form.List>
            </div>
          </div>
        );

      case 3: // 每月发送
        return (
          <div className={styles.formGroup}>
            <span className={styles.label}>
              <FormattedMessage id="marketing.target.customer.time.select" />
            </span>
            <div className={styles.monthlyConfig}>
              <div className={styles.monthlyInputGroup}>
                <span className={styles.monthText}>
                  <FormattedMessage id="marketing.target.customer.month.text" />
                </span>
                <Form.Item
                  name="monthlyDay"
                  rules={[
                    {
                      required: true,
                      message: getIntl().formatMessage({
                        id: 'marketing.target.customer.day.required',
                      }),
                    },
                  ]}
                >
                  <InputNumber className={styles.dayInput} min={1} max={31} />
                </Form.Item>
                <span className={styles.dayText}>
                  <FormattedMessage id="marketing.target.customer.day.text" />
                </span>
                <Form.Item
                  name="monthlyTime"
                  rules={[
                    {
                      required: true,
                      message: getIntl().formatMessage({
                        id: 'marketing.target.customer.time.required',
                      }),
                    },
                  ]}
                >
                  <TimePicker
                    className={styles.timeInput}
                    format="HH:mm"
                    placeholder={getIntl().formatMessage({
                      id: 'marketing.target.customer.time.select.placeholder',
                    })}
                  />
                </Form.Item>
              </div>
            </div>
          </div>
        );

      case 4: // 自定义
        return (
          <div className={styles.formGroup}>
            <span className={styles.label}>
              <FormattedMessage id="marketing.target.customer.time.select" />
            </span>
            <Form.List name="customTimes">
              {(fields, { add, remove }) => (
                <div className={styles.customConfig}>
                  {fields.map(({ key, name, ...restField }) => (
                    <div key={key} className={styles.customScheduleRow}>
                      <div className={styles.dateRangeGroup}>
                        <Form.Item
                          {...restField}
                          name={name}
                          rules={[
                            {
                              required: true,
                              message: getIntl().formatMessage({
                                id: 'marketing.target.customer.time.required',
                              }),
                            },
                          ]}
                        >
                          <DatePicker
                            className={styles.dateInput}
                            showTime={{ format: 'HH:mm' }}
                            format="YYYY-MM-DD HH:mm"
                            placeholder={getIntl().formatMessage({
                              id:
                                'marketing.target.customer.custom.placeholder',
                            })}
                          />
                        </Form.Item>
                      </div>
                      {fields.length > 1 && (
                        <CloseOutlined
                          className={styles.removeScheduleBtn}
                          onClick={() => remove(name)}
                        />
                      )}
                    </div>
                  ))}
                  <div
                    className={styles.addTimeBtn}
                    onClick={() =>
                      add(moment('2023-12-16 19:00', 'YYYY-MM-DD HH:mm'))
                    }
                  >
                    <PlusOutlined />
                    <span>
                      <FormattedMessage id="marketing.target.customer.time.add" />
                    </span>
                  </div>
                </div>
              )}
            </Form.List>
          </div>
        );

      default:
        return null;
    }
  };

  // 编辑状态 - 显示表单
  return (
    <div className={styles.targetCustomerSection}>
      <div className={styles.sectionContent}>
        <Form form={form} layout="vertical">
          {/* 客户细分选择 */}
          <div className={styles.formGroup}>
            <div className={styles.labelRow}>
              <span className={styles.label}>
                <FormattedMessage id="marketing.target.customer.segment.select" />
              </span>
              <span
                className={styles.addLink}
                onClick={handleAddCustomerSegment}
                style={{ cursor: 'pointer' }}
              >
                <FormattedMessage id="marketing.target.customer.segment.add" />
              </span>
            </div>
            <Form.Item
              name="customerSegmentId"
              rules={[
                {
                  required: true,
                  message: getIntl().formatMessage({
                    id: 'marketing.target.customer.segment.required',
                  }),
                },
              ]}
              style={{ marginBottom: 0 }}
            >
              <Select
                className={styles.selectInput}
                placeholder={getIntl().formatMessage({
                  id: 'marketing.target.customer.segment.placeholder',
                })}
                showSearch
                allowClear
                filterOption={(input, option) =>
                  option.children.toLowerCase().indexOf(input.toLowerCase()) >=
                  0
                }
              >
                {customerSegmentOptions.map(item => (
                  <Option key={item.subdivisionId} value={item.subdivisionId}>
                    {item.subdivisionName}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </div>

          {/* 发送频率选择 */}
          <div className={styles.formGroup}>
            <span className={styles.label}>
              <FormattedMessage id="marketing.target.customer.frequency.select" />
            </span>
            <Form.Item
              name="frequencyType"
              rules={[
                {
                  required: true,
                  message: getIntl().formatMessage({
                    id: 'marketing.target.customer.frequency.required',
                  }),
                },
              ]}
            >
              <Select className={styles.selectInput}>
                {sendFrequencyOptions.map(item => (
                  <Option key={item.value} value={item.value}>
                    {item.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            <div className={styles.note}>
              <FormattedMessage id="marketing.target.customer.note" />
            </div>
          </div>

          {/* 时间配置 - 根据发送频率动态渲染 */}
          {renderTimeConfiguration()}

          {/* 时区选择 */}
          <div className={styles.formGroup}>
            <span className={styles.label}>
              <FormattedMessage id="marketing.target.customer.timezone.select" />
            </span>
            <div className={styles.timezoneCheckboxGroup}>
              <div className={styles.checkboxOption}>
                <Form.Item name="useRecipientTimezone" valuePropName="checked">
                  <Checkbox disabled={true}>
                    <FormattedMessage id="marketing.target.customer.timezone.recipient" />
                  </Checkbox>
                </Form.Item>
              </div>
            </div>
          </div>
        </Form>
      </div>
    </div>
  );
};

export default TargetCustomer;
