.targetCustomerSection {
  background: #fff;
  margin: 16px 0;
  width: 380px;

  .sectionTitle {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
    padding-bottom: 12px;
  }

  .sectionContent {
    .formGroup {
      margin-bottom: 24px;

      .labelRow {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .label {
          font-size: 14px;
          font-weight: 500;
          color: #333;
        }

        .addLink {
          font-size: 14px;
          color: #1890ff;
          cursor: pointer;

          &:hover {
            color: #40a9ff;
          }
        }
      }

      .label {
        display: block;
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 12px;
      }

      .selectInput {
        width: 100%;
        height: 32px;
      }

      .note {
        font-size: 12px;
        color: #999;
        margin-top: 8px;
      }

      .timeInputGroup {
        .timeInputGroupMaxHeight {
          max-height: 200px;
          overflow-y: scroll;
        }
        .timeInputRow {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 12px;
          width: 380px;
          :global {
            .ant-form-item {
              margin-bottom: 0;
              width: 100%;
            }
          }
          .timeInput {
            width: 100%;
            // height: 36px;
          }

          .removeTimeBtn {
            cursor: pointer;
            width: 20px;
            height: 20px;
          }
        }
      }

      .weeklyConfig {
        .weekDaysGroup {
          display: flex;
          flex-wrap: wrap;
          gap: 12px;
          margin-bottom: 16px;

          .ant-checkbox-wrapper {
            margin-right: 0;
          }
        }
      }

      .monthlyConfig {
        .monthlyInputGroup {
          display: flex;
          align-items: center;
          gap: 12px;
          flex-wrap: wrap;
          :global {
            .ant-form-item {
              margin-bottom: 0;
            }
            .ant-form-item:last-child {
              flex: 1;
            }
          }
          .monthText,
          .dayText {
            color: #333;
            font-size: 14px;
          }

          .dayInput {
            width: 60px;
            // height: 36px;
          }

          .timeInput {
            width: 120px;
            // height: 36px;
          }
        }
      }

      .customConfig {
        .customScheduleRow {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 12px;

          .dateRangeGroup {
            display: flex;
            align-items: center;
            gap: 12px;
            flex: 1;
            width: 380px;
            :global {
              .ant-form-item {
                margin-bottom: 0;
                width: 100%;
              }
            }
            .dateInput {
              width: 100%;
              // height: 36px;
            }

            .timeInput {
              width: 120px;
              // height: 36px;
            }
          }

          .removeScheduleBtn {
            color: #999;
            cursor: pointer;
            font-size: 14px;

            &:hover {
              color: #ff4d4f;
            }
          }
        }
      }

      .timezoneCheckboxGroup {
        .checkboxOption {
          margin-bottom: 16px;
        }

        .fallbackOption {
          display: flex;
          align-items: center;
          gap: 12px;
          flex-wrap: wrap;

          .fallbackLabel {
            color: #666;
            font-size: 14px;
          }

          .timezoneSelect {
            width: 200px;
            // height: 36px;
          }
        }
      }
    }

    .displayContent {
      .displayItem {
        display: flex;
        align-items: flex-start;
        margin-bottom: 16px;
        padding: 12px 0;

        &:last-child {
          margin-bottom: 0;
          border-bottom: none;
        }

        .displayLabel {
          font-weight: 500;
          color: #666;
          min-width: 120px;
          margin-right: 16px;
        }

        .displayValue {
          color: #333;
          flex: 1;
          font-size: 14px;
        }
      }
    }
  }
  .addTimeBtn {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #1890ff;
    cursor: pointer;
    font-size: 14px;
    padding: 8px 0;

    &:hover {
      color: #40a9ff;
    }
  }
}
