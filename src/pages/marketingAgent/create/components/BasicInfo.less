.basicInfoSection {
  background: #fff;
  margin: 16px 0;

  .sectionContent {
    .displayContent {
      .displayItem {
        display: flex;
        align-items: flex-start;
        padding: 12px 0;

        &:last-child {
          margin-bottom: 0;
        }

        .displayLabel {
          font-weight: 500;
          color: #333;
          min-width: 120px;
          margin-right: 16px;
        }

        .displayValue {
          color: #333;
          flex: 1;
          font-size: 14px;
        }
      }
    }
  }
}

.form {
  .formSection {
    margin: 20px 0;

    .sectionTitle {
      font-family: 'Microsoft YaHei UI', sans-serif;
      font-weight: 700;
      font-size: 12px;
      line-height: 14px;
      color: #333333;
      margin-bottom: 10px;
    }

    .selectWrapper {
      position: relative;
      max-width: 520px;
      :global {
        .ant-select {
          width: 100%;
          height: 32px;
          font-size: 12px;
        }
      }
    }

    .activityDetails {
      margin-top: 10px;
      padding: 0 10px;

      .detailItem {
        display: flex;
        align-items: center;
        gap: 4px;
        margin-bottom: 4px;

        .detailLabel {
          font-family: 'Microsoft YaHei', sans-serif;
          font-size: 12px;
          color: #666666;
          white-space: nowrap;
        }

        .detailValue {
          font-family: 'Microsoft YaHei', sans-serif;
          font-size: 12px;
          color: #666666;
        }
      }
    }

    .inputWrapper {
      max-width: 520px;
      .input {
        width: 100%;
        height: 32px;
        border: 1px solid #e6e6e6;
        border-radius: 6px;
        background: #ffffff;
        padding: 0 12px;
        font-family: 'Microsoft YaHei', sans-serif;
        font-size: 12px;

        &::placeholder {
          color: #999999;
        }

        &:focus {
          border-color: #3463fc;
          box-shadow: 0 0 0 2px rgba(52, 99, 252, 0.1);
        }
      }
    }

    .whatsappCard {
      cursor: pointer;
      margin-top: 10px;
      width: 85px;
      height: 92px;
      background: linear-gradient(
        180deg,
        #ffffff 0%,
        rgba(52, 99, 252, 0.05) 100%
      );
      border: 1px solid #3463fc;
      border-radius: 4px;
      box-shadow: 0px 2px 5px 0px rgba(0, 0, 0, 0.15);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 10px;
      padding: 12px 15px;

      .whatsappIcon {
        width: 40px;
        height: 40px;
        background: #ffffff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        svg {
          filter: drop-shadow(2px 2px 2px rgba(51, 51, 51, 0.2));
        }
      }

      .whatsappText {
        font-family: 'Microsoft YaHei', sans-serif;
        font-size: 12px;
        color: #333333;
        text-align: center;
      }
    }

    .interactionOptions {
      display: flex;
      align-items: center;
      label {
        font-size: 12px;
      }
      .interactionOption {
        display: flex;
        align-items: center;

        .interactionText {
          font-family: 'Microsoft YaHei', sans-serif;
          font-size: 12px;
          color: #333333;
          line-height: 22px;
        }
      }
    }
  }
}
