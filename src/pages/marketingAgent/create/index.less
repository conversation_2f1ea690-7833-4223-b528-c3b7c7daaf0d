.createMarketingAgent {
  margin: 20px;
  padding: 24px;
  border-radius: 4px;
  background: #fff;

  .header {
    .headerLeft {
      display: flex;
      align-items: center;
      gap: 16px;
    }
  }

  .stepContent {
    margin-top: 20px;

    // Steps样式
    :global {
      .ant-steps-item-wait
        > .ant-steps-item-container
        > .ant-steps-item-content
        > .ant-steps-item-title,
      .ant-steps-item-finish
        > .ant-steps-item-container
        > .ant-steps-item-content
        > .ant-steps-item-title,
      .ant-steps-item-process
        > .ant-steps-item-container
        > .ant-steps-item-content
        > .ant-steps-item-title {
        color: #333 !important;
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        cursor: pointer;
        line-height: 40px !important;
      }

      .ant-steps-item-subtitle {
        color: #999 !important;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
      }

      .ant-steps-item-process
        > .ant-steps-item-container
        > .ant-steps-item-icon {
        background: #3463fc;
      }

      .ant-steps-item-process .ant-steps-item-icon {
        border-color: #3463fc;
      }

      .ant-steps-item-title {
        display: flex;
        flex-direction: column;
        line-height: 26px !important;

        .ant-steps-item-subtitle {
          margin-left: 0;
        }
      }

      .ant-steps-item-icon {
        width: 40px;
        height: 40px;
        font-size: 20px;
        font-weight: 700;
      }

      .ant-steps-item-icon .ant-steps-icon {
        top: 2.5px;
      }

      .ant-steps-vertical
        > .ant-steps-item
        > .ant-steps-item-container
        > .ant-steps-item-tail {
        left: 20px;
        padding-top: 48px;
      }

      .ant-steps-item-wait .ant-steps-item-icon {
        background: #e6e6e6 !important;
        border-color: #e6e6e6 !important;
        box-shadow: 0px 3px 5px 0px rgba(0, 0, 0, 0.1);
      }

      .ant-steps-item-wait .ant-steps-item-icon > .ant-steps-icon {
        color: #333 !important;
        font-size: 20px;
        font-style: normal;
        font-weight: 400;
      }

      .ant-steps-item-finish .ant-steps-item-icon {
        background-color: #13c825 !important;
        border-color: #13c825 !important;
      }

      .ant-steps-item-finish .ant-steps-item-icon > .ant-steps-icon {
        color: #fff !important;
      }

      .ant-steps-item-finish
        > .ant-steps-item-container
        > .ant-steps-item-tail::after {
        background-color: #e6e6e6 !important;
      }

      .ant-steps-vertical > .ant-steps-item .ant-steps-item-title {
        line-height: 42px;
      }

      .ant-form-item {
        margin-bottom: 15px;
      }

      .ant-select:not(.ant-select-customize-input) .ant-select-selector,
      .ant-input {
        border-radius: 6px;
        background: #fff;
        width: 100%;
        font-size: 12px;
        box-shadow: none !important;
      }
    }
  }

  .footer {
    background: #fff;
    padding: 16px 24px;

    .buttonGroup {
      display: flex;
      justify-content: center;
      gap: 12px;
      margin: 0 auto;
    }
  }
}
