import React, { useState, useRef, useEffect } from 'react';
import styles from './index.less';
import { FormattedMessage, getIntl } from 'umi';

const ShareCodeModal = ({
  visible = false,
  onClose = () => {},
  length = 8,
  onlyNumbers = false,
  mode = 'display', // 'edit' | 'display'
  value = '', // 展示模式下的值
  title = '',
  subTitle = '',
  note = '',
  expiryTime = '',
  onCopy = () => {},
  onConfirm = () => {},
  onComplete = () => {},
}) => {
  const [values, setValues] = useState(new Array(length).fill(''));
  const inputRefs = useRef([]);

  useEffect(() => {
    if (visible && mode === 'edit') {
      // 弹窗打开时聚焦第一个输入框
      setTimeout(() => {
        if (inputRefs.current[0]) {
          inputRefs.current[0].focus();
        }
      }, 100);
    }
  }, [visible, mode]);

  useEffect(() => {
    if (mode === 'display' && value) {
      // 展示模式下设置值
      const displayValues = value
        .padEnd(length, '')
        .slice(0, length)
        .split('');
      setValues(displayValues);
    } else if (mode === 'edit') {
      // 编辑模式下重置
      setValues(new Array(length).fill(''));
    }
  }, [mode, value, length, visible]);

  const handleChange = (index, inputValue) => {
    if (mode === 'display') return;

    // 只允许输入一个字符
    const newValue = inputValue.slice(-1);

    // 如果设置了只允许数字，则过滤非数字字符
    if (onlyNumbers && !/^\d*$/.test(newValue)) {
      return;
    }

    const newValues = [...values];
    newValues[index] = newValue;
    setValues(newValues);

    // 如果输入了字符且不是最后一个输入框，自动跳转到下一个
    if (newValue && index < length - 1) {
      inputRefs.current[index + 1].focus();
    }

    // 检查是否完成输入
    if (newValues.every(val => val !== '')) {
      onComplete(newValues.join(''));
    }
  };

  const handleKeyDown = (index, e) => {
    if (mode === 'display') return;

    // 处理退格键
    if (e.key === 'Backspace') {
      if (values[index] === '' && index > 0) {
        // 如果当前框为空且不是第一个框，跳转到前一个框
        inputRefs.current[index - 1].focus();
      } else {
        // 清空当前框
        const newValues = [...values];
        newValues[index] = '';
        setValues(newValues);
      }
    }
    // 处理左右箭头键
    else if (e.key === 'ArrowLeft' && index > 0) {
      inputRefs.current[index - 1].focus();
    } else if (e.key === 'ArrowRight' && index < length - 1) {
      inputRefs.current[index + 1].focus();
    }
  };

  const handlePaste = e => {
    if (mode === 'display') return;

    e.preventDefault();
    const pasteData = e.clipboardData.getData('text');

    // 正则匹配「」括号内的分享码
    const codeRegex = /「([A-Za-z0-9]{8})」/;
    const match = pasteData.match(codeRegex);

    // 匹配使用提取的分享码；否则使用原始粘贴内容
    const processedData = match ? match[1] : pasteData;
    const pasteValues = processedData.slice(0, length).split('');

    // 如果设置了只允许数字，过滤非数字字符
    const filteredValues = onlyNumbers
      ? pasteValues.filter(val => /^\d$/.test(val))
      : pasteValues;

    const newValues = [...values];
    filteredValues.forEach((val, idx) => {
      if (idx < length) {
        newValues[idx] = val;
      }
    });

    setValues(newValues);

    // 聚焦到下一个空的输入框或最后一个
    const nextEmptyIndex = newValues.findIndex(val => val === '');
    const focusIndex = nextEmptyIndex === -1 ? length - 1 : nextEmptyIndex;
    inputRefs.current[focusIndex].focus();

    // 检查是否完成输入
    if (newValues.every(val => val !== '')) {
      onComplete(newValues.join(''));
    }
  };

  if (!visible) return null;

  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modalContainer}>
        <div className={styles.modalHeader}>
          <h2 className={styles.modalTitle}>{title}</h2>

          <button className={styles.closeButton} onClick={onClose}>
            ×
          </button>
        </div>
        {subTitle && <div className={styles.modalTitleLevel}>{subTitle}</div>}
        <div className={styles.modalContent}>
          <div
            className={styles.inputGroup}
            style={{
              justifyContent: mode === 'edit' ? 'flex-start' : 'center',
              border: mode === 'edit' ? 'none' : '',
              paddingLeft: mode === 'edit' ? '0' : '',
            }}
          >
            {values.map((value, index) => (
              <input
                key={index}
                ref={el => (inputRefs.current[index] = el)}
                type="text"
                value={value}
                onChange={e => handleChange(index, e.target.value)}
                onKeyDown={e => handleKeyDown(index, e)}
                onPaste={handlePaste}
                className={`${styles.codeInput} ${
                  mode === 'display' ? styles.displayMode : styles.editMode
                }`}
                placeholder="_"
                maxLength={1}
                readOnly={mode === 'display'}
              />
            ))}
          </div>
          {/* 备注 */}
          <div
            className={styles.remarks}
            style={{ color: mode === 'edit' ? '#ED7B52' : '#999999' }}
          >
            {note}
          </div>
          <div className={styles.buttonGroup}>
            <button className={styles.button} onClick={onClose}>
              <FormattedMessage
                id="external.intelligent.agent.share.cancel"
                defaultMessage="取消"
              />
            </button>
            <button
              className={styles.button}
              onClick={() => {
                if (mode === 'edit') {
                  onConfirm(values.join(''));
                } else {
                  onCopy();
                }
              }}
            >
              {mode === 'edit' ? (
                <FormattedMessage
                  id="external.intelligent.agent.share.confirm"
                  defaultMessage="确认"
                />
              ) : (
                <FormattedMessage
                  id="external.intelligent.agent.share.copy"
                  defaultMessage="复制"
                />
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ShareCodeModal;
