<svg width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_145_7158)">
<rect x="1" y="1" width="24" height="24" rx="12" fill="white" shape-rendering="crispEdges"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.7466 9.41213C10.14 9.73153 9.39591 9.72694 8.79393 9.40009L8.76742 9.37549C8.49656 9.21929 8.41347 8.88896 8.58185 8.63768C8.75022 8.3864 9.1063 8.30932 9.37717 8.46552C9.49807 8.53097 9.63488 8.56693 9.77482 8.57005C9.98932 8.57837 10.1986 8.50722 10.3565 8.37228C10.5144 8.23733 10.6079 8.04968 10.6165 7.85068C10.6125 6.85081 11.4679 6.02999 12.5452 6H13.4532C14.5513 6 15.4415 6.82583 15.4415 7.84453C15.4397 7.9742 15.4764 8.10182 15.5475 8.21344C15.6531 8.38625 15.8287 8.51272 16.0354 8.56469C16.242 8.61666 16.4625 8.58981 16.6477 8.49012C17.5807 8.01893 18.7482 8.31135 19.2987 9.15415C19.4509 9.41135 19.3574 9.73411 19.0867 9.88581C18.8116 10.0315 18.4615 9.94688 18.298 9.69521C18.0733 9.34031 17.5824 9.21959 17.1978 9.42468C16.5892 9.73385 15.8508 9.7263 15.2497 9.40476C14.6486 9.08323 14.2716 8.49422 14.2551 7.85068C14.2623 7.64914 14.181 7.45353 14.0298 7.30865C13.8786 7.16376 13.6705 7.08202 13.4532 7.08213H12.5452C12.3272 7.08206 12.1185 7.16362 11.9663 7.30831C11.8141 7.453 11.7312 7.64856 11.7366 7.85068C11.7305 8.49773 11.3532 9.09273 10.7466 9.41213ZM11.7366 18.1739C11.7402 18.5858 12.1012 18.9179 12.5452 18.9179L12.5385 18.9302C12.7446 18.9302 12.9349 19.0321 13.0379 19.1976C13.1409 19.3631 13.1409 19.567 13.0379 19.7325C12.9349 19.898 12.7446 20 12.5385 20C11.4404 20 10.5503 19.1742 10.5503 18.1555C10.552 18.0258 10.5154 17.8982 10.4442 17.7866C10.2218 17.4294 9.73038 17.3058 9.34402 17.5099C8.41102 17.9811 7.24355 17.6886 6.69297 16.8458L6.24229 16.1142C5.73437 15.2486 6.04959 14.1656 6.95807 13.6548C7.07848 13.5891 7.17885 13.496 7.24969 13.3843C7.38261 13.2124 7.43038 12.9962 7.38105 12.7896C7.33172 12.5831 7.18995 12.4058 6.99121 12.3021C6.08273 11.7914 5.76751 10.7083 6.27543 9.84277C6.36041 9.72425 6.49282 9.64204 6.64333 9.61434C6.79385 9.58665 6.95005 9.61575 7.07737 9.69521C7.32887 9.85386 7.40179 10.1678 7.24306 10.4084C7.0237 10.765 7.15718 11.219 7.54131 11.4229C7.82991 11.587 8.06747 11.818 8.23058 12.0931C8.7385 12.9587 8.42328 14.0417 7.5148 14.5525C7.13067 14.7564 6.99719 15.2104 7.21655 15.567L7.67386 16.2986C7.77791 16.473 7.95413 16.6004 8.16178 16.6515C8.36944 16.7025 8.59056 16.6728 8.77405 16.5692C9.0699 16.41 9.4063 16.3272 9.74831 16.3294C10.8464 16.3294 11.7366 17.1552 11.7366 18.1739ZM19.0336 13.6486C18.8485 13.5497 18.7136 13.3863 18.6588 13.1946C18.6041 13.003 18.634 12.7991 18.742 12.628C18.8129 12.5163 18.9132 12.4232 19.0336 12.3575C19.3056 12.2059 19.3945 11.8789 19.2325 11.6258C19.0714 11.376 18.7278 11.2868 18.4504 11.4229C17.5419 11.9337 17.2267 13.0167 17.7346 13.8823C17.8959 14.1847 18.1464 14.4384 18.457 14.614C18.6417 14.7117 18.7765 14.8741 18.8313 15.0647C18.8861 15.2553 18.8563 15.4583 18.7486 15.6284L18.298 16.3601C18.1894 16.5313 18.0137 16.6569 17.8075 16.7106C17.601 16.7617 17.3809 16.7328 17.1978 16.6306C16.5881 16.321 15.8484 16.3292 15.2469 16.6522C14.6454 16.9752 14.2693 17.5661 14.2551 18.2108C14.2868 18.4857 14.5369 18.6943 14.835 18.6943C15.1331 18.6943 15.3832 18.4857 15.4149 18.2108C15.4095 17.9422 15.5618 17.692 15.8127 17.5577C16.0635 17.4235 16.3732 17.4263 16.6212 17.5652C17.5542 18.0364 18.7216 17.744 19.2722 16.9012L19.7229 16.1695C20.2794 15.2963 19.9715 14.1702 19.0336 13.6486ZM12.9958 10.6913C11.9873 10.6913 11.0782 11.2552 10.6929 12.1198C10.3076 12.9844 10.522 13.9793 11.2361 14.64C11.9502 15.3006 13.0231 15.4969 13.9541 15.1371C14.8851 14.7774 15.4905 13.9325 15.4878 12.9969C15.4878 12.3844 15.2251 11.797 14.7575 11.3644C14.29 10.9318 13.6562 10.6896 12.9958 10.6913ZM12.9958 14.2328C12.2638 14.2328 11.6703 13.6822 11.6703 13.0031C11.6703 12.3239 12.2638 11.7734 12.9958 11.7734C13.7279 11.7734 14.3214 12.3239 14.3214 13.0031C14.3214 13.6822 13.7279 14.2328 12.9958 14.2328Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.7466 9.41213C10.14 9.73153 9.39591 9.72694 8.79393 9.40009L8.76742 9.37549C8.49656 9.21929 8.41347 8.88896 8.58185 8.63768C8.75022 8.3864 9.1063 8.30932 9.37717 8.46552C9.49807 8.53097 9.63488 8.56693 9.77482 8.57005C9.98932 8.57837 10.1986 8.50722 10.3565 8.37228C10.5144 8.23733 10.6079 8.04968 10.6165 7.85068C10.6125 6.85081 11.4679 6.02999 12.5452 6H13.4532C14.5513 6 15.4415 6.82583 15.4415 7.84453C15.4397 7.9742 15.4764 8.10182 15.5475 8.21344C15.6531 8.38625 15.8287 8.51272 16.0354 8.56469C16.242 8.61666 16.4625 8.58981 16.6477 8.49012C17.5807 8.01893 18.7482 8.31135 19.2987 9.15415C19.4509 9.41135 19.3574 9.73411 19.0867 9.88581C18.8116 10.0315 18.4615 9.94688 18.298 9.69521C18.0733 9.34031 17.5824 9.21959 17.1978 9.42468C16.5892 9.73385 15.8508 9.7263 15.2497 9.40476C14.6486 9.08323 14.2716 8.49422 14.2551 7.85068C14.2623 7.64914 14.181 7.45353 14.0298 7.30865C13.8786 7.16376 13.6705 7.08202 13.4532 7.08213H12.5452C12.3272 7.08206 12.1185 7.16362 11.9663 7.30831C11.8141 7.453 11.7312 7.64856 11.7366 7.85068C11.7305 8.49773 11.3532 9.09273 10.7466 9.41213ZM11.7366 18.1739C11.7402 18.5858 12.1012 18.9179 12.5452 18.9179L12.5385 18.9302C12.7446 18.9302 12.9349 19.0321 13.0379 19.1976C13.1409 19.3631 13.1409 19.567 13.0379 19.7325C12.9349 19.898 12.7446 20 12.5385 20C11.4404 20 10.5503 19.1742 10.5503 18.1555C10.552 18.0258 10.5154 17.8982 10.4442 17.7866C10.2218 17.4294 9.73038 17.3058 9.34402 17.5099C8.41102 17.9811 7.24355 17.6886 6.69297 16.8458L6.24229 16.1142C5.73437 15.2486 6.04959 14.1656 6.95807 13.6548C7.07848 13.5891 7.17885 13.496 7.24969 13.3843C7.38261 13.2124 7.43038 12.9962 7.38105 12.7896C7.33172 12.5831 7.18995 12.4058 6.99121 12.3021C6.08273 11.7914 5.76751 10.7083 6.27543 9.84277C6.36041 9.72425 6.49282 9.64204 6.64333 9.61434C6.79385 9.58665 6.95005 9.61575 7.07737 9.69521C7.32887 9.85386 7.40179 10.1678 7.24306 10.4084C7.0237 10.765 7.15718 11.219 7.54131 11.4229C7.82991 11.587 8.06747 11.818 8.23058 12.0931C8.7385 12.9587 8.42328 14.0417 7.5148 14.5525C7.13067 14.7564 6.99719 15.2104 7.21655 15.567L7.67386 16.2986C7.77791 16.473 7.95413 16.6004 8.16178 16.6515C8.36944 16.7025 8.59056 16.6728 8.77405 16.5692C9.0699 16.41 9.4063 16.3272 9.74831 16.3294C10.8464 16.3294 11.7366 17.1552 11.7366 18.1739ZM19.0336 13.6486C18.8485 13.5497 18.7136 13.3863 18.6588 13.1946C18.6041 13.003 18.634 12.7991 18.742 12.628C18.8129 12.5163 18.9132 12.4232 19.0336 12.3575C19.3056 12.2059 19.3945 11.8789 19.2325 11.6258C19.0714 11.376 18.7278 11.2868 18.4504 11.4229C17.5419 11.9337 17.2267 13.0167 17.7346 13.8823C17.8959 14.1847 18.1464 14.4384 18.457 14.614C18.6417 14.7117 18.7765 14.8741 18.8313 15.0647C18.8861 15.2553 18.8563 15.4583 18.7486 15.6284L18.298 16.3601C18.1894 16.5313 18.0137 16.6569 17.8075 16.7106C17.601 16.7617 17.3809 16.7328 17.1978 16.6306C16.5881 16.321 15.8484 16.3292 15.2469 16.6522C14.6454 16.9752 14.2693 17.5661 14.2551 18.2108C14.2868 18.4857 14.5369 18.6943 14.835 18.6943C15.1331 18.6943 15.3832 18.4857 15.4149 18.2108C15.4095 17.9422 15.5618 17.692 15.8127 17.5577C16.0635 17.4235 16.3732 17.4263 16.6212 17.5652C17.5542 18.0364 18.7216 17.744 19.2722 16.9012L19.7229 16.1695C20.2794 15.2963 19.9715 14.1702 19.0336 13.6486ZM12.9958 10.6913C11.9873 10.6913 11.0782 11.2552 10.6929 12.1198C10.3076 12.9844 10.522 13.9793 11.2361 14.64C11.9502 15.3006 13.0231 15.4969 13.9541 15.1371C14.8851 14.7774 15.4905 13.9325 15.4878 12.9969C15.4878 12.3844 15.2251 11.797 14.7575 11.3644C14.29 10.9318 13.6562 10.6896 12.9958 10.6913ZM12.9958 14.2328C12.2638 14.2328 11.6703 13.6822 11.6703 13.0031C11.6703 12.3239 12.2638 11.7734 12.9958 11.7734C13.7279 11.7734 14.3214 12.3239 14.3214 13.0031C14.3214 13.6822 13.7279 14.2328 12.9958 14.2328Z" fill="#8501BB"/>
</g>
<defs>
<filter id="filter0_d_145_7158" x="0.25" y="0.25" width="25.5" height="25.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.375"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_145_7158"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_145_7158" result="shape"/>
</filter>
</defs>
</svg>
