<svg width="90" height="90" viewBox="0 0 90 90" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_1033_10646)">
<circle cx="45" cy="41" r="25" fill="#3463FC"/>
<rect x="43.9313" y="24.3551" width="1.47659" height="10.3361" fill="white" stroke="#020272" stroke-width="0.671177"/>
<circle cx="44.5884" cy="23.5132" r="2.2528" fill="white" stroke="#020272" stroke-width="0.671177"/>
<g filter="url(#filter1_i_1033_10646)">
<ellipse cx="26.6822" cy="43.556" rx="3.55724" ry="5.10094" fill="white"/>
</g>
<path d="M29.9039 43.556C29.9039 44.9045 29.5222 46.1108 28.9223 46.971C28.3217 47.8323 27.5244 48.3214 26.6822 48.3214C25.8401 48.3214 25.0428 47.8323 24.4422 46.971C23.8423 46.1108 23.4606 44.9045 23.4606 43.556C23.4606 42.2076 23.8423 41.0013 24.4422 40.1411C25.0428 39.2797 25.8401 38.7907 26.6822 38.7907C27.5244 38.7907 28.3217 39.2797 28.9223 40.1411C29.5222 41.0013 29.9039 42.2076 29.9039 43.556Z" stroke="#030178" stroke-width="0.671177"/>
<g filter="url(#filter2_i_1033_10646)">
<ellipse cx="62.6573" cy="43.556" rx="3.55724" ry="5.10094" fill="white"/>
</g>
<path d="M65.879 43.556C65.879 44.9045 65.4973 46.1108 64.8974 46.971C64.2968 47.8323 63.4995 48.3214 62.6573 48.3214C61.8152 48.3214 61.0179 47.8323 60.4173 46.971C59.8174 46.1108 59.4357 44.9045 59.4357 43.556C59.4357 42.2076 59.8174 41.0013 60.4173 40.1411C61.0179 39.2797 61.8152 38.7907 62.6573 38.7907C63.4995 38.7907 64.2968 39.2797 64.8974 40.1411C65.4973 41.0013 65.879 42.2076 65.879 43.556Z" stroke="#030178" stroke-width="0.671177"/>
<g filter="url(#filter3_i_1033_10646)">
<ellipse cx="44.8041" cy="43.0859" rx="18.1889" ry="15.1015" fill="white"/>
</g>
<path d="M62.6574 43.0859C62.6574 51.1851 54.7254 57.8518 44.8041 57.8518C34.8829 57.8518 26.9508 51.1851 26.9508 43.0859C26.9508 34.9866 34.8829 28.32 44.8041 28.32C54.7254 28.32 62.6574 34.9866 62.6574 43.0859Z" stroke="#030178" stroke-width="0.671177"/>
<g filter="url(#filter4_i_1033_10646)">
<path d="M59.1 44.4645C59.1 50.9715 52.6994 49.2727 44.8039 49.2727C36.9084 49.2727 30.5078 50.9715 30.5078 44.4645C30.5078 37.9575 36.9084 32.6826 44.8039 32.6826C52.6994 32.6826 59.1 37.9575 59.1 44.4645Z" fill="#020272"/>
</g>
<path d="M58.7644 44.4645C58.7644 46.0373 58.3785 47.0556 57.7341 47.7289C57.0842 48.4078 56.1228 48.7876 54.8629 48.9768C53.603 49.1661 52.0905 49.1587 50.3799 49.1011C49.9137 49.0854 49.4331 49.066 48.94 49.046C47.6344 48.9933 46.2421 48.9371 44.8039 48.9371C43.3657 48.9371 41.9733 48.9933 40.6677 49.046C40.1747 49.066 39.694 49.0854 39.2279 49.1011C37.5173 49.1587 36.0048 49.1661 34.7449 48.9768C33.485 48.7876 32.5235 48.4078 31.8737 47.7289C31.2293 47.0556 30.8434 46.0373 30.8434 44.4645C30.8434 38.2007 37.0301 33.0182 44.8039 33.0182C52.5777 33.0182 58.7644 38.2007 58.7644 44.4645Z" stroke="#030178" stroke-width="0.671177"/>
<g filter="url(#filter5_d_1033_10646)">
<path d="M40.6447 43.9195C41.0281 43.9866 41.4026 43.7269 41.3727 43.3388C41.3593 43.1642 41.3296 42.9908 41.2837 42.8207C41.1763 42.4229 40.983 42.0535 40.7174 41.7384C40.4519 41.4234 40.1205 41.1704 39.7466 40.9972C39.3726 40.8241 38.9653 40.7351 38.5533 40.7363C38.1412 40.7376 37.7345 40.8292 37.3617 41.0047C36.9889 41.1802 36.659 41.4352 36.3954 41.7519C36.1318 42.0686 35.9409 42.4393 35.836 42.8378C35.7912 43.0081 35.7625 43.1817 35.7502 43.3564C35.7227 43.7447 36.0989 44.002 36.4818 43.9325L36.5695 43.9166C36.9041 43.856 37.1125 43.5253 37.1991 43.1965C37.2515 42.9973 37.347 42.812 37.4788 42.6536C37.6106 42.4953 37.7755 42.3677 37.9619 42.28C38.1483 42.1923 38.3517 42.1465 38.5577 42.1458C38.7637 42.1452 38.9674 42.1897 39.1543 42.2763C39.3413 42.3628 39.507 42.4893 39.6398 42.6468C39.7726 42.8044 39.8692 42.9891 39.9229 43.188C40.0115 43.5162 40.222 43.8456 40.5569 43.9042L40.6447 43.9195Z" fill="#01E3FB"/>
<path d="M52.9943 43.9195C53.3777 43.9866 53.7522 43.7269 53.7223 43.3388C53.7089 43.1642 53.6792 42.9908 53.6333 42.8207C53.5259 42.4229 53.3326 42.0535 53.0671 41.7384C52.8015 41.4234 52.4701 41.1704 52.0962 40.9972C51.7223 40.8241 51.3149 40.7351 50.9029 40.7363C50.4909 40.7376 50.0841 40.8292 49.7113 41.0047C49.3385 41.1802 49.0086 41.4352 48.7451 41.7519C48.4815 42.0686 48.2905 42.4393 48.1856 42.8378C48.1408 43.0081 48.1121 43.1817 48.0998 43.3564C48.0723 43.7447 48.4485 44.002 48.8314 43.9325L48.9191 43.9166C49.2537 43.856 49.4621 43.5253 49.5487 43.1965C49.6011 42.9973 49.6966 42.812 49.8284 42.6536C49.9602 42.4953 50.1251 42.3677 50.3115 42.28C50.4979 42.1923 50.7013 42.1465 50.9073 42.1458C51.1133 42.1452 51.317 42.1897 51.5039 42.2763C51.6909 42.3628 51.8566 42.4893 51.9894 42.6468C52.1222 42.8044 52.2188 42.9891 52.2725 43.188C52.3611 43.5162 52.5716 43.8456 52.9065 43.9042L52.9943 43.9195Z" fill="#01E3FB"/>
</g>
</g>
<defs>
<filter id="filter0_d_1033_10646" x="0" y="0" width="90" height="90" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="10"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.203922 0 0 0 0 0.388235 0 0 0 0 0.988235 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1033_10646"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1033_10646" result="shape"/>
</filter>
<filter id="filter1_i_1033_10646" x="23.125" y="38.1866" width="7.11426" height="10.4706" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3.49012"/>
<feGaussianBlur stdDeviation="0.134235"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.592157 0 0 0 0 0.588235 0 0 0 0 0.682353 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1033_10646"/>
</filter>
<filter id="filter2_i_1033_10646" x="59.1001" y="38.1866" width="7.11426" height="10.4706" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3.49012"/>
<feGaussianBlur stdDeviation="0.134235"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.592157 0 0 0 0 0.588235 0 0 0 0 0.682353 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1033_10646"/>
</filter>
<filter id="filter3_i_1033_10646" x="26.6152" y="27.7159" width="36.3779" height="30.4716" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-3.49012"/>
<feGaussianBlur stdDeviation="0.134235"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.592157 0 0 0 0 0.588235 0 0 0 0 0.682353 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1033_10646"/>
</filter>
<filter id="filter4_i_1033_10646" x="30.5078" y="32.6826" width="28.5923" height="16.9243" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2.41624"/>
<feGaussianBlur stdDeviation="0.0671177"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.501961 0 0 0 0 0.517647 0 0 0 0 0.752941 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1033_10646"/>
</filter>
<filter id="filter5_d_1033_10646" x="31.7215" y="36.8435" width="26.0297" height="11.2621" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.134235"/>
<feGaussianBlur stdDeviation="2.01353"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.00392157 0 0 0 0 0.890196 0 0 0 0 0.984314 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1033_10646"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1033_10646" result="shape"/>
</filter>
</defs>
</svg>
