<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_11852_84592)">
<g filter="url(#filter0_di_11852_84592)">
<path d="M21.25 2.5H8.75C5.29822 2.5 2.5 5.29822 2.5 8.75V21.25C2.5 24.7018 5.29822 27.5 8.75 27.5H21.25C24.7018 27.5 27.5 24.7018 27.5 21.25V8.75C27.5 5.29822 24.7018 2.5 21.25 2.5Z" fill="url(#paint0_linear_11852_84592)" shape-rendering="crispEdges"/>
</g>
<path d="M8 8V12.7591L8.61898 12.7388C8.8321 11.0025 10.2268 9.81777 11.0197 9.62033C11.8142 9.42288 13.3436 9.63756 13.3436 9.63756C13.3436 9.63756 13.3233 19.4347 13.3233 20.1242C13.3233 20.8153 12.394 21.1115 12.394 21.1115L11.156 21.1319L11.1372 22H18.8643V21.1522H17.4509C16.8115 21.0927 16.426 20.2637 16.426 20.2637L16.4433 9.53727C16.4433 9.53727 17.4117 9.3602 18.8064 9.61719C20.201 9.87419 21.381 12.8155 21.381 12.8155H22V8H8Z" fill="#3463FC"/>
</g>
<defs>
<filter id="filter0_di_11852_84592" x="-2.5" y="-2.5" width="35" height="35" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="2.5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.231373 0 0 0 0 0.407843 0 0 0 0 1 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_11852_84592"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_11852_84592" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1.875"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.231373 0 0 0 0 0.407843 0 0 0 0 1 0 0 0 0.38 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_11852_84592"/>
</filter>
<linearGradient id="paint0_linear_11852_84592" x1="-5.625" y1="-7.5" x2="40.625" y2="33.75" gradientUnits="userSpaceOnUse">
<stop stop-color="#344EA6" stop-opacity="0.26"/>
<stop offset="0.505208" stop-color="#587EFF" stop-opacity="0.12"/>
<stop offset="1" stop-color="#829EFF" stop-opacity="0.04"/>
</linearGradient>
<clipPath id="clip0_11852_84592">
<rect width="30" height="30" fill="white"/>
</clipPath>
</defs>
</svg>
