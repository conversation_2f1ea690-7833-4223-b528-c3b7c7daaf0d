<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Frame">
<g id="Subtract" filter="url(#filter0_d_11272_43735)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M30.2783 35C32.8864 35 35.0003 32.886 35.0003 30.278H35V9.72203C35 7.11465 32.886 5 30.278 5H9.72203C7.11398 5 5 7.11465 5 9.72203V30.278C5 32.886 7.11398 35 9.72203 35H30.2783ZM22.9395 13.5831C22.4786 13.5831 22.1634 13.9489 21.9945 14.2249C21.8086 14.5291 21.755 14.8627 21.6931 15.3799C21.6308 15.8351 21.6619 18.6519 21.6619 18.6519H26.2014L25.6323 23.2161H21.6619V34.9999H16.9751V23.2161H13V18.6519H16.9751V14.7656C16.9751 13.0227 17.6078 11.8199 17.9374 11.4099C18.7236 10.334 20.1824 9.25 22.3138 9.25H26.3119V13.5831H22.9395Z" fill="#3463FC"/>
</g>
</g>
<defs>
<filter id="filter0_d_11272_43735" x="5" y="5" width="34.0004" height="34" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.2 0 0 0 0 0.2 0 0 0 0 0.2 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_11272_43735"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_11272_43735" result="shape"/>
</filter>
</defs>
</svg>
