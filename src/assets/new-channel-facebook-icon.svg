<svg width="13" height="12" viewBox="0 0 13 12" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_19321_26099)">
<g filter="url(#filter0_d_19321_26099)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M9.74455 11.0999C10.6313 11.0999 11.35 10.3812 11.35 9.49444H11.3499V2.5055C11.3499 1.619 10.6312 0.900024 9.74444 0.900024H2.7555C1.86877 0.900024 1.15002 1.619 1.15002 2.5055V9.49444C1.15002 10.3812 1.86877 11.0999 2.7555 11.0999H9.74455ZM7.52129 3.81819C7.36458 3.81819 7.25742 3.94256 7.20002 4.0364C7.13681 4.13981 7.11859 4.25324 7.09752 4.42908C7.07633 4.58386 7.08693 5.54154 7.08693 5.54154H8.63033L8.43684 7.09337H7.08693V11.0998H5.49341V7.09337H4.14191V5.54154H5.49341V4.22021C5.49341 3.62766 5.70854 3.21869 5.82061 3.07929C6.0879 2.71348 6.58388 2.34494 7.30855 2.34494H8.66791V3.81819H7.52129Z" fill="#3463FC"/>
</g>
</g>
<defs>
<filter id="filter0_d_19321_26099" x="1.15002" y="0.900024" width="11.4" height="11.3999" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.6" dy="0.6"/>
<feGaussianBlur stdDeviation="0.3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.2 0 0 0 0 0.2 0 0 0 0 0.2 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_19321_26099"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_19321_26099" result="shape"/>
</filter>
<clipPath id="clip0_19321_26099">
<rect width="12" height="12" fill="white" transform="translate(0.25)"/>
</clipPath>
</defs>
</svg>
