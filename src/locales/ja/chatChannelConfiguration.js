export default {
  'add.chat.channel.configuration.title':
    'WEBサイトオンラインチャットチャネルを追加',
  'add.chat.channel.configuration.title.update':
    'WEBサイトオンラインチャットチャネルを編集',

  'app.add.chat.channel.configuration.title.update':
    'APPオンラインチャットチャネルの変更',
  'add.chat.channel.configuration.tips':
    '顧客がWEBサイトを通じて企業に連絡しやすくなり、顧客のニーズに迅速に対応できます。',
  'app.add.chat.channel.configuration.title':
    'APPエンドでのオンラインチャットチャネルを追加',
  'app.add.chat.channel.configuration.tips':
    '顧客がAPPサイトを通じて当社に連絡しやすくなり、顧客ニーズへの対応がより迅速になります',
  'chat.channel.configuration.title.1': 'チャネルの基本情報を入力',
  'chat.channel.configuration.title.tips.1':
    'チャネルの基本情報を入力してください',
  'chat.channel.configuration.title.2': '外観設定',
  'chat.channel.configuration.title.tips.2':
    'ここでチャットウィンドウを設定し、お気に入りのロゴや色を選択できます。',
  'chat.channel.configuration.title.3': '基本機能設定',
  'chat.channel.configuration.title.tips.3':
    'ここでチャットウィンドウの基本機能を設定できます。',
  'chat.channel.configuration.title.4': 'スマートカスタマーサービス設定',
  'chat.channel.configuration.title.tips.4':
    'ここでスマートカスタマーサービスの関連情報を設定できます。',
  'chat.channel.configuration.title.5': 'デプロイ',
  'chat.channel.configuration.title.tips.5':
    '次の説明に従って、ウェブサイトにチャットウィンドウをデプロイしてください。',
  'chat.channel.configuration.channel.name': 'チャネル名',
  'chat.channel.configuration.channel.name.placeholder':
    'チャネル名を入力してください',
  'chat.channel.configuration.chat.types': '言語を選択',
  'chat.channel.configuration.chat.types.placeholder': '言語を選択してください',
  'chat.channel.configuration.chat2.name': '会社名',
  'chat.channel.configuration.chat2.name.placeholder':
    '会社名を入力してください',
  'chat.channel.configuration.chat2.address': '会社住所',
  'chat.channel.configuration.chat2.address.placeholder':
    '会社住所を入力してください',
  'chat.channel.configuration.chat2.logo': '会社ロゴ',
  'chat.channel.configuration.chat2.logo.message1':
    'jpg/pngファイルのみアップロードでき、500kbを超えないようにしてください',
  'chat.channel.configuration.chat2.logo.message2':
    '会在聊天框左上角展示，建议上传50*20px的PNG图片',
  'chat.channel.configuration.chat2.chatBoxName.placeholder':
    'チャットウィンドウ名を入力してください',
  'chat.channel.configuration.chat2.chatBoxName': 'チャットウィンドウ名',
  'chat.channel.configuration.chat2.chatBoxName.message':
    'チャットウィンドウの左上に表示されます',
  'chat.channel.configuration.chat2.templete': 'テーマカラー',
  'chat.channel.configuration.chat2.templete.custom': 'カスタム',
  'chat.channel.configuration.chat2.templete.color': '現在の色：',
  'chat.channel.configuration.chat2.templete.placeholder':
    '色を選択してください',
  'chat.channel.configuration.chat2.boxColor':
    'カスタマーサービスチャットウィンドウの色',
  'chat.channel.configuration.chat2.userBox': 'ユーザーチャットウィンドウの色',
  'chat.channel.configuration.chat2.information.configuration.completed':
    'チャットウィンドウの外観設定が完了しました。プレビューボタンをクリックしてスタイルをプレビューできます。',
  'chat.channel.configuration.work.panels.checkbox': 'オンにする',
  'chat.channel.configuration.chat3.form': 'ユーザー情報フォームページ',
  'chat.channel.configuration.chat3.form.message':
    'オンにすることをお勧めします。オンにすると、ユーザーはまず基本情報を入力してから、後続のコミュニケーションを行う必要があります。',
  'chat.channel.configuration.chat3.welcome': '最初の挨拶',
  'chat.channel.configuration.chat3.welcome.words': '一言の歓迎メッセージ',
  'chat.channel.configuration.chat3.welcome.words.placeholder':
    '歓迎メッセージを入力してください',
  'chat.channel.configuration.chat3.welcome.words.message':
    'ここでの設定は、最初のステップで選択した言語と一致させてください',
  'chat.channel.configuration.chat3.welcome.QA': 'FAQをトリガーする',
  'chat.channel.configuration.chat3.welcome.QA.placeholder':
    'トリガーするFAQを選択してください',
  'chat.channel.configuration.chat3.interval.placeholder':
    '自動招待セッション時間を入力します',
  'chat.channel.configuration.chat3.welcome.QA.message':
    'FAQを使用すると、ユーザーに一度に複数の回答を返すことができます。テキストだけでなく、画像や動画もサポートしています。特定のFAQを選択した場合、ユーザーが連絡してきた際に、システムはFAQに設定された標準回答を自動的に返信します。まだFAQを設定していない場合は、',
  'chat.channel.configuration.chat3.welcome.QA.message.1': 'ここ',
  'chat.channel.configuration.chat3.welcome.QA.message.2':
    'をクリックして設定を行います。',
  'chat.channel.configuration.chat3.talk': 'セッションを自動的に招待',
  'chat.channel.configuration.chat3.talk.ge': 'を置いて',
  'chat.channel.configuration.chat3.talk.ge2':
    '秒後に自動的にチャット・ウィンドウを開きます',
  'chat.channel.configuration.chat3.message':
    'ユーザーがウェブサイトを閲覧している間、システムは自動的にポップアップウィンドウを表示して、ユーザーに相談を促します',
  'chat.channel.configuration.chat3.talk.Input':
    '自動招待セッションのための歓迎メッセージ',
  'chat.channel.configuration.chat3.talk.Input.placeholder':
    '自動招待セッションのための歓迎メッセージを入力',
  'chat.channel.configuration.chat3.talk.Input.message':
    'ここで自動招待セッションがポップアップした際に表示される歓迎メッセージを設定します',

  'chat.channel.configuration.chat3.voice.message':
    'ユーザーは、カスタマーサービスとのコミュニケーション中に、下のビデオコミュニケーションボタンをクリックして音声コミュニケーションを行うことができます',
  'chat.channel.configuration.chat3.voice': 'オンライン音声コミュニケーション',
  'chat.channel.configuration.chat3.video.message':
    '用户在与客服沟通过程中可以点击下方视频沟通按钮进行视频沟通',
  'chat.channel.configuration.chat3.video':
    'オンラインビデオコミュニケーション',
  'chat.channel.configuration.chat3.whatsApp.message':
    'オンにすると、チャットウィンドウの下部にWhatsAppの入り口が表示されます',
  'chat.channel.configuration.chat3.whatsApp': 'WhatsAppチャネルを表示',
  'chat.channel.configuration.chat3.email.message':
    'オンにすると、チャットウィンドウの下部にメールの入り口が表示されます',
  'chat.channel.configuration.chat3.email': 'メールチャネルを表示',
  'chat.channel.configuration.chat3.evaluate.message':
    'チャット終了後、システムは自動的に満足度評価をポップアップします',
  'chat.channel.configuration.chat3.evaluate': '満足度評価',

  'chat.channel.configuration.chat3.email.select': '関連するメールチャネル',
  'chat.channel.configuration.chat3.email.select.placeholder':
    '関連するメールチャネルを選択してください',
  'chat.channel.configuration.chat3.email.select.message':
    'メールを関連付けると、ユーザーはチャットウィンドウの下部にあるメールアイコンをクリックして直接連絡できます',
  'chat.channel.configuration.chat3.WhatsApp.select':
    '関連するWhatsAppチャネル',
  'chat.channel.configuration.chat3.WhatsApp.select.placeholder':
    '関連するWhatsAppチャネルを選択してください',
  'chat.channel.configuration.chat3.whatsApp.select.message':
    'WhatsApp番号を関連付けると、ユーザーはチャットウィンドウの下部にあるWhatsAppアイコンをクリックしてWhatsAppセッションを開始できます',

  'chat.channel.configuration.chat3.information.configuration.completed':
    '基本機能の設定が完了しました。これらは保存後にチャットウィンドウに反映されます。',

  'chat.channel.configuration.chat4.mode.message':
    'スマートカスタマーサービスでは、まずロボットが応答し、ロボットが回答できない質問については、ユーザーはいつでも人間オペレーターに転送できます / 人間オペレーターのみが質問に応答します / ロボットのみが質問に応答します',
  'chat.channel.configuration.chat4.mode.message.1': ` `,
  'chat.channel.configuration.chat4.mode.message.2': ` `,
  'chat.channel.configuration.chat4.mode.1': 'スマートカスタマーサービス',
  'chat.channel.configuration.chat4.mode.2': '人間オペレーターのみ',
  'chat.channel.configuration.chat4.mode.3': 'ロボットのみ',
  'chat.channel.configuration.chat4.mode': 'カスタマーサービスモード',
  'chat.channel.configuration.chat4.robot.message':
    'ロボット名は、ロボットの回答情報の上に表示されます',
  'chat.channel.configuration.chat4.robot.placeholder':
    'ロボット名を入力してください',
  'chat.channel.configuration.chat4.robot': 'ロボット名',
  'chat.channel.configuration.chat4.language.message':
    'オンにすると、システムはユーザーの「入力質問」の言語を自動的に識別します。オフにすると、ユーザーのブラウザの言語が優先されます。',
  'chat.channel.configuration.chat4.language': '言語を自動的に識別',
  'chat.channel.configuration.chat4.document': 'ドキュメント知識ベース',
  'chat.channel.configuration.chat4.document.placeholder':
    'ドキュメント知識ベースを選択してください',
  'chat.channel.configuration.chat4.document.message.1':
    'ここでのドキュメント知識ベースは外部知識ベースのみを表示します。事前に',
  'chat.channel.configuration.chat4.document.message': 'ドキュメント知識ベース',
  'chat.channel.configuration.chat4.document.message.2':
    'ページで知識ベースの設定を行ってください',
  'chat.channel.configuration.chat4.ai.message':
    '生成型AIとの連携を設定できます',
  'chat.channel.configuration.chat4.ai': '生成型AIと連携',
  'chat.channel.configuration.chat4.workers':
    '未知の答え、人間のオペレーターに転送するボタン',
  'chat.channel.configuration.chat4.workers.content':
    '申し訳ありませんが、この質問にはお答えできません。人間のオペレーターにお問い合わせください。',
  'chat.channel.configuration.chat4.workers.position': '位置',
  'chat.channel.configuration.chat4.workers.zhuan': '人間のオペレーターに転送',
  'chat.channel.configuration.chat4.workers.message':
    'オンにすると、ロボットが答えを知らない場合、ロボットの返信内容の下に「人間オペレーターに転送」ボタンが自動的に表示されます',
  'chat.channel.configuration.chat4.unknown':
    'ロボットの未知の問題に対する返信',
  'chat.channel.configuration.chat4.unknown.placeholder':
    'ロボットのための未知の問題への応答を入力してください',
  'chat.channel.configuration.chat4.unknown.message':
    'ここでの設定は、ロボットが未知の問題に遭遇した際の返信です',
  'chat.channel.configuration.chat4.information.configuration.completed':
    'スマートカスタマーサービスの設定が完了しました。これらは保存後にチャットウィンドウに反映されます。',
  'chat.channel.configuration.chat5.message':
    '以下のコードをコピーして、ウェブサイトの<body> </body>タグの間に挿入してください。',
  'chat.channel.configuration.chat5.message.link':
    'チャットリンク：以下のリンクをウェブサイトのコードにコピーします。',
  'live.chat.title': 'チャットウィンドウプレビューエリア',
  'live.chat.title.subtitle':
    'ここでチャットウィンドウの効果をプレビューできます',
  'live.chat.customer': '顧客',
  'live.chat.customer.Dialogue': '製品の主な機能について教えていただけますか？',
  'live.chat.submit': '送信',
  'live.chat.end': 'チャットを終了',
  'live.chat.video': 'ビデオコミュニケーション',
  'chat.channel.configuration.cancel.btn': 'キャンセル',
  'chat.channel.configuration.next.btn': '次へ',
  'chat.channel.configuration.complete.btn': '完了',
  'chat.channel.configuration.title.knowledge_unknown_reply':
    '私の現在のスキルでは、あなたの質問に答えることはできません。必要に応じて、私たちのカスタマーサービス担当者を選択して、より専門的なサポートを受けることができます。',
  'chat.channel.configuration.chat5.end':
    '注意：このコードをお客様のウェブサイトに統合した後、「ConnectNow」の管理者に連絡し、上記のドメインをホワイトリストに追加してください。ホワイトリストの設定が完了すると、チャットコンポーネントが正常に表示されます。',
  'chat.channel.configuration.chat5.end.1': ' ',

  'chat.channel.configuration.channel.name.web': 'ウェブサイトドメイン名',
  'chat.channel.configuration.channel.name.placeholder.web':
    'ウェブサイトドメイン名を入力してください',
  'live.chat.customer.Dialogue.product': 'どの製品について知りたいですか？',
  'chat.channel.configuration.chat5.message.Settings': 'デプロイ設定',
  'chat.channel.configuration.channel.name.placeholder.error':
    '中国語、大文字と小文字、数字、「-」、「_」のみ入力できます',
  'chat.channel.configuration.channel.chatBoxName.placeholder.error':
    '中国語、大文字小文字、スペースのみ入力できます',
  'chat.channel.configuration.chat1.document.placeholder.language':
    'トリガーFAQデータが変更された場合は、再度選択してください',
  'chat.channel.configuration.channel.website':
    'ウェブサイトドメイン名の形式は次のとおりです:www.connectnow.cn',
  'chat.channel.configuration.channel.website.name.placeholder.error':
    '正規のウェブサイトドメイン名を入力してください',
  'chat.channel.configuration.work.panels.checkbox.ccp':
    '言語認識をオンにするかどうか',
  'chat.channel.configuration.title.pop_welcome_msg':
    'こんにちは、私はConnectNowインテリジェントカスタマーサービスです。何かお手伝いできることはありますか？',
  'chat.channel.configuration.chat4.workers.keyword.message':
    '顧客がこのキーワードを入力すると、人間のオペレーターに転送されます',
  'chat.channel.configuration.chat4.workers.keyword':
    '人間のオペレーターに転送するキーワード',
  'chat.channel.configuration.chat4.document.placeholder.keyword':
    '少なくとも1つのキーワードを入力してください',

  'wx.program.channel.configuration.title':
    'WeChatミニプログラムチャットチャネルを追加',
  'wx.program.channel.configuration.title.update':
    'WeChatミニプログラムチャットチャネルを編集',
  'wx.program.channel.configuration.tips':
    '顧客がWeChatミニプログラムを通じて企業に連絡しやすくなり、顧客のニーズに迅速に対応できます。',
  'shopify.channel.configuration.title': 'Shopifyチャットチャネルを追加',
  'shopify.channel.configuration.title.update': 'Shopifyチャットチャネルを編集',
  'shopify.channel.configuration.tips':
    '顧客がShopifyウェブサイトを通じて企業に連絡しやすくなり、顧客のニーズに迅速に対応できます。',

  'chat.channel.configuration.chat1.ai.aiagent': 'AI Agentをオンにする',
  'chat.channel.configuration.chat1.ai.aiagent.tips':
    'AI Agentをオンにした後、歓迎メッセージとAIGC知識ベースの選択は、AI Agentページで設定する必要があります。',
  'chat.channel.configuration.chat2.agent.avac':
    'カスタマーサービスデフォルトアバター',
  'chat.channel.configuration.chat2.customer.avac': '顧客デフォルトアバター',
  'chat.channel.configuration.chat2.robot.avac': 'ロボットアバター',
  'chat.channel.configuration.chat2.robot.avac.kexuan': '（オプション）',
  'chat.channel.configuration.chat2.robot.avac.tips':
    '会在聊天框里展示，建议上传50*50px的PNG图片',
  'chat.channel.configuration.chat2.agent.font.color':
    'カスタマーサービスチャットフォントカラー',
  'chat.channel.configuration.chat2.customer.font.color':
    'ユーザーチャットフォントカラー',
  'chat.channel.configuration.chat3.history.chat': '履歴会話を表示',
  'chat.channel.configuration.chat3.history.chat.tips':
    'オンにすると、エンドユーザーは以前の履歴会話を見ることができます',
  'chat.channel.configuration.chat3.history.chat.num': '履歴会話数を表示',
  'chat.channel.configuration.chat3.history.chat.num.p':
    '表示する履歴会話数を選択してください',
  'chat.channel.configuration.chat3.history.chat.select': '無制限',
  'chat.channel.configuration.chat4.transfor.btn':
    '人間のオペレーターに転送するボタンを表示',
  'chat.channel.configuration.chat4.transfor.btn.tips':
    'オンにすると、チャットウィンドウの右側に「人間オペレーターに転送」ボタンが表示されます',
  'chat.channel.configuration.chat4.agent.workTime': 'エージェントの稼働時間',
  'chat.channel.configuration.chat4.agent.workTime.p':
    'エージェントの稼働時間を選択してください',

  'chat.channel.configuration.chat4.agent.workTime.no':
    '営業時間外のロボット応答メッセージ',

  'chat.channel.configuration.chat4.agent.workTime.no.default':
    '申し訳ありませんが、現在、エージェントの稼働時間外です。08:00～18:00に再度お問い合わせください。',
  'chat.channel.configuration.chat4.agent.workTime.no.tips':
    'エージェントの稼働時間外に、顧客が人間のオペレーターに転送された際のロボット応答メッセージ',

  'chat.channel.configuration.chat1.push.agents.join.chat':
    'シートをプッシュしてチャットに参加する',

  // discord渠道
  'channel.allocation.detail.discord.title': 'Discordチャネル設定',
  'add.discord.channel.configuration.title': 'Discordチャネルを追加',
  'editor.discord.channel.configuration.title': 'Discordチャネルを編集',
  'add.discord.channel.configuration.tips': 'Discordチャネルを追加してください',
  'discord.channel.configuration.title.1': 'Discord Bot Tokenを設定',
  'discord.channel.configuration.title.tips.1':
    'Discord開発者ポータルのBot Token情報を、以下の入力欄に入力してください',
  'discord.channel.configuration.title.tips.2': '設定ドキュメントを表示',
  'discord.channel.allocation.complete.1':
    'Bot Tokenの入力が完了しました。引き続き、チャットボットの設定を完了してください',
  'discord.channel.configuration.channel.bot.name': 'ボット名',
  'discord.channel.configuration.channel.bot.name.placeholder':
    'ボット名を入力してください',
  'discord.channel.configuration.channel.bot.token': 'Bot Token',
  'discord.channel.configuration.channel.bot.token.placeholder':
    'Bot Tokenを入力してください',
  'channel.allocation.detail.input.bot.name': 'ボット名：',
  'channel.allocation.detail.input.bot.name.placeholder':
    'ボット名を入力してEnterキーで検索',
  'discord.channel.configuration.channel.application.id':
    'アプリケーションID：',
  'discord.channel.configuration.channel.application.id.placeholder':
    'アプリケーションIDを入力してください',

  // discord帮助文档
  'channel.allocation.discord.document.title': 'Discord：ConnectNowとの連携',
  'channel.allocation.discord.document.h1': 'Discordサーバーを準備',
  'channel.allocation.discord.document.step.1': ' Discordアカウントを準備',
  'channel.allocation.discord.document.step.1.text':
    'Discordアカウントをお持ちでない場合は、<a>https://discord.com/</a>にアクセスして登録してください',
  'channel.allocation.discord.document.step.1.text.1':
    ' すでにdiscordのアカウントを持っている場合は、手順2を直接ジャンプしてください',
  'channel.allocation.discord.document.step.2': ' Discordサーバーを準備',
  'channel.allocation.discord.document.step.2.text':
    '注：すでにDiscordサーバーをお持ちの場合は、このステップをスキップしてください',
  'channel.allocation.discord.document.step.2.text.1':
    ' サーバーを作成をクリック',
  'channel.allocation.discord.document.step.2.text.2': ' 自分で作成を選択',
  'channel.allocation.discord.document.step.2.text.3':
    '実際の状況に応じてサーバー情報を選択してください。不明な場合は「この質問をスキップ」をクリックしてください',
  'channel.allocation.discord.document.step.2.text.4':
    'サーバー名を入力し、ロゴをアップロードしてから「作成」をクリックします',
  'channel.allocation.discord.document.step.3': ' アプリケーションを作成',
  'channel.allocation.discord.document.step.3.text':
    '開発者ポータル<a>https://discord.com/developers/applications</a>にアクセスし、「New Application」をクリックします',
  'channel.allocation.discord.document.step.3.text.1':
    'アプリケーション名を入力Createをクリック',
  'channel.allocation.discord.document.step.3.text.2':
    '作成が完了したら、関連するアプリケーション情報を設定します',
  'channel.allocation.discord.document.step.3.text.3':
    '左側のメニューにある「Bot」をクリックします',
  'channel.allocation.discord.document.step.3.text.4':
    'Botの権限を設定します。下の図に従って権限を設定し、【Save Changes】ボタンをクリックしてください',
  'channel.allocation.discord.document.step.3.text.5':
    '承認。左側のメニューでOAuth2をクリックし、下にスクロールして「Bot」を選択します。次に下にスクロールして「Send Messages」、「Send Messages in Threads」、「Manage Messages」、「Read Message History」、「View Channels」を順に選択します。以下の2つの画像を参照してください',
  'channel.allocation.discord.document.step.3.text.6':
    '選択が完了すると、ページ下部に承認リンクが生成されます。クリックしてコピーし、ブラウザで開いてください',
  'channel.allocation.discord.document.step.3.text.7':
    'リンクを開くと以下のように表示されます。ボットを追加するサーバーを選択し、「はい」をクリックし、次のステップで「認証」ボタンをクリックします',
  'channel.allocation.discord.document.step.3.text.8':
    'ボットがサーバーに正常に追加されました',
  'channel.allocation.discord.document.step.4': ' ボットをConnectNowに統合',
  'channel.allocation.discord.document.step.4.text':
    'Bot Tokenを取得します。開発者ポータル<a>https://discord.com/developers/applications</a>にアクセスし、左側のメニューの「Bot」をクリックし、「Reset Token」をクリックします',
  'channel.allocation.discord.document.step.4.text.1':
    '生成したらCopyをクリックしてトークンを保存',
  'channel.allocation.discord.document.step.4.text.6':
    'アプリケーションIDを取得します。このフィールドは非常に重要です。間違っているとチャネルがメッセージを受信できなくなります！',
  'channel.allocation.discord.document.step.4.text.2':
    'ConnectNowの管理画面にアクセスし、チャネル -> Discord -> チャネルを追加 をクリックし、保存したTokenとボット名を入力して、「次へ」をクリックします',
  'channel.allocation.discord.document.step.4.text.3':
    '必要に応じてチャットボットのパラメータを設定し、「次へ」をクリックします',
  'channel.allocation.discord.document.step.4.text.4': 'チャネル名を入力',
  'channel.allocation.discord.document.step.4.text.5':
    '🎉おめでとうございます！Discordは正常にConnectNowと統合されました',
};
