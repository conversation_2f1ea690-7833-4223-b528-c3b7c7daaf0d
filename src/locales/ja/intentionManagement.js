export default {
  'intention.management.intent.classification': '意図分類：',
  'intention.management.intent.classification.placeholder':
    '意図分類を選択してください',
  'intention.management.intent.language': '意図言語：',
  'intention.management.intent.language.placeholder':
    '意図言語を選択してください',
  'intention.management.intent.name': '意図名：',
  'intention.management.intent.name.placeholder': '意図名を入力してください',
  'intention.management.create.intention.text': '新規意図',
  'intention.management.user.number': '{num}次',
  'intention.management.user.number.month.tips': '当月の使用回数',
  'intention.management.intent.request.reason': '意図リクエスト理由：',
  'intention.management.create.information': '作成情報：',
  'intention.management.create.intelligent.agent': '新規エージェント',
  'intention.management.intelligent.agent': 'エージェント',
  'intention.management.delete.tips':
    'このコンテンツを削除してもよろしいですか？',

  // 添加意图
  'new.intent.title.add': '意図を追加',
  'new.intent.title.edit': '意図を編集',
  'new.intent.intention.basic.information': '意図の基本情報',
  'new.intent.intention.name': '意図名',
  'new.intent.intention.name.maxlength': '長さは80文字を超えることはできません',
  'new.intent.intention.language': '意図言語',
  'new.intent.intention.classification': '意図分類',
  'new.intent.intention.request.reason': '意図リクエスト理由',
  'new.intent.intention.request.reason.placeholder':
    'お客様がこの意図をトリガーした理由を入力してください。例：お客様が注文ステータスの照会をリクエスト',
  'new.intent.intention.reason.maxlength':
    '長さは2000文字を超えることはできません',
  'new.intent.intention.basic.information.tips':
    '意図名と意図リクエスト理由は、AIGCが顧客の意図を識別するための重要な അടിസ്ഥാനとなります。',
  'new.intent.intention. language.information': '意図の話術情報',
  'new.intent.static.language.technique': '静的話術',
  'new.intent.dynamic.speech.technique': '動的話術',
  'new.intent.static.language.technique.placeholder':
    '静的意図の話術を入力してください',
  'new.intent.dynamic.speech.technique.placeholder': '動的話術を入力し',
  'new.intent.dynamic.speech.technique.placeholder2': '请输入动态意图话术',
  'new.intent.ai.static.language.technique': 'AI生成意図の話術',
  'new.intent.add.static.language.technique': '意図の話術を追加',
  'new.intent.intention.dynamic.speech.technique.tips':
    '动态话术中可包含动态属性，动态属性格式为 {Device}，其中Device为属性名，仅支持英文',
  'new.ai.generate.intent.intention.language': '选择意图话术语言',
  'new.intent.intention.language.attribute': '意図の話術属性',
  'new.intent.intention.language.attribute.name': '属性名',
  'new.intent.intention.language.attribute.name.placeholder':
    '属性名を入力してください',
  'new.intent.intention.language.attribute.example': '属性値の例',
  'new.intent.intention.language.attribute.example.placeholder':
    '属性値を入力してください',
  'new.intent.intention.language.attribute.example.tips':
    '属性値の例は、現在の属性抽出の重要な അടിസ്ഥാനとなります。現在の属性がDeviceの場合、ここでの例はエアコン、除湿機などです。',
  'new.intent.intention.attribute.description': '属性の説明',
  'new.intent.intention.attribute.description.placeholder':
    '変数の説明を入力してください',
  'new.intent.intention.attribute.mandatory': '必須項目かどうか',
  'new.intent.intention.attribute.verify.format': '属性の検証形式を選択',
  'new.intent.intention.attribute.verify.format.placeholder':
    '属性の検証形式を選択してください',
  'new.intent.intention.attribute.verify.rule.placeholder':
    '属性の検証正規表現を入力してください',
  'new.intent.intention.attribute.verify.rule.maxlength':
    '長さは200文字を超えることはできません',
  'new.intent.intention.failed.verification.attempts': '検証失敗時の再試行回数',
  'new.intent.intention.failed.verification.attempts.placeholder':
    '検証回数を入力してください',
  'new.intent.intention.failed.num.verification.attempts.placeholder':
    '検証回数は最大99回を超えません',
  'new.intent.intention.reply.after.verification.failure': '検証失敗後の応答',
  'new.intent.intention.reply.after.verification.failure.placeholder':
    '検証失敗後の応答を入力してください',
  'new.intent.intention.reply.after.verification.failure.final':
    '最終検証失敗後の応答',
  'new.intent.intention.reply.after.verification.failure.final.placeholder':
    '最終検証失敗後の応答を入力してください',
  'new.intent.intelligent.agent.variables': 'エージェントに保存される変数',
  'new.intent.intelligent.agent.variables.placeholder':
    'エージェントに保存する変数を選択してください',
  'new.intent.intention.rhetorical.question.not.collecting':
    '現在の変数の反質問話術が収集されていません',
  'new.intent.intention.rhetorical.question.not.collecting.placeholder':
    '話術を入力してください',
  'new.intent.add.variables': '属性を追加',
  'new.intent.intention.llm.extract.attribute': 'LLM 提取意图属性',
  'new.intent.llm.extract.attribute.add.variables': '属性を追加',
  'new.intent.add.cancel.confirm':
    'キャンセルするとフォームがクリアされます。キャンセルしてもよろしいですか？',
  'new.intent.add.success.tips':
    'おめでとうございます。意図の作成に成功しました。次に、現在の意図のためにエージェントを作成できます',
  'new.intent.add.static.language.technique.tips': '最大100個の意図話術を作成',
  'new.intent.add.attribute.example.tips':
    '各変数に最大4つの属性インスタンスを追加！',
  'new.intent.add.verbal.variables.tips': '最大4つの話術変数を作成！',
  'new.intent.intention.classification.management': '意図分類管理',
  'new.intent.intention.classification.management.placeholder':
    '意図分類名を入力し、Enterキーを押して検索するか、ボタンをクリックして追加',
  'new.intent.add.variables.only.tips':
    '追加する変数は一意であり、重複できません！',
  'new.intent.add.variables.only.tips.1':
    '请配置话术中包含的{variable}变量属性。',
  'new.intent.add.variables.only.tips.2':
    '话术中缺少{variable}变量，请添加对应话术。',
  'new.intent.add.variables.only.tips.3': '话术{script}重复！',
  'new.intent.add.ai.script.create':
    '意図名と意図リクエスト理由を空白にすることはできません',
  'new.intent.add.script.create.tips':
    '注意：Enterキーで自動的に変数が生成されます',
  'new.intent.intention.classification.tips':
    '意図分類を空白にすることはできません！',
  'new.intent.intention.language.attribute.code': '属性コード',
  'new.intent.intention.language.attribute.format.requirement': '属性形式要求',
  'new.intent.intention.language.attribute.is.required': '必須かどうか',
  'new.intent.intention.language.attribute.operation': '操作',
  'new.intent.intention.language.attribute.is.required.yes': '必須',
  'new.intent.intention.language.attribute.is.required.no': '非必須',
  'new.intent.intention.language.attribute.code.only.support.english':
    '英語のみをサポート',
  'new.intent.intention.language.attribute.code.only.support.english.message':
    '属性コードは英語のみをサポート',
  'new.intent.add.required.one.speech': '必須には1つの話術が必要です',
  'new.intent.add.dynamic.speech.editing.tips':
    '編集中の動的話術意図属性変数を保存してください',

  // 智能体列表
  'external.intelligent.agent.delete.synonym.rules': '削除プロンプト',
  'external.intelligent.agent.intent.language.placeholder':
    '意図名を選択してください',
  'external.intelligent.agent.name': 'エージェント名：',
  'external.intelligent.agent.name.placeholder':
    'エージェント名を入力してください',
  'external.intelligent.agent.deployment.status': 'デプロイメントステータス：',
  'external.intelligent.agent.deployment.status.placeholder':
    'デプロイメントステータスを選択してください',
  'external.intelligent.agent.tab.chat': 'チャット',
  'external.intelligent.agent.tab.email': 'メール',
  'external.intelligent.agent.tab.phone': '電話',
  'external.intelligent.agent.create.text': '新規エージェント',
  'external.intelligent.agent.use.rule': '利用ルール',
  'external.intelligent.agent.deployment.time': 'デプロイ日時',
  'external.intelligent.agent.delete.tips':
    'このエージェントを削除してもよろしいですか？',
  'external.intelligent.agent.deploy.status.1': 'デプロイ済み',
  'external.intelligent.agent.deploy.status.2': '下書き',
  'external.intelligent.agent.default.welcome.message':
    '顧客がウェブオンラインチャット、アプリオンラインチャット、Shopify、WeChatミニプログラムを通じて企業と通信する場合、現在のウェルカムエージェントが最初に実行されます',
  'external.intelligent.agent.fallback':
    'システムがエージェントと一致しない場合、デフォルトの意図が実行されます',
  'intelligent.agent.deployed.text': '已部署，{deployName}',
  'external.intelligent.agent.share.success': 'コピー成功',
  'external.intelligent.agent.share.tips':
    '注：この共有コードは24時間有効です。{expiryTime}まで使用してください。',
  'external.intelligent.agent.share': '分享',
  'external.intelligent.agent.share.content':
    '【ConnectNow】{username}からのAIエージェント共有を受け取りました。あなたの専用エクスペリエンスコードは「{shareCode}」です。このコードは24時間以内（{expiryTime}まで）有効です。',
  'external.intelligent.agent.share.confirm': '確認',
  'external.intelligent.agent.share.copy': 'コピー',
  'external.intelligent.agent.share.cancel': 'キャンセル',
  // 新建智能体
  'external.intelligent.agent.create.title': '新規エージェント',
  'external.intelligent.agent.create.add': 'エージェントを作成',
  'external.intelligent.agent.create.basic.information': '基本情報',
  'external.intelligent.agent.create.name': 'エージェント名を入力',
  'external.intelligent.agent.create.channel.type': '適用チャネルタイプ',
  'external.intelligent.agent.create.select.scenario':
    'エージェントシナリオを選択',
  'external.intelligent.agent.phone.voice': '電話・音声',
  'external.intelligent.agent.create.select.channel.type':
    '適用するチャネルタイプを選択',
  'external.intelligent.agent.create.select.channel.type.tips':
    '少なくとも1つのチャネルタイプを選択',
  'external.intelligent.agent.create.facebook.message': 'Facebookメッセージ',
  'marketing.channel.type.web.voice': 'WEB音声',
  'marketing.channel.type.app.voice': 'APP音声',
};
