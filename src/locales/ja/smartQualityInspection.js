export default {
  'smart.quality.evaluation.table.title': 'AI品質評価フォーム',
  'smart.quality.evaluation.table.add': '評価フォームを追加',
  'smart.quality.evaluation.table.name': '評価フォーム名',
  'smart.quality.evaluation.rule.category': 'ルールカテゴリ',
  'smart.quality.evaluation.assessment.name': '評価フォーム名',
  'smart.quality.evaluation.table.channel': 'チャネル',
  'smart.quality.evaluation.table.channel.all': 'すべてのチャネル',
  'smart.quality.evaluation.table.ticket.type': 'チケットタイプ',
  'smart.quality.evaluation.table.status': '公開ステータス',
  'smart.quality.evaluation.table.status.published': '公開済み',
  'smart.quality.evaluation.table.status.disabled': '未公開',
  'smart.quality.evaluation.table.rule.count': '採点ルール数',
  'smart.quality.evaluation.table.total.score': '合計点',
  'smart.quality.evaluation.table.operation': '操作',
  'smart.quality.evaluation.table.enable': '有効にする',
  'smart.quality.evaluation.table.enable.success': '有効化に成功しました',
  'smart.quality.evaluation.table.disable': '無効にする',
  'smart.quality.evaluation.table.disable.success': '無効化に成功しました',
  'smart.quality.evaluation.table.edit': '編集',
  'smart.quality.evaluation.table.edit.rule': 'ルールを編集',
  'smart.quality.evaluation.table.history': '評価履歴',
  'smart.quality.evaluation.table.delete': '削除',
  'smart.quality.evaluation.table.delete.confirm':
    'この評価フォームを削除しますか？',
  'smart.quality.evaluation.table.delete.ok': 'はい',
  'smart.quality.evaluation.table.delete.cancel': 'いいえ',
  'smart.quality.evaluation.table.delete.success': '削除に成功しました',
  'smart.quality.evaluation.list.page.total.num': '合計 {total} 件',
  // ====== 添加评估表页面相关 ======
  'smart.quality.evaluation.add': '評価フォームを追加',
  'smart.quality.evaluation.add.baseinfo': '基本情報',
  'smart.quality.evaluation.add.rule': '採点ルール',
  'smart.quality.evaluation.add.permission': '権限設定',
  'smart.quality.evaluation.add.name': '評価フォーム名',
  'smart.quality.evaluation.add.name.placeholder':
    '評価フォーム名を入力してください',
  'smart.quality.evaluation.add.name.required':
    '評価フォーム名を入力してください',
  'smart.quality.evaluation.add.name.max':
    '長さは80文字を超えることはできません',
  'smart.quality.evaluation.add.channel': '適用チャネル',
  'smart.quality.evaluation.add.channel.placeholder':
    '適用チャネルを選択してください',
  'smart.quality.evaluation.add.channel.required':
    '適用チャネルを選択してください',
  'smart.quality.evaluation.add.ticket.type': '適用チケットタイプ',
  'smart.quality.evaluation.add.ticket.type.placeholder':
    '適用チケットタイプを選択してください',
  'smart.quality.evaluation.add.ticket.type.required':
    '適用チケットタイプを選択してください',
  'smart.quality.evaluation.add.total.score': '満点',
  'smart.quality.evaluation.add.total.score.placeholder':
    '満点を入力してください',
  'smart.quality.evaluation.add.total.score.required': '満点を入力してください',
  'smart.quality.evaluation.add.score.mechanism': '採点メカニズム',
  'smart.quality.evaluation.add.score.mechanism.add': '加点方式',
  'smart.quality.evaluation.add.score.mechanism.subtract': '減点方式',
  'smart.quality.evaluation.add.score.mechanism.tooltip.add.title': '加点方式',
  'smart.quality.evaluation.add.score.mechanism.tooltip.add.1':
    'パフォーマンス付加価値評価',
  'smart.quality.evaluation.add.score.mechanism.tooltip.add.2':
    '基礎点から始まり、優れたパフォーマンスに加点',
  'smart.quality.evaluation.add.score.mechanism.tooltip.add.3':
    '基準を超える積極的な行動と付加価値を強調',
  'smart.quality.evaluation.add.score.mechanism.tooltip.subtract.title':
    '減点方式',
  'smart.quality.evaluation.add.score.mechanism.tooltip.subtract.1':
    'コンプライアンス・ベースライン評価',
  'smart.quality.evaluation.add.score.mechanism.tooltip.subtract.2':
    '満点から始まり、違反項目で減点',
  'smart.quality.evaluation.add.score.mechanism.tooltip.subtract.3':
    '基本的なサービス基準とコンプライアンスの維持を強調',
  'smart.quality.evaluation.add.score.mechanism.required':
    '採点メカニズムを選択してください',
  'smart.quality.evaluation.add.permission.scorer': '評価者',
  'smart.quality.evaluation.add.permission.scorer.tooltip':
    'この評価フォームに基づいて評価する権限を持つユーザーを定義します',
  'smart.quality.evaluation.add.permission.scorer.required':
    '評価者を選択してください',
  'smart.quality.evaluation.add.permission.scorer.placeholder':
    '評価者を選択してください',
  'smart.quality.evaluation.add.cancel.confirm':
    'キャンセルするとフォームがクリアされます。キャンセルしますか？',
  'smart.quality.common.cancel': 'キャンセル',
  'smart.quality.common.next': '次へ',
  'smart.quality.common.yes': 'はい',
  'smart.quality.common.no': 'いいえ',
  'smart.quality.evaluation.add.channel.all': 'すべて',
  'smart.quality.evaluation.add.ticket.type.all': 'すべて',
  // ====== END 添加评估表页面相关 ======

  // ====== 评估历史记录页面相关 ======
  'smart.quality.evaluation.history.title': '評価履歴',
  'smart.quality.evaluation.history.name.placeholder':
    '評価フォーム名を入力してください',
  'smart.quality.evaluation.history.channel.placeholder':
    'チャネルを選択してください',
  'smart.quality.evaluation.history.ticket.type.placeholder':
    'チケットタイプを選択してください',
  'smart.quality.evaluation.history.agent.name': 'エージェント名',
  'smart.quality.evaluation.history.agent.name.placeholder':
    'エージェント名を選択してください',
  'smart.quality.evaluation.history.ticket.id': 'チケットID',
  'smart.quality.evaluation.history.ticket.id.placeholder':
    'チケットIDを入力してください',
  'smart.quality.evaluation.history.score': 'スコア',
  'smart.quality.evaluation.history.evaluator': '評価者',
  'smart.quality.evaluation.history.evaluator.placeholder':
    '評価者を選択してください',
  'smart.quality.evaluation.history.score.range': 'スコア範囲',
  'smart.quality.evaluation.history.score.min': '最小値',
  'smart.quality.evaluation.history.score.max': '最大値',
  'smart.quality.evaluation.history.score.min.error':
    '最小値は最大値より大きくすることはできません',
  'smart.quality.evaluation.history.score.max.error':
    '最大値は最小値より小さくすることはできません',
  'smart.quality.evaluation.history.score.range.error':
    '最大値は最小値より小さくすることはできません',
  'smart.quality.evaluation.history.score.both.required':
    '最大値と最小値は両方入力する必要があります',
  'smart.quality.evaluation.history.score.range.warning':
    '最小値は最大値より大きくしないでください',
  'smart.quality.evaluation.history.score.format.error':
    '有効な数値を入力してください（小数点以下2桁まで）',
  'smart.quality.evaluation.history.search': '検索',
  'smart.quality.evaluation.history.details': '詳細',
  'smart.quality.evaluation.history.export.pdf': 'PDFをエクスポート',
  'smart.quality.evaluation.history.page.total.num': '合計 {total} 件',
  'smart.quality.evaluation.history.evaluation.time': '評価日時',
  'smart.quality.evaluation.history.return.list': '一覧へ戻る',
  // ====== END 评估历史记录页面相关 ======

  'smart.quality.evaluation.rule.version.current': '最新バージョン',
  'smart.quality.evaluation.rule.version.current.tip':
    '最新バージョンを選択してください',
  'smart.quality.evaluation.rule.delete.category.tip.title': 'ヒント',
  'smart.quality.evaluation.rule.delete.category.tip':
    'このカテゴリにはルールが含まれています。削除を続行すると、カテゴリ内のすべてのルールも削除されます',
  'smart.quality.evaluation.rule.add.node.tip':
    'まず、ノードを追加してください',
  'smart.quality.evaluation.rule.select.node.tip':
    'まず、ノードを選択してください',

  // start 规则列表
  'smart.quality.evaluation.rule.category': 'ルールカテゴリ',
  'smart.quality.evaluation.rule.title': '評価フォームを追加',
  'smart.quality.evaluation.rule.edit.title': 'ルールを編集',
  'smart.quality.evaluation.rule.deploy.status': 'デプロイステータス',
  'smart.quality.evaluation.rule.deploy.status.unpublished': '未公開',
  'smart.quality.evaluation.rule.deploy.status.published': '公開済み',
  'smart.quality.evaluation.rule.button.cancel': 'キャンセル',
  'smart.quality.evaluation.rule.button.save': '保存',
  'smart.quality.evaluation.rule.button.save.and.publish': '保存して公開',
  'smart.quality.evaluation.rule.search.placeholder':
    'ルール名またはルールの説明を入力して検索',
  'smart.quality.evaluation.rule.add': 'ルールを追加',
  'smart.quality.evaluation.rule.table.category': 'ルールカテゴリ',
  'smart.quality.evaluation.rule.table.name': 'ルール名',
  'smart.quality.evaluation.rule.table.description': 'ルールの説明',
  'smart.quality.evaluation.rule.table.score': '採点ルール',
  'smart.quality.evaluation.rule.table.evaluation.method': '採点方式',
  'smart.quality.evaluation.rule.table.evaluation.method.manual': '手動採点',
  'smart.quality.evaluation.rule.table.evaluation.method.ai': 'AI採点',
  'smart.quality.evaluation.rule.table.operation': '操作',
  'smart.quality.evaluation.rule.table.operation.edit': '編集',
  'smart.quality.evaluation.rule.table.delete.confirm':
    'このルールを削除しますか？',
  'smart.quality.evaluation.rule.table.delete.ok': 'はい',
  'smart.quality.evaluation.rule.table.delete.cancel': 'いいえ',
  'smart.quality.evaluation.rule.table.operation.delete': '削除',
  'smart.quality.evaluation.rule.table.operation.detail': '詳細',
  'smart.quality.evaluation.rule.new.top.level': '新規カテゴリ',
  'smart.quality.evaluation.rule.new.category': '新規カテゴリ',
  'smart.quality.evaluation.rule.page.total.num': '合計 {total} 件',
  'smart.quality.evaluation.rule.add.score.add': '加点',
  'smart.quality.evaluation.rule.add.score.subtract': '減点',
  // end 规则列表

  // start 添加规则页面
  'smart.quality.evaluation.rule.add.title': 'ルールを追加',
  'smart.quality.evaluation.rule.edit.title': 'ルールを編集',
  'smart.quality.evaluation.rule.detail.title': 'ルールの詳細',
  'smart.quality.evaluation.rule.add.baseinfo': '基本情報',
  'smart.quality.evaluation.rule.add.baseinfo.name': 'ルール名',
  'smart.quality.evaluation.rule.add.baseinfo.name.placeholder':
    'ルール名を入力してください',
  'smart.quality.evaluation.rule.add.baseinfo.name.required':
    'ルール名を入力してください',
  'smart.quality.evaluation.rule.name': 'ルール名',
  'smart.quality.evaluation.rule.category.required':
    'ルールカテゴリを選択してください',
  'smart.quality.evaluation.rule.name.placeholder':
    'ルール名を入力してください',
  'smart.quality.evaluation.rule.name.required': 'ルール名を入力してください',
  'smart.quality.evaluation.rule.name.max':
    '長さは80文字を超えることはできません',
  'smart.quality.evaluation.rule.description': 'ルールの説明',
  'smart.quality.evaluation.rule.description.placeholder':
    'ルールの説明を入力してください',
  'smart.quality.evaluation.rule.description.required':
    'ルールの説明を入力してください',
  'smart.quality.evaluation.rule.description.max':
    '長さは200文字を超えることはできません',
  'smart.quality.evaluation.rule.category.placeholder':
    'ルールカテゴリを選択してください',
  'smart.quality.evaluation.rule.category.required':
    'ルールカテゴリを選択してください',
  'smart.quality.evaluation.rule.evaluation.method': '採点方式',
  'smart.quality.evaluation.rule.evaluation.method.ai': 'AI採点',
  'smart.quality.evaluation.rule.evaluation.method.manual': '手動採点',
  'smart.quality.evaluation.rule.ai.settings': 'AI採点ルール',
  'smart.quality.evaluation.rule.manual.settings': '手動採点ルール',
  'smart.quality.evaluation.rule.total.score': 'ルールの合計点',
  'smart.quality.evaluation.rule.total.score.placeholder':
    'ルールの合計点を入力してください',
  'smart.quality.evaluation.rule.total.score.required':
    'ルールの合計点を入力してください',
  'smart.quality.evaluation.rule.total.score.exceed.error':
    'ルールの合計点は評価フォームの合計点を超えることはできません',
  'smart.quality.evaluation.rule.total.score.exceed.error.add':
    'ルールの合計加点は評価フォームの合計点を超えることはできません',
  'smart.quality.evaluation.rule.total.score.exceed.error.subtract':
    'ルールの合計減点は評価フォームの合計点を超えることはできません',
  'smart.quality.evaluation.rule.aigc.rule1.score.exceed.error':
    'AI採点ルールのスコアはルールの合計点を超えることはできません',
  'smart.quality.evaluation.rule.aigc.rule1.score.exceed.error.add':
    'AI採点ルールの加点はルールの合計点を超えることはできません',
  'smart.quality.evaluation.rule.aigc.rule1.score.exceed.error.subtract':
    'AI採点ルールの減点はルールの合計点を超えることはできません',
  'smart.quality.evaluation.rule.aigc.rule2.score.exceed.error':
    'AI採点ルールのスコアはルールの合計点を超えることはできません',
  'smart.quality.evaluation.rule.aigc.rule2.score.exceed.error.add':
    'AI採点ルールの加点はルールの合計点を超えることはできません',
  'smart.quality.evaluation.rule.aigc.rule2.score.exceed.error.subtract':
    'AI採点ルールの減点はルールの合計点を超えることはできません',
  'smart.quality.evaluation.rule.manual.option.required':
    '採点ルールを少なくとも1つ追加してください',
  'smart.quality.evaluation.rule.manual.option.save.first':
    'まず編集中のルールを保存してください',
  'smart.quality.evaluation.rule.check.points': 'チェックポイント',
  'smart.quality.evaluation.rule.check.point.placeholder':
    '詳細なチェックポイントの説明を入力してください',
  'smart.quality.evaluation.rule.check.point.required':
    'チェックポイントを入力してください',
  'smart.quality.evaluation.rule.add.check.point': 'チェックポイントを追加',
  'smart.quality.evaluation.rule.check.points.max':
    '最大15個のチェックポイントを追加できます',
  'smart.quality.evaluation.add.rule.button.return': '戻る',
  'smart.quality.evaluation.add.rule.button.save': '保存',
  // end 添加规则页面

  // start 人工评分规则相关
  'smart.quality.evaluation.add.rule.manual.option.name': 'オプション名',
  'smart.quality.evaluation.add.rule.manual.option.name.required':
    'オプション名を入力してください',
  'smart.quality.evaluation.add.rule.manual.option.name.placeholder':
    'オプション名を入力してください',
  'smart.quality.evaluation.add.rule.manual.option.name.duplicate':
    'オプション名は重複できません',
  'smart.quality.evaluation.add.rule.manual.option.score': 'スコア',
  'smart.quality.evaluation.add.rule.manual.option.score.required':
    'スコアを入力してください',
  'smart.quality.evaluation.add.rule.manual.option.operation': '操作',
  'smart.quality.evaluation.add.rule.manual.rules': '手動採点ルール',
  'smart.quality.evaluation.add.rule.manual.add.option': 'オプションを追加',
  'smart.quality.evaluation.add.rule.manual.standard': '評価基準参考',
  'smart.quality.evaluation.add.rule.manual.standard.required':
    '評価基準参考を入力してください',
  'smart.quality.evaluation.add.rule.manual.standard.placeholder':
    '評価基準参考を入力してください',
  // end 人工评分规则相关

  // start AIGC评分规则相关
  'smart.quality.evaluation.add.rule.aigc.rules': 'AI採点ルール',
  'smart.quality.evaluation.add.rule.aigc.rules.required':
    'AI採点ルールを選択してください',
  'smart.quality.evaluation.add.rule.aigc.rule1.title':
    'いずれかのチェックポイントが出現した場合',
  'smart.quality.evaluation.add.rule.aigc.rule1.prefix': '、出現ごとに',

  'smart.quality.evaluation.add.rule.aigc.rule1.score.required':
    'スコアを入力してください',
  'smart.quality.evaluation.add.rule.aigc.rule1.middle': '点、最大累計',
  'smart.quality.evaluation.add.rule.aigc.rule1.max.score.required':
    '最大累計スコアを入力してください',
  'smart.quality.evaluation.add.rule.aigc.rule1.suffix': '点',
  'smart.quality.evaluation.add.rule.aigc.rule2.title':
    '複数のチェックポイントが出現した場合',
  'smart.quality.evaluation.add.rule.aigc.rule2.prefix': '、出現回数が',
  'smart.quality.evaluation.add.rule.aigc.rule2.condition.required':
    '出現回数を選択してください',
  'smart.quality.evaluation.add.rule.aigc.rule2.condition.gt': '次より多い',
  'smart.quality.evaluation.add.rule.aigc.rule2.condition.eq': '次と等しい',
  'smart.quality.evaluation.add.rule.aigc.rule2.condition.lt': '次より少ない',
  'smart.quality.evaluation.add.rule.aigc.rule2.threshold.required':
    '回数を入力してください',
  'smart.quality.evaluation.add.rule.aigc.rule2.middle': '回、',
  'smart.quality.evaluation.add.rule.aigc.rule2.score.required':
    'スコアを入力してください',
  'smart.quality.evaluation.add.rule.aigc.rule2.suffix': '点',
  // end AIGC评分规则相关
};
