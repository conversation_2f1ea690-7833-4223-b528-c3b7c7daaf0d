export default {
  'customerInformation.customerName': '顧客名：',
  'customerInformation.customerName.placeholder':
    '顧客名を入力し、Enterキーで検索',
  'customerInformation.emailAddress': 'メールアドレス：',
  'customerInformation.emailAddress.placeholder':
    'メールアドレスを入力し、Enterキーで検索',
  'customerInformation.add.basicInformation.emailAddress.placeholder.1':
    'メールアドレスを入力してください',
  'customerInformation.customerBusinessName': '会社名：',
  'customerInformation.customerBusinessName.placeholder':
    '会社名を入力し、Enterキーで検索',
  'customerInformation.sourceChannel': 'チャネル：',
  'customerInformation.searchPlaceholder': '顧客情報を入力し、Enterキーで検索',
  'customerInformation.customerInformation': ' 客户资料',
  'customerInformation.customerInformation1': '顧客情報',
  'customerInformation.table.sureName': '顧客の姓',
  'customerInformation.table.customerName': '顧客名',
  'customerInformation.table.customerCode': '顧客番号',
  'customerInformation.table.mailAddress': 'メールアドレス',
  'customerInformation.table.telephonePrefixId': '市外局番',
  'customerInformation.table.mobilePhoneNumber': '携帯電話番号',
  'customerInformation.table.customerLabel': '顧客タグ',
  'customerInformation.table.ContactInformation': '連絡方法',
  'customerInformation.table.ContactTime': '最終連絡時間',
  'customerInformation.table.nation': '国',

  'customerInformation.table.creationMethod': '作成方法',
  'customerInformation.table.groupName': 'グループ名',
  'customerInformation.table.sourceChannel': 'チャネル',
  'customerInformation.table.createTime': '作成時間',
  'customerInformation.table.address': '郵送先住所',
  'customerInformation.table.operation': '操作',
  'customerInformation.table.contactCustomer': '顧客に連絡',
  'customerInformation.table.customer.detail': '詳細',
  'customerInformation.table.editor': '変更',
  'customerInformation.add.basicInformation.customerID': '会員id',

  'customerInformation.add.basicInformation.addGroup': 'グループの追加',
  'customerInformation.add.basicInformation.pattern':
    "格式不正确，仅允许中、英文、数字及'.'",
  'customerInformation.add.basicInformation.maxlength': '80文字以内',
  'customerInformation.add.basicInformation.maxlength2': '200文字以内',
  'customerInformation.add.basicInformation.maxlength3': '2000文字以内',
  'customerInformation.add.basicInformation.placeholder': '入力してください',

  'customerInformation.add.basicInformation.title': '基本情報',
  'customerInformation.add.basicInformation.title2':
    '顧客ソーシャルメディア情報',
  'customerInformation.add.basicInformation.title3': '顧客拡張情報',
  'customerInformation.add.basicInformation.selectiveGrouping':
    'グループを選択',
  'customerInformation.add.basicInformation.selectiveGrouping.placeholder':
    'グループを選択してください',
  'customerInformation.add.basicInformation.selectiveGrouping.required':
    'グループを選択してください',

  'customerInformation.add.basicInformation.name': '名：',
  'customerInformation.add.basicInformation.lastname': '姓：',
  'customerInformation.add.basicInformation.lastname.placeholder':
    '姓を入力してください',
  'customerInformation.add.basicInformation.lastname.required':
    '姓を入力してください',
  'customerInformation.add.basicInformation.name.placeholder':
    '名を入力してください',
  'customerInformation.add.basicInformation.name.required':
    '名を入力してください',
  'customerInformation.add.basicInformation.name.pattern':
    "姓名格式不正确，仅允许中、英文、数字、空格、横线、下划线及'.'",
  'customerInformation.add.basicInformation.name.maxlength':
    '名前の長さは200文字以内',

  'customerInformation.add.basicInformation.sourceChannel.placeholder':
    'チャネルを選択してください',
  'customerInformation.add.basicInformation.channel.name':
    'チャネル名を選択してください',
  'customerInformation.add.basicInformation.sourceChannel.required':
    'チャネルを選択してください',

  'customerInformation.add.basicInformation.dateBirth': '生年月日：',

  'customerInformation.add.basicInformation.sex': '性別：',
  'customerInformation.add.basicInformation.sex.placeholder':
    '性別を選択してください',
  'customerInformation.add.basicInformation.contactNumber': '電話番号：',

  'customerInformation.add.basicInformation.contactNumber.placeholder':
    '電話番号を入力してください',
  'customerInformation.add.basicInformation.contactNumber.pattern':
    '電話番号の形式が正しくありません。数字のみ入力できます',
  'customerInformation.add.basicInformation.contactNumber.pattern1':
    '密码至少包含数字、英文、符号中的两种,符号输入范围 ~!@#$%^&*()_+<>?.,',
  'customerInformation.add.basicInformation.contactNumber.maxlength':
    '電話番号は40文字以内',

  'customerInformation.add.basicInformation.emailAddress': 'メールアドレス：',
  'customerInformation.add.basicInformation.emailAddress.placeholder':
    'メールアドレスを入力してください',
  'customerInformation.add.basicInformation.Address.placeholder':
    '地域を選択してください',
  'customerInformation.add.basicInformation.emailAddress.pattern':
    'メールアドレスの形式が正しくありません',

  'customerInformation.add.basicInformation.companyName': '会社名：',
  'customerInformation.add.basicInformation.companyName.placeholder':
    '会社名を入力してください',

  'customerInformation.add.basicInformation.position': '役職：',
  'customerInformation.add.basicInformation.position.placeholder':
    '役職を入力してください',
  'customerInformation.add.basicInformation.mailingAddress': '郵送先住所',
  'customerInformation.add.basicInformation.mailingAddress.placeholder':
    '郵送先住所を入力してください',
  'customerInformation.add.basicInformation.orderAddress': '請求先住所',
  'customerInformation.add.basicInformation.orderAddress.placeholder':
    '請求先住所を入力してください',
  'customerInformation.add.basicInformation.deliveryAddress': '送付先住所',
  'customerInformation.add.basicInformation.deliveryAddress.placeholder':
    '送付先住所を入力してください',
  'customerInformation.add.basicInformation.otherAddress': 'その他住所',
  'customerInformation.add.basicInformation.otherAddress.placeholder':
    'その他住所を入力してください',
  'customerInformation.add.basicInformation.remark': '備考',
  'customerInformation.add.basicInformation.remark.placeholder':
    '備考を入力してください',

  'work.order.management.table.work.order.notes':
    '注:タイムゾーンを設定すると、システムはすべての時間をこのタイムゾーンで表示します',

  'customerInformation.add.basicInformation.return.confirm':
    '戻ると現在のフォームの変更がクリアされます。戻りますか？',
  'customerInformation.add.basicInformation.button.return': '戻る',
  'customerInformation.add.basicInformation.button.save': '保存',
  'customerInformation.add.basicInformation.button.update': '変更',

  'customerInformation.modal.basicInformation.tips': 'ヒント',
  'customerInformation.modal.basicInformation.button.ok': '顧客情報を確認',
  'customerInformation.modal.basicInformation.button.cancel': 'キャンセル',

  'customerInformation.contactCustomer.info.title': '顧客情報',
  'customerInformation.contactCustomer.info.customerName': '顧客名：',
  'customerInformation.contactCustomer.info.telephone': '電話番号：',
  'customerInformation.contactCustomer.info.emailAddress': 'メールアドレス：',
  'customerInformation.contactCustomer.info.channelName': 'チャネル：',
  'customerInformation.contactCustomer.info.customerGroupName': 'グループ：',
  'customerInformation.contactCustomer.info.mailingAddress': '郵送先住所：',
  'customerInformation.contactCustomer.channel.placeholder':
    'チャネルを選択してください',
  'customerInformation.add.basicInformation.customerID.required':
    '会員idを入力してください',
  'customerInformation.contactCustomer.subject.placeholder':
    'メールの件名を入力してください',
  'customerInformation.contactCustomer.send.button': 'Senden',

  'customerInformation.contactCustomer.table.workRecordId': 'レコードid',
  'customerInformation.contactCustomer.table.channelName': 'チャネル名',
  'customerInformation.contactCustomer.table.customerContactInfo': '顧客連絡先',
  'customerInformation.contactCustomer.table.userName': '担当者名',
  'customerInformation.contactCustomer.table.status': 'ステータス',
  'customerInformation.contactCustomer.table.createTime': '作成時間',
  'customerInformation.contactCustomer.table.resolveTime': '解決時間',
  'customerInformation.contactCustomer.button.send': 'Senden',
  'customerInformation.contactCustomer.record': '顧客問合せ履歴',

  'customerInformation.name.not.empty':
    '{lineNum}行目の顧客名は空欄にできません',
  'customerInformation.name.length.limit':
    '{lineNum}行目の顧客名は最大200文字までです',
  'customerInformation.name.input.limit':
    "第{lineNum}行客户名字只能输入中英文、数字及'.'",

  'customerInformation.last.name.not.empty':
    '{lineNum}行目の姓は空欄にできません',
  'customerInformation.last.name.length.limit':
    '{lineNum}行目の姓は最大200文字までです',
  'customerInformation.last.name.input.limit':
    "第{lineNum}行姓氏只能输入中英文、数字及'.'",

  'customerInformation.group.not.empty':
    '{lineNum}行目のグループ名は空欄にできません',
  'customerInformation.channel.not.empty':
    '{lineNum}行目のチャネルは空欄にできません',

  'customerInformation.phone.input.limit':
    '{lineNum}行目の携帯電話番号の形式が正しくありません。数字のみが許可されています',
  'customerInformation.phone.length.limit':
    '{lineNum}行目の携帯電話番号は最大40文字までです',
  'customerInformation.phone.prefix.limit':
    '{lineNum}行目の国際電話番号が間違っています。修正してください',

  'customerInformation.company.input.limit':
    "第{lineNum}行公司名称只能输入中英文、数字及'.'",
  'customerInformation.company.length.limit':
    '{lineNum}行目の会社名は最大80文字までです',

  'customerInformation.post.input.limit':
    "第{lineNum}行职务只能输入中英文、数字及'.'",
  'customerInformation.post.length.limit':
    '{lineNum}行目の役職は最大80文字までです',

  'customerInformation.mailingAddress.input.limit':
    "第{lineNum}行邮寄地址只能输入中英文、数字及'.'",
  'customerInformation.mailingAddress.length.limit':
    '{lineNum}行目の郵送先住所は最大200文字までです',

  'customerInformation.orderAddress.input.limit':
    "第{lineNum}行账单地址只能输入中英文、数字及'.'",
  'customerInformation.orderAddress.length.limit':
    '{lineNum}行目の請求先住所は最大200文字までです',

  'customerInformation.deliveryAddress.input.limit':
    "第{lineNum}行配送地址只能输入中英文、数字及'.'",
  'customerInformation.deliveryAddress.length.limit':
    '{lineNum}行目の配送先住所は最大200文字までです',

  'customerInformation.otherAddress.input.limit':
    '{lineNum}行目の備考には、中国語、英語、数字、および「.」のみを入力できます',
  'customerInformation.otherAddress.length.limit':
    '{lineNum}行目のその他の住所は最大200文字までです',

  'customerInformation.remark.input.limit':
    '{lineNum}行目の備考には、中国語、英語、数字、および「.」のみを入力できます',
  'customerInformation.remark.length.limit':
    '{lineNum}行目の備考は最大200文字までです',

  'customerInformation.repeat.phone.tips':
    'ファイル内に重複した携帯電話番号があります：{repeatPhone}',
  'customerInformation.repeat.email.tips':
    'ファイル内に重複したメールアドレスがあります：{repeatEmail}',

  'customerInformation.batchImport': '一括インポート',
  'customerInformation.export': 'エクスポート',
  'customerInformation.download.template': 'テンプレートをダウンロード',

  'customerInformation.add.basicInformation.customerLevel': '顧客レベル：',
  'customerInformation.add.basicInformation.customerLevel.placeholder':
    '顧客レベルを選択してください',
  'customerInformation.add.basicInformation.customerLevel.required':
    '顧客レベルを選択してください',
  'customerInformation.add.basicInformation.customer.language':
    '顧客の優先言語',
  'customerInformation.add.basicInformation.tags': 'タグ：',
  'customerInformation.add.basicInformation.tags.placeholder':
    'タグを入力してenterキーを押してください',

  'customerInformation.modal.batch.change.grouping': '一括変更グループ',
  'customerInformation.modal.batch.import': '一括インポート',
  'hotWord.length': 'タグが長すぎます',

  'customerInformation.upload.file':
    '1つのファイルアップロードのみをサポートし、<b>{JA}</b>',
  'knowledge.QA.upload.file.request':
    '1.Excelフォーマットは*.xls，*.xlsxの2種類のフォーマットをサポートする',
  'customerInformation.upload.file.name':
    '顧客データ一括インポートテンプレート.xlsx',
  'customerInformation.upload.btn': 'ファイルをここにドラッグまたは<b>{JA}</b>',
  'customerInformation.upload.download': 'クリックしてダウンロード',
  'customerInformation.upload.file.1': 'ファイルの説明：',

  'upload.file.tips': 'xlsxファイルのみアップロードできます',
  'upload.file.tips.btn': 'クリックしてアップロード',

  'customerInformation.option.error':
    '少なくとも1つのグループデータを選択してください！',
  'user.management.operation.table.sourceType.1': '自動作成',
  'user.management.operation.table.sourceType.2': '手動作成',
  'user.management.operation.table.sourceType.3': 'マーケティング導入',
  'user.management.operation.table.btn.1': '一括でラベルを追加',
  'user.management.operation.table.btn.2': '一括でラベルを削除',
  'user.management.operation.modal.tags.placeholder':
    '顧客タグを入力し、enterキーを押して追加します。複数入力できます',

  'customerInformation.create.customer.email.tips':
    '受信者、cc、bccの長さは2000文字を超えてはいけません！',
  'customerInformation.create.customer.email.content.tips':
    'メール内容は空欄にできません！',
  'customerInformation.add.basicInformation.tags.tips.new':
    '少なくとも1つのタグを選択または作成してください！',
};
