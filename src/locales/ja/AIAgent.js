export default {
  'worktable.notification.chat.title': '「ConnectNow」チャット通知',
  'worktable.notification.chat.content': '顧客メッセージ：',
  'worktable.notification.call.title': '「ConnectNow」着信通知',
  'worktable.notification.call.content':
    '顧客{number}から着信がありました。ワークベンチで応答してください。',
  'beta.tip':
    '現在、この機能は内部テスト段階にあります。ご利用をご希望の場合は、弊社のアカウントマネージャーまでご連絡ください。ご支援とご協力に感謝いたします。',

  'ai.agent.verification.start':
    '少なくとも1つのノードが開始ノードに接続されている必要があります。',
  'ai.agent.verification.conditionIntent':
    'インテント認識コンポーネントは、質問の問題のテキストコンポーネントにのみ関連付けることができます。',
  'ai.agent.verification.node': `节点 {nodeName} 的 {formName} 内容不能为空！`,

  'ai.agent.channnel': 'チャネル',
  'ai.agent.channnel.options': '適切なチャネルタイプを選択',
  'ai.agent.copy.fail':
    'インテリジェントエージェントが空であるため、コピーに失敗しました。',
  'ai.agent.var': '変数管理',
  'ai.agent.var.add': '変数を追加',
  'ai.agent.var.select': 'タイプを選択',
  'ai.agent.var.setting.content.title':
    '顧客がメッセージに応答しない場合のプロアクティブなリマインダー',
  'ai.agent.var.setting.content.tip':
    'ここでの設定は、顧客がメッセージに応答しない場合にロボットがプロアクティブにリマインドするためのものです。',
  'ai.agent.var.name.rules': '変数名は中国語にできず、最大長は200です。',
  'ai.agent.var.tabs.1': '現在のインテリジェントエージェント変数',
  'ai.agent.var.tabs.2': '現在のセッション変数',
  'ai.agent.var.tabs.3': 'グローバル変数',
  'ai.agent.var.tabs.4': 'システム組み込み変数',
  'ai.agent.var.table.1': '変数名',
  'ai.agent.var.table.2': 'データ型',
  'ai.agent.var.table.3': 'デフォルト値',
  'ai.agent.var.table.4': '操作',
  'ai.agent.var.table.select.option.1': '文字列',
  'ai.agent.var.table.select.option.2': '数値',
  'ai.agent.var.table.select.option.3': 'JSON',
  'ai.agent.var.table.select.option.4': '配列',

  'ai.agent.back.title': '保存されていない旨の通知',
  'ai.agent.back.title.content':
    '現在のインテリジェントエージェントは変更されています。変更を保存しますか？',
  'ai.agent.back.button.no': '保存しない',

  'ai.agent.nodes.start': '開始ノード',

  'ai.agent.nodes.header.tips.status.1': '未デプロイ',
  'ai.agent.nodes.header.tips.status.2': '未保存',
  'ai.agent.nodes.header.tips.status.3': 'デプロイ済み',
  'ai.agent.nodes.header.tips.save':
    '最新バージョンを保存しましたが、まだデプロイされていません',
  'ai.agent.nodes.header.test': 'テスト',
  'ai.agent.testAgent.endTest': 'エージェントが実行を完了しました',
  'ai.agent.nodes.header.test.tips': '保存してからテストしてください',
  'ai.agent.nodes.header.save': '保存してデプロイ',
  'ai.agent.nodes.header.save.success': 'デプロイに成功しました',
  'ai.agent.nodes.header.shareCode': '共有コードを入力',
  'ai.agent.enable.confirm.title': '現在のエージェントを有効化',
  'ai.agent.enable.confirm.content':
    '現在のエージェントを有効にしますか？有効にすると、現在のエージェントが実行を開始します。',
  'ai.agent.enable.confirm.confirm': '有効化を確認',
  'ai.agent.enable.confirm.cancel': 'キャンセル',
  'ai.agent.enable.success': '有効化に成功しました',

  'ai.agent.nodes.header.tips.shortcut.title':
    'ヒント：<b>複数のコンポーネントを選択</b>',
  'ai.agent.nodes.header.tips.shortcut.1':
    '• Shiftキーを押しながらドラッグ：範囲選択',
  'ai.agent.nodes.header.tips.shortcut.2':
    '• Command（Windowsの場合はCtrl）＋クリック：個別選択',

  'ai.agent.nodes.header.save.error.noIntent':
    'インテントを選択してからデプロイしてください',
  'ai.agent.nodes.start.noDelete': '開始ノードは削除できません。',
  'ai.agent.nodes.start.illegal':
    '操作する有効なコンポーネントを選択してください。',
  'ai.agent.nodes.form.name': 'コンポーネントライブラリ',
  'ai.agent.nodes.form.name.small': 'コンポーネント',
  'ai.agent.nodes.form.search': '検索',
  'ai.agent.nodes.form.message': 'ロボットメッセージ',
  'ai.agent.nodes.form.question': '質問をする',
  'ai.agent.nodes.form.tools': 'ツール',
  'ai.agent.nodes.form.condition': '条件',

  'ai.agent.nodes.form.popup.p': 'コンポーネント名を入力してください...',
  'ai.agent.nodes.form.popup.messageText.title': 'ロボットの言い回し',
  'ai.agent.nodes.form.popup.messageImage.radio.1': '画像をアップロード',
  'ai.agent.nodes.form.popup.messageImage.radio.2': 'URLを追加',
  'ai.agent.nodes.form.popup.messageImage.input.url': 'URLを入力',
  'ai.agent.nodes.form.popup.messageImage.input.url.p': 'URLを入力してください',
  'ai.agent.nodes.form.popup.messageImage.input.alt': '画像の説明',
  'ai.agent.nodes.form.popup.messageImage.input.alt.p':
    '説明を入力してください',
  'ai.agent.nodes.form.popup.messageImage.input.note':
    '注：説明も顧客に表示されます',
  'ai.agent.nodes.form.popup.messageImage.input.upload':
    '画像をアップロード、最大10MB',
  'ai.agent.nodes.form.popup.messageImage.input.upload.btn':
    'クリックしてアップロード',

  'ai.agent.nodes.form.popup.MessageVideo.radio.1': '動画をアップロード',
  'ai.agent.nodes.form.popup.MessageVideo.radio.2': 'URLを追加',
  'ai.agent.nodes.form.popup.MessageVideo.input.url': 'URLを入力',
  'ai.agent.nodes.form.popup.MessageVideo.input.url.p': 'URLを入力してください',
  'ai.agent.nodes.form.popup.MessageVideo.input.alt': '動画の説明',
  'ai.agent.nodes.form.popup.MessageVideo.input.alt.p':
    '説明を入力してください',
  'ai.agent.nodes.form.popup.MessageVideo.input.note':
    '注：説明も顧客に表示されます',
  'ai.agent.nodes.form.popup.MessageVideo.input.upload':
    '動画をアップロード、最大100MB',
  'ai.agent.nodes.form.popup.MessageVideo.input.upload.btn':
    'クリックしてアップロード',

  'ai.agent.nodes.form.popup.MessageDoc.radio.1': 'ドキュメントのアップロード',
  'ai.agent.nodes.form.popup.MessageDoc.radio.2': 'URLを追加',
  'ai.agent.nodes.form.popup.MessageDoc.input.url': 'URLを入力',
  'ai.agent.nodes.form.popup.MessageDoc.input.url.p': 'URLを入力してください',
  'ai.agent.nodes.form.popup.MessageDoc.input.alt': 'ドキュメントの説明',
  'ai.agent.nodes.form.popup.MessageDoc.input.alt.p': '説明を入力してください',
  'ai.agent.nodes.form.popup.MessageDoc.input.note':
    '注：説明も顧客に表示されます',
  'ai.agent.nodes.form.popup.MessageDoc.input.upload':
    'ドキュメントをアップロード、最大20MB',
  'ai.agent.nodes.form.popup.MessageDoc.input.upload.btn':
    'クリックしてアップロード',

  'ai.agent.nodes.startNode.select.title.1': 'トリガー方法',
  'ai.agent.nodes.startNode.select.title.1.options.1': 'インテントトリガー',
  'ai.agent.nodes.startNode.select.title.1.p': 'トリガー方法を選択してください',
  'ai.agent.nodes.startNode.select.title.2': 'インテントを選択',
  'ai.agent.nodes.startNode.select.title.2.p': 'インテントを選択',
  'ai.agent.nodes.startNode.var': '変数の転送',
  'ai.agent.nodes.startNode.var.table.1': '動的インテント変数',
  'ai.agent.nodes.startNode.var.table.2':
    '現在のインテリジェントエージェント変数',
  'ai.agent.nodes.startNode.var.table.add.warn':
    '編集中のデータをまず保存してください',

  'ai.agent.nodes.ToolToAgent.title.1': 'デフォルトメッセージ',
  'ai.agent.nodes.ToolToAgent.title.2': '割り当てルール',
  'ai.agent.nodes.ToolToAgent.title.3': 'チケットタイプを設定',
  'ai.agent.nodes.ToolToAgent.title.4': 'チケットの優先度を設定',
  'ai.agent.nodes.ToolToAgent.title.5': 'インバウンドインテントを設定',
  'ai.agent.nodes.ToolToAgent.title.6': '顧客タグを設定',
  'ai.agent.nodes.ToolToAgent.title.span': '(オプション)',
  'ai.agent.nodes.ToolToAgent.radio.title.1': 'システムによる自動割り当て',
  'ai.agent.nodes.ToolToAgent.radio.title.2': '手動指定',
  'ai.agent.nodes.ToolToAgent.select.rule.p': '割り当て方法を選択',
  'ai.agent.nodes.ToolToAgent.select.agent': 'エージェントを選択：',
  'ai.agent.nodes.ToolToAgent.select.team': 'チームを選択：',
  'ai.agent.nodes.ToolToAgent.select.agent.p': 'エージェントを選択してください',
  'ai.agent.nodes.ToolToAgent.select.team.p': 'チームを選択してください',
  'ai.agent.nodes.ToolToAgent.select.type': 'チケットタイプを選択してください',
  'ai.agent.nodes.ToolToAgent.select.priority':
    'チケットの優先度を選択してください',
  'ai.agent.nodes.ToolToAgent.input.default':
    'ただいま、オペレーターにおつなぎします',
  'ai.agent.nodes.ToolFailure.text': '失敗を返します',

  'ai.agent.nodes.ToolLLM.title.1': 'プロンプトを設定',
  'ai.agent.nodes.ToolLLM.checkBox.1':
    'AIGCによって生成されたものを表示するかどうか',
  'ai.agent.nodes.ToolLLM.back.type': '返される結果のタイプ',
  'ai.agent.nodes.ToolLLM.storage.var': '変数に保存',
  'ai.agent.nodes.ToolLLM.storage.var.p': '変数を選択してください',
  'ai.agent.nodes.ToolLLM.back.type.1': 'テキスト',
  'ai.agent.nodes.ToolLLM.contentShow': '結果を表示',
  //aiagent ToolAPI
  'ai.agent.nodes.ToolAPI.title1': '呼び出すAPIを選択',
  'ai.agent.nodes.ToolAPI.title3': 'API管理',
  'ai.agent.nodes.ToolAPI.title2.0': '備考：',
  'ai.agent.nodes.ToolAPI.title2.1': 'HTTPステータスコード',
  'ai.agent.nodes.ToolAPI.title2.2': 'が',
  'ai.agent.nodes.ToolAPI.title2.3': '200',
  'ai.agent.nodes.ToolAPI.title2.4':
    'の場合、システムは現在のAPI呼び出しが成功したと判断し、そうでない場合はフォールバックロジックを実行します。',
  //aiagent ToolWorkHours
  'ai.agent.nodes.ToolWorkHours.title1': '選択工作時間',
  'ai.agent.nodes.ToolWorkHours.workTime': '在工作時間',
  'ai.agent.nodes.ToolWorkHours.notWorkTime': '不在工作時間',
  //aiagent AskQuestionText
  'ai.agent.nodes.AskQuestionText.title1': '顧客からの返信の検証と保存',
  'ai.agent.nodes.AskQuestionText.title2': '変数の検証フォーマットを選択',
  'ai.agent.nodes.AskQuestionText.title21': '検証方法',
  'ai.agent.nodes.AskQuestionText.title3': '検証失敗時の再試行回数',
  'ai.agent.nodes.AskQuestionText.title4': '検証失敗後の返信',
  'ai.agent.nodes.AskQuestionText.title5': '最終検証失敗後の返信',
  'ai.agent.nodes.AskQuestionText.title6': '検証用の正規表現',
  'ai.agent.nodes.AskQuestionText.date.title1': '日付/時間範囲を選択',
  'ai.agent.nodes.AskQuestionText.date.to': '宛先',
  'ai.agent.nodes.AskQuestionText.date.select1': 'カスタム',
  'ai.agent.nodes.AskQuestionText.date.select2': '今日以前の日付を含む',
  'ai.agent.nodes.AskQuestionText.date.select3': '今日以降の日付も含む',
  'ai.agent.nodes.AskQuestionText.int.title1': '数値範囲を入力',
  'ai.agent.nodes.AskQuestionText.reg.title1': '正規表現を入力してください',
  'ai.agent.nodes.AskQuestionText.llm.title1': '请具体描述变量校验方式',
  'ai.agent.nodes.AskQuestionText.SaveValue': '変数に保存',
  'ai.agent.nodes.AskQuestionText.None': '検証不要',
  'ai.agent.nodes.AskQuestionText.Regex': '正規表現',
  'ai.agent.nodes.AskQuestionText.checkType1': '常規校驗方式',
  'ai.agent.nodes.AskQuestionText.checkType2': 'LLM校驗',
  //aiagent AskQuestionButton
  'ai.agent.nodes.AskQuestionButton.title1': 'ガイドボタン',
  'ai.agent.nodes.AskQuestionButton.title2': 'ボタンの配置方式',
  'ai.agent.nodes.AskQuestionButton.type1': '縦向',
  'ai.agent.nodes.AskQuestionButton.type2': '横向',
  'ai.agent.nodes.AskQuestionButton.addButton': 'ボタンを追加',
  //aiagent AskQuestionForm
  'ai.agent.nodes.AskQuestionForm.title1': '属性名',
  'ai.agent.nodes.AskQuestionForm.title2': '表示名',
  'ai.agent.nodes.AskQuestionForm.title3': '属性入力プロンプト',
  'ai.agent.nodes.AskQuestionForm.title4': '選択属性的形式',
  'ai.agent.nodes.AskQuestionForm.title5': '属性的値',
  'ai.agent.nodes.AskQuestionForm.title6': '値を追加',
  'ai.agent.nodes.AskQuestionForm.attributeType.1': '一行入力フィールド',
  'ai.agent.nodes.AskQuestionForm.attributeType.2': '複数行入力フィールド',
  'ai.agent.nodes.AskQuestionForm.attributeType.3': '単一選択ドロップダウン',
  'ai.agent.nodes.AskQuestionForm.attributeType.4': '複数選択ドロップダウン',
  'ai.agent.nodes.AskQuestionForm.attributeType.5': 'ラジオボタン',
  'ai.agent.nodes.AskQuestionForm.attributeType.6': 'チェックボックス',
  'ai.agent.nodes.AskQuestionForm.attributeType.7': '日付（年月日まで）',
  'ai.agent.nodes.AskQuestionForm.attributeType.8': '日付（時分秒まで）',
  'ai.agent.nodes.AskQuestionForm.attributeType.9':
    '日付範囲選択（年月日まで）',
  'ai.agent.nodes.AskQuestionForm.attributeType.10':
    '日付範囲選択（時分秒まで）',
  'ai.agent.nodes.AskQuestionForm.attributeType.11': 'ファイルアップロード',
  'ai.agent.nodes.AskQuestionForm.addForm': '変数を追加',
  'ai.agent.nodes.AskQuestionForm.addForm.button1': '送信',
  'ai.agent.nodes.AskQuestionForm.addForm.button2': 'キャンセル',
  'ai.agent.nodes.AskQuestionForm.show.upload': 'クリックしてアップロード',
  'ai.agent.nodes.AskQuestionForm.show.upload.tips':
    'ファイルをアップロード、最大20MB',
  //aiagent AskQuestionLLM
  'ai.agent.nodes.AskQuestionLLM.tip1': '属性名を入力してください',
  'ai.agent.nodes.AskQuestionLLM.tip2': '属性コードを入力してください',
  'ai.agent.nodes.AskQuestionLLM.title1': '属性名',
  'ai.agent.nodes.AskQuestionLLM.title2': '属性コード',
  'ai.agent.nodes.AskQuestionLLM.title3': '属性フォーマット要件',
  'ai.agent.nodes.AskQuestionLLM.title4': 'エージェントの変数に保存',

  //aiagent Rag
  'ai.agent.nodes.Rag.knowledgeType': '知識ベースタイプ',
  'ai.agent.nodes.Rag.selectKnowledgeTag': '知識タグを選択',
  'ai.agent.nodes.Rag.ragName': 'RAGナレッジベース名',
  'ai.agent.nodes.Rag.reUserAsk': 'お客様からのお問い合わせ質問を書き直します',
  'ai.agent.nodes.Rag.selectKnowledgeType': '知識ベースタイプを選択します',
  'ai.agent.nodes.Rag.faqKnoewledge': 'FAQナレッジベース',
  'ai.agent.nodes.Rag.ragKnoewledge': 'RAGナレッジベース',
  'ai.agent.nodes.Rag.selectRagKnowledgeType':
    'AIGC RAG ナレッジベースを選択します',
  'ai.agent.nodes.Rag.a&qStyle': '質疑応答スタイル',
  'ai.agent.nodes.Rag.a&qStyle1': '簡潔明快な',
  'ai.agent.nodes.Rag.a&qStyle2': '優しいお付き合い',
  'ai.agent.nodes.Rag.cantUnderstand': 'ロボットの知識不足時の応答',
  'ai.agent.nodes.Rag.tip1':
    '温柔陪伴を開くと、AIGC Chatbotはお客様により優しい回答を提供します。',
  'ai.agent.nodes.Rag.tip2':
    'ここに設定されているのは、ロボットに知識がない場合の返信です',
  //aiagent Intent
  'ai.agent.nodes.Intent.intentName': 'インテント名',
  'ai.agent.nodes.Intent.intentJudgmentBasis': 'インテント判断の根拠',
  'ai.agent.nodes.Intent.tip':
    '注：判断の根拠は、AIGCが現在の顧客の入力に基づいて現在のインテントに属するかどうかを判断するための重要な根拠です。慎重に記入してください。',
  'ai.agent.nodes.Intent.addIntent': 'インテントを追加',
  'ai.agent.testAgent': 'テストボット',
  // 设置等待回复
  'ai.agent.waiting.reply.table.waiting.time': '待ち時間',
  'ai.agent.waiting.reply.table.reminder.language': 'リマインダーの言い回し',
  'ai.agent.script.add': '表現を追加',
  'ai.agent.script.time.minute': '分',
  'ai.agent.script.time.second': '秒',
  'ai.agent.script.time.tips': '0から59までの数字を入力してください',
  'ai.agent.script.settings.num.tips':
    'リマインダー設定は5つ以下にしてください',
  'ai.agent.script.settings.not.empty.tips':
    '待ち時間とリマインダーの表現は空白にできません！',

  'ai.agent.nodes.start.type.2':
    'デフォルトのウェルカムインテリジェントエージェント',
  'ai.agent.nodes.start.type.2.content':
    '顧客がWEBオンラインチャット、APPオンラインチャット、Shopify、WeChatミニプログラムを通じて企業とコミュニケーションをとる際、最初に現在のウェルカムインテリジェントエージェントが実行されます',
  'ai.agent.nodes.start.type.3': 'デフォルトのフォールバックインテント',
  'ai.agent.nodes.start.type.3.content':
    'システムがインテリジェントエージェントをマッチングできなかった場合、デフォルトのインテントが実行されます',

  'ai.agent.nodes.ToolVariableSetting.title1': '変数を選択',
  'ai.agent.nodes.ToolVariableSetting.title2': '調整ルールを選択',
  'ai.agent.nodes.ToolVariableSetting.title3': 'プレフィックス内容を入力',
  'ai.agent.nodes.ToolVariableSetting.title4': 'サフィックス内容を入力',
  'ai.agent.nodes.ToolVariableSetting.title5': '特定の値を入力',
  'ai.agent.nodes.ToolVariableSetting.title6': '数値範囲を入力',
  'ai.agent.nodes.ToolVariableSetting.title7': '合計値を入力 "1"',
  'ai.agent.nodes.ToolVariableSetting.title8': '合計値を入力 "2"',
  'ai.agent.nodes.ToolVariableSetting.title9': '差の値を入力 "1"',
  'ai.agent.nodes.ToolVariableSetting.title10': '差の値を入力 "2"',
  'ai.agent.nodes.ToolVariableSetting.title11': '積の値を入力 "1"',
  'ai.agent.nodes.ToolVariableSetting.title12': '積の値を入力 "2"',
  'ai.agent.nodes.ToolVariableSetting.title13': '商の値を入力 "1"',
  'ai.agent.nodes.ToolVariableSetting.title14': '商の値を入力 "2"',
  'ai.agent.nodes.ToolVariableSetting.title15': '切り上げ値を入力',
  'ai.agent.nodes.ToolVariableSetting.title16': '切り捨て値を入力',
  'ai.agent.nodes.ToolVariableSetting.title17': '作業依頼書の属性',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.num1': '等しい',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.num2': '合計',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.num3': '差',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.num4': '積',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.num5': '商',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.num6': '切り上げ',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.num7': '切り捨て',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.str1':
    'プレフィックスを追加',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.str2': 'サフィックスを追加',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.str3': '空白を削除',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.str4':
    'プレフィックスとサフィックスを追加',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.str5': '等しい',
  'ai.agent.nodes.ToolVariableSetting.node.title': '更新変数',

  'ai.agent.nodes.ToolUpdateTicket.title1': '顧客属性',
  'ai.agent.nodes.ToolUpdateTicket.title2': '顧客属性値',
  'ai.agent.nodes.ToolUpdateTicket.title3': '他の属性を編集',
  'ai.agent.nodes.ToolUpdateTicket.title4': '属性値',
  'ai.agent.nodes.ToolUpdateTicket.title5': 'チケット属性',
  'ai.agent.nodes.ToolUpdateTicket.title6':
    '属性値ルール：resolved(解決済み) terminated(終了)',
  'ai.agent.nodes.ToolUpdateTicket.title7':
    '属性値ルール：(P5)低級影響 (P4)中級影響 (P3)重大影響 (P2)緊急処理 (P1)サービスクラッシュ',
  'ai.agent.nodes.ToolUpdateTicket.title8':
    '属性値ルール：1（男性）、2（女性）、3（その他）',
  'ai.agent.nodes.ToolUpdateTicket.title9':
    '属性値ルール：1（VIPユーザー）、2（通常ユーザー）',
  'ai.agent.nodes.ToolUpdateTicket.title10':
    '標準の日付形式を入力してください。例：1979-04-01',
  'ai.agent.nodes.ToolUpdateTicket.title1005':
    'オプション値を入力してください（チケット拡張属性定義から取得）、"オプションタブ"に追加され、ユーザーは1つだけ選択可能です',
  'ai.agent.nodes.ToolUpdateTicket.title1006':
    'オプション値を入力してください（チケット拡張属性定義から取得）、複数の値はカンマ（,）で区切り、ユーザーは複数選択可能です',
  'ai.agent.nodes.ToolUpdateTicket.title1007':
    '属性値ルール：true（オン） / false（オフ）',
  'ai.agent.nodes.ToolUpdateTicket.title1008':
    '標準の日時形式を入力してください。例：2025-04-03 00:00:00',

  'ai.agent.nodes.ConditionCheck.title1': '条件',
  'ai.agent.nodes.ConditionCheck.title2': 'それ以外',
  'ai.agent.nodes.ConditionCheck.title3': '失敗',
  'ai.agent.nodes.ConditionCheck.title4': 'もし',
  'ai.agent.nodes.ConditionCheck.title5': '次と等しい',
  'ai.agent.nodes.ConditionCheck.title6': '次を含む',
  'ai.agent.nodes.ConditionCheck.title7': '空である',
  'ai.agent.nodes.ConditionCheck.title8': '空ではない',
  'ai.agent.nodes.ConditionCheck.title9': '顧客の入力',
  'ai.agent.nodes.ConditionCheck.title10': '条件を追加',
  'ai.agent.nodes.ConditionCheck.title11': '次より大きい',
  'ai.agent.nodes.ConditionCheck.title12': '次より小さい',

  'ai.agent.nodes.AskQuestionLLM.addForm': '属性を追加',
  'ai.agent.nodes.ToolSetCustomerTag.title1': 'タグを選択',
  // MessageHotIssue
  'ai.agent.nodes.MessageHotIssue.title1': '設定方法',
  'ai.agent.nodes.MessageHotIssue.title1.type1': '手動編集',
  'ai.agent.nodes.MessageHotIssue.title1.type2': '自動推奨',
  'ai.agent.nodes.MessageHotIssue.title2': '表示形式',
  'ai.agent.nodes.MessageHotIssue.title2.type1': '横型',
  'ai.agent.nodes.MessageHotIssue.title2.type2': '縦型',
  'ai.agent.nodes.MessageHotIssue.title3': '初期言語を選択',
  'ai.agent.nodes.MessageHotIssue.title4': 'カテゴリ名',
  'ai.agent.nodes.MessageHotIssue.title4.tip': 'カテゴリ名を入力してください',
  'ai.agent.nodes.MessageHotIssue.title5': '質問',
  'ai.agent.nodes.MessageHotIssue.title5.tip': '質問を入力してください',
  'ai.agent.nodes.MessageHotIssue.title6': '質問を追加',
  'ai.agent.nodes.MessageHotIssue.title7': 'カテゴリを追加',
  'ai.agent.nodes.MessageHotIssue.title8': 'よくある質問',
  'ai.agent.nodes.MessageHotIssue.title9': '自動推奨',
  'ai.agent.nodes.MessageHotIssue.title10': 'スマート翻訳',
  'ai.agent.nodes.MessageHotIssue.title11': 'ターゲット言語',
  'ai.agent.nodes.MessageHotIssue.title12': 'FAQ自動推奨ルール',
  'ai.agent.nodes.MessageHotIssue.title12.tip1': '過去',
  'ai.agent.nodes.MessageHotIssue.title12.tip2': '日間の問い合わせ件数トップ{',
  'ai.agent.nodes.MessageHotIssue.title12.tip3': 'のFAQを自動表示',
  'ai.agent.nodes.MessageHotIssue.title12.remark':
    '備考：1つのFAQに複数の聞き方がある場合、最初のもののみ表示されます',

  'ai.agent.nodes.AskQuestionCard.title1': 'ご注文を選択してください',
  'ai.agent.nodes.AskQuestionCard.title2': 'カードレイアウト',
  'ai.agent.nodes.AskQuestionCard.title3':
    'これは製品タイトルのサンプルです。製品の主な特徴をここに記述できます',
  'ai.agent.nodes.AskQuestionCard.title4': '¥0.00',
  'ai.agent.nodes.AskQuestionCard.title5': '受領済み',
  'ai.agent.nodes.AskQuestionCard.title6': 'カルーセル',
  'ai.agent.nodes.AskQuestionCard.title7': 'リスト',
  'ai.agent.nodes.AskQuestionCard.title8': 'カードデータ',
  'ai.agent.nodes.AskQuestionCard.title9': '画像URL',
  'ai.agent.nodes.AskQuestionCard.title10': '商品タイトル',
  'ai.agent.nodes.AskQuestionCard.title11': '商品価格（任意）',
  'ai.agent.nodes.AskQuestionCard.title12': '商品数量（任意）',
  'ai.agent.nodes.AskQuestionCard.title13': '注文ステータス（任意）',
  'ai.agent.nodes.AskQuestionCard.title14': '備考：JSON変数です',
  'ai.agent.nodes.AskQuestionCard.title15': 'JSON変数のフォーマット：',
  'ai.agent.nodes.AskQuestionCard.tip1':
    'APIを呼び出してデータを取得し、カードデータ用のリストとしてJSON形式の変数に格納します。リスト内の各JSONオブジェクトのプロパティを、以下の対応するフィールドにマッピングしてください。',

  'ai.agent.nodes.ToolDelay.title1': 'x秒遅延',
  'ai.agent.nodes.header.save.error.formCancle':
    'フォームのキャンセルノードを接続してください',

  'ai.agent.exit.confirm': '未保存の変更があります。本当に離れますか？',
};
