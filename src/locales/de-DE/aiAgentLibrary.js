export default {
  'aiAgentLibrary.banner.title': '<PERSON><PERSON><PERSON> zu Handlungen',
  'aiAgentLibrary.banner.subtitle': 'ConnectNow AI Agent',
  'aiAgentLibrary.tab.list.1': 'Alle',
  'aiAgentLibrary.tab.list.2': 'Finanz',
  'aiAgentLibrary.tab.list.3': 'Erneuerbare-Energien',
  'aiAgentLibrary.tab.list.4': 'Spiele',
  'aiAgentLibrary.tab.list.5': 'E-Commerce',
  'aiAgentLibrary.tab.list.6': 'Fertigung',
  'aiAgentLibrary.agent.list.content.1.title': 'Kundenprobleme:',
  'aiAgentLibrary.agent.list.content.2.title': 'KI-Agent-Lösung:',
  'aiAgentLibrary.agent.list.content.3.title': 'Dialogbeispiele:',
  'aiAgentLibrary.agent.list.content.3.trigger.title': 'Auslösemethode',

  'aiAgentLibrary.agent.list.1.title': 'Personalisierter Finanzberater',
  'aiAgentLibrary.agent.list.1.industry': 'Finanzwesen',
  'aiAgentLibrary.agent.list.1.content.1.list.1':
    'Schwierigkeit bei der Auswahl geeigneter Finanzprodukte aus zahlreichen Optionen, die zur finanziellen Situation, Risikotoleranz und den Anlagezielen passen',
  'aiAgentLibrary.agent.list.1.content.1.list.2':
    'Traditionelle Risikobeurteilungsfragebögen sind starr und erfassen nicht die wahren Bedürfnisse und Bedenken der Kunden',
  'aiAgentLibrary.agent.list.1.content.1.list.3':
    'Hohe Zugangshürden zu Finanzberatern verhindern personalisierte, zeitnahe Anlageberatung',
  'aiAgentLibrary.agent.list.1.content.2.list.1':
    'Dieser intelligente Agent analysiert das Anlagekapital und die Risikopräferenzen der Kunden, um personalisierte Finanzproduktempfehlungen in natürlicher Sprache zu liefern.',
  'aiAgentLibrary.agent.list.1.content.3.list.1':
    'Ich möchte investieren, weiß aber nicht was. Ich habe 100.000 € verfügbares Kapital.',
  'aiAgentLibrary.agent.list.1.content.3.list.2':
    'Ich interessiere mich für Vermögensverwaltung. Können Sie mir etwas empfehlen?',

  'aiAgentLibrary.agent.list.2.title': 'Kreditantrags-Assistent',
  'aiAgentLibrary.agent.list.2.industry': 'Finanzwesen',
  'aiAgentLibrary.agent.list.2.content.1.list.1':
    'Mangelnde Klarheit über Kreditvoraussetzungen und erforderliche Unterlagen führt zu zeitaufwändigen Mehrfachanfragen',
  'aiAgentLibrary.agent.list.2.content.1.list.2':
    'Bankkreditbearbeiter verbringen übermäßig viel Zeit mit Vorprüfung, Dokumentensammlung und Verifizierungsprozessen',
  'aiAgentLibrary.agent.list.2.content.1.list.3':
    'Lange Kreditantragsprozesse mit begrenzter Transparenz führen zu schlechter Kundenerfahrung',
  'aiAgentLibrary.agent.list.2.content.2.list.1':
    'Diese Lösung reduziert effektiv manuelle Eingriffe, beschleunigt die vorläufige Kreditbearbeitung und behält dabei hochgradig personalisierte Serviceerlebnisse bei.',
  'aiAgentLibrary.agent.list.2.content.3.list.1':
    'Ich möchte einen Renovierungskredit beantragen',
  'aiAgentLibrary.agent.list.2.content.3.list.2':
    'Ich interessiere mich für einen Kreditantrag',

  'aiAgentLibrary.agent.list.3.title': 'Versicherungsansprüche Express',
  'aiAgentLibrary.agent.list.3.industry': 'Finanzwesen',
  'aiAgentLibrary.agent.list.3.content.1.list.1':
    'Komplexe Schadenregulierungsprozesse verunsichern Kunden nach Krankheit oder Unfällen',
  'aiAgentLibrary.agent.list.3.content.1.list.2':
    'Aufwendige Dokumentationsanforderungen erschweren das Verständnis der Anspruchskriterien in den Versicherungsbedingungen',
  'aiAgentLibrary.agent.list.3.content.1.list.3':
    'Mangelnde Transparenz im Antragsprozess verhindert zeitnahe Status- und Fortschrittsupdates',
  'aiAgentLibrary.agent.list.3.content.2.list.1':
    'Diese Lösung reduziert die Bearbeitungszeit von Schadensmeldungen erheblich, steigert die Kundenzufriedenheit und entlastet den Kundenservice, wodurch gleichzeitig die Effizienz des Versicherungsservice und das Kundenerlebnis verbessert werden.',
  'aiAgentLibrary.agent.list.3.content.3.list.1':
    'Ich muss einen Antrag für eine Operation stellen, die ich letzte Woche hatte. Wie gehe ich vor?',
  'aiAgentLibrary.agent.list.3.content.3.list.2':
    'Ich möchte einen Versicherungsanspruch geltend machen',

  'aiAgentLibrary.agent.list.4.title': 'Versicherungswissen-Assistent',
  'aiAgentLibrary.agent.list.4.content.1.list.1':
    'Komplexes Finanzwissen erschwert durchschnittlichen Nutzern das Verständnis und die Anwendung',
  'aiAgentLibrary.agent.list.4.content.1.list.2':
    'Fragmentierte traditionelle Finanzwissensquellen erschweren gezielte Antworten',
  'aiAgentLibrary.agent.list.4.content.1.list.3':
    'Schwer verständliche Finanzterminologie',
  'aiAgentLibrary.agent.list.4.content.1.list.4':
    'Häufige Aktualisierungen von Finanzrichtlinien erschweren Aktualität',
  'aiAgentLibrary.agent.list.4.content.1.list.5':
    'Mangel an szenariobasierter Finanzwissensinterpretation schafft Kluft zwischen Theorie und Praxis',
  'aiAgentLibrary.agent.list.4.content.2.list.1':
    'Dieser intelligente Agent nutzt eine umfassende Finanzwissensbasis kombiniert mit LLM-Sprachverständnis, um klare, szenariobasierte Finanzwissenserklärungen bereitzustellen und Nutzern beim besseren Verständnis und der Anwendung von Finanzkonzepten zu helfen.',
  'aiAgentLibrary.agent.list.4.content.3.list.1':
    'Was ist versicherbares Interesse? Für wen kann ein Versicherungsnehmer versicherbares Interesse haben?',
  'aiAgentLibrary.agent.list.4.content.3.list.2':
    'Warum erfordern Versicherungsanträge umfangreiche Gesundheitsfragebögen, einschließlich Familienanamnese?',

  'aiAgentLibrary.agent.list.5.title':
    'Intelligenter Lead-Generierungs-Assistent',
  'aiAgentLibrary.agent.list.5.industry': 'Finanzwesen',
  'aiAgentLibrary.agent.list.5.content.1.list.1':
    'Traditionelle formularbasierte Lead-Erfassung ist unflexibel und führt zu geringer Kundenbereitschaft',
  'aiAgentLibrary.agent.list.5.content.1.list.2':
    'Niedrige Konversionsrate von Anfragen zu Leads',
  'aiAgentLibrary.agent.list.5.content.1.list.3':
    'Uneinheitliche Lead-Informationsqualität beeinträchtigt Follow-up-Effektivität',
  'aiAgentLibrary.agent.list.5.content.1.list.4':
    'Mangel an personalisierter Führung und Interaktion',
  'aiAgentLibrary.agent.list.5.content.1.list.5':
    'Keine Echtzeit-Identifizierung hochwertiger Leads möglich',
  'aiAgentLibrary.agent.list.5.content.2.list.1':
    'Dieser intelligente Agent sammelt Kundeninformationen durch natürlichen Dialog, kombiniert szenariobasierte Interaktion mit Wertdemonstration zur Verbesserung der Lead-Konversionsraten und strukturiert gesammelte Informationen für präzise Marketinginitiativen.',
  'aiAgentLibrary.agent.list.5.content.3.list.1':
    'Was kostet Ihre Schwerkrankheiten-Versicherung? Ich möchte mehr darüber erfahren',
  'aiAgentLibrary.agent.list.5.content.3.list.2':
    'Wie hoch ist die Prämie für Ihre Lebensversicherung?',

  // 游戏start
  'aiAgentLibrary.agent.list.game.1.title': 'Spiel-Auflade-Assistent',
  'aiAgentLibrary.agent.list.game.1.content.1.list.1':
    'Zahlungsprobleme sind die häufigste Art von Kundendienstanfragen im Gaming-Bereich',
  'aiAgentLibrary.agent.list.game.1.content.1.list.2':
    'Die Analyse fehlgeschlagener Zahlungen ist komplex und erfordert mehrere Fehlerbehebungsschritte',
  'aiAgentLibrary.agent.list.game.1.content.1.list.3':
    'Herkömmlicher Kundendienst hat Schwierigkeiten, Transaktionsdaten schnell zu analysieren und präzise Lösungen anzubieten',
  'aiAgentLibrary.agent.list.game.1.content.2.list.1':
    'Diese Lösung identifiziert intelligent Zahlungsprobleme in Spielen, fragt automatisch Transaktionsdaten ab und bietet personalisierte Lösungen, wodurch Wartezeiten der Kunden und der Druck auf das Servicepersonal erheblich reduziert werden. Das System behandelt häufige Probleme wie nicht gutgeschriebene Zahlungen und Zahlungsanomalien und bietet Echtzeit-Anleitungen und Statusaktualisierungen, was die Zahlungsservice-Erfahrung und die Effizienz der Problemlösung erheblich verbessert.',
  'aiAgentLibrary.agent.list.game.1.content.3.list.1':
    'Ich habe gerade 100 Euro bezahlt, aber keine Diamanten im Spiel erhalten. Was ist passiert?',
  'aiAgentLibrary.agent.list.game.1.content.3.list.2':
    'Ich habe gerade eine Zahlung getätigt, aber sie wurde meinem Konto nicht gutgeschrieben',

  'aiAgentLibrary.agent.list.game.2.title': 'Spiel-Fehlerbericht-Assistent',
  'aiAgentLibrary.agent.list.game.2.content.1.list.1':
    'Unklare Beschreibungen der Spieler bei Fehlerberichten',
  'aiAgentLibrary.agent.list.game.2.content.1.list.2':
    'Kundendienst muss wiederholt nach grundlegenden Informationen fragen',
  'aiAgentLibrary.agent.list.game.2.content.1.list.3':
    'Mangel an einheitlicher Fehlerklassifizierung und Prioritätssystem',
  'aiAgentLibrary.agent.list.game.2.content.1.list.4':
    'Spieler sind sich des Bearbeitungsstatus ihrer Fehlerberichte nicht bewusst',
  'aiAgentLibrary.agent.list.game.2.content.2.list.1':
    'Diese Lösung sammelt durch vereinfachte geführte Dialoge schnell wichtige Fehlerinformationen und ermöglicht eine effiziente Problemklassifizierung und -bearbeitung. Das System weist automatisch Prioritäten basierend auf dem Fehlertyp zu und bietet Spielern klare Erwartungen zu nachfolgenden Prozessen, was die Benutzererfahrung und die Effizienz des Kundendienstes erheblich verbessert.',
  'aiAgentLibrary.agent.list.game.2.content.3.list.1':
    'Es gibt einen Systemfehler - meine Ausrüstungsverstärkung ist fehlgeschlagen, Materialien und Gold wurden abgezogen, aber die Ausrüstung wurde nicht verstärkt.',
  'aiAgentLibrary.agent.list.game.2.content.3.list.2':
    'Das Spiel ruckelt extrem, völlig unspielbar.',

  'aiAgentLibrary.agent.list.game.3.title': 'Spielberatungsexperte',
  'aiAgentLibrary.agent.list.game.3.content.1.list.1':
    'Zahlreiche Spielevents und häufige Versionsupdates erzeugen einen hohen Informationsbedarf zu Eventregeln, Belohnungen, Teilnahmebedingungen und Update-Inhalten. Die Ankündigungen sind verstreut, was es schwierig macht, schnell Antworten zu finden.',
  'aiAgentLibrary.agent.list.game.3.content.2.list.1':
    'Diese Lösung erkennt intelligent Anfragen zu Spielevents und Versionsupdates und stellt automatisch Eventregeln, Methoden zum Erhalt von Belohnungen und Update-Informationen bereit, wodurch die Zeit, die Spieler mit der Informationssuche verbringen, erheblich reduziert wird.',
  'aiAgentLibrary.agent.list.game.3.content.3.list.1':
    'Gibt es in letzter Zeit irgendwelche neuen Veranstaltungen?',
  'aiAgentLibrary.agent.list.game.3.content.3.list.2':
    'Was ist das "Bewegung! Geldsammelnder Unsterblicher"-Gameplay im Traumsternpark?',

  // 游戏end

  // 新能源 start
  'aiAgentLibrary.agent.list.newEnergy.1.title': 'Störungsalarm-Beratung',
  'aiAgentLibrary.agent.list.newEnergy.1.content.1.list.1':
    'Benutzer müssen bei Kraftwerksstörungen schnell Ursachen und Lösungen verstehen',
  'aiAgentLibrary.agent.list.newEnergy.1.content.1.list.2':
    'Reduzierung des Arbeitsaufwands für den Kundendienst bei wiederholter Beantwortung gleicher Störungsfragen',
  'aiAgentLibrary.agent.list.newEnergy.1.content.1.list.3':
    'Bereitstellung standardisierter Störungsbehandlungsverfahren zur Verringerung des Risikos von Bedienungsfehlern',
  'aiAgentLibrary.agent.list.newEnergy.1.content.1.list.4':
    '24/7 Echtzeit-Reaktion auf Störungsberatungsbedarf',
  'aiAgentLibrary.agent.list.newEnergy.1.content.2.list.1':
    'Diese Lösung identifiziert Störungen intelligent und bietet standardisierte Bearbeitungsverfahren, verbessert deutlich die Effizienz der Störungsreaktion, reduziert den manuellen Kundendienst-Arbeitsaufwand und erzielt duale Verbesserungen bei Wartungseffizienz und Serviceerlebnis. Die 24/7-Echtzeit-Reaktion und der interaktive Service stellen sicher, dass Benutzer zeitnahe, professionelle Anleitung zur Störungsbehebung erhalten.',
  'aiAgentLibrary.agent.list.newEnergy.1.content.3.list.1':
    'Wenn das System einen Kraftwerksstörungsalarm erkennt, öffnet es das Gesprächsfenster, ohne dass eine Benutzeranfrage erforderlich ist, und übermittelt direkt Störungsinformationen und Bearbeitungsvorschläge.',

  'aiAgentLibrary.agent.list.newEnergy.2.title': 'Firmware-Upgrade-Assistent',
  'aiAgentLibrary.agent.list.newEnergy.2.content.1.list.1':
    'Benutzer müssen Firmware-Upgrade-Dienste manuell beantragen',
  'aiAgentLibrary.agent.list.newEnergy.2.content.1.list.2':
    'Identifizierung von Firmware-Upgrade-Anforderungen für mehrsprachige Benutzer',
  'aiAgentLibrary.agent.list.newEnergy.2.content.1.list.3':
    'Geräte-Parameterkonfigurationsanforderungen (Erfassungsfrequenz, Sprachpakete usw.) sind verstreut',
  'aiAgentLibrary.agent.list.newEnergy.2.content.1.list.4':
    'Mangel an Echtzeit-Statusrückmeldung während des Upgrade-Prozesses',
  'aiAgentLibrary.agent.list.newEnergy.2.content.2.list.1':
    'Diese Lösung ermöglicht durch intelligente semantische Erkennung und automatisierte Prozesse eine schnelle Reaktion auf Firmware-Upgrade-Anforderungen mehrsprachiger Benutzer und bietet Echtzeit-Statusrückmeldungen, die die Effizienz und Benutzererfahrung von Geräte-Upgrade-Diensten erheblich verbessern.',
  'aiAgentLibrary.agent.list.newEnergy.2.content.3.list.1':
    'Ich muss die Firmware aktualisieren',
  'aiAgentLibrary.agent.list.newEnergy.2.content.3.list.2':
    'Ich möchte den Wechselrichter auf die neueste Version aktualisieren',

  'aiAgentLibrary.agent.list.newEnergy.3.title':
    'Wechselrichter-Wissensexperte',
  'aiAgentLibrary.agent.list.newEnergy.3.content.1.list.1':
    'Benutzern fehlt ein tiefgreifendes Verständnis der technischen Parameter und Funktionen von Wechselrichtern',
  'aiAgentLibrary.agent.list.newEnergy.3.content.1.list.2':
    'Fehlerdiagnose- und Wartungsinformationen sind verstreut und schwer schnell zugänglich',
  'aiAgentLibrary.agent.list.newEnergy.3.content.1.list.3':
    'Produktauswahlentscheidungen erfordern professionelle Wissensunterstützung',
  'aiAgentLibrary.agent.list.newEnergy.3.content.1.list.4':
    'Installations- und Konfigurationsprozesse für verschiedene Wechselrichtertypen sind komplex',
  'aiAgentLibrary.agent.list.newEnergy.3.content.1.list.5':
    'Technologieaktualisierungen erfolgen schnell, sodass Benutzer mit den neuesten Entwicklungen kaum Schritt halten können',
  'aiAgentLibrary.agent.list.newEnergy.3.content.2.list.1':
    'Diese Lösung bietet professionelle Wissensunterstützung für Wechselrichterauswahl, -installation, -konfiguration, -fehlerdiagnose und -wartung durch den Aufbau einer umfassenden Wechselrichter-Wissensdatenbank in Kombination mit intelligenter semantischer Erkennungstechnologie. Das System identifiziert die spezifischen Bedürfnisse der Benutzer und bietet gezielte technische Antworten und Betriebsanleitungen, was das Verständnis und die Effizienz der Benutzer bei der Verwendung von Wechselrichterprodukten erheblich verbessert.',
  'aiAgentLibrary.agent.list.newEnergy.3.content.3.list.1':
    'Ich habe gerade einen Wechselrichter gekauft, wie sollte ich ihn installieren?',
  'aiAgentLibrary.agent.list.newEnergy.3.content.3.list.2':
    'Ein Fehlercode wird angezeigt, was bedeutet Fehlercode 002?',
  // 新能源 end

  // 电商&零售 start

  'aiAgentLibrary.agent.list.retail.1.title': 'Sorgenfreier Rückgabe-Assistent',
  'aiAgentLibrary.agent.list.retail.1.content.1.list.1':
    'Rückgabe- und Umtauschrichtlinien sind komplex und schwer verständlich; Kunden wissen nicht, ob sie berechtigt sind oder welche Unterlagen benötigt werden.',
  'aiAgentLibrary.agent.list.retail.1.content.1.list.2':
    'Der Antragsprozess ist umständlich, erfordert viele Formulare und das Hochladen von Nachweisen.',
  'aiAgentLibrary.agent.list.retail.1.content.1.list.3':
    'Der Status von Rückgaben und Umtausch ist intransparent, Rückerstattungs- oder Versandzeiten sind unklar, Kunden müssen wiederholt nachfragen.',
  'aiAgentLibrary.agent.list.retail.1.content.2.list.1':
    'Vereinfacht den komplexen Rückgabeprozess zu einem dialogbasierten Service. Der Agent führt Kunden proaktiv durch den Antrag, automatisiert die meisten Schritte und informiert in Echtzeit über den Status.',
  'aiAgentLibrary.agent.list.retail.1.content.3.list.1':
    'Ich möchte eine Rückgabe machen.',
  'aiAgentLibrary.agent.list.retail.1.content.3.list.2':
    'Bitte helfen Sie mir, meinen Einkauf zurückzugeben.',

  'aiAgentLibrary.agent.list.retail.2.title':
    'Intelligenter Assistent für Bestellstatus & Sendungsverfolgung',
  'aiAgentLibrary.agent.list.retail.2.content.1.list.1':
    '“Wo ist meine Bestellung?“ ist die am häufigsten gestellte Frage im E-Commerce-Kundenservice. Kunden müssen die Sendungsverfolgungsnummer manuell auf externen Websites überprüfen, und die Statusmeldungen (z. B. „im Paketzentrum eingetroffen“) sind oft kryptisch. Bei Verzögerungen werden Kunden unruhig und sind über die Gründe im Unklaren.',
  'aiAgentLibrary.agent.list.retail.2.content.2.list.1':
    'Dieser Agent versorgt Kunden proaktiv mit leicht verständlichen Status-Updates zu Bestellung und Versand. Er übersetzt Logistik-Fachjargon in Alltagssprache und erklärt bei Abweichungen wie Verspätungen oder Zustellfehlern von sich aus die Ursache und bietet Lösungen an. So verwandelt er die Sorge des Kunden in Vertrauen.',
  'aiAgentLibrary.agent.list.retail.2.content.3.list.1':
    'Meinen Bestellstatus prüfen.',
  'aiAgentLibrary.agent.list.retail.2.content.3.list.2':
    'Mein Paket verfolgen.',

  'aiAgentLibrary.agent.list.retail.3.title':
    'Intelligenter Warenkorb-Rückgewinnungsberater',
  'aiAgentLibrary.agent.list.retail.3.content.1.list.1':
    'Kunden legen Produkte in den Warenkorb, schließen den Kauf jedoch aus verschiedenen Gründen nicht ab (Preisüberlegungen, Versandkosten, Produktzweifel, Ablenkungen).',
  'aiAgentLibrary.agent.list.retail.3.content.1.list.2':
    'Herkömmliche Rückgewinnungsmethoden (z. B. E-Mail-Erinnerungen) sind wenig effektiv und adressieren nicht die eigentlichen Bedenken der Kunden.',
  'aiAgentLibrary.agent.list.retail.3.content.2.list.1':
    'Nach einer gewissen Zeit des Warenkorbabbruchs initiiert der Agent über Instant-Messaging-Kanäle (z. B. Web, WhatsApp) einen freundlichen, nicht aufdringlichen Dialog, um die Gründe für den Abbruch zu verstehen und mit individuellen Lösungen (z. B. Gutscheine, Beantwortung von Fragen, Bedenken ausräumen) den Abschluss zu fördern.',
  'aiAgentLibrary.agent.list.retail.3.content.3.list.1':
    'Sendet nach einer gewissen Zeit des Warenkorbabbruchs proaktiv eine Nachricht an den Nutzer.',
  // 电商&零售 end

  // 制造 start
  'aiAgentLibrary.agent.list.manufacturing.1.title':
    'Intelligenter Assistent für Geräte-Störungsmeldung',
  'aiAgentLibrary.agent.list.manufacturing.1.content.1.list.1':
    ' <b>Ineffiziente und ungenaue Informationsübermittlung:</b> Bei Störungsmeldungen per Telefon oder E-Mail fällt es Kunden schwer, technische Probleme präzise zu beschreiben; wichtige Angaben wie Seriennummer oder Fehlercode werden oft vergessen.',
  'aiAgentLibrary.agent.list.manufacturing.1.content.1.list.2':
    ' <b>Hohe Kommunikationskosten:</b> Das Serviceteam muss wiederholt nachfragen, um vollständige Informationen zu erhalten, was zu langen Diagnosezeiten und Kundenunzufriedenheit führt.',
  'aiAgentLibrary.agent.list.manufacturing.1.content.1.list.3':
    ' <b>Fehleinschätzungen bei der Erstbewertung:</b> Nur auf mündliche Beschreibungen gestützt, kann die Schwere oder Art des Fehlers falsch eingeschätzt werden, was zu falscher Techniker- oder Ersatzteilentsendung führt.',
  'aiAgentLibrary.agent.list.manufacturing.1.content.1.list.4':
    ' <b>Keine sofortige Rückmeldung:</b> Nach der Störungsmeldung erhalten Kunden keine zeitnahe Rückmeldung und wissen nicht, ob und wann das Problem bearbeitet wird.',
  'aiAgentLibrary.agent.list.manufacturing.1.content.2.list.1':
    'Bietet einen interaktiven, intelligenten Workflow für Störungsmeldungen. Durch dialogbasierte Führung hilft er Kunden, alle notwendigen Informationen strukturiert bereitzustellen, einschließlich Gerätemodell, Fehlersymptome und Betriebsumgebung. Der Agent versteht natürlichsprachliche Beschreibungen, extrahiert automatisch Schlüsselinformationen, führt eine vorläufige Klassifizierung und Prioritätsbewertung basierend auf dem Fehlertyp durch und erstellt einen vollständigen, fokussierten und hochwertigen Serviceauftrag.',
  'aiAgentLibrary.agent.list.manufacturing.1.content.3.list.1':
    'Ich möchte eine Störung melden.',

  'aiAgentLibrary.agent.list.manufacturing.2.title':
    'KI-Wissensassistent für die Fertigungsindustrie',
  'aiAgentLibrary.agent.list.manufacturing.2.content.1.list.1':
    ' <b>Wissenssilos:</b> Wichtige Informationen sind über PDF-Handbücher, Word-Dokumente, CAD-Zeichnungen, das Wissen erfahrener Mitarbeiter und veraltete Intranetsysteme verstreut und daher schwer auffindbar und nutzbar.',
  'aiAgentLibrary.agent.list.manufacturing.2.content.1.list.2':
    ' <b>Niedrige Sucheffizienz:</b> Herkömmliche Stichwortsuchen erfassen den Kontext technischer Begriffe nicht; eine Suche nach „Lagerwechsel“ liefert oft irrelevante Bestellungen statt Wartungsanleitungen.',
  'aiAgentLibrary.agent.list.manufacturing.2.content.1.list.3':
    ' <b>Langsame Einarbeitung:</b> Neue Mitarbeiter (insbesondere Techniker und Ingenieure) benötigen Monate, um sich mit Anlagen und Abläufen vertraut zu machen, was hohe Schulungskosten und eine starke Abhängigkeit von erfahrenen Kollegen bedeutet.',
  'aiAgentLibrary.agent.list.manufacturing.2.content.1.list.4':
    ' <b>Expertenabhängigkeit:</b> Das Wissen weniger Schlüsselpersonen ist nicht systematisch dokumentiert; bei deren Weggang geht wertvolle Erfahrung verloren und es entstehen Wissensengpässe im Team.',
  'aiAgentLibrary.agent.list.manufacturing.2.content.2.list.1':
    'Schafft ein einheitliches, dialogbasiertes Wissensportal. Der Agent versteht nicht nur natürliche Sprache, sondern liest und integriert alle internen Dokumente (Handbücher, Zeichnungen, Berichte, Best Practices) und liefert präzise, nachvollziehbare Antworten. Er agiert wie ein unermüdlicher, allwissender Experte für sämtliche Anlagen und Prozesse.',

  'aiAgentLibrary.agent.list.manufacturing.2.content.3.list.1':
    'Worin bestehen die Unterschiede zwischen den Modellen G3VM-26M10, G3VM-26M11 und G3VM-66M? Wie wähle ich das passende Modell für meine Anwendung aus?',
  'aiAgentLibrary.agent.list.manufacturing.2.content.3.list.2':
    'Was ist der Unterschied zwischen VSON(R) und VSON?',
  'aiAgentLibrary.agent.list.manufacturing.2.content.3.list.3':
    'Ich möchte SOP8-oberflächenmontierbare Relais für eine automatisierte Fertigungslinie bestellen. In welcher Verpackungsart werden diese geliefert und wie hoch ist die Mindestbestellmenge (MOQ)?',
  // 制造 end

  // 立即体验
  'aiAgentLibrary.agent.list.button': 'Jetzt ausprobieren',
};
