export default {
  'homePage.homePage': 'Homepage',
  'homePage.cloudContactCenter': 'Cloud contact center',
  'homePage.contactUs': 'Contact us',
  'homePage.freeTrial': 'Free trial',
  'homePage.topContent':
    'Global, integrated, intelligent customer contact center',
  'homePage.tipsBtn': 'Experience now!',
  'homePage.centerContentTopFont': 'Choose your customer service solution',
  'homePage.centerContentBottomFont': 'Solution function comparison',
  'homePage.listData.name1': 'Basic version',
  'homePage.listData.name2': 'Advanced version',
  'homePage.listData.name3': 'Professional version',
  'homePage.listData.name4': 'Enterprise version',
  'homePage.listData.name5': 'Private deployment',
  'homePage.listData.tips1': 'Meets the needs of startups',
  'homePage.listData.tips2': 'Help medium-sized enterprises grow rapidly',
  'homePage.listData.tips3':
    'Reduce costs and increase efficiency for large enterprises',
  'homePage.listData.tips4': 'Provide enterprise-level support',
  'homePage.listData.top': 'Most popular',
  'homePage.listData.tipsBtn1': '(Within 10 agents)',
  'homePage.listData.tipsBtn2': '(3 to 20 agents)',
  'homePage.listData.tipsBtn3': '(Starting from 6 agents)',
  'homePage.listData.tipsBtn4': '(Starting from 6 agents)',
  'homePage.listData.content1.1': '100 free AIGC per month',
  'homePage.listData.content2.1': '300 Free AIGC per month',
  'homePage.listData.content3.1': '1,500 Free AIGC per month',
  'homePage.listData.content4.1': 'Unlimited AIGC',
  'homePage.listData.content1.2': 'Does not include live chat',
  'homePage.listData.content2.2': '1,000 free online chats per month',
  'homePage.listData.content3.2': '4,000 Free online chats per month',
  'homePage.listData.content4.2': '6,000 free online chats per month',
  'homePage.listData.content1.3': '1 free US number per month',
  'homePage.listData.content2.3': '1 free US number per month',
  'homePage.listData.content3.3': '1 free US number per month',
  'homePage.listData.content4.3': '2 free US numbers per month',
  'homePage.listData.content1.4': '200 minutes of inbound calls',
  'homePage.listData.content2.4': '500 minutes of inbound calls',
  'homePage.listData.content3.4': '2,500 minutes of inbound calls',
  'homePage.listData.content4.4': '5,000 minutes of inbound calls',
  'homePage.listData.result1': '6 basic benefits',
  'homePage.listData.result2': 'All features of the basic version plus...',
  'homePage.listData.result3': 'All features of the advanced version plus...',
  'homePage.listData.result4':
    'All features of the professional version plus...',
  'homePage.listData.resultList.1.1': 'Basic functions of the ticketing system',
  'homePage.listData.resultList.1.2': 'Basic functions of the customer center',
  'homePage.listData.resultList.1.3': 'Centralized work panel',
  'homePage.listData.resultList.1.4': 'Phone channel access',
  'homePage.listData.resultList.1.5': 'AIGC sentiment analysis',
  'homePage.listData.resultList.1.6': 'AIGC one-click summary',
  'homePage.listData.resultList.2.1':
    'Advanced functions of the ticketing system',
  'homePage.listData.resultList.2.2':
    'Advanced functions of the ticketing system',
  'homePage.listData.resultList.2.3': 'AIGC translation',
  'homePage.listData.resultList.2.4': 'AIGC grammar correction',
  'homePage.listData.resultList.2.5': 'AIGC content generation',
  'homePage.listData.resultList.2.6': 'AIGC pay-as-you-go',
  'homePage.listData.resultList.2.7': 'Email channel access',
  'homePage.listData.resultList.2.8': 'Web chat channel access',
  'homePage.listData.resultList.2.9': 'Mobile app chat channel access',

  'homePage.listData.resultList.3.1':
    'Full functionality of the ticketing system',
  'homePage.listData.resultList.3.2':
    'Full functionality of the customer center',
  'homePage.listData.resultList.3.3': 'Social media channel access',
  'homePage.listData.resultList.3.3.1': 'Online voice/online video',
  'homePage.listData.resultList.3.3.2': 'FAQ robot',

  'homePage.listData.resultList.3.4': 'AIGC知识库专业版+AIGC智能客服（服务端）',
  'homePage.listData.resultList.3.5':
    'Customer profile extension field settings',
  'homePage.listData.resultList.3.6': 'More ticket customization settings',
  'homePage.listData.resultList.3.7': 'Standard IVR',
  'homePage.listData.resultList.3.8': 'Customer service management',
  'homePage.listData.resultList.2.10': 'Report center',
  'homePage.listData.resultList.4.1': 'AIGC knowledge base enterprise version',
  'homePage.listData.resultList.4.2': 'Sensitive information data masking',
  'homePage.listData.resultList.4.3': 'Historical data archiving and recovery',
  'homePage.listData.resultList.4.4': 'Data storage for 2 years',

  'homePage.data.key.1.no': 'Agent dashboard',
  'homePage.data.key.2.no': 'Agent centralized work panel',
  'homePage.data.key.2.0.1.no': 'Agent aigc assistant',
  'homePage.data.key.3.no': 'Phone channel',
  'homePage.data.key.4.no': 'Email channel',
  'homePage.data.key.5.no': 'WhatsApp channel',
  'homePage.data.key.6.no': '智能客服+在线聊天',
  'homePage.data.key.7.no': 'Automatic ticket/manual ticket',
  'homePage.data.key.8.no': 'Robot ticket',
  'homePage.data.key.9.no': 'Customer profile',
  'homePage.data.key.10.no': 'Knowledge base',
  'homePage.data.key.11.no': 'Dashboard',
  'homePage.data.key.12.no': 'Customer profile customization',
  'homePage.data.key.13.no': 'Ticket settings',
  'homePage.data.key.14.no': 'Platform user management',
  'homePage.data.key.15.no': 'Agent management',
  'homePage.data.key.16.no': 'Channel configuration',
  'homePage.data.key.17.no': 'Message reminder',
  'homePage.data.key.18.no': 'Preference settings',
  'homePage.data.key.19.no': 'Personal information modification',
  'homePage.data.key.20.no': 'Data processing',

  'homePage.data.key.1.no5': 'Contact sales',

  'homePage.data.key.2.children.21.no':
    'Omnichannel agent work panel: Phone, email, SMS, web live chat, app live chat, online voice, online video, WhatsApp, etc. are all concentrated in one panel',
  'homePage.data.key.2.children.22.no': 'Omnichannel new message reminder',
  'homePage.data.key.2.children.23.no':
    'Automatically pop up tickets upon incoming calls and messages',
  'homePage.data.key.2.children.24.no': 'Manually create tickets',
  'homePage.data.key.2.children.25.no': 'Search all my tickets',
  'homePage.data.key.2.children.26.no': 'Freely switch agent work status',
  'homePage.data.key.2.children.27.no': 'Support SSO',
  'homePage.data.key.2.children.28.no': 'Create task',
  'homePage.data.key.2.children.29.no':
    'Automatically pop up customer information and automatically record customer information',
  'homePage.data.key.2.children.30.no':
    "View customer's historical consultation tickets",
  'homePage.data.key.2.children.31.no': 'Customer profile modification',
  'homePage.data.key.2.children.32.no': 'Routing, queue configuration',
  'homePage.data.key.2.children.33.no': 'Intelligent queuing mechanism',
  'homePage.data.key.2.children.34.no': 'Working hours definition',
  'homePage.data.key.2.children.35.no':
    'Limit the number of simultaneous online chats for agents',
  'homePage.data.key.2.children.36.no': 'AIGC one-click summary',

  'homePage.data.key.2.0.1.children.21.no': 'Coco online: AIGC translation',
  'homePage.data.key.2.0.1.children.22.no':
    'Coco online: AIGC grammar correction',
  'homePage.data.key.2.0.1.children.23.no':
    'Coco online: AIGC email content formalization',
  'homePage.data.key.2.0.1.children.24.no':
    'Coco online: AIGC email content generation',
  'homePage.data.key.2.0.1.children.25.no': 'Coco online: Casual chat',
  'homePage.data.key.2.0.1.children.26.no': 'AIGC sentiment analysis',
  'homePage.data.key.2.0.1.children.27.no': 'AIGC one-click summary',
  'homePage.data.key.2.0.1.children.28.no': 'Smart matching',
  'homePage.data.key.2.0.1.children.29.no': 'AIGC intelligent knowledge base',
  'homePage.data.key.2.0.1.children.30.no': 'Agent quick reply',

  'homePage.data.key.tips1': 'Pay as you go',

  'homePage.data.key.3.children.31.no':
    'Inbound and outbound calls, support toll-free, DID, UIFN',
  'homePage.data.key.3.children.32.no': 'Call forwarding, number portability',
  'homePage.data.key.3.children.33.no': 'Custom IVR',
  'homePage.data.key.3.children.34.no': 'Satisfaction survey',
  'homePage.data.key.3.children.35.no': 'Callback',
  'homePage.data.key.3.children.36.no': 'Caller ID',
  'homePage.data.key.3.children.37.no': 'Automatically record call recordings',
  'homePage.data.key.3.children.37001.no':
    'Automatic transcription of call recordings',
  'homePage.data.key.3.children.38.no': 'Agent transfer',
  'homePage.data.key.3.children.39.no': 'Automatically record phone tickets',

  'homePage.data.key.tips2': 'Standard configuration',
  'homePage.data.key.tips3': 'Customizable',
  'homePage.data.key.tips4': 'Unlimited',
  'homePage.data.key.tips6': 'File quantity limit',

  'homePage.data.key.4.children.41.no': 'Email channel access',
  'homePage.data.key.4.children.42.no': 'Email attachment function',
  'homePage.data.key.4.children.41.no.1': 'Receiving mail, sending mail',
  'homePage.data.key.4.children.42.no.1': 'Minute-level mail response',
  'homePage.data.key.4.children.43.no': 'Automatic merging of email content',
  'homePage.data.key.4.children.44.no': 'Automatically record email tickets',

  'homePage.data.key.5.children.51.no': 'WhatsApp channel access',
  'homePage.data.key.5.children.52.no': 'Support question answering robot',
  'homePage.data.key.5.children.53.no': 'Support AIGC intelligent chatbot',
  'homePage.data.key.5.children.54.no':
    'Automatically recognize customer input language',
  'homePage.data.key.5.children.55.no':
    'WhatsApp chat connects to human agents',
  'homePage.data.key.5.children.56.no':
    'Display the number of people waiting when customers are transferred to a human agent',
  'homePage.data.key.5.children.57.no': 'Support agent chat transfer',
  'homePage.data.key.5.children.57001.no': 'WhatsApp channel configuration',
  'homePage.data.key.5.children.58.no':
    'Unified queuing mechanism for phone and chat',
  'homePage.data.key.5.children.59.no':
    'Support automatic merging of multiple chat tickets',
  'homePage.data.key.5.children.591111.no':
    'Automatically record WhatsApp tickets',

  'homePage.data.key.6.children.61.no': 'Web chat channel access',
  'homePage.data.key.6.children.62.no':
    'Mobile chat channel access (H5 embedded in mobile app)',
  'homePage.data.key.6.children.63.no':
    'Support user basic information collection',
  'homePage.data.key.6.children.64.no':
    'Configurable UI (company logo, color scheme, etc.)',
  'homePage.data.key.6.children.65.no':
    'Support browser automatic invitation dialogue',
  'homePage.data.key.6.children.66.no': 'Configurable welcome message',
  'homePage.data.key.6.children.67.no': 'Display email channel entry',
  'homePage.data.key.6.children.68.no': 'Display WhatsApp channel entry',
  'homePage.data.key.6.children.69.no':
    'Display historical conversations on the client',
  'homePage.data.key.6.children.691.no': 'Support question answering robot',
  'homePage.data.key.6.children.692.no': 'Support AIGC intelligent chatbot',
  'homePage.data.key.6.children.693.no':
    'Automatically recognize customer input language',
  'homePage.data.key.6.children.694.no':
    'All common languages are supported, including but not limited to: English, Simplified Chinese, Traditional Chinese, Japanese, Korean, German, Spanish, French, Arabic, Russian, Portuguese, etc.',
  'homePage.data.key.6.children.695.no': 'Multilingual automatic adaptation',
  'homePage.data.key.6.children.696.no':
    'Support attachment upload in chat (support pictures, videos and other attachments), support emoji input, support attachments larger than 200MB',
  'homePage.data.key.6.children.697.no': 'Custom IVR',
  'homePage.data.key.6.children.698.no':
    'Chat can be transferred to a human agent at any time',
  'homePage.data.key.6.children.699.no':
    'Display the number of people waiting when customers are transferred to a human agent',
  'homePage.data.key.6.children.6991.no': 'Support agent chat transfer',
  'homePage.data.key.6.children.6992.no': 'Support online voice calls',
  'homePage.data.key.6.children.6993.no': 'Support online video chat',
  'homePage.data.key.6.children.6994.no':
    'Automatically invite evaluation after chat ends',
  'homePage.data.key.6.children.6995.no':
    'Can be directly embedded into your own system or Shopify with low code',
  'homePage.data.key.6.children.6996.no':
    'Two window sizes can be switched freely',
  'homePage.data.key.6.children.6997.no':
    'Unified queuing mechanism for phone and chat',
  'homePage.data.key.6.children.6998.no':
    'Support automatic merging of multiple chat tickets',
  'homePage.data.key.6.children.6999.no':
    'Automatically record online chat tickets',

  'homePage.data.key.7.children.71.no':
    'Centralized management of omnichannel tickets',
  'homePage.data.key.7.children.72.no':
    'Automatically record tickets upon incoming calls and messages',
  'homePage.data.key.7.children.721.no': 'Manually create tickets',
  'homePage.data.key.7.children.73.no': 'My tickets',
  'homePage.data.key.7.children.74.no': 'My pending tickets',
  'homePage.data.key.7.children.75.no': 'My resolved tickets',
  'homePage.data.key.7.children.751.no': 'Expired tickets',
  'homePage.data.key.7.children.76.no': 'Tickets I follow',
  'homePage.data.key.7.children.77.no': 'Unassigned tickets',
  'homePage.data.key.7.children.78.no': 'Tickets for my customer service group',
  'homePage.data.key.7.children.79.no': 'All tickets',
  'homePage.data.key.7.children.80.no': 'Custom query conditions',
  'homePage.data.key.7.children.81.no':
    'Customer service supervisor urges orders',
  'homePage.data.key.7.children.83.no':
    'Customer service supervisor assigns tickets, transfers tickets, customer service claims tickets',
  'homePage.data.key.7.children.85.no': 'Related tickets',
  'homePage.data.key.7.children.86.no': 'Automatic ticket merging',
  'homePage.data.key.7.children.87.no': 'Contact customer directly',
  'homePage.data.key.7.children.89.no': 'Export tickets to Excel',
  'homePage.data.key.7.children.94.no': 'AIGC sentiment analysis',
  'homePage.data.key.7.children.95.no': 'AIGC one-click summary',

  'homePage.data.key.8.children.81.no': 'Automatically record robot tickets',
  'homePage.data.key.8.children.83.no':
    'Display the content of the robot ticket when transferring to a human agent',

  'homePage.data.key.9.children.91.no':
    'Centralized control of omnichannel customer profiles',
  'homePage.data.key.9.children.92.no':
    'Automatic creation of new customer information',
  'homePage.data.key.9.children.93.no': 'Manually add customer profile',
  'homePage.data.key.9.children.94.no': 'Batch import of customer profiles',
  'homePage.data.key.9.children.95.no': 'Customer data export',
  'homePage.data.key.9.children.97.no': 'Contact customer directly',
  'homePage.data.key.9.children.98.no': 'Customer inquiry history',
  'homePage.data.key.9.children.99.no': 'Tag customers and search by tag',
  'homePage.data.key.9.children.101.no': 'Customer group management',
  'homePage.data.key.9.children.102.no':
    'Check All historical workload sheet by client',

  'homePage.data.key.10.children.101.no':
    'Enterprise AIGC intelligent knowledge base (internal)',
  'homePage.data.key.10.children.102.no':
    'AIGC intelligent online customer service knowledge base (external)',
  'homePage.data.key.10.children.103.no':
    'Document knowledge base supports batch upload of multiple formats of text',
  'homePage.data.key.10.children.104.no':
    'Document knowledge base supports docking with AIGC',
  'homePage.data.key.10.children.105.no': 'Q&A knowledge base',
  'homePage.data.key.10.children.105.no111111': 'AIGC knowledge base',

  'homePage.data.key.10.children.106.no':
    'The Q&A knowledge base supports text, pictures, and videos',
  'homePage.data.key.10.children.107.no':
    'Q&A knowledge base AIGC generates a variety of questions',
  'homePage.data.key.10.children.108.no': 'Synonym definition',

  'homePage.data.key.11.children.111.no': 'Agent dashboard',
  'homePage.data.key.11.children.112.no': 'Agent supervisor dashboard',
  'homePage.data.key.11.children.113.no': 'System administrator dashboard',

  'homePage.data.key.12.children.121.no':
    'Customer profile extension field settings',

  'homePage.data.key.13.children.131.no': 'Ticket extension field settings',
  'homePage.data.key.13.children.132.no': 'Ticket SLA settings',
  'homePage.data.key.13.children.133.no': 'Ticket type definition',
  'homePage.data.key.13.children.134.no': 'Merge ticket settings',

  'homePage.data.key.14.children.141.no':
    'Platform user addition and permission assignment',
  'homePage.data.key.14.children.142.no': 'Platform user invitation',
  'homePage.data.key.14.children.143.no': 'Platform user disable',
  'homePage.data.key.14.children.144.no': 'Platform role selection',
  'homePage.data.key.14.children.145.no': 'Support SSO',

  'homePage.data.key.15.children.151.no': 'Agent management',

  'homePage.data.key.16.children.161.no': 'Support phone channel',
  'homePage.data.key.16.children.162.no': 'Support email channel',
  'homePage.data.key.16.children.163.no': 'Support web channel',
  'homePage.data.key.16.children.164.no': 'Support app channel',
  'homePage.data.key.16.children.165.no': 'Support web video channel',
  'homePage.data.key.16.children.166.no': 'Support app video channel',
  'homePage.data.key.16.children.167.no': 'Support WhatsApp channel',

  'homePage.data.key.17.children.171.no':
    'Transfer reminder, assignment reminder',
  'homePage.data.key.17.children.172.no': 'Urging reminder',
  'homePage.data.key.17.children.173.no': 'Ticket upgrade reminder',
  'homePage.data.key.17.children.174.no':
    'Reminder for tickets exceeding SLA limits',
  'homePage.data.key.17.children.175.no': 'Other message reminders',

  'homePage.data.key.18.children.181.no': 'Time zone settings',
  'homePage.data.key.18.children.182.no':
    'Language settings (multilingual support)',

  'homePage.data.key.19.children.191.no':
    'Avatar, change password, personal information',

  'homePage.data.key.20.children.201.no': 'Sensitive information data masking',
  'homePage.data.key.20.children.202.no':
    'Historical data archiving and recovery',
  'homePage.data.key.20.children.203.no': 'Data storage time',

  'homePage.button.name': 'Free experience',
  'homePage.data.key.20.children.tips.180': '180 days',
  'homePage.data.key.20.children.tips.365': '365 days',
  'homePage.data.key.20.children.tips.730': '730 days',

  'homePage.tipsBtn1.mobile': 'Learn more',
  'homePage.tipsBtn2.mobile': 'Experience now',
};
