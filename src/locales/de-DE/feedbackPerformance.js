export default {
  'feedbackPerformance.title':
    'Analyse der leistung des intelligenten kundenservice',
  'feedbackPerformance.card.3': 'Top 5 der fragen mit den meisten likes',
  'feedbackPerformance.card.4': 'Top 5 der fragen mit den meisten dislikes',
  'feedbackPerformance.card.1.tabs.1': 'Like-rate',
  'feedbackPerformance.card.1.tabs.2': 'Dislike-rate',
  'feedbackPerformance.card.1.tabs.3': 'Interaktionsrate',
  'feedbackPerformance.card.2.text':
    '在这段时间内，“{question}”问题的点踩数量最多，高达{number}个，请及时关注是否需要更新维护知识；',
  'feedbackPerformance.card.2.text.up': 'Zunehmen',
  'feedbackPerformance.card.2.text.down': 'Abnehmen',
  'feedbackPerformance.card.5.text.up': 'Like',
  'feedbackPerformance.card.5.text.down': 'Dislike',
  'feedbackPerformance.card.5.select': 'Kanal auswählen:',
  'feedbackPerformance.card.5.select.placeholder':
    'Bitte wählen sie den kanal aus (die summe mehrerer kanäle wird angezeigt)',
  'feedbackPerformance.card.5':
    'Trenddiagramm der like- und dislike-interaktionsdaten',
  'feedbackPerformance.card.6':
    'Anzahl der likes- und dislikes-interaktionen pro kanal',

  'feedbackPerformance.card.1.tips.1':
    '统计指定时间段内智能客服回答的所有问题中被点赞的问题比例，点赞率=sum(点赞数量)/sum(问题数量)',
  'feedbackPerformance.card.1.tips.2':
    '统计指定时间段内智能客服回答的所有问题中被点踩的问题比例，点踩率=sum(点踩数量)/sum(问题数量)',
  'feedbackPerformance.card.1.tips.3':
    '统计指定时间段内智能客服回答的所有问题数量与被点赞点踩的问题数量的比例，交互率=sum(点赞数量+点踩数量)/sum(问题数量)',
  'feedbackPerformance.card.2.tips':
    'Basierend auf den fragen, die vom intelligenten kundenservice beantwortet wurden, werden die wichtigsten daten wie die anzahl der fragen mit den meisten dislikes und deren anzahl in einem bestimmten zeitraum ermittelt.',
  'feedbackPerformance.card.3.tips':
    'Die top 5 fragen mit den meisten likes, die vom intelligenten kundenservice in einem bestimmten zeitraum beantwortet wurden.',
  'feedbackPerformance.card.4.tips':
    'Die top 5 fragen mit den meisten dislikes, die vom intelligenten kundenservice in einem bestimmten zeitraum beantwortet wurden.',
  'feedbackPerformance.card.5.tips':
    'Zeigt den trend der anzahl der likes und dislikes zu verschiedenen zeitpunkten an. es können mehrere kanäle angegeben werden. standardmäßig werden alle kanäle gezählt.',
  'feedbackPerformance.card.6.tips':
    'Zeigt die anzahl der fragen, die von verschiedenen kanälen in verschiedenen zeiträumen mit "like" und "dislike" bewertet wurden.',
};
