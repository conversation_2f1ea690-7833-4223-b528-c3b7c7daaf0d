export default {
  'awsAccountSetting.title': 'AWS-Konto Verknüpfen',
  'awsAccountSetting.add.btn': 'AWS-Konto Hinzufügen',
  'awsAccountSetting.bind.status': 'Ihr Konto Wurde Erfolgreich Verknüpft',
  'awsAccountSetting.delete.btn': 'Löschen',
  'awsAccountSetting.connect.list': 'Connect-Instanzliste',
  'awsAccountSetting.synConnect': 'Connect-Instanz Synchronisieren',
  'awsAccountSetting.editor.btn': 'Bearbeiten',
  'awsAccountSetting.awsUserName': 'Kontoname',
  'awsAccountSetting.awsUserName.placeholder':
    'Bitte Geben Sie Den Kontonamen Ein',
  'awsAccountSetting.awsUserId': 'Konto-ID',
  'awsAccountSetting.awsUserId.placeholder': 'Bitte Geben Sie Die Konto-ID Ein',
  'awsAccountSetting.accessKey': 'Access Key ID',
  'awsAccountSetting.accessKey.placeholder':
    'Bitte Geben Sie Den Access Key Ein',
  'awsAccountSetting.secretAccessKey': 'Secret Access Key',
  'awsAccountSetting.secretAccessKey.placeholder':
    'Bitte Geben Sie Den Secret Access Key Ein',
  'awsAccountSetting.region': 'Region',
  'awsAccountSetting.region.placeholder': 'Bitte Wählen Sie Eine Region Aus',
  'awsAccountSetting.cancel.btn': 'Abbrechen',
  'awsAccountSetting.save.btn': 'Speichern',
  'awsAccountSetting.region.tips':
    'Bitte wählen sie mindestens eine region aus',
  'awsAccountSetting.regions.placeholder':
    'Bitte wählen sie mindestens eine region aus',
  'awsAccountSetting.secretAccessKey.tips':
    'Bitte Geben Sie Den Korrekten Secret Access Key Ein',
  'awsAccountSetting.accessKey.tips':
    'Bitte Geben Sie Die Korrekte Access Key ID Ein',
  'awsAccountSetting.awsUserId.tips':
    'Bitte Geben Sie Die Korrekte Konto-ID Ein',
  'awsAccountSetting.awsUserName.tips':
    'Bitte Geben Sie Den Korrekten Kontonamen Ein',
  'awsAccountSetting.iam.tips':
    'Generieren sie den zugriffsschlüssel in IAM, geben sie ihn unten ein und klicken sie auf',
  'awsAccountSetting.iam1.tips': 'Wie Erstelle Ich Einen Zugriffsschlüssel?',
  'awsAccountSetting.pTitle.tips':
    'Hinweis: Wird Nur In Diesem System Angezeigt',
  'awsAccountSetting.pTable.tips':
    'Hinweis: Dieser Vorgang Wirkt Sich Nur Auf Die Datenanzeige in Diesem System Aus, Wirkt Sich Nicht Auf Instanzen in Der AWS-Konsole Aus',
  'awsAccountSetting.pTable.return': 'Zurück',
  'awsAccountSetting.disable.text':
    'Sind Sie Sicher, Dass Sie Deaktivieren Möchten?',
  'awsAccountSetting.disable.text1': 'Instanz?',
  'awsAccountSetting.setting.alias': 'Alias Festlegen',
  'awsAccountSetting.label.alias': 'Alias:',
  'awsAccountSetting.alias.placeholder': 'Bitte Geben Sie Den Alias Ein',
  // 'studentManagement.altogether': '共 {total} 条',

  'awsAccountSetting.connectAlias.table': 'Instanz-Alias',
  'awsAccountSetting.connectUrl.table': 'Zugriffsadresse',
  'awsAccountSetting.bound.table': 'Kanal',
  'awsAccountSetting.createTime.table': 'Erstellungsdatum',
  'awsAccountSetting.connectStatus.table': 'Status',
  'awsAccountSetting.operation.table': 'Betrieb',
  'awsAccountSetting.disable.table': 'Deaktivieren',
  'awsAccountSetting.alias.table': 'Alias Festlegen',

  'awsAccountSetting.bound.table.type1': 'Ausgehend',
  'awsAccountSetting.bound.table.type2': 'Eingehend',
  'awsAccountSetting.bound.table.type3': 'Ausgehend/Eingehend',

  'awsAccountSetting.connectStatus.table.type1': 'Aktiv',
  'awsAccountSetting.connectStatus.table.type2': 'Deaktiviert',
};
