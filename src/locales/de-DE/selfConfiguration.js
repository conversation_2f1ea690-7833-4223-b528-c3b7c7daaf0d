export default {
  'selfConfiguration.title': 'Self-assessment konfiguration',
  'selfConfiguration.label.1':
    'Aktivieren der automatischen stichprobenbewertung durch das system:',
  'selfConfiguration.label.1.tips':
    'Wenn die self-assessment aktiviert ist, führt das system stichprobenprüfungen gemäß ihren nachfolgenden einstellungen durch und erstellt einen automatischen genauigkeitsbewertungsbericht.',
  'selfConfiguration.label.2':
    'Anzahl der täglich zu prüfenden fragen festlegen',
  'selfConfiguration.label.2.value': '每',
  'selfConfiguration.label.2.value.1': '个问题进行1次自动评估',
  'selfConfiguration.label.2.value.2': 'Und',
  'selfConfiguration.label.2.value.3': '每天最多抽取',
  'selfConfiguration.label.2.value.4': '个问题进行自动评估',
  'selfConfiguration.label.3': 'Self-assessment frequenz',
  'selfConfiguration.label.4': 'Tag',
  'selfConfiguration.label.tips': 'Bitte wählen sie die uhrzeit',

  'selfReport.title': 'Self-assessment bericht',
  'selfReport.label.card.1': 'Durchschnittliche genauigkeitsbewertung',
  'selfReport.label.card.2':
    'Durchschnittliche genauigkeitsbewertung für jede dimension',
  'selfReport.label.card.3':
    'Durchschnittliche genauigkeitsbewertung für jede dimension',
  'selfReport.label.card.4':
    'Trenddiagramm der self-assessment genauigkeitsbewertung',
  'selfReport.label.card.5':
    'Trenddiagramm der self-assessment genauigkeitsbewertung für jede dimension',
  'selfReport.fen': 'Punkte',
  'selfReport.label.1': 'Faktizitätsnähe',
  'selfReport.label.2': 'Antwortrelevanz',
  'selfReport.label.3': 'Kontextgenauigkeit',
  'selfReport.label.4': 'Semantische ähnlichkeit der antwort',
  'selfReport.label.5': 'Korrektheit der antwort',
  'selfReport.select.knowledge': 'Wissensdatenbank auswählen:',

  'selfReport.popover.1': `这衡量生成答案与给定上下文的事实一致性。它是从答案和检索到的上下文中计算出来的。答案的评分范围在(0,100)之间，分数越高越好。`,
  'selfReport.popover.1.1':
    'Eine antwort wird als getreu betrachtet, wenn alle aussagen in der generierten antwort aus dem gegebenen kontext abgeleitet werden können.',
  'selfReport.popover.2':
    'Die metrik "antwortrelevanz" konzentriert sich auf die bewertung, wie relevant eine generierte antwort für eine bestimmte aufforderung ist. Antworten, die unvollständig sind oder redundante informationen enthalten, erhalten eine niedrigere bewertung. Höhere bewertungen weisen auf eine bessere relevanz hin. Diese metrik wird basierend auf frage, kontext und antwort berechnet.',
  'selfReport.popover.3':
    'Die kontextgenauigkeit ist eine metrik, mit der bewertet wird, ob alle wirklich relevanten elemente im kontext höher eingestuft werden. Im idealfall müssen alle relevanten blöcke oben erscheinen. Diese metrik wird basierend auf frage, wahrer antwort und kontext berechnet und liegt im bereich von 0 bis 100, wobei höhere werte eine höhere genauigkeit anzeigen.',
  'selfReport.popover.4':
    'Das konzept der semantischen ähnlichkeit von antworten beinhaltet die bewertung der semantischen ähnlichkeit zwischen der generierten antwort und der wahren antwort. Diese bewertung basiert auf der wahren antwort und der generierten antwort und liegt im bereich von 0 bis 100. Höhere werte zeigen eine bessere übereinstimmung der generierten antwort mit der wahren antwort an.',
  'selfReport.popover.5': `答案正确性的评估涉及将生成的答案与真实答案进行比较，以衡量其准确性。此评估依赖于真实答案和生成的答案，评分范围在0到100之间。分数越高，表示生成的答案与真实答案的对齐度越高，从而表明正确性更好。`,
  'selfReport.popover.5.5': `答案正确性包含两个关键方面：生成答案与真实答案之间的语义相似性以及事实相似性。这些方面通过加权方案结合起来，形成答案正确性评分。`,
};
