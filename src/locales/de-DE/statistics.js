export default {
  'statistics.work.report.title': 'Agent Workload Report',
  'statistics.work.report.label.1': 'Agentengruppenname:',
  'statistics.work.report.label.1.1': 'Agentengruppenname',

  'statistics.work.report.label.2': 'Tickettyp:',
  'statistics.work.report.label.3': 'Datum auswählen:',
  'statistics.work.report.label.4': 'Kanäle Filtern',
  'statistics.work.report.label.3.placeholder': 'Bitte Datum Auswählen',
  'statistics.work.report.label.1.placeholder':
    'Bitte Agentengruppennamen auswählen',
  'statistics.work.report.label.2.placeholder': 'Bitte Tickettyp auswählen',
  'statistics.work.report.series.name.delay':
    'Anzahl Überfälliger Ungelöster Tickets',
  'statistics.work.report.series.name': 'Anzahl Ungelöster Tickets',

  // 数据明细
  'statistics.data.details.title': 'Kontaktdetails',
  'statistics.data.details.select.date': 'Datum auswählen:',
  'statistics.data.details.search.tips':
    'Bitte Kontakt-ID, betreuenden Agenten, Agentengruppe oder Warteschlange zur Suche eingeben',
  'statistics.data.details.table.contact.id': 'Kontakt ID',
  'statistics.data.details.table.incoming.call.time': 'Startzeit',
  'statistics.data.details.table.incoming.call.time.start': 'Agent Antwortzeit',

  'statistics.data.details.table.end.time': 'Anrufende Zeit',
  'statistics.data.details.table.acw.time': 'ACW Endzeit',
  'statistics.data.details.table.total.call.duration': 'Gesamtgesprächsdauer',
  'statistics.data.details.table.interaction.time': 'Interaktionszeit',
  'statistics.data.details.table.queue.waiting.time':
    'Wartezeit in der Warteschlange',
  'statistics.data.details.table.incoming.call.channel': 'Kanal',
  'statistics.data.details.table.reception.seat': 'Betreuender Agent',
  'statistics.data.details.table.seating.group': 'Agentengruppe',
  'statistics.data.details.table.queue': 'Warteschlange',
  'statistics.data.details.table.queue.in': 'Inbound/Outbound',

  'statistics.data.details.table.phone.customer': 'Kundentelefon',
  'statistics.data.details.table.acw.duration': 'ACW Dauer',
  'statistics.data.details.table.work.order.number': 'Zugehörige Ticketnummer',
  'statistics.data.details.table.on.hold.time': 'Agent On-Hold Zeit',
  'statistics.data.details.table.on.hold.num': 'Agent On-Hold Anzahl',
  'statistics.data.details.table.is.switch': 'Ob Weitergeleitet',
  'statistics.data.details.table.hanging.type': 'Art des Auflegens',

  'statistics.data.details.table.contact.id.id': 'Ursprüngliche Kontakt ID',
  'statistics.data.details.table.contact.id.previous': 'Vorherige Kontakt ID',
  'statistics.data.details.table.contact.id.next': 'Nächste Kontakt ID',

  'statistics.data.details.table.system.phone': 'Systemtelefon',
  'statistics.data.details.table.satisfaction.rating':
    'Zufriedenheitsbewertung',
  'statistics.data.details.table.operation': 'Betrieb',
  'statistics.data.details.channel.type': 'Kanaltyp：',

  // 座席历史工作指标
  'statistics.agent.performance.indicators.title': 'Agentenverlaufsmetriken',
  'statistics.agent.performance.indicators.tips': 'Bitte Agentennamen Eingeben',
  'statistics.agent.performance.indicators.table.agent.name': 'Agent',
  'statistics.agent.performance.indicators.table.accumulated.online.duration':
    'Online Zeit',
  'statistics.agent.performance.indicators.table.accumulated.idle.duration':
    'Agent Leerlaufzeit',
  'statistics.agent.performance.indicators.table.accumulated.reception.duration':
    'Agent Kontaktzeit',
  'statistics.agent.performance.indicators.table.task.time.utilization.rate':
    'Auslastung',
  'statistics.agent.performance.indicators.table.unresponsive.quantity':
    'Agent Nicht Geantwortet',
  'statistics.agent.performance.indicators.table.reception.contacts.quantity':
    'Bearbeitete Kontakte',
  'statistics.agent.performance.indicators.table.response.rate':
    'Agent Antwortrate',
  'statistics.agent.performance.indicators.table.working.hours.after.contact':
    'Durchschnittliche Kontakt Nachbearbeitungszeit',
  'statistics.agent.performance.indicators.table.customer.retention.time':
    'Durchschnittliche Kundenbindungszeit',

  // 历史队列指标
  'statistics.historical.queue.indicators.title':
    'Warteschlangen Verlaufsmetriken',
  'statistics.historical.queue.indicators.table.queue.name':
    'Warteschlangenname',
  'statistics.historical.queue.indicators.table.queue.name.label':
    'Name Der Warteschlange：',
  'statistics.historical.queue.indicators.search.tips':
    'Bitte Warteschlangennamen Eingeben',
  'statistics.historical.queue.indicators.table.working.hours.after.contact':
    'Durchschnittliche Kontakt Nachbearbeitungszeit',
  'statistics.historical.queue.indicators.table.agent.interaction.time':
    'Durchschnittliche Agenteninteraktionszeit',
  'statistics.historical.queue.indicators.table.customer.retention.time':
    'Durchschnittliche Kundenbindungszeit',
  'statistics.historical.queue.indicators.table.queue.abandonment.time':
    'Durchschnittliche Warteschlangenabbruchzeit',
  'statistics.historical.queue.indicators.table.queue.waiting.time':
    'Durchschnittliche Warteschlangen Antwortzeit',
  'statistics.historical.queue.indicators.table.abandon.contact.quantity':
    'Abgebrochene Kontakte',
  'statistics.historical.queue.indicators.table.queued.contacts.quantity':
    'Kontakte in der Warteschlange',
  //队列维度
  'statistics.queue.title': 'Warteschlange Echtzeitmetriken',
  'statistics.queue.title.right': 'Datenaktualisierungszeit：',
  'statistics.queue.title.1': 'Agentenstatusliste',
  'statistics.queue.title.2': 'Kontaktliste',
  'statistics.queue.title.3': 'Leistungsliste',
  'statistics.queue.title.btn': 'Batch Export',
  'statistics.queue.table.agent.1': 'Warteschlangenname',
  'statistics.queue.table.agent.2': 'Online',
  'statistics.queue.table.agent.3': 'Derzeit im Kontakt',
  'statistics.queue.table.agent.4': 'NPT',
  'statistics.queue.table.agent.5': 'ACW',
  'statistics.queue.table.agent.6': 'Fehler',
  'statistics.queue.table.agent.7': 'Verfügbar',

  'statistics.queue.table.contact.2': 'Verfügbarkeit',
  'statistics.queue.table.contact.3': 'Aktiv',
  'statistics.queue.table.modal.search': 'Bitte Metriknamen zur Suche eingeben',
  'statistics.queue.table.modal.panel.1': '性能（{num}）',
  'statistics.queue.table.modal.panel.2': '在X中放弃的联系人（{num}）',
  'statistics.queue.table.modal.panel.3': '在X中已应答的联系人（{num}）',
  'statistics.queue.table.modal.panel.4': '联系人服务等级（{num}）',

  'statistics.queue.table.modal.panel.1.check.1': 'Am längsten',
  'statistics.queue.table.modal.panel.1.check.2': 'Geplant',
  'statistics.queue.table.modal.panel.1.check.3': 'In Warteschlange',
  'statistics.queue.table.modal.panel.1.check.4': 'Bearbeitet',
  'statistics.queue.table.modal.panel.1.check.5': 'Aufgegeben',
  'statistics.queue.table.modal.panel.1.check.6':
    'AHT (Durchschnittliche Bearbeitungszeit)',
  'statistics.queue.table.modal.panel.1.check.7': 'Outbound Bearbeitung',
  'statistics.queue.table.modal.panel.1.check.8': 'Bearbeitete API Kontakte',
  'statistics.queue.table.modal.panel.1.check.9':
    'Bearbeitete Rückruf Kontakte',
  'statistics.queue.table.modal.panel.1.check.10':
    'Aus Warteschlange übertragen',
  'statistics.queue.table.modal.panel.1.check.11':
    'Durchschnittliche Warteschlangen Antwortzeit',
  'statistics.queue.table.modal.panel.1.check.12':
    'Durchschnittliche Abbruchzeit',
  'statistics.queue.table.modal.panel.1.check.13':
    'Durchschnittliche Aktivzeit',
  'statistics.queue.table.modal.panel.1.check.14':
    'Durchschnittliche Interaktions- und Haltezeit',
  'statistics.queue.table.modal.panel.1.check.15':
    'Durchschnittliche Interaktionszeit',
  'statistics.queue.table.modal.panel.1.check.16':
    'Durchschnittliche Lösungszeit',
  'statistics.queue.table.modal.panel.1.check.17': 'In der Warteschlange',
  'statistics.queue.table.modal.panel.1.check.18':
    'Aus Warteschlange Übertragen',
  'statistics.queue.table.modal.panel.1.check.19': 'Längste Wartezeit',
  'statistics.queue.table.modal.panel.1.check.20': 'Agent Nicht Geantwortet',
  'statistics.queue.table.modal.panel.1.check.21': 'Agent Übertragen',
  'statistics.queue.table.modal.panel.1.check.22':
    'Durchschnittliche Pausenzeit des Agenten',
  'statistics.queue.table.modal.panel.1.check.23':
    'Durchschnittliche Zeit für API-Verbindungen',
  'statistics.queue.table.modal.panel.1.check.24': 'Agent hat nicht geantworte',

  'statistics.queue.table.modal.panel.1.check.25': '在{num}中放弃的联系人',
  'statistics.queue.table.modal.panel.1.check.26': '在{num}中已应答的联系人',
  'statistics.queue.table.modal.panel.1.check.25.search':
    'In abgebrochene Kontakte',
  'statistics.queue.table.modal.panel.1.check.26.search':
    'In beantwortete Kontakte',

  //座席维度
  'statistics.agent.title': 'Agent Echtzeitmetriken',
  'statistics.agent.title.1': 'Agentenliste',
  'statistics.agent.table.modal.panel.1': '座席员（{num}）',
  'statistics.agent.table.agent.1': 'Agentenname',
  'statistics.agent.table.agent.2': 'Agent Vorname',
  'statistics.agent.table.agent.3': 'Agent Nachname',
  'statistics.agent.table.agent.4': 'Kapazität',
  'statistics.agent.table.agent.5': 'Agent Anmeldename',
  'statistics.agent.table.agent.5.label': 'Agenten Anmeldename：',
  'statistics.agent.table.agent.5.label.placeholder':
    'Bitte geben Sie den Benutzernamen des Agenten ein',

  'statistics.agent.table.agent.6': 'Nächste Aktivität',
  'statistics.agent.table.agent.7': 'Aktivität',
  'statistics.agent.table.agent.8': 'Dauer',
  'statistics.agent.table.agent.9': 'Kanal',

  'statistics.agent.table.contact.1': 'Warteschlange',
  'statistics.agent.table.contact.2': 'Dauer',
  'statistics.agent.table.contact.3': 'Kontaktstatus',
  'statistics.agent.table.contact.4': 'Verfügbarkeit',

  'statistics.agent.table.modal.panel.1.check.1': 'Outbound Bearbeitung',
  'statistics.agent.table.modal.panel.1.check.2': 'Bearbeitete API Kontakte',
  'statistics.agent.table.modal.panel.1.check.3':
    'Bearbeitete Rückruf Kontakte',
  'statistics.agent.table.modal.panel.1.check.4': 'Durchschnittliche Haltezeit',
  'statistics.agent.table.modal.panel.1.check.5':
    'Durchschnittliche Aktive Zeit',
  'statistics.agent.table.modal.panel.1.check.6':
    'Durchschnittliche Interaktionszeit',
  'statistics.agent.table.modal.panel.1.check.7':
    'Aus Warteschlange Übertragen',
  'statistics.agent.table.modal.panel.1.check.8': 'Agent Nicht Geantwortet',
  'statistics.agent.table.modal.panel.1.check.9':
    'Durchschnittliche Pausenzeit des Agenten',
  'statistics.agent.table.modal.panel.1.check.10':
    'Durchschnittliche Zeit für API-Verbindungen',
  'statistics.agent.table.modal.panel.1.check.11':
    'Bearbeitete Rückruf Kontakte',
  'statistics.agent.table.modal.panel.1.check.12':
    'Aus Warteschlange Übertragen',
  'statistics.agent.table.modal.panel.1.check.13':
    'Durchschnittliche Zeit für eingehende Verbindungen',

  // 座席工作效率统计
  'agent.work.efficiency.statistics.title': 'Agenten-Arbeitseffizienzstatistik',
  'agent.work.efficiency.statistics.title.group':
    'Agentengruppen-Arbeitseffizienzstatistik',
  'agent.work.efficiency.statistics.second.title.1':
    'Durchschnittliche Lösungszeit',
  'agent.work.efficiency.statistics.second.title.2': 'Dateneinblicke',
  'agent.work.efficiency.statistics.data.insight.agent.group': 'Agentengruppe',
  'agent.work.efficiency.statistics.data.insight.agent': 'Agent',
  'agent.work.efficiency.statistics.data.insight.1':
    'Während dieses Zeitraums hat der Agent',
  'agent.work.efficiency.statistics.data.insight.1.group':
    'In diesem Zeitraum die Agentengruppe',
  'agent.work.efficiency.statistics.data.insight.2':
    'Die längste durchschnittliche Bearbeitungszeit für Tickets und erreichte einen Durchschnitt von',
  'agent.work.efficiency.statistics.data.insight.3':
    'Bearbeitung eines Tickets, bitte achten Sie auf die Arbeitssituation.',
  'agent.work.efficiency.statistics.data.insight.4':
    'Die kürzeste durchschnittliche Bearbeitungszeit für Tickets und erreichte einen Durchschnitt von',
  'agent.work.efficiency.statistics.data.insight.5':
    'Bearbeitung eines Tickets, bitte ermutigen Sie, weiterzumachen.',
  'agent.work.efficiency.statistics.data.insight.6':
    'Die höchste durchschnittliche Effizienzsteigerung, erhöht um',
  'agent.work.efficiency.statistics.data.insight.7':
    ', bitte ermutigen Sie, weiterzumachen.',
  'agent.work.efficiency.statistics.data.insight.8':
    'Die geringste durchschnittliche Effizienzsteigerung, Rückgang um',
  'agent.work.efficiency.statistics.data.insight.9':
    ', bitte achten Sie auf die Arbeitssituation.',
  'agent.work.efficiency.statistics.second.title.3':
    'Durchschnittliche Lösungszeit Längste Top3',
  'agent.work.efficiency.statistics.second.title.3.1':
    'Durchschnittliche Lösungszeit Kürzeste Top3',
  'agent.work.efficiency.statistics.second.title.3.2':
    'Durchschnittliche Effizienzsteigerung Top3',
  'agent.work.efficiency.statistics.second.title.3.3':
    'Durchschnittliche Effizienz Sinkt Top3',
  'agent.work.efficiency.statistics.agent.name': 'Agentenname',
  'agent.work.efficiency.statistics.agent.name.group': 'Agentengruppenname',
  'agent.work.efficiency.statistics.processing.time': 'Bearbeitungszeit',
  'agent.work.efficiency.statistics.efficiency.improvement':
    'Effizienzverbesserung',
  'agent.work.efficiency.statistics.efficiency.decline': 'Effizienzrückgang',
  'agent.work.efficiency.statistics.second.title.4':
    'Durchschnittliche Lösungszeit für Tickets',
  'agent.work.efficiency.statistics.agent.name.select': 'Agentenname：',
  'agent.work.efficiency.statistics.agent.name.select.placeholder':
    'Mehrere Agenten zur Datenauswertung wählbar',
  'agent.work.efficiency.statistics.agent.name.select.placeholder.team':
    'Mehrere Agentengruppen zur Datenauswertung wählbar',
  'agent.work.efficiency.statistics.table.agent.name': 'Agentenname',
  'agent.work.efficiency.statistics.second.title.5':
    'Trend der durchschnittlichen Lösungszeit für Tickets',
  'agent.work.efficiency.statistics.hour.text': 'Stunde',
  'agent.work.efficiency.statistics.day.text': 'Tag',
  'agent.work.efficiency.statistics.week.text': 'Woche',
  'agent.work.efficiency.statistics.month.text': 'Monat',
  'agent.work.efficiency.statistics.second.title.6':
    'Verteilung der durchschnittlichen Lösungszeit für Tickets',
  'agent.work.efficiency.statistics.second.title.7': 'Ticket SLA Report',
  'agent.work.efficiency.statistics.table.time': 'Zeit',

  // 满意度报表
  'satisfaction.report.title': 'Agenten-Zufriedenheitsstatistik',
  'satisfaction.report.title.group': 'Agentengruppen-Zufriedenheitsstatistik',
  'satisfaction.report.table.rating': 'Bewertung der Zufriedenheit',
  'satisfaction.report.second.title.1':
    'Durchschnittliche Zufriedenheit Punktzahl',
  'satisfaction.report.second.title.2': 'Antwortrate',
  'satisfaction.report.second.title.3':
    'Durchschnittliche Zufriedenheit Höchste Top3',
  'satisfaction.report.second.title.4':
    'Durchschnittliche Zufriedenheit Niedrigste Top3',
  'satisfaction.report.second.title.5':
    'Durchschnittliche Zufriedenheit Steigerung Top3',
  'satisfaction.report.second.title.6':
    'Durchschnittliche Zufriedenheit Rückgang Top3',
  'satisfaction.report.second.title.7':
    'Durchschnittliche Zufriedenheit Punktzahl',
  'satisfaction.report.second.title.8':
    'Trend der durchschnittlichen Zufriedenheit Punktzahl',
  'satisfaction.report.second.title.9':
    'Zufriedenheitsverteilung nach Tickettyp',
  'satisfaction.report.second.title.10':
    'Zufriedenheitsverteilung nach Ticketkanal',
  'satisfaction.report.second.title.11': 'Antwortrate',

  'satisfaction.report.data.insight.agent.group': 'Agentengruppe',
  'satisfaction.report.data.insight.agent': 'Agent',
  'satisfaction.report.data.insight.1':
    'Während dieses Zeitraums hat der Agent',
  'satisfaction.report.data.insight.1.group':
    'In diesem Zeitraum die Agentengruppe',
  'satisfaction.report.data.insight.2':
    'Die höchste Zufriedenheitsbewertung erreichte',
  'satisfaction.report.data.insight.3': 'Punkte.',
  'satisfaction.report.data.insight.4':
    'Die niedrigste Zufriedenheitsbewertung erreichte',
  'satisfaction.report.data.insight.5': 'Punkte.',
  'satisfaction.report.data.insight.6':
    'Die größte Steigerung der durchschnittlichen Zufriedenheit, stieg um',
  'satisfaction.report.data.insight.7': 'Punkte.',
  'satisfaction.report.data.insight.8':
    'Die größte Abnahme der durchschnittlichen Zufriedenheit, sank um',
  'satisfaction.report.data.insight.9': 'Punkte.',

  'satisfaction.report.satisfaction.score':
    'Durchschnittliche Zufriedenheitsbewertung',
  'satisfaction.report.improve.average.score':
    'Durchschnittliche Bewertung Verbessern',
  'satisfaction.report.decreased.average.score':
    'Durchschnittliche Bewertung Verringern',

  'satisfaction.report.average.satisfaction.score': 'Punkte',

  // 客户工单Top10
  'customer.ticket.title': 'Kunden Ticket Statistik',
  'customer.ticket.ranking': 'Rangfolge',
  'customer.ticket.customer.ticket.number': 'Anzahl Der Kundentickets',
  'customer.ticket.customer.name': 'Kundenname',

  // 新增座席组国际化
  'agent.work.efficiency.statistics.agent.name.select.placeholder.1':
    'Mehrere Agentengruppen zur Datenauswertung wählbar',

  // 工作效率统计气泡提示框内容
  'work.efficiency.statistics.average.resolution.time.content.text':
    '统计指定时间段内所有工单的平均解决时间，平均解决时间=sum(解决时间-创建时间)/工单总量',
  'work.efficiency.statistics.data.insight.content.text':
    'Führen Sie auf der Grundlage der Lösungszeit, Effizienz und anderer wichtiger Kennzahlen für Tickets eine tiefgreifende Datenanalyse durch, um die wichtigsten Daten wie die längste, kürzeste, Effizienzsteigerung und -abnahme zu ermitteln.',
  'work.efficiency.statistics.average.duration.agent.content1.text':
    'Die Top 3 Agenten mit der längsten durchschnittlichen Ticketlösungszeit innerhalb eines bestimmten Zeitraums',
  'work.efficiency.statistics.average.duration.agent.content2.text':
    'Die Top 3 Agenten mit der kürzesten durchschnittlichen Ticketlösungszeit innerhalb eines bestimmten Zeitraums',
  'work.efficiency.statistics.average.duration.agent.content3.text':
    'Die Top 3 Agenten mit der größten Reduzierung der durchschnittlichen Ticketlösungszeit innerhalb eines bestimmten Zeitraums',
  'work.efficiency.statistics.average.duration.agent.content4.text':
    'Die Top 3 Agenten mit der größten Zunahme der durchschnittlichen Ticketlösungszeit innerhalb eines bestimmten Zeitraums',
  'work.efficiency.statistics.average.time.resolve.tickets.text':
    'Berechnen Sie die durchschnittliche Lösungszeit für Tickets für jeden Agenten innerhalb eines bestimmten Zeitraums',
  'work.efficiency.statistics.trend.average.time.resolve.tickets.text':
    'Erfassen Sie den Trend der durchschnittlichen Lösungszeit für Tickets für jeden Agenten im Laufe der Zeit innerhalb eines bestimmten Zeitraums',
  'work.efficiency.statistics.distribution.processing.time.tickets.text':
    'Berechnen Sie die Anzahl der Tickets in verschiedenen Lösungszeitintervallen (z. B. 0-1 Stunde, 1-2 Stunden usw.) innerhalb eines bestimmten Zeitraums',
  'work.efficiency.statistics.ticket.SLA.report.content.text':
    'Berechnen Sie die SLA-Erfüllung innerhalb eines bestimmten Zeitraums',
  'work.efficiency.statistics.select.time.tips.text':
    'Der ausgewählte Zeitraum muss weniger als 24 Stunden betragen',

  // 满意度报表气泡提示框内容
  'satisfaction.report.average.satisfaction.score.content.text':
    'Berechnen Sie die durchschnittliche Zufriedenheitsbewertung aller  Tickets innerhalb eines bestimmten Zeitraums,  durchschnittliche Zufriedenheitsbewertung = Summe der  Zufriedenheitsbewertungen der Tickets / Gesamtanzahl der  Tickets mit Zufriedenheitsbewertungen',
  'satisfaction.report.average.satisfaction.score.content1.text':
    'Berechnen Sie die Gesamtrücklaufquote für Tickets innerhalb eines bestimmten Zeitraums. Rücklaufquote=Gesamtanzahl der Tickets mit Zufriedenheitsbewertungen / Gesamtanzahl aller Tickets',
  'satisfaction.report.data.insight.content.text':
    'Führen Sie basierend auf Kundenzufriedenheit und Rücklaufquote eine tiefgreifende Datenanalyse durch, um die wichtigsten Daten wie höchste, niedrigste, Verbesserung und Rückgang zu ermitteln.',
  'satisfaction.report.average.duration.agent.content1.text':
    'Berechnen Sie die durchschnittliche Zufriedenheitsbewertung für jeden Agenten innerhalb eines bestimmten Zeitraums, durchschnittliche Zufriedenheitsbewertung = Summe der Zufriedenheitsbewertungen der Tickets / Gesamtanzahl der Tickets mit Zufriedenheitsbewertungen',
  'satisfaction.report.average.time.resolve.tickets.text':
    'Erfassen Sie den Trend der durchschnittlichen Zufriedenheitsbewertung für jeden Agenten im Laufe der Zeit innerhalb eines bestimmten Zeitraums',
  'satisfaction.report.distribution.processing.time.tickets.text':
    'Berechnen Sie die durchschnittliche Zufriedenheitsbewertung für jeden Agenten für verschiedene Tickettypen innerhalb eines bestimmten Zeitraums',
  'satisfaction.report.channel.text':
    'Berechnen Sie die durchschnittliche Zufriedenheitsbewertung für jeden Agenten für verschiedene Ticketkanäle innerhalb eines bestimmten Zeitraums',
  'satisfaction.report.statistical.review.rate.agent.text':
    'Berechnen Sie die Rücklaufquote für jeden Agenten innerhalb eines bestimmten Zeitraums. Rücklaufquote = Gesamtanzahl der Tickets mit Zufriedenheitsbewertungen /Gesamtanzahl aller Tickets',
  'satisfaction.report.highest.average.satisfaction.content.text':
    'Top 3 Agenten mit der höchsten durchschnittlichen Zufriedenheitsbewertung innerhalb eines bestimmten Zeitraums',
  'satisfaction.report.shortest.average.satisfaction.content.text':
    'Top 3 Agenten mit der niedrigsten durchschnittlichen Zufriedenheitsbewertung innerhalb eines bestimmten Zeitraums',
  'satisfaction.report.average.efficiency.improvements.content.text':
    'Top 3 Agenten mit der größten Steigerung der durchschnittlichen Zufriedenheitsbewertung innerhalb eines bestimmten Zeitraums',
  'satisfaction.report.decreases.average.efficiency.content.text':
    'Top 3 Agenten mit der größten Abnahme der durchschnittlichen Zufriedenheitsbewertung innerhalb eines bestimmten Zeitraums',

  'agent.work.efficiency.statistics.time.unit': '(Einheit: Stunden)',
  'agent.work.efficiency.statistics.number.unit': '(Einheint:Stück)',
  'satisfaction.report.select.agent.tips': 'Maximal fünf Agenten auswählen!',
  'satisfaction.report.select.agent.group.tips':
    'Maximal fünf Agentengruppen auswählen!',
  'statistics.data.details.contact.id': '联系ID: ',
};
