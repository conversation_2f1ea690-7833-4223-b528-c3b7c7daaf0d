export default {
  'smart.quality.evaluation.table.title':
    'Intelligentes qualitätsbewertungsformular',
  'smart.quality.evaluation.table.add': 'Bewertungsformular hinzufügen',
  'smart.quality.evaluation.table.name': 'Name des bewertungsformulars',
  'smart.quality.evaluation.rule.category': 'Regelkategorie',
  'smart.quality.evaluation.assessment.name': 'Name des bewertungsformulars',
  'smart.quality.evaluation.table.channel': 'Kanal',
  'smart.quality.evaluation.table.channel.all': 'Alle kanäle',
  'smart.quality.evaluation.table.ticket.type': 'Ticket-typ',
  'smart.quality.evaluation.table.status': 'Veröffentlichungsstatus',
  'smart.quality.evaluation.table.status.published': 'Veröffentlicht',
  'smart.quality.evaluation.table.status.disabled': 'Unveröffentlicht',
  'smart.quality.evaluation.table.rule.count': 'Anzahl der bewertungsregeln',
  'smart.quality.evaluation.table.total.score': 'Gesamtpunktzahl',
  'smart.quality.evaluation.table.operation': 'Aktionen',
  'smart.quality.evaluation.table.enable': 'Aktivieren',
  'smart.quality.evaluation.table.enable.success': 'Erfolgreich aktiviert',
  'smart.quality.evaluation.table.disable': 'Deaktivieren',
  'smart.quality.evaluation.table.disable.success': 'Erfolgreich deaktiviert',
  'smart.quality.evaluation.table.edit': 'Bearbeiten',
  'smart.quality.evaluation.table.edit.rule': 'Regel bearbeiten',
  'smart.quality.evaluation.table.history': 'Bewertungsverlauf',
  'smart.quality.evaluation.table.delete': 'Löschen',
  'smart.quality.evaluation.table.delete.confirm':
    'Dieses bewertungsformular löschen?',
  'smart.quality.evaluation.table.delete.ok': 'Ja',
  'smart.quality.evaluation.table.delete.cancel': 'Nein',
  'smart.quality.evaluation.table.delete.success': 'Erfolgreich gelöscht',
  'smart.quality.evaluation.list.page.total.num': 'Insgesamt {total} einträge',
  // ====== 添加评估表页面相关 ======
  'smart.quality.evaluation.add': 'Bewertungsformular hinzufügen',
  'smart.quality.evaluation.add.baseinfo': 'Grundlegende informationen',
  'smart.quality.evaluation.add.rule': 'Bewertungsregeln',
  'smart.quality.evaluation.add.permission': 'Berechtigungseinstellungen',
  'smart.quality.evaluation.add.name': 'Name des bewertungsformulars',
  'smart.quality.evaluation.add.name.placeholder':
    'Namen des bewertungsformulars eingeben',
  'smart.quality.evaluation.add.name.required':
    'Namen des bewertungsformulars eingeben',
  'smart.quality.evaluation.add.name.max':
    'Die länge darf 80 zeichen nicht überschreiten',
  'smart.quality.evaluation.add.channel': 'Anwendbare kanäle',
  'smart.quality.evaluation.add.channel.placeholder':
    'Anwendbare kanäle auswählen',
  'smart.quality.evaluation.add.channel.required':
    'Anwendbare kanäle auswählen',
  'smart.quality.evaluation.add.ticket.type': 'Anwendbare ticket-typen',
  'smart.quality.evaluation.add.ticket.type.placeholder':
    'Anwendbare ticket-typen auswählen',
  'smart.quality.evaluation.add.ticket.type.required':
    'Anwendbare ticket-typen auswählen',
  'smart.quality.evaluation.add.total.score': 'Volle punktzahl',
  'smart.quality.evaluation.add.total.score.placeholder':
    'Volle punktzahl eingeben',
  'smart.quality.evaluation.add.total.score.required':
    'Volle punktzahl eingeben',
  'smart.quality.evaluation.add.score.mechanism': 'Bewertungsmechanismus',
  'smart.quality.evaluation.add.score.mechanism.add': 'Additive bewertung',
  'smart.quality.evaluation.add.score.mechanism.subtract':
    'Deduktive bewertung',
  'smart.quality.evaluation.add.score.mechanism.tooltip.add.title':
    'Additive bewertung',
  'smart.quality.evaluation.add.score.mechanism.tooltip.add.1':
    'Leistungs-mehrwert-bewertung',
  'smart.quality.evaluation.add.score.mechanism.tooltip.add.2':
    'Beginnt mit einer basispunktzahl, punkte für hervorragende leistungen werden hinzugefügt',
  'smart.quality.evaluation.add.score.mechanism.tooltip.add.3':
    'Betont positive verhaltensweisen und zusätzlichen wert, der über den standard hinausgeht',
  'smart.quality.evaluation.add.score.mechanism.tooltip.subtract.title':
    'Deduktive bewertung',
  'smart.quality.evaluation.add.score.mechanism.tooltip.subtract.1':
    'Compliance-grundlagen-bewertung',
  'smart.quality.evaluation.add.score.mechanism.tooltip.subtract.2':
    'Beginnt mit der vollen punktzahl, punkte für nicht konforme elemente werden abgezogen',
  'smart.quality.evaluation.add.score.mechanism.tooltip.subtract.3':
    'Betont die einhaltung grundlegender servicestandards und compliance',
  'smart.quality.evaluation.add.score.mechanism.required':
    'Bewertungsmechanismus auswählen',
  'smart.quality.evaluation.add.permission.scorer': 'Bewerter',
  'smart.quality.evaluation.add.permission.scorer.tooltip':
    'Definiert, wer basierend auf diesem bewertungsformular bewerten darf',
  'smart.quality.evaluation.add.permission.scorer.required':
    'Bewerter auswählen',
  'smart.quality.evaluation.add.permission.scorer.placeholder':
    'Bewerter auswählen',
  'smart.quality.evaluation.add.cancel.confirm':
    'Abbrechen löscht das formular. möchten sie wirklich abbrechen?',
  'smart.quality.common.cancel': 'Abbrechen',
  'smart.quality.common.next': 'Weiter',
  'smart.quality.common.yes': 'Ja',
  'smart.quality.common.no': 'Nein',
  'smart.quality.evaluation.add.channel.all': 'Alle',
  'smart.quality.evaluation.add.ticket.type.all': 'Alle',
  // ====== END 添加评估表页面相关 ======

  // ====== 评估历史记录页面相关 ======
  'smart.quality.evaluation.history.title': 'Bewertungsverlauf',
  'smart.quality.evaluation.history.name.placeholder':
    'Namen des bewertungsformulars eingeben',
  'smart.quality.evaluation.history.channel.placeholder': 'Kanal auswählen',
  'smart.quality.evaluation.history.ticket.type.placeholder':
    'Ticket-typ auswählen',
  'smart.quality.evaluation.history.agent.name': 'Agentenname',
  'smart.quality.evaluation.history.agent.name.placeholder':
    'Agentennamen auswählen',
  'smart.quality.evaluation.history.ticket.id': 'Ticket-ID',
  'smart.quality.evaluation.history.ticket.id.placeholder':
    'Ticket-ID eingeben',
  'smart.quality.evaluation.history.score': 'Punktzahl',
  'smart.quality.evaluation.history.evaluator': 'Bewerter',
  'smart.quality.evaluation.history.evaluator.placeholder':
    'Bewerter auswählen',
  'smart.quality.evaluation.history.score.range': 'Punktebereich',
  'smart.quality.evaluation.history.score.min': 'Min',
  'smart.quality.evaluation.history.score.max': 'Max',
  'smart.quality.evaluation.history.score.min.error':
    'Mindestwert darf nicht größer als der maximalwert sein',
  'smart.quality.evaluation.history.score.max.error':
    'Maximalwert darf nicht kleiner als der mindestwert sein',
  'smart.quality.evaluation.history.score.range.error':
    'Maximalwert darf nicht kleiner als der mindestwert sein',
  'smart.quality.evaluation.history.score.both.required':
    'Maximal- und mindestwert müssen beide ausgefüllt werden',
  'smart.quality.evaluation.history.score.range.warning':
    'Mindestwert sollte nicht größer als der maximalwert sein',
  'smart.quality.evaluation.history.score.format.error':
    'Bitte geben sie eine gültige zahl mit bis zu 2 dezimalstellen ein',
  'smart.quality.evaluation.history.search': 'Suchen',
  'smart.quality.evaluation.history.details': 'Details',
  'smart.quality.evaluation.history.export.pdf': 'PDF exportieren',
  'smart.quality.evaluation.history.page.total.num':
    'Insgesamt {total} einträge',
  'smart.quality.evaluation.history.evaluation.time': 'Bewertungszeitpunkt',
  'smart.quality.evaluation.history.return.list': 'Zurück zur liste',
  // ====== END 评估历史记录页面相关 ======

  'smart.quality.evaluation.rule.version.current': 'Neueste version',
  'smart.quality.evaluation.rule.version.current.tip':
    'Neueste version auswählen',
  'smart.quality.evaluation.rule.delete.category.tip.title': 'Hinweis',
  'smart.quality.evaluation.rule.delete.category.tip':
    'Diese kategorie enthält regeln. durch das löschen werden auch alle darin enthaltenen regeln gelöscht',
  'smart.quality.evaluation.rule.add.node.tip':
    'Bitte fügen sie zuerst einen knoten hinzu',
  'smart.quality.evaluation.rule.select.node.tip':
    'Bitte wählen sie zuerst einen knoten',

  // start 规则列表
  'smart.quality.evaluation.rule.category': 'Regelkategorie',
  'smart.quality.evaluation.rule.title': 'Bewertungsformular hinzufügen',
  'smart.quality.evaluation.rule.edit.title': 'Regel bearbeiten',
  'smart.quality.evaluation.rule.deploy.status': 'Bereitstellungsstatus',
  'smart.quality.evaluation.rule.deploy.status.unpublished': 'Unveröffentlicht',
  'smart.quality.evaluation.rule.deploy.status.published': 'Veröffentlicht',
  'smart.quality.evaluation.rule.button.cancel': 'Abbrechen',
  'smart.quality.evaluation.rule.button.save': 'Speichern',
  'smart.quality.evaluation.rule.button.save.and.publish':
    'Speichern und veröffentlichen',
  'smart.quality.evaluation.rule.search.placeholder':
    'Regelnamen oder beschreibung zur suche eingeben',
  'smart.quality.evaluation.rule.add': 'Regel hinzufügen',
  'smart.quality.evaluation.rule.table.category': 'Regelkategorie',
  'smart.quality.evaluation.rule.table.name': 'Regelname',
  'smart.quality.evaluation.rule.table.description': 'Regelbeschreibung',
  'smart.quality.evaluation.rule.table.score': 'Bewertungsregel',
  'smart.quality.evaluation.rule.table.evaluation.method': 'Bewertungsmethode',
  'smart.quality.evaluation.rule.table.evaluation.method.manual':
    'Manuelle bewertung',
  'smart.quality.evaluation.rule.table.evaluation.method.ai': 'KI-bewertung',
  'smart.quality.evaluation.rule.table.operation': 'Aktionen',
  'smart.quality.evaluation.rule.table.operation.edit': 'Bearbeiten',
  'smart.quality.evaluation.rule.table.delete.confirm': 'Diese regel löschen?',
  'smart.quality.evaluation.rule.table.delete.ok': 'Ja',
  'smart.quality.evaluation.rule.table.delete.cancel': 'Nein',
  'smart.quality.evaluation.rule.table.operation.delete': 'Löschen',
  'smart.quality.evaluation.rule.table.operation.detail': 'Details',
  'smart.quality.evaluation.rule.new.top.level': 'Neue kategorie',
  'smart.quality.evaluation.rule.new.category': 'Neue kategorie',
  'smart.quality.evaluation.rule.page.total.num': 'Insgesamt {total} einträge',
  'smart.quality.evaluation.rule.add.score.add': 'Additiv',
  'smart.quality.evaluation.rule.add.score.subtract': 'Deduktiv',
  // end 规则列表

  // start 添加规则页面
  'smart.quality.evaluation.rule.add.title': 'Regel hinzufügen',
  'smart.quality.evaluation.rule.edit.title': 'Regel bearbeiten',
  'smart.quality.evaluation.rule.detail.title': 'Regeldetails',
  'smart.quality.evaluation.rule.add.baseinfo': 'Grundlegende informationen',
  'smart.quality.evaluation.rule.add.baseinfo.name': 'Regelname',
  'smart.quality.evaluation.rule.add.baseinfo.name.placeholder':
    'Regelnamen eingeben',
  'smart.quality.evaluation.rule.add.baseinfo.name.required':
    'Regelnamen eingeben',
  'smart.quality.evaluation.rule.name': 'Regelname',
  'smart.quality.evaluation.rule.category.required': 'Regelkategorie auswählen',
  'smart.quality.evaluation.rule.name.placeholder': 'Regelnamen eingeben',
  'smart.quality.evaluation.rule.name.required': 'Regelnamen eingeben',
  'smart.quality.evaluation.rule.name.max':
    'Die länge darf 80 zeichen nicht überschreiten',
  'smart.quality.evaluation.rule.description': 'Regelbeschreibung',
  'smart.quality.evaluation.rule.description.placeholder':
    'Regelbeschreibung eingeben',
  'smart.quality.evaluation.rule.description.required':
    'Regelbeschreibung eingeben',
  'smart.quality.evaluation.rule.description.max':
    'Die länge darf 200 zeichen nicht überschreiten',
  'smart.quality.evaluation.rule.category.placeholder':
    'Regelkategorie auswählen',
  'smart.quality.evaluation.rule.category.required': 'Regelkategorie auswählen',
  'smart.quality.evaluation.rule.evaluation.method': 'Bewertungsmethode',
  'smart.quality.evaluation.rule.evaluation.method.ai': 'KI-bewertung',
  'smart.quality.evaluation.rule.evaluation.method.manual':
    'Manuelle bewertung',
  'smart.quality.evaluation.rule.ai.settings': 'KI-bewertungsregel',
  'smart.quality.evaluation.rule.manual.settings': 'Manuelle bewertungsregel',
  'smart.quality.evaluation.rule.total.score': 'Gesamtpunktzahl der regel',
  'smart.quality.evaluation.rule.total.score.placeholder':
    'Gesamtpunktzahl der regel eingeben',
  'smart.quality.evaluation.rule.total.score.required':
    'Gesamtpunktzahl der regel eingeben',
  'smart.quality.evaluation.rule.total.score.exceed.error':
    'Gesamtpunktzahl der regel darf die gesamtpunktzahl des formulars nicht überschreiten',
  'smart.quality.evaluation.rule.total.score.exceed.error.add':
    'Die gesamten hinzugefügten punkte der regel dürfen die gesamtpunktzahl des formulars nicht überschreiten',
  'smart.quality.evaluation.rule.total.score.exceed.error.subtract':
    'Die gesamten abgezogenen punkte der regel dürfen die gesamtpunktzahl des formulars nicht überschreiten',
  'smart.quality.evaluation.rule.aigc.rule1.score.exceed.error':
    'Die punktzahl der KI-bewertungsregel darf die gesamtpunktzahl der regel nicht überschreiten',
  'smart.quality.evaluation.rule.aigc.rule1.score.exceed.error.add':
    'Die hinzugefügten punkte der KI-bewertungsregel dürfen die gesamtpunktzahl der regel nicht überschreiten',
  'smart.quality.evaluation.rule.aigc.rule1.score.exceed.error.subtract':
    'Die abgezogenen punkte der KI-bewertungsregel dürfen die gesamtpunktzahl der regel nicht überschreiten',
  'smart.quality.evaluation.rule.aigc.rule2.score.exceed.error':
    'Die punktzahl der KI-bewertungsregel darf die gesamtpunktzahl der regel nicht überschreiten',
  'smart.quality.evaluation.rule.aigc.rule2.score.exceed.error.add':
    'Die hinzugefügten punkte der KI-bewertungsregel dürfen die gesamtpunktzahl der regel nicht überschreiten',
  'smart.quality.evaluation.rule.aigc.rule2.score.exceed.error.subtract':
    'Die abgezogenen punkte der KI-bewertungsregel dürfen die gesamtpunktzahl der regel nicht überschreiten',
  'smart.quality.evaluation.rule.manual.option.required':
    'Bitte fügen sie mindestens eine bewertungsregel hinzu',
  'smart.quality.evaluation.rule.manual.option.save.first':
    'Bitte speichern sie zuerst die regel, die sie bearbeiten',
  'smart.quality.evaluation.rule.check.points': 'Prüfpunkt',
  'smart.quality.evaluation.rule.check.point.placeholder':
    'Geben sie eine detaillierte prüfpunktbeschreibung ein',
  'smart.quality.evaluation.rule.check.point.required': 'Prüfpunkt eingeben',
  'smart.quality.evaluation.rule.add.check.point': 'Prüfpunkt hinzufügen',
  'smart.quality.evaluation.rule.check.points.max':
    'Es können bis zu 15 prüfpunkte hinzugefügt werden',
  'smart.quality.evaluation.add.rule.button.return': 'Zurück',
  'smart.quality.evaluation.add.rule.button.save': 'Speichern',
  // end 添加规则页面

  // start 人工评分规则相关
  'smart.quality.evaluation.add.rule.manual.option.name': 'Optionsname',
  'smart.quality.evaluation.add.rule.manual.option.name.required':
    'Optionsnamen eingeben',
  'smart.quality.evaluation.add.rule.manual.option.name.placeholder':
    'Optionsnamen eingeben',
  'smart.quality.evaluation.add.rule.manual.option.name.duplicate':
    'Optionsnamen dürfen nicht doppelt vorhanden sein',
  'smart.quality.evaluation.add.rule.manual.option.score': 'Punktzahl',
  'smart.quality.evaluation.add.rule.manual.option.score.required':
    'Punktzahl eingeben',
  'smart.quality.evaluation.add.rule.manual.option.operation': 'Aktionen',
  'smart.quality.evaluation.add.rule.manual.rules': 'Manuelle bewertungsregel',
  'smart.quality.evaluation.add.rule.manual.add.option': 'Option hinzufügen',
  'smart.quality.evaluation.add.rule.manual.standard':
    'Referenz für bewertungskriterien',
  'smart.quality.evaluation.add.rule.manual.standard.required':
    'Referenz für bewertungskriterien eingeben',
  'smart.quality.evaluation.add.rule.manual.standard.placeholder':
    'Referenz für bewertungskriterien eingeben',
  // end 人工评分规则相关

  // start AIGC评分规则相关
  'smart.quality.evaluation.add.rule.aigc.rules': 'KI-bewertungsregel',
  'smart.quality.evaluation.add.rule.aigc.rules.required':
    'KI-bewertungsregel auswählen',
  'smart.quality.evaluation.add.rule.aigc.rule1.title':
    'Wenn einer der prüfpunkte auftritt',
  'smart.quality.evaluation.add.rule.aigc.rule1.prefix':
    ', für jedes vorkommen',

  'smart.quality.evaluation.add.rule.aigc.rule1.score.required':
    'Punktzahl eingeben',
  'smart.quality.evaluation.add.rule.aigc.rule1.middle':
    'punkte, mit einem maximum von',
  'smart.quality.evaluation.add.rule.aigc.rule1.max.score.required':
    'Maximale kumulative punktzahl eingeben',
  'smart.quality.evaluation.add.rule.aigc.rule1.suffix': 'punkte',
  'smart.quality.evaluation.add.rule.aigc.rule2.title':
    'Wenn mehrere prüfpunkte auftreten',
  'smart.quality.evaluation.add.rule.aigc.rule2.prefix':
    ', wenn die anzahl der vorkommen ist',
  'smart.quality.evaluation.add.rule.aigc.rule2.condition.required':
    'Anzahl der vorkommen auswählen',
  'smart.quality.evaluation.add.rule.aigc.rule2.condition.gt': 'Größer als',
  'smart.quality.evaluation.add.rule.aigc.rule2.condition.eq': 'Gleich',
  'smart.quality.evaluation.add.rule.aigc.rule2.condition.lt': 'Kleiner als',
  'smart.quality.evaluation.add.rule.aigc.rule2.threshold.required':
    'Anzahl eingeben',
  'smart.quality.evaluation.add.rule.aigc.rule2.middle': 'mal,',
  'smart.quality.evaluation.add.rule.aigc.rule2.score.required':
    'Punktzahl eingeben',
  'smart.quality.evaluation.add.rule.aigc.rule2.suffix': 'Punkte',
  // end AIGC评分规则相关
};
