export default {
  'home.page.menu.home': 'Home',
  'home.page.menu.cloud.contact.center': 'Cloud contact center',
  'home.page.menu.contact.us': 'Contact us',
  'home.page.menu.free.trial': 'Login',
  'home.page.menu.experience.btn': 'Experience now!',
  'home.page.menu.tips.text':
    'Wir glauben, dass die Nutzung jeder Gelegenheit zur Kommunikation mit Kunden Unternehmen hilft, ihr Geschäft auszubauen. Zeit und Kanäle werden nicht länger eine Grenze für Ihre Kommunikation mit Kunden sein.',

  'home.page.text.aigc': 'Aigc, smart office',
  'home.page.text.aigc.detail':
    'Verfügt über eine mehrsprachige Online-übersetzungsfunktion, mit der Sie sich von sprachbarrieren befreien und Ihren Kunden den Genuss von <b>{CH}</b>-diensten ermöglichen können;',
  'home.page.text.aigc.detail1':
    'Fasst chat-inhalte automatisch zusammen und extrahiert ticket-inhalte.',
  'home.page.text.aigc.detail2': 'Ai beurteilt kundenemotionen.',
  'home.page.text.aigc.detail3':
    'Generiert nach belieben bilder verschiedener stile basierend auf text.',

  'home.page.text.channel': 'Omnichannel access, seamless connection',
  'home.page.text.channel.detail':
    'Wir integrieren telefon, Facebook, WhatsApp und andere soziale medienkanäle, E-Mail <b>{CH}</b>, damit Kunden Sie über ihre bevorzugte methode kontaktieren und ein einheitliches serviceerlebnis genießen können.',

  'home.page.text.channel.info':
    'Omnichannel 360° customer information display',
  'home.page.text.channel.info.detail':
    'Telefon, Facebook, WhatsApp und andere soziale medienkanäle, E-Mail und andere omnichannel-kundeninformationen <b>{CH}</b>;',
  'home.page.text.channel.info.detail1':
    'Kann den verlauf von konversationsaufzeichnungen von <b>{CH}</b> einsehen;',
  'home.page.text.channel.info.detail3':
    'Sie können weitere benutzerdefinierte felder für kundendaten konfigurieren, die ihren anforderungen entsprechen.',
  'home.page.text.channel.info.span': 'Centralized display',

  'home.page.text.intelligent':
    'Smart tickets, automatic recording of each interaction',
  'home.page.text.intelligent.detail':
    '来自每个渠道的每次客户咨询，都将被记录，并<b>{CH1}</b>，便于跟进、转接，确保客户得到最佳反馈。',

  'home.page.text.intelligent.customer.service':
    'Intelligent customer service, caring assistant',
  'home.page.text.intelligent.customer.service.detail':
    "Gold medal robot customer service online <b>{CH}</b> hours, automatic reply to customers' high-frequency and repetitive questions all day, support learning all kinds of enterprise documents, and set standard faq, built-in nlp, built-in nlp natural language understanding, give customers accurate, understandable, professional automatic reply.",

  'home.page.text.teamwork': 'Internal team collaboration',
  'home.page.text.teamwork.detail':
    'Customer service staff often need to <b>{CH}</b> with other internal colleagues in the process of serving customers, and the platform allows customer service staff to directly transfer the current call or chat to other customer service staff.',

  'home.page.text.convenient': 'Multi-dimensional data visualization report',
  'home.page.text.convenient.detail':
    'Built-in <b>{CH}</b> monitoring report, multi-dimensional real-time display of customer service work.',

  'home.page.text.global': 'Global deployment, smooth and stable',
  'home.page.text.global.detail':
    "Supports deployment in <b>{CH}</b> countries around the world, with smooth lines, stable calls, and listening to every customer's voice.",

  'home.page.text.fast': 'Rapid construction, seamless upgrade',
  'home.page.text.fast.detail':
    'Complete the construction of the customer intelligent contact center within <b>{CH}</b>, and meet the rapid and imperceptible online.',

  'home.page.text.start.tips': '做好准备好为您的客户提升服务体验了吗？ ',
  'home.page.text.start.btn': 'Get started now!',

  'home.page.footer.service.consultation': 'Service consultation',
  'home.page.footer.service.phone': '服务电话：010-********',
  'home.page.footer.service.email': '联系邮箱：<EMAIL>',
  'home.page.footer.service.qq': 'QQ联系：**********',

  'home.page.footer.about.us': 'About us',
  'home.page.footer.contact.us': 'Contact us',
  'home.page.footer.friendly.links': 'Friendly links',
  'home.page.footer.friendly.links.goclouds': 'Goclouds',
  'home.page.footer.follow.us': 'Follow us',
  'home.page.footer.follow.item1': 'Follow the official account',
  'home.page.footer.follow.item2': 'Follow the official mini program',

  // 文本
  'home.page.contactUS.test.contactAddress': 'Contact address',
  'home.page.contactUS.text.address':
    'Heqiao building, no. 8 guanghua road, chaoyang district, beijing',
  'home.page.contactUS.text.emailAndPhone': 'Email address',
  'home.page.contactUS.text.socialMedia': 'Social media',

  // 表单
  'home.page.contactUS.form.lable.userName': 'Name',
  'home.page.contactUS.form.lable.companyEmail': 'Company email',
  'home.page.contactUS.form.lable.phoneNumber': 'Phone number',
  'home.page.contactUS.form.lable.question': 'Your question',
  'home.page.contactUS.form.placeholder.lastName': 'Enter your last name here',
  'home.page.contactUS.form.placeholder.userName': 'Enter your name here',
  'home.page.contactUS.form.placeholder.companyEmail':
    'Enter your company email here',
  'home.page.contactUS.form.placeholder.phoneNumber':
    'Enter your phone number here',
  'home.page.contactUS.form.placeholder.lastName1':
    'Please enter your last name',
  'home.page.contactUS.form.placeholder.userName1': 'Please enter your name',
  'home.page.contactUS.form.placeholder.companyEmail1':
    'Please enter your email',
  'home.page.contactUS.form.placeholder.phoneNumber1':
    'Please enter your phone number',
  'home.page.contactUS.form.placeholder.question': 'Please enter your question',

  // 按钮
  'home.page.contactUS.button.submit': 'Submit',

  'home.page.center.content.title': 'Features',

  'login.tips.title': 'Coco reminds you',
  'login.tips.text':
    'Please log in with your computer browser to experience it~',
  'login.tips.return.homePage.btn': 'Return to home page',
  'phone.header.select.language': 'Select language',
  'home.page.region.china': 'China',
  'home.page.region.asia': 'Asia',
  'home.page.region.europe': 'Europe',
  'home.page.region.usa': 'USA',
};
