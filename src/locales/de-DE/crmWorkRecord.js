export default {
  'workRecord.customer.link.way': 'Kundenkontaktinformationen',
  'common.total.items': 'Insgesamt {total} Elemente',

  'message.upload.size':
    'Die Größe der hochgeladenen Datei darf {fileSize} MB nicht überschreiten',

  'work.record.channel.name': 'Kanaltyp',

  'work.record.time.range': 'Zeitbereich',
  'work.record.waiting.handle': 'Ausstehend',
  'work.record.in.process': 'In Bearbeitung',
  'work.record.resolved': 'Gelöst',

  'work.record.id': 'Datensatz-ID',
  'work.record.customer.name': 'Kundenname',
  'work.record.customer.link.way': 'Kundenkontaktinformationen',
  'work.record.settle.name': 'Agentenname',
  'work.record.status': 'Status',
  'work.record.create.time': 'Erstellungszeit',
  'work.record.resolve.time': 'Lösungszeit',
  'work.record.operation': 'Betrieb',

  'work.record': 'Ticket',
  'work.record.detail': 'Details',
  'work.record.reply': 'Antworten',
  'work.record.mark.as.in.process': 'Als "In Bearbeitung" markieren',
  'work.record.mark.as.resolved': 'Als "Gelöst" markieren',
  'work.record.note': 'Notiz',
  'work.record.button.ok': 'Bestätigen',
  'work.record.button.cancel': 'Stornieren',

  'work.record.customer.info': 'Kundeninformationen',
  'work.record.return': 'Zurück',
  'work.record.channel': 'Kanal',
  'work.record.start.time': 'Startzeit',
  'work.record.button.save': 'Speichern',

  'work.record.recipient': 'Empfänger',
  'work.record.caller': 'Anrufer',
  'work.record.voice.record': 'Sprachaufzeichnung',

  'work.record.tips.note.limit':
    'Bitte geben Sie den Notizinhalt ein, maximal 2000 Zeichen.',
  'work.record.tips.customer.search.enter':
    'Bitte geben Sie die Kundenkontaktinformationen ein und drücken Sie die Eingabetaste zur Suche.',
  'work.record.tips.note.settle.search.enter':
    'Bitte geben Sie den Agentennamen ein und drücken Sie die Eingabetaste zur Suche.',
  'work.record.tips.note.keyword.enter':
    'Bitte geben Sie die Datensatz-ID, Kundenkontaktinformationen oder dynamische Spalteninhalte usw. ein und drücken Sie die Eingabetaste zur Suche.',

  'work.record.tips.note.content.not.empty':
    'Bitte geben Sie einen nicht leeren Notizinhalt ein, bevor Sie speichern!',
  'work.record.tips.note.content.not.empty.string':
    'Der Notizinhalt darf keine leere Zeichenfolge sein!',
  'work.record.tips.note.content.num.limit':
    'Der Notizinhalt darf maximal 2000 Zeichen lang sein!',
  'work.record.tips.content.not.empty.enter':
    'Bitte geben Sie einen nicht leeren Inhalt ein, bevor Sie die Eingabetaste drücken!',
  'work.record.tips.content.num.limit':
    'Der Inhalt darf nicht länger als 80 Zeichen sein!',
  'work.record.tips.customer.link.way.not.empty.string':
    'Das Eingabefeld für Kundenkontaktinformationen darf keine leere Zeichenfolge sein!',
  'work.record.tips.settle.not.empty.string':
    'Das Eingabefeld für Agentennamen darf keine leere Zeichenfolge sein!',
  'work.record.tips.keyword.not.empty.string':
    'Der Inhalt des Suchfelds darf keine leere Zeichenfolge sein!',
  'work.record.tips.at.least.one.status':
    'Bitte wählen Sie mindestens einen Status aus!',

  'work.record.detail.contact.search.copy': 'Contact Search-Entsprechung',
  'work.record.reply.content.not.empty':
    'Der Antwortinhalt darf nicht leer sein!',
  'work.record.reply.success': 'Antwort erfolgreich!',

  'work.record.detail.copy.success': 'Kopieren erfolgreich!',

  'work.record.channel.type': 'Bitte wählen sie den Kanaltyp',
};
