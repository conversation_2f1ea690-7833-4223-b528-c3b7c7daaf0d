export default {
  'add.chatVoice.channel.configuration.title':
    'Web-Online-Chat-Sprachkanal hinzufügen',
  'add.chatVoice.channel.configuration.title.update':
    'Web-Online-Chat-Sprachkanal bearbeiten',
  'app.add.chatVoice.channel.configuration.title':
    'App-Online-Chat-Sprachkanal hinzufügen',
  'app.add.chatVoice.channel.configuration.title.update':
    'App-Online-Chat-Sprachkanal bearbeiten',
  'web.video.channel.configuration.title.update':
    'Web-Online-Videokanal bearbeiten',
  'web.video.channel.configuration.title': 'Web-Online-Videokanal hinzufügen',
  'app.video.channel.configuration.title.update':
    'App-Online-Videokanal bearbeiten',
  'app.video.channel.configuration.title': 'App-Online-Videokanal hinzufügen',
  'app.video.channel.configuration.live.templete':
    'Möchten Sie diesen Chat beenden und einen Videoanruf starten?',

  'add.chatVoice.channel.configuration.tips':
    'Ermöglichen Sie Kunden, das Unternehmen einfacher über den Web-Online-Chat zu kontaktieren und reagieren Sie schneller auf Kundenbedürfnisse.',
  'app.add.chatVoice.channel.configuration.tips':
    'Ermöglichen Sie Kunden, das Unternehmen einfacher über den App-Online-Chat zu kontaktieren und reagieren Sie schneller auf Kundenbedürfnisse.',
  'add.chatVideo.channel.configuration.tips':
    'Ermöglichen Sie Kunden, das Unternehmen einfacher über den Web-Online-Video zu kontaktieren und reagieren Sie schneller auf Kundenbedürfnisse.',
  'app.add.chatVideo.channel.configuration.tips':
    'Ermöglichen Sie Kunden, das Unternehmen einfacher über den App-Online-Video zu kontaktieren und reagieren Sie schneller auf Kundenbedürfnisse.',
  'chatVoice.channel.configuration.title.1':
    'Geben Sie die grundlegenden Kanalinformationen ein',
  'chatVoice.channel.configuration.title.tips.1':
    'Bitte geben Sie die grundlegenden Kanalinformationen ein',
  'chatVoice.channel.configuration.title.2': 'Erscheinungsbild-Einstellungen',
  'chatVoice.channel.configuration.title.tips.2':
    'Hier können Sie die Chatbox konfigurieren und Ihr bevorzugtes Logo und Ihre bevorzugte Farbe auswählen.',
  'chatVoice.channel.configuration.title.3':
    'Grundlegende Funktionseinstellungen',
  'chatVoice.channel.configuration.title.tips.3':
    'Hier können Sie die grundlegenden Funktionen der Chatbox konfigurieren.',
  'chatVoice.channel.configuration.title.4':
    'Intelligente Kundenservice-Einstellungen',
  'chatVoice.channel.configuration.title.tips.4':
    'Hier können Sie die relevanten Informationen des intelligenten Kundendienstes konfigurieren.',
  'chatVoice.channel.configuration.title.5': 'Bereitstellung',
  'chatVoice.channel.configuration.title.tips.5':
    'Bitte folgen Sie den folgenden Anweisungen, um die Chatbox auf Ihrer Website bereitzustellen.',
  'chatVoice.channel.configuration.channel.name': 'Kanalname',
  'chatVoice.channel.configuration.channel.name.placeholder':
    'Bitte Kanalnamen eingeben',
  'chatVoice.channel.configuration.chatVoice.types': 'Sprache auswählen',
  'chatVoice.channel.configuration.chatVoice.types.placeholder':
    'Bitte Sprache auswählen',
  'chatVoice.channel.configuration.chat2.logo': 'Firmenlogo',
  'chatVoice.channel.configuration.chat2.logo.message1':
    'Es können nur JPG/PNG-Dateien hochgeladen werden, die 500 KB nicht überschreiten.',
  'chatVoice.channel.configuration.chat2.logo.message2':
    '会在聊天框左上角展示，建议上传50*20px的PNG图片',
  'chatVoice.channel.configuration.chat2.chatBoxName.placeholder':
    'Bitte Chatbox-Namen eingeben',
  'chatVoice.channel.configuration.chat2.chatBoxName': 'Chatbox-Name',
  'chatVoice.channel.configuration.chat2.chatBoxName.message':
    'Wird oben links in der Chatbox angezeigt',
  'chatVoice.channel.configuration.chat2.templete': 'Themenfarbe',
  'chatVoice.channel.configuration.chat2.templete.custom': 'Benutzerdefiniert',
  'chatVoice.channel.configuration.chat2.templete.color': 'Aktuelle Farbe:',
  'chatVoice.channel.configuration.chat2.templete.placeholder':
    'Bitte Farbe auswählen',
  'chatVoice.channel.configuration.chat2.boxColor':
    'Kundenservice-Chatbox-Farbe',
  'chatVoice.channel.configuration.chat2.userBox': 'Benutzer-Chatbox-Farbe',
  'chatVoice.channel.configuration.chat2.information.configuration.completed':
    'Sie haben die Erscheinungsbild-Einstellungen der Chatbox abgeschlossen. Sie können auf die Schaltfläche "Vorschau" klicken, um den Stil anzuzeigen.',
  'chatVoice.channel.configuration.work.panels.checkbox': 'Einschalten',
  'chatVoice.channel.configuration.chat3.form':
    'Benutzerinformations-Formularseite',
  'chatVoice.channel.configuration.chat3.form.message':
    'Es wird empfohlen, diese Option zu aktivieren. Nach der Aktivierung müssen Benutzer zuerst grundlegende Informationen eingeben, bevor sie weiter kommunizieren können.',
  'chatVoice.channel.configuration.chat3.welcome': 'Erste Begrüßung',
  'chatVoice.channel.configuration.chat3.welcome.words':
    'Ein-Wort-Willkommensnachricht',
  'chatVoice.channel.configuration.chat3.welcome.words.placeholder':
    'Bitte Begrüßung eingeben',
  'chatVoice.channel.configuration.chat3.welcome.words.message':
    'Bitte stellen Sie sicher, dass die Einstellungen hier mit der im ersten Schritt ausgewählten Sprache übereinstimmen.',
  'chatVoice.channel.configuration.chat3.welcome.QA': 'FAQ auslösen',
  'chatVoice.channel.configuration.chat3.welcome.QA.placeholder':
    'Bitte wählen Sie "FAQ auslösen"',
  'chatVoice.channel.configuration.chat3.interval.placeholder':
    'Bitte geben Sie die Zeit für die automatische Einladung zur Sitzung ein.',
  'chatVoice.channel.configuration.chat3.welcome.QA.message':
    'Mit FAQ können Sie mehrere Antworten auf einmal an den Benutzer zurücksenden, nicht nur Text, sondern auch Bilder und Videos. Wenn Sie eine bestimmte FAQ auswählen, antwortet das System automatisch mit der in der FAQ konfigurierten Standardantwort, wenn der Benutzer Sie kontaktiert. Wenn Sie noch keine FAQ eingerichtet haben, klicken Sie bitte hier',
  'chatVoice.channel.configuration.chat3.welcome.QA.message.1': 'Hier',
  'chatVoice.channel.configuration.chat3.welcome.QA.message.2':
    'um Einstellungen vorzunehmen.',
  'chatVoice.channel.configuration.chat3.talk':
    'Automatische Einladung zur Sitzung',
  'chatVoice.channel.configuration.chat3.talk.ge': 'Intervall',
  'chatVoice.channel.configuration.chat3.talk.ge2':
    'Sekunden, das System öffnet automatisch die Chatbox',
  'chatVoice.channel.configuration.chat3.message':
    'Während der Benutzer die Website durchsucht, öffnet das System automatisch ein Popup-Fenster, um den Benutzer zur Beratung einzuladen',
  'chatVoice.channel.configuration.chat3.talk.Input':
    'Automatische Einladung zur Sitzung Willkommensnachricht',
  'chatVoice.channel.configuration.chat3.talk.Input.placeholder':
    'Bitte geben Sie die Willkommensnachricht für die automatische Einladung zur Sitzung ein',
  'chatVoice.channel.configuration.chat3.talk.Input.message':
    'Hier können Sie die Willkommensnachricht einstellen, die angezeigt wird, wenn die automatische Einladung zur Sitzung angezeigt wird',

  'chatVoice.channel.configuration.chat3.voice.message':
    'Benutzer können während der Kommunikation mit dem Kundendienst auf die Schaltfläche für die Videokommunikation unten klicken, um die Sprachkommunikation zu nutzen',
  'chatVoice.channel.configuration.chat3.voice': 'Online-Sprachkommunikation',
  'chatVoice.channel.configuration.chat3.video.message':
    'Benutzer können während der Kommunikation mit dem Kundendienst auf die Schaltfläche für die Videokommunikation unten klicken, um die Videokommunikation zu nutzen',
  'chatVoice.channel.configuration.chat3.video': 'Online-Videokommunikation',
  'chatVoice.channel.configuration.chat3.whatsApp.message':
    'Nach der Aktivierung wird der WhatsApp-Eingang unten im Chatfenster angezeigt',
  'chatVoice.channel.configuration.chat3.whatsApp': 'WhatsApp-Kanal anzeigen',
  'chatVoice.channel.configuration.chat3.email.message':
    'Nach der Aktivierung wird der E-Mail-Eingang unten im Chatfenster angezeigt',
  'chatVoice.channel.configuration.chat3.email': 'E-Mail-Kanal anzeigen',
  'chatVoice.channel.configuration.chat3.evaluate.message':
    'Nachdem der Benutzer den Chat beendet hat, öffnet das System automatisch eine Zufriedenheitsumfrage',
  'chatVoice.channel.configuration.chat3.evaluate': 'Zufriedenheitsbewertung',

  'chatVoice.channel.configuration.chat3.email.select':
    'Verbundener E-Mail-Kanal',
  'chatVoice.channel.configuration.chat3.email.select.placeholder':
    'Bitte wählen Sie den verbundenen E-Mail-Kanal',
  'chatVoice.channel.configuration.chat3.email.select.message':
    'Nach der Verknüpfung des Postfachs können Benutzer direkt auf das E-Mail-Symbol unten im Chatfenster klicken, um Kontakt aufzunehmen',
  'chatVoice.channel.configuration.chat3.WhatsApp.select':
    'Verbundener WhatsApp-Kanal',
  'chatVoice.channel.configuration.chat3.WhatsApp.select.placeholder':
    'Bitte wählen Sie den verbundenen WhatsApp-Kanal',
  'chatVoice.channel.configuration.chat3.whatsApp.select.message':
    'Nach der Verknüpfung der WhatsApp-Nummer können Benutzer direkt auf das WhatsApp-Symbol unten im Chatfenster klicken, um eine WhatsApp-Sitzung zu starten',

  'chatVoice.channel.configuration.chat3.information.configuration.completed':
    'Sie haben die grundlegenden Funktionseinstellungen abgeschlossen. Diese werden in der Chatbox implementiert, nachdem Sie sie gespeichert haben.',

  'chatVoice.channel.configuration.chat4.mode.message':
    'Der intelligente Kundenservice wird zuerst vom Roboter beantwortet. Bei Fragen, die der Roboter nicht beantworten kann, kann der Benutzer jederzeit zum menschlichen Kundenservice wechseln. / Nur menschliche Antwort: Nur menschliche Kundendienstmitarbeiter beantworten Fragen. / Nur Roboterantwort: Nur Roboter beantworten Fragen.',
  'chatVoice.channel.configuration.chat4.mode.message.1': ` `,
  'chatVoice.channel.configuration.chat4.mode.message.2': ` `,
  'chatVoice.channel.configuration.chat4.mode.1': 'Intelligenter Kundenservice',
  'chatVoice.channel.configuration.chat4.mode.2': 'Nur manuell',
  'chatVoice.channel.configuration.chat4.mode.3': 'Nur Roboter',
  'chatVoice.channel.configuration.chat4.mode': 'Kundenservice-Modus',
  'chatVoice.channel.configuration.chat4.robot.message':
    'Der Robotername wird über der Antwort des Roboters angezeigt',
  'chatVoice.channel.configuration.chat4.robot.placeholder':
    'Bitte Roboternamen eingeben',
  'chatVoice.channel.configuration.chat4.robot': 'Robotername',
  'chatVoice.channel.configuration.chat4.language.message':
    'Nach der Aktivierung erkennt das System automatisch die Sprache der "Eingabefrage" des Benutzers. Wenn diese Option nicht aktiviert ist, wird die Sprache des Browsers des Benutzers verwendet.',
  'chatVoice.channel.configuration.chat4.language':
    'Automatische Spracherkennung',
  'chatVoice.channel.configuration.chat4.document':
    'Dokumenten-Wissensdatenbank',
  'chatVoice.channel.configuration.chat4.document.placeholder':
    'Bitte wählen Sie die Dokumenten-Wissensdatenbank',
  'chatVoice.channel.configuration.chat4.document.message.1':
    'Die Dokumenten-Wissensdatenbank hier zeigt nur externe Wissensdatenbanken an. Bitte gehen Sie vorher zu',
  'chatVoice.channel.configuration.chat4.document.message':
    'Dokumenten-Wissensdatenbank',
  'chatVoice.channel.configuration.chat4.document.message.2':
    'um die Wissensdatenbank zu konfigurieren',
  'chatVoice.channel.configuration.chat4.ai.message':
    'Sie können konfigurieren, ob eine Verbindung zu generativer KI hergestellt werden soll',
  'chatVoice.channel.configuration.chat4.ai':
    'Verbindung zu generativer KI herstellen',
  'chatVoice.channel.configuration.chat4.workers':
    'Unbekannte Antwort, Schaltfläche zum Weiterleiten an den menschlichen Agenten',
  'chatVoice.channel.configuration.chat4.workers.content':
    'Es tut mir leid, ich kann diese Frage nicht beantworten. Bitte wenden Sie sich an den menschlichen Kundenservice.',
  'chatVoice.channel.configuration.chat4.workers.position': 'Position',
  'chatVoice.channel.configuration.chat4.workers.zhuan':
    'An den menschlichen Agenten weiterleiten',
  'chatVoice.channel.configuration.chat4.workers.message':
    'Wenn Sie diese Option aktivieren, wird die Schaltfläche "An den menschlichen Agenten weiterleiten" automatisch unter der Antwort des Roboters angezeigt, wenn der Roboter die Antwort nicht kennt.',
  'chatVoice.channel.configuration.chat4.unknown':
    'Roboterantwort auf unbekannte Fragen',
  'chatVoice.channel.configuration.chat4.unknown.placeholder':
    'Bitte geben Sie die Roboterantwort auf unbekannte Fragen ein',
  'chatVoice.channel.configuration.chat4.unknown.message':
    'Diese Einstellung ist die Antwort des Roboters, wenn er auf unbekannte Fragen stößt',
  'chatVoice.channel.configuration.chat4.information.configuration.completed':
    'Sie haben die Einstellungen für den intelligenten Kundenservice abgeschlossen. Diese werden in der Chatbox implementiert, nachdem Sie sie gespeichert haben.',
  'chatVoice.channel.configuration.chat5.message':
    'Kopieren Sie den folgenden Code und fügen Sie ihn in den Inhalt des <body> </body>-Tags Ihrer Website ein.',
  'chatVoice.channel.configuration.chat5.message.link':
    'Chat-Link: Kopieren Sie den folgenden Link in den Code Ihrer Website.',
  'live.chatVoice.title': 'Chatbox-Vorschaubereich',
  'live.chatVoice.title.subtitle':
    'Hier können Sie eine Vorschau der Chatbox anzeigen',
  'live.chatVoice.customer': 'Kunde',
  'live.chatVoice.customer.Dialogue':
    'Können Sie mir etwas über die wichtigsten Funktionen des Produkts erzählen?',
  'live.chatVoice.submit': 'Senden',
  'live.chatVoice.end': 'Chat beenden',
  'live.chatVoice.video': 'Videokommunikation',
  'chatVoice.channel.configuration.cancel.btn': 'Abbrechen',
  'chatVoice.channel.configuration.next.btn': 'Weiter',
  'chatVoice.channel.configuration.complete.btn': 'Fertigstellen',
  'chatVoice.channel.configuration.title.knowledge_unknown_reply':
    'Mit meinen aktuellen Fähigkeiten kann ich Ihre Frage nicht beantworten. Bei Bedarf können Sie sich direkt an unsere Agentin wenden, um professionellere Unterstützung zu erhalten.',
  'chatVoice.channel.configuration.chat5.end':
    'Bitte beachten Sie: Nachdem Sie den obigen Code in Ihre Website integriert haben, kontaktieren Sie bitte den „ConnectNow“-Administrator, um die oben genannten Domains zur Whitelist hinzuzufügen. Erst nach Abschluss der Whitelist-Konfiguration wird die Chat-Komponente ordnungsgemäß angezeigt.',
  'chatVoice.channel.configuration.chat5.end.1': ' ',

  'chatVoice.channel.configuration.channel.name.web': 'Website-Domainname',
  'chatVoice.channel.configuration.channel.name.placeholder.web':
    'Bitte Website-Domainnamen eingeben',
  'live.chatVoice.customer.Dialogue.product':
    'Welches Produkt möchten Sie kennenlernen?',
  'chatVoice.channel.configuration.chat5.message.Settings':
    'Bereitstellungseinstellungen',
  'chatVoice.channel.configuration.channel.name.placeholder.error':
    'Es können nur Chinesisch, Groß- und Kleinbuchstaben, Zahlen, "-", "_" eingegeben werden',
  'chatVoice.channel.configuration.channel.chatBoxName.placeholder.error':
    'Es können nur Chinesisch, Groß- und Kleinbuchstaben und Leerzeichen eingegeben werden',
  'chatVoice.channel.configuration.chat1.document.placeholder.language':
    'Bitte erneut auswählen, wenn sich die Trigger-FAQ-Daten ändern',
  'chatVoice.channel.configuration.channel.website':
    'Das Format des Website-Domainnamens lautet wie folgt: www.connectnow.cn',
  'chatVoice.channel.configuration.channel.website.name.placeholder.error':
    'Bitte geben Sie einen regulären Website-Domainnamen ein',
  'chatVoice.channel.configuration.work.panels.checkbox.ccp':
    'Ob die Spracherkennung aktiviert werden soll',
  'chatVoice.channel.configuration.title.pop_welcome_msg':
    'Hallo, ich bin der intelligente Kundenservice von ConnectNow. Kann ich Ihnen behilflich sein?',
  'chatVoice.channel.configuration.chat4.workers.keyword.message':
    'Nachdem der Kunde dieses Schlüsselwort eingegeben hat, wird er zum menschlichen Kundendienst weitergeleitet',
  'chatVoice.channel.configuration.chat4.workers.keyword':
    'Schlüsselwort für die Weiterleitung an den menschlichen Agenten',
  'chatVoice.channel.configuration.chat4.document.placeholder.keyword':
    'Bitte geben Sie mindestens ein Schlüsselwort ein',
};
