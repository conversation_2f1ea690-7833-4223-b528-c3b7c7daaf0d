export default {
  'agentWorkloadReport.title': 'Agenten-Workload-Statistik',
  'agentWorkloadReport.title.admin': 'Agentengruppen-Workload-Statistik',
  'agentWorkloadReport.title.right': 'Wählen Sie Das Datum：',
  'agentWorkloadReport.card.1.title': 'Gesamtzahl Der Tickets',

  'agentWorkloadReport.card.title.tips.1':
    'Statistik der gesamtzahl aller tickets im angegebenen zeitraum',
  'agentWorkloadReport.card.title.tips.2':
    'Statistik der anzahl aller nicht abgeschlossenen tickets (einschließlich des status: in bearbeitung, nicht zugewiesen) im angegebenen zeitraum',
  'agentWorkloadReport.card.title.tips.3':
    'Führen sie eine tiefgreifende analyse basierend auf ticketdaten durch, um wertvolle informationen und trends zu ermitteln, einschließlich schlüsseldaten wie höchste, niedrigste, steigende und fallende ticketmengen',
  'agentWorkloadReport.card.title.tips.4':
    'Statistik der top 3 agenten mit der höchsten ticketanzahl im angegebenen zeitraum',
  'agentWorkloadReport.card.title.tips.5':
    'Statistik der top 3 agenten mit der niedrigsten ticketanzahl im angegebenen zeitraum',
  'agentWorkloadReport.card.title.tips.6':
    'Statistik der top 3 agenten mit dem größten anstieg der ticketanzahl im angegebenen zeitraum',
  'agentWorkloadReport.card.title.tips.7':
    'Statistik der top 3 agenten mit dem größten rückgang der ticketanzahl im angegebenen zeitraum',
  'agentWorkloadReport.card.title.tips.8':
    'Statistik der gesamtzahl der tickets jedes agenten im angegebenen zeitraum, einschließlich aller tickets in bearbeitung, gelöst, beendet und übertragen',
  'agentWorkloadReport.card.title.tips.9':
    'Zeigen sie den trend der ticketmenge jedes agenten zu unterschiedlichen zeiten an und zeigen sie gleichzeitig die trendänderungen von 5 agenten an',
  'agentWorkloadReport.card.title.tips.10':
    'Statistik der anzahl der tickets für jeden agenten in verschiedenen kanälen im angegebenen zeitraum',
  'agentWorkloadReport.card.title.tips.11':
    'Statistik der anzahl der tickets in verschiedenen status im angegebenen zeitraum',
  'agentWorkloadReport.card.title.tips.12':
    'Statistik der anzahl der tickets verschiedener typen im angegebenen zeitraum',
  'agentWorkloadReport.card.title.tips.13':
    'Statistik der anzahl der tickets mit unterschiedlichen prioritäten im angegebenen zeitraum',

  'agentWorkloadReport.card.1.tips': 'Im Vergleich Zu Zuvor',
  'agentWorkloadReport.card.2.tips': 'Tage',
  'agentWorkloadReport.card.2.title': 'Gesamtzahl Der Unvollendeten Tickets',
  'agentWorkloadReport.card.3.title': 'Data Insights',
  'agentWorkloadReport.card.4.title': 'Top 3 Der Bearbeiteten Tickets',
  'agentWorkloadReport.card.5.title': 'Bottom 3 Der Bearbeiteten Tickets',
  'agentWorkloadReport.card.6.title': 'Top 3 Des Ticketanstiegs',
  'agentWorkloadReport.card.7.title': 'Top 3 Des Ticketrückgangs',
  'agentWorkloadReport.card.table.1': 'Agentenname',
  'agentWorkloadReport.card.table.2': 'Anzahl Der Bearbeiteten Tickets',
  'agentWorkloadReport.card.table.3': 'Anzahl Der Ticketanstiege',
  'agentWorkloadReport.card.table.4': 'Anzahl Der Ticketrückgänge',
  'agentWorkloadReport.card.8.title': 'Gesamtzahl Der Tickets Pro Agent',
  'agentWorkloadReport.card.8.title.group':
    'Gesamtzahl Der Tickets Pro Agentengruppe',

  'agentWorkloadReport.card.9.title': 'Ticketmengentrend',
  'agentWorkloadReport.card.10.title': 'Verteilung Der Ticketquellen',
  'agentWorkloadReport.card.11.title': 'Verteilung Des Ticketstatus',
  'agentWorkloadReport.card.12.title': 'Verteilung Der Tickettypen',
  'agentWorkloadReport.card.13.title': 'Verteilung Der Ticketprioritäten',
  'agentWorkloadReport.card.select.1': 'Agentenname:',
  'agentWorkloadReport.card.select.1.placeholder':
    'Mehrere agenten können für den datenvergleich ausgewählt werden',
  'agentWorkloadReport.table.1.title': 'Gesamtzahl Der Tickets',
  'agentWorkloadReport.table.2.title': 'Zeit',
  'agent.work.report.data.insight':
    '在这段时间内，座席{highestName}处理的工单数量最高，共处理了{highestNumber}个工单，请鼓励继续保持；座席{minimumName}处理工单最低，共处理了{minimumNumber}个，请注意观察工作情况；',
  'agent.work.report.data.insight.shangsheng':
    '座席{riseHighestName}工单上升数量最高，上升了{riseHighestNumber}个工单，请鼓励继续保持；',
  'agent.work.report.data.insight.xiajiang':
    '座席{declineHighestName}下降数量最高，下降了{declineHighestNumber}个工单，请注意观察工作情况。',

  'agent.work.report.data.insight.admin':
    '在这段时间内，座席组{highestName}处理的工单数量最高，共处理了{highestNumber}个工单，请鼓励继续保持；座席组{minimumName}处理工单最低，共处理了{minimumNumber}个，请注意观察工作情况；',
  'agent.work.report.data.insight.admin.1':
    '座席组{riseHighestName}工单上升数量最高，上升了{riseHighestNumber}个工单，请鼓励继续保持；',
  'agent.work.report.data.insight.admin.2':
    '座席组{declineHighestName}下降数量最高，下降了{declineHighestNumber}个工单，请注意观察工作情况。',

  'agent.work.report.echarts.legend.1': 'Anzahl Der Gelösten Tickets',
  'agent.work.report.echarts.legend.2': 'Anzahl Der Ungelösten Tickets',
  'agent.work.report.echarts.legend.3':
    'Anzahl Der Tickets, Die Auf Kundenantwort Warten',

  'agent.work.machine.report.data.insight':
    '在这段时间内，智能机器人帮您接了{robotWorkTotalNumber}个工单，比人工工单{trend1}{robotContrastArtificialNumber}个工单；比起之前{daysBetween}天{trend2}了{robotContrastTimeNumber}个工单，继续努力。',
  'trend.1': 'Mehr',
  'trend.2': 'Weniger',
  'trend.3': 'Zunehmen',
  'trend.4': 'Abnehmen',
  'machineWorkloadReport.title': 'Roboter-Ticket-Statistik',
  'machineWorkloadReport.card.1.title': 'Gesamtzahl Der Roboter-Tickets',
  'machineWorkloadReport.card.2.title': 'Data Insights',
  'machineWorkloadReport.card.3.title':
    'Gesamtzahl Der Roboter-Tickets Pro Kanal',
  'machineWorkloadReport.card.4.title':
    'Anzahl Der Roboter-Tickets Nach Kanälen',
  'machineWorkloadReport.card.5.title': 'Roboter VS Mensch',
  'machineWorkloadReport.card.6.title': 'Anteil Der Roboter-Tickets',
  'machineWorkloadReport.card.1.title.tips':
    'Statistik der gesamtzahl der vom roboter bearbeiteten tickets im angegebenen zeitraum',
  'machineWorkloadReport.card.2.title.tips':
    'Führen sie eine tiefgreifende analyse basierend auf den von robotern bearbeiteten ticketdaten durch. erhalten sie vergleichsdaten zwischen robotern und menschen',
  'machineWorkloadReport.card.3.title.tips':
    'Statistik der gesamtzahl der von robotern in verschiedenen kanälen bearbeiteten tickets im angegebenen zeitraum und statistik des aufwärts- und abwärtstrends im vorherigen zeitraum',
  'machineWorkloadReport.card.4.title.tips':
    'Statistik der änderungen der anzahl der von robotern in verschiedenen kanälen bearbeiteten tickets im angegebenen zeitraum',
  'machineWorkloadReport.card.5.title.tips':
    'Statistik des vergleichs der anzahl der von robotern bearbeiteten tickets mit der anzahl der von menschen bearbeiteten tickets im angegebenen zeitraum',
  'machineWorkloadReport.card.6.title.tips':
    'Erfassen sie den anteil der vom roboter bearbeiteten ticketdaten im angegebenen zeitraum.',
  'machineWorkloadReport.table.date': 'Zeit',
  'machineWorkloadReport.table.WEB': 'WEB-Chat',
  'machineWorkloadReport.table.WhatsApp': 'WhatsApp-Chat',
  'machineWorkloadReport.table.APP': 'APP-Chat',
  'machineWorkloadReport.table.machine.work.order.num': 'Roboter-Ticket',
  'machineWorkloadReport.table.artificial.work.order.num': 'Manuelles Ticket',
  'machineWorkloadReport.channel.web': 'Web-Chat',
  'machineWorkloadReport.channel.app': 'App-Chat',
  'machineWorkloadReport.channel.phone': 'Telefon',
  'machineWorkloadReport.card.1.tips': 'Im Vergleich Zu Den Letzten 15 Tagen',
};
