export default {
  'add.whatsApp.channel.configuration.title': 'WhatsApp-Kanal hinzufügen',
  'add.whatsApp.channel.configuration.title.update': 'WhatsApp-Kanal ändern',
  'add.whatsApp.channel.configuration.tips':
    'Ermöglichen Sie es Kunden, das Unternehmen einfacher über WhatsApp Social Messages zu kontaktieren und schneller auf Kundenbedürfnisse zu reagieren.',

  'whatsApp.channel.configuration.title.1':
    'WhatsApp Business-Konto verknüpfen',
  'whatsApp.channel.configuration.title.tips.1':
    'Ermöglichen Sie es Kunden, das Unternehmen einfacher über WhatsApp Social Messages zu kontaktieren und schneller auf Kundenbedürfnisse zu reagieren.',
  'whatsApp.channel.configuration.title.2': 'WhatsApp-Telefon auswählen',
  'whatsApp.channel.configuration.title.2.please':
    'Bitte wählen Sie ein WhatsApp-Telefon aus',
  'whatsApp.channel.configuration.title.tips.2':
    'Bitte wählen Sie die Telefonnummer aus, die angezeigt werden soll, wenn Sie den Kunden später kontaktieren.',
  'whatsApp.channel.configuration.title.3': 'Kanalinformationen hinzufügen',
  'whatsApp.channel.configuration.title.tips.3':
    'Bitte geben Sie die Kanalinformationen ein',
  'whatsApp.channel.configuration.title.4':
    'Einstellungen für den intelligenten Kundenservice',
  'whatsApp.channel.configuration.title.tips.4':
    'Hier können Sie die relevanten Informationen des intelligenten Kundendienstes konfigurieren.',
  'whatsApp.channel.configuration.title.5': 'Bereitstellung',
  'whatsApp.channel.configuration.title.tips.5':
    'Bitte befolgen Sie die folgenden Anweisungen, um die Chatbox auf Ihrer Website bereitzustellen.',
  'whatsApp.channel.configuration.channel.name': 'Kanalname',
  'whatsApp.channel.configuration.channel.name.placeholder':
    'Bitte geben Sie den Kanalnamen ein',
  'whatsApp.channel.configuration.whatsApp.types': 'Sprache auswählen',
  'whatsApp.channel.configuration.whatsApp.types.placeholder':
    'Bitte wählen Sie eine Sprache aus',
  'whatsApp.channel.configuration.chat2.logo': 'Firmenlogo',
  'whatsApp.channel.configuration.chat2.logo.message1':
    'Es können nur jpg/png-Dateien hochgeladen werden, die 500 KB nicht überschreiten.',
  'whatsApp.channel.configuration.chat2.logo.message2':
    '会在聊天框左上角展示，建议上传50*20px的PNG图片',
  'whatsApp.channel.configuration.chat2.chatBoxName.placeholder':
    'Bitte geben Sie den Namen der Chatbox ein',
  'whatsApp.channel.configuration.chat2.chatBoxName': 'Chatbox-Name',
  'whatsApp.channel.configuration.chat2.chatBoxName.message':
    'Wird in der oberen linken Ecke der Chatbox angezeigt.',
  'whatsApp.channel.configuration.chat2.templete': 'Themenfarbe',
  'whatsApp.channel.configuration.chat2.templete.custom': 'Benutzerdefiniert',
  'whatsApp.channel.configuration.chat2.templete.color': 'Aktuelle Farbe:',
  'whatsApp.channel.configuration.chat2.templete.placeholder':
    'Bitte wählen Sie eine Farbe',
  'whatsApp.channel.configuration.chat2.boxColor':
    'Farbe der Kundenservice-Chatbox',
  'whatsApp.channel.configuration.chat2.userBox': 'Farbe der Benutzer-Chatbox',
  'whatsApp.channel.configuration.chat2.information.configuration.completed':
    'Sie haben das WhatsApp-Telefon erfolgreich verknüpft, die Telefonnummer lautet',
  'whatsApp.channel.configuration.work.panels.checkbox': 'Einschalten',
  'whatsApp.channel.configuration.chat3.form': 'Arbeitsintegrationspanel',
  'whatsApp.channel.configuration.chat3.form.message':
    'Es wird empfohlen, diese Option zu aktivieren. Nach dem Einschalten müssen Benutzer grundlegende Informationen eingeben, bevor sie weiter kommunizieren können.',
  'whatsApp.channel.configuration.chat3.welcome': 'Begrüßung',
  'whatsApp.channel.configuration.chat3.welcome.words': 'Ein-Satz-Begrüßung',
  'whatsApp.channel.configuration.chat3.welcome.words.placeholder':
    'Bitte geben Sie eine Begrüßung ein',
  'whatsApp.channel.configuration.chat3.welcome.words.message':
    'Bitte stellen Sie sicher, dass diese Einstellung mit der Sprache übereinstimmt, die Sie im ersten Schritt ausgewählt haben.',
  'whatsApp.channel.configuration.chat3.welcome.QA': 'FAQ auslösen',
  'whatsApp.channel.configuration.chat3.welcome.QA.placeholder':
    'Bitte wählen Sie eine FAQ zum Auslösen aus',
  'whatsApp.channel.configuration.chat3.interval.placeholder':
    'Bitte geben Sie die Zeit für die automatische Einladung zur Sitzung ein',
  'whatsApp.channel.configuration.chat3.welcome.QA.message':
    'Mit FAQs können Sie mehrere Antworten auf einmal an den Benutzer senden, nicht nur Text, sondern auch Bilder und Videos. Wenn Sie eine FAQ auswählen, antwortet das System automatisch mit der in der FAQ konfigurierten Standardantwort, wenn der Benutzer Sie kontaktiert. Wenn Sie noch keine FAQ eingerichtet haben, klicken Sie bitte auf',
  'whatsApp.channel.configuration.chat3.welcome.QA.message.1': 'Hier',
  'whatsApp.channel.configuration.chat3.welcome.QA.message.2':
    'um Einstellungen vorzunehmen.',
  'whatsApp.channel.configuration.chat3.talk':
    'Automatische Einladung zur Sitzung',
  'whatsApp.channel.configuration.chat3.talk.ge': 'Nach',
  'whatsApp.channel.configuration.chat3.talk.ge2':
    'Sekunden öffnet das System automatisch die Chatbox.',
  'whatsApp.channel.configuration.chat3.message':
    'Während der Benutzer die Website durchsucht, öffnet das System automatisch ein Popup-Fenster, um den Benutzer zur Beratung einzuladen.',
  'whatsApp.channel.configuration.chat3.talk.Input':
    'Begrüßung für automatische Einladung zur Sitzung',
  'whatsApp.channel.configuration.chat3.talk.Input.placeholder':
    'Bitte geben Sie eine Begrüßung für die automatische Einladung zur Sitzung ein',
  'whatsApp.channel.configuration.chat3.talk.Input.message':
    'Stellen Sie hier die Begrüßung ein, die angezeigt wird, wenn die automatische Sitzungseinladung angezeigt wird.',

  'whatsApp.channel.configuration.chat3.voice.message':
    'Benutzer können während der Kommunikation mit dem Kundendienst auf die Schaltfläche für die Videokommunikation unten klicken, um eine Sprachkommunikation durchzuführen.',
  'whatsApp.channel.configuration.chat3.voice': 'Online-Sprachkommunikation',
  'whatsApp.channel.configuration.chat3.video.message':
    'Benutzer können während der Kommunikation mit dem Kundendienst auf die Schaltfläche für die Videokommunikation unten klicken, um eine Videokommunikation durchzuführen.',
  'whatsApp.channel.configuration.chat3.video': 'Online-Videokommunikation',
  'whatsApp.channel.configuration.chat3.evaluate.message':
    'Nachdem der Benutzer den Chat beendet hat, öffnet das System automatisch eine Umfrage zur Zufriedenheit.',
  'whatsApp.channel.configuration.chat3.evaluate': 'Zufriedenheitsbewertung',
  'whatsApp.channel.configuration.chat3.information.configuration.completed':
    'Sie haben die Einrichtung der Grundfunktionen abgeschlossen, diese werden nach dem Speichern in der Chatbox implementiert.',
  'whatsApp.channel.configuration.chat4.mode.message':
    'Der intelligente Kundenservice wird zuerst vom Roboter beantwortet. Fragen, die der Roboter nicht beantworten kann, können jederzeit an einen menschlichen Agenten weitergeleitet werden / Nur manuell beantwortet, nur menschliche Agenten beantworten Fragen / Nur Roboter beantwortet, nur Roboter beantworten Fragen.',
  'whatsApp.channel.configuration.chat4.mode.message.1': `Wählen sie "nur agenten", nur menschliche agenten beantworten kundenfragen. `,
  'whatsApp.channel.configuration.chat4.mode.message.2': `Wählen sie "nur chatbot", nur roboter-kundendienst beantwortet kundenfragen. `,
  'whatsApp.channel.configuration.chat4.mode.1': 'Intelligenter Kundendienst',
  'whatsApp.channel.configuration.chat4.mode.2': 'Nur manuell',
  'whatsApp.channel.configuration.chat4.mode.3': 'Nur Roboter',
  'whatsApp.channel.configuration.chat4.mode': 'Kundenservice-Modus',
  'whatsApp.channel.configuration.chat4.robot.message':
    'Der Robotername wird über der Antwort des Roboters angezeigt.',
  'whatsApp.channel.configuration.chat4.robot.placeholder':
    'Bitte geben Sie den Roboternamen ein',
  'whatsApp.channel.configuration.chat4.robot': 'Robotername',
  'whatsApp.channel.configuration.chat4.language.message':
    'Nach dem Einschalten erkennt das System automatisch die Sprache der "Eingabefrage" des Benutzers; wenn es nicht eingeschaltet ist, ist die Sprache des Browsers des Benutzers maßgebend.',
  'whatsApp.channel.configuration.chat4.language':
    'Automatische Spracherkennung',
  'whatsApp.channel.configuration.chat4.document':
    'Dokumenten-Wissensdatenbank',
  'whatsApp.channel.configuration.chat4.document.placeholder':
    'Bitte wählen Sie eine Dokumenten-Wissensdatenbank aus',
  'whatsApp.channel.configuration.chat4.document.message.1':
    'Die Dokumenten-Wissensdatenbank hier zeigt nur externe Wissensdatenbanken an, bitte gehen Sie im Voraus zu',
  'whatsApp.channel.configuration.chat4.document.message':
    'Dokumenten-Wissensdatenbank',
  'whatsApp.channel.configuration.chat4.document.message.2':
    'Seite, um die Wissensdatenbank zu konfigurieren.',
  'whatsApp.channel.configuration.chat4.ai.message':
    'Sie können konfigurieren, ob eine Verbindung zu generativer KI hergestellt werden soll.',
  'whatsApp.channel.configuration.chat4.ai':
    'Verbindung zu generativer KI herstellen',
  'whatsApp.channel.configuration.chat4.workers':
    'Unbekannte Antwort, Schaltfläche zum Agent wechsel',
  'whatsApp.channel.configuration.chat4.workers.content':
    'Entschuldigung, ich kann diese Frage nicht beantworten, bitte wechseln Sie zum manuellen Gespräch .',
  'whatsApp.channel.configuration.chat4.workers.position': 'Position',
  'whatsApp.channel.configuration.chat4.workers.zhuan': 'Zum Agent wechseln',
  'whatsApp.channel.configuration.chat4.workers.message':
    'Wenn diese Option aktiviert ist, wird die Schaltfläche "Zum Agent wechseln" automatisch unter der Antwort des Roboters angezeigt, wenn der Roboter die Antwort nicht kennt.',
  'whatsApp.channel.configuration.chat4.unknown':
    'Antwort des Roboters auf unbekannte Fragen',
  'whatsApp.channel.configuration.chat4.unknown.placeholder':
    'Bitte geben Sie die Antwort des Roboters auf unbekannte Fragen ein',
  'whatsApp.channel.configuration.chat4.unknown.message':
    'Diese Einstellung ist die Antwort des Roboters, wenn er auf eine unbekannte Frage stößt.',
  'whatsApp.channel.configuration.chat4.information.configuration.completed':
    'Sie haben die Einrichtung des intelligenten Kundenservice abgeschlossen, diese werden nach dem Speichern implementiert.',
  'whatsApp.channel.configuration.chat5.message':
    'Kopieren Sie den folgenden Code und fügen Sie ihn in den Inhalt des <body> </body>-Tags Ihrer Website ein.',
  'whatsApp.channel.configuration.chat5.message.link':
    'Chat-Link: Kopieren Sie den folgenden Link in den Code Ihrer Website.',
  'live.whatsApp.title': 'Chatbox-Vorschaubereich',
  'live.whatsApp.title.subtitle':
    'Hier können Sie eine Vorschau der Chatbox anzeigen.',
  'live.whatsApp.customer': 'Kunde',
  'live.whatsApp.customer.Dialogue':
    'Können Sie mir etwas über die wichtigsten Funktionen des Produkts erzählen?',
  'live.whatsApp.submit': 'Absenden',
  'live.whatsApp.end': 'Chat beenden',
  'live.whatsApp.video': 'Videokommunikation',
  'whatsApp.channel.configuration.cancel.btn': 'Abbrechen',
  'whatsApp.channel.configuration.next.btn': 'Weiter',
  'whatsApp.channel.configuration.complete.btn': 'Fertigstellen',
  'whatsApp.channel.configuration.title.knowledge_unknown_reply':
    'Mit meinen aktuellen Fähigkeiten kann ich Ihre Frage nicht beantworten. Bei Bedarf können Sie sich direkt an unsere Agenten wenden, um professionellere Unterstützung zu erhalten.',
  'whatsApp.channel.configuration.chat5.end':
    'Bitte beachten Sie: Nachdem Sie den obigen Code in Ihre Website integriert haben, kontaktieren Sie bitte den „ConnectNow“-Administrator, um die oben genannten Domains zur Whitelist hinzuzufügen. Erst nach Abschluss der Whitelist-Konfiguration wird die Chat-Komponente ordnungsgemäß angezeigt.',
  'whatsApp.channel.configuration.chat5.end.1': ' ',

  'whatsApp.channel.configuration.channel.name.web': 'Website-Domain',
  'whatsApp.channel.configuration.channel.name.placeholder.web':
    'Bitte geben Sie die Website-Domain ein',
  'live.whatsApp.customer.Dialogue.product':
    'Über welches Produkt möchten Sie mehr erfahren?',
  'whatsApp.channel.configuration.chat5.message.Settings':
    'Bereitstellungseinstellungen',
  'whatsApp.channel.configuration.channel.name.placeholder.error':
    'Es können nur Chinesisch, Groß- und Kleinbuchstaben, Zahlen, "-", "_" eingegeben werden.',
  'whatsApp.channel.configuration.channel.chatBoxName.placeholder.error':
    'Es können nur Chinesisch, Groß- und Kleinbuchstaben und Leerzeichen eingegeben werden.',
  'whatsApp.channel.configuration.chat1.document.placeholder.language':
    'Bitte wählen Sie erneut, wenn sich die Auslöser-FAQ-Daten ändern',
  'whatsApp.channel.configuration.channel.website':
    'Das Format der Website-Domain lautet wie folgt: www.connectnow.cn',
  'whatsApp.channel.configuration.channel.website.name.placeholder.error':
    'Bitte geben Sie eine gültige Website-Domain ein',
  'whatsApp.channel.configuration.work.panels.checkbox.ccp':
    'Ermöglicht die automatische Identifizierung der vom Kunden verwendeten Sprache, um das Kundenerlebnis weiter zu verbessern.',
  'whatsApp.channel.configuration.title.pop_welcome_msg':
    'Hallo, ich bin der intelligente Kundenservice von ConnectNow. Wie kann ich Ihnen helfen?',
  'whatsApp.channel.configuration.chat4.workers.keyword.message':
    'Der Kunde wird zum menschlichen Kundendienst weitergeleitet, nachdem er dieses Schlüsselwort eingegeben hat.',
  'whatsApp.channel.configuration.chat4.workers.keyword':
    'Schlüsselwort für "Zum Agent wechseln"',
  'whatsApp.channel.configuration.chat4.document.placeholder.keyword':
    'Bitte geben Sie mindestens ein Schlüsselwort ein',
  'whatsApp.channel.configuration.chat1.information.configuration.completed':
    'Sie haben Ihr WhatsApp Business-Konto erfolgreich verknüpft. Bitte wählen Sie weiterhin ein WhatsApp-Telefon aus.',
  'whatsApp.channel.configuration.content1.btn': 'WhatsApp-Konto verknüpfen',
  'whatsApp.channel.configuration.chat1.document.placeholder.whatsApp':
    'Bitte verknüpfen Sie zuerst ein WhatsApp-Konto',
};
