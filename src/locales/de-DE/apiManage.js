export default {
  'ai.agent.api.modal.title': 'Api-verwaltung',
  'ai.agent.api.table.operation.add': 'Hinz<PERSON><PERSON><PERSON>',
  'ai.agent.api.table.key.placeholder': 'Bitte key eingeben',
  'ai.agent.api.table.value.placeholder':
    'Bitte den entsprechenden wert eingeben',
  'ai.agent.api.table.description.placeholder': 'Bitte beschreibung eingeben',
  'ai.agent.api.table.description.title': 'Beschreibung',
  'ai.agent.api.table.operation.title': 'Bedienung',
  'ai.agent.api.search.placeholder': 'Suche',
  'ai.agent.api.operation.add': 'Api-schnittstelle hinzufügen',
  'ai.agent.api.table.empty.text':
    'Sie haben noch keine api-schnittstelle hinzugefügt',
  'ai.agent.api.url.placeholder': 'Api-schnittstellenadresse eingeben',
  'ai.agent.api.operation.test.btn': 'Test',
  'ai.agent.api.operation.save.btn': 'Speichern',
  'ai.agent.api.response.title': 'Testergebnisse',
  'ai.agent.script.settings.not.empty.tips':
    'Bitte speichern Sie die derzeit bearbeitete API',
  'ai.agent.api.url.placeholder.tips':
    'Bitte geben Sie die API-Schnittstellenadresse ein',
  'ai.agent.api.authentication.open': 'API-Authentifizierung aktivieren',
  'ai.agent.api.authentication.type': 'API-Authentifizierungstyp',
  'ai.agent.api.authentication.header.placeholder':
    'Bitte geben Sie den Header ein',
  'ai.agent.api.authentication.apiKey.placeholder':
    'Bitte geben Sie das API Key ein',
  'ai.agent.api.authentication.basic': 'Basic',
  'ai.agent.api.authentication.bearer': 'Bearer',
  'ai.agent.api.authentication.custom': 'Custom',
};
