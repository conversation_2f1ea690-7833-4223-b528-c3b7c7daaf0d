export default {
  // 评估报告列表
  'self.assessment.details.select.knowledge': 'Knowledge base auswählen:',
  'self.assessment.details.select.knowledge.placeholder':
    'Bitte knowledge base auswählen',
  'self.assessment.details.notification.channels': 'Quellkanal:',
  'self.assessment.details.notification.channels.placeholder':
    'Bitte quellkanal auswählen',
  'self.assessment.details.ask.customer': 'Kunden fragen:',
  'self.assessment.details.ask.customer.placeholder':
    'Bitte kundenname, kunden-e-mail-adresse oder kunden-telefonnummer eingeben',
  'self.assessment.details.ask.customer.placeholder1':
    'Bitte kunden-e-mail-adresse eingeben',
  'self.assessment.details.ask.customer.placeholder.phone':
    'Bitte kunden-telefonnummer eingeben',
  'self.assessment.details.customer.issues': 'Kundenfrage:',
  'self.assessment.details.customer.issues.placeholder':
    'Bitte die gestellte frage eingeben',
  'self.assessment.details.question.time': 'Fragezeit:',
  'self.assessment.details.title': 'Assessment-bericht-liste',
  'self.assessment.details.table.assessment.type': 'Assessment-typ',
  'self.assessment.details.table.manual.evaluation': '<PERSON>le assessment',
  'self.assessment.details.table.automatic.evaluation':
    'Automatische assessment',
  'self.assessment.details.table.customer.issues': 'Kundenfrage',
  'self.assessment.details.table.answer.obtained': 'Erhaltene antwort',
  'self.assessment.details.table.average.accuracy.score':
    'Durchschnittliche genauigkeitsbewertung',
  'self.assessment.details.table.loyalty.score': 'Faktentreue-bewertung',
  'self.assessment.details.table.answer.relevance.score':
    'Antwortrelevanz-bewertung',
  'self.assessment.details.table.context.accuracy.score':
    'Kontextgenauigkeitsbewertung',
  'self.assessment.details.table.semantic.similarity.score':
    'Antwort semantische ähnlichkeitsbewertung',
  'self.assessment.details.table.correct.answer.score':
    'Antwortrichtigkeitsbewertung',
  'self.assessment.details.table.ask.customer': 'Fragender kunde',
  'self.assessment.details.table.interaction.time': 'Interaktionszeit',
  'self.assessment.details.table.knowledge.name': 'Knowledge base name',

  'self.assessment.details.table.loyalty.score.tips.text':
    '这衡量生成答案与给定上下文的事实一致性。它是从答案和检索到的上下文中计算出来的。答案的评分范围在(0,100)之间，分数越高越好。',
  'self.assessment.details.table.loyalty.score.tips.text.1':
    'Eine antwort gilt als treu, wenn alle aussagen in der generierten antwort aus dem gegebenen kontext abgeleitet werden können.',
  'self.assessment.details.table.answer.relevance.score.tips.text':
    'Die metrik „antwortrelevanz“ konzentriert sich auf die bewertung des grades der relevanz der generierten antwort in bezug auf die gegebene aufforderung. Antworten, die unvollständig sind oder redundante informationen enthalten, werden niedriger bewertet; höhere bewertungen zeigen eine bessere relevanz an. Diese metrik wird basierend auf der frage, dem kontext und der antwort berechnet.',
  'self.assessment.details.table.context.accuracy.score.tips.text':
    'Die kontextgenauigkeit ist eine metrik, die bewertet, ob alle wirklich relevanten elemente im kontext höher eingestuft werden. Im idealfall müssen alle relevanten blöcke oben in der liste erscheinen. Diese metrik wird basierend auf der frage, der richtigen antwort und dem kontext berechnet und liegt im bereich von 0 bis 100, wobei höhere werte eine höhere genauigkeit anzeigen.',
  'self.assessment.details.table.semantic.similarity.score.tips.text':
    'Das konzept der semantischen ähnlichkeit von antworten beinhaltet die bewertung der semantischen ähnlichkeit zwischen der generierten antwort und der richtigen antwort. Diese bewertung basiert auf der richtigen antwort und der generierten antwort und liegt im bereich von 0 bis 100. Je höher der wert, desto besser stimmt die generierte antwort mit der richtigen antwort überein.',
  'self.assessment.details.table.correct.answer.score.tips.text':
    'Die bewertung der antwortrichtigkeit beinhaltet den vergleich der generierten antwort mit der richtigen antwort, um deren genauigkeit zu messen. Diese bewertung hängt von der richtigen antwort und der generierten antwort ab und wird auf einer skala von 0 bis 100 bewertet. Je höher der wert, desto besser stimmt die generierte antwort mit der richtigen antwort überein, was auf eine bessere richtigkeit hindeutet.',
  'self.assessment.details.table.correct.answer.score.tips.text.1':
    'Die antwortrichtigkeit umfasst zwei schlüsselaspekte: die semantische ähnlichkeit zwischen der generierten antwort und der richtigen antwort sowie die faktische ähnlichkeit. Diese aspekte werden durch ein gewichtungsschema kombiniert, um die antwortrichtigkeit zu ermitteln.',

  // 评估报告详情
  'self.assessment.details.title.left': 'Frage',
  'self.assessment.details.title.right': 'Assessment-bewertung',
  'self.assessment.details.evaluation.time': 'Assessment-zeit:',
  'self.assessment.details.comparison.answers': 'Antwortvergleich',
  'self.assessment.details.document.fragments':
    'Dokumentfragmente zurückverfolgen',
  'self.assessment.details.robot.answer': 'Vom roboter gegebene antwort',
  'self.assessment.details.correct.answer': 'Richtige antwort',
  'self.assessment.details.add.faq': 'Zu FAQ hinzufügen',
  'self.assessment.details.document.knowledge': 'AIGC knowledge base',
  'self.assessment.details.input.correct.answer':
    'Manuelle eingabe der richtigen antwort',
  'self.assessment.details.create.correct.answer.text':
    'Nachdem sie die richtige antwort manuell eingegeben haben, kann das system die werte für "kontextgenauigkeit", "antwort semantische ähnlichkeit" und "antwortrichtigkeit" für sie bewerten.',
  'self.assessment.details.input.correct.answer.placeholder':
    'Bitte geben sie die richtige antwort ein',
  'self.assessment.details.start.evaluating': 'Bewertung starten',
  'self.assessment.details.cancel.evaluation': 'Bewertung abbrechen',
  'self.assessment.details.loyalty': 'Faktentreue',
  'self.assessment.details.answer.relevance': 'Antwortrelevanz',
  'self.assessment.details.context.accuracy': 'Kontextgenauigkeit',
  'self.assessment.details.semantic.similarity.answers':
    'Semantische ähnlichkeit der antworten',
  'self.assessment.details.correct.answer.radar': 'Antwortrichtigkeit',
  'self.assessment.details.handle.input.text':
    'Nachdem sie die richtige antwort unten manuell eingegeben haben, kann das system die werte für „kontextgenauigkeit“, „antwort semantische ähnlichkeit“ und „antwortrichtigkeit“ für sie bewerten.',
  'self.assessment.details.title.right.evaluation.status': 'Bewertung läuft...',

  // 智能客服对话洞察
  'self.assessment.details.interactivity.type': 'Interaktionstyp:',
  'customer.service.conversation.insights.answer.type.select': 'Antworttyp:',
  'customer.service.conversation.insights.answer.type.select.placeholder':
    'nan',
  'customer.service.conversation.insights.like': 'Like',
  'customer.service.conversation.insights.downvote': 'Downvote',
  'self.assessment.details.table.interactivity.type': 'Interaktionstyp',
  'self.assessment.details.document.fragments.faq': 'FAQ-antwort',
  'self.assessment.details.document.fragments.answer': 'Antwort',
  'customer.service.conversation.insights.no.data.tips':
    'Es fehlt an relevantem wissen, bitte pflegen sie die knowledge base',
  'customer.service.conversation.insights.no.data.tips.1':
    'Sie haben derzeit keine dateninformationen...',
  'customer.service.conversation.insights.title':
    'Intelligent customer service conversation insights',
  'customer.service.conversation.insights.from.faq':
    'From frage-antwort-knowledge base',
  'customer.service.conversation.insights.input.correct.answer':
    'Richtige antwort eingeben',
  'customer.service.conversation.insights.answer.type': 'Antworttyp',
  'customer.service.conversation.insights.table.knowledge.QA':
    'FAQ normal answer',
  'customer.service.conversation.insights.table.lack.knowledge':
    'RAG lack of knowledge',
  'customer.service.conversation.insights.table.ai.dont.know':
    "RAG doesn't know",
  'customer.service.conversation.insights.table.aigc.answer':
    'RAG normal answer',
  'customer.service.conversation.insights.table.aigc.answer.aigc':
    'AIGC normal answer',
  'customer.service.conversation.insights.title.right': 'Assessment accuracy',
  'customer.service.conversation.insights.detail.answer.null':
    'Antwort wurde nicht abgerufen',
  'self.assessment.details.title.problem.rewriting': 'Frage umschreiben:',

  // 智能客服热点问题分析
  'customer.service.hot.topic.analysis.title':
    'Intelligent customer service hot topic analysis',
  'customer.service.hot.topic.analysis.top.20': 'Wortwolke Top20',
  'customer.service.hot.topic.analysis.top.100': 'Wortwolke Top100',
  'customer.service.hot.topic.analysis.search.tips': 'Wortwolke suchen',
  'customer.service.hot.topic.analysis.table.question.tag': 'Wortwolke',
  'customer.service.hot.topic.analysis.top.10':
    'Die 10 am häufigsten gestellten fragen (Top 10)',
  'customer.service.hot.topic.analysis.table.ranking': 'Rangfolge',
  'customer.service.hot.topic.analysis.table.ask.questions': 'Gestellte fragen',
  'customer.service.hot.topic.analysis.table.ask.number': 'Anzahl der fragen',
  'customer.service.hot.topic.analysis.table.like.number': 'Anzahl der likes',
  'customer.service.hot.topic.analysis.table.dislike.number':
    'Anzahl der dislikes',
  'customer.service.hot.topic.analysis.table.recent.question.time':
    'Letzte fragezeit',
  'customer.service.hot.topic.analysis.top.20.tips':
    'Zeigt die 20 am häufigsten gestellten wortwolke an. Anhand dieser tags können sie schnell die hauptproblembereiche identifizieren, die die benutzer am meisten interessieren.',
  'customer.service.hot.topic.analysis.top.100.tops':
    'Zeigt die 100 am häufigsten gestellten wortwolke an. Durch klicken auf die tags können detaillierte daten zu fragen unter den tags abgerufen werden. Das verständnis dieser tags hilft, probleme zu identifizieren, auf die sich benutzer konzentrieren, und bietet datenunterstützung für die optimierung der knowledge base und die verbesserung des kundendienstes.',
  'customer.service.hot.topic.analysis.top.10.tips':
    'Die 10 fragen, die von benutzern am häufigsten gestellt werden. Das häufige auftreten dieser fragen zeigt, dass sie die inhalte sind, die die benutzer am meisten interessieren.',

  // 缺乏知识问题分析
  'lack.knowledge.problem.analysis.title':
    'Analyse von problemen mit fehlendem wissen',
  'lack.knowledge.problem.analysis.top.20':
    'Top 20 der wortwolke für fehlendes wissen',
  'lack.knowledge.problem.analysis.top.100':
    'Top 100 der wortwolke für fehlendes wissen',
  'lack.knowledge.problem.analysis.bottom.title':
    'Fragen, die mit „fehlendes wissen“ nicht beantwortet wurden',
  'lack.knowledge.problem.analysis.top.20.tips':
    'Zeigt die top 20 wortwolke an, die von kunden nach der frage aufgrund von "fehlendem wissen" mit "weiß nicht" beantwortet wurden. Diese tags stehen für problembereiche, auf die benutzer häufig stoßen und die dringend ergänzt und verbessert werden müssen.',
  'lack.knowledge.problem.analysis.top.100.tips':
    'Zeigt die top 100 wortwolke an, die kunden nach der frage aufgrund von "fehlendem wissen" mit "weiß nicht" beantwortet haben. Das verständnis dieser tags hilft, probleme zu identifizieren, auf die sich benutzer konzentrieren, und bietet datenunterstützung für die optimierung der knowledge base und die verbesserung des kundendienstes.',
  'lack.knowledge.problem.analysis.bottom.title.tips':
    'Zeigt alle fragen an, die aufgrund von "fehlendem wissen" mit "weiß nicht" beantwortet werden',
};
