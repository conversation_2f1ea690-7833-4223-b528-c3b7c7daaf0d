import customerInformation from './zh-CN/customerInformation';
import selfConfiguration from './zh-CN/selfConfiguration';
import piecemealWhatsApp from './zh-CN/piecemealWhatsApp';
import customerDataGroupManagement from './zh-CN/customerDataGroupManagement';
import auth from './zh-CN/auth';
import sendMsg from '@/locales/zh-CN/sendMsg';
import awsAccountSetting from './zh-CN/awsAccountSetting';
import customerList from './zh-CN/customerList';
import crmWorkRecord from './zh-CN/crmWorkRecord';
import channel from './zh-CN/channel';
import worktable from './zh-CN/worktable';
import userManagement from '@/locales/zh-CN/userManagement';
import agentManagement from '@/locales/zh-CN/agentManagment';
import knowledgeQA from '@/locales/zh-CN/knowledgeQA';
import personal from '@/locales/zh-CN/personal';
import statistics from '@/locales/zh-CN/statistics';
import cloudContactCenter from '@/locales/zh-CN/cloudContactCenter';
import home from '@/locales/zh-CN/home';
import contactCustomers from '@/locales/zh-CN/contactCustomers';
// import homePage from '@/locales/zh-CN/homePage';
import agentIMChat from './zh-CN/agentIMChat';
import customerExtensionInformation from './zh-CN/customerExtensionInformation';
import aigc from './zh-CN/AIGC';
import workOrderManagement from './zh-CN/workOrderManagement';
import homePage from './zh-CN/homePage';
import documentKnowledgeBase from './zh-CN/documentKnowledgeBase';
import definitionSynonyms from './zh-CN/definitionSynonyms';
import emailChannelConfiguration from './zh-CN/emailChannelConfiguration';
import chatChannelConfiguration from './zh-CN/chatChannelConfiguration';
import chatVoiceChannelConfiguration from './zh-CN/chatVoiceChannelConfiguration';

import emailMarketing from './zh-CN/emailMarketing';
import amazonRegionConfiguration from './zh-CN/amazonRegionConfiguration';
import faceBookConfiguration from './zh-CN/faceBookConfiguration';
import weChatConfiguration from './zh-CN/weChatConfiguration';
import feedbackPerformance from './zh-CN/feedbackPerformance';
import instagramConfiguration from './zh-CN/instagramConfiguration';
import googlePlayConfiguration from './zh-CN/googlePlayConfiguration';

import whatsAppChannelConfiguration from './zh-CN/whatsAppChannelConfiguration';
import agentWorkloadReport from './zh-CN/agentWorkloadReport';
import beginnerGuidance from './zh-CN/beginnerGuidance';
import marketingActivities from './zh-CN/marketingActivities';
import marketingResults from './zh-CN/marketingResults';
import selfAssessmentDetails from './zh-CN/selfAssessmentDetails';
import tagManagement from './zh-CN/tagManagement';
import alloctionRule from './zh-CN/alloctionRule';
import site from './zh-CN/site';
import AIAgent from './zh-CN/AIAgent';
import intentionManagement from './zh-CN/intentionManagement';
import hotlineKeyIndicators from './zh-CN/hotlineKeyIndicators';
import hotlineKeyIndicatorsConfig from './zh-CN/hotlineKeyIndicatorsConfig';
import meteringBilling from './zh-CN/meteringBilling';
import manageAgentStatus from './zh-CN/manageAgentStatus';
import apiManage from './zh-CN/apiManage';
import orderAmazon from './zh-CN/orderAmazon';
import footer from './zh-CN/footer';
import finance from './zh-CN/finance';
import header from './zh-CN/header';
import homePageNew from './zh-CN/homePageNew';
import productChannel from './zh-CN/productChannel';
import retail from './zh-CN/retail';
import manufacture from './zh-CN/manufacture';
import electronics from './zh-CN/electronics';
import newEnergy from './zh-CN/newEnergy';
import partner from './zh-CN/partner';
import resources from './zh-CN/resources';
import callCenter from './zh-CN/callCenter';
import productAiAgent from './zh-CN/productAiAgent';
import productAssistant from './zh-CN/productAssistant';
import dataReport from './zh-CN/dataReport';
import aigcCustomerService from './zh-CN/aigcCustomerService';
import marketing from './zh-CN/marketing';
import smartWorkOrder from './zh-CN/smartWorkOrder';
import videoCustomerService from './zh-CN/videoCustomerService';
import voiceRobot from './zh-CN/voiceRobot';
import settingTicket from './zh-CN/settingTicket';
import aiAgentLibrary from './zh-CN/aiAgentLibrary';
import privacyPolicy from './zh-CN/privacyPolicy';
import cookiePolicy from './zh-CN/cookiePolicy';
import inactiveMessageReminder from './zh-CN/inactiveMessageReminder';
import productComplianceGuide from './zh-CN/productComplianceGuide';
import intelligentFormFilling from './zh-CN/intelligentFormFilling';
import joinUs from './zh-CN/joinUs';
import cookie from './zh-CN/cookie';
import userTerms from './zh-CN/userTerms';
import euAiActCompliance from './zh-CN/euAiActCompliance';
import gdprCompliance from './zh-CN/gdprCompliance';
import workerOffers from './zh-CN/workerOffers';
import smartQualityInspection from './zh-CN/smartQualityInspection';
import homePageMobile from './zh-CN/homePageMobile';
import marketingAgent from './zh-CN/marketingAgent';
import helpDocument from './zh-CN/helpDocument';
import shareComponents from './zh-CN/shareComponents';
export default {
  ...workerOffers,
  ...orderAmazon,
  ...piecemealWhatsApp,
  ...AIAgent,
  ...customerInformation,
  ...chatVoiceChannelConfiguration,
  ...googlePlayConfiguration,
  ...customerDataGroupManagement,
  ...agentWorkloadReport,
  ...auth,
  ...amazonRegionConfiguration,
  ...weChatConfiguration,
  ...instagramConfiguration,
  ...statistics,
  ...agentManagement,
  ...sendMsg,
  ...feedbackPerformance,
  ...home,
  ...emailMarketing,
  ...cloudContactCenter,
  ...knowledgeQA,
  ...aigc,
  ...chatChannelConfiguration,
  ...selfConfiguration,
  ...whatsAppChannelConfiguration,
  ...faceBookConfiguration,
  // ...homePage,
  ...awsAccountSetting,
  ...customerList,
  ...crmWorkRecord,
  ...channel,
  ...worktable,
  ...userManagement,
  ...personal,
  ...customerExtensionInformation,
  ...workOrderManagement,
  ...homePage,
  ...contactCustomers,
  ...documentKnowledgeBase,
  ...definitionSynonyms,
  ...emailChannelConfiguration,
  ...beginnerGuidance,
  ...marketingActivities,
  ...marketingResults,
  ...agentIMChat,
  ...selfAssessmentDetails,
  ...tagManagement,
  ...alloctionRule,
  ...site,
  ...intentionManagement,
  ...hotlineKeyIndicators,
  ...hotlineKeyIndicatorsConfig,
  ...meteringBilling,
  ...manageAgentStatus,
  ...apiManage,
  ...footer,
  ...finance,
  ...header,
  ...homePageNew,
  ...productChannel,
  ...retail,
  ...manufacture,
  ...electronics,
  ...newEnergy,
  ...partner,
  ...resources,
  ...callCenter,
  ...productAiAgent,
  ...productAssistant,
  ...dataReport,
  ...aigcCustomerService,
  ...marketing,
  ...smartWorkOrder,
  ...videoCustomerService,
  ...voiceRobot,
  ...settingTicket,
  ...aiAgentLibrary,
  ...privacyPolicy,
  ...cookiePolicy,
  ...inactiveMessageReminder,
  ...productComplianceGuide,
  ...joinUs,
  ...intelligentFormFilling,
  ...cookie,
  ...userTerms,
  ...euAiActCompliance,
  ...gdprCompliance,
  ...smartQualityInspection,
  ...homePageMobile,
  ...marketingAgent,
  ...helpDocument,
  ...shareComponents,
};
