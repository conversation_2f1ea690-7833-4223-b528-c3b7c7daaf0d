import customerInformation from './id-ID/customerInformation';
import selfConfiguration from './id-ID/selfConfiguration';
import piecemealWhatsApp from './id-ID/piecemealWhatsApp';
import orderAmazon from './id-ID/orderAmazon';
import customerDataGroupManagement from './id-ID/customerDataGroupManagement';
import auth from './id-ID/auth';
import sendMsg from '@/locales/id-ID/sendMsg';
import awsAccountSetting from './id-ID/awsAccountSetting';
import customerList from './id-ID/customerList';
import crmWorkRecord from './id-ID/crmWorkRecord';
import channel from './id-ID/channel';
import worktable from './id-ID/worktable';
import userManagement from '@/locales/id-ID/userManagement';
import agentManagement from '@/locales/id-ID/agentManagment';
import knowledgeQA from '@/locales/id-ID/knowledgeQA';
import statistics from '@/locales/id-ID/statistics';
import personal from '@/locales/id-ID/personal';
import home from '@/locales/id-ID/home';
import emailChannelConfiguration from './id-ID/emailChannelConfiguration';
import agentIMChat from './id-ID/agentIMChat';
import feedbackPerformance from './id-ID/feedbackPerformance';
import faceBookConfiguration from './id-ID/faceBookConfiguration';
import weChatConfiguration from './id-ID/weChatConfiguration';
import instagramConfiguration from './id-ID/instagramConfiguration';
import chatVoiceChannelConfiguration from './id-ID/chatVoiceChannelConfiguration';
// import homePage from '@/locales/id-ID/homePage';
import customerExtensionInformation from './id-ID/customerExtensionInformation';
import workOrderManagement from './id-ID/workOrderManagement';
import cloudContactCenter from './id-ID/cloudContactCenter';
import aigc from './id-ID/AIGC';
import homePage from './id-ID/homePage';
import contactCustomers from './id-ID/contactCustomers';
import documentKnowledgeBase from './id-ID/documentKnowledgeBase';
import definitionSynonyms from './id-ID/definitionSynonyms';
import chatChannelConfiguration from './id-ID/chatChannelConfiguration';
import whatsAppChannelConfiguration from './id-ID/whatsAppChannelConfiguration';
import beginnerGuidance from './id-ID/beginnerGuidance';
import marketingActivities from './id-ID/marketingActivities';
import marketingResults from './id-ID/marketingResults';
import emailMarketing from './id-ID/emailMarketing';
import agentWorkloadReport from './id-ID/agentWorkloadReport';
import selfAssessmentDetails from './id-ID/selfAssessmentDetails';
import amazonRegionConfiguration from './id-ID/amazonRegionConfiguration';
import googlePlayConfiguration from './id-ID/googlePlayConfiguration';
import tagManagement from './id-ID/tagManagement';
import alloctionRule from './id-ID/alloctionRule';
import site from './id-ID/site';
import hotlineKeyIndicatorsConfig from './id-ID/hotlineKeyIndicatorsConfig';
import hotlineKeyIndicators from './id-ID/hotlineKeyIndicators';

import meteringBilling from './id-ID/meteringBilling';
import finance from './id-ID/finance';
import AIAgent from './id-ID/AIAgent';
import intentionManagement from './id-ID/intentionManagement';
import manageAgentStatus from './id-ID/manageAgentStatus';
import apiManage from './id-ID/apiManage';
import header from './id-ID/header';
import footer from './id-ID/footer';
import homePageNew from './id-ID/homePageNew';
import productChannel from './id-ID/productChannel';
import retail from './id-ID/retail';
import manufacture from './id-ID/manufacture';
import electronics from './id-ID/electronics';
import newEnergy from './id-ID/newEnergy';
import partner from './id-ID/partner';
import resources from './id-ID/resources';
import callCenter from './id-ID/callCenter';
import productAiAgent from './id-ID/productAiAgent';
import productAssistant from './id-ID/productAssistant';
import dataReport from './id-ID/dataReport';
import aigcCustomerService from './id-ID/aigcCustomerService';
import marketing from './id-ID/marketing';
import smartWorkOrder from './id-ID/smartWorkOrder';
import videoCustomerService from './id-ID/videoCustomerService';
import voiceRobot from './id-ID/voiceRobot';
import settingTicket from './id-ID/settingTicket';
import aiAgentLibrary from './id-ID/aiAgentLibrary';
import privacyPolicy from './id-ID/privacyPolicy';
import cookiePolicy from './id-ID/cookiePolicy';
import inactiveMessageReminder from './id-ID/inactiveMessageReminder';
import productComplianceGuide from './id-ID/productComplianceGuide';
import intelligentFormFilling from './id-ID/intelligentFormFilling';
import joinUs from './id-ID/joinUs';
import cookie from './id-ID/cookie';
import userTerms from './id-ID/userTerms';
import euAiActCompliance from './id-ID/euAiActCompliance';
import gdprCompliance from './id-ID/gdprCompliance';
import workerOffers from './id-ID/workerOffers';
import homePageMobile from './id-ID/homePageMobile';

import smartQualityInspection from './id-ID/smartQualityInspection';
import helpDocument from './id-ID/helpDocument';
import shareComponents from './id-ID/shareComponents';
export default {
  ...workerOffers,
  ...piecemealWhatsApp,
  ...AIAgent,
  ...orderAmazon,
  ...weChatConfiguration,
  ...chatVoiceChannelConfiguration,
  ...googlePlayConfiguration,
  ...instagramConfiguration,
  ...customerInformation,
  ...customerDataGroupManagement,
  ...selfConfiguration,
  ...amazonRegionConfiguration,
  ...faceBookConfiguration,
  ...auth,
  ...feedbackPerformance,
  ...sendMsg,
  ...home,
  ...statistics,
  ...aigc,
  ...emailMarketing,
  ...knowledgeQA,
  ...chatChannelConfiguration,
  ...whatsAppChannelConfiguration,
  ...agentWorkloadReport,
  // ...homePage,
  ...awsAccountSetting,
  ...cloudContactCenter,
  ...customerList,
  ...crmWorkRecord,
  ...channel,
  ...agentManagement,
  ...worktable,
  ...userManagement,
  ...personal,
  ...customerExtensionInformation,
  ...workOrderManagement,
  ...homePage,
  ...contactCustomers,
  ...documentKnowledgeBase,
  ...definitionSynonyms,
  ...emailChannelConfiguration,
  ...beginnerGuidance,
  ...marketingActivities,
  ...marketingResults,
  ...agentIMChat,
  ...selfAssessmentDetails,
  ...tagManagement,
  ...alloctionRule,
  ...site,
  ...intentionManagement,
  ...hotlineKeyIndicatorsConfig,
  ...hotlineKeyIndicators,
  ...meteringBilling,
  ...manageAgentStatus,
  ...apiManage,
  ...finance,
  ...header,
  ...footer,
  ...homePageNew,
  ...productChannel,
  ...retail,
  ...manufacture,
  ...electronics,
  ...newEnergy,
  ...partner,
  ...resources,
  ...callCenter,
  ...productAiAgent,
  ...productAssistant,
  ...dataReport,
  ...aigcCustomerService,
  ...marketing,
  ...smartWorkOrder,
  ...videoCustomerService,
  ...voiceRobot,
  ...settingTicket,
  ...aiAgentLibrary,
  ...privacyPolicy,
  ...cookiePolicy,
  ...inactiveMessageReminder,
  ...productComplianceGuide,
  ...joinUs,
  ...intelligentFormFilling,
  ...cookie,
  ...userTerms,
  ...euAiActCompliance,
  ...gdprCompliance,
  ...workerOffers,
  ...smartQualityInspection,
  ...homePageMobile,
  ...helpDocument,
  ...shareComponents,
};
