export default {
  'add.chat.channel.configuration.title': '添加WEB端在线聊天渠道',
  'add.chat.channel.configuration.title.update': '修改WEB端在线聊天渠道',

  'app.add.chat.channel.configuration.title.update': '修改APP端在线聊天渠道',
  'add.chat.channel.configuration.tips':
    '让客户更容易地通过 WEB 网站联系到企业，更快速的响应客户需求',
  'app.add.chat.channel.configuration.title': '添加APP端在线聊天渠道',
  'app.add.chat.channel.configuration.tips':
    '让客户更容易地通过 APP 网站联系到企业，更快速的响应客户需求',
  'chat.channel.configuration.title.1': '填写渠道基本信息',
  'chat.channel.configuration.title.tips.1': '请填写渠道基本信息',
  'chat.channel.configuration.title.2': '外观设置',
  'chat.channel.configuration.title.tips.2':
    '在这里您可以配置聊天框，选择自己喜欢的LOGO和颜色',
  'chat.channel.configuration.title.3': '基本功能设置',
  'chat.channel.configuration.title.tips.3': '在这里您可以配置聊天框的基本功能',
  'chat.channel.configuration.title.4': '智能客服设置',
  'chat.channel.configuration.title.tips.4':
    '在这里您可以配置智能客服的相关信息',
  'chat.channel.configuration.title.5': '部署',
  'chat.channel.configuration.title.tips.5':
    '请按照接下来的描述在您的网站部署聊天框',
  'chat.channel.configuration.channel.name': '渠道名称',
  'chat.channel.configuration.channel.name.placeholder': '请输入渠道名称',
  'chat.channel.configuration.chat.types': '选择语言',
  'chat.channel.configuration.chat.types.placeholder': '请选择语言',
  'chat.channel.configuration.chat2.name': '公司名称',
  'chat.channel.configuration.chat2.name.placeholder': '请输入公司名称',
  'chat.channel.configuration.chat2.address': '公司地址',
  'chat.channel.configuration.chat2.address.placeholder': '请输入公司地址',
  'chat.channel.configuration.chat2.logo': '公司Logo',
  'chat.channel.configuration.chat2.logo.message1':
    '只能上传jpg/png文件，且不超过500kb',
  'chat.channel.configuration.chat2.logo.message2':
    '会在聊天框左上角展示，建议上传50*20px的PNG图片',
  'chat.channel.configuration.chat2.chatBoxName.placeholder':
    '请输入聊天框名称',
  'chat.channel.configuration.chat2.chatBoxName': '聊天框名称',
  'chat.channel.configuration.chat2.chatBoxName.message':
    '会在聊天框左上角展示',
  'chat.channel.configuration.chat2.templete': '主题色',
  'chat.channel.configuration.chat2.templete.custom': '自定义',
  'chat.channel.configuration.chat2.templete.color': '当前颜色：',
  'chat.channel.configuration.chat2.templete.placeholder': '请选择颜色',
  'chat.channel.configuration.chat2.boxColor': '客服聊天框颜色',
  'chat.channel.configuration.chat2.userBox': '用户聊天框颜色',
  'chat.channel.configuration.chat2.information.configuration.completed':
    '您已完成聊天框外观的设置，您可以点击预览按钮预览样式。',
  'chat.channel.configuration.work.panels.checkbox': '开启',
  'chat.channel.configuration.chat3.form': '用户信息表单页',
  'chat.channel.configuration.chat3.form.message':
    '建议开启，开启后用户需要先输入基本信息，才能进行后续的沟通',
  'chat.channel.configuration.chat3.welcome': '初始问候语',
  'chat.channel.configuration.chat3.welcome.words': '一句话欢迎语',
  'chat.channel.configuration.chat3.welcome.words.placeholder': '请输入欢迎语',
  'chat.channel.configuration.chat3.welcome.words.message':
    '此处设置请跟您在第一步选择的语言进行对应',
  'chat.channel.configuration.chat3.welcome.QA': '触发FAQ',
  'chat.channel.configuration.chat3.welcome.QA.placeholder': '请选择触发FAQ',
  'chat.channel.configuration.chat3.interval.placeholder':
    '请输入自动邀请会话时间',
  'chat.channel.configuration.chat3.welcome.QA.message':
    'FAQ可以允许您一次性回复多条答案给到用户，不仅支持文本，同时也支持图片和视频。 若您选择了某个FAQ，用户在联系到您的时候，系统会自动回复FAQ中配置的标准答案。若您尚未设置任何的FAQ，可点击',
  'chat.channel.configuration.chat3.welcome.QA.message.1': '此处',
  'chat.channel.configuration.chat3.welcome.QA.message.2': '进行设置。',
  'chat.channel.configuration.chat3.talk': '自动邀请会话',
  'chat.channel.configuration.chat3.talk.ge': '隔',
  'chat.channel.configuration.chat3.talk.ge2': '秒，系统会自动弹出聊天框',
  'chat.channel.configuration.chat3.message':
    '用户浏览网站的过程中，系统将自动弹窗邀请用户进行咨询',
  'chat.channel.configuration.chat3.talk.Input': '自动邀请会话欢迎语',
  'chat.channel.configuration.chat3.talk.Input.placeholder':
    '请输入自动邀请会话欢迎语',
  'chat.channel.configuration.chat3.talk.Input.message':
    '在此处设置自动邀请会话弹出时展示的欢迎语',

  'chat.channel.configuration.chat3.voice.message':
    '用户在与客服沟通过程中可以点击下方视频沟通按钮进行语音沟通',
  'chat.channel.configuration.chat3.voice': '在线语音沟通',
  'chat.channel.configuration.chat3.video.message':
    '用户在与客服沟通过程中可以点击下方视频沟通按钮进行视频沟通',
  'chat.channel.configuration.chat3.video': '在线视频沟通',
  'chat.channel.configuration.chat3.whatsApp.message':
    '开启之后，将会在聊天窗下方展示WhatsApp入口',
  'chat.channel.configuration.chat3.whatsApp': '展示WhatsApp渠道',
  'chat.channel.configuration.chat3.email.message':
    '开启之后，将会在聊天窗下方展示邮件入口',
  'chat.channel.configuration.chat3.email': '展示邮件渠道',
  'chat.channel.configuration.chat3.evaluate.message':
    '用户在结束聊天后，系统自动弹出满意度评价',
  'chat.channel.configuration.chat3.evaluate': '满意度评价',

  'chat.channel.configuration.chat3.email.select': '关联的邮件渠道',
  'chat.channel.configuration.chat3.email.select.placeholder':
    '请选择关联的邮件渠道',
  'chat.channel.configuration.chat3.email.select.message':
    '关联邮箱后，用户可直接点击聊天框下方邮件图标进行联系',
  'chat.channel.configuration.chat3.WhatsApp.select': '关联的WhatsApp渠道',
  'chat.channel.configuration.chat3.WhatsApp.select.placeholder':
    '请选择关联的WhatsApp渠道',
  'chat.channel.configuration.chat3.whatsApp.select.message':
    '关联WhatsApp号码后，用户可直接点击聊天框下方的WhatsApp图标发起WhatsApp会话',

  'chat.channel.configuration.chat3.information.configuration.completed':
    '您已完成基本功能的设置，这些将会在您保存后实现在聊天框。',

  'chat.channel.configuration.chat4.mode.message':
    '智能客服会先有机器人应答，机器人回答不了的问题，用户可以随时转人工 / 仅人工应答只有人工客服应答问题 / 仅机器人应答只有机器人应答问题',
  'chat.channel.configuration.chat4.mode.message.1': ` `,
  'chat.channel.configuration.chat4.mode.message.2': ` `,
  'chat.channel.configuration.chat4.mode.1': '智能客服',
  'chat.channel.configuration.chat4.mode.2': '仅人工',
  'chat.channel.configuration.chat4.mode.3': '仅机器人',
  'chat.channel.configuration.chat4.mode': '客服模式',
  'chat.channel.configuration.chat4.robot.message':
    '机器人名称将会展示在机器人回答信息的上方',
  'chat.channel.configuration.chat4.robot.placeholder': '请输入机器人名称',
  'chat.channel.configuration.chat4.robot': '机器人名称',
  'chat.channel.configuration.chat4.language.message':
    '开启后系统将会自动识别用户”输入问题“的语言；若不开启将会以用户浏览器的语言为准',
  'chat.channel.configuration.chat4.language': '自动识别语言',
  'chat.channel.configuration.chat4.document': '文档知识库',
  'chat.channel.configuration.chat4.document.placeholder': '请选择文档知识库',
  'chat.channel.configuration.chat4.document.message.1':
    '此处的文档知识库仅展示外部知识库，请提前到',
  'chat.channel.configuration.chat4.document.message': '文档知识库',
  'chat.channel.configuration.chat4.document.message.2': '页面进行知识库的配置',
  'chat.channel.configuration.chat4.ai.message': '您可配置是否对接生成式AI',
  'chat.channel.configuration.chat4.ai': '对接生成式AI',
  'chat.channel.configuration.chat4.workers': '未知回答，转人工按钮',
  'chat.channel.configuration.chat4.workers.content':
    '很抱歉，这个问题我无法回答您，请转人工咨询。',
  'chat.channel.configuration.chat4.workers.position': '所在位置',
  'chat.channel.configuration.chat4.workers.zhuan': '转人工',
  'chat.channel.configuration.chat4.workers.message':
    '若选择开启，当机器人不知道答案时，会在机器人回复内容下方自动显示「转人工」按钮',
  'chat.channel.configuration.chat4.unknown': '机器人未知问题回复',
  'chat.channel.configuration.chat4.unknown.placeholder':
    '请输入机器人未知问题回复',
  'chat.channel.configuration.chat4.unknown.message':
    '此处设置是机器人在遇到未知问题时的回复',
  'chat.channel.configuration.chat4.information.configuration.completed':
    '您已完成智能客服的设置，这些将会在您保存后实现在聊天框。',
  'chat.channel.configuration.chat5.message':
    '复制以下代码，并在您的网站的<body> </body> 标签内容插入。',
  'chat.channel.configuration.chat5.message.link':
    '聊天链接：复制以下链接到您网站的代码中。',
  'live.chat.title': '聊天框预览区',
  'live.chat.title.subtitle': '在这里您可以预览聊天框的效果',
  'live.chat.customer': '客户',
  'live.chat.customer.Dialogue': '可以了解一下产品的主要功能有哪些吗？',
  'live.chat.submit': '提交',
  'live.chat.end': '结束聊天',
  'live.chat.video': '视频沟通',
  'chat.channel.configuration.cancel.btn': '取消',
  'chat.channel.configuration.next.btn': '下一步',
  'chat.channel.configuration.complete.btn': '完成',
  'chat.channel.configuration.title.knowledge_unknown_reply':
    '以我目前的技能，还不能回答您提出的问题。如果有需要，您可以直接选择我们座席小姐姐给您更专业的支持哦～',
  'chat.channel.configuration.chat5.end':
    '请注意：将以上代码集成到您的网站后，请联系「ConnectNow」管理员，将上述域名添加至白名单。白名单设置完成后，聊天组件才能正常显示。',
  'chat.channel.configuration.chat5.end.1': ' ',

  'chat.channel.configuration.channel.name.web': '网站域名',
  'chat.channel.configuration.channel.name.placeholder.web': '请输入网站域名',
  'live.chat.customer.Dialogue.product': '请问您想了解哪一款产品？',
  'chat.channel.configuration.chat5.message.Settings': '部署设置',
  'chat.channel.configuration.channel.name.placeholder.error':
    '只能输入中文、大小写字母、数字、”-”、“_”',
  'chat.channel.configuration.channel.chatBoxName.placeholder.error':
    '只能输入中文、大小写字母、空格',
  'chat.channel.configuration.chat1.document.placeholder.language':
    '触发FAQ数据发生改变请重新选择',
  'chat.channel.configuration.channel.website':
    '网站域名格式如下:www.connectnow.cn',
  'chat.channel.configuration.channel.website.name.placeholder.error':
    '请输入规则的网站域名',
  'chat.channel.configuration.work.panels.checkbox.ccp': '是否开启语言识别',
  'chat.channel.configuration.title.pop_welcome_msg':
    '您好，我是ConnectNow智能客服，请问有什么可以帮助您的吗？',
  'chat.channel.configuration.chat4.workers.keyword.message':
    '客户输入此关键词后会转人工客服',
  'chat.channel.configuration.chat4.workers.keyword': '转人工关键词',
  'chat.channel.configuration.chat4.document.placeholder.keyword':
    '请至少输入一条关键词',

  'wx.program.channel.configuration.title': '添加微信小程序聊天渠道',
  'wx.program.channel.configuration.title.update': '修改微信小程序聊天渠道',
  'wx.program.channel.configuration.tips':
    '让客户更容易地通过微信小程序联系到企业，更快速的响应客户需求',
  'shopify.channel.configuration.title': '添加shopify聊天渠道',
  'shopify.channel.configuration.title.update': '修改shopify聊天渠道',
  'shopify.channel.configuration.tips':
    '让客户更容易地通过 shopify 网站联系到企业，更快速的响应客户需求',

  'chat.channel.configuration.chat1.ai.aiagent': '开启AI Agent',
  'chat.channel.configuration.chat1.ai.aiagent.tips':
    '开启AI Agent之后，欢迎用语以及AIGC知识库的选择需要到AI Agent页面进行对应的配置。',
  'chat.channel.configuration.chat2.agent.avac': '客服默认头像',
  'chat.channel.configuration.chat2.customer.avac': '客户默认头像',
  'chat.channel.configuration.chat2.robot.avac': '机器人头像',
  'chat.channel.configuration.chat2.robot.avac.kexuan': '（可选）',
  'chat.channel.configuration.chat2.robot.avac.tips':
    '会在聊天框里展示，建议上传50*50px的PNG图片',
  'chat.channel.configuration.chat2.agent.font.color': '客服聊天字体颜色',
  'chat.channel.configuration.chat2.customer.font.color': '用户聊天字体颜色',
  'chat.channel.configuration.chat3.history.chat': '展示历史对话',
  'chat.channel.configuration.chat3.history.chat.tips':
    '开启之后，终端用户将能看到之前的历史对话',
  'chat.channel.configuration.chat3.history.chat.num': '展示历史对话数量',
  'chat.channel.configuration.chat3.history.chat.num.p':
    '请选择展示历史对话数量',
  'chat.channel.configuration.chat3.history.chat.select': '不限制',
  'chat.channel.configuration.chat4.transfor.btn': '展示转人工按钮',
  'chat.channel.configuration.chat4.transfor.btn.tips':
    '开启后聊天窗右侧会展示“转人工”按钮',
  'chat.channel.configuration.chat4.agent.workTime': '座席工作时间',
  'chat.channel.configuration.chat4.agent.workTime.p': '请选择座席工作时间',

  'chat.channel.configuration.chat4.agent.workTime.no':
    '不在工作时间机器人回复话术',

  'chat.channel.configuration.chat4.agent.workTime.no.default':
    '对不起，当前时间不在我们座席的工作时间，请您在 08:00～18:00 再来咨询我们。',
  'chat.channel.configuration.chat4.agent.workTime.no.tips':
    '当不在座席工作时间，客户转人工咨询时机器人回复的话术',
  'chat.channel.configuration.chat1.push.agents.join.chat': '推送座席加入聊天',

  // discord渠道
  'channel.allocation.detail.discord.title': 'Discord渠道配置',
  'add.discord.channel.configuration.title': '添加 Discord 渠道',
  'editor.discord.channel.configuration.title': '修改 Discord 渠道',
  'add.discord.channel.configuration.tips': '请添加 Discord 渠道',
  'discord.channel.configuration.title.1': '配置 Discord Bot Token',
  'discord.channel.configuration.title.tips.1':
    '将 Discord 开发者后台 Bot Token 信息填入下方对应的输入框，',
  'discord.channel.configuration.title.tips.2': '查看配置文档',
  'discord.channel.allocation.complete.1':
    '您已经成功输入 Bot Token，请继续完成智能客服配置。',
  'discord.channel.configuration.channel.bot.name': '机器人名称',
  'discord.channel.configuration.channel.bot.name.placeholder':
    '请输入机器人名称',
  'discord.channel.configuration.channel.bot.token': 'Bot Token',
  'discord.channel.configuration.channel.bot.token.placeholder':
    '请输入Bot Token',
  'channel.allocation.detail.input.bot.name': '机器人名称：',
  'channel.allocation.detail.input.bot.name.placeholder':
    '请输入机器人名称，回车搜索',
  'discord.channel.configuration.channel.application.id': '应用ID：',
  'discord.channel.configuration.channel.application.id.placeholder':
    '请输入应用ID',

  // discord帮助文档
  'channel.allocation.discord.document.title': 'Discord：集成ConnectNow',
  'channel.allocation.discord.document.h1': '准备 Discord 服务器',
  'channel.allocation.discord.document.step.1': ' 准备Discord账号',
  'channel.allocation.discord.document.step.1.text':
    ' 如果您还没有discord的账号，请访问<a>https://discord.com/</a>进行注册',
  'channel.allocation.discord.document.step.1.text.1':
    ' 如果您已经有discord的账号，请直接跳转步骤二',
  'channel.allocation.discord.document.step.2': ' 准备Discord服务器',
  'channel.allocation.discord.document.step.2.text':
    ' 注：如果您已经有Discord服务器也请跳过此步骤',
  'channel.allocation.discord.document.step.2.text.1': ' 点击创建服务器',
  'channel.allocation.discord.document.step.2.text.2': ' 选择亲自创建',
  'channel.allocation.discord.document.step.2.text.3':
    ' 根据实际情况选择您的服务器信息，如果您不知道如何选择，也可以点击“跳过该问题”',
  'channel.allocation.discord.document.step.2.text.4':
    ' 输入您的服务器名称，上传logo后点击创建',
  'channel.allocation.discord.document.step.3': ' 创建应用',
  'channel.allocation.discord.document.step.3.text':
    ' 访问开发者后台<a>https://discord.com/developers/applications</a>,点击创建 New Application',
  'channel.allocation.discord.document.step.3.text.1':
    '输入应用名称 点击 Create',
  'channel.allocation.discord.document.step.3.text.2':
    '创建完成后，设置您相关的应用信息',
  'channel.allocation.discord.document.step.3.text.3': '点击左侧菜单的Bot',
  'channel.allocation.discord.document.step.3.text.4':
    '配置 Bot 权限， 按照下图，进行权限配置，然后点击 【Save Changes】 按钮',
  'channel.allocation.discord.document.step.3.text.5':
    '授权 点击左侧菜单OAuth2，向下滑动，选中Bot，然后向下滑动然后依次选中Send Messages，Send Messages in Threads，Manage Messages，Read Message History，View Channels。如下两图',
  'channel.allocation.discord.document.step.3.text.6':
    '选择完成后再页面最下方会生成一个授权链接，点击复制在浏览器中打开',
  'channel.allocation.discord.document.step.3.text.7':
    '链接打开后如下图，选择要将机器人添加到的服务器，选择完成后点击继续，到下一步后点击授权按钮',
  'channel.allocation.discord.document.step.3.text.8':
    '将机器人添加到服务器成功',
  'channel.allocation.discord.document.step.4': ' 将机器人集成到ConnectNow',
  'channel.allocation.discord.document.step.4.text':
    '获取机器人Token，访问开发者后台<a>https://discord.com/developers/applications</a>，点击左侧菜单Bot，点击Reset Token',
  'channel.allocation.discord.document.step.4.text.1':
    '生成后点击Copy，将Token保存起来',
  'channel.allocation.discord.document.step.4.text.6':
    '获取应用id，这个字段很重要错误会导致渠道收不到消息！！',
  'channel.allocation.discord.document.step.4.text.2':
    '访问ConnectNow后台，点击渠道->discord->添加渠道，输入刚刚保存的Token及机器人名称，点击下一步',
  'channel.allocation.discord.document.step.4.text.3':
    '根据您的需求设置智能客服参数，点击下一步',
  'channel.allocation.discord.document.step.4.text.4': '输入渠道名称',
  'channel.allocation.discord.document.step.4.text.5':
    '🎉恭喜！你的 Discord 已成功集成到ConnectNow了',
};
