export default {
  'allocation.please.select.ticket.tag': '请选择工单标签',
  'workerOffers.copy.message': '复制',
  'workerOffers.references.message': '引用',
  'workerOffers.withdraw.message': '撤回',
  'workerOffers.withdraw.message.tips': '您已经撤回一条消息',
  'workerOffers.withdraw.message.edit': '重新编辑',
  'workerOffers.agent.status.limit': '当前在线客服已达上限，请稍后再试',
  'workerOffers.chatList.setting.translation.tips':
    '开启之后，您与客户的每次聊天都会进行实时翻译',
  'channel.allocation.detail.tiktok.title': 'Tiktok配置渠道',
  'add.tiktok.channel.configuration.title': '添加 TikTok Shop 渠道',
  'add.tiktok.channel.configuration.title.update': '修改 TikTok Shop 渠道',
  'add.tiktok.channel.configuration.tips': '请添加 TikTok Shop 渠道',
  'add.tiktok.channel.configuration.tips.update': '请修改 TikTok Shop 渠道',
  'tiktokRegion.channel.configuration.title.1':
    '请选择你的 TikTok Shop 卖家地区',
  'tiktokRegion.channel.configuration.title.tips.1':
    '如果你有不止一个 TikTok Shop 地区，那也没关系。只需选择你想要首先设置的那个地区，我们稍后可以添加其他地区。',
  'tiktokRegion.channel.configuration.title.2': '让我们进行授权，获取Token。',
  'tiktokRegion.channel.configuration.title.tips.2':
    '为了让我们能获取到您的 TikTok 的消息，你需要授予 ConnectNow 访问权限。',
  'workerOffers.chatList.setting.customer.avatar.color': '客户头像颜色设置',
  'workerOffers.chatList.setting.customer.avatar.color.table.order': '匹配顺序',
  'workerOffers.chatList.setting.customer.avatar.color.table.customer.color':
    '客户头像颜色',
  'workerOffers.chatList.setting.customer.avatar.color.table.add': '创建规则',
  'workerOffers.chatList.setting.customer.avatar.color.table.other.tag':
    '其他标签',
  'workerOffers.chatList.setting.customer.avatar.color.table.tag.add':
    '创建规则',
  'workerOffers.chatList.setting.customer.avatar.color.table.tag.edit':
    '修改规则',
  'workerOffers.chatList.setting.customer.avatar.color.table.tag.title':
    '标签名称：',
  'workerOffers.chatList.setting.customer.avatar.color.table.tag.color.placeholder':
    '请选择颜色',
  'workerOffers.chatLayout.userNav.beInputting': '正在输入...',
  'channel.allocation.detail.tiktok.title': 'Tiktok配置渠道',
  'add.tiktok.channel.configuration.title': '添加 TikTok Shop 渠道',
  'add.tiktok.channel.configuration.title.update': '修改 TikTok Shop 渠道',
  'add.tiktok.channel.configuration.tips': '请添加 TikTok Shop 渠道',
  'add.tiktok.channel.configuration.tips.update': '请修改 TikTok Shop 渠道',
  'tiktokRegion.channel.configuration.title.1':
    '请选择你的 TikTok Shop 卖家地区',
  'tiktokRegion.channel.configuration.title.tips.1':
    '如果你有不止一个 TikTok Shop 地区，那也没关系。只需选择你想要首先设置的那个地区，我们稍后可以添加其他地区。',
  'tiktokRegion.channel.configuration.title.2': '让我们进行授权，获取Token。',
  'tiktokRegion.channel.configuration.title.tips.2':
    '为了让我们能获取到您的 TikTok 的消息，你需要授予 ConnectNow 访问权限。',
  'tiktokRegion.channel.configuration.title.3.modal.title.tips':
    '如果未完成此步骤，我们将无法传送您的tiktok消息。请确认您已遵循上述所有说明，然后再继续。',
  'user.management.operation.add.avator': '添加个性化头像',
  'user.management.operation.add.avator.nickname': '您的昵称',
  'user.management.operation.add.avator.avatar': '您的头像',
  'user.management.operation.add.avator.setting': '个性化设置',
  'user.management.operation.add.avator.nickname.placeholder': '请输入您的昵称',
  'user.management.operation.add.avator.avatar.placeholder': '请上传您的头像',
  'user.management.operation.add.avator.setting.btn': '个性化头像设置',
  'user.management.operation.add.avator.setting.title': '添加个性化头像',
  'user.management.operation.add.avator.setting.delete.confirm':
    '确定要删除这个个性化头像设置吗？',
  'user.management.operation.add.avator.setting.duplicate.tips':
    '该渠道已被其他头像设置规则选中！',
  'personal.tabs.3': '个性化头像',
  'personal.tabs.3.tips':
    '一般情况下，您只需要在左侧个人信息页面设置您的头像即可。若您需要在不同的聊天渠道展示不同的头像和昵称，则可以在此进行更有针对性的个性化的设置。',
  'tiktokRegion.channel.configuration.title.2.shop':
    '请授权您的账号并选择要绑定的 TikTok 店铺。',
  'tiktokRegion.channel.configuration.title.tips.2.shop':
    '为了帮助您接入 TikTok 消息与订单系统，请授权 ConnectNow 访问您的 TikTok 店铺信息，如果您有多个店铺请选择其中的一个。',
  'tiktokRegion.channel.configuration.title.3.modal.title.tips':
    '如果未完成此步骤，我们将无法传送您的tiktok消息。请确认您已遵循上述所有说明，然后再继续。',
  'channel.allocation.detail.select.account.tiktok.name': '店铺名称：',
  'channel.allocation.detail.select.account.tiktok.name.placeholder':
    '请输入店铺名称，回车搜索',
  'channel.allocation.detail.select.account.tiktok.shopId': '店铺ID：',
  'channel.allocation.detail.select.account.tiktok.shopName': '店铺名称：',
  'channel.allocation.detail.select.account.tiktok.shopCode': '店铺编码：',
  'workerOffers.withdraw.message.tips.1': '您撤回了一条消息',
  'ticketOrCustomerSettings.config.title.1': '工单类型定义',
  'ticketOrCustomerSettings.config.title.2': '工单属性定义',
  'ticketOrCustomerSettings.config.title.3': '客户属性定义',
  'ticketOrCustomerSettings.config.title.4': '权限设置',
  'agentOperationSettings.config.title.1': 'SLA设置',
  'agentOperationSettings.config.title.2': '工作时间',
  'agentOperationSettings.config.title.3': '坐席状态定义',
};
