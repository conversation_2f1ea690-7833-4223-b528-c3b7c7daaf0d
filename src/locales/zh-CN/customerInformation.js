export default {
  'customerInformation.customerName': '客户名称：',
  'customerInformation.customerName.placeholder': '请输入客户名称，回车搜索',
  'customerInformation.emailAddress': '邮箱地址：',
  'customerInformation.emailAddress.placeholder': '请输入邮箱地址，回车搜索',
  'customerInformation.add.basicInformation.emailAddress.placeholder.1':
    '请输入邮箱地址',
  'customerInformation.customerBusinessName': '公司名称：',
  'customerInformation.customerBusinessName.placeholder':
    '请输入公司名称，回车搜索',
  'customerInformation.sourceChannel': '来源渠道：',
  'customerInformation.searchPlaceholder': '请输入客户资料信息，回车搜索',
  'customerInformation.customerInformation': ' 客户资料',
  'customerInformation.customerInformation1': '客户资料',
  'customerInformation.table.sureName': '客户姓氏',
  'customerInformation.table.customerName': '客户名称',
  'customerInformation.table.customerCode': '客户编号',
  'customerInformation.table.mailAddress': '邮箱地址',
  'customerInformation.table.telephonePrefixId': '区号',
  'customerInformation.table.mobilePhoneNumber': '手机号码',
  'customerInformation.table.customerLabel': '客户标签',
  'customerInformation.table.ContactInformation': '联系方式',
  'customerInformation.table.ContactTime': '最后联系时间',
  'customerInformation.table.nation': '国家',

  'customerInformation.table.creationMethod': '创建方式',
  'customerInformation.table.groupName': '分组名称',
  'customerInformation.table.sourceChannel': '来源渠道',
  'customerInformation.table.createTime': '创建时间',
  'customerInformation.table.address': '邮寄地址',
  'customerInformation.table.operation': '操作',
  'customerInformation.table.contactCustomer': '联系客户',
  'customerInformation.table.customer.detail': '详情',
  'customerInformation.table.editor': '修改',
  'customerInformation.add.basicInformation.customerID': '会员ID',

  'customerInformation.add.basicInformation.addGroup': '添加分组',
  'customerInformation.add.basicInformation.pattern':
    "格式不正确，仅允许中、英文、数字及'.'",
  'customerInformation.add.basicInformation.maxlength': '长度不能超过80个字符',
  'customerInformation.add.basicInformation.maxlength2':
    '长度不能超过200个字符',
  'customerInformation.add.basicInformation.maxlength3':
    '长度不能超过2000个字符',
  'customerInformation.add.basicInformation.placeholder': '请输入',

  'customerInformation.add.basicInformation.title': '基本信息',
  'customerInformation.add.basicInformation.title2': '客户社媒信息',
  'customerInformation.add.basicInformation.title3': '客户扩展信息',
  'customerInformation.add.basicInformation.selectiveGrouping': '选择分组',
  'customerInformation.add.basicInformation.selectiveGrouping.placeholder':
    '请选择分组',
  'customerInformation.add.basicInformation.selectiveGrouping.required':
    '请选择分组',

  'customerInformation.add.basicInformation.name': '名：',
  'customerInformation.add.basicInformation.lastname': '姓：',
  'customerInformation.add.basicInformation.lastname.placeholder': '请输入姓',
  'customerInformation.add.basicInformation.lastname.required': '请输入姓',
  'customerInformation.add.basicInformation.name.placeholder': '请输入名',
  'customerInformation.add.basicInformation.name.required': '请输入名',
  'customerInformation.add.basicInformation.name.pattern':
    "姓名格式不正确，仅允许中、英文、数字、空格、横线、下划线及'.'",
  'customerInformation.add.basicInformation.name.maxlength':
    '姓名长度不能超过200个字符',

  'customerInformation.add.basicInformation.sourceChannel.placeholder':
    '请选择来源渠道',
  'customerInformation.add.basicInformation.channel.name': '请选择渠道名称',
  'customerInformation.add.basicInformation.sourceChannel.required':
    '请选择来源渠道',

  'customerInformation.add.basicInformation.dateBirth': '出生日期：',

  'customerInformation.add.basicInformation.sex': '性别：',
  'customerInformation.add.basicInformation.sex.placeholder': '请选择性别',
  'customerInformation.add.basicInformation.contactNumber': '联系电话：',

  'customerInformation.add.basicInformation.contactNumber.placeholder':
    '请输入联系电话',
  'customerInformation.add.basicInformation.contactNumber.pattern':
    '联系电话格式不正确，仅允许数字',
  'customerInformation.add.basicInformation.contactNumber.pattern1':
    '密码至少包含数字、英文、符号中的两种,符号输入范围 ~!@#$%^&*()_+<>?.,',
  'customerInformation.add.basicInformation.contactNumber.maxlength':
    '联系电话长度不能超过40个字符',

  'customerInformation.add.basicInformation.emailAddress': '邮箱地址：',
  'customerInformation.add.basicInformation.emailAddress.placeholder':
    '请输入邮箱地址',
  'customerInformation.add.basicInformation.Address.placeholder': '请选择地域',
  'customerInformation.add.basicInformation.emailAddress.pattern':
    '邮箱地址格式不正确',

  'customerInformation.add.basicInformation.companyName': '公司名称：',
  'customerInformation.add.basicInformation.companyName.placeholder':
    '请输入公司名称',

  'customerInformation.add.basicInformation.position': '职务：',
  'customerInformation.add.basicInformation.position.placeholder': '请输入职务',
  'customerInformation.add.basicInformation.mailingAddress': '邮寄地址',
  'customerInformation.add.basicInformation.mailingAddress.placeholder':
    '请输入邮寄地址',
  'customerInformation.add.basicInformation.orderAddress': '账单地址',
  'customerInformation.add.basicInformation.orderAddress.placeholder':
    '请输入账单地址',
  'customerInformation.add.basicInformation.deliveryAddress': '配送地址',
  'customerInformation.add.basicInformation.deliveryAddress.placeholder':
    '请输入配送地址',
  'customerInformation.add.basicInformation.otherAddress': '其他地址',
  'customerInformation.add.basicInformation.otherAddress.placeholder':
    '请输入其他地址',
  'customerInformation.add.basicInformation.remark': '备注',
  'customerInformation.add.basicInformation.remark.placeholder': '请输入备注',

  'work.order.management.table.work.order.notes':
    '注：设置时区后，系统将按照该时区展示所有时间',

  'customerInformation.add.basicInformation.return.confirm':
    '返回将清空当前表单修改的内容，确定返回么？',
  'customerInformation.add.basicInformation.button.return': '返回',
  'customerInformation.add.basicInformation.button.save': '保存',
  'customerInformation.add.basicInformation.button.update': '修改',

  'customerInformation.modal.basicInformation.tips': '提示',
  'customerInformation.modal.basicInformation.button.ok': '查看客户信息',
  'customerInformation.modal.basicInformation.button.cancel': '取消',

  'customerInformation.contactCustomer.info.title': '客户信息',
  'customerInformation.contactCustomer.info.customerName': '客户名称：',
  'customerInformation.contactCustomer.info.telephone': '联系电话：',
  'customerInformation.contactCustomer.info.emailAddress': '邮箱地址：',
  'customerInformation.contactCustomer.info.channelName': '渠道：',
  'customerInformation.contactCustomer.info.customerGroupName': '分组：',
  'customerInformation.contactCustomer.info.mailingAddress': '邮寄地址：',
  'customerInformation.contactCustomer.channel.placeholder': '请选择渠道',
  'customerInformation.add.basicInformation.customerID.required':
    '请输入会员ID',
  'customerInformation.contactCustomer.subject.placeholder': '请输入邮件主题',
  'customerInformation.contactCustomer.send.button': '发送',

  'customerInformation.contactCustomer.table.workRecordId': '记录 ID',
  'customerInformation.contactCustomer.table.channelName': '渠道名称',
  'customerInformation.contactCustomer.table.customerContactInfo':
    '客户联系方式',
  'customerInformation.contactCustomer.table.userName': '客服名称',
  'customerInformation.contactCustomer.table.status': '状态',
  'customerInformation.contactCustomer.table.createTime': '创建时间',
  'customerInformation.contactCustomer.table.resolveTime': '解决时间',
  'customerInformation.contactCustomer.button.send': '发送',
  'customerInformation.contactCustomer.record': '客户咨询记录',

  'customerInformation.name.not.empty': '第{lineNum}行客户名字不能为空',
  'customerInformation.name.length.limit': '第{lineNum}行客户名字最多200个字符',
  'customerInformation.name.input.limit':
    "第{lineNum}行客户名字只能输入中英文、数字及'.'",

  'customerInformation.last.name.not.empty': '第{lineNum}行姓氏不能为空',
  'customerInformation.last.name.length.limit':
    '第{lineNum}行姓氏最多200个字符',
  'customerInformation.last.name.input.limit':
    "第{lineNum}行姓氏只能输入中英文、数字及'.'",

  'customerInformation.group.not.empty': '第{lineNum}行分组名称不能为空',
  'customerInformation.channel.not.empty': '第{lineNum}行来源渠道不能为空',

  'customerInformation.phone.input.limit':
    '第{lineNum}行手机号码格式不正确，仅允许数字',
  'customerInformation.phone.length.limit': '第{lineNum}行手机号码最多40个字符',
  'customerInformation.phone.prefix.limit':
    '第{lineNum}行国际区号填写错误，请进行修改',

  'customerInformation.company.input.limit':
    "第{lineNum}行公司名称只能输入中英文、数字及'.'",
  'customerInformation.company.length.limit':
    '第{lineNum}行公司名称最多80个字符',

  'customerInformation.post.input.limit':
    "第{lineNum}行职务只能输入中英文、数字及'.'",
  'customerInformation.post.length.limit': '第{lineNum}行职务最多80个字符',

  'customerInformation.mailingAddress.input.limit':
    "第{lineNum}行邮寄地址只能输入中英文、数字及'.'",
  'customerInformation.mailingAddress.length.limit':
    '第{lineNum}行邮寄地址最多200个字符',

  'customerInformation.orderAddress.input.limit':
    "第{lineNum}行账单地址只能输入中英文、数字及'.'",
  'customerInformation.orderAddress.length.limit':
    '第{lineNum}行账单地址最多200个字符',

  'customerInformation.deliveryAddress.input.limit':
    "第{lineNum}行配送地址只能输入中英文、数字及'.'",
  'customerInformation.deliveryAddress.length.limit':
    '第{lineNum}行配送地址最多200个字符',

  'customerInformation.otherAddress.input.limit':
    "第{lineNum}行其他地址只能输入中英文、数字及'.'",
  'customerInformation.otherAddress.length.limit':
    '第{lineNum}行其他地址最多200个字符',

  'customerInformation.remark.input.limit':
    "第{lineNum}行备注只能输入中英文、数字及'.'",
  'customerInformation.remark.length.limit': '第{lineNum}行备注最多200个字符',

  'customerInformation.repeat.phone.tips':
    '文件中存在重复的手机号码：{repeatPhone}',
  'customerInformation.repeat.email.tips':
    '文件中存在重复的邮箱：{repeatEmail}',

  'customerInformation.batchImport': '批量导入',
  'customerInformation.export': '导出',
  'customerInformation.download.template': '下载模板',

  'customerInformation.add.basicInformation.customerLevel': '客户等级：',
  'customerInformation.add.basicInformation.customerLevel.placeholder':
    '请选择客户等级',
  'customerInformation.add.basicInformation.customerLevel.required':
    '请选择客户等级',
  'customerInformation.add.basicInformation.customer.language': '客户偏好语言',
  'customerInformation.add.basicInformation.tags': '标签：',
  'customerInformation.add.basicInformation.tags.placeholder':
    '请输入标签并回车',

  'customerInformation.modal.batch.change.grouping': '批量修改分组',
  'customerInformation.modal.batch.import': '批量导入',
  'hotWord.length': 'tag标签长度过长',

  'customerInformation.upload.file': '仅支持一个文件上传，点击<b>{CH}</b>',
  'knowledge.QA.upload.file.request': '1. Excel格式支持*.xls，*.xlsx两种格式',
  'customerInformation.upload.btn': '将文件拖拽到此处或<b>{CH}</b>',
  'customerInformation.upload.download': '点击下载',
  'customerInformation.upload.file.1': '文件说明：',

  'upload.file.tips': '只能上传.xlsx文件',
  'upload.file.tips.btn': '点击上传',

  'customerInformation.option.error': '至少选择一个分组数据！',
  'user.management.operation.table.sourceType.1': '自动创建',
  'user.management.operation.table.sourceType.2': '手动创建',
  'user.management.operation.table.sourceType.3': '营销导入',
  'user.management.operation.table.btn.1': '批量添加标签',
  'user.management.operation.table.btn.2': '批量删除标签',
  'user.management.operation.modal.tags.placeholder':
    '请输入客户标签，回车添加，可输入多个',

  'customerInformation.create.customer.email.tips':
    '收件人、抄送、密送长度不超过2000字符！',
  'customerInformation.create.customer.email.content.tips':
    '邮件内容不能为空！',
  'customerInformation.add.basicInformation.tags.tips.new':
    '请最少选择或创建一个标签！',
};
