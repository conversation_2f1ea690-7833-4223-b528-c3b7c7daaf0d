export default {
  'intention.management.intent.classification': '意图分类：',
  'intention.management.intent.classification.placeholder': '请选择意图分类',
  'intention.management.intent.language': '意图语言：',
  'intention.management.intent.language.placeholder': '请选择意图语言',
  'intention.management.intent.name': '意图名称：',
  'intention.management.intent.name.placeholder': '请输入意图名称',
  'intention.management.create.intention.text': '新建意图',
  'intention.management.user.number': '{num}次',
  'intention.management.user.number.month.tips': '当前月使用次数',
  'intention.management.intent.request.reason': '意图请求原因：',
  'intention.management.create.information': '创建信息：',
  'intention.management.create.intelligent.agent': '新建智能体',
  'intention.management.intelligent.agent': '智能体',
  'intention.management.delete.tips': '您确定要删除这个内容吗？',

  // 添加意图
  'new.intent.title.add': '添加意图',
  'new.intent.title.edit': '修改意图',
  'new.intent.intention.basic.information': '意图基本信息',
  'new.intent.intention.name': '意图名称',
  'new.intent.intention.name.maxlength': '长度不能超过80个字符',
  'new.intent.intention.language': '意图语言',
  'new.intent.intention.classification': '意图分类',
  'new.intent.intention.request.reason': '意图请求原因',
  'new.intent.intention.request.reason.placeholder':
    '请输入客户触发这个意图的原因，例如客户请求查询订单状态',
  'new.intent.intention.reason.maxlength': '长度不能超过2000个字符',
  'new.intent.intention.basic.information.tips':
    '意图名称和意图请求原因是AIGC识别客户意图的重要依据。',
  'new.intent.intention. language.information': '意图话术信息',
  'new.intent.static.language.technique': '静态话术',
  'new.intent.dynamic.speech.technique': '动态话术',
  'new.intent.static.language.technique.placeholder': '请输入静态意图话术',
  'new.intent.dynamic.speech.technique.placeholder': '请输入动态话术',
  'new.intent.dynamic.speech.technique.placeholder2': '请输入动态意图话术',
  'new.intent.ai.static.language.technique': 'AI生成意图话术',
  'new.intent.add.static.language.technique': '添加意图话术',
  'new.intent.intention.dynamic.speech.technique.tips':
    '动态话术中可包含动态属性，动态属性格式为 {Device}，其中Device为属性名，仅支持英文',
  'new.ai.generate.intent.intention.language': '选择意图话术语言',
  'new.intent.intention.language.attribute': '意图话术属性',
  'new.intent.intention.language.attribute.name': '属性名称',
  'new.intent.intention.language.attribute.name.placeholder': '请输入属性名称',
  'new.intent.intention.language.attribute.example': '属性值示例',
  'new.intent.intention.language.attribute.example.placeholder': '请输入属性值',
  'new.intent.intention.language.attribute.example.tips':
    '属性值示例会作为当前属性提取的关键依据，若您当前的属性是Device，那此处的示例可能是空调，除湿机等。',
  'new.intent.intention.attribute.description': '属性描述',
  'new.intent.intention.attribute.description.placeholder': '请输入变量的描述',
  'new.intent.intention.attribute.mandatory': '是否必填项',
  'new.intent.intention.attribute.verify.format': '选择属性的校验格式',
  'new.intent.intention.attribute.verify.format.placeholder':
    '请选择属性的校验格式',
  'new.intent.intention.attribute.verify.rule.placeholder':
    '请输入属性的校验正则',
  'new.intent.intention.attribute.verify.rule.maxlength':
    '长度不能超过200个字符',
  'new.intent.intention.failed.verification.attempts': '校验失败重试次数',
  'new.intent.intention.failed.verification.attempts.placeholder':
    '请输入校验的次数',
  'new.intent.intention.failed.num.verification.attempts.placeholder':
    '校验次数最大不超过99',
  'new.intent.intention.reply.after.verification.failure': '校验失败后的回复',
  'new.intent.intention.reply.after.verification.failure.placeholder':
    '请输入校验失败后的回复',
  'new.intent.intention.reply.after.verification.failure.final':
    '最终校验失败后的回复',
  'new.intent.intention.reply.after.verification.failure.final.placeholder':
    '请输入最终校验失败后的回复',
  'new.intent.intelligent.agent.variables': '存储到智能体的变量',
  'new.intent.intelligent.agent.variables.placeholder':
    '请选择存储到智能体的变量',
  'new.intent.intention.rhetorical.question.not.collecting':
    '未采集到当前变量的反问话术',
  'new.intent.intention.rhetorical.question.not.collecting.placeholder':
    '请输入话术',
  'new.intent.add.variables': '添加变量',
  'new.intent.intention.llm.extract.attribute': 'LLM 提取意图属性',
  'new.intent.llm.extract.attribute.add.variables': '添加属性',
  'new.intent.add.cancel.confirm': '取消将清空表单，确定取消么？',
  'new.intent.add.success.tips':
    '恭喜您已经成功创建了一个意图，接下来您可以为当前意图创建智能体',
  'new.intent.add.static.language.technique.tips': '最多创建100个意图话术',
  'new.intent.add.attribute.example.tips': '每个变量最多增加四个属性实例！',
  'new.intent.add.verbal.variables.tips': '最多创建4个话术变量！',
  'new.intent.intention.classification.management': '意图分类管理',
  'new.intent.intention.classification.management.placeholder':
    '请输入意图分类名称，回车搜索或点击按钮添加',
  'new.intent.add.variables.only.tips': '添加的变量必须唯一不能重复！',
  'new.intent.add.variables.only.tips.1':
    '请配置话术中包含的{variable}变量属性。',
  'new.intent.add.variables.only.tips.2':
    '话术中缺少{variable}变量，请添加对应话术。',
  'new.intent.add.variables.only.tips.3': '话术{script}重复！',
  'new.intent.add.ai.script.create': '意图名称和意图请求原因不能为空！',
  'new.intent.add.script.create.tips': '注意：回车自动成生变量',
  'new.intent.intention.classification.tips': '意图分类不能为空！',
  'new.intent.add.required.one.speech': '必须有一条话术',
  'new.intent.add.dynamic.speech.editing.tips':
    '请先保存正在编辑的动态话术意图属性变量',

  'new.intent.intention.language.attribute.code': '属性编码',
  'new.intent.intention.language.attribute.format.requirement': '属性格式要求',
  'new.intent.intention.language.attribute.is.required': '是否必填',
  'new.intent.intention.language.attribute.operation': '操作',
  'new.intent.intention.language.attribute.is.required.yes': '必填',
  'new.intent.intention.language.attribute.is.required.no': '非必填',
  'new.intent.intention.language.attribute.code.only.support.english':
    '仅支持英文字母',
  'new.intent.intention.language.attribute.code.only.support.english.message':
    '属性编码只能包含英文字母',

  // 智能体列表
  'external.intelligent.agent.delete.synonym.rules': '删除提示',
  'external.intelligent.agent.intent.language.placeholder': '请选择意图名称',
  'external.intelligent.agent.name': '智能体名称：',
  'external.intelligent.agent.name.placeholder': '请输入智能体名称',
  'external.intelligent.agent.deployment.status': '部署状态：',
  'external.intelligent.agent.deployment.status.placeholder': '请选择部署状态',
  'external.intelligent.agent.tab.chat': '聊天',
  'external.intelligent.agent.tab.email': '邮件',
  'external.intelligent.agent.tab.phone': '电话',
  'external.intelligent.agent.create.text': '新建智能体',
  'external.intelligent.agent.use.rule': '使用规则：',
  'external.intelligent.agent.deployment.time': '部署时间：',
  'external.intelligent.agent.delete.tips': '您确定要删除这个智能体么？',
  'external.intelligent.agent.deploy.status.1': '已部署',
  'external.intelligent.agent.deploy.status.2': '草稿',
  'external.intelligent.agent.default.welcome.message':
    '当客户通过WEB在线聊天、APP在线聊天、Shopify、微信小程序与企业进行沟通时，会首先执行当前的欢迎智能体',
  'external.intelligent.agent.fallback':
    '当系统没有匹配到任何智能体时，将会执行默认意图',
  'intelligent.agent.deployed.text': '已部署，{deployName}',
  'external.intelligent.agent.share.success': '复制成功',
  'external.intelligent.agent.share.tips':
    '备注：此分享码有效期为24小时，请在{expiryTime}之前使用。',

  'external.intelligent.agent.share': '分享',
  'external.intelligent.agent.share.content':
    '【ConnectNow】您收到一份来自"{username}"的AI Agent分享，您的专属体验码为：「{shareCode}」该码将于24小时内（{expiryTime}前）失效，请及时使用。',
  'external.intelligent.agent.share.confirm': '确认',
  'external.intelligent.agent.share.copy': '复制',
  'external.intelligent.agent.share.cancel': '取消',

  // 新建智能体
  'external.intelligent.agent.create.title': '新建智能体',
  'external.intelligent.agent.create.add': '创建智能体',
  'external.intelligent.agent.create.basic.information': '基本信息',
  'external.intelligent.agent.create.name': '输入智能体名称',
  'external.intelligent.agent.create.channel.type': '适用渠道类型',
  'external.intelligent.agent.create.select.scenario': '选择智能体场景',
  'external.intelligent.agent.phone.voice': '电话/语音',
  'external.intelligent.agent.create.select.channel.type': '选择适用的渠道类型',
  'external.intelligent.agent.create.select.channel.type.tips':
    '至少选择一个渠道类型',
  'external.intelligent.agent.create.facebook.message': 'Facebook Message',
  'marketing.channel.type.web.voice': 'WEB语音',
  'marketing.channel.type.app.voice': 'APP语音',
};
