export default {
  'add.weChat.channel.configuration.title': 'Add WeChat Customer Service',
  'add.weChat.channel.configuration.title.update':
    'Update WeChat Customer Service',
  'add.weChat.channel.configuration.tips':
    'Simplify customer interactions via WeChat customer service, enabling faster and more effective responses to their inquiries.',
  'weChat.channel.configuration.title.1': 'Bind WeChat Customer Service',
  'weChat.channel.configuration.title.tips.1':
    'Please Bind your WeChat Customer Service',
  'weChat.channel.configuration.title.1.go': 'Bind WeChat Customer Service',
  'weChat.channel.configuration.title.1.go.p':
    'Please bind your WeChat Work account first',

  'weChat.channel.configuration.title.1.go.finish':
    'You have successfully bound your WeChat Customer Service account. Please confirm your WeChat Customer Service account.',
  'weChat.channel.configuration.1.finish': 'Selection completed',
  'weChat.channel.configuration.title.2':
    'Add WeChat customer service information',
  'weChat.channel.configuration.title.tips.2':
    'Please confirm your WeChat Customer Service',
  'weChat.channel.configuration.title.2.label.1': 'Company full name:',
  'weChat.channel.configuration.title.2.label.2': 'Company short name:',
  'weChat.channel.configuration.title.2.label.3': 'Company ID:',
  'weChat.channel.configuration.title.2.label.4':
    'WeChat customer service name',
  'weChat.channel.configuration.title.2.label.4.p':
    'Please enter WeChat customer service name',
  'weChat.channel.configuration.title.2.label.5': 'Upload avatar',
  'weChat.channel.configuration.title.2.label.5.p':
    'Please upload WeChat customer service avatar',

  'weChat.channel.configuration.title.2.label.5.tips':
    'Only jpg/png files can be uploaded, and the size should not exceed 500kb',

  'weChat.channel.configuration.title.3':
    'Intelligent Customer Service Settings',
  'weChat.channel.configuration.title.tips.3':
    'You can configure the relevant information of the intelligent customer service here.',
  'weChat.channel.configuration.title.3.qingxu': 'Chatbot mode',
  'weChat.channel.configuration.title.3.qingxu.radio.1': 'Professional Mode',
  'weChat.channel.configuration.title.3.qingxu.radio.2':
    'Emotionally Intelligent Mode (Softer Responses)',
  'weChat.channel.configuration.title.3.qingxu.radio.tips':
    'By enabling "Emotionally Intelligent Mode", the AlGC Chatbot will deliver softer responses to your customers.',

  'weChat.channel.configuration.title.4': 'Adding channel information',
  'weChat.channel.configuration.title.tips.4':
    'Please enter channel information',
};
