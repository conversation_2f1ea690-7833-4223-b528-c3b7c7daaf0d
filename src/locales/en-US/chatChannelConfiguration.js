export default {
  'add.chat.channel.configuration.title': 'Add live chat channel on WEB',
  'add.chat.channel.configuration.title.update':
    'Update live chat channel on WEB',
  'app.add.chat.channel.configuration.title.update':
    'Update live chat channel on APP',
  'add.chat.channel.configuration.tips':
    'Simplify customer interactions via WEB site, enabling faster and more effective responses to their inquiries.',
  'app.add.chat.channel.configuration.title': 'Add live chat channel on APP',
  'app.add.chat.channel.configuration.tips':
    'Simplify customer interactions via APP site, enabling faster and more effective responses to their inquiries.',
  'chat.channel.configuration.title.1': 'Basic information',
  'chat.channel.configuration.title.tips.1':
    'Please fill in the channel basic information',
  'chat.channel.configuration.title.2': 'Appearance settings',
  'chat.channel.configuration.title.tips.2':
    'Here you can customize the chatbox, choose your favorite LOGO and color scheme',
  'chat.channel.configuration.title.3': 'Basic feature settings',
  'chat.channel.configuration.title.tips.3':
    'Here you can configure the basic functions of the chatbox',
  'chat.channel.configuration.chat3.whatsApp.message':
    'After enabling, an WhatsApp icon will be displayed at the bottom of the chat window.',
  'chat.channel.configuration.chat3.whatsApp': 'Display WhatsApp channel',
  'chat.channel.configuration.chat3.email.message':
    'After enabling, an email icon will be displayed at the bottom of the chat window.',
  'chat.channel.configuration.chat3.email': 'Display email channel',
  'chat.channel.configuration.chat3.email.select': 'Associated email channels',
  'chat.channel.configuration.chat3.email.select.placeholder':
    'Please select the associated email channel',
  'chat.channel.configuration.chat3.email.select.message':
    'After associating the email, users can directly click the email icon at the bottom of the chatbox for contact.',
  'chat.channel.configuration.chat3.WhatsApp.select':
    'Associated WhatsApp channels',
  'chat.channel.configuration.chat3.WhatsApp.select.placeholder':
    'Please select the associated WhatsApp channel',
  'chat.channel.configuration.chat3.whatsApp.select.message':
    'After associating the WhatsApp number, users can click directly on the WhatsApp icon below the chatbox to initiate a WhatsApp conversation',

  'chat.channel.configuration.title.4': 'Intelligent customer service settings',
  'chat.channel.configuration.title.tips.4':
    'Here you can configure the information related to the intelligent customer service',
  'chat.channel.configuration.title.5': 'Deploy',
  'chat.channel.configuration.title.tips.5':
    'Please add a chatbox to your website according to the next description',
  'chat.channel.configuration.channel.name': 'Channel name',
  'chat.channel.configuration.channel.name.placeholder':
    'Enter the channel name',
  'chat.channel.configuration.chat.types': 'Language',
  'chat.channel.configuration.chat.types.placeholder': 'Please select language',
  'chat.channel.configuration.chat2.name': 'Company Name',
  'chat.channel.configuration.chat2.name.placeholder': 'Enter the company name',
  'chat.channel.configuration.chat2.address': 'Company Address',
  'chat.channel.configuration.chat2.address.placeholder':
    'Enter the company address',
  'chat.channel.configuration.chat2.logo': 'Company Logo',
  'chat.channel.configuration.chat2.logo.message1':
    'Only JPG or PNG are supported, and the image size must not exceed 500KB.',
  'chat.channel.configuration.chat2.logo.message2':
    'The icon will be displayed in the upper left corner of the chatbox, it is recommended to upload a 50*20px PNG image',
  'chat.channel.configuration.chat2.chatBoxName.placeholder':
    'Enter chatbot name',
  'chat.channel.configuration.chat2.chatBoxName': 'Chatbot name',
  'chat.channel.configuration.chat2.chatBoxName.message':
    'It will display in the upper left corner of the chatbox',
  'chat.channel.configuration.chat2.templete': 'Theme color',
  'chat.channel.configuration.chat2.templete.custom': 'Custom',
  'chat.channel.configuration.chat2.templete.color': 'Current color：',
  'chat.channel.configuration.chat2.templete.placeholder':
    'Please select color',
  'chat.channel.configuration.chat2.boxColor': 'Agent chatbox color',
  'chat.channel.configuration.chat2.userBox': 'User chatbox color',
  'chat.channel.configuration.chat2.information.configuration.completed':
    'You have completed the setting of the chatbox appearance, you can click the preview button to preview the style.',
  'chat.channel.configuration.work.panels.checkbox': 'Enable',
  'chat.channel.configuration.chat3.form': 'User information form page',
  'chat.channel.configuration.chat3.form.message':
    'Suggest to open, after opening users need to input basic information first, then can conduct subsequent communication',
  'chat.channel.configuration.chat3.welcome': 'Initial greeting',
  'chat.channel.configuration.chat3.welcome.words': 'A brief greeting',
  'chat.channel.configuration.chat3.welcome.words.placeholder':
    'Please enter welcome message',
  'chat.channel.configuration.chat3.interval.placeholder':
    'Please enter the automatic invitation session time',

  'chat.channel.configuration.chat3.welcome.words.message':
    'Here settings please correspond to the language you selected in the first step',
  'chat.channel.configuration.chat3.welcome.QA': 'Trigger FAQ',
  'chat.channel.configuration.chat3.welcome.QA.placeholder':
    'Please select the corresponding FAQ.',
  'chat.channel.configuration.chat3.welcome.QA.message':
    'FAQ allows you to reply multiple answers to users at once, not only supporting text, but also supporting pictures and videos. If you select a certain FAQ, when users contact you, the system will automatically reply with the standard answer configured in the FAQ. If you have not set up any FAQ, you can click',
  'chat.channel.configuration.chat3.welcome.QA.message.1': ' here ',
  'chat.channel.configuration.chat3.welcome.QA.message.2': 'to set it up.',

  'chat.channel.configuration.chat3.talk': 'Auto popup chat',
  'chat.channel.configuration.chat3.talk.ge': 'Interval',
  'chat.channel.configuration.chat3.talk.ge2':
    'seconds, the system will automatically pop up the chatbox',
  'chat.channel.configuration.chat3.message':
    'When customers browse your website, the system will automatically trigger a chat window to actively invite customers for consultation.',
  'chat.channel.configuration.chat3.voice.message':
    'Customers can click on the video button below the chat window to engage in voice communication with the agent.',
  'chat.channel.configuration.chat3.voice': 'Online voice communication',
  'chat.channel.configuration.chat3.video.message':
    'Customers can click on the video button below the chat window to engage in video communication with the agent.',
  'chat.channel.configuration.chat3.video': 'Online video communication',
  'chat.channel.configuration.chat3.evaluate.message':
    'After ending the chat, the system automatically pops up a satisfaction evaluation for the agent.',
  'chat.channel.configuration.chat3.evaluate': 'Satisfaction evaluation',
  'chat.channel.configuration.chat3.information.configuration.completed':
    'You have completed the basic functionality settings, these will be implemented in the chatbox after you save',
  'chat.channel.configuration.chat4.mode.message': `Choose "Intelligent customer service", there will be a robotic response to customer queries first, if the chatbot cannot answer, users can switch to human agent service at any time.`,
  'chat.channel.configuration.chat4.mode.message.1': ` Choose "Only agent", only human agents will answer customer questions.`,
  'chat.channel.configuration.chat4.mode.message.2': `Choose "Only chatbot", only robotic customer service will answer customer questions.`,
  'chat.channel.configuration.chat4.mode.1': 'Intelligent customer service',
  'chat.channel.configuration.chat4.mode.2': 'Only agent',
  'chat.channel.configuration.chat4.mode.3': 'Only chatbot',
  'chat.channel.configuration.chat4.mode': 'Customer service mode',
  'chat.channel.configuration.chat4.robot.message':
    "The chatbot name will be displayed above the chatbot's answer",
  'chat.channel.configuration.chat4.robot.placeholder': 'Enter bot name',
  'chat.channel.configuration.chat4.robot': 'Chatbot name',
  'chat.channel.configuration.chat4.language.message': `With this feature turned on, the system will automatically identify the language of the user's input question; if not turned on, it will use the language of the user's browser by default.`,
  'chat.channel.configuration.chat4.language':
    'Automatic language identification',
  'chat.channel.configuration.chat4.document': 'Document knowledge base',
  'chat.channel.configuration.chat4.document.placeholder':
    'Please select a document knowledge base',
  'chat.channel.configuration.chat4.document.message.1':
    'This document knowledge base only displays external knowledge bases, please configure the knowledge base on the ',

  'chat.channel.configuration.chat4.document.message':
    ' Document knowledge base',
  'chat.channel.configuration.chat4.document.message.2': 'page.',
  'chat.channel.configuration.chat4.ai.message':
    'You can configure whether to enable Generative AI.',
  'chat.channel.configuration.chat4.ai': 'Integrating Generative AI',
  'chat.channel.configuration.chat4.workers': `Show "Transfer to agent" button for unknown questions`,
  'chat.channel.configuration.chat4.workers.message': `If enabled, when the chatbot does not know the answer, it will automatically display a "Transfer to agent" button below the chatbot's response content`,
  'chat.channel.configuration.chat4.unknown':
    "Chatbot's response when encountering unknown questions",
  'chat.channel.configuration.chat4.unknown.placeholder':
    "Please enter the chatbot's reply to unknown questions",
  'chat.channel.configuration.chat4.unknown.message':
    "This setting is the chatbot's response when encountering unknown questions",
  'chat.channel.configuration.chat4.information.configuration.completed':
    'You have completed the setup of the intelligent customer service, these will be implemented in the chatbox after you save.',
  'chat.channel.configuration.chat5.message':
    'Copy the following code and insert it within the <body> </body> tag on your website.',
  'chat.channel.configuration.chat5.message.link':
    'Chat link: Copy the following link into your website code',
  'live.chat.title': 'Chatbox preview area',
  'live.chat.title.subtitle': 'Here you can preview the chatbox effect',
  'live.chat.customer': 'Customer',
  'live.chat.customer.Dialogue':
    'Can you let me know what are the main features of the product?',
  'live.chat.submit': "Let's chat",
  'live.chat.end': 'End of Conversation',
  'live.chat.video': 'Video Call',
  'chat.channel.configuration.cancel.btn': 'Cancel',
  'chat.channel.configuration.next.btn': 'Next step',
  'chat.channel.configuration.complete.btn': 'Finished',
  'chat.channel.configuration.title.knowledge_unknown_reply':
    'With my current skills, I am unable to answer the question you posed. If needed, you can directly choose our human agent for more professional support😊',
  'chat.channel.configuration.chat5.end': `Please note: After integrating the above code into your website, please contact the "ConnectNow" administrator to add the specified domains to the whitelist. The chat component will only display correctly once the whitelist configuration is complete.`,
  'chat.channel.configuration.chat5.end.1': ` `,
  'chat.channel.configuration.channel.name.web': 'Website domain',
  'chat.channel.configuration.channel.name.placeholder.web':
    'Please enter website domain name',
  'chat.channel.configuration.chat4.workers.content':
    "I'm sorry, I cannot answer this question for you, please contact customer support.",
  'chat.channel.configuration.chat4.workers.position': 'Location',
  'chat.channel.configuration.chat4.workers.zhuan': 'Transfer to Agent',
  'live.chat.customer.Dialogue.product':
    'Which product would you like to know?',
  'chat.channel.configuration.chat4.workers.position.zhuan':
    'Transfer to Agent',
  'chat.channel.configuration.chat5.message.Settings': 'Deployment settings',
  'chat.channel.configuration.channel.name.placeholder.error':
    'Can only enter Chinese characters, uppercase and lowercase letters, numbers, "-" and "_"',
  'chat.channel.configuration.channel.chatBoxName.placeholder.error':
    'Only Chinese characters, uppercase and lowercase letters, spaces are allowed',
  'chat.channel.configuration.chat1.document.placeholder.language':
    'Trigger FAQ data change Please select again',
  'chat.channel.configuration.channel.website':
    'The website domain name format is as follows: www.connectnow.cn',
  'chat.channel.configuration.channel.website.name.placeholder.error':
    'Please enter the website domain of the rules',
  'chat.channel.configuration.work.panels.checkbox.ccp':
    'Whether to enable language recognition',
  'chat.channel.configuration.chat3.talk.Input': 'Auto popup message',
  'chat.channel.configuration.chat3.talk.Input.placeholder':
    'Please enter the auto-invite conversation welcome message',
  'chat.channel.configuration.chat3.talk.Input.message':
    'Set the welcome message displayed when automatic invitation for chat pops up here.',
  'chat.channel.configuration.title.pop_welcome_msg':
    'Hello, I am the ConnectNow intelligent customer service, is there anything I can help you with?',
  'chat.channel.configuration.chat4.workers.keyword.message':
    'When customers enter this keyword, the system will automatically transfer to a human customer service agent.',
  'chat.channel.configuration.chat4.workers.keyword':
    'Transfer to Agent Keyword',
  'chat.channel.configuration.chat4.document.placeholder.keyword':
    'Please input at least one keyword',

  'wx.program.channel.configuration.title':
    'Add Wechat Mini Program chat channel',
  'wx.program.channel.configuration.title.update':
    'Update Wechat Mini Program chat channel',
  'wx.program.channel.configuration.tips':
    'Simplify customer interactions via Wechat Mini Program, enabling faster and more effective responses to their inquiries.',
  'shopify.channel.configuration.title': 'Add Shopify chat channel',
  'shopify.channel.configuration.title.update': 'Update Shopify chat channel',
  'shopify.channel.configuration.tips':
    'Simplify customer interactions via Shopify website, enabling faster and more effective responses to their inquiries.',

  'chat.channel.configuration.chat1.ai.aiagent': 'AI Agent',
  'chat.channel.configuration.chat1.ai.aiagent.tips':
    'After enabling the AI Agent, you need to go to the AI Agent page to configure the welcome message and select the AIGC knowledge base.',
  'chat.channel.configuration.chat2.agent.avac': 'Default agent avatar',
  'chat.channel.configuration.chat2.customer.avac': 'Default customer avatar',
  'chat.channel.configuration.chat2.robot.avac': 'Robot avatar',
  'chat.channel.configuration.chat2.robot.avac.tips':
    'It will be displayed in the chatbox, it is recommended to upload a 50*50px PNG image',
  'chat.channel.configuration.chat2.agent.font.color': 'Agent chat font color',
  'chat.channel.configuration.chat2.robot.avac.kexuan': '(Optional)',
  'chat.channel.configuration.chat2.customer.font.color':
    'User chat font color',
  'chat.channel.configuration.chat3.history.chat': 'Show history dialogue',
  'chat.channel.configuration.chat3.history.chat.tips':
    'After enabling this, end users will be able to see the previous conversation history.',
  'chat.channel.configuration.chat3.history.chat.num':
    'Number of historical conversations',
  'chat.channel.configuration.chat3.history.chat.num.p':
    'Please select the number of historical conversations to display',
  'chat.channel.configuration.chat3.history.chat.select': 'No restrictions',
  'chat.channel.configuration.chat4.transfor.btn':
    'Show transfer to human button',
  'chat.channel.configuration.chat4.transfor.btn.tips':
    'After enabling, the "Transfer to Agent" button will be displayed on the right side of the chatbox',
  'chat.channel.configuration.chat4.agent.workTime': 'Agent work time',
  'chat.channel.configuration.chat4.agent.workTime.p':
    'Please select the agent work time',
  'chat.channel.configuration.chat4.agent.workTime.no':
    'Response message for non-working hours',
  'chat.channel.configuration.chat4.agent.workTime.no.default': `Sorry, the current time is not within our staff's working hours. Please come back to consult us between 8:00 AM and 6:00 PM.`,
  'chat.channel.configuration.chat4.agent.workTime.no.tips': `Robot's response when a customer requests to speak with an agent outside of working hours.`,

  'chat.channel.configuration.chat1.push.agents.join.chat':
    'Push agents to join the chat',

  // discord渠道
  'channel.allocation.detail.discord.title': 'Discord channel configuration',
  'add.discord.channel.configuration.title': 'Add Discord channel',
  'editor.discord.channel.configuration.title': 'Edit Discord channel',
  'add.discord.channel.configuration.tips': 'Please add a Discord channel',
  'discord.channel.configuration.title.1': 'Configure Discord Bot Token',
  'discord.channel.configuration.title.tips.1':
    'Enter the Bot Token from the Discord Developer Portal into the input field below',
  'discord.channel.configuration.title.tips.2': 'View configuration document',
  'discord.channel.allocation.complete.1':
    'You have successfully entered the Bot Token, please proceed to complete the chatbot configuration',
  'discord.channel.configuration.channel.bot.name': 'Bot name',
  'discord.channel.configuration.channel.bot.name.placeholder':
    'Please enter the bot name',
  'discord.channel.configuration.channel.bot.token': 'Bot Token',
  'discord.channel.configuration.channel.bot.token.placeholder':
    'Please enter the Bot Token',
  'channel.allocation.detail.input.bot.name': 'Bot name:',
  'channel.allocation.detail.input.bot.name.placeholder':
    'Enter a bot name and press Enter to search',
  'discord.channel.configuration.channel.application.id': 'Application ID:',
  'discord.channel.configuration.channel.application.id.placeholder':
    'Please enter the Application ID',

  // discord帮助文档
  'channel.allocation.discord.document.title':
    'Discord: Integrate with ConnectNow',
  'channel.allocation.discord.document.h1': 'Prepare a Discord server',
  'channel.allocation.discord.document.step.1': ' Prepare a Discord account',
  'channel.allocation.discord.document.step.1.text': `If you don't have a Discord account, please visit <a>https://discord.com/</a> to register`,
  'channel.allocation.discord.document.step.1.text.1':
    ' If you already have a Discord account, please proceed directly to step two',
  'channel.allocation.discord.document.step.2': ' Prepare a Discord server',
  'channel.allocation.discord.document.step.2.text':
    'Note: If you already have a Discord server, please skip this step',
  'channel.allocation.discord.document.step.2.text.1': ' Click Create a Server',
  'channel.allocation.discord.document.step.2.text.2': ' Select Create My Own',
  'channel.allocation.discord.document.step.2.text.3': `Select your server information based on your needs. If you're not sure, you can also click "Skip this question"`,
  'channel.allocation.discord.document.step.2.text.4':
    'Enter your server name, upload a logo, and then click Create',
  'channel.allocation.discord.document.step.3': ' Create an application',
  'channel.allocation.discord.document.step.3.text':
    'Visit the Developer Portal <a>https://discord.com/developers/applications</a> and click New Application',
  'channel.allocation.discord.document.step.3.text.1':
    'Enter the application name and click Create',
  'channel.allocation.discord.document.step.3.text.2':
    'After creation is complete, set up your application information',
  'channel.allocation.discord.document.step.3.text.3':
    'Click Bot in the left menu',
  'channel.allocation.discord.document.step.3.text.4':
    'Configure Bot permissions. Follow the image below to configure permissions, then click the [Save Changes] button',
  'channel.allocation.discord.document.step.3.text.5':
    'Authorize. Click OAuth2 in the left menu, scroll down, check Bot, then scroll down and sequentially check Send Messages, Send Messages in Threads, Manage Messages, Read Message History, and View Channels. See the two images below',
  'channel.allocation.discord.document.step.3.text.6':
    'After making selections, an authorization link will be generated at the bottom of the page. Click to copy it and open it in a browser',
  'channel.allocation.discord.document.step.3.text.7':
    'After opening the link, as shown below, select the server to add the bot to, click Continue, and then click the Authorize button on the next step',
  'channel.allocation.discord.document.step.3.text.8':
    'Bot successfully added to the server',
  'channel.allocation.discord.document.step.4':
    ' Integrate the bot into ConnectNow',
  'channel.allocation.discord.document.step.4.text':
    'Get the Bot Token. Visit the Developer Portal <a>https://discord.com/developers/applications</a>, click Bot in the left menu, and click Reset Token',
  'channel.allocation.discord.document.step.4.text.1':
    'After generation, click Copy to save the Token',
  'channel.allocation.discord.document.step.4.text.6':
    'Get the Application ID. This field is very important, an error will cause the channel to not receive messages!',
  'channel.allocation.discord.document.step.4.text.2':
    'Go to the ConnectNow admin, click Channels -> Discord -> Add Channel, enter the Token you just saved and the bot name, then click Next',
  'channel.allocation.discord.document.step.4.text.3':
    'Set the chatbot parameters according to your needs, then click Next',
  'channel.allocation.discord.document.step.4.text.4': 'Enter channel name',
  'channel.allocation.discord.document.step.4.text.5':
    '🎉Congratulations! Your Discord has been successfully integrated with ConnectNow',
};
