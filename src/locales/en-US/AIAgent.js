export default {
  'worktable.notification.chat.title': '「ConnectNow」chat reminder',
  'worktable.notification.chat.content': 'Customer message: ',
  'worktable.notification.call.title': '「ConnectNow」incoming call reminder',
  'worktable.notification.call.content':
    'Customer {number} is calling, please answer at the workstation',
  'beta.tip':
    'This feature is currently in the beta testing phase. If you have any usage requirements, please contact your account manager. Thank you for your support and cooperation!',
  'ai.agent.channnel': 'Channel',
  'ai.agent.channnel.options': 'Choose the applicable channel type',
  'ai.agent.copy.fail': 'AI Agent is empty, copy failed!',
  'ai.agent.var': 'Variable management',
  'ai.agent.var.add': 'Add variables',
  'ai.agent.var.select': 'Select type',
  'ai.agent.var.setting.content.title':
    "Active reminder when customer doesn't reply to messages",
  'ai.agent.var.name.rules':
    'Variable name cannot be in Chinese and have a maximum length of 200!',

  'ai.agent.verification.start':
    'There is at least one node connected to the start node!',
  'ai.agent.verification.conditionIntent':
    'The intent recognition component can only be associated with the text component in the question!',
  'ai.agent.verification.node': `The {formName} content of node {nodeName} cannot be empty!`,

  'ai.agent.var.setting.content.tip':
    'This setting is for the robot to actively remind customers when they do not respond to messages.',
  'ai.agent.var.tabs.1': 'Current agent variables',
  'ai.agent.var.tabs.2': 'Current session variables',
  'ai.agent.var.tabs.3': 'Global variable',
  'ai.agent.var.tabs.4': 'System built-in variables',
  'ai.agent.var.table.1': 'Variable name',
  'ai.agent.var.table.2': 'Data types',
  'ai.agent.var.table.3': 'Default value',
  'ai.agent.var.table.4': 'Operation',
  'ai.agent.var.table.select.option.1': 'String',
  'ai.agent.var.table.select.option.2': 'Number',
  'ai.agent.var.table.select.option.3': 'JSON',
  'ai.agent.var.table.select.option.4': 'Array',

  'ai.agent.back.title': 'Unsaved prompt',
  'ai.agent.back.title.content':
    'The current agent has been modified, do you want to save the changes?',
  'ai.agent.back.button.no': 'Do not save',

  'ai.agent.nodes.start': 'Start node',
  'ai.agent.nodes.header.tips.status.1': 'Not deployed',
  'ai.agent.nodes.header.tips.status.2': 'Not saved',
  'ai.agent.nodes.header.tips.status.3': 'Deployed',
  'ai.agent.nodes.header.tips.save':
    'The latest version has been saved but not yet deployed.',
  'ai.agent.nodes.header.test': 'Test',
  'ai.agent.nodes.header.test.tips': 'Please save first before testing.',
  'ai.agent.testAgent.endTest': 'Ai Agent is completed',
  'ai.agent.nodes.header.shareCode': 'Enter share code',
  'ai.agent.nodes.header.tips.shortcut.title':
    'Tip: <b>Selecting Multiple Components</b>',
  'ai.agent.nodes.header.tips.shortcut.1':
    '• Hold Shift and drag to select components in an area',
  'ai.agent.nodes.header.tips.shortcut.2':
    '• Hold Command (Ctrl on Windows) and click to select individual components',

  'ai.agent.nodes.header.save': 'Save and deploy',
  'ai.agent.nodes.header.save.success': 'Deployment successful',

  'ai.agent.enable.confirm.title': 'Enable Current Agent',
  'ai.agent.enable.confirm.content':
    'Enable the current agent? Once enabled, the current agent will start running.',
  'ai.agent.enable.confirm.confirm': 'Confirm Enable',
  'ai.agent.enable.confirm.cancel': 'Cancel',
  'ai.agent.enable.success': 'Enabled successfully',

  'ai.agent.nodes.header.save.error.noIntent':
    'Please select an intent before deploying.',
  'ai.agent.nodes.start.noDelete': 'The start node cannot be deleted!',
  'ai.agent.nodes.start.illegal':
    'Please select valid components for operation.',
  'ai.agent.nodes.form.name': 'Component library',
  'ai.agent.nodes.form.name.small': 'Components',
  'ai.agent.nodes.form.search': 'Search',
  'ai.agent.nodes.form.message': 'Bot Message',
  'ai.agent.nodes.form.question': 'Ask Question',
  'ai.agent.nodes.form.tools': 'Tools',
  'ai.agent.nodes.form.condition': 'Condition',

  'ai.agent.nodes.form.popup.p': 'Please enter the component name...',
  'ai.agent.nodes.form.popup.messageText.title': 'Bot Message',
  'ai.agent.nodes.form.popup.messageImage.radio.1': 'Upload image',
  'ai.agent.nodes.form.popup.messageImage.radio.2': 'Add URL',
  'ai.agent.nodes.form.popup.messageImage.input.url': 'Enter URL',
  'ai.agent.nodes.form.popup.messageImage.input.url.p': 'Please enter the URL',
  'ai.agent.nodes.form.popup.messageImage.input.alt': 'Image description',
  'ai.agent.nodes.form.popup.messageImage.input.alt.p':
    'Please enter a description',
  'ai.agent.nodes.form.popup.messageImage.input.note':
    'Note: The description will also be presented to your customer.',
  'ai.agent.nodes.form.popup.messageImage.input.upload':
    'The file size must not exceed 10MB.',
  'ai.agent.nodes.form.popup.messageImage.input.upload.btn': 'Click to upload',

  'ai.agent.nodes.form.popup.MessageVideo.radio.1': 'Upload video',
  'ai.agent.nodes.form.popup.MessageVideo.radio.2': 'Add URL',
  'ai.agent.nodes.form.popup.MessageVideo.input.url': 'Enter URL',
  'ai.agent.nodes.form.popup.MessageVideo.input.url.p': 'Please enter the URL',
  'ai.agent.nodes.form.popup.MessageVideo.input.alt': 'Video description',
  'ai.agent.nodes.form.popup.MessageVideo.input.alt.p':
    'Please enter a description',
  'ai.agent.nodes.form.popup.MessageVideo.input.note':
    'Note: The description will also be presented to your customer.',
  'ai.agent.nodes.form.popup.MessageVideo.input.upload':
    'The file size must not exceed 100MB.',
  'ai.agent.nodes.form.popup.MessageVideo.input.upload.btn': 'Click to upload',

  'ai.agent.nodes.form.popup.MessageDoc.radio.1': 'Upload document',
  'ai.agent.nodes.form.popup.MessageDoc.radio.2': 'Add URL',
  'ai.agent.nodes.form.popup.MessageDoc.input.url': 'Enter URL',
  'ai.agent.nodes.form.popup.MessageDoc.input.url.p': 'Please enter the URL',
  'ai.agent.nodes.form.popup.MessageDoc.input.alt': 'Document description',
  'ai.agent.nodes.form.popup.MessageDoc.input.alt.p':
    'Please enter a description',
  'ai.agent.nodes.form.popup.MessageDoc.input.note':
    'Note: The description will also be presented to your customer.',
  'ai.agent.nodes.form.popup.MessageDoc.input.upload':
    'The file size must not exceed 20MB.',
  'ai.agent.nodes.form.popup.MessageDoc.input.upload.btn': 'Click to upload',

  'ai.agent.nodes.startNode.select.title.1': 'Trigger method',
  'ai.agent.nodes.startNode.select.title.1.options.1': 'Intent trigger',
  'ai.agent.nodes.startNode.select.title.1.p': 'Please select a trigger method',
  'ai.agent.nodes.startNode.select.title.2': 'Choose an intent',
  'ai.agent.nodes.startNode.select.title.2.p': 'Please select a intent',
  'ai.agent.nodes.startNode.var': 'Passing variables',
  'ai.agent.nodes.startNode.var.table.1': 'Dynamic intent variables',
  'ai.agent.nodes.startNode.var.table.2': 'Current agent variables',
  'ai.agent.nodes.startNode.var.table.add.warn':
    'Please save the data being edited first',

  'ai.agent.nodes.ToolToAgent.title.1': 'Default message',
  'ai.agent.nodes.ToolToAgent.title.2': 'Assignment rules',
  'ai.agent.nodes.ToolToAgent.title.3': 'Set ticket type',
  'ai.agent.nodes.ToolToAgent.title.4': 'Set the priority of the ticket',
  'ai.agent.nodes.ToolToAgent.title.5': 'Set inbound intent',
  'ai.agent.nodes.ToolToAgent.title.6': 'Set customer tags',
  'ai.agent.nodes.ToolToAgent.title.span': '(Optional)',
  'ai.agent.nodes.ToolToAgent.radio.title.1': 'Automatic allocation',
  'ai.agent.nodes.ToolToAgent.radio.title.2': 'Manually specified',
  'ai.agent.nodes.ToolToAgent.select.rule.p': 'Select the allocation method',
  'ai.agent.nodes.ToolToAgent.select.agent': 'Select agents: ',
  'ai.agent.nodes.ToolToAgent.select.team': 'Select teams: ',
  'ai.agent.nodes.ToolToAgent.select.agent.p': 'Please select the agents',
  'ai.agent.nodes.ToolToAgent.select.team.p': 'Please select the teams',
  'ai.agent.nodes.ToolToAgent.select.type': 'Please select the ticket type',
  'ai.agent.nodes.ToolToAgent.select.priority':
    'Please select the priority of the ticket',
  'ai.agent.nodes.ToolToAgent.input.default':
    'Transferring you to a live agent',

  'ai.agent.nodes.ToolFailure.text': 'Failed to return',

  'ai.agent.nodes.ToolLLM.title.1': 'Prompt',
  'ai.agent.nodes.ToolLLM.checkBox.1': 'Displayed as generated by AIGC',
  'ai.agent.nodes.ToolLLM.back.type': 'Result type',
  'ai.agent.nodes.ToolLLM.storage.var': 'Store in a variable',
  'ai.agent.nodes.ToolLLM.storage.var.p': 'Please select variables',
  'ai.agent.nodes.ToolLLM.back.type.1': 'Text',
  'ai.agent.nodes.ToolLLM.contentShow': 'Display the return result',
  //aiagent ToolAPI
  'ai.agent.nodes.ToolAPI.title1': 'Select the API to call',
  'ai.agent.nodes.ToolAPI.title3': 'API management',
  'ai.agent.nodes.ToolAPI.title2.0': 'Note: When the ',
  'ai.agent.nodes.ToolAPI.title2.1': ' HTTP status code',
  'ai.agent.nodes.ToolAPI.title2.2': ' is ',
  'ai.agent.nodes.ToolAPI.title2.3': '200',
  'ai.agent.nodes.ToolAPI.title2.4':
    ', the system will consider the current API call successful; otherwise, the fallback logic will be executed.',
  //aiagent ToolWorkHours
  'ai.agent.nodes.ToolWorkHours.title1': 'Select work time',
  'ai.agent.nodes.ToolWorkHours.workTime': 'In working hours',
  'ai.agent.nodes.ToolWorkHours.notWorkTime': 'Not in working hours',
  //aiagent AskQuestionText
  'ai.agent.nodes.AskQuestionText.title1': 'Customer answer check and store',
  'ai.agent.nodes.AskQuestionText.title2': 'Variable format',
  'ai.agent.nodes.AskQuestionText.title21': 'Verification method',
  'ai.agent.nodes.AskQuestionText.title3': 'Number of retries',
  'ai.agent.nodes.AskQuestionText.title4': 'Failure message',
  'ai.agent.nodes.AskQuestionText.title5': 'Final failure message',
  'ai.agent.nodes.AskQuestionText.title6': 'Validate the regular expression',
  'ai.agent.nodes.AskQuestionText.date.title1': 'Select Date and Time Range',
  'ai.agent.nodes.AskQuestionText.date.to': 'to',
  'ai.agent.nodes.AskQuestionText.date.select1': 'Custom',
  'ai.agent.nodes.AskQuestionText.date.select2': 'Include Dates Before Today',
  'ai.agent.nodes.AskQuestionText.date.select3': 'Include Dates After Today',
  'ai.agent.nodes.AskQuestionText.int.title1': 'Value range',
  'ai.agent.nodes.AskQuestionText.reg.title1':
    'Please enter a regular expression',
  'ai.agent.nodes.AskQuestionText.llm.title1':
    'Please describe the variable check method',
  'ai.agent.nodes.AskQuestionText.SaveValue': 'Store in a variable',
  'ai.agent.nodes.AskQuestionText.None': 'None',
  'ai.agent.nodes.AskQuestionText.Regex': 'Regex',
  'ai.agent.nodes.AskQuestionText.checkType1': 'Regular check',
  'ai.agent.nodes.AskQuestionText.checkType2': 'LLM check',
  //aiagent AskQuestionButton
  'ai.agent.nodes.AskQuestionButton.title1': 'Buttons',
  'ai.agent.nodes.AskQuestionButton.title2': 'Button arrangement',
  'ai.agent.nodes.AskQuestionButton.type1': 'Vertical',
  'ai.agent.nodes.AskQuestionButton.type2': 'Horizontal',
  'ai.agent.nodes.AskQuestionButton.addButton': 'Add button',
  //aiagent AskQuestionForm
  'ai.agent.nodes.AskQuestionForm.title1': 'Attribute name',
  'ai.agent.nodes.AskQuestionForm.title2': 'Display name',
  'ai.agent.nodes.AskQuestionForm.title3': 'Attribute input hint',
  'ai.agent.nodes.AskQuestionForm.title4': 'Select the attribute form',
  'ai.agent.nodes.AskQuestionForm.title5': 'Attribute value',
  'ai.agent.nodes.AskQuestionForm.title6': 'Add value',
  'ai.agent.nodes.AskQuestionForm.attributeType.1': 'Single line input box',
  'ai.agent.nodes.AskQuestionForm.attributeType.2': 'Multi-line input box',
  'ai.agent.nodes.AskQuestionForm.attributeType.3':
    'Single select dropdown box',
  'ai.agent.nodes.AskQuestionForm.attributeType.4': 'Multi-select dropdown box',
  'ai.agent.nodes.AskQuestionForm.attributeType.5':
    'Single select radio button',
  'ai.agent.nodes.AskQuestionForm.attributeType.6': 'Multi-select radio button',
  'ai.agent.nodes.AskQuestionForm.attributeType.7': 'Date (to year-month-day)',
  'ai.agent.nodes.AskQuestionForm.attributeType.8':
    'Date (to hour-minute-second)',
  'ai.agent.nodes.AskQuestionForm.attributeType.9':
    'Date range selection (to year-month-day)',
  'ai.agent.nodes.AskQuestionForm.attributeType.10':
    'Date range selection (to hour-minute-second)',
  'ai.agent.nodes.AskQuestionForm.attributeType.11': 'File upload',
  'ai.agent.nodes.AskQuestionForm.addForm': 'Add another variable',
  'ai.agent.nodes.AskQuestionForm.addForm.button1': 'Submit',
  'ai.agent.nodes.AskQuestionForm.addForm.button2': 'Cancel',
  'ai.agent.nodes.AskQuestionForm.show.upload': 'Click to upload',
  'ai.agent.nodes.AskQuestionForm.show.upload.tips':
    'Upload files, up to 5 files, each file up to 20MB',
  //aiagent AskQuestionLLM
  'ai.agent.nodes.AskQuestionLLM.tip1': 'Please enter the attribute name',
  'ai.agent.nodes.AskQuestionLLM.tip2': 'Please enter the attribute code',
  'ai.agent.nodes.AskQuestionLLM.title1': 'Attribute name',
  'ai.agent.nodes.AskQuestionLLM.title2': 'Attribute code',
  'ai.agent.nodes.AskQuestionLLM.title3': 'Attribute format requirements',
  'ai.agent.nodes.AskQuestionLLM.title4': 'Store in a variable',
  //aiagent Rag
  'ai.agent.nodes.Rag.knowledgeType': 'Knowledge base type',
  'ai.agent.nodes.Rag.selectKnowledgeTag': 'Select Knowledge tag',
  'ai.agent.nodes.Rag.ragName': 'RAG knowledge base Name',
  'ai.agent.nodes.Rag.reUserAsk': 'Rewrite customer question',
  'ai.agent.nodes.Rag.selectKnowledgeType': 'Select Knowledge base type',
  'ai.agent.nodes.Rag.faqKnoewledge': 'FAQ Knowledge base',
  'ai.agent.nodes.Rag.ragKnoewledge': 'RAG Knowledge base',
  'ai.agent.nodes.Rag.selectRagKnowledgeType': 'Select a RAG knowledge base',
  'ai.agent.nodes.Rag.a&qStyle': 'Bot mode',
  'ai.agent.nodes.Rag.a&qStyle1': 'Professional Mode',
  'ai.agent.nodes.Rag.a&qStyle2':
    'Emotionally Intelligent Mode (Softer Responses)',
  'ai.agent.nodes.Rag.cantUnderstand':
    "Bot's response when encountering unknown questions",
  'ai.agent.nodes.Rag.tip1':
    'By enabling "Emotionally Intelligent Mode", the AIGC Chatbot will deliver softer responses to your customers.',
  'ai.agent.nodes.Rag.tip2':
    "This setting is the bot's response when encountering unknown questions",
  //aiagent Intent
  'ai.agent.nodes.Intent.intentName': 'Intent Name',
  'ai.agent.nodes.Intent.intentJudgmentBasis': 'Intent judgment basis',
  'ai.agent.nodes.Intent.tip':
    "Note: The basis for judgment is AIGC’s determination of whether the current customers's input belongs to the intent. Please fill in carefully.",
  'ai.agent.nodes.Intent.addIntent': 'Add another intent',
  'ai.agent.testAgent': 'Testing bot',
  // 设置等待回复
  'ai.agent.waiting.reply.table.waiting.time': 'Waiting time',
  'ai.agent.waiting.reply.table.reminder.language': 'Reminder message',
  'ai.agent.script.add': 'Add a message',
  'ai.agent.script.time.minute': ' minute ',
  'ai.agent.script.time.second': ' second ',
  'ai.agent.script.time.tips': 'Please enter a number between 0 and 59',
  'ai.agent.script.settings.num.tips': 'The number of settings cannot exceed 5',
  'ai.agent.script.settings.not.empty.tips':
    'Waiting time and reminder message cannot be empty',

  'ai.agent.nodes.start.type.2': 'Welcome AI by default',
  'ai.agent.nodes.start.type.2.content':
    'When customers communicate with enterprises via WEB online chat, APP online chat, Shopify, and WeChat Mini Program, the current welcome AI Agent will be executed first.',
  'ai.agent.nodes.start.type.3': 'Default Fallback Intent',
  'ai.agent.nodes.start.type.3.content': `When the system doesn't match any AI Agent, it will execute the default AI Agent.`,
  //aiagent ToolVariableSetting
  'ai.agent.nodes.ToolVariableSetting.title1': 'Select variable',
  'ai.agent.nodes.ToolVariableSetting.title2': 'Select adjustment rule',
  'ai.agent.nodes.ToolVariableSetting.title3': 'Enter prefix content',
  'ai.agent.nodes.ToolVariableSetting.title4': 'Enter suffix content',
  'ai.agent.nodes.ToolVariableSetting.title5': 'Enter specific value',
  'ai.agent.nodes.ToolVariableSetting.title6': 'Enter numeric range',
  'ai.agent.nodes.ToolVariableSetting.title7': 'Enter sum value "1"',
  'ai.agent.nodes.ToolVariableSetting.title8': 'Enter sum value "2"',
  'ai.agent.nodes.ToolVariableSetting.title9': 'Enter difference value "1"',
  'ai.agent.nodes.ToolVariableSetting.title10': 'Enter difference value "2"',
  'ai.agent.nodes.ToolVariableSetting.title11': 'Enter product value "1"',
  'ai.agent.nodes.ToolVariableSetting.title12': 'Enter product value "2"',
  'ai.agent.nodes.ToolVariableSetting.title13': 'Enter quotient value "1"',
  'ai.agent.nodes.ToolVariableSetting.title14': 'Enter quotient value "2"',
  'ai.agent.nodes.ToolVariableSetting.title15': 'Enter rounded up value',
  'ai.agent.nodes.ToolVariableSetting.title16': 'Enter rounded down value',
  'ai.agent.nodes.ToolVariableSetting.title17': 'Ticket attributes',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.num1': 'Equal to',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.num2': 'Addition in',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.num3': 'Subtraction in',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.num4': 'Multiply',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.num5': 'Divide by',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.num6': 'Round up',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.num7': 'Round down',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.str1': 'Add prefix',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.str2': 'Add suffix',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.str3': 'Remove spaces',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.str4':
    'Add prefix and Suffix',
  'ai.agent.nodes.ToolVariableSetting.changeSelect.str5': 'Equal to',
  'ai.agent.nodes.ToolVariableSetting.node.title': 'Update variable',

  'ai.agent.nodes.ToolUpdateTicket.title1': 'Customer attributes',
  'ai.agent.nodes.ToolUpdateTicket.title2': 'Customer attribute value',
  'ai.agent.nodes.ToolUpdateTicket.title3': 'Modify other attributes',
  'ai.agent.nodes.ToolUpdateTicket.title4': 'Attribute value',
  'ai.agent.nodes.ToolUpdateTicket.title5': 'Ticket attributes',
  'ai.agent.nodes.ToolUpdateTicket.title6':
    'Attribute value rule: resolved(resolved) terminated(terminated)',
  'ai.agent.nodes.ToolUpdateTicket.title7':
    'Attribute value rule: (P5) low impact (P4) medium impact (P3) severe impact (P2) emergency processing (P1) service crash',
  'ai.agent.nodes.ToolUpdateTicket.title8':
    'Attribute value rules: 1 (Male), 2 (Female), 3 (Other)',
  'ai.agent.nodes.ToolUpdateTicket.title9':
    'Attribute value rules: 1 (VIP user), 2 (Regular user)',
  'ai.agent.nodes.ToolUpdateTicket.title10':
    'Please enter the standard date format, e.g., 1979-04-01',
  'ai.agent.nodes.ToolUpdateTicket.title1005':
    'Please enter the option value (obtainable from ticket extended attribute definitions), to be added in the "Options Tab"; users can select only one option',
  'ai.agent.nodes.ToolUpdateTicket.title1006':
    'Please enter option values (obtainable from ticket extended attribute definitions), separated by commas (,); users can select multiple options',
  'ai.agent.nodes.ToolUpdateTicket.title1007':
    'Attribute value rules: true (enabled) / false (disabled)',
  'ai.agent.nodes.ToolUpdateTicket.title1008':
    'Please enter the standard time format, e.g., 2025-04-03 00:00:00',

  'ai.agent.nodes.ConditionCheck.title1': 'Condition',
  'ai.agent.nodes.ConditionCheck.title2': 'Else',
  'ai.agent.nodes.ConditionCheck.title3': 'Failed',
  'ai.agent.nodes.ConditionCheck.title4': 'If',
  'ai.agent.nodes.ConditionCheck.title5': 'Is',
  'ai.agent.nodes.ConditionCheck.title6': 'Contains',
  'ai.agent.nodes.ConditionCheck.title7': 'Is empty',
  'ai.agent.nodes.ConditionCheck.title8': 'Is not empty',
  'ai.agent.nodes.ConditionCheck.title9': 'Customer input',
  'ai.agent.nodes.ConditionCheck.title10': 'Add condition',
  'ai.agent.nodes.ConditionCheck.title11': 'Greater than',
  'ai.agent.nodes.ConditionCheck.title12': 'Less than',

  'ai.agent.nodes.AskQuestionLLM.addForm': 'Add attribute',
  'ai.agent.nodes.ToolSetCustomerTag.title1': 'Select tag',
  // MessageHotIssue
  'ai.agent.nodes.MessageHotIssue.title1': 'Configuration method',
  'ai.agent.nodes.MessageHotIssue.title1.type1': 'Manual edit',
  'ai.agent.nodes.MessageHotIssue.title1.type2': 'Automatic recommendation',
  'ai.agent.nodes.MessageHotIssue.title2': 'Display format',
  'ai.agent.nodes.MessageHotIssue.title2.type1': 'Horizontal',
  'ai.agent.nodes.MessageHotIssue.title2.type2': 'Vertical',
  'ai.agent.nodes.MessageHotIssue.title3': 'Select initial language',
  'ai.agent.nodes.MessageHotIssue.title4': 'Category name',
  'ai.agent.nodes.MessageHotIssue.title4.tip': 'Enter category name',
  'ai.agent.nodes.MessageHotIssue.title5': 'Question',
  'ai.agent.nodes.MessageHotIssue.title5.tip': 'Enter question',
  'ai.agent.nodes.MessageHotIssue.title6': 'Add question',
  'ai.agent.nodes.MessageHotIssue.title7': 'Add category',
  'ai.agent.nodes.MessageHotIssue.title8': 'Top questions',
  'ai.agent.nodes.MessageHotIssue.title9': 'Automatic recommendation',
  'ai.agent.nodes.MessageHotIssue.title10': 'Smart translation',
  'ai.agent.nodes.MessageHotIssue.title11': 'Target language',
  'ai.agent.nodes.MessageHotIssue.title12': 'Auto-recommend FAQ rule',
  'ai.agent.nodes.MessageHotIssue.title12.tip1': 'Automatically show the last',
  'ai.agent.nodes.MessageHotIssue.title12.tip2': "day's top",
  'ai.agent.nodes.MessageHotIssue.title12.tip3': 'FAQs',
  'ai.agent.nodes.MessageHotIssue.title12.remark':
    'Note: If an FAQ has multiple question variations, only the first one is displayed',

  'ai.agent.nodes.AskQuestionCard.title1': 'Please select your order',
  'ai.agent.nodes.AskQuestionCard.title2': 'Card layout',
  'ai.agent.nodes.AskQuestionCard.title3':
    'This is a sample product title. You can describe the main features of the product here',
  'ai.agent.nodes.AskQuestionCard.title4': '$0.00',
  'ai.agent.nodes.AskQuestionCard.title5': 'Delivered',
  'ai.agent.nodes.AskQuestionCard.title6': 'Carousel',
  'ai.agent.nodes.AskQuestionCard.title7': 'List',
  'ai.agent.nodes.AskQuestionCard.title8': 'Card data',
  'ai.agent.nodes.AskQuestionCard.title9': 'Image URL',
  'ai.agent.nodes.AskQuestionCard.title10': 'Product title',
  'ai.agent.nodes.AskQuestionCard.title11': 'Product price (Optional)',
  'ai.agent.nodes.AskQuestionCard.title12': 'Product quantity (Optional)',
  'ai.agent.nodes.AskQuestionCard.title13': 'Order status (Optional)',
  'ai.agent.nodes.AskQuestionCard.title14': 'Note: This is a json variable',
  'ai.agent.nodes.AskQuestionCard.title15': 'JSON variable format:',
  'ai.agent.nodes.AskQuestionCard.tip1':
    'Retrieve data by calling an API and store it in a JSON variable to use as a list for the card data. Map the properties of each JSON object in the list to the corresponding fields below.',

  'ai.agent.nodes.ToolDelay.title1': 'Delay for x seconds',
  'ai.agent.nodes.header.save.error.formCancle':
    "Ensure the form's cancel node is connected",

  'ai.agent.exit.confirm':
    'You have unsaved changes. Are you sure you want to leave?',
};
