export default {
  'selfConfiguration.title': 'Kon<PERSON>gu<PERSON><PERSON> Evaluasi Chatbot AIGC',
  'selfConfiguration.label.1': 'Aktifkan evaluasi sampling otomatis sistem:',
  'selfConfiguration.label.1.tips':
    'Jika evaluasi mandiri diaktifkan, sistem akan melakukan pemeriksaan sampel berdasarkan pengaturan selanjutnya dan memberikan laporan evaluasi akurasi otomatis.',
  'selfConfiguration.label.2':
    'Atur jumlah pertanyaan untuk disampling dan dievaluasi setiap hari',
  'selfConfiguration.label.2.value': 'Lakukan evaluasi otomatis setiap',
  'selfConfiguration.label.2.value.1': 'pertanyaan',
  'selfConfiguration.label.2.value.2': 'dan',
  'selfConfiguration.label.2.value.3': 'HINGGA',
  'selfConfiguration.label.2.value.4':
    'pertanyaan dapat diekstrak untuk evaluasi otomatis setiap hari',
  'selfConfiguration.label.3': 'Frekuensi evaluasi mandiri',
  'selfConfiguration.label.4': ' ',
  'selfConfiguration.label.tips': 'Silakan pilih waktu',
  'selfReport.title': 'Laporan Evaluasi Chatbot AIGC',
  'selfReport.label.card.1': 'Skor akurasi rata-rata',
  'selfReport.label.card.2': 'Skor akurasi rata-rata di setiap dimensi',
  'selfReport.label.card.3': 'Skor akurasi rata-rata di setiap dimensi',
  'selfReport.label.card.4': 'Grafik tren skor akurasi evaluasi mandiri',
  'selfReport.label.card.5':
    'Grafik tren skor akurasi evaluasi mandiri per dimensi',
  'selfReport.fen': 'poin',
  'selfReport.label.1': 'Skor Kedekatan dengan Fakta',
  'selfReport.label.2': 'Skor Relevansi Jawaban',
  'selfReport.label.3': 'Skor Presisi Konteks',
  'selfReport.label.4': 'Skor Kesamaan Semantik Jawaban',
  'selfReport.label.5': 'Skor Ketepatan Jawaban',
  'selfReport.select.knowledge': 'Basis pengetahuan: ',
  'selfReport.popover.1': `Ini mengukur konsistensi faktual jawaban yang dihasilkan terhadap konteks yang diberikan. Dihitung dari jawaban dan konteks yang diambil. Jawaban diskalakan dalam rentang (0,100). Semakin tinggi semakin baik.`,
  'selfReport.popover.1.1':
    'Jawaban dianggap setia jika semua klaim yang dibuat dalam jawaban dapat disimpulkan dari konteks yang diberikan.',
  'selfReport.popover.2': `Metrik evaluasi, Relevansi Jawaban, berfokus pada penilaian seberapa penting jawaban yang dihasilkan terhadap prompt yang diberikan. Skor yang lebih rendah diberikan untuk jawaban yang tidak lengkap atau mengandung informasi berlebihan, dan skor yang lebih tinggi menunjukkan relevansi yang lebih baik. Metrik ini dihitung menggunakan pertanyaan, konteks, dan jawaban.`,
  'selfReport.popover.3': `Presisi Konteks adalah metrik yang mengevaluasi apakah semua item yang relevan sebenarnya yang ada dalam konteks memiliki peringkat lebih tinggi atau tidak. Idealnya, semua potongan yang relevan harus muncul di peringkat teratas. Metrik ini dihitung menggunakan pertanyaan, kebenaran dasar, dan konteks, dengan nilai antara 0 dan 1, di mana skor yang lebih tinggi menunjukkan presisi yang lebih baik.`,
  'selfReport.popover.4': `Konsep Kesamaan Semantik Jawaban berkaitan dengan penilaian kesamaan semantik antara jawaban yang dihasilkan dan kebenaran. Evaluasi ini didasarkan pada kebenaran dan jawaban, dengan nilai antara 0 hingga 1. Skor yang lebih tinggi menandakan keselarasan yang lebih baik antara jawaban yang dihasilkan dan kebenaran.`,
  'selfReport.popover.5': `Penilaian Ketepatan Jawaban melibatkan pengukuran akurasi jawaban yang dihasilkan dibandingkan dengan kebenaran. Evaluasi ini mengandalkan kebenaran dan jawaban, dengan skor antara 0 hingga 1. Skor yang lebih tinggi menunjukkan keselarasan yang lebih dekat antara jawaban yang dihasilkan dan kebenaran, yang menandakan ketepatan yang lebih baik.`,
  'selfReport.popover.5.5': `Ketepatan jawaban mencakup dua aspek kritis: kesamaan semantik antara jawaban yang dihasilkan dan kebenaran dasar, serta kesamaan faktual. Aspek-aspek ini digabungkan menggunakan skema pembobotan untuk merumuskan skor ketepatan jawaban.`,
};
