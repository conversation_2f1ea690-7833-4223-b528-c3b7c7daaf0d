export default {
  'product.aiAgent.banner.title.first': 'Berikan',
  'product.aiAgent.banner.title.second':
    '<PERSON>anan <PERSON>i yang Dipersonalisasi untuk Pelanggan',
  'product.aiAgent.banner.subtitle':
    'Layanan yang dipersonalisasi adalah salah satu faktor kunci dalam meningkatkan kepuasan pelanggan. AI Agent ConnectNow dapat menganalisis riwayat dan data perilaku setiap pelanggan dengan akurat, memberikan layanan yang disesuaikan.',
  'product.aiAgent.content.title':
    'Mudah Mencapai Peningkatan Kecerdasan dalam Interaksi Pelanggan',
  'product.aiAgent.content.desc':
    'AI Agent dapat dengan cepat menjawab pertanyaan pelanggan, secara cerdas memandu minat, dan membantu dalam transaksi, secara signifikan meningkatkan pengalaman pelanggan dan efisiensi bisnis.',
  'product.aiAgent.card1.title':
    'Penerimaan Pra-Penjualan yang <PERSON>h <PERSON> dan <PERSON> Prospek',
  'product.aiAgent.card1.desc':
    'Tersedia 24/7, den<PERSON> pen<PERSON> ma<PERSON>, pan<PERSON><PERSON> k<PERSON><PERSON>, dan respons yang cepat dan akurat, AI Agent dapat sangat menyerupai staf pra-penjualan manusia, meningkatkan efisiensi perolehan prospek.',
  'product.aiAgent.card2.title':
    'Rekomendasi yang Dipersonalisasi dan Akurat Melalui Teknologi Pembelajaran',
  'product.aiAgent.card2.desc':
    'AI Agent belajar dari perilaku, preferensi, dan kebutuhan pengguna untuk secara otomatis menghasilkan rekomendasi yang dipersonalisasi. Dibandingkan dengan sistem rekomendasi tradisional, AI Agent memiliki tingkat kecerdasan yang lebih tinggi dan kemampuan adaptasi yang lebih kuat, memungkinkannya menangkap kebutuhan pelanggan dengan lebih akurat dan memberikan rekomendasi yang tepat.',
  'product.aiAgent.card3.title':
    'Otomatisasi yang Stabil dan Andal serta Alur Kerja yang Dapat Disesuaikan',
  'product.aiAgent.card3.desc':
    'Mencapai otomatisasi proses bisnis dan bantuan cerdas, membuat proses bisnis tidak hanya stabil dan andal tetapi juga lebih fleksibel dan efisien. Berhasil diterapkan pada manajemen pengetahuan, rekomendasi cerdas, manajemen formulir, pemrosesan pengembalian dan pertukaran, dan banyak lagi.',
};
