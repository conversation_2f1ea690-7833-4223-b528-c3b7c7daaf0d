export default {
  'intention.management.intent.classification': 'Kategori: ',
  'intention.management.intent.classification.placeholder':
    'Silakan pilih kategori intent',
  'intention.management.intent.language': 'Bahasa: ',
  'intention.management.intent.language.placeholder':
    '<PERSON>lakan pilih bahasa intent',
  'intention.management.intent.name': '<PERSON>a intent: ',
  'intention.management.intent.name.placeholder':
    'Silakan masukkan nama intent',
  'intention.management.create.intention.text': 'Buat intent baru',
  'intention.management.user.number': '{num} kali',
  'intention.management.user.number.month.tips':
    'Jumlah pemicu dalam bulan ini',
  'intention.management.intent.request.reason': 'Alasan permintaan intent: ',
  'intention.management.create.information': 'Informasi pembuatan: ',
  'intention.management.create.intelligent.agent': 'Buat Agen AI',
  'intention.management.intelligent.agent': 'Agen AI',
  'intention.management.delete.tips':
    '<PERSON><PERSON><PERSON><PERSON> <PERSON>a yakin ingin menghapus konten ini?',
  'new.intent.title.add': 'Tambah intent',
  'new.intent.title.edit': 'Edit intent',
  'new.intent.intention.basic.information': 'Informasi dasar intent',
  'new.intent.intention.name': 'Nama intent',
  'new.intent.intention.name.maxlength':
    'Panjang tidak boleh melebihi 80 karakter',
  'new.intent.intention.language': 'Bahasa intent',
  'new.intent.intention.classification': 'Kategori intent',
  'new.intent.intention.request.reason': 'Alasan permintaan intent',
  'new.intent.intention.request.reason.placeholder':
    'Silakan masukkan alasan pelanggan memicu intent ini, misalnya pelanggan meminta memeriksa status pesanan',
  'new.intent.intention.reason.maxlength':
    'Panjang tidak boleh melebihi 2000 karakter',
  'new.intent.intention.basic.information.tips':
    'Nama intent dan alasan permintaan intent adalah faktor penting bagi AIGC untuk mengidentifikasi intent pelanggan.',
  'new.intent.intention. language.information': 'Ujaran intent',
  'new.intent.static.language.technique': 'Ujaran statis',
  'new.intent.dynamic.speech.technique': 'Ujaran dinamis',
  'new.intent.static.language.technique.placeholder':
    'Silakan masukkan ujaran intent statis',
  'new.intent.dynamic.speech.technique.placeholder':
    'Silakan masukkan ujaran dinamis',
  'new.intent.dynamic.speech.technique.placeholder2':
    'Silakan masukkan ujaran intent dinamis',
  'new.intent.ai.static.language.technique': 'Hasilkan ujaran intent AI',
  'new.intent.add.static.language.technique': 'Tambah ujaran intent',
  'new.intent.intention.dynamic.speech.technique.tips':
    "Ujaran dinamis dapat mencakup atribut dinamis, yang diformat sebagai {Device}, di mana 'Device' adalah nama atribut dan hanya mendukung bahasa Inggris.",
  'new.ai.generate.intent.intention.language': 'Pilih bahasa ujaran intent',
  'new.intent.intention.language.attribute': 'Atribut ujaran intent',
  'new.intent.intention.language.attribute.name': 'Nama atribut',
  'new.intent.intention.language.attribute.name.placeholder':
    'Silakan masukkan nama atribut',
  'new.intent.intention.language.attribute.example': 'Contoh nilai atribut',
  'new.intent.intention.language.attribute.example.placeholder':
    'Silakan masukkan contoh nilai atribut',
  'new.intent.intention.language.attribute.example.tips':
    'Contoh nilai atribut adalah kunci referensi untuk mengekstrak atribut saat ini. Misalnya, jika atributnya adalah Device, contohnya bisa berupa AC, dehumidifier, dll.',
  'new.intent.intention.attribute.description': 'Deskripsi atribut',
  'new.intent.intention.attribute.description.placeholder':
    'Silakan masukkan deskripsi variabel',
  'new.intent.intention.attribute.mandatory': 'Ini adalah Atribut yang wajib',
  'new.intent.intention.attribute.verify.format':
    'Pilih format validasi atribut',
  'new.intent.intention.attribute.verify.format.placeholder':
    'Silakan pilih format validasi atribut',
  'new.intent.intention.attribute.verify.rule.placeholder':
    'Silakan masukkan regex validasi atribut',
  'new.intent.intention.attribute.verify.rule.maxlength':
    'Panjang tidak boleh melebihi 200 karakter',
  'new.intent.intention.failed.verification.attempts':
    'Percobaan ulang verifikasi yang gagal',
  'new.intent.intention.failed.verification.attempts.placeholder':
    'Silakan masukkan jumlah percobaan verifikasi',
  'new.intent.intention.failed.num.verification.attempts.placeholder':
    'Jumlah maksimum percobaan tidak boleh melebihi 99',
  'new.intent.intention.reply.after.verification.failure':
    'Balasan setelah verifikasi gagal',
  'new.intent.intention.reply.after.verification.failure.placeholder':
    'Silakan masukkan balasan setelah verifikasi gagal',
  'new.intent.intention.reply.after.verification.failure.final':
    'Balasan akhir setelah verifikasi gagal',
  'new.intent.intention.reply.after.verification.failure.final.placeholder':
    'Silakan masukkan balasan akhir setelah verifikasi gagal',
  'new.intent.intelligent.agent.variables': 'Variabel yang disimpan di Agen AI',
  'new.intent.intelligent.agent.variables.placeholder':
    'Silakan pilih variabel untuk disimpan di Agen AI',
  'new.intent.intention.rhetorical.question.not.collecting':
    'Tidak ada pertanyaan retoris yang dikumpulkan untuk variabel saat ini',
  'new.intent.intention.rhetorical.question.not.collecting.placeholder':
    'Silakan masukkan ujaran',
  'new.intent.add.variables': 'Tambah atribut',
  'new.intent.add.cancel.confirm':
    'Membatalkan akan menghapus formulir. Apakah Anda yakin ingin membatalkan?',
  'new.intent.intention.llm.extract.attribute': 'Ekstrak atribut intent LLM',
  'new.intent.llm.extract.attribute.add.variables': 'Tambah Atribut',
  'new.intent.add.success.tips':
    'Selamat! Anda telah berhasil membuat intent. Selanjutnya, Anda dapat membuat Agen AI untuk intent ini.',
  'new.intent.add.static.language.technique.tips':
    'Anda dapat membuat hingga 100 ujaran intent!',
  'new.intent.add.attribute.example.tips':
    'Anda dapat menambahkan hingga empat contoh atribut untuk setiap variabel!',
  'new.intent.add.verbal.variables.tips':
    'Anda dapat membuat hingga 4 variabel ujaran!',
  'new.intent.intention.classification.management': 'Manajemen kategori intent',
  'new.intent.intention.classification.management.placeholder':
    'Silakan masukkan nama kategori intent, tekan Enter untuk mencari atau klik tombol untuk menambahkan.',
  'new.intent.add.variables.only.tips':
    'Variabel yang ditambahkan harus unik dan tidak boleh diulang!',
  'new.intent.add.variables.only.tips.1':
    'Silakan konfigurasikan variabel yang termasuk dalam ujaran untuk {variable}.',
  'new.intent.add.variables.only.tips.2':
    'Ujaran kehilangan variabel {variable}. Silakan tambahkan ujaran yang sesuai.',
  'new.intent.add.variables.only.tips.3': 'Skrip {script} berulang!',
  'new.intent.add.ai.script.create':
    'Nama intent dan alasan permintaan intent tidak boleh kosong!',
  'new.intent.add.script.create.tips':
    'Catatan: Menekan enter akan secara otomatis menghasilkan variabel.',
  'new.intent.intention.classification.tips':
    'Kategori intent tidak boleh kosong!',
  'new.intent.intention.language.attribute.code': 'Kode atribut',
  'new.intent.intention.language.attribute.format.requirement':
    'Persyaratan format atribut',
  'new.intent.intention.language.attribute.is.required': 'Diperlukan',
  'new.intent.intention.language.attribute.operation': 'Operasi',
  'new.intent.intention.language.attribute.is.required.yes': 'Ya',
  'new.intent.intention.language.attribute.is.required.no': 'Tidak',
  'new.intent.intention.language.attribute.code.only.support.english':
    'Hanya bahasa Inggris yang didukung',
  'new.intent.intention.language.attribute.code.only.support.english.message':
    'Kode atribut hanya mendukung bahasa Inggris',
  'new.intent.add.required.one.speech': 'Harus memiliki satu skrip',
  'new.intent.add.dynamic.speech.editing.tips':
    'Silakan simpan variabel atribut intent ujaran dinamis yang sedang diedit',
  // 智能体列表
  'external.intelligent.agent.delete.synonym.rules': 'Hapus prompt',
  'external.intelligent.agent.intent.language.placeholder':
    'Silakan pilih nama intent',
  'external.intelligent.agent.name': 'Nama Agen AI:: ',
  'external.intelligent.agent.name.placeholder':
    'Silakan masukkan nama Agen AI',
  'external.intelligent.agent.deployment.status': 'Status: ',
  'external.intelligent.agent.deployment.status.placeholder':
    'Silakan pilih status penyebaran',
  'external.intelligent.agent.tab.chat': 'Obrolan',
  'external.intelligent.agent.tab.email': 'Email',
  'external.intelligent.agent.tab.phone': 'Telepon',
  'external.intelligent.agent.create.text': 'Buat Agen AI baru',
  'external.intelligent.agent.use.rule': 'Aturan penggunaan: ',
  'external.intelligent.agent.deployment.time': 'Waktu penyebaran: ',
  'external.intelligent.agent.delete.tips':
    'Apakah Anda yakin ingin menghapus Agen AI ini?',
  'external.intelligent.agent.deploy.status.1': 'Disebarkan',
  'external.intelligent.agent.deploy.status.2': 'Draf',
  'external.intelligent.agent.default.welcome.message':
    'Ketika pelanggan berkomunikasi dengan perusahaan melalui obrolan online WEB, obrolan online APP, Shopify, dan Program Mini WeChat, Agen AI selamat datang saat ini akan dijalankan terlebih dahulu.',
  'external.intelligent.agent.fallback':
    'Ketika sistem tidak mencocokkan Agen AI apa pun, ia akan menjalankan Agen AI default.',
  'intelligent.agent.deployed.text': 'Disebarkan, {deployName}',
  'external.intelligent.agent.share.success': 'Berhasil disalin',
  'external.intelligent.agent.share.tips':
    'Catatan: Kode berbagi ini berlaku selama 24 jam. Silakan gunakan sebelum {expiryTime}.',
  'external.intelligent.agent.share': 'Bagikan',
  'external.intelligent.agent.share.content':
    '【ConnectNow】Anda telah menerima berbagi Agen AI dari "{username}". Kode pengalaman eksklusif Anda adalah: "「{shareCode}」". Kode ini berlaku selama 24 jam (sebelum {expiryTime}).',
  'external.intelligent.agent.share.confirm': 'Konfirmasi',
  'external.intelligent.agent.share.copy': 'Salin',
  'external.intelligent.agent.share.cancel': 'Batal',
  // 新建智能体
  'external.intelligent.agent.create.title': 'Buat Agen AI baru',
  'external.intelligent.agent.create.add': 'Buat Agen AI baru',
  'external.intelligent.agent.create.basic.information': 'Informasi dasar',
  'external.intelligent.agent.create.name': 'Silakan masukkan nama Agen AI',
  'external.intelligent.agent.create.channel.type':
    'Jenis saluran yang berlaku',
  'external.intelligent.agent.create.select.scenario': 'Pilih skenario Agen AI',
  'external.intelligent.agent.phone.voice': 'Telepon/Suara',
  'external.intelligent.agent.create.select.channel.type':
    'Silakan pilih jenis saluran yang berlaku',
  'external.intelligent.agent.create.select.channel.type.tips':
    'Setidaknya satu jenis saluran harus dipilih!',
  'external.intelligent.agent.create.facebook.message': 'Pesan Facebook',
  'marketing.channel.type.web.voice': 'Suara WEB',
  'marketing.channel.type.app.voice': 'Suara APP',
};
