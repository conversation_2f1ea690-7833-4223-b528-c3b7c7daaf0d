export default {
  'marketing.agent.tab.whatsapp': 'WhatsApp',
  'marketing.agent.search.btn': '查询',
  'marketing.agent.activity.name': '活动名称：',
  'marketing.agent.add.title': '添加WhatsApp营销智能体',
  'marketing.agent.select.activity': '选择营销活动',
  'marketing.agent.name': '营销智能体名称',
  'marketing.agent.select.channel': '选择营销渠道',
  'marketing.agent.select.whatsapp.channel': '选择WhatsApp渠道',
  'marketing.agent.interaction.type': '交互方式',
  'marketing.agent.interaction.type.one.time': '一次性营销',
  'marketing.agent.cancel': '取消',
  'marketing.agent.confirm': '确定',
  'marketing.agent.activity.select.name': '请选择活动名称',
  // 测试相关
  'marketing.test.modal.title': 'WhatsApp营销测试',
  'marketing.test.phone.label': '请输入测试的手机号（最多5个）',
  'marketing.test.phone.placeholder': '请输入手机号',
  'marketing.test.phone.required': '请至少输入一个手机号',
  'marketing.test.phone.invalid': '请输入有效的手机号格式',
  'marketing.test.add.phone': '添加手机号',
  'marketing.test.cancel': '取消',
  'marketing.test.send': '发送',
  'marketing.test.send.success': '测试消息发送成功',
  'marketing.test.send.error': '发送失败，请重试',

  'marketing.agent.create.step.basic.info': '营销基本信息',
  'marketing.agent.create.title': '添加WhatsApp营销智能体',
  'marketing.agent.create.step.target.customer': '目标客户&营销时间',
  'marketing.agent.create.cancel': '取消',
  'marketing.agent.create.next': '下一步',
  'marketing.agent.create.finish': '保存',

  'marketing.agent.edit.title': '修改WhatsApp营销智能体',

  'marketing.agent.status.start': '立即开始',
  'marketing.agent.status.pause': '立即暂停',
  'marketing.agent.status.end': '活动已结束',

  'marketing.agent.activity.name': '活动名称：',
  'marketing.agent.channel.name': '渠道名称：',

  'marketing.agent.whatsapp.marketing': 'WhatsApp营销',

  // 模版
  'marketing.template.select': '请选择模版',
  'marketing.template.empty': '请选择模版',

  // 变量编辑器
  'marketing.variable.need.replace': '需要替换的变量',
  'marketing.variable.replace.input': '替换变量',
  'marketing.variable.select.placeholder': '请输入或选择变量',
  'marketing.variable.header.title': '替换变量（Header）',
  'marketing.variable.body.title': '替换变量（Body）',
  'marketing.variable.buttons.title': '替换变量（Buttons）',
  'marketing.variable.no.variables': '当前模版无需要替换的变量',

  // whatsapp 营销详情
  'marketing.results.table.send.failure': '发送失败',
  'marketing.results.marketing.agent.name': '营销智能体：',
  'marketing.results.marketing.agent.name.placeholder': '请选择营销智能体',
  'marketing.test.phone.placeholder': '请输入手机号',

  // StartNode 组件
  'marketing.start.node.customer.segment.select': '选择客户细分',
  'marketing.start.node.customer.segment.add': '添加客户细分',
  'marketing.start.node.customer.segment.placeholder': '请选择客户细分',
  'marketing.start.node.customer.segment.one': '客户细分一',
  'marketing.start.node.customer.segment.two': '客户细分二',
  'marketing.start.node.customer.segment.high.value': '高价值客户',
  'marketing.start.node.customer.segment.potential': '潜在客户',
  'marketing.start.node.send.frequency.select': '选择发送频率',
  'marketing.start.node.send.frequency.immediate': '立即发送',
  'marketing.start.node.send.frequency.daily': '每日发送',
  'marketing.start.node.send.frequency.weekly': '每周发送',
  'marketing.start.node.send.frequency.monthly': '每月发送',
  'marketing.start.node.send.frequency.custom': '自定义',
  'marketing.start.node.send.time.select': '选择发送时间：',
  'marketing.start.node.send.time.add': '添加发送时间',
  'marketing.start.node.timezone.select': '选择时区：',
  'marketing.start.node.timezone.recipient': '使用收件人当地时区',
  'marketing.start.node.timezone.fallback':
    '无法识别收件人时区时，使用此时区：',
  'marketing.start.node.timezone.utc0': 'UTC+0',
  'marketing.start.node.timezone.utc8': 'UTC+8 (北京时间)',
  'marketing.start.node.timezone.utc5': 'UTC-5 (东部时间)',
  'marketing.start.node.timezone.utc8.pacific': 'UTC-8 (太平洋时间)',
  'marketing.start.node.week.monday': '周一',
  'marketing.start.node.week.tuesday': '周二',
  'marketing.start.node.week.wednesday': '周三',
  'marketing.start.node.week.thursday': '周四',
  'marketing.start.node.week.friday': '周五',
  'marketing.start.node.week.saturday': '周六',
  'marketing.start.node.week.sunday': '周日',
  'marketing.start.node.month.text': '每月',
  'marketing.start.node.day.text': '日',
  'marketing.start.node.time.placeholder': '选择时间',
  'marketing.start.node.note': '注: 发送时间将会在活动时间范围内',
  'marketing.start.node.config.label': '营销活动配置',

  // Template 组件
  'marketing.template.select.title': '选择WhatsApp营销模版',
  'marketing.template.add': '添加模版',
  'marketing.template.select.placeholder': '请选择WhatsApp营销模版',
  'marketing.template.preview.title': 'WhatsApp模版预览',
  'marketing.template.tooltip':
    '若您的WhatsApp模版中包含变量，变量类型请选择"数字"，不要选择"名称"',

  // BasicInfo 组件
  'marketing.basic.info.activity.select': '选择营销活动',
  'marketing.basic.info.activity.placeholder': '请选择营销活动',
  'marketing.basic.info.activity.required': '请选择营销活动',
  'marketing.basic.info.activity.time': '活动时间：',
  'marketing.basic.info.activity.target': '活动目标：',
  'marketing.basic.info.agent.name.required': '请输入营销智能体名称',
  'marketing.basic.info.agent.name.max.length': '名称最大长度为80字符',
  'marketing.basic.info.agent.name.placeholder': '请输入',
  'marketing.basic.info.channel.required': '请选择具体的渠道',
  'marketing.basic.info.channel.placeholder': '请选择具体的渠道',
  'marketing.basic.info.interaction.required': '请选择交互方式',
  'marketing.basic.info.display.activity': '营销活动：',
  'marketing.basic.info.display.agent.name': '营销智能体名称：',
  'marketing.basic.info.display.channel.type': '渠道类型：',
  'marketing.basic.info.display.whatsapp.channel': 'WhatsApp渠道：',
  'marketing.basic.info.display.interaction.type': '交互方式：',
  'marketing.basic.info.interaction.one.time': '一次性营销',

  // TargetCustomer 组件
  'marketing.target.customer.segment.select': '选择客户细分',
  'marketing.target.customer.segment.add': '添加客户细分',
  'marketing.target.customer.segment.placeholder': '请选择客户细分',
  'marketing.target.customer.segment.required': '请选择客户细分',
  'marketing.target.customer.frequency.select': '选择发送频率',
  'marketing.target.customer.frequency.required': '请选择发送频率',
  'marketing.target.customer.frequency.immediate': '立即发送',
  'marketing.target.customer.frequency.daily': '每日发送',
  'marketing.target.customer.frequency.weekly': '每周发送',
  'marketing.target.customer.frequency.monthly': '每月发送',
  'marketing.target.customer.frequency.custom': '自定义',
  'marketing.target.customer.time.select': '选择发送时间：',
  'marketing.target.customer.time.add': '添加发送时间',
  'marketing.target.customer.time.required': '请选择时间',
  'marketing.target.customer.time.placeholder': '12:00',
  'marketing.target.customer.date.required': '请选择发送日期',
  'marketing.target.customer.day.required': '请选择日期',
  'marketing.target.customer.month.text': '每月',
  'marketing.target.customer.day.text': '日',
  'marketing.target.customer.time.select.placeholder': '选择时间',
  'marketing.target.customer.custom.placeholder': '2023-12-16 19:00',
  'marketing.target.customer.timezone.select': '选择时区：',
  'marketing.target.customer.timezone.recipient': '使用收件人当地时区',
  'marketing.target.customer.note': '注: 发送时间将会在活动时间范围内',
  'marketing.target.customer.week.sunday': '周日',
  'marketing.target.customer.week.monday': '周一',
  'marketing.target.customer.week.tuesday': '周二',
  'marketing.target.customer.week.wednesday': '周三',
  'marketing.target.customer.week.thursday': '周四',
  'marketing.target.customer.week.friday': '周五',
  'marketing.target.customer.week.saturday': '周六',

  'marketing.details.customer.phone': '客户号码：',
  'marketing.template.variable.empty': '营销模板变量 "{key}" 不能为空',
};
