export default {
  'add.amazonRegion.channel.configuration.title': 'Tambahkan Amazon',
  'add.amazonRegion.channel.configuration.title.update': 'Perbarui Amazon',
  'add.amazonRegion.channel.configuration.tips': 'Silakan tambahkan <PERSON>',
  'amazonRegion.channel.configuration.title.1':
    '<PERSON><PERSON><PERSON> wilayah penjual Amazon Anda',
  'amazonRegion.channel.configuration.title.tips.1':
    'Jika Anda memiliki lebih dari satu wilayah Amazon, tidak apa-apa. Cukup pilih wilayah yang ingin Anda atur terlebih dahulu, dan kami dapat menambahkan wilayah lain nanti.',
  'amazonRegion.channel.configuration.step.1.region': 'Amerika Utara',
  'amazonRegion.channel.configuration.step.2.region': 'Wilayah Eropa',
  'amazonRegion.channel.configuration.step.3.region': 'Wilayah Timur Jauh',
  'amazonRegion.channel.configuration.1.finish': '<PERSON><PERSON><PERSON>',
  'amazonRegion.channel.configuration.title.2': '<PERSON><PERSON><PERSON> otor<PERSON>',
  'amazonRegion.channel.configuration.title.2.btn': '<PERSON><PERSON> ke otorisasi',
  'amazonRegion.channel.configuration.title.2.btn.finish':
    'Selesaikan otorisasi',
  'amazonRegion.channel.configuration.title.tips.2':
    'Agar kami dapat mengambil pesan Amazon Anda, Anda perlu memberikan izin akses ConnectNow.',
  'amazonRegion.channel.configuration.title.3':
    'Pergi ke Amazon untuk mengonfigurasi Pesan',
  'amazonRegion.channel.configuration.title.tips.3':
    'Anda dapat mengikuti langkah-langkah ini untuk mengonfigurasi Pesan di situs web Amazon.',
  'amazonRegion.channel.configuration.title.3.go': 'Pergi ke konfigurasi',
  'amazonRegion.channel.configuration.title.3.go.finish': 'Konfigurasi selesai',
  'amazonRegion.channel.configuration.title.step.1.step':
    '1. Salin email ConnectNow Anda',
  'amazonRegion.channel.configuration.title.step.2.step':
    '2. Masuk ke akun penjual Amazon Anda dan pastikan wilayah marketplace Amazon yang sama ditampilkan di bagian atas layar.',
  'amazonRegion.channel.configuration.title.step.3.step':
    '3. Klik {value1} di sudut kanan atas, pergi ke {value2}, pastikan {value3} diatur ke bahasa Cina atau Inggris; gulir ke bawah ke {value4} dan klik {value5}.',
  'amazonRegion.channel.configuration.title.step.3.step.1': '"Pengaturan"',
  'amazonRegion.channel.configuration.title.step.3.step.2':
    '"Preferensi Pemberitahuan"',
  'amazonRegion.channel.configuration.title.step.3.step.3':
    '"Kirim pemberitahuan dalam bahasa ini"',
  'amazonRegion.channel.configuration.title.step.3.step.4': '"Pesan"',
  'amazonRegion.channel.configuration.title.step.3.step.5': '"Edit"',
  'amazonRegion.channel.configuration.title.step.4.step': '4. Konfirmasi ',
  'amazonRegion.channel.configuration.title.step.4.step.1': '"Pesan Pembeli"',
  'amazonRegion.channel.configuration.title.step.4.step.2': ' dan ',
  'amazonRegion.channel.configuration.title.step.4.step.3':
    '"Kegagalan Pengiriman" ',
  'amazonRegion.channel.configuration.title.step.4.step.4':
    'keduanya dicentang.',
  'amazonRegion.channel.configuration.title.step.5.step': '5. Masukkan ',
  'amazonRegion.channel.configuration.title.step.5.step.1': 'email ConnectNow ',
  'amazonRegion.channel.configuration.title.step.5.step.2': 'di dua kolom.',
  'amazonRegion.channel.configuration.title.step.6.step':
    '6. Jika ada alamat email lain yang tercantum di bawah "Pesan Pembeli" atau "Kegagalan Pengiriman", hapus yang meneruskan ke helpdesk Anda.',
  'amazonRegion.channel.configuration.title.step.7.step': '7. Klik',
  'amazonRegion.channel.configuration.title.step.7.step.1': ' "Simpan" ',
  'amazonRegion.channel.configuration.title.step.7.step.2':
    'jika Anda membuat perubahan.',
  'amazonRegion.channel.configuration.title.step.8.step':
    '8. Ulangi langkah 1 hingga 8 untuk setiap wilayah penjual Amazon.',
  'amazonRegion.channel.configuration.title.3.modal.title':
    'Apakah Anda yakin sudah selesai dengan pengaturan?',
  'amazonRegion.channel.configuration.title.3.modal.title.tips':
    'Jika langkah ini tidak diselesaikan, kami tidak akan dapat mengirimkan pesan dan data Amazon Anda. Harap konfirmasi bahwa Anda telah mengikuti semua petunjuk di halaman ini sebelum melanjutkan',
  'amazonRegion.channel.configuration.title.4':
    'Pengaturan Layanan Pelanggan Cerdas',
  'amazonRegion.channel.configuration.title.tips.4':
    'Anda dapat mengonfigurasi informasi terkait layanan pelanggan cerdas di sini.',
  'amazonRegion.channel.configuration.title.5': 'Menambahkan informasi saluran',
  'amazonRegion.channel.configuration.title.tips.5':
    'Silakan masukkan informasi saluran',
};
