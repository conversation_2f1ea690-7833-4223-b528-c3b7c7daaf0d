export default {
  'awsAccountSetting.title': 'Ikat akun AWS',
  'awsAccountSetting.add.btn': 'Tambah akun AWS',
  'awsAccountSetting.bind.status': '<PERSON><PERSON><PERSON> Anda telah berhasil terikat',
  'awsAccountSetting.delete.btn': '<PERSON><PERSON>',
  'awsAccountSetting.connect.list': 'Daftar instans AWS connect',
  'awsAccountSetting.synConnect': 'Sinkronkan instans AWS connect',
  'awsAccountSetting.editor.btn': 'Edit',
  'awsAccountSetting.awsUserName': '<PERSON>a akun',
  'awsAccountSetting.awsUserName.placeholder': 'Masukkan nama akun',
  'awsAccountSetting.awsUserId': 'ID Akun',
  'awsAccountSetting.awsUserId.placeholder': 'Masukkan ID akun',
  'awsAccountSetting.accessKey': 'ID Kunc<PERSON>',
  'awsAccountSetting.accessKey.placeholder': '<PERSON><PERSON>kka<PERSON> kunci akses',
  'awsAccountSetting.secretAccessKey': '<PERSON><PERSON><PERSON> Rahasia',
  'awsAccountSetting.secretAccessKey.placeholder':
    'Masukkan kunci akses rahasia',
  'awsAccountSetting.region': 'Wilayah',
  'awsAccountSetting.region.placeholder': 'Pilih wilayah',
  'awsAccountSetting.cancel.btn': 'Batal',
  'awsAccountSetting.save.btn': 'Simpan',
  'awsAccountSetting.region.tips': 'Pilih setidaknya satu wilayah',
  'awsAccountSetting.regions.placeholder': 'Pilih setidaknya satu wilayah',
  'awsAccountSetting.secretAccessKey.tips':
    'Masukkan Kunci Akses Rahasia yang benar',
  'awsAccountSetting.accessKey.tips': 'Masukkan ID Kunci Akses yang benar',
  'awsAccountSetting.awsUserId.tips': 'Masukkan ID akun yang benar',
  'awsAccountSetting.awsUserName.tips': 'Masukkan nama akun yang benar',
  'awsAccountSetting.iam.tips':
    'Hasilkan kunci akses di IAM dan masukkan di bawah ini, klik',
  'awsAccountSetting.iam1.tips': 'Lihat cara menghasilkan kunci akses?',
  'awsAccountSetting.pTitle.tips': 'Catatan: Hanya ditampilkan di sistem ini',
  'awsAccountSetting.pTable.tips':
    'Catatan: Operasi ini hanya memengaruhi tampilan data di sistem ini dan tidak mengoperasikan instans AWS console.',
  'awsAccountSetting.pTable.return': 'Kembali',
  'awsAccountSetting.disable.text': 'Apakah Anda yakin ingin menonaktifkan',
  'awsAccountSetting.disable.text1': 'instans?',
  'awsAccountSetting.setting.alias': 'Atur alias',
  'awsAccountSetting.label.alias': 'Alias:',
  'awsAccountSetting.alias.placeholder': 'Masukkan alias',
  'awsAccountSetting.connectAlias.table': 'Alias instans',
  'awsAccountSetting.connectUrl.table': 'URL akses',
  'awsAccountSetting.bound.table': 'Saluran',
  'awsAccountSetting.createTime.table': 'Tanggal pembuatan',
  'awsAccountSetting.connectStatus.table': 'Status',
  'awsAccountSetting.operation.table': 'Tindakan',
  'awsAccountSetting.disable.table': 'Nonaktifkan',
  'awsAccountSetting.alias.table': 'Atur alias',
  'awsAccountSetting.bound.table.type1': 'Keluar',
  'awsAccountSetting.bound.table.type2': 'Masuk',
  'awsAccountSetting.bound.table.type3': 'Masuk dan keluar',
  'awsAccountSetting.connectStatus.table.type1': 'Aktif',
  'awsAccountSetting.connectStatus.table.type2': 'Dinonaktifkan',
};
