export default {
  'add.whatsApp.channel.configuration.title': 'Menambahkan Saluran WhatsApp',
  'add.whatsApp.channel.configuration.title.update':
    'Perbarui Saluran WhatsApp',
  'add.whatsApp.channel.configuration.tips':
    'Memungkinkan pelanggan lebih mudah menghubungi perusahaan melalui pesan sosial WhatsApp, merespons kebutuhan pelanggan lebih cepat',
  'whatsApp.channel.configuration.title.1': 'Mengikat Akun Bisnis WhatsApp',
  'whatsApp.channel.configuration.title.tips.1':
    'Memungkinkan pelanggan lebih mudah menghubungi perusahaan melalui pesan sosial WhatsApp, merespons kebutuhan pelanggan lebih cepat',
  'whatsApp.channel.configuration.title.2': 'Memilih Nomor Telepon WhatsApp',
  'whatsApp.channel.configuration.title.tips.2':
    'Silakan pilih nomor telepon yang akan ditampilkan saat menghubungi pelanggan selanjutnya',
  'whatsApp.channel.configuration.title.3': 'Menambahkan Informasi Saluran',
  'whatsApp.channel.configuration.title.tips.3':
    'Silakan masukkan informasi saluran',
  'whatsApp.channel.configuration.title.4':
    'Pengaturan Layanan Pelanggan Cerdas',
  'whatsApp.channel.configuration.title.tips.4':
    'Di sini Anda dapat mengonfigurasi informasi terkait layanan pelanggan cerdas',
  'whatsApp.channel.configuration.title.5': 'Terapkan',
  'whatsApp.channel.configuration.title.tips.5':
    'Silakan tambahkan kotak obrolan ke situs web Anda sesuai dengan deskripsi berikut',
  'whatsApp.channel.configuration.channel.name': 'Nama saluran',
  'whatsApp.channel.configuration.channel.name.placeholder':
    'Masukkan nama saluran',
  'whatsApp.channel.configuration.whatsApp.types': 'Bahasa',
  'whatsApp.channel.configuration.whatsApp.types.placeholder':
    'Silakan pilih bahasa',
  'whatsApp.channel.configuration.chat2.logo': 'Logo Perusahaan',
  'whatsApp.channel.configuration.chat2.logo.message1':
    'Hanya JPG atau PNG yang didukung, dan ukuran gambar tidak boleh melebihi 500KB.',
  'whatsApp.channel.configuration.chat2.logo.message2':
    'Ikon akan ditampilkan di sudut kiri atas kotak obrolan, disarankan untuk mengunggah gambar PNG 50*20px',
  'whatsApp.channel.configuration.chat2.chatBoxName.placeholder':
    'Masukkan nama chatbot',
  'whatsApp.channel.configuration.chat2.chatBoxName': 'Nama chatbot',
  'whatsApp.channel.configuration.chat2.chatBoxName.message':
    'Akan ditampilkan di sudut kiri atas kotak obrolan',
  'whatsApp.channel.configuration.chat2.templete': 'Warna tema',
  'whatsApp.channel.configuration.chat2.templete.custom': 'Kustom',
  'whatsApp.channel.configuration.chat2.templete.color': 'Warna saat ini：',
  'whatsApp.channel.configuration.chat2.templete.placeholder':
    'Silakan pilih warna',
  'whatsApp.channel.configuration.chat2.boxColor': 'Warna kotak obrolan agen',
  'whatsApp.channel.configuration.chat2.userBox':
    'Warna kotak obrolan pengguna',
  'whatsApp.channel.configuration.work.panels.checkbox': 'Aktifkan',
  'whatsApp.channel.configuration.chat3.form.message':
    'Disarankan untuk membuka, setelah membuka pengguna perlu memasukkan informasi dasar terlebih dahulu, baru dapat melakukan komunikasi selanjutnya',
  'whatsApp.channel.configuration.chat3.welcome': 'Salam awal',
  'whatsApp.channel.configuration.chat3.welcome.words': 'Salam singkat',
  'whatsApp.channel.configuration.chat3.welcome.words.placeholder':
    'Silakan masukkan pesan sambutan',
  'whatsApp.channel.configuration.chat3.interval.placeholder':
    'Silakan masukkan waktu sesi undangan otomatis',
  'whatsApp.channel.configuration.chat3.welcome.words.message':
    'Pengaturan di sini harap sesuai dengan bahasa yang Anda pilih di langkah pertama',
  'whatsApp.channel.configuration.chat3.welcome.QA': 'Pemicu FAQ',
  'whatsApp.channel.configuration.chat3.welcome.QA.placeholder':
    'Silakan pilih FAQ yang sesuai.',
  'whatsApp.channel.configuration.chat3.welcome.QA.message':
    'FAQ memungkinkan Anda membalas beberapa jawaban kepada pengguna sekaligus, tidak hanya mendukung teks, tetapi juga mendukung gambar dan video. Jika Anda memilih FAQ tertentu, saat pengguna menghubungi Anda, sistem akan secara otomatis membalas dengan jawaban standar yang dikonfigurasi dalam FAQ. Jika Anda belum menyiapkan FAQ apa pun, Anda dapat mengklik',
  'whatsApp.channel.configuration.chat3.welcome.QA.message.1': ' di sini ',
  'whatsApp.channel.configuration.chat3.welcome.QA.message.2':
    'untuk menyiapkannya.',
  'whatsApp.channel.configuration.chat3.talk': 'Obrolan popup otomatis',
  'whatsApp.channel.configuration.chat3.talk.ge': 'Interval',
  'whatsApp.channel.configuration.chat3.talk.ge2':
    'detik, sistem akan secara otomatis memunculkan kotak obrolan',
  'whatsApp.channel.configuration.chat3.message':
    'Saat pelanggan menjelajahi situs web Anda, sistem akan secara otomatis memicu jendela obrolan untuk mengundang pelanggan berkonsultasi.',
  'whatsApp.channel.configuration.chat3.voice.message':
    'Pelanggan dapat mengklik tombol video di bawah jendela obrolan untuk melakukan komunikasi suara dengan agen.',
  'whatsApp.channel.configuration.chat3.voice': 'Komunikasi suara online',
  'whatsApp.channel.configuration.chat3.video.message':
    'Pelanggan dapat mengklik tombol video di bawah jendela obrolan untuk melakukan komunikasi video dengan agen.',
  'whatsApp.channel.configuration.chat3.video': 'Komunikasi video online',
  'whatsApp.channel.configuration.chat3.evaluate.message':
    'Setelah mengakhiri obrolan, sistem secara otomatis memunculkan evaluasi kepuasan untuk agen.',
  'whatsApp.channel.configuration.chat3.evaluate': 'Evaluasi kepuasan',
  'whatsApp.channel.configuration.chat3.information.configuration.completed':
    'Anda telah menyelesaikan pengaturan fungsionalitas dasar, ini akan diimplementasikan dalam kotak obrolan setelah Anda menyimpan',
  'whatsApp.channel.configuration.chat4.mode.message': `Pilih "Layanan pelanggan cerdas", akan ada respons robotik terhadap pertanyaan pelanggan terlebih dahulu, jika chatbot tidak dapat menjawab, pengguna dapat beralih ke layanan agen manusia kapan saja.`,
  'whatsApp.channel.configuration.chat4.mode.message.1': ` Pilih "Hanya agen", hanya agen manusia yang akan menjawab pertanyaan pelanggan.`,
  'whatsApp.channel.configuration.chat4.mode.message.2': `Pilih "Hanya chatbot", hanya layanan pelanggan robotik yang akan menjawab pertanyaan pelanggan.`,
  'whatsApp.channel.configuration.chat4.mode.1': 'Layanan pelanggan cerdas',
  'whatsApp.channel.configuration.chat4.mode.2': 'Hanya agen',
  'whatsApp.channel.configuration.chat4.mode.3': 'Hanya chatbot',
  'whatsApp.channel.configuration.chat4.mode': 'Mode layanan pelanggan',
  'whatsApp.channel.configuration.chat4.robot.message':
    'Nama chatbot akan ditampilkan di atas jawaban chatbot',
  'whatsApp.channel.configuration.chat4.robot.placeholder': 'Masukkan nama bot',
  'whatsApp.channel.configuration.chat4.robot': 'Nama chatbot',
  'whatsApp.channel.configuration.chat4.language.message': `Dengan fitur ini diaktifkan, sistem akan secara otomatis mengidentifikasi bahasa dari pertanyaan input pengguna; jika tidak diaktifkan, akan menggunakan bahasa browser pengguna secara default.`,
  'whatsApp.channel.configuration.chat4.language':
    'Identifikasi bahasa otomatis',
  'whatsApp.channel.configuration.chat4.document': 'Basis pengetahuan dokumen',
  'whatsApp.channel.configuration.chat4.document.placeholder':
    'Silakan pilih basis pengetahuan dokumen',
  'whatsApp.channel.configuration.chat4.document.message.1':
    'Basis pengetahuan dokumen ini hanya menampilkan basis pengetahuan eksternal, silakan konfigurasikan basis pengetahuan di ',
  'whatsApp.channel.configuration.chat4.document.message':
    ' Basis pengetahuan dokumen',
  'whatsApp.channel.configuration.chat4.document.message.2': 'halaman.',
  'whatsApp.channel.configuration.chat4.ai.message':
    'Anda dapat mengonfigurasi apakah akan mengaktifkan AI Generatif.',
  'whatsApp.channel.configuration.chat4.ai': 'Mengintegrasikan AI Generatif',
  'whatsApp.channel.configuration.chat4.workers': `Tampilkan tombol "Transfer ke agen" untuk pertanyaan yang tidak diketahui`,
  'whatsApp.channel.configuration.chat4.workers.message': `Jika diaktifkan, ketika chatbot tidak mengetahui jawabannya, akan secara otomatis menampilkan tombol "Transfer ke agen" di bawah konten respons chatbot`,
  'whatsApp.channel.configuration.chat4.unknown':
    'Respons chatbot saat menghadapi pertanyaan yang tidak diketahui',
  'whatsApp.channel.configuration.chat4.unknown.placeholder':
    'Silakan masukkan balasan chatbot untuk pertanyaan yang tidak diketahui',
  'whatsApp.channel.configuration.chat4.unknown.message':
    'Pengaturan ini adalah respons chatbot saat menghadapi pertanyaan yang tidak diketahui',
  'whatsApp.channel.configuration.chat4.information.configuration.completed':
    'Anda telah menyelesaikan pengaturan layanan pelanggan cerdas, ini akan diimplementasikan dalam kotak obrolan setelah Anda menyimpan.',
  'whatsApp.channel.configuration.chat5.message':
    'Salin kode berikut dan masukkan dalam tag <body> </body> di situs web Anda.',
  'whatsApp.channel.configuration.chat5.message.link':
    'Tautan obrolan: Salin tautan berikut ke dalam kode situs web Anda',
  'whatsApp.channel.configuration.cancel.btn': 'Batal',
  'whatsApp.channel.configuration.next.btn': 'Langkah selanjutnya',
  'whatsApp.channel.configuration.complete.btn': 'Selesai',
};
