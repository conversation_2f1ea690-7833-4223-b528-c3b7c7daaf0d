import request from '@/utils/request';

/** 获取质检规则列表 */
export async function getQualityInspectionRuleList(payload) {
  return request(
    `/api/crm/call/assessment/form/pages?pageSize=${payload.pageSize}&pageNum=${payload.pageNum}`,
    {
      method: 'POST',
      data: payload,
    },
  );
}

/** 获取质检规则详情 */
export async function getQualityInspectionRuleDetail(payload) {
  return request(`/api/crm/call/assessment/form/${payload.assessmentId}`, {
    method: 'GET',
  });
}

/** 删除质检规则 */
export async function deleteQualityInspectionRule(payload) {
  return request(`/api/crm/call/assessment/form/${payload.assessmentId}`, {
    method: 'DELETE',
  });
}

/** 评估表基本信息保存 */
export async function saveQualityInspectionRule(payload) {
  return request('/api/crm/call/assessment/form/save', {
    method: 'POST',
    data: payload,
  });
}

/** 启用或停用评估表 */
export async function enableOrDisableQualityInspectionRule(payload) {
  return request(
    `/api/crm/call/assessment/form/status/${payload.assessmentId}`,
    {
      method: 'PUT',
      data: payload,
    },
  );
}

/** --评估表版本控制器 */
// 保存评估表版本
export async function saveAssessmentVersion(payload) {
  return request(`/api/crm/call/assessment/version/save`, {
    method: 'POST',
    data: payload,
  });
}

// 保存并部署
export async function saveAndDeployAssessmentVersion(payload) {
  return request(`/api/crm/call/assessment/version/saveAndDeploy`, {
    method: 'POST',
    data: payload,
  });
}

/** 根据assessmentId查询所有版本号 */
export async function getAssessmentVersionList(payload) {
  return request(
    `/api/crm/call/assessment/version?assessmentId=${payload.assessmentId}`,
    {
      method: 'GET',
    },
  );
}

/** 获取分类树结构 */
export async function getCategoryTreeStructureByAssessmentId(payload) {
  return request(`/api/crm/call/assessment/category/tree/assessmentById`, {
    method: 'POST',
    data: payload,
  });
}

/** 添加分类树结构 */
export async function addCategoryTreeStructure(payload) {
  return request(`/api/crm/call/assessment/category`, {
    method: 'POST',
    data: payload,
  });
}

/** 更新分类树结构 */
export async function updateCategoryTreeStructure(payload) {
  return request(`/api/crm/call/assessment/category`, {
    method: 'PUT',
    data: payload,
  });
}

/** 删除分类树结构 */
export async function deleteCategoryTreeStructure(payload) {
  return request(`/api/crm/call/assessment/category/${payload.categoryId}`, {
    method: 'DELETE',
  });
}

/** 规则table列表 */
export async function getRuleTableList(payload) {
  return request(
    `/api/crm/call/assessment/rule/pages?pageSize=${payload.pageSize}&pageNum=${payload.pageNum}`,
    {
      method: 'POST',
      data: payload,
    },
  );
}
// 获取所有规则
export async function getAllRuleList(payload) {
  return request(
    `/api/crm/call/assessment/rule/ruleList/${payload.assessmentId}`,
    {
      method: 'POST',
    },
  );
}

// 保存规则
export async function saveRule(payload) {
  return request(`/api/crm/call/assessment/rule/save`, {
    method: 'POST',
    data: payload,
  });
}

///规则详情
export async function getRuleDetail(payload) {
  return request(`/api/crm/call/assessment/rule/${payload.ruleId}`, {
    method: 'GET',
  });
}

// 删除规则
export async function deleteRule(payload) {
  return request(`/api/crm/call/assessment/rule/${payload.ruleId}`, {
    method: 'DELETE',
  });
}

// 评估记录分页列表
export async function getAssessmentRecordPage(payload) {
  return request(
    `/api/crm/call/assessment/record/assessmentRecordPages?pageSize=${payload.pageSize}&pageNum=${payload.pageNum}`,
    {
      method: 'POST',
      data: payload,
    },
  );
}

// 评估表导出PDF
export async function exportAssessmentForm(payload) {
  return request(
    `/api/crm/call/assessment/form/export/${payload.assessmentId}`,
    {
      method: 'POST',
    },
  );
}
