import request from '@/utils/request';

/**
 * 头像规则列表
 * @param payload
 * @returns {Promise<*>}
 */
export async function queryAllPhotoSettingList() {
  return request('api/crm/customer/photo/queryAllPhotoSettingList', {
    method: 'get',
  });
}
/**
 * 新增头像规则
 * @param payload
 * @returns {Promise<*>}
 */
export async function addPhotoSetting(payload) {
  return request('api/crm/customer/photo/addPhotoSetting', {
    method: 'post',
    data: payload,
  });
}
/**
 * 修改头像规则
 * @param payload
 * @returns {Promise<*>}
 */
export async function updatePhotoSetting(payload) {
  return request('api/crm/customer/photo/updatePhotoSetting', {
    method: 'post',
    data: payload,
  });
}

/**
 * 删除头像规则
 * @param payload
 * @returns {Promise<*>}
 */
export async function removePhotoSetting(payload) {
  return request('api/crm/customer/photo/removePhotoSetting', {
    method: 'post',
    data: payload,
  });
}
/**
 * 更新头像规则顺序
 * @param payload
 * @returns {Promise<*>}
 */
export async function updatePhotoOrder(payload) {
  return request('api/crm/customer/photo/updatePhotoOrder', {
    method: 'post',
    data: payload,
  });
}
/**
 * 查询客户头像颜色返回所有客户的列表
 * @param payload
 * @returns {Promise<*>}
 */
export async function queryCustomerPhotoColor(payload) {
  return request('api/crm/customer/photo/queryCustomerPhotoColor', {
    method: 'get',
  });
}
/**
 * 查询头像规则详情单个对象
 * @param payload
 * @returns {Promise<*>}
 */
export async function queryPhotoSettingDetail(payload) {
  return request(
    `api/crm/customer/photo/queryPhotoSettingDetail?photoId=${payload.photoId}`,
    {
      method: 'get',
    },
  );
}
