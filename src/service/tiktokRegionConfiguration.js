import request from '@/utils/request';

/**
 * 获取国家
 */
export async function queryTiktokMarketRegion() {
  return request('api/crm/channel/channelConfig/queryTiktokShopRegionDefList', {
    method: 'GET',
  });
}
/**
 * 根据auth_code查询tiktok店铺信息
 */
export async function queryTiktokShopInfo(payload) {
  return request('api/crm/channel/channelConfig/queryTiktokShopInfo', {
    method: 'post',
    data: payload,
  });
}
/**
 * TikTok 添加渠道之前判断所选的店铺是否被占用
 */
export async function checkShopInfoExist(payload) {
  return request(
    `api/crm/channel/channelConfig/checkShopInfoExist?shopId=${payload.shopId}`,
    {
      method: 'GET',
    },
  );
}

/**
 * 查询订单
 * @param payload
 * @returns {Promise<*>}
 */
export async function getTikTokOrderList(payload) {
  return request('api/crm/channel/channelConfig/tikTokOrderList', {
    method: 'POST',
    data: payload,
  });
}
