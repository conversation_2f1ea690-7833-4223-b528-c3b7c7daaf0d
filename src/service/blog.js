import request from '@/utils/request';

/** 文章列表 */
export async function getArticleList(payload) {
  return request(
    `api/crm/system/article/pages?pageNum=${payload.pageNum}&pageSize=${payload.pageSize}`,
    {
      method: 'POST',
      data: payload,
    },
  );
}

/** 热门文章 */
export async function getHotArticleList(payload) {
  return request(`api/crm/system/article/hotList?type=${payload.type}`, {
    method: 'GET',
  });
}

/** 文章详情 */
export async function getArticleDetail(payload) {
  return request(`api/crm/system/article/detail/${payload.articleId}`, {
    method: 'GET',
  });
}
