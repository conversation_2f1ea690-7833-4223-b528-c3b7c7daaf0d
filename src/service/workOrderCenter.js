import request from '@/utils/request';

/** 查询工单 */
export async function queryWorkOrderList(payload) {
  let selectItem = payload.selectItem;
  return request('api/crm/call/workRecord/queryWorkOrder', {
    method: 'POST',
    data: selectItem,
    params: {
      pageNum: payload.pageNum,
      pageSize: payload.pageSize,
    },
  });
}
/** 查询工单筛选器列表 */
export async function queryWorkRecordFilter() {
  return request('api/crm/call/workRecordFilter/queryWorkRecordFilter', {
    method: 'GET',
  });
}
/** 查询筛选器对应筛选条件 */
export async function queryFilterCondition(payload) {
  return request(
    'api/crm/call/workRecordFilter/queryFilterCondition?workRecordFilterId=' +
      payload,
    {
      method: 'GET',
    },
  );
}
/** 查询工单属性定义扩展表 */
export async function queryWorkOrderExtList(payload) {
  return request(
    'api/crm/call/workOrderExt/queryWorkOrderExtList?queryType=' + payload,
    {
      method: 'GET',
    },
  );
}
/** 另存为新的筛选器 */
export async function createFilter(payload) {
  return request('api/crm/call/workRecordFilter/createFilter', {
    method: 'POST',
    data: payload,
  });
}
/** 保存筛选器（修改） */
export async function modifyFilter(payload) {
  return request('api/crm/call/workRecordFilter/modifyFilter', {
    method: 'POST',
    data: payload,
  });
}
/** 创建工单-选择客户*/
export async function queryCustomerInformation() {
  return request('api/crm/customer/customerinfo/queryCustomerInformation', {
    method: 'GET',
  });
}
/** 创建工单-查询客服*/
export async function querySeatsUser() {
  return request('api/crm/system/user/attendant/querySeatsUser', {
    method: 'GET',
  });
}
/** 创建工单-紧急程度*/
export async function queryWorkRecordLevel() {
  return request('api/crm/call/workRecord/queryWorkRecordLevel', {
    method: 'GET',
  });
}
/** 创建工单-查询工单类型*/
export async function queryWorkRecordType() {
  return request('api/crm/call/workRecord/queryWorkRecordType', {
    method: 'GET',
  });
}
/** 创建工单-客户历史工单*/
export async function queryHistoryCustomerWorkOrder(payload) {
  return request(
    'api/crm/call/workRecord/queryHistoryCustomerWorkOrder/' +
      payload.customerId,
    {
      method: 'GET',
      params: {
        workRecordId: payload.workRecordId,
        pageNum: payload.pageNum,
        pageSize: payload.pageSize,
      },
    },
  );
}
/** 创建工单-选择渠道*/
export async function queryChannelNameList() {
  return request('api/crm/channel/channelConfig/queryChannelDefNameList', {
    method: 'GET',
  });
}
/** 创建工单-选择渠道*/
export async function queryChannelNameListNew() {
  return request('api/crm/channel/channelConfig/queryChannelNameList', {
    method: 'GET',
  });
}

/** 创建工单-保存工单创建*/
export async function queryCreateWork(payload) {
  return request('api/crm/call/workRecord/createWork', {
    method: 'POST',
    data: payload,
  });
}
/** 创建工单-修改工单*/
export async function queryUpdateWork(payload) {
  return request('api/crm/call/workRecord/updateWork', {
    method: 'POST',
    data: payload,
  });
}
/** 查询工单详情*/
export async function queryWorkOrderDetail(payload) {
  return request(
    'api/crm/call/workRecord/workOrderDetail?workRecordId=' + payload,
    {
      method: 'GET',
    },
  );
}
/** 查询关联工单*/
export async function queryAssociationWorkOrder(payload) {
  // return request('api/crm/call/workRecord/associationWorkOrder', {
  //   method: 'GET',
  //   params: payload,
  // });
  let workRecordId = payload.workRecordId;
  let workRecordInfo = payload.workRecordInfo;
  let pageSize = payload.pageSize;
  let pageNum = payload.pageNum;
  return request(
    'api/crm/call/workRecord/associationWorkOrder?workRecordId=' +
      workRecordId +
      '&workRecordInfo=' +
      workRecordInfo +
      '&pageNum=' +
      pageNum +
      '&pageSize=' +
      pageSize,
    {
      method: 'GET',
    },
  );
}
/** 创建工单-扩展字段查询*/
export async function queryDefineExtList() {
  return request('api/crm/call/workOrderExt/queryDefineExtList', {
    method: 'GET',
  });
}
/**关注工单*/
export async function queryConcernedWorkOrder(payload) {
  return request('api/crm/call/workRecord/concernedWorkOrder', {
    method: 'POST',
    data: payload,
  });
}
/**获取当前用户工单列表的表头*/
export async function queryWorkRecordByUserId(payload) {
  return request(
    'api/crm/call/workOrderExt/queryWorkRecordByUserId?queryType=' + payload,
    {
      method: 'GET',
    },
  );
}

/**获取联络明细表头*/
export async function queryContactDetailByUserId() {
  return request('api/crm/call/statIndex/queryContactDetailByUserId', {
    method: 'GET',
  });
}
/**修改表头*/
export async function addUserWorkRecordExt(payload) {
  return request('api/crm/call/workOrderExt/addUserWorkRecordExt', {
    method: 'POST',
    data: payload,
  });
}
// 联络明细修改表头 new
export async function addUserContactDetailExt(payload) {
  return request('api/crm/call/statIndex/addUserContactDetailExt', {
    method: 'POST',
    data: payload,
  });
}
/**解决工单*/
export async function solveWorkStatus(payload) {
  return request('api/crm/call/workRecord/solveWorkStatus', {
    method: 'POST',
    data: payload,
  });
}
/**终止工单*/
export async function terminationWorkStatus(payload) {
  return request('api/crm/call/workRecord/terminationWorkStatus', {
    method: 'POST',
    data: payload,
  });
}
/**指派工单*/
export async function assignWorkOrder(payload) {
  return request('api/crm/call/workRecord/assignWorkOrder', {
    method: 'POST',
    data: payload,
  });
}
/**分配工单*/
export async function allocationTicket(payload) {
  return request('api/crm/call/workRecord/allocationTicket', {
    method: 'POST',
    data: payload,
  });
}
/**转派工单*/
export async function transferWorkOrder(payload) {
  return request('api/crm/call/workRecord/transferWorkOrder', {
    method: 'POST',
    data: payload,
  });
}
/**催办工单*/
export async function urgingWorkOrder(payload) {
  return request('api/crm/call/workRecord/urgingWorkOrder', {
    method: 'POST',
    data: payload,
  });
}
/**添加关联工单*/
export async function updateWorkAssociation(payload) {
  return request('api/crm/call/workRecord/updateWorkAssociation', {
    method: 'POST',
    data: payload,
  });
}
/**查询关联工单时的工单列表*/
export async function associationWorkOrderTable(payload) {
  let workRecordId = payload.workRecordId;
  let workRecordInfo = payload.workRecordInfo;
  let pageSize = payload.pageSize;
  let pageNum = payload.pageNum;
  return request(
    'api/crm/call/workRecord/associationWorkOrderTable?workRecordId=' +
      workRecordId +
      '&workRecordInfo=' +
      workRecordInfo +
      '&pageNum=' +
      pageNum +
      '&pageSize=' +
      pageSize,
    {
      method: 'GET',
    },
  );
}

/**升级工单*/
export async function workUpgrade(payload) {
  return request('api/crm/call/workRecord/workUpgrade', {
    method: 'POST',
    data: payload,
  });
}
/**备注工单*/
export async function workRemark(payload) {
  return request('api/crm/call/workRecord/workRemark', {
    method: 'POST',
    data: payload,
  });
}
/**认领工单*/
export async function claimWorkOrder(payload) {
  return request('api/crm/call/workRecord/claimWorkOrder', {
    method: 'POST',
    data: payload,
  });
}
/**查询工单操作记录*/
export async function WorkOrderOperationRecords(payload) {
  return request(
    'api/crm/call/workRecord/WorkOrderOperationRecords?workRecordId=' + payload,
    {
      method: 'GET',
    },
  );
}
/**批量标记已解决*/
export async function batchSolveWork(payload) {
  return request('api/crm/call/workRecord/batchSolveWork', {
    method: 'POST',
    data: payload,
  });
}
/**批量导出*/
export async function exportTicket(payload) {
  // post请求导出工单，必须如下写法
  return request.post(
    'api/crm/call/workRecord/exportTicket',
    {
      data: payload,
      responseType: 'blob',
    },
    'blob',
  );
}
/**回复工单*/
export async function replyMessage(payload) {
  return request('api/crm/call/workRecord/replyMessage', {
    method: 'POST',
    data: payload,
  });
}
/**查询回复详情*/
export async function queryWorkRecordInfo(payload) {
  return request(
    'api/crm/call/workRecord/queryWorkRecordInfo?workRecordId=' +
      payload.workRecordId +
      '&channelTypeId=' +
      payload.channelTypeId,
    {
      method: 'GET',
    },
  );
}
/**上传附件接口*/
export async function upload(payload) {
  return request('api/crm/call/workRecord/upload', {
    method: 'POST',
    data: payload,
  });
}
export async function uploadByUrl(payload) {
  return request('api/crm/call/workRecord/uploadByUrl', {
    method: 'POST',
    data: payload,
  });
}
/**删除附件*/
export async function deleteFile(payload) {
  return request('api/crm/call/workRecord/deleteFile', {
    method: 'POST',
    data: payload,
  });
}
/**上传图片*/
export async function uploadPicture(payload) {
  return request('api/crm/call/workRecord/uploadPicture', {
    method: 'POST',
    data: payload,
  });
}
/**下载附件*/
export async function download(payload) {
  return request('api/crm/call/workRecord/download', {
    method: 'POST',
    data: payload,
  });
}
/**语音转文本*/
export async function voiceAnalysis(payload) {
  // return request('api/crm/call/workRecord/voiceAnalysis/729dd5ea-749e-4bcf-9b16-18518a310151', {
  return request('api/crm/call/workRecord/voiceAnalysis/' + payload, {
    method: 'GET',
    // data: payload,
  });
}
/**工单内容总结*/
export async function workSummarize(payload) {
  return request('api/crm/call/workRecord/saveWorkSummarize', {
    method: 'POST',
    data: payload,
  });
}
/**智能总结内容保存*/
export async function saveWorkSummarizeNew(payload) {
  return request('api/crm/call/workRecord/workSummarize', {
    method: 'POST',
    data: payload,
  });
}

/**新增待办事项*/
export async function addWaitExecute(payload) {
  return request('api/crm/call/workRecord/saveWaitExecute', {
    method: 'POST',
    data: payload,
  });
}
/**更改待办事项*/
export async function updateWaitExecute(payload) {
  return request('api/crm/call/workRecord/saveWaitExecute', {
    method: 'POST',
    data: payload,
  });
}
/**获取总结内容*/
export async function querySummary(payload) {
  return request('api/crm/aigc/ai/chat/summary', {
    method: 'POST',
    data: payload,
  });
}
/**智能翻译*/
export async function queryAnswer(payload) {
  return request('api/crm/aigc/ai/chat/answer', {
    method: 'POST',
    data: payload,
  });
}
/**查询工单状态数量*/
// export async function queryWorkOrderNumber(payload) {
//   return request(
//     'api/crm/call/workRecord/queryWorkOrder?queryType=' + payload,
//     {
//       method: 'GET',
//     },
//   );
// }
export async function queryWorkOrderNumber(payload) {
  return request('api/crm/call/workRecord/queryWorkOrderStatusNumber', {
    method: 'POST',
    data: payload,
  });
}
/**查询客户工单总结*/
export async function queryCustomTicketRecord(payload) {
  return request(
    'api/crm/call/workRecord/customTicketRecord?customId=' + payload,
    {
      method: 'GET',
    },
  );
}
/**查询工单备注接口*/
export async function queryByTicketRemarks(payload) {
  return request(
    'api/crm/call/workRecord/queryByTicketRemarks?workRecordId=' + payload,
    // 'api/crm/call/workRecord/queryByTicketRemarks?workRecordId=c65b2f5d-6904-464f-a014-2933229263d6',
    {
      method: 'GET',
    },
  );
}
/**查询工单新备注接口 分页*/
export async function queryByTicketRemarksNew(payload) {
  return request('api/crm/call/workRecord/queryByTicketRemarksPage', {
    method: 'GET',
    params: payload,
  });
}
/**查询工单客户的客户标签*/
export async function queryCustomerLabels(payload) {
  return request(
    'api/crm/customer/customerinfo/queryCustomerLabels?ticketId=' + payload,
    {
      method: 'GET',
    },
  );
}
/**查询座席团队列表*/
export async function queryCallDeptList(payload) {
  return request('api/crm/system/user/queryCallDeptList', {
    method: 'GET',
  });
}
/**工单类型\优先级修改*/
export async function updateWorkStatus(payload) {
  return request('api/crm/call/workRecord/updateWorkStatus', {
    method: 'POST',
    data: payload,
  });
}
/** 新版查询客服*/
export async function querySeatsUserList() {
  return request('api/crm/system/user/attendant/querySeatsUserList', {
    method: 'GET',
  });
}

/** 查询路由规则*/
export async function queryRuleList() {
  return request('api/crm/call/routingChannel/queryRuleEnumsList', {
    method: 'GET',
  });
}

/**智能匹配规则列表 */
export async function getAiMatchList(payload) {
  return request('api/crm/call/routingChannel/queryRuleList', {
    method: 'GET',
    params: {
      pageNum: payload.pageNum,
      pageSize: payload.pageSize,
    },
  });
}

// 查询输入框快捷回复
export async function allQuickReply(payload) {
  return request('api/crm/knowledge/quickReply/allQuickReply', {
    method: 'POST',
    data: payload,
  });
}

/** 工作台新版查询工单详情*/
export async function workbenchTicketDetails(payload) {
  return request(
    'api/crm/call/workRecord/workbenchTicketDetails?workRecordId=' + payload,
    {
      method: 'GET',
    },
  );
}

/** 关联工单、历史工单展示工单详情*/
export async function ticketGetDetails(payload) {
  return request('api/crm/call/workRecord/getDetails?workRecordId=' + payload, {
    method: 'GET',
  });
}

// 获取工单最新评估记录
export async function assessmentRecordByTicket(payload) {
  return request(
    'api/crm/call/assessment/record/latestAssessment?ticketId=' +
      payload.ticketId +
      '&assessorId=' +
      payload.assessorId +
      '&assessmentRecordId=' +
      payload.assessmentRecordId,
    {
      method: 'GET',
    },
  );
}
// 获取可用的评估表列表，根据工单信息
export async function availableAssessmentListByTicket(payload) {
  return request(
    'api/crm/call/assessment/record/availableAssessments?ticketType=' +
      payload.ticketType +
      '&channelConfigId=' +
      payload.channelConfigId,
    {
      method: 'GET',
    },
  );
}

// 生成评估记录数据并返回评估规则数据----评估完成的详情
export async function generateAssessmentRecords(payload) {
  return request(
    'api/crm/call/assessment/record/assessmentDetailRecord?recordId=' +
      payload.recordId,
    {
      method: 'GET',
    },
  );
}
// 点击分数查看规则详情得分点
export async function getAssessmentRuleDetail(payload) {
  return request(
    'api/crm/call/assessment/record/assessmentDetailRules?recordId=' + payload,
    {
      method: 'GET',
    },
  );
}
// 点击完成评估按钮
export async function assessmentCompleted(payload) {
  return request(
    'api/crm/call/assessment/record/assessmentCompleted?recordId=' +
      payload.recordId,
    {
      method: 'GET',
    },
  );
}
