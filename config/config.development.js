import { defineConfig } from 'umi';

export default defineConfig({
  define: {
    'process.env.UMI_ENV': 'development',
    'process.env.CHAT_PATH':
      'https://dm4lhw9jczavq.cloudfront.net/ai-agent/umi.js',
    'process.env.CHAT_PARAMS_I': '675c4b15-8172-4f56-8223-92972646c387',
    'process.env.CHAT_PARAMS_V': 'd6ff59a027861d7d4133e1d6b6872464',
    'process.env.DOMAIN_NAME_HOME': 'dev.connectnowai.com',
    'process.env.DOMAIN_NAME_OVER': 'dev.connectnowai.com',
    'process.env.WXCHAT_DOMAIN': 'dev.connectnowai.com',
    'process.env.CHANNEL_APP_ID': '851209337208854',
    'process.env.WHATSAPP_APP_ID': '1080872296369089',
    'process.env.IG_APP_ID': '1219913835731415',
    'process.env.AMAZON_APPLICATION_ID':
      'amzn1.sp.solution.0aa45153-b8f9-48ff-abbb-2aa8ff95c9a7',
  },
  // esbuild: {},
  // // 关闭sourceMap
  // devtool: false,

  // // 关闭node_modules处理，提升构建速度。注：部分依赖不做处理会报错
  // nodeModulesTransform: {
  //   type: 'none',
  //   exclude: [],
  // },
  // // 减少要兼容的浏览器范围，默认umi配置为{ chrome: 49, firefox: 64, safari: 10, edge: 13, ios: 10 }
  // targets: {
  //   chrome: 79,
  //   firefox: false,
  //   safari: false,
  //   edge: false,
  //   ios: false,
  // },
  // // 设置构建缓存
  // chainWebpack: function(config, { webpack }) {
  //   config.merge({
  //     cache: {
  //       //  memory ｜ filesystem，生产环境禁用缓存到memory
  //       type: 'filesystem',
  //     },
  //   });
  // },
});
