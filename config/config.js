import { defineConfig } from 'umi';
import routes from './routes';
import proxy from './proxy';
import { join } from 'lodash';
import path from 'path';

export default defineConfig({
  nodeModulesTransform: {
    type: 'none',
  },
  extraBabelIncludes: [
    // 支持绝对路径
    join(__dirname, '../node_modules/amazon-connect-streams'),
    // 支持 npm 包
    'amazon-connect-streams',
    path.resolve('node_modules', '@editorjs/editorjs'),
    path.resolve('node_modules', '@editorjs/image'),
    path.resolve('node_modules', '@editorjs/quote'),
    path.resolve('node_modules', '@editorjs/code'),
    path.resolve('node_modules', 'emoji-mart'),
    path.resolve('node_modules', 'editorjs-text-color-plugin'),
    path.resolve('node_modules', '@studio-freight/lenis'),
  ],
  // extraBabelPresets: ['@babel/preset-env'],
  extraBabelPlugins: ['@babel/plugin-transform-optional-chaining'],
  hash: true,
  // terserOptions: {
  //   output: { comments: true }, // 移除注释
  //   compress: {
  //     drop_console: true, // 移除日志
  //   },
  // },

  history: {
    type: 'hash',
  },
  // mfsu: {
  // },
  // webpack5: {},
  targets: {
    ie: 11,
  },
  // dynamicImport: {
  //   loading: '@/components/PageLoading/index',
  // },
  routes, // 路由
  // 配置布局
  // layout: {},
  // 配置dva
  dva: {
    immer: true,
    hmr: false, // 热更新
  },
  // 配置antd
  antd: {},
  proxy, // 开启代理
  manifest: {
    basePath: '/',
  },
  mock: false,
  links: [
    {
      rel: 'icon',
      href:
        'https://connectnow-demo-html-prod.s3.ap-southeast-1.amazonaws.com/ConnectNow.ico',
      // 'https://ai-edu-prod-static-files.s3.cn-north-1.amazonaws.com.cn/goclouds.ico',
    },
    {
      rel: 'manifest',
      href: '/manifest.json',
    },
    {
      rel: 'apple-touch-icon',
      href: '/icons/192.png',
    },
  ],
  title: 'ConnectNow',
  metas: [
    {
      name: 'keywords',
      content: 'ConnectNow,云联络中心,智能办公,AWS',
    },
    {
      name: 'description',
      content:
        '包括AIGC,智能工单,智能客服等功能，全球部署，畅通稳定的全球化、一体化、智能化的客户联络中心。',
    },
    {
      name: 'theme-color',
      content: '#8500BB',
    },
    {
      name: 'apple-mobile-web-app-capable',
      content: 'yes',
    },
    {
      name: 'apple-mobile-web-app-status-bar-style',
      content: 'black',
    },
    {
      name: 'viewport',
      content:
        'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no',
    },
  ],
  locale: {
    default: 'zh-CN',
    antd: true,
    title: false,
    baseNavigator: true,
    baseSeparator: '-',
  },
});
