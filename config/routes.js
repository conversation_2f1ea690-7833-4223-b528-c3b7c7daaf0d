export default [
  {
    exact: true,
    path: '/sso/login/:token',
    component: '@/pages/sso',
  },
  {
    path: '/terms-service',
    component: '@/pages/termsService',
  },
  {
    path: '/login',
    component: '@/pages/login',
  },
  {
    path: '/site',
    component: '@/pages/site',
  },
  // {
  //   path: '/register',
  //   component: '@/pages/register',
  // },
  {
    path: '/register',
    component: '@/pages/registerNew',
  },
  {
    path: '/unsubscribe',
    component: '@/pages/unsubscribe',
  },
  {
    path: '/inviteRegister',
    component: '@/pages/inviteRegister',
  },
  // {
  //   exact: true,
  //   path: '/workshopNewChat',
  //   component: '@/pages/workshopNewChat',
  // },
  {
    exact: true,
    path: '/homepage',
    component: '@/pages/homepageMobile',
  },
  {
    exact: true,
    path: '/homepage/detail',
    component: '@/pages/homepageMobile/MobileDetail',
  },
  {
    exact: true,
    path: '/home',
    component: '@/pages/homePageNew',
  },
  {
    exact: true,
    path: '/productChannel',
    component: '@/pages/product/channel',
  },
  {
    exact: true,
    path: '/productAiCallCenter',
    component: '@/pages/product/callCenter',
  },
  {
    exact: true,
    path: '/productAiAgent',
    component: '@/pages/product/aiAgent',
  },
  {
    exact: true,
    path: '/productAIGCCustomerService',
    component: '@/pages/product/AigcCustomerService',
  },
  {
    exact: true,
    path: '/productAIGCAssistant',
    component: '@/pages/product/AigcAssistant',
  },
  {
    exact: true,
    path: '/productSmartWorkOrder',
    component: '@/pages/product/smartWorkOrder',
  },
  {
    exact: true,
    path: '/productAiVoiceRobot',
    component: '@/pages/product/voiceRobot',
  },
  {
    exact: true,
    path: '/productAIGCMarketing',
    component: '@/pages/product/marketing',
  },
  {
    exact: true,
    path: '/productVideoCustomerService',
    component: '@/pages/product/videoCustomerService',
  },
  {
    exact: true,
    path: '/productDataReport',
    component: '@/pages/product/dataReport',
  },
  {
    exact: true,
    path: '/solutionFinance',
    component: '@/pages/solution/finance',
  },
  {
    exact: true,
    path: '/solutionConsumerElectronics',
    component: '@/pages/solution/electronics',
  },
  {
    exact: true,
    path: '/solutionNewEnergy',
    component: '@/pages/solution/newEnergy',
  },
  {
    exact: true,
    path: '/solutionRetail',
    component: '@/pages/solution/retail',
  },
  {
    exact: true,
    path: '/solutionManufacturing',
    component: '@/pages/solution/manufacture',
  },
  {
    exact: true,
    path: '/resourcesBlog',
    component: '@/pages/resourcesPage/blog',
  },
  {
    exact: true,
    path: '/resourcesBlogDetail',
    component: '@/pages/resourcesPage/blogDetail',
  },
  {
    exact: true,
    path: '/resourcesHelpCenter',
    component: '@/pages/resourcesPage/helpCenter',
  },
  {
    exact: true,
    path: '/companyNews',
    component: '@/pages/companyNews',
  },
  {
    exact: true,
    path: '/partner',
    component: '@/pages/partner',
  },
  {
    exact: true,
    path: '/aiAgentLibrary',
    component: '@/pages/AIAgentLibrary',
  },
  {
    exact: true,
    path: '/privacyPolicy',
    component: '@/pages/privacyPolicy',
  },
  {
    exact: true,
    path: '/cookiePolicy',
    component: '@/pages/cookiePolicy',
  },
  {
    exact: true,
    path: '/complianceGuide',
    component: '@/pages/productComplianceGuide',
  },
  {
    exact: true,
    path: '/userTerms',
    component: '@/pages/userTerms',
  },
  {
    exact: true,
    path: '/euAiActCompliance',
    component: '@/pages/euAiActCompliance',
  },
  {
    exact: true,
    path: '/gdprCompliance',
    component: '@/pages/gdprCompliance',
  },
  {
    exact: true,
    path: '/joinUs',
    component: '@/pages/joinUs',
  },
  {
    exact: true,
    path: '/cloudContactCenter',
    component: '@/pages/cloudContactCenter',
  },
  {
    exact: true,
    path: '/contactUs',
    component: '@/pages/contactUs',
  },
  {
    exact: true,
    path: '/loginTips',
    component: '@/pages/loginTips',
  },
  {
    exact: true,
    path: '/mobileChat',
    component: '@/pages/mobileChat',
  },
  {
    exact: true,
    path: '/lineChannelDocument',
    component: '@/pages/helpDocument/lineChannel',
  },
  {
    exact: true,
    path: '/weChatOfficialDocument',
    component: '@/pages/helpDocument/weChatOfficial',
  },
  {
    exact: true,
    path: '/whatsAppChannelDocument',
    component: '@/pages/helpDocument/whatsAppChannel',
  },
  {
    path: '/documentPreview',
    component: '@/pages/documentPreview',
  },
  {
    path: '/discordHelpDocument',
    component: '@/pages/helpDocument/discordChannel',
  },
  {
    path: '/',
    component: '@/layouts/index',
    routes: [
      {
        path: '/test',
        component: '@/pages/test',
      },
      {
        path: '/beta',
        component: '@/pages/beta',
      },
      {
        exact: true,
        path: '/',
        component: '@/pages/home',
      },
      // {
      //   exact: true,
      //   path: '/',
      //   component: '@/pages/home',
      // },
      {
        exact: true,
        path: '/agentWorkloadReport',
        component: '@/pages/statistics/agentWorkloadReport',
      },
      {
        exact: true,
        path: '/machineWorkloadReport',
        component: '@/pages/statistics/machineWorkloadReport',
      },
      {
        exact: true,
        path: '/agentDimension',
        component: '@/pages/statistics/agentDimension',
      },
      {
        exact: true,
        path: '/queueDimension',
        component: '@/pages/statistics/queueDimension',
      },
      // {
      //   exact: true,
      //   path: '/worktable',
      //   component: '@/pages/worktable',
      // },
      // workTableUpgrade
      {
        exact: true,
        path: '/worktable',
        component: '@/pages/workTableUpgrade',
      },
      {
        exact: true,
        path: '/personalCenter',
        component: '@/pages/personalCenter',
      },
      {
        exact: true,
        path: '/preferences',
        component: '@/pages/preferences',
      },
      {
        exact: true,
        path: '/channelAllocation',
        component: '@/pages/channelAllocation',
      },
      {
        exact: true,
        path: '/channelAllocation/emailChannelConfiguration',
        component: '@/pages/channelAllocation/emailChannelConfiguration',
      },
      {
        exact: true,
        path: '/channelAllocation/chatChannelConfiguration',
        component: '@/pages/channelAllocation/chatChannelConfiguration',
      },
      {
        exact: true,
        path: '/channelAllocation/chatVoiceChannelConfiguration',
        component: '@/pages/channelAllocation/chatVoiceChannelConfiguration',
      },
      {
        exact: true,
        path: '/channelAllocation/whatsAppChannelConfiguration',
        component: '@/pages/channelAllocation/whatsAppChannelConfiguration',
      },
      {
        exact: true,
        path: '/channelAllocation/amazonRegionConfiguration',
        component: '@/pages/channelAllocation/amazonRegionConfiguration',
      },
      {
        exact: true,
        path: '/channelAllocation/faceBookConfiguration',
        component: '@/pages/channelAllocation/faceBookConfiguration',
      },
      {
        exact: true,
        path: '/channelAllocation/wechatConfiguration',
        component: '@/pages/channelAllocation/wechatConfiguration',
      },
      {
        exact: true,
        path: '/channelAllocation/instagramConfiguration',
        component: '@/pages/channelAllocation/instagramConfiguration',
      },
      {
        exact: true,
        path: '/channelAllocation/googlePlayConfiguration',
        component: '@/pages/channelAllocation/googlePlayConfiguration',
      },
      {
        exact: true,
        path: '/channelAllocation/tiktokConfiguration',
        component: '@/pages/channelAllocation/tiktokConfiguration',
      },
      {
        exact: true,
        path: '/channelAllocation/discordConfiguration',
        component: '@/pages/channelAllocation/discordConfiguration',
      },
      {
        exact: true,
        path: '/emailMarketingEvent',
        component: '@/pages/emailMarketing',
      },
      {
        exact: true,
        path: '/emailTemplate',
        component: '@/pages/emailTemplate',
      },
      {
        exact: true,
        path: '/emailTemplateDetail',
        component: '@/pages/emailTemplate/updateEmailTemplate',
      },
      {
        exact: true,
        path: '/addEmailMarketingEvent',
        component: '@/pages/emailMarketing/emailMarketingEvent',
      },
      {
        exact: true,
        path: '/awsAccountSetting',
        component: '@/pages/awsAccountSetting',
      },
      {
        exact: true,
        path: '/systemConfiguration',
        component: '@/pages/systemConfiguration',
      },
      {
        exact: true,
        path: '/settingTicket',
        component: '@/pages/settingTicket',
      },
      {
        exact: true,
        path: '/permissionSettings',
        component: '@/pages/settingTicket/permissionSettings',
      },
      {
        exact: true,
        path: '/settingTicket/ticketAttr',
        component: '@/pages/settingTicket/ticketAttr',
      },
      {
        exact: true,
        path: '/settingTicket/ticketSLA',
        component: '@/pages/settingTicket/ticketSLA',
      },
      {
        exact: true,
        path: '/settingTicket/ticketType',
        component: '@/pages/settingTicket/ticketType',
      },
      {
        exact: true,
        path: '/settingTicket/ticketMerge',
        component: '@/pages/settingTicket/ticketMerge',
      },
      {
        exact: true,
        path: '/settingTicket/agentWorkerTime',
        component: '@/pages/settingTicket/agentWorkerTime',
      },
      {
        exact: true,
        path: '/settingTicket/manageAgentStatus',
        component: '@/pages/settingTicket/manageAgentStatus',
      },
      {
        exact: true,
        path: '/settingTicket/configureCustomerExtensionInformation',
        component:
          '@/pages/settingTicket/configureCustomerExtensionInformation',
      },
      {
        exact: true,
        path: '/autoCloseSetting',
        component: '@/pages/settingTicket/autoCloseSetting',
      },
      {
        exact: true,
        path: '/autoCloseSetting/addAutoCloseSetting',
        component: '@/pages/settingTicket/autoCloseSetting/addAutoCloseSetting',
      },
      {
        exact: true,
        path: '/companyInfo',
        component: '@/pages/settingTicket/companyInfo',
      },
      {
        exact: true,
        path: '/configureCustomerExtensionInformation',
        component: '@/pages/configureCustomerExtensionInformation',
      },
      {
        exact: true,
        path: '/customerInformation',
        component: '@/pages/customerInformation',
      },
      {
        exact: true,
        path: '/customerInfoDetail',
        component: '@/pages/customerInformation/editorCustomerInfo',
      },
      {
        exact: true,
        path: '/customerDataGroupManagement',
        component: '@/pages/customerDataGroupManagement',
      },
      {
        exact: true,
        path: '/customerDataGroupManagement/addCustomerGroups',
        component: '@/pages/customerDataGroupManagement/addCustomerGroup',
      },
      {
        exact: true,
        path: '/customerDataGroupManagement/editorCustomerGroups',
        component: '@/pages/customerDataGroupManagement/editorCustomerGroups',
      },
      {
        exact: true,
        path: '/customerList',
        component: '@/pages/customerList',
      },

      {
        exact: true,
        path: '/platformUserManagement',
        component: '@/pages/platformUserManagement',
      },
      {
        exact: true,
        path: '/platformUserManagement/createOrganization',
        component: '@/pages/platformUserManagement/createOrganization',
      },
      {
        exact: true,
        path: '/platformUserManagement/createUser',
        component: '@/pages/platformUserManagement/createUser',
      },
      {
        exact: true,
        path: '/platformUserManagement/addAvatorSetting',
        component: '@/pages/platformUserManagement/addAvatorSetting',
      },
      {
        exact: true,
        path: '/agentManagement',
        component: '@/pages/agentManagement',
      },
      {
        exact: true,
        path: '/qnaKnowledgeBase',
        component: '@/pages/knowledgeQA',
      },
      {
        exact: true,
        path: '/qnaKnowledgeBaseAdd',
        component: '@/pages/knowledgeQA/addedQA',
      },
      {
        exact: true,
        path: '/workRecord',
        component: '@/pages/workRecord',
      },
      {
        exact: true,
        path: '/workOrderExtension',
        component: '@/pages/workOrderExtension',
      },
      {
        exact: true,
        path: '/workOrderManagement',
        component: '@/pages/ticketCenter/workOrderManagement',
      },
      {
        exact: true,
        path: '/timeoutUnassignedTicket',
        component: '@/pages/ticketCenter/timeoutUnassignedTicket',
      },
      {
        exact: true,
        path: '/timedoutUnresolvedTicket',
        component: '@/pages/ticketCenter/timedoutUnresolvedTicket',
      },
      {
        exact: true,
        path: '/progressingTicket',
        component: '@/pages/ticketCenter/progressingTicket',
      },
      {
        exact: true,
        path: '/myGroupTicket',
        component: '@/pages/ticketCenter/myGroupTicket',
      },

      {
        exact: true,
        path: '/myCreatTicket',
        component: '@/pages/ticketCenter/myCreatTicket',
      },
      {
        exact: true,
        path: '/myInterestTicket',
        component: '@/pages/ticketCenter/myInterestTicket',
      },
      {
        exact: true,
        path: '/robotTicket',
        component: '@/pages/ticketCenter/robotTicket',
      },
      {
        exact: true,
        path: '/inactiveMessageReminder',
        component:
          '@/pages/ticketCenter/ticketSettings/inactiveMessageReminder',
      },
      {
        exact: true,
        path: '/contactCustomers',
        component: '@/pages/contactCustomers',
      },
      {
        exact: true,
        path: '/documentKnowledgeBase',
        component: '@/pages/documentKnowledgeBase',
      },
      {
        exact: true,
        path: '/documentKnowledgeBase/createKnowledgeBase',
        component: '@/pages/createKnowledgeBase',
      },
      {
        exact: true,
        path: '/documentKnowledgeBase/uploadDocuments',
        component: '@/pages/uploadDocuments',
      },
      {
        exact: true,
        path: '/documentKnowledgeBase/uploadOcr',
        component: '@/pages/uploadOCR',
      },
      {
        exact: true,
        path: '/definitionSynonyms',
        component: '@/pages/definitionSynonyms',
      },

      {
        exact: true,
        path: '/marketingActivities',
        component: '@/pages/marketingActivities/common',
      },
      {
        exact: true,
        path: '/myCampaign',
        component: '@/pages/marketingActivities/myActivities',
      },
      {
        exact: true,
        path: '/allCampaign',
        component: '@/pages/marketingActivities/allActivities',
      },
      {
        exact: true,
        path: '/createCampaign',
        component: '@/pages/marketingActivities/createActivity',
      },
      {
        exact: true,
        path: '/modifyCampaign',
        component: '@/pages/marketingActivities/modifyActivity',
      },
      {
        exact: true,
        path: '/createCampaignCalendar',
        component: '@/pages/marketingActivities/createActivityCalendar',
      },
      {
        exact: true,
        path: '/campaignCalendar',
        component: '@/pages/marketingActivities/activityCalendar',
      },
      {
        exact: true,
        path: '/marketingResults',
        component: '@/pages/marketingResults',
      },
      {
        exact: true,
        path: '/marketingDetails',
        component: '@/pages/marketingDetails',
      },
      {
        exact: true,
        path: '/marketingResultsDetails',
        component: '@/pages/marketingResultsDetails',
      },
      {
        exact: true,
        path: '/abTestAnalysis',
        component: '@/pages/testAnalysis',
      },
      {
        exact: true,
        path: '/historicalPerformanceIndicatorsSeats',
        component: '@/pages/statistics/historicalPerformanceIndicatorsSeats',
      },
      {
        exact: true,
        path: '/historicalQueueIndicators',
        component: '@/pages/statistics/historicalQueueIndicators',
      },
      {
        exact: true,
        path: '/dataDetails',
        component: '@/pages/statistics/dataDetails',
      },
      {
        exact: true,
        path: '/hotlineKeyIndicators',
        component: '@/pages/statistics/hotlineKeyIndicators',
      },
      {
        exact: true,
        path: '/hotlineKeyIndicatorsConfig',
        component: '@/pages/statistics/hotlineKeyIndicatorsConfig',
      },
      {
        exact: true,
        path: '/agentWorkEfficiencyStatistics',
        component: '@/pages/statistics/agentWorkEfficiencyStatistics',
      },
      {
        exact: true,
        path: '/satisfactionReport',
        component: '@/pages/statistics/satisfactionReport',
      },
      {
        exact: true,
        path: '/customerTicket',
        component: '@/pages/statistics/customerTicket',
      },
      {
        exact: true,
        path: '/evaluationReportsList',
        component: '@/pages/selfEvaluation/evaluationReportsList',
      },
      {
        exact: true,
        path: '/evaluationReportDetails',
        component: '@/pages/selfEvaluation/evaluationReportDetails',
      },
      {
        exact: true,
        path: '/selfReport',
        component: '@/pages/selfEvaluation/selfReport',
      },
      {
        exact: true,
        path: '/selfConfiguration',
        component: '@/pages/selfEvaluation/selfConfiguration',
      },
      {
        exact: true,
        path: '/customerServiceConversationInsights',
        component: '@/pages/insights/customerServiceConversationInsights',
      },
      {
        exact: true,
        path: '/customerServiceConversationInsightsDetail',
        component: '@/pages/insights/customerServiceConversationInsightsDetail',
      },
      {
        exact: true,
        path: '/customerServiceHotTopicAnalysis',
        component: '@/pages/AIGCAnalysis/customerServiceHotTopicAnalysis',
      },
      {
        exact: true,
        path: '/lackKnowledgeProblemAnalysis',
        component: '@/pages/AIGCAnalysis/lackKnowledgeProblemAnalysis',
      },
      {
        exact: true,
        path: '/AIGCAnalysis/feedbackPerformance',
        component: '@/pages/AIGCAnalysis/feedbackPerformance',
      },
      {
        exact: true,
        path: '/channelConfigurationList',
        component: '@/pages/newChannelConfiguration/listPage',
      },
      {
        exact: true,
        path: '/channelConfigurationDetailList',
        component: '@/pages/newChannelConfiguration/detailPage',
      },
      {
        exact: true,
        path: '/googlePlayAllApps',
        component: '@/pages/newChannelConfiguration/googlePlayAllApps',
      },
      {
        exact: true,
        path: '/commentFeedback',
        component: '@/pages/newChannelConfiguration/commentFeedback',
      },
      {
        exact: true,
        path: '/channelAllocation/lineChannelConfiguration',
        component: '@/pages/channelAllocation/lineChannelConfiguration',
      },
      {
        exact: true,
        path: '/channelAllocation/weChatOfficialAccountChannelConfiguration',
        component: '@/pages/channelAllocation/weChatOfficialAccountChannel',
      },
      {
        exact: true,
        path: '/weChatOfficialAccountHelpDocument',
        component: '@/pages/channelAllocation/weChatOfficialAccountDocument',
      },
      {
        exact: true,
        path: '/lineChannelConfigurationHelpDocument',
        component: '@/pages/channelAllocation/lineChannelDocument',
      },
      {
        exact: true,
        path: '/allocationRule',
        component: '@/pages/allocationRule',
      },
      {
        exact: true,
        path: '/allocationRuleAdd',
        component: '@/pages/allocationRule/allocationRuleAdd',
      },
      {
        exact: true,
        path: '/allocationRuleDetail',
        component: '@/pages/allocationRule/allocationRuleDetail',
      },
      {
        exact: true,
        path: '/tagManagement',
        component: '@/pages/tagManagement',
      },
      {
        exact: true,
        path: '/tagClassification',
        component: '@/pages/tagClassification',
      },
      {
        exact: true,
        path: '/intentionManagement',
        component: '@/pages/intentionManagement',
      },
      {
        exact: true,
        path: '/intentionManagement/newIntent',
        component: '@/pages/intentionManagement/NewIntent',
      },
      {
        exact: true,
        path: '/externalIntelligentAgentList',
        component: '@/pages/externalIntelligentAgent/listPage',
      },
      {
        exact: true,
        path: '/externalIntelligentAgentList/createExternalIntelligentAgent',
        component: '@/pages/externalIntelligentAgent/createPage',
      },
      {
        exact: true,
        path: '/agentFlow',
        component: '@/pages/AIAgentBox/layouts/index.jsx',
      },
      {
        exact: true,
        path: '/overview',
        component: '@/pages/meteringBilling/overView',
      },
      {
        exact: true,
        path: '/externalIntelligentAgentAIGCPackage',
        component:
          '@/pages/meteringBilling/externalIntelligentAgentAigcPackage',
      },
      {
        exact: true,
        path: '/agentAIGCPackage',
        component: '@/pages/meteringBilling/agentAIGCPackage',
      },
      {
        exact: true,
        path: '/phoneAIGCPackage',
        component: '@/pages/meteringBilling/phoneAIGCPackage',
      },
      {
        exact: true,
        path: '/alarmRule',
        component: '@/pages/meteringBilling/alarmRule',
      },
      {
        exact: true,
        path: '/telephoneExpenses',
        component: '@/pages/meteringBilling/telephoneExpenses',
      },
      {
        exact: true,
        path: '/manageAgentStatus',
        component: '@/pages/manageAgentStatus',
      },
      {
        exact: true,
        path: '/intelligentFormFilling',
        component: '@/pages/IntelligentFormFilling',
      },
      {
        exact: true,
        path: '/addIntelligentFormFilling',
        component: '@/pages/IntelligentFormFilling/addComponent',
      },
      {
        exact: true,
        path: '/evaluationList',
        component: '@/pages/smartQualityInspection/evaluationList',
      },
      {
        exact: true,
        path: '/evaluationAdd',
        component: '@/pages/smartQualityInspection/evaluationAdd',
      },
      {
        exact: true,
        path: '/evaluationHistory',
        component: '@/pages/smartQualityInspection/evaluationHistory',
      },
      {
        exact: true,
        path: '/evaluationRuleList',
        component: '@/pages/smartQualityInspection/evaluationRuleList',
      },
      {
        exact: true,
        path: '/evaluationRuleAdd',
        component: '@/pages/smartQualityInspection/evaluationAddRule',
      },
      {
        exact: true,
        path: '/intelligentQualityInspection',
        component: '@/pages/ticketCenter/common/intelligentQualityInspection',
      },
      {
        exact: true,
        path: '/marketingAgent',
        component: '@/pages/marketingAgent',
      },
      {
        exact: true,
        path: '/marketingAgent/create',
        component: '@/pages/marketingAgent/create',
      },
      {
        exact: true,
        path: '/marketingWhatsAppDetails',
        component: '@/pages/marketingWhatsAppDetails',
      },
    ],
  },
  { component: '@/pages/404' },
];
