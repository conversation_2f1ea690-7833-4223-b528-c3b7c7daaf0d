<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>GoClouds CRM - 离线模式</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      margin: 0;
      padding: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      background-color: #f0f2f5;
      color: #333;
      text-align: center;
    }
    .container {
      padding: 20px;
      max-width: 600px;
    }
    h1 {
      font-size: 24px;
      margin-bottom: 16px;
      color: #1890ff;
    }
    p {
      font-size: 16px;
      line-height: 1.6;
      margin-bottom: 24px;
    }
    .icon {
      font-size: 64px;
      margin-bottom: 24px;
      color: #1890ff;
    }
    .button {
      background-color: #1890ff;
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
      transition: background-color 0.3s;
    }
    .button:hover {
      background-color: #40a9ff;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="icon">📶</div>
    <h1>您当前处于离线状态</h1>
    <p>无法连接到GoClouds CRM平台。请检查您的网络连接，然后重试。</p>
    <p>如果您之前访问过的页面已被缓存，您仍然可以访问它们。</p>
    <button class="button" onclick="window.location.reload()">重新连接</button>
  </div>

  <script>
    // 监听在线状态
    window.addEventListener('online', function() {
      window.location.reload();
    });
  </script>
</body>
</html>
