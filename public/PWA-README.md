# PWA配置说明

本项目已配置为Progressive Web App (PWA)，可以实现离线访问、添加到主屏幕等功能。PWA功能通过手动配置实现，不依赖于额外的UmiJS插件。

## 1. 生成图标

PWA需要各种尺寸的图标。我们提供了一个脚本来生成这些图标：

1. 准备一个高分辨率的图标文件（至少512x512像素），命名为`source-icon.png`并放在`public`目录下
2. 安装依赖：
   ```
   npm install sharp
   ```
3. 运行图标生成脚本：
   ```
   node public/generate-icons.js
   ```

这将在`public/icons`目录下生成所有所需的图标。

## 2. PWA文件说明

本项目的PWA功能由以下文件组成：

1. `public/manifest.json` - 定义应用的元数据、图标和主题色等
2. `public/service-worker.js` - 处理缓存和离线功能
3. `public/offline.html` - 离线时显示的页面
4. `src/pages/document.ejs` - HTML模板，包含Service Worker注册脚本

## 3. 构建项目

配置完成后，使用正常的构建命令构建项目：

```
yarn build
```

或

```
npm run build
```

## 4. 测试PWA功能

要测试PWA功能，您需要：

1. 使用HTTPS或localhost环境（PWA要求安全环境）
2. 使用支持PWA的浏览器（如Chrome、Edge、Firefox等）
3. 访问应用并检查以下功能：
   - 在Chrome开发者工具的"Application"标签页中查看Service Worker是否已注册
   - 测试离线功能（在开发者工具中禁用网络）
   - 尝试将应用添加到主屏幕

## 5. 注意事项

- PWA只在HTTPS或localhost环境下工作
- 每次更新Service Worker文件时，记得更新`CACHE_NAME`变量以确保用户获得最新版本
- 如果您更改了应用的主题颜色，记得同时更新`manifest.json`和`config.js`中的相关配置

## 6. 自定义

您可以根据需要自定义以下文件：

- `public/manifest.json`：修改应用名称、描述、颜色等
- `public/service-worker.js`：修改缓存策略、离线行为等
- `public/offline.html`：自定义离线页面的外观和内容
- `src/pages/document.ejs`：修改HTML模板，包括Service Worker注册脚本 
