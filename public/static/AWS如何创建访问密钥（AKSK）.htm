<html
  xmlns:v="urn:schemas-microsoft-com:vml"
  xmlns:o="urn:schemas-microsoft-com:office:office"
  xmlns:w="urn:schemas-microsoft-com:office:word"
  xmlns:m="http://schemas.microsoft.com/office/2004/12/omml"
  xmlns="http://www.w3.org/TR/REC-html40"
>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="ProgId" content="Word.Document" />
    <meta name="Generator" content="Microsoft Word 15" />
    <meta name="Originator" content="Microsoft Word 15" />
    <link
      rel="File-List"
      href="AWS如何创建访问密钥（AKSK）.files/filelist.xml"
    />
    <link
      rel="Edit-Time-Data"
      href="AWS如何创建访问密钥（AKSK）.files/editdata.mso"
    />
    <!--[if !mso]>
      <style>
        v\:* {
          behavior: url(#default#VML);
        }
        o\:* {
          behavior: url(#default#VML);
        }
        w\:* {
          behavior: url(#default#VML);
        }
        .shape {
          behavior: url(#default#VML);
        }
      </style>
    <![endif]-->
    <!--[if gte mso 9
      ]><xml>
        <o:DocumentProperties>
          <o:Author>Len0467</o:Author>
          <o:LastAuthor>于 国斌</o:LastAuthor>
          <o:Revision>2</o:Revision>
          <o:TotalTime>8</o:TotalTime>
          <o:Created>2023-06-17T12:26:00Z</o:Created>
          <o:LastSaved>2023-06-17T12:26:00Z</o:LastSaved>
          <o:Pages>1</o:Pages>
          <o:Words>128</o:Words>
          <o:Characters>735</o:Characters>
          <o:Lines>6</o:Lines>
          <o:Paragraphs>1</o:Paragraphs>
          <o:CharactersWithSpaces>862</o:CharactersWithSpaces>
          <o:Version>16.00</o:Version>
        </o:DocumentProperties>
        <o:OfficeDocumentSettings>
          <o:AllowPNG />
        </o:OfficeDocumentSettings> </xml
    ><![endif]-->
    <link
      rel="dataStoreItem"
      href="AWS如何创建访问密钥（AKSK）.files/item0001.xml"
      target="AWS如何创建访问密钥（AKSK）.files/props002.xml"
    />
    <link
      rel="themeData"
      href="AWS如何创建访问密钥（AKSK）.files/themedata.thmx"
    />
    <link
      rel="colorSchemeMapping"
      href="AWS如何创建访问密钥（AKSK）.files/colorschememapping.xml"
    />
    <!--[if gte mso 9
      ]><xml>
        <w:WordDocument>
          <w:SpellingState>Clean</w:SpellingState>
          <w:GrammarState>Clean</w:GrammarState>
          <w:TrackMoves>false</w:TrackMoves>
          <w:TrackFormatting />
          <w:PunctuationKerning />
          <w:DrawingGridHorizontalSpacing
            >5.5 磅</w:DrawingGridHorizontalSpacing
          >
          <w:DrawingGridVerticalSpacing>7.8 磅</w:DrawingGridVerticalSpacing>
          <w:DisplayHorizontalDrawingGridEvery
            >2</w:DisplayHorizontalDrawingGridEvery
          >
          <w:DisplayVerticalDrawingGridEvery
            >2</w:DisplayVerticalDrawingGridEvery
          >
          <w:ValidateAgainstSchemas />
          <w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid>
          <w:IgnoreMixedContent>false</w:IgnoreMixedContent>
          <w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText>
          <w:DoNotPromoteQF />
          <w:LidThemeOther>EN-US</w:LidThemeOther>
          <w:LidThemeAsian>ZH-CN</w:LidThemeAsian>
          <w:LidThemeComplexScript>X-NONE</w:LidThemeComplexScript>
          <w:Compatibility>
            <w:BreakWrappedTables />
            <w:SnapToGridInCell />
            <w:WrapTextWithPunct />
            <w:UseAsianBreakRules />
            <w:DontGrowAutofit />
            <w:SplitPgBreakAndParaMark />
            <w:EnableOpenTypeKerning />
            <w:DontFlipMirrorIndents />
            <w:OverrideTableStyleHps />
            <w:UseFELayout />
          </w:Compatibility>
          <m:mathPr>
            <m:mathFont m:val="Cambria Math" />
            <m:brkBin m:val="before" />
            <m:brkBinSub m:val="&#45;-" />
            <m:smallFrac m:val="off" />
            <m:dispDef />
            <m:lMargin m:val="0" />
            <m:rMargin m:val="0" />
            <m:defJc m:val="centerGroup" />
            <m:wrapIndent m:val="1440" />
            <m:intLim m:val="subSup" />
            <m:naryLim m:val="undOvr" /> </m:mathPr
        ></w:WordDocument> </xml
    ><![endif]-->
    <!--[if gte mso 9
      ]><xml>
        <w:LatentStyles
          DefLockedState="false"
          DefUnhideWhenUsed="false"
          DefSemiHidden="false"
          DefQFormat="false"
          DefPriority="99"
          LatentStyleCount="376"
        >
          <w:LsdException
            Locked="false"
            Priority="0"
            QFormat="true"
            Name="Normal"
          />
          <w:LsdException
            Locked="false"
            Priority="9"
            QFormat="true"
            Name="heading 1"
          />
          <w:LsdException
            Locked="false"
            Priority="9"
            SemiHidden="true"
            UnhideWhenUsed="true"
            QFormat="true"
            Name="heading 2"
          />
          <w:LsdException
            Locked="false"
            Priority="9"
            SemiHidden="true"
            UnhideWhenUsed="true"
            QFormat="true"
            Name="heading 3"
          />
          <w:LsdException
            Locked="false"
            Priority="9"
            SemiHidden="true"
            UnhideWhenUsed="true"
            QFormat="true"
            Name="heading 4"
          />
          <w:LsdException
            Locked="false"
            Priority="9"
            SemiHidden="true"
            UnhideWhenUsed="true"
            QFormat="true"
            Name="heading 5"
          />
          <w:LsdException
            Locked="false"
            Priority="9"
            SemiHidden="true"
            UnhideWhenUsed="true"
            QFormat="true"
            Name="heading 6"
          />
          <w:LsdException
            Locked="false"
            Priority="9"
            SemiHidden="true"
            UnhideWhenUsed="true"
            QFormat="true"
            Name="heading 7"
          />
          <w:LsdException
            Locked="false"
            Priority="9"
            SemiHidden="true"
            UnhideWhenUsed="true"
            QFormat="true"
            Name="heading 8"
          />
          <w:LsdException
            Locked="false"
            Priority="9"
            SemiHidden="true"
            UnhideWhenUsed="true"
            QFormat="true"
            Name="heading 9"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="index 1"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="index 2"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="index 3"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="index 4"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="index 5"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="index 6"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="index 7"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="index 8"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="index 9"
          />
          <w:LsdException
            Locked="false"
            Priority="39"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="toc 1"
          />
          <w:LsdException
            Locked="false"
            Priority="39"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="toc 2"
          />
          <w:LsdException
            Locked="false"
            Priority="39"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="toc 3"
          />
          <w:LsdException
            Locked="false"
            Priority="39"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="toc 4"
          />
          <w:LsdException
            Locked="false"
            Priority="39"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="toc 5"
          />
          <w:LsdException
            Locked="false"
            Priority="39"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="toc 6"
          />
          <w:LsdException
            Locked="false"
            Priority="39"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="toc 7"
          />
          <w:LsdException
            Locked="false"
            Priority="39"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="toc 8"
          />
          <w:LsdException
            Locked="false"
            Priority="39"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="toc 9"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Normal Indent"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="footnote text"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="annotation text"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="header"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="footer"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="index heading"
          />
          <w:LsdException
            Locked="false"
            Priority="35"
            SemiHidden="true"
            UnhideWhenUsed="true"
            QFormat="true"
            Name="caption"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="table of figures"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="envelope address"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="envelope return"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="footnote reference"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="annotation reference"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="line number"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="page number"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="endnote reference"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="endnote text"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="table of authorities"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="macro"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="toa heading"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="List"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="List Bullet"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="List Number"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="List 2"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="List 3"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="List 4"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="List 5"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="List Bullet 2"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="List Bullet 3"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="List Bullet 4"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="List Bullet 5"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="List Number 2"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="List Number 3"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="List Number 4"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="List Number 5"
          />
          <w:LsdException
            Locked="false"
            Priority="10"
            QFormat="true"
            Name="Title"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Closing"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Signature"
          />
          <w:LsdException
            Locked="false"
            Priority="1"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Default Paragraph Font"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Body Text"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Body Text Indent"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="List Continue"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="List Continue 2"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="List Continue 3"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="List Continue 4"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="List Continue 5"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Message Header"
          />
          <w:LsdException
            Locked="false"
            Priority="11"
            QFormat="true"
            Name="Subtitle"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Salutation"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Date"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Body Text First Indent"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Body Text First Indent 2"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Note Heading"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Body Text 2"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Body Text 3"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Body Text Indent 2"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Body Text Indent 3"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Block Text"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Hyperlink"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="FollowedHyperlink"
          />
          <w:LsdException
            Locked="false"
            Priority="22"
            QFormat="true"
            Name="Strong"
          />
          <w:LsdException
            Locked="false"
            Priority="20"
            QFormat="true"
            Name="Emphasis"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Document Map"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Plain Text"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="E-mail Signature"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="HTML Top of Form"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="HTML Bottom of Form"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Normal (Web)"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="HTML Acronym"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="HTML Address"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="HTML Cite"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="HTML Code"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="HTML Definition"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="HTML Keyboard"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="HTML Preformatted"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="HTML Sample"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="HTML Typewriter"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="HTML Variable"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Normal Table"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="annotation subject"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="No List"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Outline List 1"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Outline List 2"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Outline List 3"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Simple 1"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Simple 2"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Simple 3"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Classic 1"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Classic 2"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Classic 3"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Classic 4"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Colorful 1"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Colorful 2"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Colorful 3"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Columns 1"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Columns 2"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Columns 3"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Columns 4"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Columns 5"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Grid 1"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Grid 2"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Grid 3"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Grid 4"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Grid 5"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Grid 6"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Grid 7"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Grid 8"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table List 1"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table List 2"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table List 3"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table List 4"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table List 5"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table List 6"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table List 7"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table List 8"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table 3D effects 1"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table 3D effects 2"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table 3D effects 3"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Contemporary"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Elegant"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Professional"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Subtle 1"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Subtle 2"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Web 1"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Web 2"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Web 3"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Balloon Text"
          />
          <w:LsdException Locked="false" Priority="39" Name="Table Grid" />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Table Theme"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            Name="Placeholder Text"
          />
          <w:LsdException
            Locked="false"
            Priority="1"
            QFormat="true"
            Name="No Spacing"
          />
          <w:LsdException Locked="false" Priority="60" Name="Light Shading" />
          <w:LsdException Locked="false" Priority="61" Name="Light List" />
          <w:LsdException Locked="false" Priority="62" Name="Light Grid" />
          <w:LsdException
            Locked="false"
            Priority="63"
            Name="Medium Shading 1"
          />
          <w:LsdException
            Locked="false"
            Priority="64"
            Name="Medium Shading 2"
          />
          <w:LsdException Locked="false" Priority="65" Name="Medium List 1" />
          <w:LsdException Locked="false" Priority="66" Name="Medium List 2" />
          <w:LsdException Locked="false" Priority="67" Name="Medium Grid 1" />
          <w:LsdException Locked="false" Priority="68" Name="Medium Grid 2" />
          <w:LsdException Locked="false" Priority="69" Name="Medium Grid 3" />
          <w:LsdException Locked="false" Priority="70" Name="Dark List" />
          <w:LsdException
            Locked="false"
            Priority="71"
            Name="Colorful Shading"
          />
          <w:LsdException Locked="false" Priority="72" Name="Colorful List" />
          <w:LsdException Locked="false" Priority="73" Name="Colorful Grid" />
          <w:LsdException
            Locked="false"
            Priority="60"
            Name="Light Shading Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="61"
            Name="Light List Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="62"
            Name="Light Grid Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="63"
            Name="Medium Shading 1 Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="64"
            Name="Medium Shading 2 Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="65"
            Name="Medium List 1 Accent 1"
          />
          <w:LsdException Locked="false" SemiHidden="true" Name="Revision" />
          <w:LsdException
            Locked="false"
            Priority="34"
            QFormat="true"
            Name="List Paragraph"
          />
          <w:LsdException
            Locked="false"
            Priority="29"
            QFormat="true"
            Name="Quote"
          />
          <w:LsdException
            Locked="false"
            Priority="30"
            QFormat="true"
            Name="Intense Quote"
          />
          <w:LsdException
            Locked="false"
            Priority="66"
            Name="Medium List 2 Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="67"
            Name="Medium Grid 1 Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="68"
            Name="Medium Grid 2 Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="69"
            Name="Medium Grid 3 Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="70"
            Name="Dark List Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="71"
            Name="Colorful Shading Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="72"
            Name="Colorful List Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="73"
            Name="Colorful Grid Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="60"
            Name="Light Shading Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="61"
            Name="Light List Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="62"
            Name="Light Grid Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="63"
            Name="Medium Shading 1 Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="64"
            Name="Medium Shading 2 Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="65"
            Name="Medium List 1 Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="66"
            Name="Medium List 2 Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="67"
            Name="Medium Grid 1 Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="68"
            Name="Medium Grid 2 Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="69"
            Name="Medium Grid 3 Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="70"
            Name="Dark List Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="71"
            Name="Colorful Shading Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="72"
            Name="Colorful List Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="73"
            Name="Colorful Grid Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="60"
            Name="Light Shading Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="61"
            Name="Light List Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="62"
            Name="Light Grid Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="63"
            Name="Medium Shading 1 Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="64"
            Name="Medium Shading 2 Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="65"
            Name="Medium List 1 Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="66"
            Name="Medium List 2 Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="67"
            Name="Medium Grid 1 Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="68"
            Name="Medium Grid 2 Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="69"
            Name="Medium Grid 3 Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="70"
            Name="Dark List Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="71"
            Name="Colorful Shading Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="72"
            Name="Colorful List Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="73"
            Name="Colorful Grid Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="60"
            Name="Light Shading Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="61"
            Name="Light List Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="62"
            Name="Light Grid Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="63"
            Name="Medium Shading 1 Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="64"
            Name="Medium Shading 2 Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="65"
            Name="Medium List 1 Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="66"
            Name="Medium List 2 Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="67"
            Name="Medium Grid 1 Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="68"
            Name="Medium Grid 2 Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="69"
            Name="Medium Grid 3 Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="70"
            Name="Dark List Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="71"
            Name="Colorful Shading Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="72"
            Name="Colorful List Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="73"
            Name="Colorful Grid Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="60"
            Name="Light Shading Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="61"
            Name="Light List Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="62"
            Name="Light Grid Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="63"
            Name="Medium Shading 1 Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="64"
            Name="Medium Shading 2 Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="65"
            Name="Medium List 1 Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="66"
            Name="Medium List 2 Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="67"
            Name="Medium Grid 1 Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="68"
            Name="Medium Grid 2 Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="69"
            Name="Medium Grid 3 Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="70"
            Name="Dark List Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="71"
            Name="Colorful Shading Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="72"
            Name="Colorful List Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="73"
            Name="Colorful Grid Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="60"
            Name="Light Shading Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="61"
            Name="Light List Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="62"
            Name="Light Grid Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="63"
            Name="Medium Shading 1 Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="64"
            Name="Medium Shading 2 Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="65"
            Name="Medium List 1 Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="66"
            Name="Medium List 2 Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="67"
            Name="Medium Grid 1 Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="68"
            Name="Medium Grid 2 Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="69"
            Name="Medium Grid 3 Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="70"
            Name="Dark List Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="71"
            Name="Colorful Shading Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="72"
            Name="Colorful List Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="73"
            Name="Colorful Grid Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="19"
            QFormat="true"
            Name="Subtle Emphasis"
          />
          <w:LsdException
            Locked="false"
            Priority="21"
            QFormat="true"
            Name="Intense Emphasis"
          />
          <w:LsdException
            Locked="false"
            Priority="31"
            QFormat="true"
            Name="Subtle Reference"
          />
          <w:LsdException
            Locked="false"
            Priority="32"
            QFormat="true"
            Name="Intense Reference"
          />
          <w:LsdException
            Locked="false"
            Priority="33"
            QFormat="true"
            Name="Book Title"
          />
          <w:LsdException
            Locked="false"
            Priority="37"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Bibliography"
          />
          <w:LsdException
            Locked="false"
            Priority="39"
            SemiHidden="true"
            UnhideWhenUsed="true"
            QFormat="true"
            Name="TOC Heading"
          />
          <w:LsdException Locked="false" Priority="41" Name="Plain Table 1" />
          <w:LsdException Locked="false" Priority="42" Name="Plain Table 2" />
          <w:LsdException Locked="false" Priority="43" Name="Plain Table 3" />
          <w:LsdException Locked="false" Priority="44" Name="Plain Table 4" />
          <w:LsdException Locked="false" Priority="45" Name="Plain Table 5" />
          <w:LsdException
            Locked="false"
            Priority="40"
            Name="Grid Table Light"
          />
          <w:LsdException
            Locked="false"
            Priority="46"
            Name="Grid Table 1 Light"
          />
          <w:LsdException Locked="false" Priority="47" Name="Grid Table 2" />
          <w:LsdException Locked="false" Priority="48" Name="Grid Table 3" />
          <w:LsdException Locked="false" Priority="49" Name="Grid Table 4" />
          <w:LsdException
            Locked="false"
            Priority="50"
            Name="Grid Table 5 Dark"
          />
          <w:LsdException
            Locked="false"
            Priority="51"
            Name="Grid Table 6 Colorful"
          />
          <w:LsdException
            Locked="false"
            Priority="52"
            Name="Grid Table 7 Colorful"
          />
          <w:LsdException
            Locked="false"
            Priority="46"
            Name="Grid Table 1 Light Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="47"
            Name="Grid Table 2 Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="48"
            Name="Grid Table 3 Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="49"
            Name="Grid Table 4 Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="50"
            Name="Grid Table 5 Dark Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="51"
            Name="Grid Table 6 Colorful Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="52"
            Name="Grid Table 7 Colorful Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="46"
            Name="Grid Table 1 Light Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="47"
            Name="Grid Table 2 Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="48"
            Name="Grid Table 3 Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="49"
            Name="Grid Table 4 Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="50"
            Name="Grid Table 5 Dark Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="51"
            Name="Grid Table 6 Colorful Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="52"
            Name="Grid Table 7 Colorful Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="46"
            Name="Grid Table 1 Light Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="47"
            Name="Grid Table 2 Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="48"
            Name="Grid Table 3 Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="49"
            Name="Grid Table 4 Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="50"
            Name="Grid Table 5 Dark Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="51"
            Name="Grid Table 6 Colorful Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="52"
            Name="Grid Table 7 Colorful Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="46"
            Name="Grid Table 1 Light Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="47"
            Name="Grid Table 2 Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="48"
            Name="Grid Table 3 Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="49"
            Name="Grid Table 4 Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="50"
            Name="Grid Table 5 Dark Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="51"
            Name="Grid Table 6 Colorful Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="52"
            Name="Grid Table 7 Colorful Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="46"
            Name="Grid Table 1 Light Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="47"
            Name="Grid Table 2 Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="48"
            Name="Grid Table 3 Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="49"
            Name="Grid Table 4 Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="50"
            Name="Grid Table 5 Dark Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="51"
            Name="Grid Table 6 Colorful Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="52"
            Name="Grid Table 7 Colorful Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="46"
            Name="Grid Table 1 Light Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="47"
            Name="Grid Table 2 Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="48"
            Name="Grid Table 3 Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="49"
            Name="Grid Table 4 Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="50"
            Name="Grid Table 5 Dark Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="51"
            Name="Grid Table 6 Colorful Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="52"
            Name="Grid Table 7 Colorful Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="46"
            Name="List Table 1 Light"
          />
          <w:LsdException Locked="false" Priority="47" Name="List Table 2" />
          <w:LsdException Locked="false" Priority="48" Name="List Table 3" />
          <w:LsdException Locked="false" Priority="49" Name="List Table 4" />
          <w:LsdException
            Locked="false"
            Priority="50"
            Name="List Table 5 Dark"
          />
          <w:LsdException
            Locked="false"
            Priority="51"
            Name="List Table 6 Colorful"
          />
          <w:LsdException
            Locked="false"
            Priority="52"
            Name="List Table 7 Colorful"
          />
          <w:LsdException
            Locked="false"
            Priority="46"
            Name="List Table 1 Light Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="47"
            Name="List Table 2 Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="48"
            Name="List Table 3 Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="49"
            Name="List Table 4 Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="50"
            Name="List Table 5 Dark Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="51"
            Name="List Table 6 Colorful Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="52"
            Name="List Table 7 Colorful Accent 1"
          />
          <w:LsdException
            Locked="false"
            Priority="46"
            Name="List Table 1 Light Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="47"
            Name="List Table 2 Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="48"
            Name="List Table 3 Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="49"
            Name="List Table 4 Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="50"
            Name="List Table 5 Dark Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="51"
            Name="List Table 6 Colorful Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="52"
            Name="List Table 7 Colorful Accent 2"
          />
          <w:LsdException
            Locked="false"
            Priority="46"
            Name="List Table 1 Light Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="47"
            Name="List Table 2 Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="48"
            Name="List Table 3 Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="49"
            Name="List Table 4 Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="50"
            Name="List Table 5 Dark Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="51"
            Name="List Table 6 Colorful Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="52"
            Name="List Table 7 Colorful Accent 3"
          />
          <w:LsdException
            Locked="false"
            Priority="46"
            Name="List Table 1 Light Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="47"
            Name="List Table 2 Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="48"
            Name="List Table 3 Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="49"
            Name="List Table 4 Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="50"
            Name="List Table 5 Dark Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="51"
            Name="List Table 6 Colorful Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="52"
            Name="List Table 7 Colorful Accent 4"
          />
          <w:LsdException
            Locked="false"
            Priority="46"
            Name="List Table 1 Light Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="47"
            Name="List Table 2 Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="48"
            Name="List Table 3 Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="49"
            Name="List Table 4 Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="50"
            Name="List Table 5 Dark Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="51"
            Name="List Table 6 Colorful Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="52"
            Name="List Table 7 Colorful Accent 5"
          />
          <w:LsdException
            Locked="false"
            Priority="46"
            Name="List Table 1 Light Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="47"
            Name="List Table 2 Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="48"
            Name="List Table 3 Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="49"
            Name="List Table 4 Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="50"
            Name="List Table 5 Dark Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="51"
            Name="List Table 6 Colorful Accent 6"
          />
          <w:LsdException
            Locked="false"
            Priority="52"
            Name="List Table 7 Colorful Accent 6"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Mention"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Smart Hyperlink"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Hashtag"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Unresolved Mention"
          />
          <w:LsdException
            Locked="false"
            SemiHidden="true"
            UnhideWhenUsed="true"
            Name="Smart Link"
          />
        </w:LatentStyles> </xml
    ><![endif]-->
    <style>
      <!--
       /* Font Definitions */
       @font-face
      	{font-family:Wingdings;
      	panose-1:5 0 0 0 0 0 0 0 0 0;
      	mso-font-charset:2;
      	mso-generic-font-family:auto;
      	mso-font-pitch:variable;
      	mso-font-signature:0 268435456 0 0 -2147483648 0;}
      @font-face
      	{font-family:宋体;
      	panose-1:2 1 6 0 3 1 1 1 1 1;
      	mso-font-alt:SimSun;
      	mso-font-charset:134;
      	mso-generic-font-family:auto;
      	mso-font-pitch:variable;
      	mso-font-signature:3 680460288 22 0 262145 0;}
      @font-face
      	{font-family:"Cambria Math";
      	panose-1:2 4 5 3 5 4 6 3 2 4;
      	mso-font-charset:0;
      	mso-generic-font-family:roman;
      	mso-font-pitch:variable;
      	mso-font-signature:-536869121 1107305727 33554432 0 415 0;}
      @font-face
      	{font-family:微软雅黑;
      	panose-1:2 11 5 3 2 2 4 2 2 4;
      	mso-font-charset:134;
      	mso-generic-font-family:swiss;
      	mso-font-pitch:variable;
      	mso-font-signature:-2147483001 718224464 22 0 262175 0;}
      @font-face
      	{font-family:"Times New Roman \(Body CS\)";
      	mso-font-alt:"Times New Roman";
      	mso-font-charset:0;
      	mso-generic-font-family:roman;
      	mso-font-pitch:auto;
      	mso-font-signature:0 0 0 0 0 0;}
      @font-face
      	{font-family:"Times New Roman \(Headings CS\)";
      	mso-font-alt:"Times New Roman";
      	mso-font-charset:0;
      	mso-generic-font-family:roman;
      	mso-font-pitch:auto;
      	mso-font-signature:0 0 0 0 0 0;}
      @font-face
      	{font-family:"Helvetica Neue";
      	mso-font-alt:Sylfaen;
      	mso-font-charset:0;
      	mso-generic-font-family:auto;
      	mso-font-pitch:variable;
      	mso-font-signature:-452984065 1342208475 16 0 1 0;}
      @font-face
      	{font-family:"\@微软雅黑";
      	mso-font-charset:134;
      	mso-generic-font-family:swiss;
      	mso-font-pitch:variable;
      	mso-font-signature:-2147483001 718224464 22 0 262175 0;}
      @font-face
      	{font-family:"\@宋体";
      	panose-1:2 1 6 0 3 1 1 1 1 1;
      	mso-font-charset:134;
      	mso-generic-font-family:auto;
      	mso-font-pitch:variable;
      	mso-font-signature:3 680460288 22 0 262145 0;}
       /* Style Definitions */
       p.MsoNormal, li.MsoNormal, div.MsoNormal
      	{mso-style-unhide:no;
      	mso-style-qformat:yes;
      	mso-style-parent:"";
      	margin:0cm;
      	mso-pagination:widow-orphan;
      	font-size:11.0pt;
      	mso-bidi-font-size:12.0pt;
      	font-family:"微软雅黑",sans-serif;
      	mso-bidi-font-family:"Times New Roman \(Body CS\)";}
      h1
      	{mso-style-priority:9;
      	mso-style-unhide:no;
      	mso-style-qformat:yes;
      	mso-style-link:"标题 1 字符";
      	mso-style-next:正文;
      	margin-top:12.0pt;
      	margin-right:0cm;
      	margin-bottom:0cm;
      	margin-left:0cm;
      	text-indent:0cm;
      	line-height:150%;
      	mso-pagination:widow-orphan lines-together;
      	page-break-after:avoid;
      	mso-outline-level:1;
      	mso-list:l12 level1 lfo1;
      	font-size:16.0pt;
      	font-family:"微软雅黑",sans-serif;
      	mso-bidi-font-family:"Times New Roman \(Headings CS\)";
      	color:black;
      	mso-themecolor:text1;
      	mso-font-kerning:0pt;
      	font-weight:bold;
      	mso-bidi-font-weight:normal;}
      h2
      	{mso-style-priority:9;
      	mso-style-qformat:yes;
      	mso-style-link:"标题 2 字符";
      	mso-style-next:正文;
      	margin-top:2.0pt;
      	margin-right:0cm;
      	margin-bottom:0cm;
      	margin-left:0cm;
      	text-indent:0cm;
      	line-height:150%;
      	mso-pagination:widow-orphan lines-together;
      	page-break-after:avoid;
      	mso-outline-level:2;
      	mso-list:l12 level2 lfo1;
      	font-size:15.0pt;
      	mso-bidi-font-size:13.0pt;
      	font-family:"微软雅黑",sans-serif;
      	mso-bidi-font-family:"Times New Roman \(Headings CS\)";
      	color:black;
      	mso-themecolor:text1;
      	font-weight:bold;
      	mso-bidi-font-weight:normal;}
      h3
      	{mso-style-priority:9;
      	mso-style-qformat:yes;
      	mso-style-link:"标题 3 字符";
      	mso-style-next:正文;
      	margin-top:2.0pt;
      	margin-right:0cm;
      	margin-bottom:0cm;
      	margin-left:0cm;
      	text-indent:0cm;
      	line-height:150%;
      	mso-pagination:widow-orphan lines-together;
      	page-break-after:avoid;
      	mso-outline-level:3;
      	mso-list:l12 level3 lfo1;
      	font-size:14.0pt;
      	mso-bidi-font-size:12.0pt;
      	font-family:"微软雅黑",sans-serif;
      	mso-bidi-font-family:"Times New Roman \(Headings CS\)";
      	color:black;
      	mso-themecolor:text1;
      	font-weight:bold;
      	mso-bidi-font-weight:normal;}
      h4
      	{mso-style-priority:9;
      	mso-style-qformat:yes;
      	mso-style-link:"标题 4 字符";
      	mso-style-next:正文;
      	margin-top:2.0pt;
      	margin-right:0cm;
      	margin-bottom:0cm;
      	margin-left:0cm;
      	text-indent:0cm;
      	line-height:150%;
      	mso-pagination:widow-orphan lines-together;
      	page-break-after:avoid;
      	mso-outline-level:4;
      	mso-list:l12 level4 lfo1;
      	font-size:13.0pt;
      	mso-bidi-font-size:12.0pt;
      	font-family:"微软雅黑",sans-serif;
      	mso-bidi-font-family:"Times New Roman \(Headings CS\)";
      	color:black;
      	mso-themecolor:text1;
      	font-weight:bold;
      	mso-bidi-font-weight:normal;
      	mso-bidi-font-style:italic;}
      h5
      	{mso-style-priority:9;
      	mso-style-qformat:yes;
      	mso-style-link:"标题 5 字符";
      	mso-style-next:正文;
      	margin-top:2.0pt;
      	margin-right:0cm;
      	margin-bottom:0cm;
      	margin-left:0cm;
      	text-indent:0cm;
      	line-height:150%;
      	mso-pagination:widow-orphan lines-together;
      	page-break-after:avoid;
      	mso-outline-level:5;
      	mso-list:l12 level5 lfo1;
      	font-size:11.0pt;
      	mso-bidi-font-size:12.0pt;
      	font-family:"微软雅黑",sans-serif;
      	mso-bidi-font-family:"Times New Roman \(Headings CS\)";
      	color:black;
      	mso-themecolor:text1;
      	font-weight:bold;
      	mso-bidi-font-weight:normal;}
      h6
      	{mso-style-priority:9;
      	mso-style-qformat:yes;
      	mso-style-link:"标题 6 字符";
      	mso-style-next:正文;
      	margin-top:2.0pt;
      	margin-right:0cm;
      	margin-bottom:0cm;
      	margin-left:0cm;
      	text-indent:0cm;
      	mso-pagination:widow-orphan lines-together;
      	page-break-after:avoid;
      	mso-outline-level:6;
      	mso-list:l12 level6 lfo1;
      	font-size:11.0pt;
      	mso-bidi-font-size:12.0pt;
      	font-family:"微软雅黑",sans-serif;
      	mso-bidi-font-family:"Times New Roman \(Headings CS\)";
      	color:black;
      	mso-themecolor:text1;
      	font-weight:bold;
      	mso-bidi-font-weight:normal;}
      p.MsoHeading7, li.MsoHeading7, div.MsoHeading7
      	{mso-style-noshow:yes;
      	mso-style-priority:9;
      	mso-style-qformat:yes;
      	mso-style-link:"标题 7 字符";
      	mso-style-next:正文;
      	margin-top:2.0pt;
      	margin-right:0cm;
      	margin-bottom:0cm;
      	margin-left:0cm;
      	text-indent:0cm;
      	mso-pagination:widow-orphan lines-together;
      	page-break-after:avoid;
      	mso-outline-level:7;
      	mso-list:l12 level7 lfo1;
      	font-size:11.0pt;
      	mso-bidi-font-size:12.0pt;
      	font-family:"Calibri Light",sans-serif;
      	mso-ascii-font-family:"Calibri Light";
      	mso-ascii-theme-font:major-latin;
      	mso-fareast-font-family:"等线 Light";
      	mso-fareast-theme-font:major-fareast;
      	mso-hansi-font-family:"Calibri Light";
      	mso-hansi-theme-font:major-latin;
      	mso-bidi-font-family:"Times New Roman";
      	mso-bidi-theme-font:major-bidi;
      	color:#1F3763;
      	mso-themecolor:accent1;
      	mso-themeshade:127;
      	font-style:italic;}
      p.MsoToc1, li.MsoToc1, div.MsoToc1
      	{mso-style-update:auto;
      	mso-style-priority:39;
      	mso-style-next:正文;
      	margin-top:6.0pt;
      	margin-right:0cm;
      	margin-bottom:6.0pt;
      	margin-left:0cm;
      	mso-pagination:widow-orphan;
      	font-size:10.0pt;
      	font-family:"Calibri",sans-serif;
      	mso-ascii-font-family:Calibri;
      	mso-ascii-theme-font:minor-latin;
      	mso-fareast-font-family:微软雅黑;
      	mso-hansi-font-family:Calibri;
      	mso-hansi-theme-font:minor-latin;
      	mso-bidi-font-family:"Times New Roman \(Body CS\)";
      	text-transform:uppercase;
      	font-weight:bold;}
      p.MsoToc2, li.MsoToc2, div.MsoToc2
      	{mso-style-update:auto;
      	mso-style-priority:39;
      	mso-style-next:正文;
      	margin-top:0cm;
      	margin-right:0cm;
      	margin-bottom:0cm;
      	margin-left:11.0pt;
      	mso-pagination:widow-orphan;
      	font-size:10.0pt;
      	font-family:"Calibri",sans-serif;
      	mso-ascii-font-family:Calibri;
      	mso-ascii-theme-font:minor-latin;
      	mso-fareast-font-family:微软雅黑;
      	mso-hansi-font-family:Calibri;
      	mso-hansi-theme-font:minor-latin;
      	mso-bidi-font-family:"Times New Roman \(Body CS\)";
      	font-variant:small-caps;}
      p.MsoToc3, li.MsoToc3, div.MsoToc3
      	{mso-style-update:auto;
      	mso-style-priority:39;
      	mso-style-next:正文;
      	margin-top:0cm;
      	margin-right:0cm;
      	margin-bottom:0cm;
      	margin-left:22.0pt;
      	mso-pagination:widow-orphan;
      	font-size:10.0pt;
      	font-family:"Calibri",sans-serif;
      	mso-ascii-font-family:Calibri;
      	mso-ascii-theme-font:minor-latin;
      	mso-fareast-font-family:微软雅黑;
      	mso-hansi-font-family:Calibri;
      	mso-hansi-theme-font:minor-latin;
      	mso-bidi-font-family:"Times New Roman \(Body CS\)";
      	font-style:italic;}
      p.MsoToc4, li.MsoToc4, div.MsoToc4
      	{mso-style-update:auto;
      	mso-style-noshow:yes;
      	mso-style-priority:39;
      	mso-style-next:正文;
      	margin-top:0cm;
      	margin-right:0cm;
      	margin-bottom:0cm;
      	margin-left:33.0pt;
      	mso-pagination:widow-orphan;
      	font-size:9.0pt;
      	font-family:"Calibri",sans-serif;
      	mso-ascii-font-family:Calibri;
      	mso-ascii-theme-font:minor-latin;
      	mso-fareast-font-family:微软雅黑;
      	mso-hansi-font-family:Calibri;
      	mso-hansi-theme-font:minor-latin;
      	mso-bidi-font-family:"Times New Roman \(Body CS\)";}
      p.MsoToc5, li.MsoToc5, div.MsoToc5
      	{mso-style-update:auto;
      	mso-style-noshow:yes;
      	mso-style-priority:39;
      	mso-style-next:正文;
      	margin-top:0cm;
      	margin-right:0cm;
      	margin-bottom:0cm;
      	margin-left:44.0pt;
      	mso-pagination:widow-orphan;
      	font-size:9.0pt;
      	font-family:"Calibri",sans-serif;
      	mso-ascii-font-family:Calibri;
      	mso-ascii-theme-font:minor-latin;
      	mso-fareast-font-family:微软雅黑;
      	mso-hansi-font-family:Calibri;
      	mso-hansi-theme-font:minor-latin;
      	mso-bidi-font-family:"Times New Roman \(Body CS\)";}
      p.MsoToc6, li.MsoToc6, div.MsoToc6
      	{mso-style-update:auto;
      	mso-style-noshow:yes;
      	mso-style-priority:39;
      	mso-style-next:正文;
      	margin-top:0cm;
      	margin-right:0cm;
      	margin-bottom:0cm;
      	margin-left:55.0pt;
      	mso-pagination:widow-orphan;
      	font-size:9.0pt;
      	font-family:"Calibri",sans-serif;
      	mso-ascii-font-family:Calibri;
      	mso-ascii-theme-font:minor-latin;
      	mso-fareast-font-family:微软雅黑;
      	mso-hansi-font-family:Calibri;
      	mso-hansi-theme-font:minor-latin;
      	mso-bidi-font-family:"Times New Roman \(Body CS\)";}
      p.MsoToc7, li.MsoToc7, div.MsoToc7
      	{mso-style-update:auto;
      	mso-style-noshow:yes;
      	mso-style-priority:39;
      	mso-style-next:正文;
      	margin-top:0cm;
      	margin-right:0cm;
      	margin-bottom:0cm;
      	margin-left:66.0pt;
      	mso-pagination:widow-orphan;
      	font-size:9.0pt;
      	font-family:"Calibri",sans-serif;
      	mso-ascii-font-family:Calibri;
      	mso-ascii-theme-font:minor-latin;
      	mso-fareast-font-family:微软雅黑;
      	mso-hansi-font-family:Calibri;
      	mso-hansi-theme-font:minor-latin;
      	mso-bidi-font-family:"Times New Roman \(Body CS\)";}
      p.MsoToc8, li.MsoToc8, div.MsoToc8
      	{mso-style-update:auto;
      	mso-style-noshow:yes;
      	mso-style-priority:39;
      	mso-style-next:正文;
      	margin-top:0cm;
      	margin-right:0cm;
      	margin-bottom:0cm;
      	margin-left:77.0pt;
      	mso-pagination:widow-orphan;
      	font-size:9.0pt;
      	font-family:"Calibri",sans-serif;
      	mso-ascii-font-family:Calibri;
      	mso-ascii-theme-font:minor-latin;
      	mso-fareast-font-family:微软雅黑;
      	mso-hansi-font-family:Calibri;
      	mso-hansi-theme-font:minor-latin;
      	mso-bidi-font-family:"Times New Roman \(Body CS\)";}
      p.MsoToc9, li.MsoToc9, div.MsoToc9
      	{mso-style-update:auto;
      	mso-style-noshow:yes;
      	mso-style-priority:39;
      	mso-style-next:正文;
      	margin-top:0cm;
      	margin-right:0cm;
      	margin-bottom:0cm;
      	margin-left:88.0pt;
      	mso-pagination:widow-orphan;
      	font-size:9.0pt;
      	font-family:"Calibri",sans-serif;
      	mso-ascii-font-family:Calibri;
      	mso-ascii-theme-font:minor-latin;
      	mso-fareast-font-family:微软雅黑;
      	mso-hansi-font-family:Calibri;
      	mso-hansi-theme-font:minor-latin;
      	mso-bidi-font-family:"Times New Roman \(Body CS\)";}
      p.MsoHeader, li.MsoHeader, div.MsoHeader
      	{mso-style-priority:99;
      	mso-style-link:"页眉 字符";
      	margin:0cm;
      	mso-pagination:widow-orphan;
      	tab-stops:center 234.0pt right 468.0pt;
      	font-size:11.0pt;
      	mso-bidi-font-size:12.0pt;
      	font-family:"微软雅黑",sans-serif;
      	mso-bidi-font-family:"Times New Roman \(Body CS\)";}
      p.MsoFooter, li.MsoFooter, div.MsoFooter
      	{mso-style-priority:99;
      	mso-style-link:"页脚 字符";
      	margin:0cm;
      	mso-pagination:widow-orphan;
      	tab-stops:center 234.0pt right 468.0pt;
      	font-size:11.0pt;
      	mso-bidi-font-size:12.0pt;
      	font-family:"微软雅黑",sans-serif;
      	mso-bidi-font-family:"Times New Roman \(Body CS\)";}
      a:link, span.MsoHyperlink
      	{mso-style-priority:99;
      	color:#0563C1;
      	mso-themecolor:hyperlink;
      	text-decoration:underline;
      	text-underline:single;}
      a:visited, span.MsoHyperlinkFollowed
      	{mso-style-noshow:yes;
      	mso-style-priority:99;
      	color:#954F72;
      	mso-themecolor:followedhyperlink;
      	text-decoration:underline;
      	text-underline:single;}
      p
      	{mso-style-noshow:yes;
      	mso-style-priority:99;
      	mso-margin-top-alt:auto;
      	margin-right:0cm;
      	mso-margin-bottom-alt:auto;
      	margin-left:0cm;
      	mso-pagination:widow-orphan;
      	font-size:12.0pt;
      	font-family:宋体;
      	mso-bidi-font-family:宋体;}
      p.MsoNoSpacing, li.MsoNoSpacing, div.MsoNoSpacing
      	{mso-style-priority:1;
      	mso-style-unhide:no;
      	mso-style-qformat:yes;
      	mso-style-parent:"";
      	mso-style-link:"无间隔 字符";
      	margin:0cm;
      	mso-pagination:widow-orphan;
      	font-size:11.0pt;
      	font-family:"Calibri",sans-serif;
      	mso-ascii-font-family:Calibri;
      	mso-ascii-theme-font:minor-latin;
      	mso-fareast-font-family:等线;
      	mso-fareast-theme-font:minor-fareast;
      	mso-hansi-font-family:Calibri;
      	mso-hansi-theme-font:minor-latin;
      	mso-bidi-font-family:"Times New Roman";
      	mso-bidi-theme-font:minor-bidi;}
      p.MsoListParagraph, li.MsoListParagraph, div.MsoListParagraph
      	{mso-style-priority:34;
      	mso-style-unhide:no;
      	mso-style-qformat:yes;
      	margin-top:0cm;
      	margin-right:0cm;
      	margin-bottom:0cm;
      	margin-left:36.0pt;
      	mso-add-space:auto;
      	mso-pagination:widow-orphan;
      	font-size:11.0pt;
        text-align: left;
        margin-left: 28.5% !important;
      	mso-bidi-font-size:12.0pt;
      	font-family:"微软雅黑",sans-serif;
      	mso-bidi-font-family:"Times New Roman \(Body CS\)";}
      p.MsoListParagraphCxSpFirst, li.MsoListParagraphCxSpFirst, div.MsoListParagraphCxSpFirst
      	{mso-style-priority:34;
      	mso-style-unhide:no;
      	mso-style-qformat:yes;
      	mso-style-type:export-only;
      	margin-top:0cm;
      	margin-right:0cm;
      	margin-bottom:0cm;
      	margin-left:36.0pt;
      	mso-add-space:auto;
      	mso-pagination:widow-orphan;
      	font-size:11.0pt;
      	mso-bidi-font-size:12.0pt;
      	font-family:"微软雅黑",sans-serif;
      	mso-bidi-font-family:"Times New Roman \(Body CS\)";}
      p.MsoListParagraphCxSpMiddle, li.MsoListParagraphCxSpMiddle, div.MsoListParagraphCxSpMiddle
      	{mso-style-priority:34;
      	mso-style-unhide:no;
      	mso-style-qformat:yes;
      	mso-style-type:export-only;
      	margin-top:0cm;
      	margin-right:0cm;
      	margin-bottom:0cm;
      	margin-left:36.0pt;
      	mso-add-space:auto;
      	mso-pagination:widow-orphan;
      	font-size:11.0pt;
      	mso-bidi-font-size:12.0pt;
      	font-family:"微软雅黑",sans-serif;
      	mso-bidi-font-family:"Times New Roman \(Body CS\)";}
      p.MsoListParagraphCxSpLast, li.MsoListParagraphCxSpLast, div.MsoListParagraphCxSpLast
      	{mso-style-priority:34;
      	mso-style-unhide:no;
      	mso-style-qformat:yes;
      	mso-style-type:export-only;
      	margin-top:0cm;
      	margin-right:0cm;
      	margin-bottom:0cm;
      	margin-left:36.0pt;
      	mso-add-space:auto;
      	mso-pagination:widow-orphan;
      	font-size:11.0pt;
      	mso-bidi-font-size:12.0pt;
      	font-family:"微软雅黑",sans-serif;
      	mso-bidi-font-family:"Times New Roman \(Body CS\)";}
      p.MsoTocHeading, li.MsoTocHeading, div.MsoTocHeading
      	{mso-style-priority:39;
      	mso-style-qformat:yes;
      	mso-style-parent:"标题 1";
      	mso-style-next:正文;
      	margin-top:24.0pt;
      	margin-right:0cm;
      	margin-bottom:0cm;
      	margin-left:0cm;
      	line-height:115%;
      	mso-pagination:widow-orphan lines-together;
      	page-break-after:avoid;
      	font-size:14.0pt;
      	font-family:"Calibri Light",sans-serif;
      	mso-ascii-font-family:"Calibri Light";
      	mso-ascii-theme-font:major-latin;
      	mso-fareast-font-family:"等线 Light";
      	mso-fareast-theme-font:major-fareast;
      	mso-hansi-font-family:"Calibri Light";
      	mso-hansi-theme-font:major-latin;
      	mso-bidi-font-family:"Times New Roman";
      	mso-bidi-theme-font:major-bidi;
      	color:#2F5496;
      	mso-themecolor:accent1;
      	mso-themeshade:191;
      	mso-fareast-language:EN-US;
      	font-weight:bold;}
      span.1
      	{mso-style-name:"标题 1 字符";
      	mso-style-priority:9;
      	mso-style-unhide:no;
      	mso-style-locked:yes;
      	mso-style-link:"标题 1";
      	mso-ansi-font-size:16.0pt;
      	mso-bidi-font-size:16.0pt;
      	font-family:"微软雅黑",sans-serif;
      	mso-ascii-font-family:微软雅黑;
      	mso-fareast-font-family:微软雅黑;
      	mso-hansi-font-family:微软雅黑;
      	mso-bidi-font-family:"Times New Roman \(Headings CS\)";
      	color:black;
      	mso-themecolor:text1;
      	font-weight:bold;
      	mso-bidi-font-weight:normal;}
      span.2
      	{mso-style-name:"标题 2 字符";
      	mso-style-priority:9;
      	mso-style-unhide:no;
      	mso-style-locked:yes;
      	mso-style-link:"标题 2";
      	mso-ansi-font-size:15.0pt;
      	mso-bidi-font-size:13.0pt;
      	font-family:"微软雅黑",sans-serif;
      	mso-ascii-font-family:微软雅黑;
      	mso-fareast-font-family:微软雅黑;
      	mso-hansi-font-family:微软雅黑;
      	mso-bidi-font-family:"Times New Roman \(Headings CS\)";
      	color:black;
      	mso-themecolor:text1;
      	font-weight:bold;
      	mso-bidi-font-weight:normal;}
      span.3
      	{mso-style-name:"标题 3 字符";
      	mso-style-priority:9;
      	mso-style-unhide:no;
      	mso-style-locked:yes;
      	mso-style-link:"标题 3";
      	mso-ansi-font-size:14.0pt;
      	font-family:"微软雅黑",sans-serif;
      	mso-ascii-font-family:微软雅黑;
      	mso-fareast-font-family:微软雅黑;
      	mso-hansi-font-family:微软雅黑;
      	mso-bidi-font-family:"Times New Roman \(Headings CS\)";
      	color:black;
      	mso-themecolor:text1;
      	font-weight:bold;
      	mso-bidi-font-weight:normal;}
      span.4
      	{mso-style-name:"标题 4 字符";
      	mso-style-priority:9;
      	mso-style-unhide:no;
      	mso-style-locked:yes;
      	mso-style-link:"标题 4";
      	mso-ansi-font-size:13.0pt;
      	font-family:"微软雅黑",sans-serif;
      	mso-ascii-font-family:微软雅黑;
      	mso-fareast-font-family:微软雅黑;
      	mso-hansi-font-family:微软雅黑;
      	mso-bidi-font-family:"Times New Roman \(Headings CS\)";
      	color:black;
      	mso-themecolor:text1;
      	font-weight:bold;
      	mso-bidi-font-weight:normal;
      	mso-bidi-font-style:italic;}
      span.5
      	{mso-style-name:"标题 5 字符";
      	mso-style-priority:9;
      	mso-style-unhide:no;
      	mso-style-locked:yes;
      	mso-style-link:"标题 5";
      	font-family:"微软雅黑",sans-serif;
      	mso-ascii-font-family:微软雅黑;
      	mso-fareast-font-family:微软雅黑;
      	mso-hansi-font-family:微软雅黑;
      	mso-bidi-font-family:"Times New Roman \(Headings CS\)";
      	color:black;
      	mso-themecolor:text1;
      	font-weight:bold;
      	mso-bidi-font-weight:normal;}
      span.6
      	{mso-style-name:"标题 6 字符";
      	mso-style-priority:9;
      	mso-style-unhide:no;
      	mso-style-locked:yes;
      	mso-style-link:"标题 6";
      	font-family:"微软雅黑",sans-serif;
      	mso-ascii-font-family:微软雅黑;
      	mso-fareast-font-family:微软雅黑;
      	mso-hansi-font-family:微软雅黑;
      	mso-bidi-font-family:"Times New Roman \(Headings CS\)";
      	color:black;
      	mso-themecolor:text1;
      	font-weight:bold;
      	mso-bidi-font-weight:normal;}
      span.7
      	{mso-style-name:"标题 7 字符";
      	mso-style-noshow:yes;
      	mso-style-priority:9;
      	mso-style-unhide:no;
      	mso-style-locked:yes;
      	mso-style-link:"标题 7";
      	font-family:"Calibri Light",sans-serif;
      	mso-ascii-font-family:"Calibri Light";
      	mso-ascii-theme-font:major-latin;
      	mso-fareast-font-family:"等线 Light";
      	mso-fareast-theme-font:major-fareast;
      	mso-hansi-font-family:"Calibri Light";
      	mso-hansi-theme-font:major-latin;
      	mso-bidi-font-family:"Times New Roman";
      	mso-bidi-theme-font:major-bidi;
      	color:#1F3763;
      	mso-themecolor:accent1;
      	mso-themeshade:127;
      	font-style:italic;}
      span.a
      	{mso-style-name:"无间隔 字符";
      	mso-style-priority:1;
      	mso-style-unhide:no;
      	mso-style-locked:yes;
      	mso-style-link:无间隔;
      	mso-ansi-font-size:11.0pt;
      	mso-bidi-font-size:11.0pt;
      	mso-ansi-language:EN-US;}
      span.a0
      	{mso-style-name:"页眉 字符";
      	mso-style-priority:99;
      	mso-style-unhide:no;
      	mso-style-locked:yes;
      	mso-style-link:页眉;
      	mso-ansi-font-size:11.0pt;
      	font-family:"微软雅黑",sans-serif;
      	mso-ascii-font-family:微软雅黑;
      	mso-fareast-font-family:微软雅黑;
      	mso-hansi-font-family:微软雅黑;
      	mso-bidi-font-family:"Times New Roman \(Body CS\)";}
      span.a1
      	{mso-style-name:"页脚 字符";
      	mso-style-priority:99;
      	mso-style-unhide:no;
      	mso-style-locked:yes;
      	mso-style-link:页脚;
      	mso-ansi-font-size:11.0pt;
      	font-family:"微软雅黑",sans-serif;
      	mso-ascii-font-family:微软雅黑;
      	mso-fareast-font-family:微软雅黑;
      	mso-hansi-font-family:微软雅黑;
      	mso-bidi-font-family:"Times New Roman \(Body CS\)";}
      .MsoChpDefault
      	{mso-style-type:export-only;
      	mso-default-props:yes;
      	font-size:12.0pt;
      	mso-ansi-font-size:12.0pt;
      	mso-bidi-font-size:12.0pt;
      	font-family:"Calibri",sans-serif;
      	mso-bidi-font-family:"Times New Roman";
      	mso-bidi-theme-font:minor-bidi;
      	mso-font-kerning:0pt;
      	mso-ligatures:none;}
       /* Page Definitions */
       @page
      	{mso-page-border-surround-header:no;
      	mso-page-border-surround-footer:no;
      	mso-footnote-separator:url("AWS如何创建访问密钥（AKSK）.files/header.htm") fs;
      	mso-footnote-continuation-separator:url("AWS如何创建访问密钥（AKSK）.files/header.htm") fcs;
      	mso-endnote-separator:url("AWS如何创建访问密钥（AKSK）.files/header.htm") es;
      	mso-endnote-continuation-separator:url("AWS如何创建访问密钥（AKSK）.files/header.htm") ecs;}
      @page WordSection1
      	{size:595.3pt 841.9pt;
      	margin:36.0pt 36.0pt 36.0pt 36.0pt;
      	mso-header-margin:42.55pt;
      	mso-footer-margin:49.6pt;
      	mso-title-page:yes;
      	mso-header:url("AWS如何创建访问密钥（AKSK）.files/header.htm") h1;
      	mso-footer:url("AWS如何创建访问密钥（AKSK）.files/header.htm") f1;
      	mso-paper-source:0;
      	layout-grid:15.6pt;}
      div.WordSection1
      	{page:WordSection1;}
       /* List Definitions */
       @list l0
      	{mso-list-id:125662682;
      	mso-list-type:hybrid;
      	mso-list-template-ids:-1780473174 -1092846592 67698713 67698715 67698703 67698713 67698715 67698703 67698713 67698715;}
      @list l0:level1
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:18.0pt;
      	text-indent:-18.0pt;}
      @list l0:level2
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%2\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:42.0pt;
      	text-indent:-21.0pt;}
      @list l0:level3
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:63.0pt;
      	text-indent:-21.0pt;}
      @list l0:level4
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:84.0pt;
      	text-indent:-21.0pt;}
      @list l0:level5
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%5\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:105.0pt;
      	text-indent:-21.0pt;}
      @list l0:level6
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:126.0pt;
      	text-indent:-21.0pt;}
      @list l0:level7
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:147.0pt;
      	text-indent:-21.0pt;}
      @list l0:level8
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%8\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:168.0pt;
      	text-indent:-21.0pt;}
      @list l0:level9
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:189.0pt;
      	text-indent:-21.0pt;}
      @list l1
      	{mso-list-id:272442871;
      	mso-list-type:hybrid;
      	mso-list-template-ids:386840138 -949302928 67698713 67698715 67698703 67698713 67698715 67698703 67698713 67698715;}
      @list l1:level1
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:18.0pt;
      	text-indent:-18.0pt;}
      @list l1:level2
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%2\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:42.0pt;
      	text-indent:-21.0pt;}
      @list l1:level3
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:63.0pt;
      	text-indent:-21.0pt;}
      @list l1:level4
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:84.0pt;
      	text-indent:-21.0pt;}
      @list l1:level5
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%5\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:105.0pt;
      	text-indent:-21.0pt;}
      @list l1:level6
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:126.0pt;
      	text-indent:-21.0pt;}
      @list l1:level7
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:147.0pt;
      	text-indent:-21.0pt;}
      @list l1:level8
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%8\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:168.0pt;
      	text-indent:-21.0pt;}
      @list l1:level9
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:189.0pt;
      	text-indent:-21.0pt;}
      @list l2
      	{mso-list-id:300162175;
      	mso-list-type:hybrid;
      	mso-list-template-ids:1188736842 -503260910 67698713 67698715 67698703 67698713 67698715 67698703 67698713 67698715;}
      @list l2:level1
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:18.0pt;
      	text-indent:-18.0pt;}
      @list l2:level2
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%2\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:42.0pt;
      	text-indent:-21.0pt;}
      @list l2:level3
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:63.0pt;
      	text-indent:-21.0pt;}
      @list l2:level4
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:84.0pt;
      	text-indent:-21.0pt;}
      @list l2:level5
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%5\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:105.0pt;
      	text-indent:-21.0pt;}
      @list l2:level6
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:126.0pt;
      	text-indent:-21.0pt;}
      @list l2:level7
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:147.0pt;
      	text-indent:-21.0pt;}
      @list l2:level8
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%8\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:168.0pt;
      	text-indent:-21.0pt;}
      @list l2:level9
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:189.0pt;
      	text-indent:-21.0pt;}
      @list l3
      	{mso-list-id:384528514;
      	mso-list-type:hybrid;
      	mso-list-template-ids:1063448618 -949302928 67698713 67698715 67698703 67698713 67698715 67698703 67698713 67698715;}
      @list l3:level1
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:18.0pt;
      	text-indent:-18.0pt;}
      @list l3:level2
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%2\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:42.0pt;
      	text-indent:-21.0pt;}
      @list l3:level3
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:63.0pt;
      	text-indent:-21.0pt;}
      @list l3:level4
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:84.0pt;
      	text-indent:-21.0pt;}
      @list l3:level5
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%5\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:105.0pt;
      	text-indent:-21.0pt;}
      @list l3:level6
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:126.0pt;
      	text-indent:-21.0pt;}
      @list l3:level7
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:147.0pt;
      	text-indent:-21.0pt;}
      @list l3:level8
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%8\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:168.0pt;
      	text-indent:-21.0pt;}
      @list l3:level9
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:189.0pt;
      	text-indent:-21.0pt;}
      @list l4
      	{mso-list-id:434373290;
      	mso-list-type:hybrid;
      	mso-list-template-ids:-981591966 67698703 67698713 67698715 67698703 67698713 67698715 67698703 67698713 67698715;}
      @list l4:level1
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:21.0pt;
      	text-indent:-21.0pt;}
      @list l4:level2
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%2\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:42.0pt;
      	text-indent:-21.0pt;}
      @list l4:level3
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:63.0pt;
      	text-indent:-21.0pt;}
      @list l4:level4
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:84.0pt;
      	text-indent:-21.0pt;}
      @list l4:level5
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%5\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:105.0pt;
      	text-indent:-21.0pt;}
      @list l4:level6
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:126.0pt;
      	text-indent:-21.0pt;}
      @list l4:level7
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:147.0pt;
      	text-indent:-21.0pt;}
      @list l4:level8
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%8\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:168.0pt;
      	text-indent:-21.0pt;}
      @list l4:level9
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:189.0pt;
      	text-indent:-21.0pt;}
      @list l5
      	{mso-list-id:436632693;
      	mso-list-type:hybrid;
      	mso-list-template-ids:-1003571296 -949302928 67698713 67698715 67698703 67698713 67698715 67698703 67698713 67698715;}
      @list l5:level1
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:18.0pt;
      	text-indent:-18.0pt;}
      @list l5:level2
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%2\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:42.0pt;
      	text-indent:-21.0pt;}
      @list l5:level3
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:63.0pt;
      	text-indent:-21.0pt;}
      @list l5:level4
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:84.0pt;
      	text-indent:-21.0pt;}
      @list l5:level5
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%5\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:105.0pt;
      	text-indent:-21.0pt;}
      @list l5:level6
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:126.0pt;
      	text-indent:-21.0pt;}
      @list l5:level7
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:147.0pt;
      	text-indent:-21.0pt;}
      @list l5:level8
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%8\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:168.0pt;
      	text-indent:-21.0pt;}
      @list l5:level9
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:189.0pt;
      	text-indent:-21.0pt;}
      @list l6
      	{mso-list-id:498228497;
      	mso-list-type:hybrid;
      	mso-list-template-ids:2126910686 -949302928 67698713 67698715 67698703 67698713 67698715 67698703 67698713 67698715;}
      @list l6:level1
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:18.0pt;
      	text-indent:-18.0pt;}
      @list l6:level2
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%2\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:42.0pt;
      	text-indent:-21.0pt;}
      @list l6:level3
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:63.0pt;
      	text-indent:-21.0pt;}
      @list l6:level4
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:84.0pt;
      	text-indent:-21.0pt;}
      @list l6:level5
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%5\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:105.0pt;
      	text-indent:-21.0pt;}
      @list l6:level6
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:126.0pt;
      	text-indent:-21.0pt;}
      @list l6:level7
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:147.0pt;
      	text-indent:-21.0pt;}
      @list l6:level8
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%8\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:168.0pt;
      	text-indent:-21.0pt;}
      @list l6:level9
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:189.0pt;
      	text-indent:-21.0pt;}
      @list l7
      	{mso-list-id:1014113827;
      	mso-list-type:hybrid;
      	mso-list-template-ids:-1567474208 67698689 67698691 67698693 67698689 67698691 67698693 67698689 67698691 67698693;}
      @list l7:level1
      	{mso-level-number-format:bullet;
      	mso-level-text:;
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	text-indent:-18.0pt;
      	font-family:Symbol;}
      @list l7:level2
      	{mso-level-number-format:bullet;
      	mso-level-text:o;
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	text-indent:-18.0pt;
      	font-family:"Courier New";
      	mso-bidi-font-family:"Times New Roman";}
      @list l7:level3
      	{mso-level-number-format:bullet;
      	mso-level-text:;
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	text-indent:-18.0pt;
      	font-family:Wingdings;}
      @list l7:level4
      	{mso-level-number-format:bullet;
      	mso-level-text:;
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	text-indent:-18.0pt;
      	font-family:Symbol;}
      @list l7:level5
      	{mso-level-number-format:bullet;
      	mso-level-text:o;
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	text-indent:-18.0pt;
      	font-family:"Courier New";
      	mso-bidi-font-family:"Times New Roman";}
      @list l7:level6
      	{mso-level-number-format:bullet;
      	mso-level-text:;
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	text-indent:-18.0pt;
      	font-family:Wingdings;}
      @list l7:level7
      	{mso-level-number-format:bullet;
      	mso-level-text:;
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	text-indent:-18.0pt;
      	font-family:Symbol;}
      @list l7:level8
      	{mso-level-number-format:bullet;
      	mso-level-text:o;
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	text-indent:-18.0pt;
      	font-family:"Courier New";
      	mso-bidi-font-family:"Times New Roman";}
      @list l7:level9
      	{mso-level-number-format:bullet;
      	mso-level-text:;
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	text-indent:-18.0pt;
      	font-family:Wingdings;}
      @list l8
      	{mso-list-id:1036732774;
      	mso-list-type:hybrid;
      	mso-list-template-ids:798509042 -949302928 67698713 67698715 67698703 67698713 67698715 67698703 67698713 67698715;}
      @list l8:level1
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:18.0pt;
      	text-indent:-18.0pt;}
      @list l8:level2
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%2\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:42.0pt;
      	text-indent:-21.0pt;}
      @list l8:level3
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:63.0pt;
      	text-indent:-21.0pt;}
      @list l8:level4
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:84.0pt;
      	text-indent:-21.0pt;}
      @list l8:level5
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%5\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:105.0pt;
      	text-indent:-21.0pt;}
      @list l8:level6
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:126.0pt;
      	text-indent:-21.0pt;}
      @list l8:level7
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:147.0pt;
      	text-indent:-21.0pt;}
      @list l8:level8
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%8\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:168.0pt;
      	text-indent:-21.0pt;}
      @list l8:level9
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:189.0pt;
      	text-indent:-21.0pt;}
      @list l9
      	{mso-list-id:1091242498;
      	mso-list-type:hybrid;
      	mso-list-template-ids:-799607010 -98394112 67698713 67698715 67698703 67698713 67698715 67698703 67698713 67698715;}
      @list l9:level1
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:18.0pt;
      	text-indent:-18.0pt;}
      @list l9:level2
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%2\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:42.0pt;
      	text-indent:-21.0pt;}
      @list l9:level3
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:63.0pt;
      	text-indent:-21.0pt;}
      @list l9:level4
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:84.0pt;
      	text-indent:-21.0pt;}
      @list l9:level5
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%5\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:105.0pt;
      	text-indent:-21.0pt;}
      @list l9:level6
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:126.0pt;
      	text-indent:-21.0pt;}
      @list l9:level7
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:147.0pt;
      	text-indent:-21.0pt;}
      @list l9:level8
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%8\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:168.0pt;
      	text-indent:-21.0pt;}
      @list l9:level9
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:189.0pt;
      	text-indent:-21.0pt;}
      @list l10
      	{mso-list-id:1198466652;
      	mso-list-type:hybrid;
      	mso-list-template-ids:-1309773334 67698703 67698713 67698715 67698703 67698713 67698715 67698703 67698713 67698715;}
      @list l10:level1
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:21.0pt;
      	text-indent:-21.0pt;}
      @list l10:level2
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%2\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:42.0pt;
      	text-indent:-21.0pt;}
      @list l10:level3
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:63.0pt;
      	text-indent:-21.0pt;}
      @list l10:level4
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:84.0pt;
      	text-indent:-21.0pt;}
      @list l10:level5
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%5\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:105.0pt;
      	text-indent:-21.0pt;}
      @list l10:level6
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:126.0pt;
      	text-indent:-21.0pt;}
      @list l10:level7
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:147.0pt;
      	text-indent:-21.0pt;}
      @list l10:level8
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%8\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:168.0pt;
      	text-indent:-21.0pt;}
      @list l10:level9
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:189.0pt;
      	text-indent:-21.0pt;}
      @list l11
      	{mso-list-id:1211915405;
      	mso-list-type:hybrid;
      	mso-list-template-ids:-1331036086 1281159160 67698713 67698715 67698703 67698713 67698715 67698703 67698713 67698715;}
      @list l11:level1
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:18.0pt;
      	text-indent:-18.0pt;}
      @list l11:level2
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%2\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:42.0pt;
      	text-indent:-21.0pt;}
      @list l11:level3
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:63.0pt;
      	text-indent:-21.0pt;}
      @list l11:level4
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:84.0pt;
      	text-indent:-21.0pt;}
      @list l11:level5
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%5\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:105.0pt;
      	text-indent:-21.0pt;}
      @list l11:level6
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:126.0pt;
      	text-indent:-21.0pt;}
      @list l11:level7
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:147.0pt;
      	text-indent:-21.0pt;}
      @list l11:level8
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%8\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:168.0pt;
      	text-indent:-21.0pt;}
      @list l11:level9
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:189.0pt;
      	text-indent:-21.0pt;}
      @list l12
      	{mso-list-id:1351024677;
      	mso-list-template-ids:1133539172;}
      @list l12:level1
      	{mso-level-style-link:"标题 1";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:0cm;
      	text-indent:0cm;}
      @list l12:level2
      	{mso-level-style-link:"标题 2";
      	mso-level-text:"%1\.%2\.";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:0cm;
      	text-indent:0cm;}
      @list l12:level3
      	{mso-level-style-link:"标题 3";
      	mso-level-text:"%1\.%2\.%3\.";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:0cm;
      	text-indent:0cm;}
      @list l12:level4
      	{mso-level-style-link:"标题 4";
      	mso-level-text:"%1\.%2\.%3\.%4\.";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:0cm;
      	text-indent:0cm;}
      @list l12:level5
      	{mso-level-style-link:"标题 5";
      	mso-level-text:"%1\.%2\.%3\.%4\.%5\.";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:0cm;
      	text-indent:0cm;}
      @list l12:level6
      	{mso-level-style-link:"标题 6";
      	mso-level-text:"%1\.%2\.%3\.%4\.%5\.%6\.";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:0cm;
      	text-indent:0cm;}
      @list l12:level7
      	{mso-level-style-link:"标题 7";
      	mso-level-text:"%1\.%2\.%3\.%4\.%5\.%6\.%7\.";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:0cm;
      	text-indent:0cm;}
      @list l12:level8
      	{mso-level-text:"%1\.%2\.%3\.%4\.%5\.%6\.%7\.%8\.";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:187.2pt;
      	text-indent:-61.2pt;}
      @list l12:level9
      	{mso-level-text:"%1\.%2\.%3\.%4\.%5\.%6\.%7\.%8\.%9\.";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:216.0pt;
      	text-indent:-72.0pt;}
      @list l13
      	{mso-list-id:1435397738;
      	mso-list-type:hybrid;
      	mso-list-template-ids:1517740876 1616810996 67698713 67698715 67698703 67698713 67698715 67698703 67698713 67698715;}
      @list l13:level1
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:18.0pt;
      	text-indent:-18.0pt;}
      @list l13:level2
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%2\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:42.0pt;
      	text-indent:-21.0pt;}
      @list l13:level3
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:63.0pt;
      	text-indent:-21.0pt;}
      @list l13:level4
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:84.0pt;
      	text-indent:-21.0pt;}
      @list l13:level5
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%5\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:105.0pt;
      	text-indent:-21.0pt;}
      @list l13:level6
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:126.0pt;
      	text-indent:-21.0pt;}
      @list l13:level7
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:147.0pt;
      	text-indent:-21.0pt;}
      @list l13:level8
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%8\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:168.0pt;
      	text-indent:-21.0pt;}
      @list l13:level9
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:189.0pt;
      	text-indent:-21.0pt;}
      @list l14
      	{mso-list-id:1499244540;
      	mso-list-type:simple;
      	mso-list-template-ids:1499244540;}
      @list l14:level1
      	{mso-level-start-at:3;
      	mso-level-suffix:none;
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:0cm;
      	text-indent:0cm;}
      @list l15
      	{mso-list-id:1499244572;
      	mso-list-type:simple;
      	mso-list-template-ids:1499244572;}
      @list l15:level1
      	{mso-level-suffix:none;
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:0cm;
      	text-indent:0cm;}
      @list l16
      	{mso-list-id:1713185068;
      	mso-list-type:hybrid;
      	mso-list-template-ids:1615785632 -1416612536 67698713 67698715 67698703 67698713 67698715 67698703 67698713 67698715;}
      @list l16:level1
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:18.0pt;
      	text-indent:-18.0pt;}
      @list l16:level2
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%2\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:42.0pt;
      	text-indent:-21.0pt;}
      @list l16:level3
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:63.0pt;
      	text-indent:-21.0pt;}
      @list l16:level4
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:84.0pt;
      	text-indent:-21.0pt;}
      @list l16:level5
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%5\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:105.0pt;
      	text-indent:-21.0pt;}
      @list l16:level6
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:126.0pt;
      	text-indent:-21.0pt;}
      @list l16:level7
      	{mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:147.0pt;
      	text-indent:-21.0pt;}
      @list l16:level8
      	{mso-level-number-format:alpha-lower;
      	mso-level-text:"%8\)";
      	mso-level-tab-stop:none;
      	mso-level-number-position:left;
      	margin-left:168.0pt;
      	text-indent:-21.0pt;}
      @list l16:level9
      	{mso-level-number-format:roman-lower;
      	mso-level-tab-stop:none;
      	mso-level-number-position:right;
      	margin-left:189.0pt;
      	text-indent:-21.0pt;}
      ol
      	{margin-bottom:0cm;}
      ul
      	{margin-bottom:0cm;}
      -->
    </style>
    <!--[if gte mso 10]>
      <style>
        /* Style Definitions */
        table.MsoNormalTable {
          mso-style-name: 普通表格;
          mso-tstyle-rowband-size: 0;
          mso-tstyle-colband-size: 0;
          mso-style-noshow: yes;
          mso-style-priority: 99;
          mso-style-parent: '';
          mso-padding-alt: 0cm 5.4pt 0cm 5.4pt;
          mso-para-margin: 0cm;
          mso-pagination: widow-orphan;
          font-size: 12pt;
          font-family: 'Calibri', sans-serif;
          mso-ascii-font-family: Calibri;
          mso-ascii-theme-font: minor-latin;
          mso-hansi-font-family: Calibri;
          mso-hansi-theme-font: minor-latin;
          mso-bidi-font-family: 'Times New Roman';
          mso-bidi-theme-font: minor-bidi;
        }
        table.MsoTableGrid {
          mso-style-name: 网格型;
          mso-tstyle-rowband-size: 0;
          mso-tstyle-colband-size: 0;
          mso-style-priority: 39;
          mso-style-unhide: no;
          border: solid windowtext 1pt;
          mso-border-alt: solid windowtext 0.5pt;
          mso-padding-alt: 0cm 5.4pt 0cm 5.4pt;
          mso-border-insideh: 0.5pt solid windowtext;
          mso-border-insidev: 0.5pt solid windowtext;
          mso-para-margin: 0cm;
          mso-pagination: widow-orphan;
          font-size: 12pt;
          font-family: 'Calibri', sans-serif;
          mso-ascii-font-family: Calibri;
          mso-ascii-theme-font: minor-latin;
          mso-hansi-font-family: Calibri;
          mso-hansi-theme-font: minor-latin;
          mso-bidi-font-family: 'Times New Roman';
          mso-bidi-theme-font: minor-bidi;
        }
      </style>
    <![endif]-->
    <!--[if gte mso 9
      ]><xml> <o:shapedefaults v:ext="edit" spidmax="2050" /> </xml
    ><![endif]-->
    <!--[if gte mso 9
      ]><xml>
        <o:shapelayout v:ext="edit">
          <o:idmap v:ext="edit" data="2" /> </o:shapelayout></xml
    ><![endif]-->
  </head>

  <body
    lang="ZH-CN"
    link="#0563C1"
    vlink="#954F72"
    style="tab-interval:36.0pt;
word-wrap:break-word;text-align: center;"
  >
    <div class="WordSection1" style="layout-grid:15.6pt">
      <p class="MsoNormal" align="center" style="text-align:center">
        <a name="OLE_LINK5"></a
        ><a name="OLE_LINK6"
          ><span style="mso-bookmark:OLE_LINK5"
            ><b
              ><span style="font-size:15.0pt"
                >如何创建访问秘钥<span lang="EN-US"
                  ><o:p></o:p></span></span></b></span
        ></a>
      </p>

      <p
        class="MsoListParagraph"
        style="margin-left:18.0pt;mso-add-space:auto;
text-indent:-18.0pt;mso-list:l16 level1 lfo16"
      >
        <span style="mso-bookmark:OLE_LINK6"
          ><span style="mso-bookmark:OLE_LINK5"
            ><![if !supportLists]><span
              lang="EN-US"
              style="mso-bidi-font-family:微软雅黑"
              ><span style="mso-list:Ignore"
                >1.<span style='font:7.0pt "Times New Roman"'
                  >&nbsp;&nbsp;&nbsp;
                </span></span
              ></span
            ><![endif]>在<span lang="EN-US">AWS</span>控制台搜索<span
              lang="EN-US"
              >IAM</span
            >服务</span
          ></span
        >
      </p>

      <p class="MsoNormal">
        <span style="mso-bookmark:OLE_LINK6"
          ><span
            style="mso-bookmark:
OLE_LINK5"
            ><span lang="EN-US" style="mso-no-proof:yes"
              ><!--[if gte vml 1
                ]><v:shapetype
                  id="_x0000_t75"
                  coordsize="21600,21600"
                  o:spt="75"
                  o:preferrelative="t"
                  path="m@4@5l@4@11@9@11@9@5xe"
                  filled="f"
                  stroked="f"
                >
                  <v:stroke joinstyle="miter" />
                  <v:formulas>
                    <v:f eqn="if lineDrawn pixelLineWidth 0" />
                    <v:f eqn="sum @0 1 0" />
                    <v:f eqn="sum 0 0 @1" />
                    <v:f eqn="prod @2 1 2" />
                    <v:f eqn="prod @3 21600 pixelWidth" />
                    <v:f eqn="prod @3 21600 pixelHeight" />
                    <v:f eqn="sum @0 0 1" />
                    <v:f eqn="prod @6 1 2" />
                    <v:f eqn="prod @7 21600 pixelWidth" />
                    <v:f eqn="sum @8 21600 0" />
                    <v:f eqn="prod @7 21600 pixelHeight" />
                    <v:f eqn="sum @10 21600 0" />
                  </v:formulas>
                  <v:path
                    o:extrusionok="f"
                    gradientshapeok="t"
                    o:connecttype="rect"
                  />
                  <o:lock v:ext="edit" aspectratio="t" /> </v:shapetype
                ><v:shape
                  id="图片_x0020_11"
                  o:spid="_x0000_i1036"
                  type="#_x0000_t75"
                  style="width:522.6pt;height:327pt;visibility:visible;mso-wrap-style:square"
                >
                  <v:imagedata
                    src="AWS如何创建访问密钥（AKSK）.files/image001.png"
                    o:title=""
                  /> </v:shape><!
              [endif]--><![if !vml]><img
                width="697"
                height="436"
                src="AWS如何创建访问密钥（AKSK）.files/image002.png"
                v:shapes="图片_x0020_11"
              /><![endif]></span
            ></span
          ></span
        >
      </p>

      <p
        class="MsoListParagraph"
        style="margin-left:18.0pt;mso-add-space:auto;
text-indent:-18.0pt;mso-list:l16 level1 lfo16"
      >
        <span style="mso-bookmark:OLE_LINK6"
          ><span style="mso-bookmark:OLE_LINK5"
            ><![if !supportLists]><span
              lang="EN-US"
              style="mso-bidi-font-family:微软雅黑"
              ><span style="mso-list:Ignore"
                >2.<span style='font:7.0pt "Times New Roman"'
                  >&nbsp;&nbsp;&nbsp;
                </span></span
              ></span
            ><![endif]>选择用户并创建用户</span
          ></span
        >
      </p>

      <p class="MsoNormal">
        <span style="mso-bookmark:OLE_LINK6"
          ><span
            style="mso-bookmark:
OLE_LINK5"
            ><span lang="EN-US" style="mso-no-proof:yes"
              ><!--[if gte vml 1
                ]><v:shape
                  id="图片_x0020_12"
                  o:spid="_x0000_i1035"
                  type="#_x0000_t75"
                  style="width:522.6pt;
 height:327pt;visibility:visible;mso-wrap-style:square"
                >
                  <v:imagedata
                    src="AWS如何创建访问密钥（AKSK）.files/image003.png"
                    o:title=""
                  /> </v:shape><!
              [endif]--><![if !vml]><img
                width="697"
                height="436"
                src="AWS如何创建访问密钥（AKSK）.files/image004.png"
                v:shapes="图片_x0020_12"
              /><![endif]></span
            ></span
          ></span
        >
      </p>

      <p
        class="MsoListParagraph"
        style="margin-left:18.0pt;mso-add-space:auto;
text-indent:-18.0pt;mso-list:l16 level1 lfo16"
      >
        <span style="mso-bookmark:OLE_LINK6"
          ><span style="mso-bookmark:OLE_LINK5"
            ><![if !supportLists]><span
              lang="EN-US"
              style="mso-bidi-font-family:微软雅黑"
              ><span style="mso-list:Ignore"
                >3.<span style='font:7.0pt "Times New Roman"'
                  >&nbsp;&nbsp;&nbsp;
                </span></span
              ></span
            ><![endif]>根据引导创建用户，并附加权限策略</span
          ></span
        >
      </p>

      <p class="MsoNormal">
        <span style="mso-bookmark:OLE_LINK6"
          ><span
            style="mso-bookmark:
OLE_LINK5"
            ><span lang="EN-US" style="mso-no-proof:yes"
              ><!--[if gte vml 1
                ]><v:shape
                  id="图片_x0020_13"
                  o:spid="_x0000_i1034"
                  type="#_x0000_t75"
                  alt="图形用户界面, 文本, 应用程序, 电子邮件&#10;&#10;描述已自动生成"
                  style="width:522.6pt;height:327pt;visibility:visible;mso-wrap-style:square"
                >
                  <v:imagedata
                    src="AWS如何创建访问密钥（AKSK）.files/image005.png"
                    o:title="图形用户界面, 文本, 应用程序, 电子邮件&#10;&#10;描述已自动生成"
                  /> </v:shape><!
              [endif]--><![if !vml]><img
                width="697"
                height="436"
                src="AWS如何创建访问密钥（AKSK）.files/image006.png"
                alt="图形用户界面, 文本, 应用程序, 电子邮件&#10;&#10;描述已自动生成"
                v:shapes="图片_x0020_13"
              /><![endif]></span
            ></span
          ></span
        >
      </p>

      <p class="MsoNormal">
        <span style="mso-bookmark:OLE_LINK6"
          ><span
            style="mso-bookmark:
OLE_LINK5"
            ><span lang="EN-US" style="mso-no-proof:yes"
              ><!--[if gte vml 1
                ]><v:shape
                  id="图片_x0020_14"
                  o:spid="_x0000_i1033"
                  type="#_x0000_t75"
                  alt="图形用户界面, 应用程序&#10;&#10;描述已自动生成"
                  style="width:522.6pt;height:327pt;visibility:visible;mso-wrap-style:square"
                >
                  <v:imagedata
                    src="AWS如何创建访问密钥（AKSK）.files/image007.png"
                    o:title="图形用户界面, 应用程序&#10;&#10;描述已自动生成"
                  /> </v:shape><!
              [endif]--><![if !vml]><img
                width="697"
                height="436"
                src="AWS如何创建访问密钥（AKSK）.files/image008.png"
                alt="图形用户界面, 应用程序&#10;&#10;描述已自动生成"
                v:shapes="图片_x0020_14"
              /><![endif]></span
            ></span
          ></span
        >
      </p>

      <p class="MsoNormal">
        <span style="mso-bookmark:OLE_LINK6"
          ><span
            style="mso-bookmark:
OLE_LINK5"
            >只需要<span lang="EN-US">S3/Connect/SES</span
            >的权限，可直接在策略搜索栏搜索以下策略并添加</span
          ></span
        >
      </p>

      <p class="MsoNormal">
        <span style="mso-bookmark:OLE_LINK6"
          ><span
            style="mso-bookmark:
OLE_LINK5"
          ></span></span
        ><a
          href="https://us-east-1.console.aws.amazon.com/iamv2/home?region=us-east-1#/policies/details/arn%3Aaws%3Aiam%3A%3Aaws%3Apolicy%2FAmazonS3FullAccess"
          target="_blank"
          ><span style="mso-bookmark:OLE_LINK6"
            ><span
              style="mso-bookmark:
OLE_LINK5"
              ><span
                lang="EN-US"
                style='font-size:10.5pt;font-family:"Helvetica Neue";
background:#F1FAFF'
                >AmazonS3FullAccess</span
              ></span
            ></span
          ></a
        ><span style="mso-bookmark:OLE_LINK6"
          ><span style="mso-bookmark:OLE_LINK5"></span
        ></span>
      </p>

      <p class="MsoNormal">
        <span style="mso-bookmark:OLE_LINK6"
          ><span
            style="mso-bookmark:
OLE_LINK5"
          ></span></span
        ><a
          href="https://us-east-1.console.aws.amazon.com/iamv2/home?region=us-east-1#/policies/details/arn%3Aaws%3Aiam%3A%3Aaws%3Apolicy%2FAmazonConnect_FullAccess"
          target="_blank"
          ><span style="mso-bookmark:OLE_LINK6"
            ><span
              style="mso-bookmark:
OLE_LINK5"
              ><span
                lang="EN-US"
                style='font-size:10.5pt;font-family:"Helvetica Neue";
background:#F1FAFF'
                >AmazonConnect_FullAccess</span
              ></span
            ></span
          ></a
        ><span style="mso-bookmark:OLE_LINK6"
          ><span style="mso-bookmark:OLE_LINK5"
            ><span
              lang="EN-US"
              style="font-size:12.0pt;font-family:宋体;mso-bidi-font-family:宋体"
              ><o:p></o:p></span></span
        ></span>
      </p>

      <p class="MsoNormal">
        <span style="mso-bookmark:OLE_LINK6"
          ><span
            style="mso-bookmark:
OLE_LINK5"
          ></span></span
        ><a
          href="https://us-east-1.console.aws.amazon.com/iamv2/home?region=us-east-1#/policies/details/arn%3Aaws%3Aiam%3A%3Aaws%3Apolicy%2FAmazonSESFullAccess"
          target="_blank"
          ><span style="mso-bookmark:OLE_LINK6"
            ><span
              style="mso-bookmark:
OLE_LINK5"
              ><span
                lang="EN-US"
                style='font-size:10.5pt;font-family:"Helvetica Neue";
background:#F1FAFF'
                >AmazonSESFullAccess</span
              ></span
            ></span
          ></a
        ><span style="mso-bookmark:OLE_LINK6"
          ><span style="mso-bookmark:OLE_LINK5"></span
        ></span>
      </p>

      <p class="MsoNormal">
        <span style="mso-bookmark:OLE_LINK6"
          ><span
            style="mso-bookmark:
OLE_LINK5"
            >下一步确认权限添加成功，并创建用户</span
          ></span
        ><span style="mso-bookmark:OLE_LINK6"
          ><span style="mso-bookmark:OLE_LINK5"
            ><span
              lang="EN-US"
              style="font-size:12.0pt;
font-family:宋体;mso-bidi-font-family:宋体"
              ><o:p></o:p></span></span
        ></span>
      </p>

      <p class="MsoNormal">
        <span style="mso-bookmark:OLE_LINK6"
          ><span
            style="mso-bookmark:
OLE_LINK5"
            ><span
              lang="EN-US"
              style="font-size:12.0pt;font-family:宋体;mso-bidi-font-family:
宋体;mso-no-proof:yes"
              ><!--[if gte vml 1
                ]><v:shape
                  id="图片_x0020_32"
                  o:spid="_x0000_i1032"
                  type="#_x0000_t75"
                  alt="图形用户界面, 应用程序&#10;&#10;描述已自动生成"
                  style="width:522.6pt;
 height:327pt;visibility:visible;mso-wrap-style:square"
                >
                  <v:imagedata
                    src="AWS如何创建访问密钥（AKSK）.files/image009.png"
                    o:title="图形用户界面, 应用程序&#10;&#10;描述已自动生成"
                  /> </v:shape><!
              [endif]--><![if !vml]><img
                border="0"
                width="697"
                height="436"
                src="AWS如何创建访问密钥（AKSK）.files/image010.png"
                alt="图形用户界面, 应用程序&#10;&#10;描述已自动生成"
                v:shapes="图片_x0020_32"
              /><![endif]></span
            ></span
          ></span
        ><span
          style="mso-bookmark:
OLE_LINK6"
          ><span style="mso-bookmark:OLE_LINK5"
            ><span
              lang="EN-US"
              style="font-size:12.0pt;font-family:宋体;mso-bidi-font-family:宋体"
              ><o:p></o:p></span></span
        ></span>
      </p>

      <p class="MsoNormal">
        <span style="mso-bookmark:OLE_LINK6"
          ><span
            style="mso-bookmark:
OLE_LINK5"
            ><span
              lang="EN-US"
              style="font-size:12.0pt;font-family:宋体;mso-bidi-font-family:
宋体"
              ><o:p>&nbsp;</o:p></span
            ></span
          ></span
        >
      </p>

      <p
        class="MsoListParagraph"
        style="margin-left:18.0pt;mso-add-space:auto;
text-indent:-18.0pt;mso-list:l16 level1 lfo16"
      >
        <span style="mso-bookmark:OLE_LINK6"
          ><span style="mso-bookmark:OLE_LINK5"
            ><![if !supportLists]><span
              lang="EN-US"
              style="mso-bidi-font-family:微软雅黑"
              ><span style="mso-list:Ignore"
                >4.<span style='font:7.0pt "Times New Roman"'
                  >&nbsp;&nbsp;&nbsp;
                </span></span
              ></span
            ><![endif]>在<span lang="EN-US">IAM</span
            >控制台用户处选择需要创建<span lang="EN-US">AKSK</span>的用户</span
          ></span
        >
      </p>

      <p class="MsoNormal">
        <span style="mso-bookmark:OLE_LINK6"
          ><span
            style="mso-bookmark:
OLE_LINK5"
            ><span lang="EN-US" style="mso-no-proof:yes"
              ><!--[if gte vml 1
                ]><v:shape
                  id="图片_x0020_33"
                  o:spid="_x0000_i1031"
                  type="#_x0000_t75"
                  alt="图形用户界面, 文本, 应用程序&#10;&#10;描述已自动生成"
                  style="width:522.6pt;height:327pt;visibility:visible;mso-wrap-style:square"
                >
                  <v:imagedata
                    src="AWS如何创建访问密钥（AKSK）.files/image011.png"
                    o:title="图形用户界面, 文本, 应用程序&#10;&#10;描述已自动生成"
                  /> </v:shape><!
              [endif]--><![if !vml]><img
                border="0"
                width="697"
                height="436"
                src="AWS如何创建访问密钥（AKSK）.files/image012.png"
                alt="图形用户界面, 文本, 应用程序&#10;&#10;描述已自动生成"
                v:shapes="图片_x0020_33"
              /><![endif]></span
            ></span
          ></span
        >
      </p>

      <p
        class="MsoListParagraph"
        style="margin-left:18.0pt;mso-add-space:auto;
text-indent:-18.0pt;mso-list:l16 level1 lfo16"
      >
        <span style="mso-bookmark:OLE_LINK6"
          ><span style="mso-bookmark:OLE_LINK5"
            ><![if !supportLists]><span
              lang="EN-US"
              style="mso-bidi-font-family:微软雅黑"
              ><span style="mso-list:Ignore"
                >5.<span style='font:7.0pt "Times New Roman"'
                  >&nbsp;&nbsp;&nbsp;
                </span></span
              ></span
            ><![endif]>选择安全凭证</span
          ></span
        >
      </p>

      <p class="MsoNormal">
        <span style="mso-bookmark:OLE_LINK6"
          ><span
            style="mso-bookmark:
OLE_LINK5"
            ><span lang="EN-US" style="mso-no-proof:yes"
              ><!--[if gte vml 1
                ]><v:shape
                  id="图片_x0020_38"
                  o:spid="_x0000_i1030"
                  type="#_x0000_t75"
                  alt="图形用户界面&#10;&#10;描述已自动生成"
                  style="width:522.6pt;height:327pt;visibility:visible;mso-wrap-style:square"
                >
                  <v:imagedata
                    src="AWS如何创建访问密钥（AKSK）.files/image013.png"
                    o:title="图形用户界面&#10;&#10;描述已自动生成"
                  /> </v:shape><!
              [endif]--><![if !vml]><img
                border="0"
                width="697"
                height="436"
                src="AWS如何创建访问密钥（AKSK）.files/image014.png"
                alt="图形用户界面&#10;&#10;描述已自动生成"
                v:shapes="图片_x0020_38"
              /><![endif]></span
            ></span
          ></span
        >
      </p>

      <p
        class="MsoListParagraph"
        style="margin-left:18.0pt;mso-add-space:auto;
text-indent:-18.0pt;mso-list:l16 level1 lfo16"
      >
        <span style="mso-bookmark:OLE_LINK6"
          ><span style="mso-bookmark:OLE_LINK5"
            ><![if !supportLists]><span
              lang="EN-US"
              style="mso-bidi-font-family:微软雅黑"
              ><span style="mso-list:Ignore"
                >6.<span style='font:7.0pt "Times New Roman"'
                  >&nbsp;&nbsp;&nbsp;
                </span></span
              ></span
            ><![endif]>创建访问密钥</span
          ></span
        >
      </p>

      <p class="MsoNormal">
        <span style="mso-bookmark:OLE_LINK6"
          ><span
            style="mso-bookmark:
OLE_LINK5"
            ><span lang="EN-US" style="mso-no-proof:yes"
              ><!--[if gte vml 1
                ]><v:shape
                  id="图片_x0020_39"
                  o:spid="_x0000_i1029"
                  type="#_x0000_t75"
                  alt="图形用户界面, 文本, 应用程序&#10;&#10;描述已自动生成"
                  style="width:522.6pt;height:327pt;visibility:visible;mso-wrap-style:square"
                >
                  <v:imagedata
                    src="AWS如何创建访问密钥（AKSK）.files/image015.png"
                    o:title="图形用户界面, 文本, 应用程序&#10;&#10;描述已自动生成"
                  /> </v:shape><!
              [endif]--><![if !vml]><img
                border="0"
                width="697"
                height="436"
                src="AWS如何创建访问密钥（AKSK）.files/image016.png"
                alt="图形用户界面, 文本, 应用程序&#10;&#10;描述已自动生成"
                v:shapes="图片_x0020_39"
              /><![endif]></span
            ></span
          ></span
        >
      </p>

      <p
        class="MsoListParagraph"
        style="margin-left:18.0pt;mso-add-space:auto;
text-indent:-18.0pt;mso-list:l16 level1 lfo16"
      >
        <span style="mso-bookmark:OLE_LINK6"
          ><span style="mso-bookmark:OLE_LINK5"
            ><![if !supportLists]><span
              lang="EN-US"
              style="mso-bidi-font-family:微软雅黑"
              ><span style="mso-list:Ignore"
                >7.<span style='font:7.0pt "Times New Roman"'
                  >&nbsp;&nbsp;&nbsp;
                </span></span
              ></span
            ><![endif]>使用案例根据具体情况选择，并下一步进行创建</span
          ></span
        >
      </p>

      <p class="MsoNormal">
        <span style="mso-bookmark:OLE_LINK6"
          ><span
            style="mso-bookmark:
OLE_LINK5"
            ><span lang="EN-US" style="mso-no-proof:yes"
              ><!--[if gte vml 1
                ]><v:shape
                  id="图片_x0020_40"
                  o:spid="_x0000_i1028"
                  type="#_x0000_t75"
                  alt="图形用户界面, 文本, 应用程序, 电子邮件&#10;&#10;描述已自动生成"
                  style="width:522.6pt;height:327pt;visibility:visible;mso-wrap-style:square"
                >
                  <v:imagedata
                    src="AWS如何创建访问密钥（AKSK）.files/image017.png"
                    o:title="图形用户界面, 文本, 应用程序, 电子邮件&#10;&#10;描述已自动生成"
                  /> </v:shape><!
              [endif]--><![if !vml]><img
                border="0"
                width="697"
                height="436"
                src="AWS如何创建访问密钥（AKSK）.files/image018.png"
                alt="图形用户界面, 文本, 应用程序, 电子邮件&#10;&#10;描述已自动生成"
                v:shapes="图片_x0020_40"
              /><![endif]></span
            ></span
          ></span
        >
      </p>

      <span style="mso-bookmark:OLE_LINK5"></span
      ><span style="mso-bookmark:OLE_LINK6"></span>

      <p class="MsoNormal">
        <span lang="EN-US" style="mso-no-proof:yes"
          ><!--[if gte vml 1
            ]><v:shape
              id="图片_x0020_42"
              o:spid="_x0000_i1027"
              type="#_x0000_t75"
              alt="图形用户界面, 文本, 应用程序&#10;&#10;描述已自动生成"
              style="width:522.6pt;height:327pt;visibility:visible;mso-wrap-style:square"
            >
              <v:imagedata
                src="AWS如何创建访问密钥（AKSK）.files/image019.png"
                o:title="图形用户界面, 文本, 应用程序&#10;&#10;描述已自动生成"
              /> </v:shape><!
          [endif]--><![if !vml]><img
            border="0"
            width="697"
            height="436"
            src="AWS如何创建访问密钥（AKSK）.files/image020.png"
            alt="图形用户界面, 文本, 应用程序&#10;&#10;描述已自动生成"
            v:shapes="图片_x0020_42"
          /><![endif]></span
        >
      </p>

      <p class="MsoNormal">
        <span lang="EN-US" style="mso-no-proof:yes"
          ><!--[if gte vml 1
            ]><v:shape
              id="图片_x0020_44"
              o:spid="_x0000_i1026"
              type="#_x0000_t75"
              alt="图形用户界面, 文本, 应用程序&#10;&#10;描述已自动生成"
              style="width:522.6pt;height:327pt;visibility:visible;mso-wrap-style:square"
            >
              <v:imagedata
                src="AWS如何创建访问密钥（AKSK）.files/image021.png"
                o:title="图形用户界面, 文本, 应用程序&#10;&#10;描述已自动生成"
              /> </v:shape><!
          [endif]--><![if !vml]><img
            border="0"
            width="697"
            height="436"
            src="AWS如何创建访问密钥（AKSK）.files/image022.png"
            alt="图形用户界面, 文本, 应用程序&#10;&#10;描述已自动生成"
            v:shapes="图片_x0020_44"
          /><![endif]></span
        >
      </p>

      <p class="MsoNormal">
        此时已经创建完成，需要注意的是创建完成后点击右下角的下载<span
          lang="EN-US"
          >.csv</span
        >文件，将<span lang="EN-US">AKSK</span>保存，此页面一旦关闭，<span
          lang="EN-US"
          >AKSK</span
        >将无法查看，只能通过下载的<span lang="EN-US">csv</span>文件查看。
      </p>

      <p class="MsoNormal">
        <span lang="EN-US" style="color:black;mso-themecolor:text1"
          ><o:p>&nbsp;</o:p></span
        >
      </p>
    </div>
  </body>
</html>
